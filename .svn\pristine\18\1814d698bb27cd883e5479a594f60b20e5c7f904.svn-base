package com.shunwang.basepassport.user.response;

import com.google.gson.annotations.Expose;
import com.shunwang.baseStone.core.response.BaseStoneResponse;
import com.shunwang.basepassport.user.pojo.Member;

import java.util.ArrayList;
import java.util.List;

public class QueryUserByBindMobileResponse extends BaseStoneResponse {

	public QueryUserByBindMobileResponse(List<Member> members) {
		List<UserInfo> userInfos = new ArrayList<>();
		members.forEach(member -> {
			UserInfo userInfo = new UserInfo();
			userInfo.setUserId(member.getMemberId());
			userInfo.setMemberName(member.getMemberName());
			userInfo.setNickName(member.getNickName());
			userInfo.setBindState(member.getBindState());
			userInfos.add(userInfo);
		});
		items = userInfos;
	}

	public static class UserInfo{
		/**会员ID**/
		@Expose
		private Integer userId;
		@Expose
		private String memberName;
		/**真实姓名**/
		@Expose
		private String nickName;
		/**会员绑定状**/
		@Expose
		private Integer bindState;

		public Integer getBindState() {
			return bindState;
		}

		public void setBindState(Integer bindState) {
			this.bindState = bindState;
		}

		public String getNickName() {
			return nickName;
		}

		public void setNickName(String nickName) {
			this.nickName = nickName;
		}

		public Integer getUserId() {
			return userId;
		}

		public void setUserId(Integer userId) {
			this.userId = userId;
		}

		public String getMemberName() {
			return memberName;
		}

		public void setMemberName(String memberName) {
			this.memberName = memberName;
		}
	}

}
