$import('SFSelWinInp');
$import('SFDirect');
var ServiceSelInp = $createClass('ServiceSelInp',function(param){
	if(!param) param= {};
	param.valueName = 'servicekey';
	param.gridCol = [
		{id:'servicekey',text:'key'},
		{id:'servicename',text:'名称'},	
		{id:'servicestateshow',text:'状态'},
		{id:'remark',text:'备注'}	
	];
	param.schControl =  [
		new SFInput({field:'schServicekey',name:'key'}),
		new SFInput({field:'schServicename',name:'名称'})
	];
	param.gridUrl="listService.do";
	this.SFSelWinInp(param);
	
},'SFSelWinInp');