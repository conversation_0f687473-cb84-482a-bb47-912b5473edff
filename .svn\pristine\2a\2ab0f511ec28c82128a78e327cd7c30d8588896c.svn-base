package com.shunwang.baseStone.category.dao;

import java.util.Date;

import com.shunwang.baseStone.category.pojo.Category;
import com.shunwang.baseStone.context.BackUserContext;
import com.shunwang.baseStone.core.detail.HasDetailDao;
import com.shunwang.framework.exception.WinterException;



public class CategoryDao extends HasDetailDao<Category>{
	
	@Override
	public void delete(Category p) throws WinterException {
		super.delete(p);
	}

	@Override
	public Category save(Category p) throws WinterException {
		p.setTimeadd(new Date());
		p.setUseradd(BackUserContext.getUserName());
		return super.save(p);
	}

	@Override
	public Category update(Category p) throws WinterException {
		p.setTimeedit(new Date());
		p.setUseredit(BackUserContext.getUserName());
		return super.update(p);
	}

}
