package com.shunwang.baseStone.backuser.pojo;

/**
 * <AUTHOR>
 * @since 2015-10-10
 */
public class OAuth2AccessTokenReq {
    public static final String GRANT_TYPE = "authorization_code";

    private String clientId;
    private String clientSecret;
    private String grantType;
    private String code;
    private String redirectUri;

    public String buildRequestUrl(String apiUrl) {
        StringBuilder sb = new StringBuilder();
        sb.append(apiUrl).append("?client_id=").append(clientId).append("&client_secret=").append(clientSecret)
                .append("&grant_type=").append(grantType).append("&code=").append(code).append("&redirect_uri=").append(redirectUri);
        return sb.toString();
    }

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public String getClientSecret() {
        return clientSecret;
    }

    public void setClientSecret(String clientSecret) {
        this.clientSecret = clientSecret;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getGrantType() {
        return grantType;
    }

    public void setGrantType(String grantType) {
        this.grantType = grantType;
    }

    public String getRedirectUri() {
        return redirectUri;
    }

    public void setRedirectUri(String redirectUri) {
        this.redirectUri = redirectUri;
    }
}
