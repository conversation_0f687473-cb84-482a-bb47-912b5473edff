<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN"
        "http://ibatis.apache.org/dtd/sql-map-2.dtd" >

<sqlMap namespace="com.shunwang.basepassport.user.pojo.MemberUnsafe">

    <typeAlias alias="memberUnsafe"
               type="com.shunwang.basepassport.user.pojo.MemberUnsafe"/>

    <resultMap class="com.shunwang.basepassport.user.pojo.MemberUnsafe"
               id="memberUnsafeMap">
        <result column="member_id" property="memberId" jdbcType="INTEGER"/>
        <result column="member_name" property="memberName" jdbcType="varchar"/>
        <result column="member_type" property="memberType" jdbcType="INTEGER"/>
        <result column="time_add" property="timeAdd" jdbcType="datetime"/>
        <result column="time_edit" property="timeEdit" jdbcType="datetime"/>
        <result column="remark" property="remark" jdbcType="varchar"/>
    </resultMap>

    <select id="get" resultMap="memberUnsafeMap">
    select * from personal_member_unsafe  where  member_id=#memberId:INTEGER# limit 1
  </select>


    <update id="update" parameterClass="memberUnsafe">
    update personal_member_unsafe set
      member_type = #memberType:INTEGER#,
      time_edit = #timeEdit:DATETIME#
    where member_id = #memberId:INTEGER#
  </update>



</sqlMap>
