package com.shunwang.basepassport.user.response;

import com.google.gson.annotations.Expose;
import com.shunwang.baseStone.core.response.BaseStoneResponse;
import com.shunwang.baseStone.encrypt.Encrypt;
import com.shunwang.baseStone.encrypt.pojo.EncryptItem;
import com.shunwang.xmlbean.annotation.XmlInit;

public class GamePeriodQueryRespone extends BaseStoneResponse {

	private GamePeriodQueryResult gamePeriodQueryResult;

	public GamePeriodQueryRespone(Boolean canplay, String period) {
		this.gamePeriodQueryResult = new GamePeriodQueryResult();
		this.gamePeriodQueryResult.canplay = canplay;
		this.gamePeriodQueryResult.period = period;

	}
	
	public String buildSign(){
		Encrypt  encrypt=new Encrypt();
		encrypt.addItem(new EncryptItem(String.valueOf(this.gamePeriodQueryResult.canplay)));
		encrypt.addItem(new EncryptItem(this.gamePeriodQueryResult.period));
		return encrypt.buildSign();
	}
	
	public class GamePeriodQueryResult {
		@Expose
		private Boolean canplay;
		@Expose
		private String period;

		@XmlInit
		public Boolean getCanplay() {
			return canplay;
		}

		public void setCanplay(Boolean canplay) {
			this.canplay = canplay;
		}

		@XmlInit
		public String getPeriod() {
			return period;
		}

		public void setPeriod(String period) {
			this.period = period;
		}

	}

	@XmlInit(path="items/item")
	public GamePeriodQueryResult getGamePeriodQueryResult() {
		return gamePeriodQueryResult;
	}

	public void setGamePeriodQueryResult(GamePeriodQueryResult gamePeriodQueryResult) {
		this.gamePeriodQueryResult = gamePeriodQueryResult;
	}

}
