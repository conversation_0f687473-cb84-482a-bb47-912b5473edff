package com.shunwang.basepassport.business.response;

import com.shunwang.basepassport.config.pojo.ApiApp;
import org.jdom.Document;
import org.jdom.Element;

import java.util.ArrayList;
import java.util.List;

/**
 * 所有站点信息响应
 *
 * <AUTHOR>
 * @since 2013-10-31
 */
public class AllBusinessResponse extends BusinessResponse {

    private List<BusinessItem> list = new ArrayList<>();

    public AllBusinessResponse(List<ApiApp> businessList) {
        for (ApiApp apiApp : businessList) {
            list.add(convert(apiApp));
        }
        setItems(list);
    }

    protected Document processDocument() {
        Element root = new Element("kedou");
        Document doc = new Document(root);
        doc.setRootElement(root);

        Element businesses = new Element("businesses");
        businesses.setAttribute("total", String.valueOf(list.size()));
        doc.getRootElement().addContent(businesses);

        for (BusinessItem item : list ) {
            Element element = new Element("business");
            element.addContent(new Element("key").setText(item.getKey()));
            element.addContent(new Element("name").setText(item.getName()));
            element.addContent(new Element("state").setText(item.getState() + ""));
            element.addContent(new Element("createdAt").setText(item.getTimeAdd()));
            element.addContent(new Element("updatedAt").setText(item.getTimeEdit()));

            businesses.addContent(element);
        }
        return doc;
    }

}
