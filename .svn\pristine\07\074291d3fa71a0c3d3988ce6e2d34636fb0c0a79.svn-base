package com.shunwang.baseStone.css.dao;

import com.shunwang.baseStone.cache.CacheKeyGenerator;
import com.shunwang.baseStone.core.dao.CacheMapDao;
import com.shunwang.baseStone.core.dao.RefreshMem;
import com.shunwang.baseStone.css.pojo.Css;
import com.shunwang.basepassport.jms.CacheMessageBuilder;
import com.shunwang.basepassport.jms.MessageProducer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

@Deprecated
public class CssDao extends CacheMapDao<Css> implements RefreshMem {

    private final static Logger log = LoggerFactory.getLogger(CssDao.class);
    private MessageProducer messageProducer;


    @Override
    protected String getKey(Css pojo) {
        return CacheKeyGenerator.getCssConfigKey(pojo.getSiteId(), pojo.getBussCode());
    }

    public Css findALLByID(String siteId, String bussCode) {
        CssDbDao dao = (CssDbDao) this.getDbDao();
        return dao.getCssBySiteIdAndBussCode(siteId, bussCode);
    }

    public Css getCssBySiteIdAndBussCode(String siteId, String bussCode) {
        CssDbDao dao = (CssDbDao) this.getDbDao();
        return dao.getCssBySiteIdAndBussCode(siteId, bussCode);
    }

    @Override
    protected Css loadCachePojo(Css pojo) {
        CssDbDao dao = (CssDbDao) this.getDbDao();
        return dao.getCssBySiteIdAndBussCode(pojo.getSiteId(), pojo.getBussCode());
    }

    @Override
    public void refresh() {
        Map<String, Css> ret = this.loadMap();
        //saveMap(ret);
        //不建议使用，需要实现刷新全部的逻辑
    }

    public void refreshCachePojo(Css css) {
        removeCachePojo(css);
    }

    @Override
    protected void removeCachePojo(Css css) {
        String key = CacheKeyGenerator.getCssConfigKey(css.getSiteId(), css.getBussCode());
        messageProducer.sendMessage(CacheMessageBuilder.newDeleteArrayMessage(key));
    }

    @Override
    protected String getClassName() {
        return "com.shunwang.baseStone.css.dao.CssDao";
    }

    public MessageProducer getMessageProducer() {
        return messageProducer;
    }

    public void setMessageProducer(MessageProducer messageProducer) {
        this.messageProducer = messageProducer;
    }
}
