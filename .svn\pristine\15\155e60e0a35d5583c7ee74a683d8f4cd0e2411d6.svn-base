package com.shunwang.basepassport.manager.response.inter;


import com.shunwang.basepassport.manager.IResponse;

public class BaseXmlResponse implements IResponse {
    private String msgId;
    private String msg;
    private String rawXml;
    public static final String SUCCESS = "0";

    public boolean isSuccess() {
        return SUCCESS.equals(getMsgId());
    }


    public String getMsgId() {
        return msgId;
    }

    public void setMsgId(String msgId) {
        this.msgId = msgId;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getRawXml() {
        return rawXml;
    }

    public void setRawXml(String rawXml) {
        this.rawXml = rawXml;
    }
}
