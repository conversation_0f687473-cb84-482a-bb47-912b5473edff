package com.shunwang.basepassport.util;

import com.shunwang.basepassport.weixin.constant.HttpClientConstant;
import org.apache.struts2.ServletActionContext;

/**
 * @author: lj.zeng
 * @create: 2025-03-27 09:57:51
 * @Description:
 */
public class UserAgentUtil {

    public static String getUserAgent() {
        return ServletActionContext.getRequest().getHeader(HttpClientConstant.Header.USER_AGENT).toLowerCase();
    }

    public static boolean isWechatWeb() {
        return getUserAgent().contains("micromessenger");
    }

    public static boolean isWechatInnerWeb() {
        return getUserAgent().contains("micromessenger") && !getUserAgent().contains("windowswechat");
    }

    public static boolean isAliapyWeb() {
        return getUserAgent().contains("aliapp");
    }
    public static boolean isQQ() {
        return getUserAgent().contains("qq/");
    }
    public static boolean isWeibo() {
        return getUserAgent().contains("weibo");
    }

}
