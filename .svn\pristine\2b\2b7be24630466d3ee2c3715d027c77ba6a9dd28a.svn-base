package com.shunwang.baseStone.test;

import com.opensymphony.xwork2.ActionSupport;
import com.shunwang.baseStone.bussiness.dao.BussinessDao;
import com.shunwang.baseStone.bussiness.pojo.Bussiness;
import com.shunwang.baseStone.context.BaseStoneContext;

public class Test2Action extends ActionSupport {
	/**
	 * <AUTHOR> create at 2011-10-17 下午02:17:55 
	 */
	private static final long serialVersionUID = -7883866313521818823L;
	private boolean ex;
	
	public boolean isEx() {
		return ex;
	}

	public void setEx(boolean ex) {
		this.ex = ex;
	}

	@Override
	public String execute() throws Exception {
		// TODO Auto-generated method stub
		BussinessDao dao =(BussinessDao)BaseStoneContext.getInstance().getBean("bussinessDao");
		Bussiness bs = dao.getById("1008_homepage");
		bs.setPassportname("aaa11");
		dao.update(bs);
		if(ex) throw new RuntimeException("出错了");
		Bussiness bs1 = dao.getById("1009_homepage");
		bs1.setPassportname("aaa122");
		dao.update(bs1);
		return super.execute();
	}
	
}
