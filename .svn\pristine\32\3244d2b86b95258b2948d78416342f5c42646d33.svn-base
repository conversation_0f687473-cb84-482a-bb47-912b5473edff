package com.shunwang.basepassport.filesystem.web;

import com.shunwang.baseStone.core.action.BaseStoneAction;
import com.shunwang.baseStone.core.exception.BaseStoneException;
import com.shunwang.basepassport.binder.common.ErrorCode;
import com.shunwang.basepassport.commonExp.FileTypeNotSupportedExp;
import com.shunwang.basepassport.commonExp.ParamNotFoundExp;
import com.shunwang.basepassport.filesystem.exception.FileExistException;
import com.shunwang.basepassport.filesystem.response.FileUploadResponse;
import com.shunwang.basepassport.filesystem.service.FileService;
import org.apache.commons.lang.StringUtils;
import org.apache.tika.Tika;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;

/**
 * 图片上传
 *
 * <AUTHOR>
 * @date 2020/3/18
 **/
public class FileUploadAction extends BaseStoneAction {

    private final static Logger log = LoggerFactory.getLogger(FileUploadAction.class);

    //5M
    public static final int MAX_FILE_SIZE = 5 * 1024 * 1024;

    private String directory;
    private File file;

    private FileService fileService;

    @Override
    public void process() throws Exception {
        log.info("directory[{}], file[{}]", directory, file.getName());
        if (fileService.isExistFile(directory)) {
            throw new FileExistException();
        }

        String path = fileService.uploadFile(directory, file);
        this.setBaseResponse(new FileUploadResponse(path));
    }

    @Override
    public String getSiteName() {
        return "加密文件上传";
    }

    @Override
    public void checkParam() {
        if (StringUtils.isEmpty(directory)) {
            throw new ParamNotFoundExp("directory");
        }
        if (file == null) {
            throw new ParamNotFoundExp("file");
        }
        if (file.length() / 1024 > MAX_FILE_SIZE) {
            throw new BaseStoneException(ErrorCode.C_1011);
        }

        Tika tika = new Tika();
        String result = null;
        try {
            result = tika.detect(file);
        } catch (IOException e) {
            log.warn("读取修改用户头像接口的上传文件出错:" + e.getMessage());
        }
        if (result == null) {
            throw new FileTypeNotSupportedExp();
        }
        String fileType = null;
        if ("image/gif".equals(result)) {
            fileType = "gif";
        }
        if ("image/jpeg".equals(result)) {
            fileType = "jpg";
        }
        if ("image/png".equals(result)) {
            fileType = "png";
        }
        if ("image/bmp".equals(result)) {
            fileType = "bmp";
        }
        if ("image/x-ms-bmp".equals(result)) {
            fileType = "bmp";
        }
        if (fileType == null) {
            throw new FileTypeNotSupportedExp();
        }
    }

    public FileService getFileService() {
        return fileService;
    }

    public void setFileService(FileService fileService) {
        this.fileService = fileService;
    }

    public String getDirectory() {
        return directory;
    }

    public void setDirectory(String directory) {
        this.directory = directory;
    }

    public File getFile() {
        return file;
    }

    public void setFile(File file) {
        this.file = file;
    }

}
