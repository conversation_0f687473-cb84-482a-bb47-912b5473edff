<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd" >
	<sqlMap namespace="com.shunwang.basepassport.config.pojo.RichText">
	<typeAlias alias="richText" type="com.shunwang.basepassport.config.pojo.RichText"/>
	<resultMap class="com.shunwang.basepassport.config.pojo.RichText" id="BaseResultMap">
		<result property="id" column="id" jdbcType="int"/>
		<result property="name" column="name" jdbcType="varchar"/>
		<result property="nameEn" column="name_en" jdbcType="varchar"/>
		<result property="terminal" column="terminal" jdbcType="varchar"/>
		<result property="type" column="type" jdbcType="int"/>
		<result property="state" column="state" jdbcType="int"/>
		<result property="link" column="link" jdbcType="varchar"/>
		<result property="content" column="content" jdbcType="varchar"/>
		<result property="version" column="version" jdbcType="varchar"/>
		<result property="remark" column="remark" jdbcType="varchar"/>
		<result property="timeAdd" column="time_add" jdbcType="datetime"/>
		<result property="userAdd" column="user_add" jdbcType="varchar"/>
		<result property="timeEdit" column="time_edit" jdbcType="datetime"/>
		<result property="userEdit" column="user_edit" jdbcType="varchar"/>
	</resultMap>

	<sql id="baseColumn">
		a.id,
		a.name,
		a.name_en,
		a.terminal,
		a.type,
		a.state,
		a.link,
		a.content,
		a.version,
		a.time_add,
		a.user_add,
		a.time_edit,
		a.user_edit
	</sql>
	<select id="get" resultMap="BaseResultMap" parameterClass="java.lang.Integer">
		select
		<include refid="baseColumn"/>
		from config_rich_text a
		where a.id = #id#
	</select>
	<select id="getByNameEnAndTerminal" resultMap="BaseResultMap" parameterClass="richText">
		select
		<include refid="baseColumn"/>
		from config_rich_text a
		where a.name_en = #nameEn# and a.terminal = #terminal#
	</select>
</sqlMap>