package com.shunwang.basepassport.config.dao;

import com.shunwang.baseStone.core.dao.BaseStoneIbatisDao;
import com.shunwang.basepassport.config.constants.ConfigEnum;
import com.shunwang.basepassport.config.pojo.UserOutInterface;
import org.springframework.cache.annotation.Cacheable;

import java.util.ArrayList;
import java.util.List;

public class UserOutInterfaceDao extends BaseStoneIbatisDao<UserOutInterface>{

	/**
	 * @see com.shunwang.baseStone.cache.keygenerator.ConfigUserOutInterfaceKeyGenerator
	 * @param interfaceId 接口id
	 */
	@Cacheable(value = "cache", keyGenerator = "configUserOutInterfaceKeyGenerator", unless = "#result==null")
	public UserOutInterface getById(String interfaceId) {
		return (UserOutInterface)getSqlMapClientTemplate().queryForObject(getStatementNameWrap("get"), interfaceId);
	}
	
	/**
	 * 查询前缀名list
	 * @see com.shunwang.baseStone.cache.keygenerator.ConfigUserOutInterfacePrefixListKeyGenerator
	 */
	@Cacheable(value = "cache_5", keyGenerator = "configUserOutInterfacePrefixListKeyGenerator", unless = "#result==null")
	public List<String> getPrefixNameList(){
		List<UserOutInterface> list = getSqlMapClientTemplate().queryForList(getStatementNameWrap("findAll"));
		List<String> prefixList = new ArrayList<>();
		list.forEach(item -> {
			if (item.getServiceState() != null && ConfigEnum.ConfigSwitch.ZERO_OPEN.getValue() == Integer.parseInt(item.getServiceState())) {
				prefixList.add(item.getPrefixName());
			}
		});
		return prefixList;
	}
	/**
	 * 查询app配置的第三方登录list
	 *
	 */
	public List<UserOutInterface> findAppOutInterface(String appid) {
		return getSqlMapClientTemplate().queryForList(getStatementNameWrap("findAppOutInterface"), appid);
	}

	/**
	 * 查询业务线配置的第三方登录list
	 *
	 */
	public List<UserOutInterface> findBusinessLineOutInterface(Integer businessLineId) {
		return getSqlMapClientTemplate().queryForList(getStatementNameWrap("findBusinessLineOutInterface"), businessLineId);
	}
}
