package com.shunwang.passport.member.web;

import com.shunwang.baseStone.core.exception.BaseStoneException;
import com.shunwang.basepassport.binder.common.ErrorCode;
import com.shunwang.basepassport.context.UserContext;
import com.shunwang.basepassport.detail.common.DetailContants;
import com.shunwang.basepassport.user.pojo.MemberAccountBind;

import java.io.IOException;

/**
 * User:pf.ma
 * Date:2018/06/13
 * Time:13:47
 */
public class AppleBindManageAction extends BindManageAction {

	@Override
	public void toBind() throws IOException {
		//无苹果登录，不应该走到这里
	}

	public void doUnbind(MemberAccountBind memberAccountBind){
		if(memberAccountBind.getApple().equals(memberAccountBind.getMemberId())){
			logger.info("apple账号[memberId:{}]是主体账号，不允许解绑",memberAccountBind.getMemberId()) ;
			throw new BaseStoneException(ErrorCode.C_1090.getCode(),ErrorCode.C_1090.getDescription());
		}
		memberAccountBind.beginBuildLog("apple账号解绑");
		memberAccountBind.getPersonalEditLog().setMember(UserContext.getMember()) ;
		memberAccountBind.getPersonalEditLog().setUserAdd(UserContext.getUserName());
		memberAccountBind.getPersonalEditLog().setType(DetailContants.FRONT);
		memberAccountBind.setApple(null);
		getMemberAccountBindDao().unbindApple(memberAccountBind) ;
	}

	@Override
	public String callback() {
		return SUCCESS;
	}
}
