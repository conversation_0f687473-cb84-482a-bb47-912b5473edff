<?xml version="1.0" encoding="UTF-8"?>
<web-app version="2.5" xmlns="http://java.sun.com/xml/ns/javaee"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://java.sun.com/xml/ns/javaee 
	http://java.sun.com/xml/ns/javaee/web-app_2_5.xsd">
	<display-name>sso</display-name>

	<context-param>
		<param-name>contextConfigLocation</param-name>
		<param-value>classpath*:/ssoContext.xml</param-value>
	</context-param>

    <context-param>
	    <param-name>logbackConfigLocation</param-name>
	    <param-value>file:${SSO_KEDOU_CONFIG_HOME}/logback.xml</param-value>
	</context-param>
	<listener>
	    <listener-class>ch.qos.logback.ext.spring.web.LogbackConfigListener</listener-class>
	</listener>
	<listener>
        <listener-class>com.shunwang.baseStone.sso.core.listenter.ServerInfoListener</listener-class>
    </listener>

	<!-- trace日志 -->
	<filter>
		<filter-name>Tracefilter</filter-name>
		<filter-class>com.shunwang.util.trace.Tracefilter</filter-class>
	</filter>
	<filter-mapping>
		<filter-name>Tracefilter</filter-name>
		<url-pattern>/*</url-pattern>
	</filter-mapping>
	
	<!-- 用于记录url的所以请求和所以的参数信息	-->
	<filter>
		<filter-name>urlFilter</filter-name>
		<filter-class>com.shunwang.basepassport.filter.InterfaceLogFilter</filter-class>
	</filter>
 	<filter-mapping>
		<filter-name>urlFilter</filter-name>
		<url-pattern>*.htm</url-pattern>
		<url-pattern>*.do</url-pattern>
		<url-pattern>*.jsp</url-pattern>
		<url-pattern>*.js</url-pattern>
	</filter-mapping>
	<!-- XSS过滤器，发现XSS跳转到错误	-->
	<filter>
		<filter-name>IPFilter</filter-name>
		<filter-class>com.shunwang.baseStone.sso.filter.IpFilter</filter-class>
	</filter>
	<filter-mapping>
		<filter-name>IPFilter</filter-name>
		<url-pattern>/*</url-pattern>
	</filter-mapping>
	<!-- XSS过滤器，发现XSS跳转到错误	-->
	<filter>
		<filter-name>XSSFilter</filter-name>
		<filter-class>com.shunwang.baseStone.sso.filter.XSSFilter</filter-class>
	</filter>
	<filter-mapping>
		<filter-name>XSSFilter</filter-name>
		<url-pattern>/*</url-pattern>
	</filter-mapping>
    <!-- 单点登录 -->
    <filter>
		<filter-name>userTocken</filter-name>
		<filter-class>com.shunwang.baseStone.sso.filter.UserTockenFilter</filter-class>
	</filter>
    <filter-mapping>
    	<filter-name>userTocken</filter-name>
    	<url-pattern>/*</url-pattern>
    </filter-mapping>
	<filter>
		<filter-name>outOuathFilter</filter-name>
		<filter-class>com.shunwang.baseStone.sso.filter.OutOuathFilter</filter-class>
	</filter>
	<filter-mapping>
		<filter-name>outOuathFilter</filter-name>
		<url-pattern>/getOutSiteOauthByServer.do</url-pattern>
		<url-pattern>/checkWxOpenLoginByServer.do</url-pattern>
	</filter-mapping>
	 <filter>
		<filter-name>struts2</filter-name>
		<filter-class>
			org.apache.struts2.dispatcher.filter.StrutsPrepareAndExecuteFilter
		</filter-class>
		<init-param>
			<param-name>config</param-name>
			<param-value>ssoStruts.xml</param-value>
		</init-param>
     </filter>
	  
	 <filter>
		<filter-name>sessionContext</filter-name>
		<filter-class>com.shunwang.baseStone.filter.SessionFilter</filter-class>
	</filter>
    <filter-mapping>
    	<filter-name>sessionContext</filter-name>
    	<url-pattern>/*</url-pattern>
    </filter-mapping>
	  
     <filter-mapping>
		<filter-name>struts2</filter-name>
		<url-pattern>/*</url-pattern>
     </filter-mapping>
   	
	<session-config>
		<session-timeout>20</session-timeout>
	</session-config>

    <welcome-file-list>
        <welcome-file>index.html</welcome-file>
        <welcome-file>index.jsp</welcome-file>
    </welcome-file-list>

	<error-page>
		<error-code>500</error-code>
		<location>/404.jsp</location>
	</error-page>
	<error-page>
		<error-code>404</error-code>
		<location>/404.jsp</location>
	</error-page>
</web-app>
