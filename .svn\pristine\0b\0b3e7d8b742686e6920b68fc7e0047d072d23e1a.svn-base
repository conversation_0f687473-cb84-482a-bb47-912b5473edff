.wx-container{
  width: 100%;
  min-height: 100%;
  position: relative;
}
/* 头部 */
.wx-container .wx-header{
  width: 100%;
  text-align: center;
}
.wx-container .wx-header .header{
  width:100%;
  height:51px;
  margin: 0 auto;
  background:#fff;
  box-shadow:0px 0px 8px 0px rgba(16,17,18,0.16);
}
/* 内容 */
.wx-container .wx-layout{
  width: 1200px;
  text-align: center;
  margin: 0 auto;
  padding-top: 249px;
  padding-bottom: 300px;
  color: #7A7F8A;
  font-size:14px;
}
.wx-container .wx-layout .tip{
  margin: 29px 0px 19px 0px;
  color: #303030;
  font-size: 20px;
}
.wx-container .wx-layout .btn button{
  width:140px;
  height:40px;
  background:#697DF4;
  color: #fff;
  margin-top: 60px;
  cursor: pointer;
}
/* 底部 */
.wx-container .wx-footer{
  width: 100%;
  height: 58px;
  padding: 20px 0px;
  position: fixed;
  bottom: 0px;
  left: 0;
  text-align: center;
  border-top:1px solid rgba(204,204,204,1);
  background: #fff;
}
.wx-container .wx-footer .footer{
  width: 1200px;
  margin: 0 auto;
  color:#989ba2;
  padding-top: 10px;
}
.wx-container .wx-footer .footer .link a{
  color:#989ba2;
  margin: 0 8px;
}
.wx-container .wx-footer .footer .a1{
  margin-bottom: 5px;
}
.wx-container .wx-footer .footer .link .s1{
  margin-left: 8px;
}
.wx-container .wx-footer .footer .a2{
  height: 24px;
  line-height: 24px;
}
.wx-container .wx-footer .footer .a2 .icon-1{
  position: relative;
  left: 8px;
  bottom: 3px;
}
.wx-container .wx-footer .footer .a2 .icon-2{
  position: relative;
  bottom: 2px;
}
.wx-container .wx-footer .footer .link a:hover{
  color:#000;
}
.wx-container .wx-footer .footer .link .s2{
  margin-left: 10px;
}