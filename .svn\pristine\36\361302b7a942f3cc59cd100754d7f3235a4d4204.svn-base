package com.shunwang.baseStone.sso.apapter;

import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.request.AlipaySystemOauthTokenRequest;
import com.alipay.api.request.AlipayUserInfoShareRequest;
import com.alipay.api.response.AlipaySystemOauthTokenResponse;
import com.alipay.api.response.AlipayUserInfoShareResponse;
import com.shunwang.baseStone.config.constants.ConfigOauthConstant;
import com.shunwang.baseStone.context.IPContext;
import com.shunwang.baseStone.core.exception.BaseStoneException;
import com.shunwang.baseStone.siteinterface.context.SiteContext;
import com.shunwang.baseStone.sso.constant.UserOutsiteConstant;
import com.shunwang.baseStone.sso.util.UploadFileUtil;
import com.shunwang.baseStone.useroutinterface.context.UseroutInterfaceContext;
import com.shunwang.basepassport.binder.common.ErrorCode;
import com.shunwang.basepassport.config.pojo.Oauth;
import com.shunwang.basepassport.user.common.MemberConstants;
import com.shunwang.basepassport.user.common.MemberUtil;
import com.shunwang.basepassport.user.common.UserLoginSessionUtil;
import com.shunwang.basepassport.user.pojo.Member;
import com.shunwang.basepassport.user.pojo.MemberAccountBind;
import com.shunwang.basepassport.user.pojo.MemberOutSite;
import com.shunwang.basepassport.weixin.constant.WeixinConstant;
import com.shunwang.util.StringUtil;
import com.shunwang.util.json.GsonUtil;
import com.shunwang.util.math.RandomUtil;
import com.shunwang.util.net.IpUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.struts2.ServletActionContext;

import java.io.IOException;
import java.math.BigInteger;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

public class AlipayAuthAdapter extends UserOutsiteApapter {
    /**
     * 支付宝回调时的参数
     */
    private String auth_code;
    private String app_id;

    private static final String V_SCOPE_AUTH_USER = "auth_user";

    private static final String GRANT_TYPE = "authorization_code";

    private static final String GATE_WAY_URL = "https://openapi.alipay.com/gateway.do";
    private static final String OAUTH_URL = "https://openauth.alipay.com/oauth2/publicAppAuthorize.htm";

    private String userId;

    protected void initContextInfo() {
        //初始 ip context 和 siteContext
        IPContext.setIp(IpUtil.getIpAddress(ServletActionContext.getRequest()));
        SiteContext.setSiteId(site_id);
    }

    @Override
    public String getInterfaceId() {
        return UserOutsiteConstant.ALIPAY_INTERFACE_ID;
    }

    @Override
    protected String getUserKeyPrefix() {
        return WeixinConstant.QRSCENE_ALI_PRE;
    }

    /**
     * 第一步
     * 进入授权页面并获取到code
     */
    @Override
    public String goToOauth() throws Exception {
        Oauth configOauth = getOauthConfig() ;
        String userSceneKey = StringUtil.isBlank(innerScene) ? buildCacheUserKey() : innerScene;
        cacheExtData(userSceneKey, 30 * 60);
        String redirectUrl = concatRedirectUrl(getWebCallbackUrl(), userSceneKey, true);

        return OAUTH_URL + "?" + "app_id=" + configOauth.getAppId() + "&" +
                SCOPE + "=" + V_SCOPE_AUTH_USER + "&" + REDIRECT_URI + "=" + redirectUrl + "&" +
                STATE + "=" + UUID.randomUUID();
    }

    @Override
    public Map<String, Object> getOauthByServer() throws Exception {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put(REDIRECT_OAUTH_URL, goToOauth());
        return resultMap;
    }

    @Override
    protected void cacheExtData(String key, Integer expireSeconds) {
        super.cacheExtData(key, expireSeconds);
        if (StringUtil.isNotBlank(appId)) {
            redisOperation.set(WeixinConstant.EXT_SCENE_KEY_APPID + key, appId, expireSeconds);
        }
    }

    @Override
    public String oauthCallback() {
        try {
            //从缓存还原接入方透传的数据
            pullCacheExtData(userSceneKey);

            initContextInfo();
            loadPrivacyCss();
            // 获取第三方配置
            Oauth configOauth = getOauthConfig() ;
            AlipayClient alipayClient = new DefaultAlipayClient( GATE_WAY_URL, app_id, configOauth.getMerchantPrivateKey(),
                    "json", "GBK", configOauth.getPublicKey(), "RSA2");
            //支付宝code换取token
            AlipaySystemOauthTokenRequest alipaySystemOauthTokenRequest = new AlipaySystemOauthTokenRequest();
            alipaySystemOauthTokenRequest.setCode(getAuth_code());
            alipaySystemOauthTokenRequest.setGrantType(GRANT_TYPE);
            AlipaySystemOauthTokenResponse alipaySystemOauthTokenResponse = alipayClient.execute(alipaySystemOauthTokenRequest);
            if (alipaySystemOauthTokenResponse == null || !alipaySystemOauthTokenResponse.isSuccess()) {
                LOG.error("支付宝codeToToken返回数据[{}]异常", GsonUtil.toJson(alipaySystemOauthTokenResponse));
                throw new BaseStoneException(ErrorCode.C_1086);
            }
            LOG.info("支付宝获取accessToken返回数据:[{}],code[{}]", alipaySystemOauthTokenResponse.getCode(), alipaySystemOauthTokenResponse.getMsg());
            //支付宝token获取用户头像昵称等信息
            AlipayUserInfoShareRequest alipayUserInfoShareRequest = new AlipayUserInfoShareRequest();
            AlipayUserInfoShareResponse alipayUserInfoShareResponse = alipayClient.execute(alipayUserInfoShareRequest, alipaySystemOauthTokenResponse.getAccessToken());
            if (alipayUserInfoShareResponse == null || !alipayUserInfoShareResponse.isSuccess()) {
                LOG.error("支付宝token换用户返回数据[{}]异常", GsonUtil.toJson(alipaySystemOauthTokenResponse));
                throw new BaseStoneException(ErrorCode.C_1087);
            }
            LOG.info("支付宝获取userInfo返回数据:[{}],code[{}]", alipayUserInfoShareResponse.getCode(), alipayUserInfoShareResponse.getMsg());

            userId = alipaySystemOauthTokenResponse.getUserId();
            headImage = alipayUserInfoShareResponse.getAvatar();
            nickName = alipayUserInfoShareResponse.getNickName();

            memberOutSite = buildMemberOutSite();
            member = queryMemberBy(memberOutSite.getOutMemberId());
            // 外部用户不存在，为其生成
            if (member == null) {
                member = outSiteMemberRegister(memberOutSite);
                member.setHeadImg(UploadFileUtil.saveNetHeadImgToPath(member.getMemberId(), headImage));
                interfaceService.updateMember(member);
            } else if (member.getMemberState() == MemberConstants.USER_CANCEL_STATE) {
                setMsg("您的账号已被注销！");
                memberName = member.getMemberName();
                return inputCancelView();
            } else {
                updateHeadAndNick();
            }
            member.setOpenId(userId);
            //处理登录来源
            //获取请求二维码的原始客户端ip到登录日志
            member.setClientIp(clientIp);
            memberOutSite.setMemberFrom(StringUtil.isBlank(cacheResult) ? memberOutSite.getMemberFrom() : UserOutsiteConstant.ALIPAY_QR_INTERFACE_ID);
            MemberUtil.doLogin(member, memberOutSite);
            initCss(super.getSite_id());
            memberName = member.getMemberName();
            UserLoginSessionUtil.saveSession(memberName, UserLoginSessionUtil.LoginType.SSO_OUT.getType() + getInterfaceId(), null, site_id);
            if (StringUtil.isNotBlank(visitType) && visitType.equals("bind")) {
                responseVisitType();
                return SUCCESS;
            }
            initLoginElement(super.getSite_id());
            if (isSingleAccount) {
                String toBind = goSingleBind(memberOutSite.getNickName(), memberOutSite.getHeadImg());
                if (!NO_RETURN.equalsIgnoreCase(toBind)) {
                    return toBind;
                }
                UserLoginSessionUtil.saveMasterSession(memberName, member.getMemberName(), UserLoginSessionUtil.LoginType.SSO_OUT.getType() + getInterfaceId(), null, site_id);
            }
            login(member);
            return WeixinConstant.MULTI_TERMINAL_LOGIN_JUMP.equals(cacheResult) ? qrSuccess() : SUCCESS;
        } catch (Exception e) {
            LOG.error("获取支付宝用户信息过程出错:{}", e.getMessage(), e);
            try {
                getResponse().sendRedirect(callbackUrl);
                return null;
            } catch (IOException e1) {
                LOG.error("跳转[{}]异常", callbackUrl, e);
            }
        }
        return INPUT;
    }

	@Override
	protected MemberAccountBind getByMemberId() {
		return memberAccountBindDao.getByAlipay(member.getMemberId());
	}

	@Override
	protected Integer getOutOauthType() {
		return ConfigOauthConstant.TYPE.ALIPAY.getInt();
	}

	@Override
	protected String getOutOauthLogName() {
		return "支付宝";
	}

	@Override
	protected MemberAccountBind buildMemberAccountBind() {
		MemberAccountBind memberAccountBind = super.buildMemberAccountBind();
        memberAccountBind.beginBuildLog("Alipay帐号绑定");
		memberAccountBind.setAlipay(member.getMemberId());
		return memberAccountBind;
	}

    /**
     * 解析获取到的用户信息
     * @return 第三方帐号对象
     */
    private MemberOutSite buildMemberOutSite() {
        MemberOutSite memberOutSite = new MemberOutSite();
        memberOutSite.setOutMemberId(userId);
        memberOutSite.setMemberName(generalMemberName(userId));
        memberOutSite.setOutMemberName(memberOutSite.getMemberName());
        memberOutSite.setNickName(StringUtils.isBlank(nickName)?"请自定义昵称":nickName);
        memberOutSite.setRegFrom(site_id);
        memberOutSite.setVersion(version);
        memberOutSite.setEnv(env);
        memberOutSite.setRemark(extData);
        memberOutSite.setMemberFrom(UserOutsiteConstant.ALIPAY_INTERFACE_ID);
        if (StringUtil.isNotBlank(headImage)) {
            memberOutSite.setHeadImg(headImage);
        }
        return memberOutSite;
    }

    /**
     * 前缀+unionId + random(6) 转36位字符
     * @param unionId 第三方id
     * @return 用户名
     */
    public String generalMemberName(String unionId) {
        BigInteger integer = new BigInteger(unionId + RandomUtil.getRandomStr(6), 10);
        return "ali_" + integer.toString(36);
    }

    private void updateHeadAndNick() {
        Member paramMember = null;
        if (StringUtil.isBlank(member.getHeadImg()) && StringUtil.isNotBlank(headImage)) {
            paramMember = new Member();
            paramMember.setMemberId(member.getMemberId());
            member.setHeadImg(UploadFileUtil.saveNetHeadImgToPath(member.getMemberId(), headImage));
            paramMember.setHeadImg(member.getHeadImg());
        }
        if (!StringUtil.isBlank(nickName) && !nickName.equals(member.getNickName())) {
            if (null == paramMember) {
                paramMember = new Member();
                paramMember.setMemberId(member.getMemberId());
            }
            paramMember.setMemberName(member.getMemberName());
            paramMember.setNickName(nickName);
        }

        if (paramMember != null) {
            interfaceService.updateMember(paramMember);
        }
    }
	
	private String getWebCallbackUrl() {
		useroutInterface=UseroutInterfaceContext.getUseroutInterfaceById(getInterfaceId());
        LOG.info("登录方式从数据库中获取的回调地址是:[{}]", useroutInterface.getCallbackURL());
		return useroutInterface.getCallbackURL();
	}

    public String getAuth_code() {
        return auth_code;
    }

    public void setAuth_code(String auth_code) {
        this.auth_code = auth_code;
    }

    public String getApp_id() {
        return app_id;
    }

    public void setApp_id(String app_id) {
        this.app_id = app_id;
    }

}
