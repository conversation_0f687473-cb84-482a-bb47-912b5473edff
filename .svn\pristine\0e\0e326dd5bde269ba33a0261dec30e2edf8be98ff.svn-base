<%@ page contentType="text/html;charset=UTF-8"%>
<%@ include file="/common/taglibs.jsp"%>
<table cellpadding="0" cellspacing="0">
    <thead>
    <tr>
        <th class="w_lg"></th>
        <td>
                <span id="showInfo" class="form_error_bar">
                    <c:if test="${!empty errorMsg}">
                        <img src="<c:url value='/images/front/error.gif'/>" /><font color="red">${errorMsg}</font>
                    </c:if>
                </span>
        </td>
    </tr>
    </thead>
    <tbody>
    <tr>
        <th>新的手机号码：</th>
        <td>
            <input type="text" class="form_input"  style="width:200px" id="newMobile" name="newMobile" tabindex="1" value="${newMobile}" maxlength="16" onkeyup="unableSpaceKey('newMobile');" />
            <a href="###" class="btn_default_md" id="sent-code">发送验证码</a>
        </td>
    </tr>
    <tr>
        <th>手机短信验证码：</th>
        <td>
            <input type="text" class="form_input" style="width:200px" id="smsCheckCode" name="smsCheckCode" tabindex="3" value="" maxlength="6" onkeyup="unableSpaceKey('smsCheckCode');" /></p>
        </td>
    </tr>
    <tr>
        <th></th>
        <td>
            <a href="###" class="btn_default_lg" onclick="modifyMobile();">确定</a></td>
    </tr>
    </tbody>
</table>
    <script src="${staticServer}/scripts/front/member/gt.js" type="text/javascript" ></script>
    <script src="${staticServer}/scripts/front/member/gtUtil.js" type="text/javascript"></script>
	<script type="text/javascript">
        function buildGtData(that, data) {
            data.geetest_challenge =  that.find("input[name='geetest_challenge']").val();
            data.geetest_validate = that.find("input[name='geetest_validate']").val();
            data.geetest_seccode = that.find("input[name='geetest_seccode']").val();
        }
		function send() {
			clearInfo();
			var data ={
			    "memberName":"${appeal.userName}",
                "newMobile":$("#newMobile").val()
			};
			buildGtData($("#changMobileForm"), data);
			$.ajax( {
                url :"${appServer}/front/noLogin/sendCheckCode_front.htm",
                data : data,
                dataType : 'json',
                type : 'post',
                traditional:true,
                success : function(json) {
                    if(!json.errorMsg){
                        var sendAgain=document.getElementById("sent-code");
                        sendAgain.disabled=true;
                        sec=60;
                        showSendAgain();
                    } else {
                        showInfo(json.errorMsg);
                    }
                },
                error : function() {
                    timeOut();
                }
             });
		}

		function showSendAgain(){
		    var sendAgain=document.getElementById("sent-code");
		    if(null != sendAgain) {
			    if(sec > 0){
			        sendAgain.disabled=true;
			        sendAgain.className="chk_time";
                    $("#sent-code").html(sec.toString()+"后重新获取");
                    sendAgain.style.color="#494949";
			        sec = sec-1;
			        $("#second").val(sec);
			        setTimeout("showSendAgain()",1000); 
			    } else {
                    sendAgain.className="btn_default_md";
                    sendAgain.style.color="#FFFFFF";
                    sendAgain.disabled=false;
                    $("#sent-code").html("重新获取验证码 ");
			    }
		    }
		}
		
		function showInfo(showCode){
		    var showInfo = document.getElementById("showInfo");
		    showInfo.innerHTML='<img src="${staticServer}/images/front/error.gif" /><font color="red">' + showCode +'</font>';
		 }
		
		function clearInfo(){
		    var showInfo = document.getElementById("showInfo");
		    showInfo.innerHTML='';
		}

        //以下极验业务---------------------------
        var showGt = "true" == '${showGt}';
        var gtRegisterUrl = "/front/common/gtRegister.htm?r=" + new Date().getTime();
        var sendGt = $.gtUtil({
                "showGt": showGt,
                "formId": "changMobileForm",
                "gtRegisterUrl": gtRegisterUrl,
                "btnId": "sent-code"
            },
            function () {
                send();
            },
            function() {
                return checkMobile();
            },
            function (msg) {
                showInfo(msg);
            }
        );
	</script>
