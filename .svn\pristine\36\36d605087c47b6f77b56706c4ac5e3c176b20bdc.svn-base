package com.shunwang.basepassport.filesystem.response;

import com.google.gson.annotations.Expose;
import com.shunwang.baseStone.core.response.BaseStoneResponse;


public class FileUploadResponse extends BaseStoneResponse {

    @Expose
    private String path;

    public FileUploadResponse(String path) {
        this.path = path;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }
}
