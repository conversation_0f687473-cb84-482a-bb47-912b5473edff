package com.shunwang.baseStone.sso.exception;

import com.shunwang.baseStone.core.exception.BaseStoneException;
/**
 * @Description:窜号or欠费用户提示异常
 * <AUTHOR>  create at 2011-10-25 上午09:44:53
 * @FileName com.shunwang.baseStone.sso.exception.UserCheckFailExp.java
 */
public class UserCheckFailExp  extends BaseStoneException{

	/**
	 * <AUTHOR> create at 2011-10-24 下午06:13:49 
	 */
	private static final long serialVersionUID = -612834557996488959L;
	
	public UserCheckFailExp() {
		super("500", "检测到通行证窜号或欠费，请联系客户经理！");
	}
}
