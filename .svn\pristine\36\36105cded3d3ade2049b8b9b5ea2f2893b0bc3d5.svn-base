package com.shunwang.baseStone.sso.simpleImport;

import com.shunwang.util.encrypt.AesEncrypt;
import com.shunwang.util.encrypt.Md5Encrypt;
import com.shunwang.util.net.HttpClientUtils;
import org.checkerframework.checker.units.qual.A;
import org.junit.Test;

import java.io.UnsupportedEncodingException;
import java.math.BigInteger;
import java.nio.charset.Charset;
import java.util.HashMap;
import java.util.Map;

/**
 * User:pf.ma
 * Date:2017/04/05
 * Time:16:24
 */
public class IdCardTest extends BaseTest{

//	String userId = "610203198606179384" ;
	String userId = "610121199810061879";

	@Test
	public void Test() throws UnsupportedEncodingException {
		String extInfo = "{\"barId\":\"jf123\",\"name\":\"刘**\"}";
		Map<String, String> params = new HashMap<>();
		params.put("siteId", siteId);
		params.put("timestamp", timestamp);
		params.put("signVersion", signVersion);
		params.put("clientIp", clientIp);
		params.put("userId", encryptUserId());
//		params.put("userId", userId);
		params.put("extInfo",extInfo);
		params.put("sign", getSign(params));
		String respond = HttpClientUtils.doPost("https://sso.kedou.com/createSimpleUserByIDCard.do", params, header,null, Charset.defaultCharset());
		System.out.println(respond);
	}

	public String encryptUserId() {
		//JF+upper(md5(upper(data)+key))   key=J!@F^3Sa21fe
		String key = "J!@F^3Sa21fe";
		return "JF" + Md5Encrypt.encrypt(userId.toLowerCase() + key).toUpperCase();
	}

	public String buildSignString() {
		return userId+"|"+siteId+"|"+timestamp+"|"+md5Key ;
	}


	public static void main(String[] args) {
		String key = "J!@F^3Sa21fe";
		String userIdEncrypt = "JF" + Md5Encrypt.encrypt("610203198606179384" + key).toUpperCase();

		System.out.println(userIdEncrypt);
		userIdEncrypt = userIdEncrypt.replace("JF","");
//		long l = Long.parseLong(userIdEncrypt,16);
//		System.out.println(l);
		BigInteger bi = new BigInteger(userIdEncrypt, 16);
		System.out.println("id_" + bi.toString(36));

		String userIdTail = "123456";
		String aesKey = "!@#$%^&*swkjkd.,";
		String aesTail = null;
		try {
			aesTail = AesEncrypt.Encrypt(userIdTail, aesKey);
		} catch (Exception e) {
			e.printStackTrace();
		}
		System.out.println("userIdTail:\t" + userIdTail);
		System.out.println("aesKey:\t\t" + aesKey);
		System.out.println("aesTail:\t" + aesTail);
	}
}
