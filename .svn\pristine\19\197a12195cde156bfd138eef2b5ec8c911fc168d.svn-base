package com.shunwang.basepassport.user.pojo;

import java.io.Serializable;

import com.shunwang.baseStone.context.BaseStoneContext;
import com.shunwang.baseStone.core.pojo.BaseStoneObject;
import com.shunwang.basepassport.user.dao.MemberProtectedQuestionDao;

/**
 * @Description:用户密保问题
 * <AUTHOR>  create at 2011-7-26 下午01:20:27
 * @FileName com.shunwang.basepassport.user.pojo.MemberProtectedQuestion.java
 */
public class MemberProtectedQuestion extends BaseStoneObject{
	/**
	 * <AUTHOR> create at 2011-7-26 下午01:30:21 
	 */
	private static final long serialVersionUID = -3899945487639647019L;
	/**
	 * @YHJ 用户密保问题
	 */
	private ProtectedQuestion [] protectedQuestions=new ProtectedQuestion[3];
	
	private Member member;

	public void setProtectedQuestions(ProtectedQuestion [] protectedQuestions) {
		this.protectedQuestions = protectedQuestions;
	}

	public ProtectedQuestion [] getProtectedQuestions() {
		return protectedQuestions;
	}
	/**
	 * @Description:校验密保问题是否有效
	 * @return
	 * <AUTHOR> create at 2011-7-26 下午01:43:36
	 */
	public boolean isAvailable(){
		for(ProtectedQuestion question:protectedQuestions){
			if(null == question)
				return false;
			if(!question.isAvailable())
				return false;
		}
		return true;
	}

	@Override
	public Serializable getPk() {
		return null;
	}

	public void setMember(Member member) {
		this.member = member;
	}

	public Member getMember() {
		return member;
	}
	public void save(){
		getDao().save(this);
	}
    public MemberProtectedQuestionDao getDao(){
    	return (MemberProtectedQuestionDao)BaseStoneContext.getInstance().getBean("memberProtectedQuestionDao");
    }
	
	
}
