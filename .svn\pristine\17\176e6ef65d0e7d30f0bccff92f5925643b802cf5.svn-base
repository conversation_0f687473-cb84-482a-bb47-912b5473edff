<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">
<!--	<bean id="cssDao" class="com.shunwang.baseStone.css.dao.CssDao"  >-->
<!--		<property name="dbDao" ref="cssDbDao"></property>-->
<!--		<property name="messageProducer" ref="cacheMessageProducer"/>-->
<!--	</bean>-->
<!--	-->
<!--	<bean id="cssDbDao" class="com.shunwang.baseStone.css.dao.CssDbDao"  >-->
<!--		<property name="sqlMapClient" ref="baseStone.sqlMapClient"></property>-->
<!--	</bean>-->
	
</beans>
