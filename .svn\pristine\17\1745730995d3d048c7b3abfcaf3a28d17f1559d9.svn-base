package com.shunwang.baseStone.context;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * ****************************
 * 版权所有：顺网科技 保留所有权利
 * 创建日期: 2011-8-23
 * 创建作者：mj.guan
 * 文件名称：
 * 版本： 1.0
 * 功能：上下文
 * 最后修改时间：
 * 修改记录：
****************************************
 */
public class SessionContext {
	private static ConcurrentHashMap<String,Object> sessionMap = new ConcurrentHashMap<String,Object>();
	private static ThreadLocal<String> sessions = new ThreadLocal<String>();
	protected static String getCacheId(String session){
		StringBuffer sb = new StringBuffer();
		sb.append("session[");
		sb.append(session);
		sb.append("]");
		return sb.toString();
	}

	protected static ConcurrentHashMap<String,Object> getMap(){
		String sessionId = sessions.get();
		if(sessionId==null){
			return sessionMap;
		}

		ConcurrentHashMap<String,Object> map = RedisContext.getRedisCache().get( getCacheId(sessionId),ConcurrentHashMap.class);

		if(map==null){
			map = new ConcurrentHashMap<String,Object>();
		}
		return map;
	}
	/**
	 * 放置值
	 * @param key
	 * @param value
	 */
	public static void put(String key,Object value){
		ConcurrentHashMap<String,Object> map = getMap();
		map.put(key, value);
		saveMap(map);
		
		
	}
	
	
	protected static void saveMap(ConcurrentHashMap<String,Object> map){
		String session = sessions.get();
		if(session!=null){
			RedisContext.getRedisCache().set(getCacheId(session),map, 20, TimeUnit.MINUTES);
		}
	}
	
	/**
	 * 取得值
	 * @param key
	 * @return
	 */
	public static Object  get(String key){
		return getMap().get(key);
	}
	
	/**
	 * 删除值
	 * @param key
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public static Object  remove(String key){
		ConcurrentHashMap<String,Object> map = getMap();
		Object ret = map.remove(key);
		saveMap(map);
		return ret;
	}
	
	public static void setSessionId(String session){
		sessions.set(session);
	}

}
