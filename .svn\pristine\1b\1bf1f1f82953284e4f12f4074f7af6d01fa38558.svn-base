
/**
 * 刷新检验码
 */
function refreshCheckcode(){
    var nowTime = new Date();
    $("#checkCodeImg").attr("src", "checkCode.do?=t"+nowTime.getTime());
}

function refreshSmsCheckcode() {
	var nowTime = new Date();
	$("#checkCode2").attr("src", "smsCheckCode.do?=t"+nowTime.getTime());
}

/**
 * login 按钮被点击
 */
function loginDefaultClick(actionUrl){

}

function loginClick() {
    loginDefaultClick("/login.do");
}

function loginFormLogin() {
    if (!loginFormLoginCheck(true)) {
        return false;
    }
    $('#login-form').target = "";
    $('#login-form').attr("action", "/login.do");
    $('#login-form').submit();
};

function chkEmail(strEmail) {
    if (!/^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/.test(strEmail)) {
        return false;
    }
    else {
        return true;
    }
}

function loginFormLoginCheck(isCleanPwd){
    var pswd = $('#password');
    var passwordInp = $('#passwordInp');
    var md5 =  $('#md5');
    var userName =  $('#userName');
    var userNameInp =  $('#userNameInp');
    var agreementCheckbox =  $('#agreementCheckbox');
    var pwdi = passwordInp.val();

    if(pwdi == ""
        || pwdi=="请输入登录密码"){
        showTipsError("请输入登录密码");
        return false;
    }
    if (isWeakPwd(pwdi) || pwdi == userName) {
        $('#weakPwdState').val("1");
    } else {
        $('#weakPwdState').val("2");
    }

    var hexMd5 = hex_md5(pwdi);
    var rsaEnc = RSAEnc(hexMd5);
    pswd.val(rsaEnc);
    if (isCleanPwd) {
        passwordInp.val("");
    }
    md5.val(true);


    var userNameVal = userNameInp.val();
    if(userNameVal == ""
        || userName.val()=="用户名/邮箱/手机号"){
        showTipsError("请输入用户名/邮箱/手机号");
        return false;
    }
    if (chkEmail(userNameVal)) {
        userNameInp.val(userNameVal.toLowerCase());
    }
    userName.val(userNameInp.val());

    if (agreementCheckbox.checked == false) {
        showTipsError("请确认相关条款");
        return false;
    }
    return true;
}

var  CONSTANTS = {
    SEC: 60,
    SEND_ACTIVENO_TIME: 'sso_login_mobile_send_time',
    HAS_SEND:false
};

//倒计时
var wait=60;
function time(o) {
    if (wait == 0) {
        o.prop("disabled",false).text("获取验证码").removeClass("disabled").addClass("btn-primary");
        wait = 60;
    } else {
        o.prop("disabled", true).text(wait+"秒后重新获取").removeClass("btn-primary").addClass("disabled");
        wait--;
        setTimeout(function() {
            time(o)
        },1000)
    }

}

function isMobileNo(s){
	var reg = "";
	if (typeof(validPhoneRex)==="undefined" || validPhoneRex == "" || validPhoneRex == null) {
		reg = /^1[34578]\d{9}$/;
	} else{
		reg = validPhoneRex;
	}
    if(reg.exec(s)) return true;
    else return false;
}

function checkMobileCode(mobileCheckCode) {
    var mobileCheckCode = $.trim(mobileCheckCode);
    if (mobileCheckCode.length == 0) {
        showTipsError("请输入短信验证码");
        return false;
    }
    var patn = /^[0-9]{6}$/;
    if (!patn.test(mobileCheckCode)) {
        showTipsError("验证码由6位数字组成");
        return false;
    }
    $("#mobileActiveNo").val(mobileCheckCode);
    return true;
}

function checkMobile(number) {
    var number = $.trim(number);

    if (number.length == 0) {
        showTipsError("请输入您的手机号码");
        return false;
    }
    if (!isMobileNo(number)) {
        showTipsError("手机号码格式错误，请重新输入");
        return false;
    }
    $("#mobile").val(number);
    return true;
}

function checkAgreement() {
    if ($("#agreementCheckbox").prop("checked") == true)
        return true;

    showTipsError("请确认相关条款");
    return false;
}

function showTipsError(msg) {
    $("#tips-error p").html(msg);
    $('#tips-error').show();
}

function hideTipsError() {
    $("#tips-error p").html('');
    $('#tips-error').hide();
}

function showTipsInfo(msg) {
    $("#tips-info").html(msg);
    $("#tips-info").show();
}


//密码明文显示
$("#password-show").on("click",function(){
    if($("#password-show").prop("checked")){
        $("#passwordInp").attr("type","text")
    }
    else{
        $("#passwordInp").attr("type","password")
    }
});

//隐私协议勾选框
$(".agreement_chk").on('click', function () {
    $("#agreementCheckbox").trigger('click');
});

function restoreSendState() {
    var hasSend = false;
    if (!!smsSendExpTime && smsSendExpTime > 0) {
        wait = smsSendExpTime;
        time($("#sent-code"));
        hasSend = true;
    }
    if(!hasSend) {
        $('#mobileCheckCode').attr('disabled', true)
    }

}

function numberInputCheck() {
    var number = $("#number").val();
    if(!checkMobile(number)) {
        $("#number").focus();
        return false;
    }
    hideTipsError();
    return true;
}
//发送验证码点击事件
function sendAgain(callback){
    var geetest_challenge = $("#geetest_challenge").val();
    var geetest_validate = $("#geetest_validate").val();
    var geetest_seccode = $("#geetest_seccode").val();

    var number = $("#number").val();
    if(!checkMobile(number)) {
        $("#number").focus();
        return false;
    }

    hideTipsError();
    $("#sent-code").prop("disabled", true);
    var timestamp = Date.parse(new Date().toString());
    try {
        window.sessionStorage.setItem(CONSTANTS.SEND_ACTIVENO_TIME, timestamp);
        window.sessionStorage.setItem(CONSTANTS.HAS_SEND, true);
    } catch (e) {
        console.error(e);
    }
    $('#mobileCheckCode').removeAttr('disabled');

    var aj = $.ajax( {
        url:'/sendActiveNo.do',// 跳转到 action
        data:{
            number : number,
            site_id:$("#login-form").find("input[name='site_id']").val(),
            env:$("#login-form").find("input[name='env']").val(),
            geetest_challenge:geetest_challenge,
            geetest_validate:geetest_validate,
            geetest_seccode:geetest_seccode
        },
        type:'post',
        cache:false,
        dataType:'json',
        success:function(data) {
            if(!data.result){
                if(data.msg=='同一业务同一手机号码1分钟内只能发一次短信验证码！'){
                    data.msg="短信发送过于频繁，请稍后再试";
                }
                showTipsError(data.msg);
                $("#sent-code").prop("disabled", false);
            } else {
                wait = 60;
            	time($("#sent-code"));
            }
            if (data['isReg'] == 'true' || data['isReg'] == true) {
                $("#agreementCheckbox").attr('checked', true);
            }
            callback&&callback();
        },
        error : function(e) {
            // view("异常！");
            showTipsError("异常！");
            $("#sent-code").prop("disabled", true);
        }
    });

}

//短信登录验证
$("#tel-login").click(function (e) {
    var mobileCheckCode = $("#mobileCheckCode").val();
    var number = $("#number").val();
    if(!checkAgreement() || !checkMobile(number) || !checkMobileCode(mobileCheckCode))
        return false;
    e.preventDefault();

    $("#login-form").attr("action","/outMobileConfirm.do");
    $("#login-form").submit();
});

$('header.header-bar .back').on('click', function() {
    try {
        window.SWMobileSDK.onClose();
    } catch (e) {
		try{
			window.webkit.messageHandlers.onClose.postMessage({body:''});
		}catch(ex){
			window.history.back();
		}
    }
});


//短信登录验证
$("#choiseAccount").click(function (e) {
    $("#login-form").attr("action","/bindAsLoginAccount.do");
    $("#login-form").submit();
})

// tips-info close
function tipClose(){
    $(".tips-info").fadeOut(300)
}


function cleanTip() {
    $(".drop").each(function () {
        $(this).removeClass('on');
    });
}
function bIsAndroid() {
    var sUserAgent = navigator.userAgent.toLowerCase();
    var bIsAndroid = sUserAgent.match(/android/i) == "android";
    return bIsAndroid;
}
//解决微信公账号打开登录页面白屏问题
function bIsWeixin() {
    var ua = window.navigator.userAgent.toLowerCase();
    if ((ua.match(/MicroMessenger/i) == 'micromessenger')) {
        return true;
    } else {
        return false;
    }
}

function goToOtherSite(siteId) {
    cleanTip();
    document.getElementById("loginSiteId").value = siteId;
    document.getElementById("login-form").action = "/goOutOauth.do";
    if(bIsWeixin() || !bIsAndroid()){ //IOS统一新窗体
        document.getElementById("login-form").target = "_blank";
    }else {
        var linkTgt = $("#linkTgt").val();
        document.getElementById("login-form").target = linkTgt;
    }
    document.getElementById("login-form").submit();
}

/**
 * 检测是否为弱密码
 * @param pwdi
 * @returns {boolean}
 */
function isWeakPwd(pwdi) {
    if (!/^[A-Za-z0-9`~!@#\$%\^&\*\(\)\-_=\+\[\{\]\}\\\|;:'"",<.>\/\?]+$/.test(pwdi)) {
        return true;
    }
    if (!/^(?![A-Za-z]+$)(?!\d+$)(?![`~!@#\$%\^&\*\(\)\-_=\+\[\{\]\}\\\|;:'"",<.>\/\?]+$)\S{2,16}$/.test(pwdi)) {
        return true;
    }
    return false;
}

$(".btn").on("touchstart",function(){
    $(this).addClass(".active")
}).on("touchend",function(){
    $(this).removeClass(".active")
})

restoreSendState();

