package com.shunwang.basepassport.binder.web;

import com.shunwang.baseStone.context.BaseStoneContext;
import com.shunwang.baseStone.context.TransactionContext;
import com.shunwang.baseStone.core.action.BaseStoneAction;
import com.shunwang.basepassport.binder.common.BinderConstants;
import com.shunwang.basepassport.binder.dao.BinderDao;
import com.shunwang.basepassport.binder.pojo.*;
import com.shunwang.basepassport.commonExp.SystemExp;

import java.util.Date;

/**
 * 用来更新发送短信之后的一系列表
 *
 * <AUTHOR>
 * @date 2018/12/28
 * @see MobileBinder#send()
 **/
public class SendBinderUpdateAction extends BaseStoneAction {
    private Integer memberId;
    private String sendMobile;
    private String number;
    private String activeNo;//验证码
    private Long firstActiveTime;
    private String sendNumber;
    private String businessType;
    private Integer doType;
    private String terminal;
    private String accessSiteId;

    @Override
    public void process() throws Exception {
        SendBinder sendBinder = new MobileBinder();
        sendBinder.setMemberId(memberId);
        sendBinder.setSendMobile(sendMobile);
        sendBinder.setNumber(number);
        sendBinder.setActiveNo(activeNo);
        sendBinder.setSendTime(new Date());
        if (firstActiveTime != null && firstActiveTime != 0) {
            sendBinder.setFirstActiveTime(new Date(firstActiveTime));
        }
        sendBinder.setBusinessType(businessType);
        sendBinder.setTerminal(terminal);

        try {
            TransactionContext.beginTransaction();
            PersonalSendNumber personalSendNumber = buildPersonalSendNumber();
            updateSendInfo(sendBinder);
            personalSendNumber.save();
            TransactionContext.commitTran();
        } catch (Exception e) {
            TransactionContext.rollbackTran();
            log.error("发送验证码异常", e);
            throw new SystemExp();
        }
    }

    private void updateSendInfo(SendBinder sendBinder) {
        sendBinder.setSendNumber(sendBinder.getNumber());
        sendBinder.setNumber(null);

        SendBinder binder = null;

        if (!BinderConstants.MOBLIE_REGISTER.equals(this.getBusinessType()) && !BinderConstants.MOBILE_SINGLE_ACCOUNT_BIND.equals(this.getBusinessType())) {
            binder = (SendBinder) getBinderDao().getById(getMemberId());
        } else {
            binder = (SendBinder) getBinderDao().getRegitsterBinderByNumber(sendBinder.getSendNumber());
        }
        if (binder == null) {
            getBinderDao().save(sendBinder);
        } else {
            if (sendBinder.getMemberId() == -1) {
                getBinderDao().updateTempRecord(sendBinder);
            } else {
                getBinderDao().update(sendBinder);
            }
        }
    }

    private PersonalSendNumber buildPersonalSendNumber() {
        PersonalSendNumber personalSendNumber = new PersonalSendNumber();
        personalSendNumber.setDoType(this.getDoType());
        personalSendNumber.setFromType(Integer.parseInt(this.getBusinessType()));
        personalSendNumber.setNumber(this.getNumber());
        personalSendNumber.setMemberId(this.getMemberId());
        personalSendNumber.setSendTime(new Date());
        personalSendNumber.setSiteId(getAccessSiteId());
        return personalSendNumber;
    }

    private BinderDao<Binder> getBinderDao() {
        return (BinderDao<Binder>) BaseStoneContext.getInstance().getBean("mobileBinderDao");
    }

    @Override
    public String getSiteName() {
        return "短信表更新";
    }

    public Integer getMemberId() {
        return memberId;
    }

    public void setMemberId(Integer memberId) {
        this.memberId = memberId;
    }

    public String getSendMobile() {
        return sendMobile;
    }

    public void setSendMobile(String sendMobile) {
        this.sendMobile = sendMobile;
    }

    public String getNumber() {
        return number;
    }

    public void setNumber(String number) {
        this.number = number;
    }

    public String getActiveNo() {
        return activeNo;
    }

    public void setActiveNo(String activeNo) {
        this.activeNo = activeNo;
    }

    public Long getFirstActiveTime() {
        return firstActiveTime;
    }

    public void setFirstActiveTime(Long firstActiveTime) {
        this.firstActiveTime = firstActiveTime;
    }

    public String getSendNumber() {
        return sendNumber;
    }

    public void setSendNumber(String sendNumber) {
        this.sendNumber = sendNumber;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public Integer getDoType() {
        return doType;
    }

    public void setDoType(Integer doType) {
        this.doType = doType;
    }

    public String getTerminal() {
        return terminal;
    }

    public void setTerminal(String terminal) {
        this.terminal = terminal;
    }

    public String getAccessSiteId() {
        return accessSiteId;
    }

    public void setAccessSiteId(String accessSiteId) {
        this.accessSiteId = accessSiteId;
    }
}
