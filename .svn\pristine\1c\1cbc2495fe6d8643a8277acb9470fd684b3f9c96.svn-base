<%@ page import="com.shunwang.baseStone.context.LoginElementService" %>
<%@ page import="com.shunwang.baseStone.context.BaseStoneContext" %>
<%@ page import="com.shunwang.passport.common.geetest.GeetestUtil" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ include file="/common/taglibs.jsp"%>
<% String returnUrl = request.getParameter("returnUrl");
	if(returnUrl == null )
		returnUrl = "";
	String needCode = request.getParameter("needCode");
	String site_id = request.getParameter("site_id") ;
	String version = request.getParameter("version") ;
	String env = request.getParameter("env") ;
	if(null == env){
		env = "" ;
	}
	if(null == version){
		version = "";
	}
	if(needCode == null)
		needCode = "0";
	String userAgent = request.getHeader("User-Agent");
	String linkTgt = request.getParameter("linkTgt");
	request.setAttribute("linkTgt",linkTgt);
%>
<!DOCTYPE html>
<html>
<head>

	<title>注册账号</title>
		<%
			LoginElementService loginElementService = (LoginElementService) BaseStoneContext.getInstance().getBean("loginElementService");
			String siteId =  request.getParameter("site_id") ;
			if(null == siteId){//如果输入错误，则取siteId
				siteId = request.getParameter("siteId") ;
			}
			
			request.setAttribute("h5RegCheckCodeSiteId",siteId);
			
			boolean needActual = loginElementService.getIsNeedActualRegBySiteId(siteId) ;
			request.setAttribute("needActual",needActual) ;

			boolean showGt = GeetestUtil.isShowGt();
			request.setAttribute("showGt",showGt) ;
			//极验开启则不需要查图片验证码
			boolean needSmsCheckCode = showGt ? false : loginElementService.getIsNeedSmsCheckCodeBySiteId(siteId) ;

			request.setAttribute("needSmsCheckCode",needSmsCheckCode) ;

			if(userAgent.contains("shunwangapp")) {
				out.println("<meta name=\"header-template\" content=\"t1\" />");
			}
    %>
	<style>
		.link-normal-reg{
			line-height: 30px;
			height: 30px;
			font-size: 14px;
			text-align: center;
		}
	</style>
</head>
<body>
<content tag="header_title">注册账号</content>
<div class="tips-error" style="display: none;">
	<p>${msg}</p>
</div>
<form action="${appServer}/front/swpaysdk/doRegForHtml5.htm" method="post" class="form-box" id="js-form">
		<c:if test="${needSmsCheckCode && !showGt}">
           <div class="form-group inline-group">
               <input class="form-control" name="h5RegCheckCode" id="h5RegCheckCode" type="text"  placeholder="请输入右侧验证码" class="form-control" value="<s:property value="h5RegCheckCode"/>">
               <img id="checkCode2" class="v-code" src="${appServer}/front/swpaysdk/h5RegCheckCode.htm?t=<%=System.currentTimeMillis()%>" title="换一张" onclick="refreshH5RegCheckcode()"/>
               <button type="button" class="btn btn-primary btn-mini" onclick="refreshH5RegCheckcode()">换一张</button>
            </div>
    	</c:if>
	<div class="form-group inline-group">
		<input name="mobile" type="tel" placeholder="请输入手机号" class="form-control" value="${mobile}">
		<button type="button" id="sent-code" class="btn btn-primary btn-mini">发送验证码</button>
	</div>
	<div class="form-group">
		<input name="mobileActiveNo" type="tel" placeholder="请输入手机短信中的验证码" class="form-control" value="${mobileActiveNo}">
	</div>
	<div class="form-group inline-group">
		<input name="passwordShow"  type="password" placeholder="请输入密码（长度在6~16个字符之间）" class="form-control">
		<input name="password" id="password" type="hidden">
		<input type="checkbox" id="password-show" class="weui_switch">
	</div>
	<c:if test="${needActual}">
	<div class="form-group">
		<input name="realName" id="realName" class="form-control" type="text" placeholder="请输入真实姓名">
	</div>
	<div class="form-group">
		<input name="idCard" id="idCard" class="form-control" type="text" placeholder="请输入身份证号码">
	</div>
	</c:if>
	<div class="other-group">
		<label  class="form-label">
			<input type="checkbox" class="form_check" tabindex="7" id="agreementCheckbox"/>
			<span class="agreement_chk">我已阅读并接受</span>
			<a href="${appServer}/front/swpaysdk/userAgreement.htm">《顺网用户协议》</a>  <c:if test="${not empty PRIVACY_URL}"> 及<a href="${PRIVACY_URL}" target="_blank">《隐私政策》</a></c:if>
		</label>
	</div>
	<input type="hidden" name="returnUrl" value="<%=returnUrl%>">
	<input type="hidden" name="needCode" value="<%=needCode%>">
	<input type="hidden" name="siteId" value="<%=siteId%>">
	<input type="hidden" name="siteVersion" value="<%=version%>">
	<input type="hidden" name="regEnvironment" value="<%=env%>">
	<input type="hidden" name="checkGt" id="showGt" value="<%=showGt%>">
	<div class="other-group">
		<p id="normalReg" class="link-normal-reg"><a href="${appServer}/front/swpaysdk/register/index.jsp?returnUrl=<%=returnUrl%>&needCode=<%=needCode%>&siteId=<%=siteId%>&siteVersion=<%=version%>&regEnvironment=<%=env%>&sdk=false">普通账号注册</a></p>
	</div>
	<div class="other-group btn-box">
		<button type="submit" id="reg" class="btn btn-primary" >注册</button>
	</div>
	<c:if test="${!(linkTgt == '_blank')}">
	<div class="other-group">
		<p id="linkLogin" class="link-login"><a href="javascript: window.history.go(-1);">已有顺网通行证账号${linkTgt}</a></p>
	</div>
	</c:if>
</form>

<content tag="scripts">
	<script src="${staticServer}/scripts/front/member/min/validation.min.js"></script>
	<script src="${staticServer}/scripts/front/swpaysdk/register/src/reg.js"></script>
	<script src="${staticServer}/scripts/front/member/gt.js" type="text/javascript" ></script>
	<script src="${staticServer}/scripts/common/md5.js"></script>
	<script src="${staticServer}/scripts/front/member/gtUtil.js" type="text/javascript" ></script>
	<script>
		function refreshH5RegCheckcode() {
			var nowTime = new Date();
			$("#checkCode2").attr("src", "${appServer}/front/swpaysdk/h5RegCheckCode.htm?=t"+nowTime.getTime());
		}
		var needSmsCheckCode = "${needSmsCheckCode}";
		var h5RegCheckCodeSiteId = "${h5RegCheckCodeSiteId}";

		//以下极验业务---------------------------
		var showGt = "true" == '${showGt}';
		var gtRegisterUrl = "/front/common/gtRegister.htm?r=" + new Date().getTime();
		var sendGt = $.gtUtil({
					"showGt": showGt,
					"formId": "js-form",
					"gtRegisterUrl": gtRegisterUrl,
					"btnId": "sent-code"
				},
				function () {
					sendCode();
				},
				function() {
					return sendCodeCheck();
				},
				function (msg) {
					$('#tips-error p').html(msg);
					$('#tips-error').show();
				}
		);
	</script>
</content>
</body>
</html>