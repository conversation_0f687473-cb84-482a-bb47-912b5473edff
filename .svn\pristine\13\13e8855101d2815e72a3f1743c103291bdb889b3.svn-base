package com.shunwang.basepassport.user.action;

import com.shunwang.util.date.DateUtil;
import com.shunwang.util.encrypt.Md5Encrypt;
import org.junit.Test;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;

/**
 * User:pf.ma
 * Date:2017/02/07
 * Time:16:23
 */
public class TestGetAuthorizedToken {
	@Test
	public void test() throws UnsupportedEncodingException {
		String siteId = "sw_pay";
		String md5Key = "123456" ;
		String memberId = "91485220" ;
		String authorizedCode = "a1568137-9b71-488c-9e2f-d42e8769a1b2" ;
		String time = DateUtil.getCurrentDateStamp();

		String plainText = siteId+"|"+time +"|" + memberId+"|"+authorizedCode  +"|"+md5Key ;

		String sign = Md5Encrypt.encrypt(URLEncoder.encode(plainText, "UTF-8").toUpperCase(), "UTF-8").toUpperCase();

		String respond = "";
		HttpURLConnection conn = null;

		try {
			URL url = new URL("http://interface.kedou.com/front/interface/getAuthorizedToken.htm");
			conn = (HttpURLConnection) url.openConnection();
			conn.setDoOutput(true);
			conn.setUseCaches(false);
			conn.setRequestMethod("POST");
//			conn.setRequestProperty("Accept", "application/json");
			conn.getOutputStream().write(("siteId=" + siteId
					+"&authorizedCode="+authorizedCode
					+"&time=" +time + "&memberId=" + memberId+ "&sign=" + sign).getBytes());
			conn.connect();

			BufferedReader reader = new BufferedReader(new InputStreamReader(conn.getInputStream(), "utf-8"));
			String lines = "";

			while ((lines = reader.readLine()) != null) {
				respond += lines;
			}

			reader.close();
			conn.disconnect();
		} catch (Exception e) {
			//e.printStackTrace();
			respond = e.getMessage();
		} finally {
			if (conn != null) {
				conn.disconnect();
			}
			conn = null;
		}

		System.out.println(respond);
	}
}
