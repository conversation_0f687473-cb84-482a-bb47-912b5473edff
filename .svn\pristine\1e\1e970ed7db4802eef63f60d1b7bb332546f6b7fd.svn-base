package com.shunwang.basepassport.manager.response.risk;

import com.google.gson.annotations.Expose;
import com.shunwang.basepassport.manager.IResponse;
import com.shunwang.util.json.GsonUtil;


public class RiskResponse implements IResponse {

    public static final int SUCCESS_CODE = 0;

    private Integer code;

    private String message;

    private String json;

    @Expose
    private RiskResponseData data;

    public RiskResponse() {
    }

    public RiskResponse parse() {
        return GsonUtil.jsonToBean(json, this.getClass());
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getJson() {
        return json;
    }

    public void setJson(String json) {
        this.json = json;
    }

    public boolean isSuccess() {
        return code != null && code == SUCCESS_CODE;
    }

    public RiskResponseData getData() {
        return data;
    }

    public void setData(RiskResponseData data) {
        this.data = data;
    }

    @Override
    public String toString() {
        return "RiskResponse{" +
                "code=" + code +
                ", message='" + message + '\'' +
                ", json='" + json + '\'' +
                ", data=" + data +
                '}';
    }
}
