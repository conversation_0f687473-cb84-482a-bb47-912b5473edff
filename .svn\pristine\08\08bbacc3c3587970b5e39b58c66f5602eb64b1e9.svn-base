<%@ page import="org.apache.struts2.ServletActionContext" %>
<%@ page contentType="text/html; charset=UTF-8"%>
<%@ include file="../common/taglibs.jsp" %> 
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>正在跳转，请稍候...</title>
</head>
<body>

<c:if test="${empty fmtCallback || fmtCallback=='' || fmtCallback=='null'}">
<span>系统提示：callbackUrl不存在，页面响应终止</span>
</c:if>
<c:if test="${not empty fmtCallback && fmtCallback!='' && fmtCallback!='null'}">
    <c:if test="${isFreeLoginWriteDataFlag == true}">
<%--        <script>function setClientCallback(){};</script>--%>
        <script type="text/javascript">
            var script=document.createElement("script");
            script.src="https://plugin.kedou.com:9199/setClientTicket.htm?clientTicket=${clientTicket}&callback=JsonpCallBack";
            document.getElementsByTagName("head")[0].appendChild(script);
        </script>
    </c:if>
    <script type="text/javascript" src="${staticServer}/${cdnVersion}/js/jquery-1.4.3.js"></script>
    <script type="text/javascript" src="https://res.icafe28.com/slot/js/common.js"></script>
<script type="text/javascript">
	$(document).ready(function() {
        if (window.SW_RTB_SDK) {
            SW_RTB_SDK.getIds(function (value) {
                jump(value);
            });
        } else {
            jump('');
        }
        function jump(sso_rbt_data) {
            var tgt = "${tgt}" ;
            if(tgt=="self"){
                window.location.href="${fmtCallback}" + '&sso_rbt_data=' + sso_rbt_data;
                return;
            }else{
                top.location="${fmtCallback}" + '&sso_rbt_data=' + sso_rbt_data;
                return;
            }
        }
	});
</script>
</c:if>
</body>