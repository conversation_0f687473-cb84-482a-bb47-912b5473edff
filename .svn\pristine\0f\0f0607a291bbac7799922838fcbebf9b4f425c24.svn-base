package com.shunwang.baseStone.sso.freeloginweb;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

import com.shunwang.baseStone.core.exception.BaseStoneException;
import com.shunwang.basepassport.config.common.SiteInterfaceUtil;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.google.gson.Gson;
import com.shunwang.baseStone.bussiness.dao.BussinessDao;
import com.shunwang.baseStone.context.BaseStoneContext;
import com.shunwang.baseStone.siteinterface.context.SiteContext;
import com.shunwang.basepassport.config.pojo.SiteInterface;
import com.shunwang.baseStone.sso.common.util.SignatureUtil;
import com.shunwang.baseStone.sso.core.constants.GenericErrorEnum;
import com.shunwang.baseStone.sso.core.result.ApiResult;
import com.shunwang.baseStone.sso.exception.ValidateExp;
import com.shunwang.baseStone.useroutinterface.constant.UseroutConstant;
import com.shunwang.framework.struts2.action.BaseAction;
import com.shunwang.util.encrypt.Md5Encrypt;

abstract class FreeLoginBaseAction extends BaseAction {

	private static final long serialVersionUID = 5937619458890488594L;
	
    private final static Logger log = LoggerFactory.getLogger(FreeLoginBaseAction.class);

    protected SiteInterface siteInterface;
    protected String siteId;
    protected String sign;
    
	public String execute() throws Exception {
		getResponse().setContentType("text/json;charset=UTF-8");
        try {
        	checkSiteId();
            checkParam();
            checkSign(siteInterface);
            Map<String, Object> result = process();
            
            getResponse().getWriter().write(toJSON(ApiResult.newSuccessResult(result)));
        } catch(BaseStoneException e) {
        	log.error("参数校验异常", e);
        	getResponse().getWriter().write(toJSON(ApiResult.newErrorResult(GenericErrorEnum.PARAM_INVALID.getErrorCode(), e.getMessage())));
        } catch (Exception e) {
        	log.error("系统异常", e);
        	getResponse().getWriter().write(toJSON(ApiResult.newErrorResult(GenericErrorEnum.SYS_ERROR.getErrorCode(), GenericErrorEnum.SYS_ERROR.getErrorMessage())));
        }
        return null;
	}
	
	private void checkSiteId() {
		if (StringUtils.isBlank(siteId))
            throw new ValidateExp("参数[siteId]不能为空");
		
		BussinessDao bussinessDao = (BussinessDao) BaseStoneContext.getInstance().getBean("bussinessDao");
        if (bussinessDao.getById(getSiteId()) == null)
            throw new ValidateExp("非法参数[siteId]");
        
        SiteContext.setSiteName(getSiteName());
        SiteContext.setSiteId(getSiteId());
        siteInterface = SiteInterfaceUtil.loadSiteInterface();
        if (siteInterface == null || !siteInterface.getState().equals(Integer.valueOf(UseroutConstant.S_Open))) {
        	throw new ValidateExp("接口权限已关闭[" + siteId +"]");
        }
	}

	protected abstract Map<String, Object> process() throws Exception;
	
    protected abstract void checkParam() throws Exception;
    
    protected abstract String getSiteName();
    
    protected void checkSign(SiteInterface siteInterface) throws UnsupportedEncodingException {
    	if (StringUtils.isBlank(sign))
            throw new ValidateExp("参数[sign]不能为空");
    	Map<String, String> paramsMap = buildParamMap();
    	
        String content = SignatureUtil.getSignatureContent(paramsMap, null, SignatureUtil.PARAM_SIGN_TYPE);
		String mysign = Md5Encrypt.encrypt(content + siteInterface.getMd5Key(), "utf-8");
		if (!mysign.equalsIgnoreCase(sign)) {
			log.error("签名错误,sign[{}], mySign[{}], source[{}]", sign, mysign, content);
			throw new ValidateExp("签名错误");
		}
    }
    
    protected Map<String, String> buildParamMap() throws UnsupportedEncodingException {
		Map<String, String> params = new HashMap<String, String>();
		Map requestParams = getRequest().getParameterMap();
		for (Iterator iter = requestParams.keySet().iterator(); iter
		.hasNext(); ) {
			String name = (String) iter.next();
			String[] values = (String[]) requestParams.get(name);
			String valueStr = "";
			for (int i = 0; i < values.length; i++) {
				valueStr = (i == values.length - 1) ? valueStr + values[i]
				                                                        : valueStr + values[i] + ",";
			}
			if ("get".equalsIgnoreCase(getRequest().getMethod())) {
				valueStr = new String(valueStr.getBytes("ISO-8859-1"), "UTF-8"); //乱码解决
			}
			params.put(name, valueStr);
		}
		return params;
	}

    
    protected Map<String, Object> createResultJSON() {
        Map<String, Object> json = new HashMap<String, Object>();
        return json;
    }

    protected String toJSON(Object json){
        try {
            Gson gson = new Gson();
            return gson.toJson(json);
        } catch (Exception e) {
            log.error("JSON转换异常", e);
        }
        return null;
    }
    
    protected String buildSign(String source) {
        String sign = null;
        try {
            sign = Md5Encrypt.encrypt(URLEncoder.encode(source, "UTF-8").toUpperCase(), "UTF-8").toUpperCase();
        } catch (UnsupportedEncodingException e) {
            if (log.isErrorEnabled())
                log.error("构建签名时出现异常：", e);
        }
        return sign;
    }
    
	public String getSiteId() {
		return siteId;
	}
	public void setSiteId(String siteId) {
		this.siteId = siteId;
	}
	public String getSign() {
		return sign;
	}
	public void setSign(String sign) {
		this.sign = sign;
	}


}
