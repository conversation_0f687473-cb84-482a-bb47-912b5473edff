<%@ page import="com.shunwang.basepassport.context.UserContext" %>
<%@ page contentType="text/html;charset=UTF-8"%>
<%@ include file="/common/taglibs.jsp"%>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name="Keywords" content="顺网通行证、 密码 、找回 、申诉" />
<meta name="Description" content="填写申诉理由，填写新密码等待审核。" />
<title>顺网通行证-找回密码-申诉找回</title>

<script type="text/javascript" src="${staticServer}/scripts/common/tabindex.js"></script>
<script type="text/javascript" src="${staticServer}/scripts/front/member/password.js"></script>
<script type="text/javascript" src="${staticServer}/scripts/front/member/min/validation.min.js"></script>
<script type="text/javascript" src="${staticServer}/scripts/common/md5.js"></script>
<script type="text/javascript" src="${staticServer}/scripts/front/find/resetPwd_front.js"></script>
<script type="text/javascript" src="${staticServer}/scripts/front/common/citys.js" ></script>
<script type="text/javascript" src="${staticServer}/scripts/front/common/calendar.js"></script>
<script type="text/javascript" src="${staticServer}/scripts/front/find/globalAppeal_front.js"></script>

</head>
<body>
<div class="c_head">
    <i class="forget_icon"></i>
    <span class="title">找回密码</span>
    <span class="desc">您正在找回帐号<strong>${memberName}</strong>的密码
        <% if(UserContext.getMember()==null){%>
            <a href="/front/noLogin/mapFindWay.htm?findWay=2">[更换账号]</a></span>
         <%}%>
</div>
<div class="c_body forget_s04">
    <ul class="step_bar">
        <li class="current"><em>1</em> 填写账号信息</li>
        <li><em>2</em> 设置新密码</li>
    </ul>
    <div class="appeal_tip">为了帐号安全，请您尽量多提供帐号使用资料以帮助我们判断您是帐号的主人，而非试图</br>获取他人帐号的盗号者。 <strong>即使您对某些答案不确定，也可提供您认为最正确的答案。</strong></div>

          <form id="gotoResetForm" action="<c:url value='/front/noLogin/pwdFind_appeal_toreset.htm'/>"  method="post" enctype="multipart/form-data">
              <input type="hidden" name="memberName"  value="${memberName }"/> 
              <input type="hidden" name="appeal.userName" id="userName" value="${appeal.userName }"/> 
              <input type="hidden" name="appeal.realName" id="realName" value="${appeal.realName }"/> 
              <input type="hidden" name="appeal.idCardNo" id="userIdCard" value="${appeal.idCardNo}"/> 
              <%@include file="/front/find/globalAppeal_front.jsp" %>
          </form>
</div>


<script type="text/javascript" >
	var selectPro = document.getElementById("selectPro");
	var selectPro1 = document.getElementById("selectPro1");
	doInitPro(selectPro, "${usedProvince}");
	doChangeCity("${usedProvince}", document.getElementById('selectCity'), "${usedCity}");
	doInitPro(selectPro1, "${regProvince}");
	doChangeCity("${regProvince}", document.getElementById('selectCity1'), "${regCity}");
	doInitForError()
</script>
</body>
</html>
