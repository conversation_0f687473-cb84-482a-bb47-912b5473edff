package com.shunwang.passport.changeemail.web;

import com.shunwang.baseStone.context.BaseStoneContext;
import com.shunwang.baseStone.key.SWKey;
import com.shunwang.baseStone.key.SWKeyContext;
import com.shunwang.baseStone.key.exception.KeyNotExistExp;
import com.shunwang.basepassport.find.pojo.Appeal;
import com.shunwang.basepassport.email.constants.EmailCheckCodeConstants;
import com.shunwang.basepassport.email.dao.EmailCheckCodeDao;
import com.shunwang.basepassport.email.pojo.EmailCheckCode;
import com.shunwang.basepassport.question.pojo.MemberQuestion;
import com.shunwang.basepassport.user.pojo.Member;
import com.shunwang.passport.find.web.AppealAction;
import com.shunwang.util.date.DateUtil;

import java.util.Date;
import java.util.List;

public class QuestionChangeEmailAction extends AppealAction {

	private static final long serialVersionUID = 3271510376598682397L;
	private Appeal appeal;
	private String newEmail;
	private String smsCheckCode;
	
	/**
	 * ***********
	 * 创建日期: 2013-8-11
	 * 创建作者：JINBAO
	 * @param 
	 * @return String
	 * 功能：跳转到用密保问题修改邮箱页面
	 *************
	 */
	public String goQuestionChangeEmail(){

		try {
			SWKeyContext.put(new SWKey("changeEmailByQuestion"));
			memberQuestion = new MemberQuestion();
		    List<MemberQuestion>memberQuestions = memberQuestion.getMemberBindQueList(appeal.getUserName());
			for(int i=0;i<memberQuestions.size();++i){
				memberQuestion=memberQuestions.get(i);
				this.getRequest().setAttribute("memberQuestion"+(i+1), memberQuestion);
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return INPUT;
		}
		this.getUserState(appeal.getUserName());
    	return SUCCESS;
	}
	
	/**
	 * ***********
	 * 创建日期: 2013-8-11
	 * 创建作者：JINBAO
	 * @param 
	 * @return String
	 * 功能：用密保问题修改邮箱成功
	 *************
	 */
	public String questionChangeEmailSuccess() {
		try {
			SWKeyContext.get(new String[]{"changeEmailByQuestion"});
				
			if(null == appeal.getUserName())
				return INPUT;
			EmailCheckCode emailCheckCode = getEmailCheckCode(appeal.getUserName());
			if(null == emailCheckCode) {
				setErrorMsg("请先获取验证码");
				return INPUT;
			}
			if(!newEmail.equals(emailCheckCode.getEmail())) {
				setErrorMsg("更换的邮箱与获取验证码的邮箱不一致");
				return INPUT;
			}
			if(!emailCheckCode.getCheckCode().equals(smsCheckCode)) {
				setErrorMsg("邮箱验证码不正确，请重新输入");
				return INPUT;
			}
			
			if(DateUtil.compare(new Date(), emailCheckCode.getSendTime(), DateUtil.ONE_MINUTE) > Integer.valueOf(EmailCheckCodeConstants.VALID_TIME_DEFAULT)) {
				setErrorMsg("邮箱验证码已过期，请重新发送");
				return INPUT;
			}
			
			if(null == newEmail) {
				setErrorMsg("邮箱不能为空");
				return INPUT;
			}
			
			Member tmpMember = getMemberDao().getByEmail(newEmail);
			if(null != tmpMember) {
				setErrorMsg("该邮箱已被其它用户绑定");
				return INPUT;
			}
			
			validateProtectedQuestions(appeal.getUserName());
			
			Member member = getMemberDao().getByName(appeal.getUserName());
			String oldEmail = member.getEmail();
    		member.setEmail(newEmail);
    		member.beginBuildLog("密保问题修改邮箱");
    		member.doResetEmail(oldEmail);
    		
		} catch(KeyNotExistExp e) {
			setErrorMsg("执行顺序不正确");
			return INPUT;
		} catch (Exception e) {
			setErrorMsg(e.getMessage());
			return INPUT;
			
		}
		return SUCCESS;
	}
	
	/**
	 * ***********
	 * 创建日期: 2013-8-11
	 * 创建作者：JINBAO
	 * @param 
	 * @return String
	 * 功能：跳转到重置邮箱页面
	 *************
	 */
	public String questionResetEmail() {
    	try {
    		SWKeyContext.get(new String[]{"changeEmailByQuestion"});
			validateProtectedQuestions(appeal.getUserName());
		} catch(KeyNotExistExp e) {
			setErrorMsg("执行顺序不正确");
			return INPUT;
		} catch (Exception e) {
			goQuestionChangeEmail();
			setErrorMsg(e.getMessage());
			return INPUT;
		}
    	return SUCCESS;
    }
	
	public EmailCheckCode getEmailCheckCode(String memberName) {
		EmailCheckCodeDao  emailCheckCodeDao = getEmailCheckCodeDao();
		EmailCheckCode paramEmailCheckCode = new EmailCheckCode();
		paramEmailCheckCode.setMemberName(memberName);
		paramEmailCheckCode.setType(EmailCheckCodeConstants.CHANGE_EMAIL);
		return emailCheckCodeDao.findByNameAndType(paramEmailCheckCode);
	}
	
	private EmailCheckCodeDao getEmailCheckCodeDao() {
		return (EmailCheckCodeDao)BaseStoneContext.getInstance().getBean("emailCheckCodeDao");
	}

	public Appeal getAppeal() {
		return appeal;
	}

	public void setAppeal(Appeal appeal) {
		this.appeal = appeal;
	}

	public String getNewEmail() {
		return newEmail;
	}

	public void setNewEmail(String newEmail) {
		this.newEmail = newEmail;
	}

	public String getSmsCheckCode() {
		return smsCheckCode;
	}

	public void setSmsCheckCode(String smsCheckCode) {
		this.smsCheckCode = smsCheckCode;
	}
}
