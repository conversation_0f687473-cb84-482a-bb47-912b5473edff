var reg = /[\\<\\>\'\"\\\\]+/;
var appServer;
var member;
var page = {
	    companyName: $("#companyName"),
		linkMan:$("#linkMan"),
	    idCardNo: $("#idCardNo"),
		realName: $("#realName"),
		nickName: $("#nickName"),
		qq: $("#qq"),
		fixedMobile: $("#fixedMobile"),
		linkAddress: $("#linkAddress"),
		postalCode: $("#postalCode"),		
	    checkNickName: function(){	  
	    	var val=page.nickName.val();
	    	val=$.trim(val);
	        if(val==""){	         
	        	showOKmsg( $("#nickNameErr") );
	        	return true;
	        	}
	        if(val.length > 24){
	        	 showError($("#nickNameErr"),"昵称长度不能超过24个字符");
		         return false;
		     }
		     if(reg.test(val)){
		    
		     	showError($("#nickNameErr"),"昵称不能包含英文单双引号，反斜杠和左右尖括号");
		         return false;
		     }
		     if(isSensitive(val)){
		     	showError($("#nickNameErr"),"昵称不能包含禁用词语");
		         return false;
		     }
		     showOKmsg( $("#nickNameErr") );
		     page.nickName.val(val);
		     return true;
	    },
	    checkRealName: function(){
	    	var val=page.realName.val();
	    	 val=$.trim(val);
		    if( val== ""){		      
		    	showError($("#realNameErr"),"请填写真实姓名");
		        return false;
		    }
		    if(val=='审核中'){
		       	 val="";
		       	 return true;
		    }
		    if(val!=""){
		       if(! validateRealName(val)){
		       showError($("#realNameErr"),"真实姓名请使用中文，2-10个汉字");
		        return false;
		    }
		    }
		    showOKmsg( $("#realNameErr") );
		    page.realName.val(val);
		    return true;
		},
		
		checkIDCardNo: function(){
			var val=page.idCardNo.val();
			val=$.trim(val);
		    if(val == ""){
		        showError($("#idCardErr"),"请填写身份证号");
		        return false;
		    }
		    if(val=='审核中'){
		       	 val="";
		       	 return true;
		    }
		    if(validateIdCard( document.getElementById("idCardNo")) != 0){
		        showError($("#idCardErr"),"无效身份证号码,请输入18位有效身份证号");
		        return false;
		    }
		    showOKmsg( $("#idCardErr") );
		    page.idCardNo.val(val);
		    return true;
		},
		
	    checkCompanyName: function(){
	        var val = page.companyName.val();
	         val=$.trim(val);
  		    if(val == ""){
  		    	showError($("#companyNameErr"),"请填写公司名称");
		        return false;
		    }
		    if(reg.test(val)){
		    
		     	showError($("#companyNameErr"),"公司名称不能包含英文单双引号，反斜杠和左右尖括号");
		         return false;
		     }
		    if(val.length > 128){
		    	showError($("#companyNameErr"),"公司名称最多输入128个字符");
		        return false;
		    }
		    if(isSensitive(val)){
		    	showError($("#companyNameErr"),"公司名称不能包含禁用词语");
		        return false;
		    }
		    showOKmsg( $("#companyNameErr") );
		     page.companyName.val(val);
		    return true;
		},
		checkLinkMan: function(){
	        var val = page.linkMan.val();
	         val=$.trim(val);
	        if(val == ""){
	        	showError($("#linkManErr"),"请填写联系人");
		        return false;
		    }
		    if(val.length > 16){
		    	showError($("#linkManErr"),"联系人最多输入16个字符");
		        return false;
		    }
		    if(isSensitive(val)){
		    	showError($("#linkManErr"),"联系人不能包含禁用词语");
		        return false;
		    }
		    showOKmsg( $("#linkManErr") );
		     page.linkMan.val(val);
		    return true;
		},
		checkQQ: function(){
			
			 var val = page.qq.val();
			 val=$.trim(val);
			
			 if(val==""){
			 	showOKmsg( $("#qqErr") );
			 	return true;
			 }
			 var reg = /^\d{5,20}$/;
			 if(!reg.exec(val)){ 
			 	showError($("#qqErr"),"QQ只能输入5-20位数字");
		        return false;
			 }
			 if(val.length <5 || val.length>20){
			 	showError($("#qqErr"),"QQ只能输入5-20位数字");
		        return false;
			 }
			 showOKmsg( $("#qqErr") );
			 page.qq.val(val);
		    return true;
		},
		checkFixedMobile: function(){
			var val = page.fixedMobile.val();
			val=$.trim(val);
			if(val==""){
			 	showOKmsg( $("#fixedMobileErr") );
			 	return true;
			 }
			if(!isPhoneNo(val)){
				showError($("#fixedMobileErr"),"联系电话格式不正确");
		        return false;
			}
			if(val.length > 20){
				showError($("#fixedMobileErr"),"只能输入20位以下数字");
		        return false;
			}
			showOKmsg( $("#fixedMobileErr") );
			page.fixedMobile.val(val);
		    return true;
		},
		checkLinkAddress: function(){
			var val = page.linkAddress.val();
			val=$.trim(val);
			if(val==""){
			 	showOKmsg( $("#linkAddressErr") );
			 	return true;
			 }
			if(val.length > 128){
				showError($("#linkAddressErr"),"输入不能超过128个字符");
		        return false;
			}
			showOKmsg( $("#linkAddressErr") );
			page.linkAddress.val(val);
		    return true;
		},
		checkPostalCode: function(){
			var val = page.postalCode.val();
			val=$.trim(val);
			if(val==""){
			 	showOKmsg( $("#postalCodeErr") );
			 	return true;
			 }
			if(val.length != 6){
				showError($("#postalCodeErr"),"只能输入6位的数字");
		        return false;
			}
			var reg = /^\d{6}$/;
	        if(!reg.exec(val)){
				showError($("#postalCodeErr"),"只能输入6位的数字");
		        return false;
			 }
			 showOKmsg( $("#postalCodeErr") );
			 page.postalCode.val(val);
		    return true;
		},
		canSubmit: function(){
			if(member.memberType == 1){
			    if(! page.checkNickName()) return false;
			    if(page.realName.attr("name") == "member.realName"){
			    	if(! page.checkRealName()) return false;
			    }
			    if(page.idCardNo.attr("name") == "member.memberInfo.idCardNo"){
					if(! page.checkIDCardNo()) return false;
				}
			}
			if(member.memberType == 2){
				if(page.companyName.attr("name")=="member.companyName"){
					if(! page.checkCompanyName()) return false;
				}
			    if(page.linkMan.attr("name")=="member.linkMan"){
			    	if(! page.checkLinkMan()) return false;
			    }
			}
			if(!page.checkQQ()) return false;
			if(!page.checkFixedMobile()) return false;
			if(!page.checkLinkAddress()) return false;
			if(!page.checkPostalCode()) return false;
			if(!page.checkFixedMobile()) return false;
		    return true;
		    
		},
		
		showAltMsg:function(obj,msg){
			obj.html(msg);
		}
		
};

function doSubmit(){
	function doSubmit(){
	if(page.canSubmit){
		$("#infoForm").submit();
	}
}
}
function init(){
	page.linkMan = $("#linkMan");
    page.realName = $("#realName");
    page.idCardNo = $("#idCardNo");
    page.companyName = $("#companyName");
    page.nickName = $("#nickName");
    page.fixedMobile = $("#fixedMobile");
    page.linkAddress = $("#linkAddress");
    page.postalCode = $("#postalCode");  
    page.nickNameErr=$("#nickNameErr");
    page.qq= $("#qq");

    page.realName.blur(page.checkRealName);
    page.nickName.blur(page.checkNickName);
    page.idCardNo.blur(page.checkIDCardNo);
    page.linkMan.blur(page.checkLinkMan);
    page.qq.blur(page.checkQQ);
    page.fixedMobile.blur(page.checkFixedMobile);
    page.linkAddress.blur(page.checkLinkAddress);
    page.postalCode.blur(page.checkPostalCode);
    page.companyName.blur(page.checkCompanyName);
    
    
}	

function showError(obj,msg){
	obj.html("<img src='" + appServer + "/images/front/error.gif'/><font color='red'>"+msg+"</font>");
}

function showOKmsg( obj ){
	obj.html("<img src='" + appServer + "/images/front/ok.gif' />");
}
function cleanInfo(){
   $("#showInfo").html("");
}