package com.shunwang.basepassport.actu.dao;

import com.shunwang.baseStone.core.dao.BaseStoneIbatisDao;
import com.shunwang.basepassport.actu.pojo.ActuInfo;
import com.shunwang.basepassport.user.pojo.Member;
/**
 * @Description:实名认证dao
 * <AUTHOR>  create at 2011-7-27 下午02:13:50
 * @FileName com.shunwang.basepassport.actu.dao.ActuDao.java
 */
public abstract class ActuDao<Pojo extends ActuInfo> extends BaseStoneIbatisDao<Pojo> {
	/**
	 * @Description:
	 * @param memberId
	 * @return
	 * <AUTHOR> create at 2011-7-27 下午02:14:10
	 */
	public abstract ActuInfo getByMemberId(Integer memberId);
	/**
	 * 根据用户ID获取最后一次未审核的实名认证信息
	 * @param memberId
	 * @return
	 * <AUTHOR> create at 2011-9-3 下午08:22:42
	 */
	public abstract Integer findLatestedUnCheckCntByMemberId(Integer memberId);
	/**
	 * 撤销
	 * @param memberId
	 * @return
	 * <AUTHOR> create at 2011-9-3 下午09:07:47
	 */
	public abstract void repeal(Member member);
	/**
	 * 加载最后一次实名认证的信息
	 * @param memberId
	 * @return
	 * <AUTHOR> create at 2011-9-6 下午03:50:56
	 */
	public abstract ActuInfo loadActuInprocess(Integer memberId);

    /**
     * 更新infoState状态
     * @param pojo
     * @return
     */
	public abstract Integer updateState(Pojo pojo);
}
