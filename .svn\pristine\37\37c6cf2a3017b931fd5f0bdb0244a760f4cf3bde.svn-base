package com.shunwang.common;

import java.util.Map;
import java.util.Date;
import java.text.SimpleDateFormat;
import org.apache.struts2.util.StrutsTypeConverter;

public class DateTypeConverter extends StrutsTypeConverter {   
   

    @Override   
    @SuppressWarnings("all")
    public Object convertFromString(Map context, String[] values, Class toClass) {
        try {
            SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd");
            Date date= sdf.parse(values[0]);
            return date;
        } catch (Exception e) {
            return null;
        }
    }   
   
    @Override   
    @SuppressWarnings("all")
    public String convertToString(Map context, Object o) {
        SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd");
        return sdf.format(o);
    }   
}
