<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd" >
<sqlMap namespace="com.shunwang.basepassport.actu.pojo.PersonalActuVerifyRecord">
    <resultMap id="personalActuVerifyRecordMap" class="com.shunwang.basepassport.actu.pojo.PersonalActuVerifyRecord">
        <result column="id" property="id" jdbcType="INTEGER"/>
        <result column="member_id" property="memberId" jdbcType="INTEGER"/>
        <result column="member_name" property="memberName" jdbcType="VARCHAR"/>
        <result column="site_id" property="siteId" jdbcType="VARCHAR"/>
        <result column="id_card_no_coded" property="idCardNoCoded" jdbcType="VARCHAR"/>
        <result column="real_name_coded" property="realNameCoded" jdbcType="VARCHAR"/>
        <result column="charge_status" property="chargeStatus" jdbcType="INTEGER"/>
        <result column="result" property="result" jdbcType="INTEGER"/>
        <result column="origin_from" property="originFrom" jdbcType="INTEGER"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="DATETIME"/>
        <result column="category_id" property="categoryId" jdbcType="INTEGER" />
		<result column="category_name" property="categoryName" jdbcType="VARCHAR" />
    </resultMap>
    <typeAlias alias="personalActuVerifyRecord" type="com.shunwang.basepassport.actu.pojo.PersonalActuVerifyRecord"/>
    <select id="get" resultMap="personalActuVerifyRecordMap" parameterClass="java.lang.Integer">
        SELECT
            id,
            member_id,
            member_name,
            site_id,
            category_id,
            category_name,
            id_card_no_coded,
            real_name_coded,
            charge_status,
            result,
            remark,
            origin_from,
            create_time
        FROM personal_actu_verify_record
        WHERE id = #id:INTEGER #
    </select>
    <select id="getByVerified" resultMap="personalActuVerifyRecordMap" parameterClass="personalActuVerifyRecord">
        SELECT
            id,
            member_id,
            member_name,
            site_id,
            category_id,
            category_name,
            id_card_no_coded,
            real_name_coded,
            charge_status,
            result,
            remark,
            origin_from,
            create_time
        FROM personal_actu_verify_record
        WHERE member_id = #memberId#
        and result = 1
        order by create_time DESC
        limit 1
    </select>
    <select id="getByMemberId" resultMap="personalActuVerifyRecordMap" parameterClass="personalActuVerifyRecord">
        SELECT
            id,
            member_id,
            member_name,
            site_id,
            category_id,
            category_name,
            id_card_no_coded,
            real_name_coded,
            charge_status,
            result,
            remark,
            origin_from,
            create_time
        FROM personal_actu_verify_record
        WHERE member_id = #memberId#
        order by create_time DESC
        limit 1
    </select>
    <select id="getByIdCard" resultMap="personalActuVerifyRecordMap" parameterClass="personalActuVerifyRecord">
        SELECT
        id,
        member_id,
        member_name,
        site_id,
        category_id,
        category_name,
        id_card_no_coded,
        real_name_coded,
        charge_status,
        result,
        remark,
        origin_from,
        create_time
        FROM personal_actu_verify_record
        WHERE result = 1
            and id_card_no_coded = #idCardNoCoded:VARCHAR #
        limit 1
    </select>
    <!-- 添加记录 -->
    <insert id="insert" parameterClass="personalActuVerifyRecord">
        INSERT INTO
        personal_actu_verify_record (
            member_id,
            member_name,
            site_id,
            category_id,
            category_name,
            id_card_no_coded,
            real_name_coded,
            charge_status,
            result,
            remark,
            origin_from,
            create_time
        )
        VALUES (
            #memberId#,
            #memberName#,
            #siteId#,
            #categoryId:INTEGER#,
            #categoryName:VARCHAR#,
            #idCardNoCoded#,
            #realNameCoded#,
            #chargeStatus#,
            #result#,
            #remark#,
            #originFrom#,
            #createTime#
        )
        <selectKey resultClass="INTEGER" keyProperty="id">
            SELECT LAST_INSERT_ID()
        </selectKey>
    </insert>
    <update id="update" parameterClass="personalActuVerifyRecord">
        update personal_actu_verify_record
        set create_time = NOW()
        <isNotEmpty prepend="," property="memberId"> member_id = #memberId:INTEGER# </isNotEmpty>
        <isNotEmpty prepend="," property="memberName"> member_name = #memberName:VARCHAR# </isNotEmpty>
        <isNotEmpty prepend="," property="siteId"> site_id = #siteId:VARCHAR# </isNotEmpty>
        <isNotEmpty prepend="," property="idCardNoCoded"> id_card_no_coded = #idCardNoCoded:VARCHAR# </isNotEmpty>
        <isNotEmpty prepend="," property="realNameCoded"> real_name_coded = #realNameCoded:VARCHAR# </isNotEmpty>
        <isNotEmpty prepend="," property="chargeStatus"> charge_status = #chargeStatus:INTEGER# </isNotEmpty>
        <isNotEmpty prepend="," property="result"> result = #result:INTEGER# </isNotEmpty>
        <isNotEmpty prepend="," property="remark"> remark = #remark:VARCHAR# </isNotEmpty>
        <isNotEmpty prepend="," property="originFrom"> origin_from = #originFrom:INTEGER# </isNotEmpty>
        where id = #id:INTEGER#
    </update>

</sqlMap>