package com.shunwang.basepassport.binder.web.send;

import com.shunwang.basepassport.binder.common.BinderConstants;
import com.shunwang.basepassport.binder.pojo.SendBinder;
import com.shunwang.basepassport.user.pojo.Member;

/**
 * 13.  注册获取验证码（userName账号不用填），
 * 使用此字段后在使用【10.短信验证接口】时发送字段bindType需填1或3。
 *
 * <AUTHOR>
 * @date 2018/12/17
 */
public class SendForMobileRegisterAction extends MobileSendBaseAction {
    @Override
    public void checkParam() {
        setInterfaceType(BinderConstants.SendConstants.SendInterfaceType.TYPE_MOBILE_REGISTER);
        super.checkParam();
    }

    @Override
    protected SendBinder buildSendBinder() {
        SendBinder sendBinder = createSendBinder();
        Member member = getDao().getByMobile(getNumber());
        if (member != null) {
            setMember(member);
            sendBinder.setMember(getMember());
            sendBinder.setBusinessType(BinderConstants.MOBLIE_LOGIN);
            setInterfaceType(BinderConstants.MOBLIE_LOGIN);
            sendBinder.setMemberName(getMember().getMemberName());
            sendBinder.setAccessSiteId(getAccessSiteId());
            sendBinder.setTerminal(getTerminal());
        }
        return sendBinder;
    }

}
