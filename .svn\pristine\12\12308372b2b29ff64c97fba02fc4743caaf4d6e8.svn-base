<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">
	
	
	<bean id="memberAction" class="com.shunwang.passport.member.web.MemberAction" scope="prototype">
        <property name="memberDao">
            <ref  bean="memberDao"/>
        </property>
        <property name="idCardVerifyBo" ref="idCardVerifyBo"/>
        <property name="redisOperation" ref="redisOperation"/>
        <property name="bussinessDao" ref="bussinessDao" />
        <property name="swpayInterfaceBo" ref="swpayInterfaceBo"/>
        <property name="serviceNotifyDao" ref="serviceNotifyDao"/>
 	</bean>
	<bean id="loginAction" class="com.shunwang.passport.member.web.LoginAction" scope="prototype">
        <property name="agreementService" ref="agreementService"/>
	</bean>

    <bean id="personalEditLogAction"  class="com.shunwang.passport.member.web.EditLogAction" scope="prototype">
        <property name="editLogDao">
            <ref bean="personalEditLogDao"/>
        </property>
    </bean>

    <bean id="pwdManageAction" class="com.shunwang.passport.member.web.PwdManageAction" scope="prototype">
        <property name="memberDao">
            <ref  bean="memberDao"/>
        </property>
    </bean>
    
    <!--  为了解决和interface中的spring bean冲突所以改名 -->
    <bean id="webHeadImageAction" class="com.shunwang.passport.member.web.HeadImageAction" scope="prototype">
        <property name="memberDao" ref ="memberDao"/>
    </bean>
    
    <bean id="bigHeadImageUploadAction" class="com.shunwang.passport.member.web.BigHeadImageUploadAction" scope="prototype">
        <property name="memberDao">
            <ref  bean="memberDao"/>
        </property>
    </bean>
    
    <bean id="smallHeadImageUploadAction" class="com.shunwang.passport.member.web.SmallHeadImageUploadAction" scope="prototype">
        <property name="memberDao" ref ="memberDao"/>
        <property name="memberHeadImgService" ref ="memberHeadImgService"/>
    </bean>
    <bean id="bbsLoginAction" class="com.shunwang.passport.member.web.BbsLoginAction" scope="prototype">
    </bean>
    
</beans>