package com.shunwang.basepassport.manager;

/**
 * 接口常量
 */
public abstract class InterfaceConstant {

    public static final int INTERFACE_KEY_SWPAY_GET_MEM_DEPOSIT_TYPE = 3001; //get_mem_deposit_type
    public static final int INTERFACE_KEY_SWPAY_MONEY_UNFROZE_TYPE = 3002; //money_unforst
    public static final int INTERFACE_KEY_SWPAY_MONEY_SELECT_TYPE = 3005; //money_select
    public static final int INTERFACE_KEY_SWPAY_PAY_LIST_QUERY = 7002; //pay_list_query

    public static final int INTERFACE_KEY_NETBAR_CAFE_ACTU_NOTICE = 4000;
    public static final int INTERFACE_KEY_NETBAR_CAFE_ACTU_CHANGE_NOTICE = 4001;

    public static final int INTERFACE_KEY_FILE_UPLOAD = 4002;//文件上传
    public static final int INTERFACE_KEY_AGREEMENT_QUERY = 4003;//协议查询
    public static final int INTERFACE_KEY_VERIFY_TOKEN = 4004;//验证访问令牌接口
    public static final int INTERFACE_KEY_USER_GENERAL_ORDER = 4005;//收银台激活后扫码上机接口
    public static final int INTERFACE_KEY_ID_CARD_NO_DESENSITIZATION = 4006;//网吧脱敏数据转换接口

    public static final int INTERFACE_KEY_WEIXIN_MSG_CRYPT = 1201 ;//微信消息解密
    public static final int INTERFACE_KEY_WEIXIN_GET_VERIFY_TOKEN = 1202 ;//微信获取verifyToken
    public static final int INTERFACE_KEY_WEIXIN_GET_PRE_AUTH = 1203 ;//微信获取预授权code
    public static final int INTERFACE_KEY_WEIXIN_GET_PRE_URL = 1204 ;//微信获取预授权地址
    public static final int INTERFACE_KEY_WEIXIN_GET_AUTHORIZE_TOKEN = 1205 ;//微信获取授权AccessToken
    public static final int INTERFACE_KEY_WEIXIN_REFRESH_AUTHORIZE_TOKEN = 1206 ;//微信获取授权AccessToken
    public static final int INTERFACE_KEY_WEIXIN_CREATE_QRCODE = 1207 ;//微信生成二维码
    public static final int INTERFACE_KEY_WEIXIN_GET_USER_INFO = 1208 ;//微信获取用户信息
    public static final int INTERFACE_KEY_WEIXIN_SEND_MESSAGE = 1209 ;//微信发送客服信息
    public static final int INTERFACE_KEY_WEIXIN_MINI_CREATE_QRCODE = 1210 ;//微信生成小程序二维码（无限制）

    public static final int INTERFACE_KEY_CHUANGLAN_IDCRAD_VERIFY = 1400; //身份证实名认证
    public static final int INTERFACE_KEY_DATA_IDCRAD_VERIFY = 1401; //大数据身份证实名认证
    public static final int INTERFACE_KEY_DATA_BLACK_CARD_CHECK = 1403; //大数据黑卡检测
    public static final int INTERFACE_KEY_DATA_REPORT_FOR_FREE_LOGIN = 1405;//大数据免登上报-计费免登
    public static final int INTERFACE_KEY_DATA_REPORT_FOR_WX_WORK = 1407;//大数据免登上报-企微

    public static final int INTERFACE_KEY_DC_REPORT = 1402; //大数据统一采集服务接口

    public static final int INTERFACE_KEY_RISK_REPORT = 6000; //风控上报接口

    public static final int INTERFACE_KEY_GAME_IDCRAD_VERIFY = 6010; //国家游戏防沉迷身份证实名认证

    public static final int INTERFACE_KEY_GAME_IDCRAD_VERIFY_QUERY = 6011; //国家游戏防沉迷身份证实名认证查询

    public static final int INTERFACE_KEY_OA_GET_USER_BY_EMAIL = 1500;//根据域账号获取
    public static final int INTERFACE_KEY_OA_GET_USER_BY_IDCARD = 1501;//根据身份证号获取

    public static final int INTERFACE_KEY_GOOGLE_AUTH = 1300 ;//google授权配置

    public static final int INTERFACE_KEY_GEETEST_CHECK_MOBILE = 1600; //极验一键登录获取手机号码

    public static final int INTERFACE_KEY_GEETEST_H5_CHECK_MOBILE = 1601; //极验H5一键登录获取手机号码

    public static final int INTERFACE_KEY_YIDUN_H5_CHECK_MOBILE = 1602; //易盾H5一键登录获取手机号码

    public static final int INTERFACE_KEY_CARD_MSG = 1700;//卡片消息

    public static final int INTERFACE_KEY_LOGON_API = 30000;
}
