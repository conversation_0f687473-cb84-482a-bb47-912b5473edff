<%@ page contentType="text/html;charset=UTF-8" language="java" trimDirectiveWhitespaces="true" %>
<%@ include file="/common/taglibs.jsp"%>
<!DOCTYPE html>
<html>
  <head>
    <title>首页</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="description" content="">
    <meta name="keywords" content="">
    <link rel="stylesheet" href="${staticServer}/styles/swpaysdk/sw.css">
  </head>
  <body ontouchstart>
    <div class="page">
      <!-- header-->
      <div class="header">
        <h1 class="header-title">注销通行证账号</h1><a class="header-link left"><i class="iconfont icon-arrow-left"></i></a>
      </div>
      <div class="page-bd">
        <div class="msg">
          <div class="msg-icon-area"><i class="iconfont icon-succeed"></i></div>
          <div class="msg-text-area">
            <div class="msg-title">已完成注销申请</div>
            <div class="msg-desc">顺网通行证团队将在15天内处理你的注销申请，15天内你可通过重新登录来撤销注销，15天后该账号可被重新注册。如有疑问请联系客服：400-8559558</div>
          </div>
          <div class="msg-opr-area mt120">
            <div class="btn-area">
              <button class="btn btn-default">确定</button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <content tag="scripts">
      <script type="text/javascript" src="${staticServer}/scripts/front/swpaysdk/common.js"></script>
      <script type="text/javascript" src="${staticServer}/scripts/front/swpaysdk/sw/resize.js"></script>
      <script>
        var appServer = '${appServer}';
        var pageUrl = '${pageUrl}';
        $(".btn-default").on("click", function () {
          goBack();
        })
        $(".iconfont.icon-arrow-left").on("click", function () {
          goBack();
        });
        function goBack() {
          var isSelfJump = true;
          //H5页面的跳转
          if (pageUrl) {
            pageUrl = pageUrl + (pageUrl.indexOf("?") != -1 ? "" : "?") + "&islogout=true";
            window.location = pageUrl;
            return;
          }
          //Android APP回调
          try{
            //swJsAndroidInterface为约定的别名(别名也可以自己定义，保持一致就好)；
            //memberCancelBack()为原生的方法，方法名自己定保持一致就好
            window.swJsAndroidInterface.memberCancelBack(true);
            isSelfJump = false;
          } catch (e) {

          }
          try{
            window.webkit.messageHandlers.memberCancelBack.postMessage({isLoginOut:"true"})
            isSelfJump = false;
          }catch (e){
          }
          //以上都不跳转，则跳转到个人资料页面
          if (isSelfJump) {
            window.location = appServer + '/front/swpaysdk/logout.htm';
          }
        }
      </script>
    </content>
  </body>
</html>