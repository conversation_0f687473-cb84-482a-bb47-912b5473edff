package com.shunwang.basepassport.jms.param;

/**
 * <AUTHOR>
 * @date 2019/3/12
 **/
public class StringArrayParam implements Param {

    private String[] param;

    public StringArrayParam() {
    }

    public StringArrayParam(String... param) {
        this.param = param;
    }

    @Override
    public int getType() {
        return TYPE_STRING_ARRAY;
    }

    @Override
    public String[] getValue() {
        return param;
    }

    public void setValue(String[] param) {
        this.param = param;
    }

    @Override
    public String toString() {
        return "type:TYPE_STRING_ARRAY,value:" + getType() + ",paramValue:" + param;
    }
}
