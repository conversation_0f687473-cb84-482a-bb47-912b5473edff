package com.shunwang.basepassport.user.web;

import com.shunwang.baseStone.core.exception.BaseStoneException;
import com.shunwang.basepassport.binder.common.ErrorCode;
import com.shunwang.basepassport.headImg.service.MemberHeadImgService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.apache.tika.Tika;
import com.shunwang.basepassport.commonExp.FileTypeNotSupportedExp;
import com.shunwang.basepassport.user.exception.MsgNotFoundExp;
import com.shunwang.baseStone.core.response.BaseStoneResponse;
import com.shunwang.basepassport.commonExp.ParamNotFoundExp;
import com.shunwang.baseStone.encrypt.Encrypt;
import com.shunwang.baseStone.encrypt.pojo.EncryptItem;
import com.shunwang.basepassport.user.common.MemberConstants;
import com.shunwang.baseStone.core.action.BaseStoneAction;
import org.jdom.output.Format;
import org.jdom.Element;
import javax.servlet.http.HttpServletRequest;
import org.jdom.output.XMLOutputter;
import org.jdom.Document;
import java.io.OutputStream;
import javax.servlet.http.HttpServletResponse;
import java.util.UUID;
import java.io.FileOutputStream;
import java.io.FileInputStream;
import java.io.InputStream;
import java.io.IOException;
import com.shunwang.basepassport.user.common.HeadUrlUtil;
import com.shunwang.basepassport.user.dao.MemberDao;
import com.shunwang.util.StringUtil;
import java.io.File;
import com.shunwang.baseStone.sysconfig.context.SysConfigContext;
import com.shunwang.baseStone.context.BaseStoneContext;
import com.shunwang.basepassport.user.pojo.Member;

public class HeadImageModifyAction extends BaseStoneAction {

    private static final long serialVersionUID = 1665585228L;
	private final static Logger log = LoggerFactory.getLogger(HeadImageModifyAction.class);
	
    private static final String fileSplit = "/";
	private String memberId;
    private File newHeadImg;
    private File file;

    private String fileType;
    //文件的最大size,通过配置文件读入
    private int headImgMaxSize;

	@Override
	public String buildSignString() {
		Encrypt  encrypt=new Encrypt();
		encrypt.addItem(new EncryptItem(super.getSiteId()));
		encrypt.addItem(new EncryptItem(memberId));
		encrypt.addItem(new EncryptItem(super.getTime()));
		return encrypt.buildSign();
	}

	@Override
	public String getSiteName() {
		return MemberConstants.HEAD_IMG_MODIFY;
	}

    @Override
	public void checkParam() {
		if(StringUtil.isBlank(memberId)) throw new ParamNotFoundExp("memberId");
		if (newHeadImg == null) {
		    newHeadImg = file;
        }
		if(newHeadImg == null) throw new ParamNotFoundExp("newHeadImg");
        if (newHeadImg.length() /1024 > headImgMaxSize) throw new BaseStoneException(ErrorCode.C_1011);


        Tika tika = new Tika();
        String result = null;
        try {
            result = tika.detect(newHeadImg);
        } catch(IOException e)  {
            log.warn("读取修改用户头像接口的上传文件出错:" + e.getMessage());
        }
        if (result == null ) throw new FileTypeNotSupportedExp();
        if (result.equals("image/gif")) fileType="gif";
        if (result.equals("image/jpeg")) fileType="jpg";
        if (result.equals("image/png")) fileType = "png";
        if (fileType == null ) throw new FileTypeNotSupportedExp();

    }
	
	public void process() throws Exception {
        MemberDao memberDao = (MemberDao)BaseStoneContext.getInstance().getBean("memberDao");
        Member member = memberDao.getById(memberId);
        
        if (member == null ) {
			throw new MsgNotFoundExp("用户");
        }


		String id=UUID.randomUUID().toString() + "." + fileType;

        HeadUrlUtil util = new HeadUrlUtil();
		util.setHeadImageUrl(SysConfigContext.getImgUrl() + fileSplit + getFileDir());
		util.setHeadImagePath(SysConfigContext.getImgDir() + fileSplit + getFileDir());
        String bigPath = util.bigPath(member.getMemberId());
        String smallPath = util.smallPath(member.getMemberId());

        upload(newHeadImg, bigPath, id);
        upload(newHeadImg, smallPath, id);

        Integer memberId = member.getMemberId();
        // 修改头像前用户的图片
        String oldHeadImg = member.getHeadImg();

        //删除临时图片
        if (StringUtil.isNotBlank(oldHeadImg)) {
            util.deleteHeadImage(memberId, oldHeadImg);
            util.deleteSmallHeadImage(memberId, oldHeadImg);
            util.deleteTmpImage(memberId, oldHeadImg);
        }
        member.beginBuildLog("头像修改");
        member.setHeadImg(id);
        member.update();

        //写入用户头像表,用于运营审核
        MemberHeadImgService memberHeadImgService = (MemberHeadImgService)BaseStoneContext.getInstance().getBean("memberHeadImgService");
        memberHeadImgService.updateHeadImg(member,id);

        BaseStoneResponse response = new BaseStoneResponse();
        response.setMsgId("0");
        response.setMsg("操作成功");
        setBaseResponse(response);

	}

    private String getFileDir() {
		return HeadUrlUtil.M_UPLOAD;
	}

    private void upload(File file, String path, String name) throws Exception {
		if(file != null){	
			File dir = new File(path);
			if(!dir.exists())
				dir.mkdirs();
			File pathToSave = new File(path, name);  
			FileOutputStream out = null;
			InputStream input = null;
        	try {
        		input = new FileInputStream(file);
                int b=0;
                out = new FileOutputStream(pathToSave);
                while((b=input.read())!=-1)
                	out.write(b);
			} catch (Exception e) {
                log.error("fileName:["+name+"],上传异常",e);
			} finally{
				try {
					if(out !=null)
						out.close();
					if(input !=null){
						input.close();
					}
				} catch (IOException e) {
                    log.error("fileName:["+name+"],上传异常",e);
				}
			}
        }
	}



    public void process(HttpServletRequest request,HttpServletResponse response, HeadImageModifyResult result ){
        response.setContentType("text/xml;charset=UTF-8");
        response.setCharacterEncoding("UTF-8");
        try {
            //<kedou><Result msg="操作成功" msgId="0" returnSign=""><items><item>
            OutputStream out = response.getOutputStream();

            Element root = new Element("kedou");
            Document doc = new Document(root);
            doc.setRootElement(root);

            Element resultEle = new Element("Result");
            resultEle.setAttribute("msg", result.getMsg());
            resultEle.setAttribute("msgId", result.getMsgId());
            doc.getRootElement().addContent(resultEle);

            XMLOutputter xmlOutput = new XMLOutputter();

            xmlOutput.setFormat(Format.getPrettyFormat());
            xmlOutput.output(doc, out);
        } catch (IOException e) {
            log.error("xml文件写异常",e);
        }
    }



	
	public String getSmallHeadImageUrl(Member member) {
		return HeadUrlUtil.getSmallHeadImageUrl(member);
	}


    public void setMemberId(String memberId) {
        this.memberId=memberId;
    }
    public String getMemberId() {
        return this.memberId;
    }

    class HeadImageModifyResult {
        private boolean  succ;
        private String msg; 
        private String msgId;
        public void setSucc(boolean succ) {
            this.succ=succ;
        }
        public boolean getSucc() {
            return this.succ;
        }


        public void setMsg(String msg) {
            this.msg=msg;
        }
        public String getMsg() {
            return this.msg;
        }

        public void setMsgId(String msgId) {
            this.msgId=msgId;
        }
        public String getMsgId() {
            return this.msgId;
        }

    }



    


    public void setNewHeadImg(File newHeadImg) {
        this.newHeadImg=newHeadImg;
    }
    public File getNewHeadImg() {
        return this.newHeadImg;
    }

    public void setHeadImgMaxSize(int headImgMaxSize) {
        this.headImgMaxSize=headImgMaxSize;
    }
    public int getHeadImgMaxSize() {
        return this.headImgMaxSize;
    }

    public File getFile() {
        return file;
    }

    public void setFile(File file) {
        this.file = file;
    }
}
