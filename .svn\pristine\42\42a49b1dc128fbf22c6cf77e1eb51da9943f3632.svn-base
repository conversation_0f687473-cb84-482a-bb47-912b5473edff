package com.shunwang.basepassport.user.pojo;

import com.shunwang.baseStone.cache.CacheKeyConstant;
import com.shunwang.baseStone.context.BaseStoneContext;
import com.shunwang.baseStone.context.IPContext;
import com.shunwang.baseStone.context.RedisContext;
import com.shunwang.baseStone.core.detail.Detail;
import com.shunwang.baseStone.core.detail.DetailItem;
import com.shunwang.baseStone.core.detail.HasDetail;
import com.shunwang.baseStone.core.pojo.BaseStoneObject;
import com.shunwang.baseStone.siteinterface.context.SiteContext;
import com.shunwang.basepassport.actu.constant.ActuConstant;
import com.shunwang.basepassport.actu.dao.ActuDao;
import com.shunwang.basepassport.actu.pojo.ActuInfo;
import com.shunwang.basepassport.actu.pojo.PersonalActuVerifyRecord;
import com.shunwang.basepassport.binder.common.BinderConstants;
import com.shunwang.basepassport.binder.dao.EmailBinderDao;
import com.shunwang.basepassport.binder.dao.MobileBinderDao;
import com.shunwang.basepassport.binder.pojo.EmailBinder;
import com.shunwang.basepassport.binder.pojo.MobileBinder;
import com.shunwang.basepassport.commonExp.MsgIsExistExp;
import com.shunwang.basepassport.commonExp.PwdErrorExp;
import com.shunwang.basepassport.commonExp.UserFormateErrorExp;
import com.shunwang.basepassport.detail.common.DetailContants;
import com.shunwang.basepassport.detail.pojo.PersonalEditLog;
import com.shunwang.basepassport.mobile.constants.MobileCheckCodeConstants;
import com.shunwang.basepassport.safeNotice.constants.SafeNoticeConstants;
import com.shunwang.basepassport.user.common.*;
import com.shunwang.basepassport.user.dao.*;
import com.shunwang.basepassport.user.exception.*;
import com.shunwang.encrypt.core.annotation.EncryptEnabled;
import com.shunwang.encrypt.core.annotation.EncryptField;
import com.shunwang.exceptionContext.ExceptionContext;
import com.shunwang.util.date.DateUtil;
import com.shunwang.util.lang.StringUtil;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.text.DateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@EncryptEnabled
public class Member extends BaseStoneObject implements HasDetail {

    private static final long serialVersionUID = -220986472401817409L;
    private static final Logger log = LoggerFactory.getLogger(Member.class);
    private Integer memberId;//用户编号
    private String memberName;//用户名
    private String memberPwd;//用户密码
    private Integer bindState;//绑定状态
    private String realName;//真实姓名
    private String nickName;//昵称
    @EncryptField(ref = "emailCoded")
    private String email;//
    private String emailCoded;

    @EncryptField(ref = "mobileCoded")
    private String mobile;//
    private String mobileCoded;

    private String headImg;//头像
    private Date timeAdd;//注册时间
    private Date timeEdit;//修改时间
    private Date timeLogon;//最后登陆时间
    private Date timeCheck;
    private Integer memberType;//用户类型
    private Integer memberState;//用户状态
    private Date pwdErrTime; //
    private Integer pwdErrNum; //
    private Integer personCertState;
    private Integer companyCertState;
    @EncryptField
    private MemberInfo memberInfo;//用户扩展信息
    private ActuInfo actuInfo;
    private MemberProtectedQuestion memberProtectedQuestions;
    private EmailBinder emailBinder;
    private MobileBinder mobileBinder;
    private String companyName;
    private PersonalEditLog personalEditLog = new PersonalEditLog();
    private String loginType;
    private Date lifeTime;
    private String titleName; //显示名称 用户显示
    private String siteType;  // 0站内用户 1站外导入用户
    private MemberOutSite memberOutSite;
    private String vrsion;
    private String env;
    private String extData;
    private String clientIp;
    private MemberSafeNotice memberSafeNotice;
    private MemberSafeGuard memberSafeGuard;
    private Integer safeItemsScore;  //开启安全功能总得分
    private Integer cafeCertState;//网吧业主实名认证
    private Integer memberSpecialType;//用户特殊类型 1:吉胜网吧业主
    //1-是弱密码，2-不是弱密码
    private String weakPwdState;

    private String accessSiteId ;//第三方作业单元siteId（在记录登录日志时，会记到第三方上面)

    private String openId;

    /**
     * 假装是企业用户,（适用于个人升级企业实名认证信息已提交但是后台还未审核时，值为true）
     * 否则为 false
     * 该值通过外部调用处查询并设置
     */
    private boolean fakeCompanyCert;


    /**
     * 是否是从接口注册(按2013-05-31运营需求, 接口注册和web的注册的身份证验证算法要求不一样) web注册时只验证身份证的年龄, 接口注册时做其他校验证
     */
    private boolean regFromInterface = false;

    /**
     * 防沉迷认证
     */
    private boolean antiAdditionCert;
    private PersonalActuVerifyRecord personalActuVerifyRecord;

    private String pwdType;


    public String getEmailCoded() {
        return emailCoded;
    }

    public void setEmailCoded(String emailCoded) {
        this.emailCoded = emailCoded;
    }

    public String getMobileCoded() {
        return mobileCoded;
    }

    public void setMobileCoded(String mobileCoded) {
        this.mobileCoded = mobileCoded;
    }

    public Member() {
        super();
    }

    public String getLoginType() {
        return loginType;
    }

    public void setLoginType(String loginType) {
        this.loginType = loginType;
    }

    /**
     * 重置密码
     * @param oldPwd 旧密码
     * @param newPwd 新密码
     * <AUTHOR> create at 2011-7-21 下午04:01:42
     */
    public void resetPwd(String oldPwd, String newPwd) {

        oldPwd = PwdUtil.convertMd5(oldPwd);
        if (isPwdRight(oldPwd)) {
            doResetPwd(newPwd);
        } else {
            throw new PwdErrorExp("原");
        }
    }

    /**
     * 检查密码是否正确
     * <AUTHOR> create at 2011-7-21 下午04:41:18
     */
    private boolean isPwdRight(String oldPwd) {
        return this.memberPwd.equalsIgnoreCase(oldPwd);
    }

    /**
     * 重置密码
     * @param newPwd 新密码
     * <AUTHOR> create at 2011-7-21 下午02:58:36
     */
    public void doResetPwd(String newPwd) {
        if (StringUtil.isBlank(((PersonalEditLog) getDetail()).getEditItem())) {
            beginBuildLog("密码修改");
        }

        newPwd = PwdUtil.convertMd5(newPwd);
        buildLog(DetailContants.FRONT);
        this.setMemberPwd(newPwd);
        getDao().update(this);
        createMemberLog(this);
    }

    public void doResetMobile(String oldMobile) {
        if (StringUtil.isBlank(((PersonalEditLog) getDetail()).getEditItem())) {
            beginBuildLog("修改手机号码");
        }
        buildLog(DetailContants.FRONT, oldMobile);
        getDao().update(this);
    }
    public void doResetEmail(String oldEmail) {
        if (StringUtil.isBlank(((PersonalEditLog) getDetail()).getEditItem())) {
            beginBuildLog("修改邮箱");
        }
        buildLog(DetailContants.FRONT, oldEmail);
        getDao().update(this);
    }
    /**
     * 加载会员的扩展信息
     * <AUTHOR> create at 2011-7-25 下午01:36:09
     */
    public MemberInfo loadMemberInfo() {
        if (null == memberInfo) {
            memberInfo = getMemberInfoDao().getById(this.memberId);
        }
        return memberInfo;
    }

    /**
     * 用户登录
     * <AUTHOR> create at 2011-7-25 下午01:39:34
     */
    public void login(String oldPwd) {
        String time = RedisContext.getRedisCache().get(getPwdErrInvalidTimeKey(this.getMemberName()));
        long leftTime = StringUtil.isBlank(time) ? -1 : getLimitSecondsToLimitLogin() -  DateUtil.compare(new Date(), new Date(Long.parseLong(time)), DateUtil.ONE_SECOND);
        if (StringUtil.isNotBlank(time) && leftTime > 0) {
            throw new LoginPwdErrorExp(getErrTimesToLimitLogin(), leftTime);
        }
        oldPwd = PwdUtil.convertMd5(oldPwd);
        if(StringUtil.isBlank(memberPwd)){
            throw new LoginNullPwdErrorExp();
        }
        if (isPwdRight(oldPwd)) {
            UserCheckUtil.checkMemberState(this.getMemberState(), null);
            doLogin(loginType);
            return;
        }
        processPwdError();
    }

    /**
     * 无密码登录
     *
     * @author:  YHJ create at 2012-2-21 下午04:21:11
     */
    public void loginWithNoPwd() {
        UserCheckUtil.checkMemberState(this.getMemberState(), null);
        doLogin(loginType);
    }

    /**
     * 密码错误次数处理
     *
     * <AUTHOR>  create at 2011-8-1 下午04:49:55
     */
    private void processPwdError() {
    	String pwdErrKey = getPwdErrKey(this.getMemberName());

        Integer errNum  = RedisContext.getRedisCache().get(pwdErrKey,Integer.class);

    	if(errNum == null) {
            errNum = 0;
    	}
        errNum = errNum + 1;

        RedisContext.getRedisCache().set(pwdErrKey, errNum+"", 1,TimeUnit.DAYS);

        if (errNum >= getErrTimesToLimitLogin()) {
            RedisContext.getRedisCache().set(getPwdErrInvalidTimeKey(this.getMemberName()), new Date().getTime(), getLimitSecondsToLimitLogin(), TimeUnit.SECONDS);
        }
        if(log.isInfoEnabled()) {
			log.info("[redis set] SetErrNum,key=" + pwdErrKey + ",value=" + errNum);
		}
    	
		throw new LoginPwdErrorExp(errNum);
    }

    /**
     * 登录，写登录日志，更新最后登录时间（加事务）
     * <AUTHOR> create at 2011-7-26 下午04:01:38
     */
    private void doLogin(String loginType) {
    	doPwdErrNum(this.getMemberName());
        saveLog(loginType);
    }

    /**
     * 互联组件快速登录调用. 此调用产生的logonlog的pwdType为4
     */
    public void quickLogin() {
        UserCheckUtil.checkMemberState(this.getMemberState(), null);
        doPwdErrNum(this.getMemberName());
        LogonLogUtil.saveLog(buildQuickLogonLog());
    }
    
    public void doPwdErrNum(String memberName) {
    	String pwdErrKey = getPwdErrKey(memberName);
    	Integer errNum = RedisContext.getRedisCache().get(pwdErrKey,Integer.class);
        if(errNum == null){
            errNum = 0;
        }
    	
    	if(errNum > 0) {
    		RedisContext.getRedisCache().set(pwdErrKey, "1", 1, TimeUnit.DAYS);
    		if(log.isInfoEnabled()) {
    			log.info("[redis set]SetErrNum status ,key=" + pwdErrKey + ",value=" + 0);
    		}
    	}
    }
    
    public String getPwdErrKey(String memberName) {
    	return CacheKeyConstant.SSO.LOGIN_PERR+ memberName +CacheKeyConstant.CACHE_SPLIT + DateUtil.ymdTimeFormat(new Date());
    }

    public String getPwdErrInvalidTimeKey(String memberName) {
        return getPwdErrKey(memberName) + "_invalid";
    }

    /**
     * 注册
     * <AUTHOR> create at 2011-7-26 上午10:45:35
     */
    public Member regist() {
        return this.regist(true) ;
    }

    /**
     * 是否检查前缀，默认检查
     * @param checkPrefix 前缀
     */
    public Member regist(boolean checkPrefix) {
        this.check(checkPrefix);
        return doRegist();
    }

    /**
     * 微信公众号多次回调加锁，避免重复写登录日志
     * @param loginType 登录类型
     */
    private void saveLog(String loginType) {
        if (RedisContext.getRedisCache().setNx(CacheKeyConstant.SSO.LOGIN_LOG_LOCK_ + loginType + "_" + memberId, " ", 20)) {
            LogonLogUtil.saveLog(buildLogonLog(loginType));
        }
    }

    /**
     * 更新是否弱密码
     * @param weakPwdState 为空及相等时不更新，其他传接入方的值
     */
    public void updateWeakPwdState(String weakPwdState) {
        //接入方未传时，不做处理
        if (StringUtil.isBlank(weakPwdState)) {
            return;
        }
        //传入的与数据库不同时,进行更新，
        if (StringUtil.isBlank(this.weakPwdState) || !this.weakPwdState.equals(weakPwdState)) {
            this.weakPwdState = weakPwdState;
            this.setTimeEdit(new Date());
            update();
        }
    }
    /**
     * 构建登录日志
     * <AUTHOR> create at 2011-7-26 下午03:53:42
     */
    @SuppressWarnings("deprecation")
    private LogonLog buildLogonLog(String loginType) {
        LogonLog logonLog = new LogonLog();
        logonLog.setLogonType(loginType);
        logonLog.setClientIp(StringUtil.isBlank(clientIp) ? IPContext.getIp() : clientIp);
        logonLog.setMemberId(memberId);
        logonLog.setLoginFrom(accessSiteId != null ? accessSiteId:SiteContext.getSiteId());
        logonLog.setMemberName(memberName);

        if (this.getIsBindDynamicPwd()) {
            logonLog.setPwdType(MemberConstants.PWD_TYPE_DYNAMIC_PWD);
            logonLog.setStep(MemberConstants.LOGIN_STEP);
        } else if (this.getIsBindShunLing()) {
            logonLog.setPwdType(MemberConstants.PWD_TYPE_DYNAMIC_PASSWORD);
            logonLog.setStep(MemberConstants.LOGIN_STEP);
        }
        if (MemberConstants.LOGIN_TYPE_ONE_LOGIN.equals(loginType) && StringUtil.isNotBlank(pwdType)){
            logonLog.setPwdType(pwdType);
        }

        logonLog.setLoginVersion(vrsion);
        logonLog.setLogonEnvironment(env);
        logonLog.setRemark(extData);

        return logonLog;
    }

    private LogonLog buildQuickLogonLog() {
        LogonLog logonLog = new LogonLog();
        logonLog.setLogonType(MemberConstants.LOGIN_TYPE_ACCOUNT);
        logonLog.setPwdType(MemberConstants.PWD_TYPE_QUICKLOGIN);
        logonLog.setClientIp(IPContext.getIp());
        logonLog.setMemberId(memberId);
        logonLog.setLoginFrom(SiteContext.getSiteId());
        logonLog.setMemberName(memberName);
        logonLog.setLoginVersion(vrsion);
        logonLog.setLogonEnvironment(env);
        logonLog.setRemark(extData);

        return logonLog;
    }

    /**
     * 注册，存档到数据库
     * <AUTHOR> create at 2011-7-25 下午01:41:18
     */
    public Member doRegist() {
        if (MemberConstants.USER_TYPE_PERSONAL.equals(memberType)) {
            this.setTitleName(StringUtil.isBlank(realName) ? memberName : realName);
        } else {
            this.setTitleName(StringUtil.isBlank(companyName) ? memberName : companyName);
        }
        this.siteType =
            StringUtil.isBlank(this.siteType) ? MemberConstants.SITE_TYPE_IN : this.siteType;
        
        TransactionTemplate template = (TransactionTemplate)BaseStoneContext.getInstance().getBean("basepassport.transactionTemplate");
    	template.execute(new TransactionCallback() {
            @Override
            public Member doInTransaction(TransactionStatus status) {
                try {
                	Member memberInDb = getDao().save(Member.this);
                	Member.this.setMemberId(memberInDb.getMemberId());
                    Member.this.getMemberInfo().setMemberId(memberInDb.getMemberId());
                    getMemberInfoDao().save(memberInfo);

                    if (null != memberProtectedQuestions) {
                        memberProtectedQuestions.setMember(Member.this);
                        memberProtectedQuestions.save();
                    }
                    if (null != emailBinder) {
                        emailBinder.setMemberId(Member.this.getMemberId());
                        Member.this.getEmailBinderDao().save(emailBinder);
                    }
                    if (null != mobileBinder) {
                        mobileBinder.setMemberId(Member.this.getMemberId());
                        Member.this.getMobileBinderDao().save(mobileBinder);
                    }
                    createMemberLog(Member.this);

                    return Member.this;
                } catch (Exception e) {
                    status.setRollbackOnly();
                    log.error("用户" + Member.this.getTitleName() + "注册异常", e);
                    throw new RuntimeException(e);
                }
            }
        });
        
        return this;
    }

    /**
     * 更新用户信息
     * <AUTHOR> create at 2011-7-29 下午01:47:27
     */
    public void update() {
        buildLog(DetailContants.FRONT);
        getDao().update(this);
        if (null != memberInfo) {
            getMemberInfoDao().update(memberInfo);
        }
    }

    /**
     * 初始化登录信息
     *
     * <AUTHOR> create at 2013-8-4 下午02:13:35
     */
    public void initLoginMsg(String version, String env, String extData) {
        setVrsion(version);
        setEnv(env);
        setExtData(extData);
    }

    /**
     * 加载用户的实名认证信息
     * <AUTHOR> create at 2011-7-27 下午02:02:47
     */
    public ActuInfo loadActuInfo() {
        if (null == actuInfo) {
            actuInfo = getActuDao().getByMemberId(this.memberId);
        }
        return actuInfo;
    }

    /**
     * 加载emailBinder
     *
     * <AUTHOR> 创建于 2011-8-22 下午01:57:39
     */
    public EmailBinder loadEmailBinder() {
        if (null == emailBinder) {
            emailBinder = (EmailBinder) getEmailBinderDao().getById(this.memberId);
        }
        return emailBinder;
    }

    /**
     * 加载emailBinder
     *
     * <AUTHOR> 创建于 2011-8-22 下午01:57:39
     */
    public MobileBinder loadMobileBinder() {
        if (null == mobileBinder) {
            mobileBinder = (MobileBinder) getMobileBinderDao().getById(this.memberId);
        }
        return mobileBinder;
    }

    public MemberOutSite loadMemberOutSite() {
        if (null == memberOutSite) {
            memberOutSite = getMemberOutSiteDao().getById(this.getMemberId());
        }
        return memberOutSite;
    }

    /**
     * 是否绑定过邮箱
     *
     * <AUTHOR> 创建于 2011-8-22 下午02:30:17
     */
    public boolean getIsBindEmail() {
        return this.getIsBind(MemberConstants.MEMBER_STATE_MAIL);
    }

    /**
     * 设置邮箱绑定状态
     *
     * <AUTHOR> 创建于 2011-8-22 下午12:42:36
     */
    public void setIsBindEmail(boolean isBind) {
        this.setIsBind(isBind, MemberConstants.MEMBER_STATE_MAIL);
    }

    /**
     * 是否绑定手机
     *
     * <AUTHOR> 创建于 2011-8-22 下午03:55:07
     */
    public boolean getIsBindMobile() {
        return this.getIsBind(MemberConstants.MEMBER_STATE_PHONE);
    }

    public boolean getIsEmptyPwd() {
        return StringUtil.isBlank(memberPwd);
    }

    /**
     * 设置手机绑定状态
     *
     * <AUTHOR> 创建于 2011-8-22 下午12:42:56
     */
    public void setIsBindMobile(boolean isBind) {
        this.setIsBind(isBind, MemberConstants.MEMBER_STATE_PHONE);
    }

    /**
     * 是否绑定了密保问题
     *
     * <AUTHOR> 创建于 2011-8-22 下午04:02:02
     */
    public boolean getIsBindQuestion() {
        return this.getIsBind(MemberConstants.MEMBER_STATE_PROBLEM);
    }

    /**
     * 设置密保问题绑定状态
     *
     * <AUTHOR> 创建于 2011-8-22 下午03:58:56
     */
    public void setIsBindQuestion(boolean isBind) {
        this.setIsBind(isBind, MemberConstants.MEMBER_STATE_PROBLEM);
    }

    /**
     * 是否绑定密保卡
     *
     * <AUTHOR> 创建于 2011-8-22 下午04:04:12
     */
    public boolean getIsBindSafeCard() {
        //密保卡废除
        return false;
    }

    /**
     * 设置密保卡绑定状态
     *
     * <AUTHOR> 创建于 2011-8-22 下午04:08:26
     */
    public void setIsBindSafeCard(boolean isBind) {
        this.setIsBind(isBind, MemberConstants.MEMBER_STATE_SAFECARD);
    }

    /**
     * *********** 创建日期: 2013-7-25 创建作者：JINBAO
     *
     * @return boolean 功能：是否绑定手机动态验证码 ************
     */
    public boolean getIsBindDynamicPwd() {
        return this.getIsBind(MemberConstants.MEMBER_STATE_DYNAMIC_PWD);
    }

    /**
     * *********** 创建日期: 2013-7-25 创建作者：JINBAO
     * 功能：设置动态验证码绑定状态 ************
     */
    public void setIsBindDynamicPwd(boolean isBind) {
        this.setIsBind(isBind, MemberConstants.MEMBER_STATE_DYNAMIC_PWD);
    }

    /**
     * *********** 创建日期: 2013-7-25 创建作者：JINBAO
     *
     * @return boolean 功能：是否绑定手机顺令 ************
     */
    public boolean getIsBindShunLing() {
        return this.getIsBind(MemberConstants.MEMBER_STATE_SHUN_LING);
    }

    /**
     * *********** 创建日期: 2013-7-25 创建作者：JINBAO
     * 功能：设置手机顺令绑定状态 ************
     */
    public void setIsBindShunLing(boolean isBind) {
        this.setIsBind(isBind, MemberConstants.MEMBER_STATE_SHUN_LING);
    }

    /**
     * ** 创建日期: 2013-9-9 创建作者：JINBAO
     *
     * @return String 功能：获取Android下载地址 ************
     */
    public String getDownloadUrlAndroid() {
        return RedisContext.getResourceCache().getResourceValue(MobileCheckCodeConstants.SHUN_LING_CONGIF, MobileCheckCodeConstants.SHUN_LING_DOWN_URL_ANDROID);
    }

    /**
     * ** 创建日期: 2013-9-9 创建作者：JINBAO
     *
     * @return String 功能：获取IOS下载地址 ************
     */
    public String getDownloadUrlIos() {
        return RedisContext.getResourceCache().getResourceValue(MobileCheckCodeConstants.SHUN_LING_CONGIF, MobileCheckCodeConstants.SHUN_LING_DOWN_URL_IOS);
    }

    public String getVersionAndroid() {
        return RedisContext.getResourceCache().getResourceValue(MobileCheckCodeConstants.SHUN_LING_CONGIF, MobileCheckCodeConstants.SHUN_LING_VERSION_ANDROID);
    }

    public String getVersionIos() {
    	return RedisContext.getResourceCache().getResourceValue(MobileCheckCodeConstants.SHUN_LING_CONGIF, MobileCheckCodeConstants.SHUN_LING_VERSION_IOS);
    }

    /**
     * 是否开通账户安全异动提醒
     *
     * @return 创建日期: 2013-12-6上午10:08:57 创建作者: Zeng Lingjie
     */
    public boolean getIsBindSafeNotice() {
        return this.getIsBind(MemberConstants.MEMBER_STATE_SAFE_NOTICE);
    }

    /**
     * 设置账户安全异动提醒状态
     *
     * @param isBind 创建日期: 2013-12-6上午11:03:40 创建作者: Zeng Lingjie
     */
    public void setIsBindSafeNotice(boolean isBind) {
        this.setIsBind(isBind, MemberConstants.MEMBER_STATE_SAFE_NOTICE);
    }

    /**
     * 账户安全异动提醒是否开启
     *
     * @return 创建日期: 2013-12-6上午10:51:36 创建作者: Zeng Lingjie
     */
    public Boolean getSafeNoticeIsOpen() {
        return getSafeNoticeItemIsOpen(SafeNoticeConstants.SAFE_NOTICE_IS_OPEN);
    }

    /**
     * 账户安全-邮件修改提醒开关
     *
     * @return 创建日期: 2013-12-10下午05:01:25 创建作者: Zeng Lingjie
     */
    public boolean getSafeNoticeEmailChangeIsOpen() {
        return getSafeNoticeItemIsOpen(SafeNoticeConstants.EMAIL_CHANGE_NOTICE_IS_OPEN);
    }

    /**
     * 账户安全-手机修改提醒开关
     *
     * @return 创建日期: 2013-12-10下午05:01:25 创建作者: Zeng Lingjie
     */
    public boolean getSafeNoticeMobielChangeIsOpen() {
        return getSafeNoticeItemIsOpen(SafeNoticeConstants.MOBILE_CHANGE_NOTICE_IS_OPEN);
    }

    /**
     * 账户安全-密码修改提醒开关
     *
     * @return 创建日期: 2013-12-10下午05:01:25 创建作者: Zeng Lingjie
     */
    public boolean getSafeNoticePWDChangeIsOpen() {
        return getSafeNoticeItemIsOpen(SafeNoticeConstants.PWD_CHANGE_NOTICE_IS_OPEN);
    }

    /**
     * 账户安全-密保问题提醒开关
     *
     * @return 创建日期: 2013-12-10下午05:01:25 创建作者: Zeng Lingjie
     */
    public boolean getSafeNoticeQuestionChangeIsOpen() {
        return getSafeNoticeItemIsOpen(SafeNoticeConstants.QUESTION_CHANGE_NOTICE_IS_OPEN);
    }

    /**
     * 获取账户安全提醒开关
     *
     * @return 创建日期: 2013-12-10下午05:13:46 创建作者: Zeng Lingjie
     */
    private boolean getSafeNoticeItemIsOpen(String name) {
        Boolean isOpen = Boolean.TRUE;
        String noticeIsOpen = RedisContext.getResourceCache().getResourceValue(SafeNoticeConstants.SAFE_NOTICE_CONFIG, name);
        if ("n".equalsIgnoreCase(noticeIsOpen)) {
            isOpen = Boolean.FALSE;
        }
        return isOpen;
    }

    /**
     * 账户安全提醒系统开通状态map(若有新的项添加则添加相应项到map)
     *
     * @return 创建日期: 2013-12-30下午03:29:25 创建作者: Zeng Lingjie
     */
    private Map<Integer, Boolean> getSysSafeNoticeItemIsOpenMap() {
        Map<Integer, Boolean> map = new HashMap<>();
        map.put(SafeNoticeConstants.SAFE_NOTICE_STATE_PWD_CHANGE, getSafeNoticePWDChangeIsOpen());
        map.put(SafeNoticeConstants.SAFE_NOTICE_STATE_MOBILE_CHANGE,
                getSafeNoticeMobielChangeIsOpen());
        map.put(SafeNoticeConstants.SAFE_NOTICE_STATE_EMAIL_CHANGE,
                getSafeNoticeEmailChangeIsOpen());
        map.put(SafeNoticeConstants.SAFE_NOTICE_STATE_QUESTION_CHANGE,
                getSafeNoticeQuestionChangeIsOpen());
        return map;
    }

    /**
     * 获取用户安全异动项开启的状态
     *
     * @return 创建日期: 2013-12-30上午11:22:39 创建作者: Zeng Lingjie
     */
    public String getSafeNoticeOpenStateStr() {
        int sysOpenNum = 0;
        int userOpenNum = 0;
        Map<Integer, Boolean> map = getSysSafeNoticeItemIsOpenMap();
        Set<Integer> keySet = map.keySet();
        if (getMemberSafeNotice() == null || getMemberSafeNotice().getNoticeState() == 0) {
            for (Integer item : keySet) {
                if (Boolean.TRUE.equals(map.get(item))) {
                    sysOpenNum++;
                }
            }
        } else {
            for (Integer item : keySet) {
                if (Boolean.TRUE.equals(map.get(item))) {
                    sysOpenNum++;
                    if (getMemberSafeNotice().getIsOpen(item)) {
                        userOpenNum++;
                    }
                }
            }
        }
        return "(" + userOpenNum + "/" + sysOpenNum + ")";
    }

    /**
     * 获取用户是否有账户安全异动项打开
     *
     * @return 创建日期: 2013-12-30上午11:21:38 创建作者: Zeng Lingjie
     */
    public boolean getAnySafeNoticeHasOpen() {
        Map<Integer, Boolean> map = getSysSafeNoticeItemIsOpenMap();
        Set<Integer> keySet = map.keySet();
        if (getMemberSafeNotice() == null || getMemberSafeNotice().getNoticeState() == 0) {
            return false;
        } else {
            for (Integer item : keySet) {
                if (map.get(item)) {
                    if (getMemberSafeNotice().getIsOpen(item)) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    /**
     * 获取账户安全异动提醒dao
     *
     * @return 创建日期: 2013-12-30上午10:43:29 创建作者: Zeng Lingjie
     */
    private MemberSafeNoticeDao getMemberSafeNoticeDao() {
        return (MemberSafeNoticeDao) BaseStoneContext.getInstance().getBean("memberSafeNoticeDao");
    }

    /**
     * 校验用户信息
     * <AUTHOR> create at 2011-7-26 上午10:45:15
     */
    private void check(boolean checkPrefix) {
        String siteId = this.memberInfo.getRegFrom();
        //临时方案：结算不检查中文2019-08-07
        if (!"sw_pay".equalsIgnoreCase(siteId)) {
            checkUserName();
        }

        checkSensitivityWord();
        checkMail();
        checkMobile();
        checkIDCard();
        checkProtectQuestion();
        checkPostCode();
        //临时方案，结算请求的注册不检查前缀，其他保持检查2019-08-07
        if ("sw_pay".equalsIgnoreCase(siteId)) {
            checkUserNameIsExist(false);
        } else {
            checkUserNameIsExist(checkPrefix);
        }
        checkMailBindMemberCount(); //一个邮箱可以绑定多个通行证后,这里改为检验邮箱绑定过的通行证数量是否超过限制
        checkMobileBindMemberCount();//一个手机号码可以绑定多个通行证后,这里改为检验邮箱绑定过的通行证数量是否超过限制
    }

    /**
     * 检查用户名
     * <AUTHOR> create at 2011-7-26 下午03:13:27
     */
    private void checkUserNameIsExist(boolean checkPrefix) {
        if (keepName(memberName) || null != getDao().getByName(memberName) || checkPrefix && !UserCheckUtil
            .checkPrefixName(memberName)) {
            throw new MsgIsExistExp("用户名["+memberName+"]");
        }
    }

    /**
     * *********** 创建日期: 20112011-8-24下午05:16:34 创建作者：jinbao
     *
     * @return 功能：宇酷关于保留部分用户名：需要保留的用户名字符串为BX00001到BX9999999999999 ************
     */
    public boolean keepName(String str) {
        Pattern pattern1 = Pattern.compile("BX(?=\\d*[1-9])\\d{5,13}", Pattern.CASE_INSENSITIVE);
        Matcher matcher1 = pattern1.matcher(str);
        return matcher1.matches();
    }

    /**
     * 检测邮箱是否存在
     * <AUTHOR> create at 2011-7-26 下午03:18:42
     */
    private void checkMailIsExist() {//修改
        if (null == this.getEmail()) {
            return;
        }
        if (null != getDao().getByEmail(this.getEmail())) {
            throw new MsgIsExistExp("邮箱["+this.getEmail()+"]");
        }
    }

    /**
     * 检查邮箱绑定过的通行证数量是否超过限制
     *
     * <AUTHOR>
     * 2014年10月21日17:48:20
     */
    private void checkMailBindMemberCount() {
        if (StringUtils.isBlank(this.getEmail())) return;
        Integer cnt = getDao().getCntByEmail(this.getEmail());
        if (null != cnt && cnt >= BinderConstants.BINDER_LIMIT_EMAIL)
            throw new EmailBindLimitExp();
    }

    @SuppressWarnings("unused")
    private void checkIdcardExists() {
        if (null == this.getMemberInfo() || null == this.getMemberInfo().getIdCardNo()) {
            return;
        }
        String idcard = this.getMemberInfo().getIdCardNo();
        //如果身份证为空,则不检查
        if (idcard == null || StringUtil.isBlank(idcard)) {
            return;
        }
        if (null != this.getMemberInfoDao().getByIdcard(idcard)) {
            throw new MsgIsExistExp("身份证号["+idcard+"]");
        }
    }

    /**
     * 检测手机是否已存在
     * <AUTHOR> create at 2011-7-26 下午03:18:57
     */
    private void checkMobileIsExist() {
        if (null == this.getMobile()) {
            return;
        }
        if (null != getDao().getByMobile(this.getMobile())) {
            throw new MsgIsExistExp("手机["+this.getMobile()+"]");
        }
    }

    /**
     * 检查手机号码绑定过的通行证数量是否超过限制
     *
     * <AUTHOR>
     * 2014年10月21日17:48:26
     */
    private void checkMobileBindMemberCount() {
        if (StringUtils.isBlank(this.getMobile())) return;
        Integer cnt = getDao().getCntByMobile(this.getMobile());
        if (null != cnt && cnt >= BinderConstants.getMobileBinderLimit())
            throw new MobileBindLimitExp();
    }

    /**
     * 构建用户日志
     *
     * <AUTHOR> create at 2012-3-30 下午01:57:45
     */
    private void createMemberLog(Member currentMember) {
        MemberLog memberLog = new MemberLog();
        memberLog.setLogItem(MemberConstants.MEMBER_LOG_ITEM_PWD);
        memberLog.setLogValue(currentMember.getMemberPwd());
        memberLog.setMemberId(currentMember.getMemberId());
        memberLog.setTimeAdd(new Date());
        memberLog.save();
    }

    /**
     * 检查用户名
     * <AUTHOR> create at 2011-7-26 上午10:35:45
     */
    private void checkUserName() {
        if (StringUtil.isBlank(this.memberName)) {
            throw new MsgNullExp("用户名");
        }
        if (!UserCheckUtil.checkNameIsAvailable(this.memberName)) {
            throw new UserFormateErrorExp("用户名");
        }
    }

    /**
     * 检测敏感词
     * <AUTHOR> create at 2011-7-26 上午10:35:58
     */
    private void checkSensitivityWord() {
        if (StringUtil.isBlank(this.memberName)) {
            throw new MsgNullExp("用户名");
        }
        if (UserCheckUtil.checkIsSensitivityWord(this.memberName)) {
            throw new UserNameSensitiveExp();
        }
        if(StringUtils.isNotBlank(this.nickName)) {
            if (UserCheckUtil.checkIsSensitivityWord(this.nickName)) {
                throw  new UserNameSensitiveExp("昵称不能为敏感词！");
            }
        }
    }

    /**
     * 检查邮箱
     * <AUTHOR> create at 2011-7-26 上午10:35:58
     */
    private void checkMail() {
        checkMemberInfo();
        if (isNeedBindMail() && StringUtil.isBlank(this.getEmail())) {
            throw new MsgNullExp("邮箱");
        } else if (isNeedBindMail() && !UserCheckUtil.checkEmail(this.getEmail())) {
            throw new UserFormateErrorExp("邮箱");
        } else if (!isNeedBindMail()) {
            this.setEmail(null);
        }
    }

    /**
     * 检测用户扩展信息
     * <AUTHOR> create at 2011-7-26 上午11:28:31
     */
    private void checkMemberInfo() {
        if (null == this.getMemberInfo()) {
            throw new MsgNullExp("用户扩展信息");
        }
    }

    /**
     * 检测用户是否需要绑定
     * <AUTHOR> create at 2011-7-26 上午11:30:01
     */
    private boolean isNeedBindMail() {
        return this.getIsBindEmail();
    }

    /**
     * 检测是否需要绑定手机
     * <AUTHOR> create at 2011-7-26 下午12:43:43
     */
    private boolean isNeedMobile() {
        return this.getIsBindMobile();
    }

    /**
     * 检测手机
     * <AUTHOR> create at 2011-7-26 上午10:35:58
     */
    private void checkMobile() {
        checkMemberInfo();
        if (isNeedMobile() && StringUtil.isBlank(this.getMobile())) {
            throw new MsgNullExp("手机");
        } else if (isNeedMobile() && !UserCheckUtil.checkMobile(this.getMobile())) {
            throw new UserFormateErrorExp("手机");
        } else if (!isNeedMobile()) {
            this.setMobile(null);
        }
    }

    /**
     * 检测身份证
     * <AUTHOR> create at 2011-7-26 上午10:35:58
     */
    private void checkIDCard() {

        if (StringUtil.isNotBlank(this.getMemberInfo().getIdCardNo())) {

            /* 只有接口过来的才做合法性验证 */
            if (this.isRegFromInterface()) {
                if (!IDCardCheckUtil.isAvailableIDCard(this.getMemberInfo().getIdCardNo())) {
                    throw new UserFormateErrorExp("身份证号码");
                }
            } else {
                /* 页面过需要验证年龄 */
                if (!IDCardCheckUtil.isAgeAbove18(this.getMemberInfo().getIdCardNo())) {
                    throw new UserFormateErrorExp("注册用户未满18周岁或身份证日期非法", "");
                }
            }
        }
    }

    /**
     * 检测密保问题
     * <AUTHOR> create at 2011-7-26 上午10:35:58
     */
    private void checkProtectQuestion() {
        if (null != this.memberProtectedQuestions && !this.memberProtectedQuestions.isAvailable()) {
            throw new MsgNotFoundExp("密保问题编号");
        }
    }

    /**
     * 检测邮政编码
     * <AUTHOR> create at 2011-7-26 上午10:35:58
     */
    private void checkPostCode() {
        if (StringUtil.isNotBlank(memberInfo.getPostCode()) && !UserCheckUtil
            .checkPostalCode(memberInfo.getPostCode())) {
            throw new UserFormateErrorExp("邮政编码");
        }
    }

    /**
     * 是否绑定过（bindState为四种绑定状态）
     *
     * <AUTHOR> 创建于 2011-8-22 下午02:29:03
     */
    private boolean getIsBind(Integer bindState) {
        return (this.getBindState() & bindState) == bindState;
    }

    /**
     * 设置绑定状态
     *
     * <AUTHOR> 创建于 2011-8-22 下午04:07:32
     */
    private void setIsBind(boolean isBind, Integer bindState) {
        if (isBind && !this.getIsBind(bindState))//绑定
        {
            this.setBindState(this.getBindState() | bindState);
        } else if (!isBind && this.getIsBind(bindState))//解绑
        {
            this.setBindState(this.getBindState() ^ bindState);
        }
    }

    /**
     * *********** 创建日期: Jul 21, 2011 2:00:58 PM 创建作者：chenjh
     *
     * @return 功能：判断是否是成年人
     */
    @SuppressWarnings("unused")
    private boolean isAdult() {
        Integer age = getAge(this.getMemberInfo().getIdCardNo());
        if (age >= 18) {
            return true;
        }
        return false;
    }

    /**
     * *********** 创建日期: Jul 21, 2011 2:58:53 PM 创建作者：chenjh
     *
     * @return 功能：由身份证号获取年龄
     */
    private Integer getAge(String id) {
        String birthday = "";
        DateFormat format = DateUtil.getInstance("yyyymmdd");
        try {
            Date nowDate = DateUtil.praseDate(format, DateUtil.getCurrentDateStr());
            int len = id.length();
            if (len == MemberConstants.NUM_EIGHTEEN) {//身份证位数为18
                int IDYear = Integer.parseInt(id.substring(6, 10));
                int IDMonth = Integer.parseInt(id.substring(10, 12));
                int IDDay = Integer.parseInt(id.substring(12, 14));
                birthday =
                    IDYear + "" + (IDMonth < 10 ? ("0" + IDMonth) : IDMonth) + (IDDay < 10 ? ("0"
                                                                                              + IDDay)
                                                                                           : IDDay);
                long
                    age =
                    DateUtil.compareToFloor(DateUtil.praseDate(format, birthday), nowDate,
                                            DateUtil.ONE_YEAR);
                return Integer.parseInt(Math.abs(age) + "");
            }
            if (len == MemberConstants.NUM_FIFTEEN) {//身份证位数为15
                int IDYear = Integer.parseInt(id.substring(6, 8)) + 1900;
                int IDMonth = Integer.parseInt(id.substring(8, 10));
                int IDDay = Integer.parseInt(id.substring(10, 12));
                birthday =
                    IDYear + "" + (IDMonth < 10 ? ("0" + IDMonth) : IDMonth) + (IDDay < 10 ? ("0"
                                                                                              + IDDay)
                                                                                           : IDDay);
                long
                    age =
                    DateUtil.compareToFloor(DateUtil.praseDate(format, birthday), nowDate,
                                            DateUtil.ONE_YEAR);
                return Integer.parseInt(Math.abs(age) + "");
            }
        } catch (Exception e) {
            ExceptionContext.process(e);
        }
        return 0;

    }

    /**
     * *********** 创建日期: Jul 21, 2011 2:12:34 PM 创建作者：chenjh
     *
     * @return 功能： ************
     */
    public MemberDao getDao() {
        return (MemberDao) BaseStoneContext.getInstance().getBean("memberDao");
    }

    /**
     * 获取扩展的DAO
     * <AUTHOR> create at 2011-7-25 下午01:31:16
     */
    private MemberInfoDao getMemberInfoDao() {
        return (MemberInfoDao) BaseStoneContext.getInstance().getBean("memberInfoDao");
    }

    /**
     * 获取邮箱绑定Dao
     *
     * <AUTHOR> 创建于 2011-8-22 下午01:58:51
     */
    private EmailBinderDao getEmailBinderDao() {
        return (EmailBinderDao) BaseStoneContext.getInstance().getBean("emailBinderDao");
    }

    /**
     * 获取手机绑定Dao
     *
     * <AUTHOR> 创建于 2011-8-22 下午01:59:36
     */
    private MobileBinderDao getMobileBinderDao() {
        return (MobileBinderDao) BaseStoneContext.getInstance().getBean("mobileBinderDao");
    }

    /**
     * 关系stieDao
     *
     * <AUTHOR> 创建于 2011-8-22 下午01:59:36
     */
    private MemberOutSiteDao getMemberOutSiteDao() {
        return (MemberOutSiteDao) BaseStoneContext.getInstance().getBean("memberOutSiteDao");
    }

    /**
     * 用户账号泄露dao
     */
    public MemberUnsafeDao getMemberUnsafeDao() {
        return (MemberUnsafeDao) BaseStoneContext.getInstance().getBean("memberUnsafeDao");
    }

    /**
     * 构建personalEditLog
     */
    public void buildLog(String type) {
        if (!this.personalEditLog.isLog()) {
            return;
        }
        this.personalEditLog.setType(type);
        this.personalEditLog.setUserAdd(this.getMemberName());
        this.personalEditLog.setMember(this);
        if (null != memberInfo) {
            HashMap<String, DetailItem> memberInfoMap = memberInfo.getDetail().getItems();
            for (String key : memberInfoMap.keySet()) {
                this.personalEditLog.addItem(memberInfoMap.get(key));
            }
        }

    }

    public void buildLog(String type, String oldMobile) {
        if (!this.personalEditLog.isLog()) {
            return;
        }
        this.personalEditLog.setType(type);
        this.personalEditLog.setUserAdd(this.getMemberName());
        this.personalEditLog.setMember(this);
        this.personalEditLog.addItem(new DetailItem("手机", oldMobile, this.getMobile()));
        personalEditLog.save();
    }

    public Integer getMemberId() {
        return memberId;
    }

    public void setMemberId(Integer memberId) {
        this.memberId = memberId;
    }

    public String getMemberName() {
        return memberName;
    }

    public void setMemberName(String memberName) {
        this.memberName = memberName;
    }

    public String getEncodeMemberName() throws UnsupportedEncodingException {
        return URLEncoder.encode(this.memberName, "UTF-8");
    }

    public String getMemberPwd() {
        return memberPwd;
    }

    public void setMemberPwd(String memberPwd) {
        personalEditLog.addItem(new DetailItem("密码", "", ""));
        this.memberPwd = memberPwd;
    }

    public Integer getMemberState() {
        return memberState;
    }

    public void setMemberState(Integer memberState) {
        this.memberState = memberState;
    }

    public String getRealName() {
        return StringUtil.removeIllegalXmlChar(realName);
    }

    public void setRealName(String realName) {
        personalEditLog.addItem(new DetailItem("真实姓名", this.realName, realName));
        this.realName = realName;
    }

    public String getNickName() {
        return StringUtil.removeIllegalXmlChar(nickName);
    }

    public void setNickName(String nickName) {
        personalEditLog.addItem(new DetailItem("昵称", this.nickName, nickName));
        this.nickName = nickName;
    }

    public String getHeadImg() {
        return headImg;
    }

    public void setHeadImg(String headImg) {
        personalEditLog.addItem(new DetailItem("头像", this.headImg, headImg));
        this.headImg = headImg;
    }

    public Date getTimeAdd() {
        return timeAdd;
    }

    public void setTimeAdd(Date timeAdd) {
        this.timeAdd = timeAdd;
    }

    public Date getTimeLogon() {
        return timeLogon;
    }

    public void setTimeLogon(Date timeLogon) {
        this.timeLogon = timeLogon;
    }

    @Override
    public Serializable getPk() {
        return memberId;
    }

    public Date getTimeEdit() {
        return timeEdit;
    }

    public void setTimeEdit(Date timeEdit) {
        this.timeEdit = timeEdit;
    }

    public MemberProtectedQuestion getMemberProtectedQuestions() {
        return memberProtectedQuestions;
    }

    public void setMemberProtectedQuestions(MemberProtectedQuestion memberProtectedQuestions) {
        this.memberProtectedQuestions = memberProtectedQuestions;
    }

    /**
     * 获取实名认证dao
     * <AUTHOR> create at 2011-7-27 下午03:13:27
     */
    @SuppressWarnings("unchecked")
    public ActuDao getActuDao() {
        String beanName = MemberConstants.ACTU_USER_TYPE_MAP.get(this.memberType);
        if (StringUtil.isBlank(beanName)) {
            throw new MsgIllExp("用户类型");
        }
        return (ActuDao) BaseStoneContext.getInstance().getBean(beanName);
    }

    public ActuInfo getActuInfo() {
        return actuInfo;
    }

    public void setActuInfo(ActuInfo actuInfo) {
        this.actuInfo = actuInfo;
    }

    public MemberInfo getMemberInfo() {
        return memberInfo;
    }

    public void setMemberInfo(MemberInfo memberInfo) {
        this.memberInfo = memberInfo;
    }

    public PersonalEditLog getPersonalEditLog() {
        return personalEditLog;
    }

    public void setPersonalEditLog(PersonalEditLog personalEditLog) {
        this.personalEditLog = personalEditLog;
    }

    public Integer getBindState() {
        return bindState;
    }

    public void setBindState(Integer bindState) {
        this.bindState = bindState;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getEmailShow() {
        return getEmailSecurityPolicyStr();
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getMobileShow() {
        //return MaskUtil.maskMobile(mobile);
        return this.getMobile().substring(0, 3) + "*****" + this.getMobile().substring(8);
	}


    /**
     * 兼容旧的页面展示
     *
     * <AUTHOR> create at 2012-7-31 上午09:35:57
     */
    public String getShowName() {
        return getShortShowName();
    }

    /**
     * 显示较短的名称 只显示top8字符
     *
     * <AUTHOR> create at 2012-7-31 上午09:34:31
     */
    public String getShortShowName() {
        String showName = memberName;
        //@YHJ 任何时候 显示名称都只取前8个
        if (showName.length() > 8) {
            showName = showName.substring(0, 8) + "...";
        }
        return showName;
    }



    /**
     * *********** 创建日期: 2012-5-18 创建作者：JINBAO
     *
     * @return 功能：判断用户是否进行了实名认证,实名认证返回true,未实名返回false ************
     */
    public Boolean getIsActu() {
        if(getMemberSpecialType()==1)
        {
            return getCafeCertState() == 2;
        }else{
            if (memberType == 1) {
                return personCertState == 2;
            } else if (memberType == 2) {
                return companyCertState == 2;
            }
        }
        return false;
    }

    public Integer getMemberType() {
        return memberType;
    }

    public void setMemberType(Integer memberType) {
        this.memberType = memberType;
    }

    public Date getPwdErrTime() {
        return pwdErrTime;
    }

    public void setPwdErrTime(Date pwdErrTime) {
        this.pwdErrTime = pwdErrTime;
    }

    public Integer getPwdErrNum() {
        if (pwdErrNum == null) {
            pwdErrNum = 0;
        }
        return pwdErrNum;
    }

    public void setPwdErrNum(Integer pwdErrNum) {
        this.pwdErrNum = pwdErrNum;
    }

    public Integer getPersonCertState() {
        return personCertState;
    }

    public void setPersonCertState(Integer personCertState) {
        this.personCertState = personCertState;
    }

    public String getPersonCertStateShow() {
        return ActuConstant.INFO_STATE_MAP.get(personCertState + "");
    }

    public Integer getCompanyCertState() {
        return companyCertState;
    }

    public void setCompanyCertState(Integer companyCertState) {
        this.companyCertState = companyCertState;
    }

    public String getCompanyCertStateShow() {
        return ActuConstant.INFO_STATE_MAP.get(companyCertState + "");
    }

    public EmailBinder getEmailBinder() {
        return emailBinder;
    }

    public void setEmailBinder(EmailBinder emailBinder) {
        this.emailBinder = emailBinder;
    }

    public MobileBinder getMobileBinder() {
        return mobileBinder;
    }

    public void setMobileBinder(MobileBinder mobileBinder) {
        this.mobileBinder = mobileBinder;
    }

    public Date getTimeCheck() {
        return timeCheck;
    }

    public void setTimeCheck(Date timeCheck) {
        this.timeCheck = timeCheck;
    }

    @Override
    public void beginBuildLog() {
        this.personalEditLog.beginBuildLog(true);
    }

    public void beginBuildLog(String editItem) {
        beginBuildLog();
        personalEditLog.setEditItem(editItem);
    }

    @Override
    public Detail getDetail() {
        return personalEditLog;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        personalEditLog.addItem(new DetailItem("公司名称", this.companyName, companyName));
        this.companyName = companyName;
    }

    public Date getLifeTime() {
        return lifeTime;
    }

    public void setLifeTime(Date lifeTime) {
        this.lifeTime = lifeTime;
    }

    public String getTitleName() {
        return titleName;
    }

    public void setTitleName(String titleName) {
        this.titleName = titleName;
    }

    public String getSiteType() {
        return siteType;
    }

    public void setSiteType(String siteType) {
        this.siteType = siteType;
    }

    public MemberOutSite getMemberOutSite() {
        return memberOutSite;
    }

    public void setMemberOutSite(MemberOutSite memberOutSite) {
        this.memberOutSite = memberOutSite;
    }

    public boolean isRegFromInterface() {
        return regFromInterface;
    }

    public void setRegFromInterface(boolean regFromInterface) {
        this.regFromInterface = regFromInterface;
    }

    public String getVrsion() {
        return vrsion;
    }

    public void setVrsion(String vrsion) {
        this.vrsion = vrsion;
    }

    public String getEnv() {
        return env;
    }

    public void setEnv(String env) {
        this.env = env;
    }

    public String getExtData() {
        return extData;
    }

    public void setExtData(String extData) {
        this.extData = extData;
    }

    public MemberSafeNotice getMemberSafeNotice() {
        if (memberSafeNotice == null) {
            this.memberSafeNotice = getMemberSafeNoticeDao().getById(memberId);
        }
        return memberSafeNotice;
    }

    public void setMemberSafeNotice(MemberSafeNotice memberSafeNotice) {
        this.memberSafeNotice = memberSafeNotice;
    }

    public MemberSafeGuard getMemberSafeGuard() {
        if (memberSafeGuard == null) {
            memberSafeGuard = getMemberSafeGuardDao().getById(memberId);
        }
        return memberSafeGuard;
    }

    public void setMemberSafeGuard(MemberSafeGuard memberSafeGuard) {
        this.memberSafeGuard = memberSafeGuard;
    }

    /**
     * 用户安全卫士有效结束日期
     *
     * <AUTHOR> CreateTime: 2014-3-4 上午10:18:07
     */
    public String getMemberSafeGuardEndTime() {
        return getMemberSafeGuard() == null ? "" : DateUtil
            .ymdhmsFormat(memberSafeGuard.getTimeValidEnd());
    }

    /**
     * 用户安全卫士有效结束日期
     *
     * <AUTHOR> CreateTime: 2014-3-4 上午10:18:07
     */
    public String getMemberSafeGuardEndDate() {
        return getMemberSafeGuard() == null ? "" : DateUtil
                .ymdFormat(memberSafeGuard.getTimeValidEnd());
    }

    /**
     * 用户安全卫士是否过期失效
     *
     * <AUTHOR> CreateTime: 2014-3-4 上午10:18:07
     */
    public boolean hasMemberSafeGuardIsFailure() {
        if (getMemberSafeGuard() == null) {
            return true;
        }
        return
            DateUtil.compare(memberSafeGuard.getTimeValidEnd(), new Date(), DateUtil.ONE_SECOND) > 0
            ? false : true;
    }

    public MemberSafeGuardDao getMemberSafeGuardDao() {
        return (MemberSafeGuardDao) BaseStoneContext.getInstance().getBean("memberSafeGuardDao");
    }

    /**
     * 判断用户是否开通安全卫士
     *
     * <AUTHOR> CreateTime: 2014-3-4 下午02:57:35
     */
    public boolean getMemberSafeGuardHasOpen() {
        return getMemberSafeGuard() == null ? false : true;
    }

    /**
     * 安全卫士是否打开
     *
     * <AUTHOR> CreateTime: 2014-3-4 上午10:18:31
     */
    public boolean getSafeGuardIsOpen() {
        Boolean isOpen = Boolean.TRUE;
        String noticeIsOpen = RedisContext.getResourceCache().getResourceValue(MemberSafeGuardContants.SAFEG_GUARD_CONFIG, MemberSafeGuardContants.SAFE_GUARD_IS_OPEN);
        if ("n".equalsIgnoreCase(noticeIsOpen)) {
            isOpen = Boolean.FALSE;
        }
        return isOpen;
    }

    /**
     * 手机号码是否被设置为登录账号
     *
     * <AUTHOR>
     */
    public boolean getMobileAsLoginAccount() {
        return this.getIsBind(MemberConstants.MEMBER_STATE_PHONE_AS_LOGIN_ACCOUNT);
    }

    /**
     * 邮箱账号是否被设置为登录账号
     *
     * <AUTHOR>
     */
    public boolean getMailAsLoginAccount() {
        return this.getIsBind(MemberConstants.MEMBER_STATE_MAIL_AS_LOGIN_ACCOUNT);
    }

    /**
     * 设置手机号码为登录账户
     *
     * <AUTHOR>
     * @param isBind true、绑定(启用) false、解绑(禁用)
     */
    public void setMoblieAsLoginAccount(boolean isBind) {
        this.setIsBind(isBind, MemberConstants.MEMBER_STATE_PHONE_AS_LOGIN_ACCOUNT);
    }

    /**
     * 设置邮箱账号为登录账户
     *
     * <AUTHOR>
     * @param isBind true、绑定(启用) false、解绑(禁用)
     */
    public void setMailAsLoginAccount(boolean isBind) {
        this.setIsBind(isBind, MemberConstants.MEMBER_STATE_MAIL_AS_LOGIN_ACCOUNT);
    }

    /**
     * 返回隐藏中间4位后的手机号码
     *
     * <AUTHOR>
     * @return
     */
    public String getMobileSecurityPolicyStr() {
        if (StringUtils.isBlank(this.getMobile())) return "";
        return this.getMobile().substring(0, 3) + "*****" + this.getMobile().substring(8);
    }


    /**
     * 返回隐藏@前部分字符后的邮箱账号
     *
     * <AUTHOR>
     * @return
     */
    public String getEmailSecurityPolicyStr() {
        if (StringUtils.isBlank(this.getEmail())) return "";
        int atIdx = this.getEmail().indexOf("@");
        if (atIdx == -1) return this.getEmail();
        String preStr = this.getEmail().substring(0, atIdx);
        int preLen = preStr.length();
        String regex;
        String replacement;
        if (preLen == 1) { //只有一位的情况 example: <EMAIL> --> *@qq.com
            regex = ".+(@.+)";
            replacement = "*$1";
        } else if (preLen < 5) { //大于1位小于5位的情况统一处理 example: <EMAIL> --> 8****@qq.com
            regex = "(.{1}).+(@.+)";
            replacement = "$1****$2";
        } else {    //大于等于5位的情况统一 example: <EMAIL> --> 86****@qq.com
            regex = "(.{2}).+(.{0}@.+)";
            replacement = "$1****$2";
        }
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(this.getEmail());
        return matcher.replaceAll(replacement);
    }

    public Integer getSafeItemsScore() {
        return safeItemsScore;
    }

    public Integer returnCalSafeItemsScore() {
        int reCalScore = recheckSafeItemsScore();
        if (safeItemsScore == null || safeItemsScore != reCalScore) {
            safeItemsScore = reCalScore;
            getDao().update(this);
        }
        return safeItemsScore;
    }

    public void setSafeItemsScore(Integer safeItemsScore) {
        this.safeItemsScore = safeItemsScore;
    }

    public Integer getSafeInitScore() {
        return getSafeItemScoreByResourceName(CacheKeyConstant.ConfigResourcesConstants.SAFE_ITEMS_SCORE_CONFIG_INIT_SCORE);
    }

    /**
     * 计算安全评分 (注意: 目前这里不控制该项操作是否重复操作, 交给外面操作的时候判断。比如开启安全提醒的某一项, 需要外面控制不能重复开启该项)
     * @param enable 开启/关闭
     * @param safeItemScoreResourceName 具体哪项操作调用具体ConfigResource的KEY(比如绑定手机,就调用CacheKeyConstant.ConfigResourcesConstants.SAFE_ITEMS_SCORE_CONFIG_BIND_MOBILE)
     */
    public void calSafeItemsScore(boolean enable, String safeItemScoreResourceName) {
        if (getIsEmptyPwd() && safeItemsScore != null) {  //如果是第三方账号登录，未设置密码的情况下评分默认得分(即使所有安全功能全部开启)
            safeItemsScore = getSafeItemScoreByResourceName(CacheKeyConstant.ConfigResourcesConstants.SAFE_ITEMS_SCORE_CONFIG_NO_PASSWORD);
        } else {
            Integer score = getSafeItemScoreByResourceName(safeItemScoreResourceName);
            if (score != null) {
                if (safeItemsScore == null) {
                    safeItemsScore = recheckSafeItemsScore();
                }
                if (enable) {
                    safeItemsScore += score;
                } else {
                    safeItemsScore -= score;
                }
            }
        }
    }

    /**
     * 计算安全评分 (注意: 目前这里不控制该项操作是否重复操作, 交给外面操作的时候判断。比如开启安全提醒的某一项, 需要外面控制不能重复开启该项)
     * @param enable 开启/关闭
     * @param score 实际相应操作分数(开启加分,关闭减分)
     */
    public void calSafeItemsScore(boolean enable, Integer score) {
        if (getIsEmptyPwd() && safeItemsScore != null) {  //如果是第三方账号登录，未设置密码的情况下评分默认得分(即使所有安全功能全部开启)
            safeItemsScore = getSafeItemScoreByResourceName(CacheKeyConstant.ConfigResourcesConstants.SAFE_ITEMS_SCORE_CONFIG_NO_PASSWORD);
        } else {
            if (score != null) {
                if (safeItemsScore == null) {
                    safeItemsScore = recheckSafeItemsScore();
                }
                if (enable) {
                    safeItemsScore += score;
                } else {
                    safeItemsScore -= score;
                }
            }
        }
    }

    /**
     * resources表的name作为key, 根据key查询配置的分数
     * @param name 用户名
     */
    public Integer getSafeItemScoreByResourceName(String name) {
        String score = RedisContext.getResourceCache().getResourceValue(CacheKeyConstant.ConfigResourcesConstants.SAFE_ITEMS_SCORE_CONFIG_KEY, name);
        return score == null ? null : Integer.valueOf(score);
    }

    /**
     * 重新计算安全评分
     */
    public int recheckSafeItemsScore() {
        int score = 0;
        if (getIsEmptyPwd()) {  //如果是第三方账号登录，未设置密码的情况下评分默认得分(即使所有安全功能全部开启)
            score = RedisContext.getIntConfig(CacheKeyConstant.ConfigResourcesConstants.SAFE_ITEMS_SCORE_CONFIG_KEY, CacheKeyConstant.ConfigResourcesConstants.SAFE_ITEMS_SCORE_CONFIG_NO_PASSWORD, 0);
        } else {
            score = RedisContext.getIntConfig(CacheKeyConstant.ConfigResourcesConstants.SAFE_ITEMS_SCORE_CONFIG_KEY, CacheKeyConstant.ConfigResourcesConstants.SAFE_ITEMS_SCORE_CONFIG_INIT_SCORE, 5);  //初始评分
            if (getIsBindEmail())  //邮箱绑定
                score += RedisContext.getIntConfig(CacheKeyConstant.ConfigResourcesConstants.SAFE_ITEMS_SCORE_CONFIG_KEY, CacheKeyConstant.ConfigResourcesConstants.SAFE_ITEMS_SCORE_CONFIG_BIND_EMAIL, 5);
            if (getIsBindMobile())  //手机绑定
                score += RedisContext.getIntConfig(CacheKeyConstant.ConfigResourcesConstants.SAFE_ITEMS_SCORE_CONFIG_KEY, CacheKeyConstant.ConfigResourcesConstants.SAFE_ITEMS_SCORE_CONFIG_BIND_MOBILE, 5);
            if (getIsBindQuestion())  //密保问题
                score += RedisContext.getIntConfig(CacheKeyConstant.ConfigResourcesConstants.SAFE_ITEMS_SCORE_CONFIG_KEY, CacheKeyConstant.ConfigResourcesConstants.SAFE_ITEMS_SCORE_CONFIG_BIND_QUESTION, 5);
            if (getIsBindShunLing() || getIsBindDynamicPwd())  //登录保护(手机顺令或动态密码)
                score += RedisContext.getIntConfig(CacheKeyConstant.ConfigResourcesConstants.SAFE_ITEMS_SCORE_CONFIG_KEY, CacheKeyConstant.ConfigResourcesConstants.SAFE_ITEMS_SCORE_CONFIG_LOGIN_PROTECTION, 5);
            if (Boolean.TRUE.equals(getIsActu()))  //实名认证
                score += RedisContext.getIntConfig(CacheKeyConstant.ConfigResourcesConstants.SAFE_ITEMS_SCORE_CONFIG_KEY, CacheKeyConstant.ConfigResourcesConstants.SAFE_ITEMS_SCORE_CONFIG_REALNAME_AUTH, 5);
            if (getIsBindSafeNotice()) {  //账户安全提醒,每项得分相同
                if (getMemberSafeNotice().getIsOpenEmailChange())
                    score += RedisContext.getIntConfig(CacheKeyConstant.ConfigResourcesConstants.SAFE_ITEMS_SCORE_CONFIG_KEY, CacheKeyConstant.ConfigResourcesConstants.SAFE_ITEMS_SCORE_CONFIG_SAFENOTICE, 5);
                if (getMemberSafeNotice().getIsOpenMobielChange())
                    score += RedisContext.getIntConfig(CacheKeyConstant.ConfigResourcesConstants.SAFE_ITEMS_SCORE_CONFIG_KEY, CacheKeyConstant.ConfigResourcesConstants.SAFE_ITEMS_SCORE_CONFIG_SAFENOTICE, 5);
                if (getMemberSafeNotice().getIsOpenPWDChange())
                    score += RedisContext.getIntConfig(CacheKeyConstant.ConfigResourcesConstants.SAFE_ITEMS_SCORE_CONFIG_KEY, CacheKeyConstant.ConfigResourcesConstants.SAFE_ITEMS_SCORE_CONFIG_SAFENOTICE, 5);
                if (getMemberSafeNotice().getIsOpenQuestionChange())
                    score += RedisContext.getIntConfig(CacheKeyConstant.ConfigResourcesConstants.SAFE_ITEMS_SCORE_CONFIG_KEY, CacheKeyConstant.ConfigResourcesConstants.SAFE_ITEMS_SCORE_CONFIG_SAFENOTICE, 5);
            }
        }
        return score;
    }

    public int getErrTimesToLimitLogin() {
        return RedisContext.getIntConfig(CacheKeyConstant.ConfigResourcesConstants.COMMON_CONFIG, CacheKeyConstant.ConfigResourcesConstants.ERR_TIMES_OF_LIMIT_LOGIN, 5);
    }

    /**
     * 获取
     */
    public int getLimitSecondsToLimitLogin() {
        return RedisContext.getIntConfig(CacheKeyConstant.ConfigResourcesConstants.COMMON_CONFIG, CacheKeyConstant.ConfigResourcesConstants.LIMIT_SENCONDS_OF_LIMIT_LOGIN, 10);
    }

    public Integer getMemberSpecialType() {
        return memberSpecialType==null?0:memberSpecialType;
    }

    public void setMemberSpecialType(Integer memberSpecialType) {
        this.memberSpecialType = memberSpecialType;
    }

    public Integer getCafeCertState() {
        return cafeCertState==null?0:cafeCertState;
    }

    public void setCafeCertState(Integer cafeCertState) {
        this.cafeCertState = cafeCertState;
    }
    public String getCafeCertStateShow() {
        return ActuConstant.INFO_STATE_MAP.get(cafeCertState + "");
    }

    public String getAccessSiteId() {
        return accessSiteId;
    }

    public void setAccessSiteId(String accessSiteId) {
        this.accessSiteId = accessSiteId;
    }


    /**
     * 复合了 memberType 和 fakeCompanyCert
     *  对于个人账户类型可能返回企业账户类型 （memberType == MemberConstants.USER_TYPE_PERSONAL && fakeCompanyCert == true）
     */
    public Integer getExtendedMemberType() {
        //网吧业主不做处理
        if (MemberConstants.MEMBER_SPECIAL_TYPE_CAFE.equals(memberSpecialType)) {
            return memberType;
        }
        //非网吧业主,个人
        if (MemberConstants.USER_TYPE_PERSONAL.equals(memberType)) {
            Integer transformedMemberType = fakeCompanyCert ? MemberConstants.USER_TYPE_COMPANY : MemberConstants.USER_TYPE_PERSONAL;
            if (log.isDebugEnabled()) {
                log.debug("转换memberType, memberId: {}, fakeCompanyCert = {}, memberType = {}", memberId, fakeCompanyCert, memberType);
            }
            return transformedMemberType;
        }
        return memberType;
    }

    public String getWeakPwdState() {
        return weakPwdState;
    }

    public void setWeakPwdState(String weakPwdState) {
        this.weakPwdState = weakPwdState;
    }

    public void setFakeCompanyCert(boolean fakeCompanyCert) {
        this.fakeCompanyCert = fakeCompanyCert;
    }

    public boolean getAntiAdditionCert() {
        return antiAdditionCert;
    }

    public void setAntiAdditionCert(boolean antiAdditionCert) {
        this.antiAdditionCert = antiAdditionCert;
    }

    public PersonalActuVerifyRecord getPersonalActuVerifyRecord() {
        return personalActuVerifyRecord;
    }

    public void setPersonalActuVerifyRecord(PersonalActuVerifyRecord personalActuVerifyRecord) {
        this.personalActuVerifyRecord = personalActuVerifyRecord;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public String getClientIp() {
        return clientIp;
    }

    public void setClientIp(String clientIp) {
        this.clientIp = clientIp;
    }

    public String getPwdType() {
        return pwdType;
    }

    public void setPwdType(String pwdType) {
        this.pwdType = pwdType;
    }
}

