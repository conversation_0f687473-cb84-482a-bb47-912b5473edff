package com.shunwang.basepassport.user.web;

import com.shunwang.baseStone.context.BaseStoneContext;
import com.shunwang.baseStone.context.RedisContext;
import com.shunwang.baseStone.siteinterface.context.SiteContext;
import com.shunwang.basepassport.config.pojo.SiteInterface;
import com.shunwang.basepassport.key.common.DynamicKeyUtil;
import com.shunwang.basepassport.mobile.constants.MobileCheckCodeConstants;
import com.shunwang.basepassport.mobile.dao.MobileCheckCodeDao;
import com.shunwang.basepassport.mobile.pojo.MobileCheckCode;
import com.shunwang.basepassport.user.dao.MemberDao;
import com.shunwang.util.lang.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @since 2013-11-8
 */
public abstract class DynamicSecurityAction extends MemberAction {

    private static final long serialVersionUID = 5891356345551662025L;

    protected final Logger LOG = LoggerFactory.getLogger(getClass());

    protected String userName; // 可以是邮箱、手机、账号

    protected String dynamic; // 是否采用动态密钥

    protected String key; // MD5加密KEY

    protected String token; // 登录会话令牌


    @Override
    public String createSignString() {
        SiteInterface site = SiteContext.getSiteInterface();

        if (StringUtil.isNotBlank(dynamic) && dynamic.equals("1")) {
            key = DynamicKeyUtil.getDynamicKey(getSiteId(), userName, getSiteName());
        } else {
            key = site.getMd5Key();
        }

        return super.createSignString();
    }

    protected MobileCheckCode getMobileCheckCode(String memberName) {
        MobileCheckCodeDao  mobileCheckCodeDao = getMobileCheckCodeDao();
        MobileCheckCode paramMobileCheckCode = new MobileCheckCode();
        paramMobileCheckCode.setMemberName(memberName);
        paramMobileCheckCode.setType(MobileCheckCodeConstants.DYNAMIC_PWD_LOGIN);

        return mobileCheckCodeDao.findByNameAndType(paramMobileCheckCode);
    }
    
	public String getValidTime() {
        String validTime = RedisContext.getResourceCache()
                        .getResourceValue(MobileCheckCodeConstants.DYNAMIC_PWD_CONFIG, MobileCheckCodeConstants.VALID_TIME);

		if (StringUtil.isNotEmpty(validTime) && StringUtil.isNumer(validTime)) {
			return validTime;
		}
		
		return MobileCheckCodeConstants.VALID_TIME_DEFAULT;
	}


    /****************************************************************************************
     *                                 Getter && Setter                                     *
     ***************************************************************************************/

    public MemberDao getDao(){
        return (MemberDao) BaseStoneContext.getInstance().getBean("memberDao");
    }

    public MobileCheckCodeDao getMobileCheckCodeDao() {
        return (MobileCheckCodeDao)BaseStoneContext.getInstance().getBean("mobileCheckCodeDao");
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getDynamic() {
        return dynamic;
    }

    public void setDynamic(String dynamic) {
        this.dynamic = dynamic;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }
}
