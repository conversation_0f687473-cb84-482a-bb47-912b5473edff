package com.shunwang.basepassport.outActu;

import com.shunwang.util.date.DateUtil;
import com.shunwang.util.encrypt.Md5Encrypt;
import org.junit.Test;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;

/**
 * User:pf.ma
 * Date:2017/05/09
 * Time:19:26
 */
public class TestOutActualChange {
	@Test
	public void test() throws UnsupportedEncodingException {
		String siteId = "Passport";
		String md5Key = "123456" ;
		String time = DateUtil.getCurrentDateStamp();

		String memberName = "feifei002";
		String cafeName = "网吧名称接口";
		String applyUserName = "你好·额";
		String applyMobile = "13858411987";
		String cafeAddr = "网吧地址接口";
		String idCardNo = "621223198601111063" ;
		String cafeLicenceImg = "http://www.huian168.com/userfiles/2014/04/1397736607.jpg" ;
		String idCardFrontImg = "http://uploads.yjbys.com/allimg/150608/3-15060P9230a31.jpg" ;
		String idCardBackImg = "http://uploads.yjbys.com/allimg/150608/3-15060P92211Z4.jpg" ;
//		String idCardHeadImg = "http://www.wdc66.cn/template/default/img/bangzhuzhongxin/sfz3.jpg";
		String idCardHeadImg = "";
//		String facadeImg = "http://images.v007.net/uploadfiles/others/200910/2009102315354425.jpg" ;
		String facadeImg = "" ;
		String unification = "1" ;

		String changeReason = "我也不知道为什么" ;
		String unbind = "1" ;

		String plainText = siteId+"|"+memberName+"|"+applyMobile+"|"+applyUserName+"|"+cafeAddr+"|"+cafeLicenceImg + "|"+ cafeName
				+ "|"+changeReason+"|"
				+ facadeImg + "|"
				+idCardBackImg+"|"+idCardFrontImg+"|"
				+idCardHeadImg+"|"
				+idCardNo + "|"
				+unification+"|"
				+ time +"|"+md5Key ;
		System.out.println(plainText);
		plainText = URLEncoder.encode(plainText, "UTF-8").toUpperCase() ;
		String sign = Md5Encrypt.encrypt(plainText,"UTF-8").toUpperCase();

		String respond = "";
		HttpURLConnection conn = null;

		try {
			URL url = new URL("http://interface.kedou.com/front/interface/outActuChange.htm");
			conn = (HttpURLConnection) url.openConnection();
			conn.setDoOutput(true);
			conn.setUseCaches(false);
			conn.setRequestMethod("POST");
//			conn.setRequestProperty("Accept", "application/json");
			conn.getOutputStream().write(("siteId=" + siteId+"&memberName="+memberName +"&applyUserName="+applyUserName+"&applyMobile="+applyMobile
					+ "&cafeAddr="+cafeAddr
					+"&cafeLicenceImg="+URLEncoder.encode(cafeLicenceImg,"UTF-8")
					+ "&cafeName="+cafeName
					+"&changeReason="+changeReason
//					+"&facadeImg="+URLEncoder.encode(facadeImg,"UTF-8")
					+"&idCardBackImg="+URLEncoder.encode(idCardBackImg,"UTF-8") + "&idCardFrontImg="+URLEncoder.encode(idCardFrontImg,"UTF-8")
//					+"&idCardHeadImg="+URLEncoder.encode(idCardHeadImg,"UTF-8")
					+"&idCardNo="+idCardNo
					+"&unification="+unification
					+"&unbindMobile="+unbind
					+ "&time=" +time + "&sign=" + sign).getBytes());
			conn.connect();

			BufferedReader reader = new BufferedReader(new InputStreamReader(conn.getInputStream(), "utf-8"));
			String lines = "";

			while ((lines = reader.readLine()) != null) {
				respond += lines;
			}

			reader.close();
			conn.disconnect();
		} catch (Exception e) {
			//e.printStackTrace();
			respond = e.getMessage();
		} finally {
			if (conn != null) {
				conn.disconnect();
			}
			conn = null;
		}

		System.out.println(respond);
	}
}
