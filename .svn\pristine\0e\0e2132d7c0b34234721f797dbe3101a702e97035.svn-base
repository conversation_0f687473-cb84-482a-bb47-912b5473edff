package com.shunwang.basepassport.user.common;

import com.shunwang.baseStone.cache.CacheKeyConstant;
import com.shunwang.baseStone.context.BaseStoneContext;
import com.shunwang.baseStone.context.LoginElementService;
import com.shunwang.baseStone.context.RedisContext;
import com.shunwang.baseStone.core.exception.BaseStoneException;
import com.shunwang.baseStone.loginelement.constants.LoginElementConstant;
import com.shunwang.basepassport.binder.common.ErrorCode;
import com.shunwang.util.StringUtil;
import com.shunwang.util.date.DateUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;

import java.util.Date;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

public class UserLoginSessionUtil {

	public enum LoginType{
		SSO_PWD("s_pwd", "sso帐号密码登录", true)
		,SSO_OUT("s_out_", "sso第三方授权登录", true)
		,SSO_IMPORT("s_import_", "sso第三方导入登录", true)
		,SSO_FREE_DLL("sf_dll", "sso免登插件免登", true)
		,SSO_FREE_IDENTITY("sf_identity", "sso免登票据免登", true)
		,SSO_FREE_INTERFACE("sf_access_token", "sso转interface免登", true)
		,INTERFACE_PWD("i_pwd", "interface帐号密码登录", true)
		,INTERFACE_SMS_LOGIN("i_sms_login", "interface短信登录", true)
		,INTERFACE_SMS_REG("i_sms_reg", "interface短信注册登录", true)
		,INTERFACE_SMS_DYNAMIC("i_dynamic", "interface帐号动态密钥登录", true)
		,INTERFACE_SMS_DYNAMIC_INNER("i_dynamic_inner", "interface帐号动态密钥登录", true)
		,INTERFACE_SMS_SINGLE("i_sms_single", "interface单帐号登录", true)
		,INTERFACE_SINGLE_HOTEL("i_single_hotel", "interface电竞酒店绑单帐号登录", true)
		,INTERFACE_HOTEL("i_hotel", "interface电竞酒店登录", true)
		,INTERFACE_SMS_AS_LOGIN("i_sms_as_login", "interface设登录帐号免登", true)
		,INTERFACE_FREE_ACCESS_TOKEN("if_auth_token", "interface授权免登", true)
		,SSO_IMPORT_NET_BAR("s_bar_import", "sso网吧会员导入登录", false)
		;
		private final String type;
		private final String name;
		private final boolean ssoAuth;

		public String getType() {
			return type;
		}

		public String getName() {
			return name;
		}

		public boolean isSsoAuth() {
			return ssoAuth;
		}

		LoginType(String type, String name, boolean ssoAuth) {
			this.name = name;
			this.type = type;
			this.ssoAuth = ssoAuth;
		}

		/**
		 * 判断当前登录或免登类型是否是sso授权认证
		 * @param type 当前登录类型
		 */
		public static boolean checkSsoAuth(String type) {
			if (type.contains(SSO_OUT.type) || type.contains(SSO_IMPORT.type)) {
				return true;
			}
			for (LoginType loginType : LoginType.values()) {
				if (loginType.type.equals(type)) {
					return loginType.ssoAuth;
				}
			}
			return false;
		}
	}
	private static final Logger log = LoggerFactory.getLogger(UserLoginSessionUtil.class);

	public static String getCacheKey(String memberName) {
		return CacheKeyConstant.SSO.LOGIN_USER_SESSION + memberName;
	}

	public static LoginElementService getLoginElementService() {
		return (LoginElementService)BaseStoneContext.getInstance().getBean("loginElementService");
	}

	public static boolean ssoUnAuthFreeLoginSwitch(String siteId) {
		if (StringUtil.isBlank(siteId)) {
			return true;
		}
		Map<String, String> loginConfig = getLoginElementService().getLoginConfig(siteId, LoginElementConstant.SSO_UN_AUTH_FREE_LOGIN_SWITCH);
		return getLoginElementService().configIsOpen(loginConfig, LoginElementConstant.STATE);
	}

	/**
	 * 不检查是否有登录记录，兼容已接入业务线使用，待业务线改完后需关闭对应的站点
	 * @param siteId
	 * @return
	 */
	private static boolean unCheckFreeLoginSwitch(String siteId) {
		if (StringUtil.isBlank(siteId)) {
			return true;
		}
		Map<String, String> loginConfig = getLoginElementService().getLoginConfig(siteId, LoginElementConstant.UN_CHECK_FREE_LOGIN_SWITCH);
		return getLoginElementService().configIsOpen(loginConfig, LoginElementConstant.STATE);
	}

	public static boolean isSsoAuth(String memberName) {
		String cacheKey = getCacheKey(memberName);
		LoginSession loginTrace = RedisContext.getRedisCache().get(cacheKey, LoginSession.class);
		return loginTrace != null && loginTrace.ssoAuth;
	}

	public static String getExtData(String memberName) {
		LoginSession loginTrace = getLoginSession(memberName);
		return loginTrace != null ? loginTrace.extData : null;
	}

	public static LoginSession getLoginSession(String memberName) {
		String cacheKey = getCacheKey(memberName);
		return RedisContext.getRedisCache().get(cacheKey, LoginSession.class);
	}

	public static void ssoAuthCheck(String memberName, String siteId) {
		String cacheKey = getCacheKey(memberName);
		LoginSession loginTrace = RedisContext.getRedisCache().get(cacheKey, LoginSession.class);
		if (loginTrace == null) {
			if (unCheckFreeLoginSwitch(siteId)) {
				return;
			}
			log.warn("未找到登录数据，[{}]不免登", memberName);
			throw new BaseStoneException(ErrorCode.C_1213);
		}
		if (!loginTrace.ssoAuth && !ssoUnAuthFreeLoginSwitch(siteId)) {
			log.error("站点[" + siteId + "]非sso授权免登关闭，[" + memberName + "]不免登");
			throw new BaseStoneException(ErrorCode.C_1213);
		}
	}

	public static boolean saveSession(String memberName, String loginType, String siteId, String targetSiteId) {
		return saveSession(memberName, loginType, siteId, targetSiteId, null);
	}

	public static boolean saveSession(String memberName, String loginType, String siteId, String targetSiteId, String extData) {
		try {
			//插件登录已在freeLogin中保存了session,这里不再保存
			if (LoginType.SSO_FREE_DLL.getType().equals(loginType)) {
				return true;
			}
			boolean ssoAuth = LoginType.checkSsoAuth(loginType);
			String cacheKey = getCacheKey(memberName);
			LoginSession loginSession = RedisContext.getRedisCache().get(cacheKey, LoginSession.class);
			if (StringUtil.isNotBlank(siteId)) {
				if (loginSession == null || loginSession.getSessionMap().get(siteId) == null) {
					log.warn("没有登录记录，[{}]不免登", memberName);
					throw new BaseStoneException(ErrorCode.C_1213);
				}
				if (!loginSession.ssoAuth && !ssoUnAuthFreeLoginSwitch(targetSiteId)) {
					log.warn("站点[{}]非sso授权免登关闭，[{}]不免登", targetSiteId, memberName);
					throw new BaseStoneException(ErrorCode.C_1213);
				}
			}
			if (loginSession == null) {
				loginSession = new LoginSession();
				loginSession.setMemberName(memberName);
				loginSession.setLoginType(loginType);
				loginSession.setSiteId(targetSiteId);
				loginSession.setExtData(extData);
				loginSession.setSsoAuth(ssoAuth);
				loginSession.setTraceId(UUID.randomUUID().toString());
			}
			loginSession.setSsoAuth(ssoAuth);
			loginSession.setTimeExpire(DateUtil.ymdhmsFormat(DateUtil.addDay(new Date(), 1)));
			if (StringUtil.isNotBlank(extData)) {
				loginSession.setExtData(extData);
			}
			LoginSessionItem item = new LoginSessionItem();
			item.setMemberName(memberName);
			item.setLoginType(loginType);
			item.setSiteId(targetSiteId);
			item.setFromSiteId(siteId);
			item.setTimeExpire(loginSession.getTimeExpire());
			item.setSsoAuth(loginSession.ssoAuth);
			item.setTraceId(loginSession.traceId);
			loginSession.getSessionMap().put(targetSiteId, item);
			RedisContext.getRedisCache().set(cacheKey, loginSession, 24 * 60 * 60L, TimeUnit.SECONDS);
			logTrace(loginSession);
		} catch (Exception e) {
			log.error("保存loginSession异常：[{}]", e.getMessage());
		}
		return true;
	}

	public static boolean saveMasterSession(String subMemberName, String memberName, String loginType, String siteId, String targetSiteId) {
		return saveMasterSession(subMemberName, memberName, loginType, siteId, targetSiteId, true);
	}

	public static boolean saveMasterSession(String subMemberName, String memberName, String loginType, String siteId, String targetSiteId, boolean resetAuth) {
		try {
			LoginSession subItem = RedisContext.getRedisCache().get(getCacheKey(subMemberName), LoginSession.class);
			if (StringUtil.isNotBlank(siteId)) {
				if (subItem == null || subItem.getSessionMap().get(siteId) == null) {
					log.warn("没有登录记录，[{}]不免登", memberName);
					throw new BaseStoneException(ErrorCode.C_1213);
				}
				if (!subItem.ssoAuth && !ssoUnAuthFreeLoginSwitch(targetSiteId)) {
					log.warn("站点[{}]非sso授权免登关闭，[{}]不免登", targetSiteId, memberName);
					throw new BaseStoneException(ErrorCode.C_1213);
				}
			}
			String cacheKey = getCacheKey(memberName);
			LoginSession loginSession = RedisContext.getRedisCache().get(cacheKey, LoginSession.class);
			if (loginSession == null) {
				loginSession = new LoginSession();
				BeanUtils.copyProperties(subItem, loginSession);
				loginSession.setMemberName(memberName);
				loginSession.setLoginType(loginType);
				loginSession.setSiteId(targetSiteId);
			}
			if (subItem != null)	{
				loginSession.setSsoAuth(resetAuth ? subItem.ssoAuth : loginSession.ssoAuth);
				if (StringUtil.isNotBlank(subItem.extData)) {
					loginSession.setExtData(subItem.extData);
				}
				loginSession.getSessionMap().put(subItem.getSiteId(), subItem.getSessionMap().get(subItem.getSiteId()));
			}

			loginSession.setTimeExpire(DateUtil.ymdhmsFormat(DateUtil.addDay(new Date(), 1)));
			LoginSessionItem item = new LoginSessionItem();
			item.setMemberName(subMemberName);
			item.setLoginType(loginType);
			item.setSiteId(targetSiteId);
			item.setFromSiteId(siteId);
			item.setTimeExpire(loginSession.getTimeExpire());
			item.setSsoAuth(loginSession.ssoAuth);
			item.setTraceId(loginSession.traceId);
			loginSession.getSessionMap().put(targetSiteId, item);
			RedisContext.getRedisCache().set(cacheKey, loginSession, 24 * 60 * 60L, TimeUnit.SECONDS);
			logTrace(loginSession);
		} catch (Exception e) {
			log.error("保存loginSession异常：[{}]", e.getMessage());
		}
		return true;
	}

	private static void logTrace(LoginSession loginTrace) {
		StringBuilder sb = new StringBuilder();
		sb.append("user:[").append(loginTrace.getMemberName())
				.append(",from:").append(loginTrace.siteId)
				.append(",ssoAuth:").append(loginTrace.ssoAuth)
				.append(",traceId:").append(loginTrace.traceId)
				.append(",timeExpire:").append(loginTrace.timeExpire).append("]");
		loginTrace.getSessionMap().forEach((k, v) -> {
			sb.append(",item[").append("siteId:").append(k).append(",info:[")
					.append(v.getMemberName()).append(",").append(v.getLoginType()).append(",").append(v.getFromSiteId())
					.append(",").append(v.isSsoAuth()).append(",").append(v.getTraceId()).append(",").append(v.timeExpire).append("]]");
		});
		sb.append("]");
		log.info(sb.toString());
	}

	public static class LoginSession {
		private String memberName;
		private String loginType;
		private String siteId;
		private boolean ssoAuth;
		private String timeExpire;
		private String extData;
		private String traceId;
		private LinkedHashMap<String, LoginSessionItem> sessionMap = new LinkedHashMap<>();

		public String getMemberName() {
			return memberName;
		}

		public void setMemberName(String memberName) {
			this.memberName = memberName;
		}

		public String getLoginType() {
			return loginType;
		}

		public void setLoginType(String loginType) {
			this.loginType = loginType;
		}

		public String getSiteId() {
			return siteId;
		}

		public void setSiteId(String siteId) {
			this.siteId = siteId;
		}

		public boolean isSsoAuth() {
			return ssoAuth;
		}

		public void setSsoAuth(boolean ssoAuth) {
			this.ssoAuth = ssoAuth;
		}

		public String getTimeExpire() {
			return timeExpire;
		}

		public void setTimeExpire(String timeExpire) {
			this.timeExpire = timeExpire;
		}

		public String getExtData() {
			return extData;
		}

		public void setExtData(String extData) {
			this.extData = extData;
		}

		public String getTraceId() {
			return traceId;
		}

		public void setTraceId(String traceId) {
			this.traceId = traceId;
		}

		public LinkedHashMap<String, LoginSessionItem> getSessionMap() {
			return sessionMap;
		}

		public void setSessionMap(LinkedHashMap<String, LoginSessionItem> sessionMap) {
			this.sessionMap = sessionMap;
		}
	}

	static class LoginSessionItem {
		private String memberName;
		private String loginType;
		private String siteId;
		private String fromSiteId;
		private boolean ssoAuth;
		private String timeExpire;
		private String traceId;

		public String getMemberName() {
			return memberName;
		}

		public void setMemberName(String memberName) {
			this.memberName = memberName;
		}

		public String getLoginType() {
			return loginType;
		}

		public void setLoginType(String loginType) {
			this.loginType = loginType;
		}

		public String getSiteId() {
			return siteId;
		}

		public void setSiteId(String siteId) {
			this.siteId = siteId;
		}

		public boolean isSsoAuth() {
			return ssoAuth;
		}

		public void setSsoAuth(boolean ssoAuth) {
			this.ssoAuth = ssoAuth;
		}

		public String getTimeExpire() {
			return timeExpire;
		}

		public void setTimeExpire(String timeExpire) {
			this.timeExpire = timeExpire;
		}

		public String getFromSiteId() {
			return fromSiteId;
		}

		public void setFromSiteId(String fromSiteId) {
			this.fromSiteId = fromSiteId;
		}

		public String getTraceId() {
			return traceId;
		}

		public void setTraceId(String traceId) {
			this.traceId = traceId;
		}
	}
	
}
