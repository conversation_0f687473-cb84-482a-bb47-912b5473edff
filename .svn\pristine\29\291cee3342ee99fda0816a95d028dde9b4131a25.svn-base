package com.shunwang.baseStone.sso.web;

import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.shunwang.baseStone.bussiness.dao.BussinessDao;
import com.shunwang.baseStone.bussiness.pojo.Bussiness;
import com.shunwang.baseStone.cache.CacheKeyConstant;
import com.shunwang.baseStone.config.constants.ConfigOauthConstant;
import com.shunwang.baseStone.context.LoginElementService;
import com.shunwang.baseStone.context.RedisContext;
import com.shunwang.baseStone.core.detail.HasDetail;
import com.shunwang.baseStone.css.context.CssContext;
import com.shunwang.baseStone.css.pojo.Css;
import com.shunwang.baseStone.loginelement.constants.LoginElementConstant;
import com.shunwang.baseStone.sso.constant.UserOutsiteConstant;
import com.shunwang.baseStone.sso.intfc.InterfaceService;
import com.shunwang.baseStone.sso.util.CommonUtil;
import com.shunwang.baseStone.sysconfig.context.SysConfigContext;
import com.shunwang.basepassport.detail.common.DetailContants;
import com.shunwang.basepassport.user.common.UserLoginSessionUtil;
import com.shunwang.basepassport.user.dao.MemberAccountBindDao;
import com.shunwang.basepassport.user.dao.MemberDao;
import com.shunwang.basepassport.user.dao.MemberOutSiteDao;
import com.shunwang.basepassport.user.pojo.*;
import com.shunwang.basepassport.user.service.BarFreeLoginAuthService;
import com.shunwang.basepassport.user.service.DcReportService;
import com.shunwang.basepassport.weixin.constant.BarLoginEnum;
import com.shunwang.basepassport.weixin.constant.WeixinConstant;
import com.shunwang.basepassport.weixin.pojo.WeixinOpenIdUnionId;
import com.shunwang.basepassport.weixin.service.WeixinOpenIdUnionIdService;
import com.shunwang.util.OS.OsUtil;
import com.shunwang.util.date.DateUtil;
import com.shunwang.util.json.GsonUtil;
import com.shunwang.util.math.RandomUtil;
import org.apache.struts2.ServletActionContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.shunwang.baseStone.context.IPContext;
import com.shunwang.baseStone.core.exception.BaseStoneException;
import com.shunwang.baseStone.siteinterface.context.SiteContext;
import com.shunwang.baseStone.sso.constant.LoginConstant;
import com.shunwang.baseStone.sso.constant.RandConstant;
import com.shunwang.baseStone.sso.context.FreeLoginTicketContext;
import com.shunwang.baseStone.sso.context.TicketContext;
import com.shunwang.baseStone.sso.context.UserLoginContext;
import com.shunwang.baseStone.sso.context.UserTockenContext;
import com.shunwang.baseStone.sso.pojo.FreeLoginTicket;
import com.shunwang.framework.struts2.action.BaseAction;
import com.shunwang.util.lang.StringUtil;
import com.shunwang.util.net.IpUtil;

import java.io.IOException;
import java.io.PrintWriter;
import java.math.BigInteger;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

public class FreeLoginAction extends BaseAction {

	private static final long serialVersionUID = 3381343300838288383L;
	private static final Logger logger = LoggerFactory.getLogger(FreeLoginAction.class);
	private int rand = RandConstant.rand(20000);
	//要免登录的站点ID
	private String site_id;
	private String tockenId;//令牌信息
	private String freeTicket;
	private String site_name;
	private Integer memberId;
	private String userName;
	private Member member;
	private String ticket;
	private String callbackUrl="";
	//默认为普通登录;
	private String loginType= "normalLogin";
	private String loginPage;
	private String version;
	private String env;
	private String extData;
	private String tgt;

	protected BussinessDao bussinessDao ;
	protected WeixinOpenIdUnionIdService weixinOpenIdUnionIdService;
	protected BarFreeLoginAuthService barFreeLoginAuthService;
	protected Boolean isSingleAccount = Boolean.FALSE;
	private String singleBindSign;
	protected LoginElementService loginElementService;
	private MemberOutSiteDao memberOutSiteDao;
	protected MemberAccountBindDao memberAccountBindDao ;
	protected MemberDao memberDao;
	protected InterfaceService interfaceService;
	protected Css loginCss ;
	private boolean needCheckCode;
	private boolean singleGeetestSwitch = false;
	private int count;
	private boolean showGt;

	@Override
	public String execute() throws Exception {
		String requestType = getRequest().getHeader("X-Requested-With");
		if (StringUtil.isNotBlank(requestType) && requestType.equalsIgnoreCase("XMLHttpRequest")) {
			return processAjax();
		}
		return process();
	}

	public String process() {
		initLoginPage();
		if(paramIsBlank()){
			setMsg("非法请求[Param null]");
			return loginPage;
		}
		FreeLoginTicket freeLoginTicket = FreeLoginTicketContext.getTicketById(freeTicket);
		if(freeLoginTicket == null || (!freeLoginTicket.getMemberId().equals(memberId))) {
			logger.error("freeLoginTicket验证失败, loginTicket={}, memberId={}", freeTicket, freeLoginTicket == null ? "" : freeLoginTicket.getMemberId());
			setMsg("该账号已过期,请重新登录");
			return loginPage;
		}
		setUserName(freeLoginTicket.getMemberName());
		try{
			IPContext.setIp(IpUtil.getIpAddress(ServletActionContext.getRequest()));
			SiteContext.setSiteId(site_id);
			String loginMsg = StringUtil.isBlank(extData)?site_id:site_id+" || "+extData;
			initLoginElement(site_id);
			boolean ssoAuth = UserLoginSessionUtil.isSsoAuth(userName);
			if (!ssoAuth) {
				//sso授权的登录不应该进入
				logger.error("非ssoAuth的帐号目前不支持多账号的免登,请检查站点配置");
				return loginPage;
			}
			if (Boolean.TRUE.equals(isSingleAccount)) {
				MemberOutSite outSite = memberOutSiteDao.getMemberByMemberId(memberId);
				String memberFrom = outSite == null ? "" : outSite.getMemberFrom();
				MemberAccountBind singleAccount = getSingleAccount(memberFrom, memberId);
				if (singleAccount == null) {
					initCss(site_id);
					setSingleBindSign(createSingleBindSign(memberFrom, memberId));
					return singleBindInputView();//单账号绑定
				}
			}
			member = UserLoginContext.loginWithNoPwd(member == null ? userName : member.getMemberName() ,version,env,loginMsg,true);
			UserLoginSessionUtil.saveMasterSession(freeLoginTicket.getMemberName(),member.getMemberName(), UserLoginSessionUtil.LoginType.SSO_FREE_DLL.getType(), freeLoginTicket.getSiteId(), site_id);
			String freeTicketSiteId = freeLoginTicket.getSiteId() ;
			setTicket(TicketContext.createTicket(member, freeTicketSiteId, UserLoginSessionUtil.LoginType.SSO_FREE_DLL.getType()).toString());
			setTockenId(UserTockenContext.createTocken(member, site_id).toString());
			DcReportService.freeLoginReport(member.getMemberName(), DcReportService.OptTypeAndExtInfo.FREE_LOGIN_HEAD_AUTH_AUTH_SUCCESS, getSite_id());
			CommonUtil.addCookieAfterLogin(getRequest(), getResponse(), getTockenId(), member.getMemberId());
		} catch (BaseStoneException e) {
			setErrorMsg(e.getMessage());
			return INPUT;
		}
		return SUCCESS;
	}

	public String processAjax() {
		boolean unAuthFreeLogin = UserLoginSessionUtil.ssoUnAuthFreeLoginSwitch(site_id);
		if(paramIsBlank() || !unAuthFreeLogin){
			outMsg("{\"qrType\":\"none\",\"msg\":\"unAuth no freeLogin\"}");
			return null;
		}

		FreeLoginTicket freeLoginTicket = FreeLoginTicketContext.getTicketById(freeTicket);
		if(freeLoginTicket == null || (!freeLoginTicket.getMemberId().equals(memberId))) {
			logger.error("freeLoginTicket验证失败, loginTicket={}, memberId={}", freeTicket, freeLoginTicket == null ? "" : freeLoginTicket.getMemberId());
			outMsg("{\"qrType\":\"none\",\"msg\":\"wrong ticket or timeout\"}");
			return null;
		}
		setUserName(freeLoginTicket.getMemberName());
		try{
			IPContext.setIp(IpUtil.getIpAddress(ServletActionContext.getRequest()));
			SiteContext.setSiteId(site_id);
			String loginMsg = StringUtil.isBlank(extData)?site_id:site_id+" || "+extData;
			initLoginElement(site_id);
			boolean ssoAuth = UserLoginSessionUtil.isSsoAuth(getUserName());
			if (ssoAuth) {
				//sso授权的登录不应该进入
				outMsg("{\"qrType\":\"none\",\"msg\":\"access error\"}");
				return null;
			}
			if (Boolean.TRUE.equals(isSingleAccount)) {
				MemberOutSite outSite = memberOutSiteDao.getMemberByMemberId(memberId);
				String memberFrom = outSite == null ? "" : outSite.getMemberFrom();
				MemberAccountBind singleAccount = getSingleAccount(memberFrom, memberId);
				Map<String, String> ajaxResult = new HashMap<>();
				if (singleAccount == null) {
					String businessScene = buildAndCacheScene(freeLoginTicket.getMemberName(), UserOutsiteConstant.WXGZH_INTERFACE_ID);
					ajaxResult.put("qrType", UserOutsiteConstant.WXGZH_INTERFACE_ID);
					ajaxResult.put("businessScene", businessScene);
					outMsg(GsonUtil.toJson(ajaxResult));
					DcReportService.freeLoginReport(freeLoginTicket.getMemberName(), DcReportService.OptTypeAndExtInfo.FREE_LOGIN_WX_BIND, getSite_id());
					return null;
				}
				String sessionExtData = UserLoginSessionUtil.getExtData(freeLoginTicket.getMemberName());
				if (StringUtil.isNotBlank(sessionExtData)) {
					JsonObject jsonObject = JsonParser.parseString(sessionExtData).getAsJsonObject();
					String name = GsonUtil.getStringFromJsonObject(jsonObject, "name");
					if (singleAccount.getWeixin() == null) {
						String businessScene = buildAndCacheScene(freeLoginTicket.getMemberName(), UserOutsiteConstant.MINI_INTERFACE_ID);
						ajaxResult.put("qrType", UserOutsiteConstant.MINI_INTERFACE_ID);
						ajaxResult.put("businessScene", businessScene);
						ajaxResult.put("name", name);
						outMsg(GsonUtil.toJson(ajaxResult));
						DcReportService.freeLoginReport(freeLoginTicket.getMemberName(), DcReportService.OptTypeAndExtInfo.FREE_LOGIN_MINI_BIND, getSite_id());
						return null;
					}
					//检测是否关注了公众号
					String appid = RedisContext.getResourceCache().getResourceValue(
							CacheKeyConstant.ConfigResourcesConstants.NET_BAR_FREE_LOGIN_CONFIG, CacheKeyConstant.ConfigResourcesConstants.DEFAULT_APPID);
					MemberOutSite wxOutSite = memberOutSiteDao.getMemberByMemberId(singleAccount.getWeixin());
					WeixinOpenIdUnionId appIdAndUnionId = weixinOpenIdUnionIdService.getByAppIdAndUnionId(appid, wxOutSite.getOutMemberId());
					if (appIdAndUnionId == null) {
						String businessScene = buildAndCacheScene(freeLoginTicket.getMemberName(), UserOutsiteConstant.WXGZH_INTERFACE_ID);
						ajaxResult.put("qrType", UserOutsiteConstant.WXGZH_INTERFACE_ID);
						ajaxResult.put("businessScene", businessScene);
						ajaxResult.put("name", name);
						outMsg(GsonUtil.toJson(ajaxResult));
						DcReportService.freeLoginReport(freeLoginTicket.getMemberName(), DcReportService.OptTypeAndExtInfo.FREE_LOGIN_WX_SUB, getSite_id());
						return null;
					}

					String barid = GsonUtil.getStringFromJsonObject(jsonObject, "barId");
					if (StringUtil.isNotBlank(barid)) {
						boolean barAuthSwitch = RedisContext.getSwitchConfig(CacheKeyConstant.ConfigResourcesConstants.NET_BAR_FREE_LOGIN_CONFIG,
								CacheKeyConstant.ConfigResourcesConstants.BAR_AUTH_SWITCH, true);
						if (barAuthSwitch) {
							//检查是否授权了网吧
							BarFreeLoginAuth barAuth = barFreeLoginAuthService.getByBaridAndMemberId(barid, memberId);
							if (barAuth == null || BarLoginEnum.BarAuthState.AUTH.getValue() != barAuth.getState()) {
								String businessScene = buildAndCacheScene(freeLoginTicket.getMemberName(), UserOutsiteConstant.MINI_INTERFACE_ID);
								ajaxResult.put("qrType", UserOutsiteConstant.MINI_INTERFACE_ID);
								ajaxResult.put("businessScene", businessScene);
								ajaxResult.put("name", name);
								outMsg(GsonUtil.toJson(ajaxResult));
								DcReportService.freeLoginReport(freeLoginTicket.getMemberName(), DcReportService.OptTypeAndExtInfo.FREE_LOGIN_MINI_AUTH, getSite_id());
								return null;
							}
						}
						member = UserLoginContext.loginWithNoPwd(member == null ? userName : member.getMemberName() ,version,env,loginMsg,true);
						UserLoginSessionUtil.saveMasterSession(freeLoginTicket.getMemberName(),member.getMemberName(), UserLoginSessionUtil.LoginType.SSO_FREE_DLL.getType(), freeLoginTicket.getSiteId(), site_id);
						String freeTicketSiteId = freeLoginTicket.getSiteId();
						setTicket(TicketContext.createTicket(member, freeTicketSiteId, UserLoginSessionUtil.LoginType.SSO_FREE_DLL.getType()).toString());
						setTockenId(UserTockenContext.createTocken(member, site_id).toString());

						CommonUtil.addCookieAfterLogin(getRequest(), getResponse(), getTockenId(), member.getMemberId());
						ajaxResult.put("qrType", "dll");
						ajaxResult.put("ssoAuth", "false");
						ajaxResult.put("name", name);
						ajaxResult.put("ticket", getTicket());
						ajaxResult.put("tockenId", getTockenId());
						outMsg(GsonUtil.toJson(ajaxResult));
						DcReportService.freeLoginReport(member.getMemberName(), DcReportService.OptTypeAndExtInfo.FREE_LOGIN_HEAD_AUTH, getSite_id());
						return null;
					}
				}
			}
		} catch (BaseStoneException e) {
			logger.error("免登异常[{}]", e.getMessage(), e);
		}
		outMsg("{\"qrType\":\"none\",\"msg\":\"not single or exception\"}");
		return null;
	}

	private String buildAndCacheScene(String memberName, String type) {
		String businessScene = (UserOutsiteConstant.WXGZH_INTERFACE_ID.equals(type)
				? WeixinConstant.INNER_SCENE_PRE : WeixinConstant.INNER_MINI_PRE)
				+ new BigInteger(DateUtil.getCurrentDateStamp() + RandomUtil.getRandomStr(10)).toString(36);
		Map<String, String> extInfoData = new HashMap<>();
		extInfoData.put("memberName", memberName);
		extInfoData.put("businessType", "innerScan");
		RedisContext.getRedisCache().set(CacheKeyConstant.SSO.INNER_SCAN + businessScene, GsonUtil.toJson(extInfoData), 30, TimeUnit.MINUTES);
		return businessScene;
	}

	/**
	 * 创建单帐号绑定页面的签名
	 * @return
	 */
	protected String createSingleBindSign(String memberFrom, Integer memberId){
		String sign = UUID.randomUUID().toString();
		String key = CacheKeyConstant.InterfaceToken.SINGLE_ACCOUNT_SIGN+ sign;
		SingleAccountToken singleAccountToken = new SingleAccountToken() ;
		switch (memberFrom) {
			case UserOutsiteConstant.QQ_INTERFACE_ID :
				singleAccountToken.setType(ConfigOauthConstant.TYPE.QQ.getInt());
				break;
			case UserOutsiteConstant.WX_INTERFACE_ID :
			case UserOutsiteConstant.WXGZH_INTERFACE_ID :
			case UserOutsiteConstant.MINI_INTERFACE_ID :
				singleAccountToken.setType(ConfigOauthConstant.TYPE.WEIXIN.getInt());
				break;
			case UserOutsiteConstant.WEIBO_INTERFACE_ID :
				singleAccountToken.setType(ConfigOauthConstant.TYPE.WEIBO.getInt());
				break;
			case UserOutsiteConstant.APPLE_INTERFACE_ID :
				singleAccountToken.setType(ConfigOauthConstant.TYPE.APPLE.getInt());
				break;
			case UserOutsiteConstant.ALIPAY_INTERFACE_ID :
				singleAccountToken.setType(ConfigOauthConstant.TYPE.ALIPAY.getInt());
				break;
			case UserOutsiteConstant.IDCARD_INTERFACE_ID :
				singleAccountToken.setType(ConfigOauthConstant.TYPE.ID_CARD.getInt());
				break;
			default:
				singleAccountToken.setType(ConfigOauthConstant.TYPE.NORMAL.getInt());
		}
		singleAccountToken.setUnionId(memberId) ;
		RedisContext.getRedisCache().set(key , singleAccountToken, 30, TimeUnit.MINUTES);

		return sign ;
	}

	public void initLoginElement(String siteId) {
		if (null == siteId) {
			return;
		}
		// 查询账号体系
		Bussiness bussiness = bussinessDao.getById(siteId) ;
		isSingleAccount = bussiness.isSingleAccount() ;
	}

	protected MemberAccountBind getSingleAccount(String memberFrom, Integer memberId){
		MemberAccountBind memberAccountBind;
		MemberAccountBind temp = buildMemberAccountBind();
		switch (memberFrom) {
			case UserOutsiteConstant.QQ_INTERFACE_ID :
				memberAccountBind = memberAccountBindDao.getByQq(memberId);
				temp.beginBuildLog("QQ帐号绑定");
				temp.setQq(memberId);
				break;
			case UserOutsiteConstant.WX_INTERFACE_ID :
			case UserOutsiteConstant.WXGZH_INTERFACE_ID :
			case UserOutsiteConstant.MINI_INTERFACE_ID :
				memberAccountBind = memberAccountBindDao.getByWeixin(memberId);
				temp.beginBuildLog("WEIXIN帐号绑定");
				temp.setWeixin(memberId);
				break;
			case UserOutsiteConstant.WEIBO_INTERFACE_ID :
				memberAccountBind = memberAccountBindDao.getByWeibo(memberId);
				temp.beginBuildLog("WEIBO帐号绑定");
				temp.setWeibo(memberId);
				break;
			case UserOutsiteConstant.APPLE_INTERFACE_ID :
				memberAccountBind = memberAccountBindDao.getByApple(memberId);
				temp.beginBuildLog("APPLE帐号绑定");
				temp.setApple(memberId);
				break;
			case UserOutsiteConstant.ALIPAY_INTERFACE_ID :
				memberAccountBind = memberAccountBindDao.getByAlipay(memberId);
				temp.beginBuildLog("ALIPAY帐号绑定");
				temp.setAlipay(memberId);
				break;
			case UserOutsiteConstant.IDCARD_INTERFACE_ID :
				memberAccountBind = memberAccountBindDao.getByIdCard(memberId);
				temp.beginBuildLog("IDCARD帐号绑定");
				temp.setIdCard(memberId);
				break;
			default:
				memberAccountBind = memberAccountBindDao.getByMemberId(memberId);
				temp.beginBuildLog("普通帐号绑定");
		}
		if(null != memberAccountBind){
			memberId = memberAccountBind.getMemberId() ;
			member = memberDao.getByMemberId(memberId) ;
			return memberAccountBind ;
		}
		member = memberDao.getByMemberId(memberId) ;
		//手机设为登录账号了
		if(member.getMobileAsLoginAccount()){
			memberAccountBind = memberAccountBindDao.getByMobile(member.getMobile());
			if (memberAccountBind != null) {
				if (memberAccountBind.getMemberId().equals(member.getMemberId())) {
					logger.info("账号[{}]绑定的手机已经存在关联表直接绑定", member.getMemberName()) ;
					return memberAccountBind;
				}
				throw new BaseStoneException("", "绑定失败，数据异常");
			}
			temp.setPhone(member.getMobile());
			temp.setTimeAdd(new Date());
			return singleAccountBindExt(temp);
		}
		return null;
	}

	/**
	 * 子类需设定特定id（子类若调用了getSingleAccount()方法必须重写）
	 * @return
	 */
	protected MemberAccountBind buildMemberAccountBind() {
		MemberAccountBind memberAccountBind = new MemberAccountBind();
		memberAccountBind.setMemberId(memberId);
		memberAccountBind.setTimeEdit(new Date());
		return memberAccountBind;
	}

	private MemberAccountBind singleAccountBindExt(MemberAccountBind memberAccountBind) {
		memberAccountBind.getPersonalEditLog().setMember(getMember());
		memberAccountBind.getPersonalEditLog().setUserAdd(getMember().getMemberName());
		memberAccountBind.getPersonalEditLog().setType(DetailContants.FRONT);
		memberAccountBind.setTimeEdit(new Date());
		try {
			memberAccountBindDao.save(memberAccountBind);
			((HasDetail)memberAccountBind).getDetail().save();
		} catch (Exception e) {
			logger.error("绑定异常", e);
			throw new BaseStoneException("", "绑定异常");
		}
		return memberAccountBind;
	}

	/**
	 * @param siteId
	 */
	protected void initCss(String siteId) {
		loginCss = CssContext.getCssBySiteIdAndBussCode(siteId, "login");
	}

	protected String singleBindInputView(){
		//初始化极验信息
		initSingleGeetestSwitch();
		if(OsUtil.isMobile(getRequest().getHeader("User-Agent"))){
			return "singleAccountBindForH5";
		}
		return "singleAccountBind";
	}
	private void initSingleGeetestSwitch() {
		setSingleGeetestSwitch(configIsOpen(loginElementService.getLoginConfig(site_id, LoginElementConstant.SINGLE_GEETEST_SWITCH))
				&& SysConfigContext.isGtSwitchOpen());
	}

	private boolean configIsOpen(Map<String, String> configMap) {
		if (null != configMap) {
			if (null != configMap.get(LoginElementConstant.STATE)
					&& configMap.get(LoginElementConstant.STATE).equals("1")) {
				return Boolean.TRUE;
			}
		}
		return Boolean.FALSE;
	}

	private boolean paramIsBlank(){
		return StringUtil.isBlank(site_id)||
		StringUtil.isBlank(freeTicket)||
		StringUtil.isBlank(String.valueOf(memberId));
	}

	private void initLoginPage(){
		ServletActionContext.getResponse().addHeader("P3P","CP=CAO PSA OUR");
		if(StringUtil.isBlank(loginPage)||!LoginConstant.LOGIN_PAGE_SET.contains(loginPage))
			loginPage = INPUT;
	}
	public String getFmtCallback(){
		if(StringUtil.isBlank(callbackUrl)) return null;
		String split = "?";
		if(callbackUrl.indexOf("?")!=-1){
			split = "&";
		}
		StringBuffer sb = new StringBuffer();
		sb.append(callbackUrl)
		.append(split)
		.append("ticketId=")
		.append(ticket)
		.append("&tockenId=")
		.append(tockenId);
		return sb.toString();
	}

	private void outMsg(String json) {
		PrintWriter out = null;
		try {
			out = this.getResponse().getWriter();
			getResponse().setContentType("text/json;charset=UTF-8");
			out.write(json);
		} catch (IOException e) {
			logger.error("系统异常：",e);
			assert out != null;
			out.write("{\"errorMsg\":\"" + "系统异常，请稍候再试！" + "\"}");
		} finally {
			assert out != null;
			out.close();
		}
	}

	public String getSite_id() {
		return site_id;
	}
	public void setSite_id(String siteId) {
		site_id = siteId;
	}

	public String getTockenId() {
		return tockenId;
	}

	public void setTockenId(String tockenId) {
		this.tockenId = tockenId;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getCallbackUrl() {
		return callbackUrl;
	}

	public void setCallbackUrl(String callbackUrl) {
		this.callbackUrl = callbackUrl;
	}
	public void setTicket(String ticket) {
		this.ticket = ticket;
	}
	public String getTicket() {
		return ticket;
	}
	public Member getMember() {
		return member;
	}
	public void setMember(Member member) {
		this.member = member;
	}
	public void setRand(int rand) {
		this.rand = rand;
	}
	public int getRand() {
		return rand;
	}
	public boolean getNeedSafe() {
		return Boolean.TRUE;
	}
	public void setLoginType(String loginType) {
		this.loginType = loginType;
	}
	public String getLoginType() {
		return loginType;
	}
	public void setVersion(String version) {
		this.version = version;
	}
	public String getVersion() {
		return version;
	}
	public void setEnv(String env) {
		this.env = env;
	}
	public String getEnv() {
		return env;
	}
	public void setExtData(String extData) {
		this.extData = extData;
	}
	public String getExtData() {
		return extData;
	}
	public void setLoginPage(String loginPage) {
		this.loginPage = loginPage;
	}
	public String getLoginPage() {
		return loginPage;
	}
	public void setSite_name(String site_name) {
		this.site_name = site_name;
	}
	public String getSite_name() {
		return site_name;
	}

	public String getFreeTicket() {
		return freeTicket;
	}

	public void setFreeTicket(String freeTicket) {
		this.freeTicket = freeTicket;
	}

	public Integer getMemberId() {
		return memberId;
	}

	public void setMemberId(Integer memberId) {
		this.memberId = memberId;
	}

	public BussinessDao getBussinessDao() {
		return bussinessDao;
	}

	public void setBussinessDao(BussinessDao bussinessDao) {
		this.bussinessDao = bussinessDao;
	}

	public void setSingleAccount(Boolean singleAccount) {
		isSingleAccount = singleAccount;
	}

	public String getSingleBindSign() {
		return singleBindSign;
	}

	public void setSingleBindSign(String singleBindSign) {
		this.singleBindSign = singleBindSign;
	}

	public LoginElementService getLoginElementService() {
		return loginElementService;
	}

	public void setLoginElementService(LoginElementService loginElementService) {
		this.loginElementService = loginElementService;
	}

	public MemberAccountBindDao getMemberAccountBindDao() {
		return memberAccountBindDao;
	}

	public void setMemberAccountBindDao(MemberAccountBindDao memberAccountBindDao) {
		this.memberAccountBindDao = memberAccountBindDao;
	}

	public MemberDao getMemberDao() {
		return memberDao;
	}

	public void setMemberDao(MemberDao memberDao) {
		this.memberDao = memberDao;
	}

	public InterfaceService getInterfaceService() {
		return interfaceService;
	}

	public void setInterfaceService(InterfaceService interfaceService) {
		this.interfaceService = interfaceService;
	}

	public Css getLoginCss() {
		return loginCss;
	}

	public void setLoginCss(Css loginCss) {
		this.loginCss = loginCss;
	}

	public boolean isNeedCheckCode() {
		return needCheckCode;
	}

	public void setNeedCheckCode(boolean needCheckCode) {
		this.needCheckCode = needCheckCode;
	}

	public int getCount() {
		return count;
	}

	public void setCount(int count) {
		this.count = count;
	}

	public boolean isShowGt() {
		return showGt;
	}

	public void setShowGt(boolean showGt) {
		this.showGt = showGt;
	}

	public MemberOutSiteDao getMemberOutSiteDao() {
		return memberOutSiteDao;
	}

	public void setMemberOutSiteDao(MemberOutSiteDao memberOutSiteDao) {
		this.memberOutSiteDao = memberOutSiteDao;
	}

	public boolean isSingleGeetestSwitch() {
		return singleGeetestSwitch;
	}

	public void setSingleGeetestSwitch(boolean singleGeetestSwitch) {
		this.singleGeetestSwitch = singleGeetestSwitch;
	}

	public String getTgt() {
		return tgt;
	}

	public void setTgt(String tgt) {
		this.tgt = tgt;
	}

	public WeixinOpenIdUnionIdService getWeixinOpenIdUnionIdService() {
		return weixinOpenIdUnionIdService;
	}

	public void setWeixinOpenIdUnionIdService(WeixinOpenIdUnionIdService weixinOpenIdUnionIdService) {
		this.weixinOpenIdUnionIdService = weixinOpenIdUnionIdService;
	}

	public BarFreeLoginAuthService getBarFreeLoginAuthService() {
		return barFreeLoginAuthService;
	}

	public void setBarFreeLoginAuthService(BarFreeLoginAuthService barFreeLoginAuthService) {
		this.barFreeLoginAuthService = barFreeLoginAuthService;
	}
}
