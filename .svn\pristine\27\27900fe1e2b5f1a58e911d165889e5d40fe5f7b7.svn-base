<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYP<PERSON> struts PUBLIC "-//Apache Software Foundation//DTD Struts Configuration 2.0//EN" "http://struts.apache.org/dtds/struts-2.0.dtd">

<struts>
	<package name="upload" namespace="/front/upload" extends="struts-default">
        <!--<result-types>-->
            <!--<result-type name="json" class="org.apache.struts2.json.JSONResult"/>-->
        <!--</result-types>-->
        <!--<interceptors>-->
            <!--<interceptor name="json" class="org.apache.struts2.json.JSONInterceptor"/>-->
        <!--</interceptors>-->
		<!--	对应包下面值包含fileUploadStack拦截器栈	-->
		<default-interceptor-ref name="fileUploadStack"></default-interceptor-ref>
		<action name="upload" class="com.shunwang.passport.upload.web.UploadAction"/>
        <action name="ajaxUploadFile" method="ajaxUploadFile" class="com.shunwang.passport.upload.web.UploadAction"/>
        <action name="ajaxUploadNetFile" method="ajaxUploadNetFile" class="com.shunwang.passport.upload.web.UploadAction"/>
		<action name="bigHeadSwfUpload" class="com.shunwang.passport.member.web.BigHeadImageUploadAction" >
        </action>
        
        <action name="smallHeadSwfUpload" class="com.shunwang.passport.member.web.SmallHeadImageUploadAction" >
        </action>

        <action name="iframeBigHeadUpload" method="iframeUpload" class="com.shunwang.passport.member.web.BigHeadImageUploadAction">
            <!--<interceptor-ref name="fileUpload">-->
                <!--<param name="maximumSize">2097152</param> &lt;!&ndash; max 2M &ndash;&gt;-->
                <!--<param name="allowedTypes">image/bmp,image/x-png,image/gif,image/pjpeg,image/jpeg,image/jpg,application/octet-stream</param>-->
            <!--</interceptor-ref>-->
            <!--<interceptor-ref name="swDefaultStack" />-->
            <!--<result type="json"><param name="contentType">text/html</param></result>-->
        </action>
	</package>
	
</struts>
