package com.shunwang.basepassport.user.exception;

import com.shunwang.baseStone.core.exception.BaseStoneException;
/**
 * @Description:数据参数不正确异常
 * <AUTHOR>  create at 2011-7-27 下午03:08:09
 * @FileName com.shunwang.basepassport.user.exception.MsgIllExp.java
 */
public class MsgIllExp extends BaseStoneException {

	/**
	 * <AUTHOR> create at 2011-7-27 下午03:08:35 
	 */
	private static final long serialVersionUID = -256170208575777750L;

	public MsgIllExp(String name) {
		super("1047",name+"不合法！");
	}
	
}
