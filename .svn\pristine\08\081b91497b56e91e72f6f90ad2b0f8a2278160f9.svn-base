package com.shunwang.baseStone.jms;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jms.core.JmsTemplate;

import javax.jms.Destination;

/**
 * 消息生成者，负责向消费服务（MQ）发送消息到队列消息
 *
 * <AUTHOR>
 * @date 2019/3/11
 **/
public class MessageProducer {

    private static final Logger LOGGER = LoggerFactory.getLogger(MessageProducer.class);

    protected JmsTemplate template;
    protected Destination destination;

    /**
     * 发送
     *
     * @param message 消息
     */
    public <T> void sendMessage(T message) {
        LOGGER.info("发送清除缓存消息 - start message:{}", message);
        // 发送
        this.template.convertAndSend(this.destination, message.toString());
        LOGGER.info("发送清除缓存消息 - end message:{}", message);
    }


    public void setTemplate(JmsTemplate template) {
        this.template = template;
    }

    public void setDestination(Destination destination) {
        this.destination = destination;
    }
}
