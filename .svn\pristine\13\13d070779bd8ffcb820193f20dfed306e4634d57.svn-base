package com.shunwang.baseStone.cache.alert;

import com.shunwang.baseStone.context.RedisContext;
import com.shunwang.baseStone.context.RedisOperation;
import com.shunwang.util.date.DateUtil;

import java.util.Date;
import java.util.concurrent.TimeUnit;

public class RedisWindow implements Window {
    private String key;
    private long windowSizeInMillis;

    public RedisWindow(String key, long expire, TimeUnit timeUnit) {
        this.key = key;
        windowSizeInMillis = timeUnit.toMillis(expire);
    }


    /**
     * 基于redis 实现滑动窗口，用来统计失败格式
     *
     * @return 当前key 在时间窗口中的元素个数
     * @link {https://dzone.com/articles/rolling-time-window-counters}
     */
    @Override
    public Window addAndGet() {
        //转成秒
        long now = System.currentTimeMillis();
        //当前时间点 - 窗口大小的时间点之前的属于老的key 需要过期
        long expires = now - getWindowsSizeInSeconds();
        //把过期时间前的key全部删除
        getRedisOperation().zremrangeByScore(getKey(), "-inf", String.valueOf(expires));
        //添加当前时间点
        getRedisOperation().zadd(getKey(), now, String.valueOf(now));
        //返回集合种元素数量
        getRedisOperation().zcard(getKey());
        return this;
    }


    /**
     * Get the current hits per rolling time window.
     *
     * @return how many hits we have within the current rolling time window
     */
    @Override
    public long totalCount() {
        //转成秒
        long now = System.currentTimeMillis();
        //当前时间点 - 窗口大小的时间点之前的属于老的key 需要过期
        long expires = now - getWindowsSizeInSeconds();
        //把过期时间前的key全部删除
        getRedisOperation().zremrangeByScore(getKey(), "-inf", String.valueOf(expires));
        //返回集合种元素数量
        return getRedisOperation().zcard(getKey());
    }

    @Override
    public void triggerOn(int limit, TriggerCallback callback) {
        long totalCount = totalCount();
        if (totalCount > limit && !withInLastTriggerTime()) {
            if (callback != null) {
                callback.onTriggered(totalCount);
                callback.afterTriggered();
            }
        }
    }

    /**
     * 是否 上次报警 + 时间窗时间 > 当前时间
     *
     * @return
     */
    public boolean withInLastTriggerTime() {
        Date lastTriggerTime = getLastTriggerTime();
        if (lastTriggerTime == null) {
            return false;
        }
        Date afterTime = DateUtil.addSecond(lastTriggerTime, (int) windowSizeInMillis / 1000);
        return DateUtil.compare(afterTime, new Date(), DateUtil.ONE_SECOND) > 0;
    }


    @Override
    public void updateTriggerTime() {
        getRedisOperation().set(getLastTriggerTimeKey(), new Date(), windowSizeInMillis / 1000, TimeUnit.SECONDS);
    }

    @Override
    public String getKey() {
        return key;
    }

    @Override
    public String getLastTriggerTimeKey() {
        return key + LAST_TIME_TRIGGER_KEY;
    }

    @Override
    public Date getLastTriggerTime() {
        String lastTriggerTimeKey = getLastTriggerTimeKey();
        return getRedisOperation().get(lastTriggerTimeKey, Date.class);
    }

    protected RedisOperation getRedisOperation() {
        return RedisContext.getRedisCache();
    }

    public long getWindowsSizeInSeconds() {
        return windowSizeInMillis;
    }
}
