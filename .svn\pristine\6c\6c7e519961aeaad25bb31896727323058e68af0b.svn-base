package com.shunwang.baseStone.sso.context;

import com.shunwang.baseStone.context.RedisContext;
import com.shunwang.baseStone.sso.exception.TicketExp;
import com.shunwang.baseStone.sso.pojo.Ticket;
import com.shunwang.baseStone.sso.pojo.UserTocken;
import com.shunwang.basepassport.user.common.HeadUrlUtil;
import com.shunwang.basepassport.user.pojo.Member;
import com.shunwang.util.date.DateUtil;
import com.shunwang.util.lang.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.TimeUnit;

public class TicketContext {
	
	private static final Logger logger = LoggerFactory.getLogger(TicketContext.class);
	
	public TicketContext(){
		
	}

	private static void setCache(Ticket ticket){
		boolean result = RedisContext.getRedisCache().set(getId(ticket.getTicketId()),ticket, 5, TimeUnit.MINUTES);
		if(logger.isInfoEnabled()) {
			logger.info("createTicket," + ticket.getTicketId() + "," + ticket.getUserName() + "," + ticket.getUserId() + "," + DateUtil.getDateStamp(ticket.getExpiryDate()) + "," + result);
		}
	}

	public static Ticket createTicket(Member member,String siteId){
		return createTicket(member, siteId, null);
	}

	public static Ticket createTicket(Member member,String siteId, String loginType){
		return createTicket(member, siteId, loginType, null, false);
	}

	public static Ticket createTicket(Member member,String siteId, String loginType, Integer guestType, boolean isMaster){
		Ticket ticket = new Ticket();
		ticket.setUserId(member.getMemberId());
		ticket.setUserName(member.getMemberName());
		ticket.setTitleName(member.getTitleName());
		ticket.setNickName(member.getNickName());
		if(StringUtil.isNotBlank(siteId)) {
			ticket.setSiteId(siteId);
		}
		ticket.setLoginType(loginType);
		ticket.setOpenId(member.getOpenId());
		ticket.setHeadImg(HeadUrlUtil.getSmallHeadImageUrl(member));
		if (guestType != null) {
			ticket.setGuestType(guestType);
			ticket.setMaster(isMaster);
		}
		setCache(ticket);

		return ticket;
	}
	
	public static Ticket createTicket(UserTocken tocken,String siteId){
		return createTicket(tocken, siteId, null);
	}

	public static Ticket createTicket(UserTocken tocken,String siteId, String loginType){
		Ticket ticket = new Ticket();
		ticket.setUserId(tocken.getUserId());
		ticket.setUserName(tocken.getUserName());
		ticket.setTitleName(tocken.getTitleName());
		ticket.setNickName(tocken.getNickName());
		ticket.setHeadImg(tocken.getHeadImg());
		ticket.setSiteId(siteId) ;
		ticket.setLoginType(loginType);
		setCache(ticket);

		return ticket;
	}
	
	public static Ticket createTicket(Ticket oldTicket){
		Ticket ticket = new Ticket();
		ticket.setUserId(oldTicket.getUserId());
		ticket.setUserName(oldTicket.getUserName());
		ticket.setNickName(oldTicket.getNickName());
		ticket.setHeadImg(oldTicket.getHeadImg());
		ticket.setTitleName(oldTicket.getTitleName());
		setCache(ticket);

		return ticket;
	}
	
	protected static String getId(String ticketId){
		return "ticket[" + ticketId + "]";
	}
	
	public static Ticket getTicketById (String id){
		return  RedisContext.getRedisCache().get(getId(id),Ticket.class);
	}
	/**
	 * 检验ticket是否合法
	 * @param id
	 */
	public static Ticket validateTicket(String id){
		if(logger.isInfoEnabled()) {
			logger.info("validateTicket," + id);
		}
		
		if(id==null) throw new TicketExp();
		Ticket oldTicket = getTicketById(id);
		if( oldTicket==null){
			throw new TicketExp();
		} else {
			boolean result =RedisContext.getRedisCache().del(getId(id));
			if(logger.isInfoEnabled()) {
				logger.info("delete," + getId(id) + "," + result);
			}
			
		}
		return oldTicket;
		
	}
	
}
