package com.shunwang.baseStone.sso.apapter;

import com.shunwang.baseStone.sso.constant.UserOutsiteConstant;
import com.shunwang.baseStone.sso.weixin.oauth.service.WeixinRemoteCall;
import com.shunwang.baseStone.sso.weixin.pojo.WeixinResult;
import com.shunwang.baseStone.sso.weixin.pojo.WeixinUser;
import com.shunwang.basepassport.config.pojo.Oauth;
import com.shunwang.basepassport.user.common.MemberConstants;
import com.shunwang.basepassport.user.common.MemberUtil;
import com.shunwang.basepassport.user.common.UserLoginSessionUtil;
import com.shunwang.basepassport.user.dao.MemberOutSiteDao;
import com.shunwang.basepassport.weixin.constant.WeixinConstant;
import com.shunwang.basepassport.weixin.exception.WeixinOauthException;
import com.shunwang.basepassport.weixin.pojo.WeixinOauth;
import com.shunwang.basepassport.weixin.pojo.WeixinOauthToken;
import com.shunwang.basepassport.weixin.pojo.WeixinOpenIdUnionId;
import com.shunwang.util.StringUtil;
import org.apache.struts2.ServletActionContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

public class WeixinAuthAdapter extends WeixinAdapter {

    private final static long serialVersionUID = 125231234L;
    private static final Logger log = LoggerFactory.getLogger(WeixinAuthAdapter.class);

    private static final String WEIXIN_OAUTH2_URL = "https://open.weixin.qq.com/connect/oauth2/authorize";
    private static final String WEIXIN_OAUTH2_QR_URL = "https://open.weixin.qq.com/connect/qrconnect";

    private String code;

    private WeixinRemoteCall weixinRemoteCall;

    private String weixinWebCallbackUrl;

    @Override
    public String getInterfaceId() {
        return UserOutsiteConstant.WX_INTERFACE_ID;
    }

	@Override
	public String goToOauth() throws Exception {
        String userAgent = ServletActionContext.getRequest().getHeader("User-Agent");
        if(userAgent != null && userAgent.contains("MicroMessenger") && !userAgent.contains("WindowsWechat")) { //微信内嵌浏览器打开
            return goToOauthH5();
        }
        //以下都当PC处理，生成二维码
        Oauth configOauth = getOauthConfig();
        if (null == configOauth) {
            log.info("第三方配置获取出现异常,[siteId:{}]", site_id);
            throw new RuntimeException("获取第三方配置异常");
        }

        String userSceneKey = com.shunwang.util.StringUtil.isBlank(innerScene) ? buildCacheUserKey() : innerScene;

        //未配置有效期，默认30秒
        cacheExtData(userSceneKey, 30 * 60);

		String redirectUrl = weixinWebCallbackUrl +
                "?site_id=" + site_id +
                "&callbackUrl=" + callbackUrl +
                "&userSceneKey=" + userSceneKey +
                "&tgt=" + (StringUtil.isBlank(tgt) ? "" : tgt) +
                "&tgtCancel=" + (StringUtil.isBlank(tgtCancel) ? "" : tgtCancel) +
                "&href=" + href;

        StringBuilder sb = new StringBuilder();
        sb.append(WEIXIN_OAUTH2_QR_URL);
        sb.append("?");
        sb.append("appid=").append(configOauth.getAppId()).append("&");
        sb.append("redirect_uri=").append(URLEncoder.encode(redirectUrl, "UTF-8")).append("&");
        sb.append("response_type=").append("code").append("&");
        sb.append("scope=").append("snsapi_login").append("&");
        sb.append("state=").append(UUID.randomUUID()).append("&");
        //href参数传递在微信文档中并没有,但是试验中是可以生效的,这里加上,
        // 同理style参数也可以,但是微信页面上两个颜色很相近,导致无法区分,故此这里不加
        initCss(site_id);
        if (getLoginCss() != null && StringUtil.isNotBlank(getLoginCss().getCssUrl())) {
            sb.append("href=").append(URLEncoder.encode(getLoginCss().getCssUrl(), "UTF-8")).append("&");
        }
        if (StringUtil.isNotBlank(tgt)) {
            sb.append("self_redirect=").append("true").append("&");
        }
        sb.append("#wechat_redirect");
        return sb.toString();
    }

    private WeixinOauth getH5Oauth() {
        WeixinOauth oauth = weixinOauthService.getBySiteId(site_id, WeixinConstant.TYPE.AUTHORIZER) ;
        //传入的为空时，使用默认的站点passport
        String defaultSiteId = "passport";
        oauth = oauth == null ? weixinOauthService.getBySiteId(defaultSiteId, WeixinConstant.TYPE.AUTHORIZER) : oauth;
        if (null == oauth) {
            log.info("第三方配置获取出现异常,[siteId:{}]", site_id);
            throw new RuntimeException("获取第三方配置异常");
        }
        return oauth;
    }

    private String goToOauthH5(){
        WeixinOauth componetOauth = weixinOauthService.getBySiteId(getSsoSiteId(), WeixinConstant.TYPE.COMPONENT);
        String cacheUserKey = StringUtil.isBlank(siteScene) ? buildCacheUserKey() : siteScene;

        //未配置有效期，默认30秒
        cacheExtData(cacheUserKey, 30 * 60);
        String redirectUrl = weixinWebCallbackUrl +
                "?site_id=" + site_id +
                "&callbackUrl=" + callbackUrl +
                "&userSceneKey=" + cacheUserKey +
                "&tgt=" + (StringUtil.isBlank(tgt) ? "" : tgt) +
                "&tgtCancel=" + (StringUtil.isBlank(tgtCancel) ? "" : tgtCancel) +
                "&cacheResult=" + (isMultiTerminalLoginJump() ? "1" : "0") +
                "&href=" + href;

        StringBuilder sb = new StringBuilder();
        sb.append(WEIXIN_OAUTH2_URL);
        sb.append("?");
        sb.append("appid=").append(StringUtil.isBlank(appId) ? getH5Oauth().getAppId() : appId).append("&");
        try {
            sb.append("redirect_uri=").append(URLEncoder.encode(redirectUrl, "UTF-8")).append("&");
        } catch (Exception e) {
            log.error("回调地址编码异常", e);
        }
        sb.append("response_type=").append("code").append("&");
        sb.append("scope=").append("snsapi_userinfo").append("&");
        sb.append("component_appid=").append(componetOauth.getAppId()).append("&");
        sb.append("state=").append(UUID.randomUUID()).append("&");
        if (StringUtil.isNotBlank(tgt)) {
            sb.append("self_redirect=").append("true").append("&");
        }
        sb.append("#wechat_redirect");
        return sb.toString();
    }

    @Override
    public Map<String, Object> getOauth() {
        Oauth configOauth = getOauthConfig() ;
        if(null == configOauth){
            log.info("第三方配置获取出现异常,[siteId:{}]", site_id) ;
            throw new RuntimeException("获取第三方配置异常") ;
        }
        // 创建一个随机场景值，用来查询是否已经关注
        String cacheUserKey = buildCacheUserKey() ;
        //未配置有效期，默认30秒
        cacheExtData(cacheUserKey, 30 * 60);
        String redirectUrl = weixinWebCallbackUrl +
                "?site_id=" + site_id +
                "&callbackUrl=" + callbackUrl +
                "&userSceneKey=" + cacheUserKey +
                "&tgt=" + (StringUtil.isBlank(tgt) ? "" : tgt) +
                "&tgtCancel=" + (StringUtil.isBlank(tgtCancel) ? "" : tgtCancel) +
                "&href=" + href;

        Map<String, Object> resultMap = new HashMap<>();
        try {
            resultMap.put("appid", configOauth.getAppId());
            resultMap.put("redirect_uri", URLEncoder.encode(redirectUrl, "UTF-8"));
            resultMap.put("scope", "snsapi_login");
            resultMap.put("state", UUID.randomUUID());
            resultMap.put("style", "black");
            initCss(site_id);
            resultMap.put("href", getLoginCss().getCssUrl()) ;
        } catch (Exception e) {
            log.error("微信登录pre异常[{}]",e.getMessage(), e);
            throw new WeixinOauthException("获取Css配置数据异常") ;
        }
        return resultMap;
    }

    @Override
    public Map<String, Object> getOauthByServer() {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("oauthUrl", goToOauthH5());
        return resultMap;
    }

    @Override
    public String oauthCallback() {
        //从缓存还原接入方透传的数据
        pullCacheExtData(userSceneKey);
        initContextInfo();
        loadPrivacyCss();
        try {
            WeixinResult result;
            WeixinOpenIdUnionId weixinOpenIdUnionId = new WeixinOpenIdUnionId();
            log.info("微信登录回调，code:" + code);
            String userAgent = ServletActionContext.getRequest().getHeader("User-Agent");
            if(userAgent != null && userAgent.contains("MicroMessenger")) { //微信内嵌浏览器打开
                String appId = redisOperation.get(WeixinConstant.EXT_SCENE_KEY_APPID + userSceneKey);
                WeixinOauth componetOauth = weixinOauthService.getBySiteId(getSsoSiteId(), WeixinConstant.TYPE.COMPONENT);
                WeixinOauthToken componentOauthToken = weixinOauthTokenService.getByAppIdAndType(componetOauth.getAppId(), WeixinConstant.TYPE.COMPONENT) ;
                String weixinAppId = StringUtil.isBlank(appId) ? getH5Oauth().getAppId() : appId;
                weixinOpenIdUnionId.setAppId(weixinAppId);
                result = weixinRemoteCall.queryOpenId(code, weixinAppId, componetOauth.getAppId(), componentOauthToken.getAccessToken());
            } else {
                Oauth configOauth = getOauthConfig();
                if (null == configOauth) {
                    log.info("第三方配置获取出现异常,[siteId:{}]", site_id);
                    throw new RuntimeException("获取第三方配置异常");
                }
                weixinOpenIdUnionId.setAppId(configOauth.getAppId());
                result = weixinRemoteCall.queryOpenId(code, configOauth.getAppId(), configOauth.getAppSecret());
            }
            if (result == null) {
                setErrorMsg("微信授权获取openId失败");
                return INPUT;
            }
            WeixinUser weixinUser = weixinRemoteCall.queryWeixinUserInfo(result);
            if (log.isInfoEnabled()) {
                log.info(weixinUser.getNickname() + ' ' + weixinUser.getHeadimgurl());
            }

            memberOutSite = buildMemberOutSite(weixinUser, UserOutsiteConstant.WX_INTERFACE_ID);
            if (memberOutSite == null) {
                return INPUT;
            }
            MemberOutSiteDao memberOutSiteDao = memberOutSite.getDao();

            member = memberOutSiteDao.getByOutMemberId(memberOutSite.getOutMemberId());
            // 外部用户不存在
            if (member == null) {
                // sso 第三方微信登录
                member = outSiteMemberRegister(memberOutSite);
            } else if (member.getMemberState() == MemberConstants.USER_CANCEL_STATE) {
                setMsg("您的账号已被注销！");
                memberName = member.getMemberName();
                refreshPage = true;
                return inputCancelView();
            } else {
                updateHeadAndNick(weixinUser);
            }
            //缓存openId，在验证票据时返回，用于业务方与微信交互使用
            member.setOpenId(weixinUser.getOpenid());
            //获取请求二维码的原始客户端ip到登录日志
            member.setClientIp(clientIp);
            //处理登录来源
            if (StringUtil.isNotBlank(cacheResult) && cacheResult.equals("1")) {
                memberOutSite.setMemberFrom(UserOutsiteConstant.WX_QR_INTERFACE_ID);
            }
            MemberUtil.doLogin(member, memberOutSite);
            memberName = member.getMemberName();
            UserLoginSessionUtil.saveSession(memberName, UserLoginSessionUtil.LoginType.SSO_OUT.getType() + getInterfaceId(), null, site_id);

            weixinOpenIdUnionId.setUnionId(weixinUser.getUnionid());
            weixinOpenIdUnionId.setOpenId(weixinUser.getOpenid());
            weixinOpenIdUnionId.setOriginFrom(WeixinOpenIdUnionId.ORIGIN_FROM.WEIXIN_AUTH.name());
            saveIfNotExist(weixinOpenIdUnionId);

            initCss(getSite_id());
            if (StringUtil.isNotBlank(visitType) && "bind".equals(visitType)) {
                responseVisitType();
                return SUCCESS;
            }
            initLoginElement(super.getSite_id());
            if (isSingleAccount) {
                String toBind = goSingleBind(weixinUser.getNickname(), weixinUser.getHeadimgurl());
                if (!NO_RETURN.equalsIgnoreCase(toBind)) {
                    return toBind;
                }
                UserLoginSessionUtil.saveMasterSession(memberName, member.getMemberName(), UserLoginSessionUtil.LoginType.SSO_OUT.getType() + getInterfaceId(), null, site_id);
            }

            login(member);

            if (log.isInfoEnabled()) {
                log.info(URLDecoder.decode(getCallbackUrl(), "UTF-8"));
            }
            return WeixinConstant.MULTI_TERMINAL_LOGIN_JUMP.equals(cacheResult) ? qrSuccess() : SUCCESS;
        } catch (IOException e) {
            log.error("openid授权重定向出错:" + e);
            try {
                getResponse().sendRedirect(callbackUrl);
            } catch (IOException e1) {
                log.error("跳转[{}]异常", callbackUrl, e);
            }
        }
        return INPUT;

    }

	@Override
	protected String getOutOauthLogName() {
		return "微信";
	}

	@Override
    protected String getUserKeyPrefix() {
        return WeixinConstant.QRSCENE_OAURH_PRE;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getCode() {
        return this.code;
    }

    public WeixinRemoteCall getWeixinRemoteCall() {
        return weixinRemoteCall;
    }

    public void setWeixinRemoteCall(WeixinRemoteCall weixinRemoteCall) {
        this.weixinRemoteCall = weixinRemoteCall;
    }

    public String getWeixinWebCallbackUrl() {
        return weixinWebCallbackUrl;
    }

    public void setWeixinWebCallbackUrl(String weixinWebCallbackUrl) {
        this.weixinWebCallbackUrl = weixinWebCallbackUrl;
    }

}