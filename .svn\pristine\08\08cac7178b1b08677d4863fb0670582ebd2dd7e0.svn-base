package com.shunwang.basepassport.binder.common;

/**
 * Created by bd.fang on 2017/3/23.
 */
public enum ErrorCode {
    C_1000("1000", "失败，系统异常"),
    C_1001("1001","参数不能为空"),
    C_1002("1002","签名错误"),
    C_1003("1003", "请求参数time超时"),
    C_1004("1004","站点id不存在"),
    C_1005("1005", "IP地址不合法"),
    C_1006("1006", "用户账号不存在"),
    C_1008("1008", "新手机或邮箱格式不正确"),
    C_1009("1009", "查询时间间隔太长"),
    C_1010("1010", "手机或邮箱格式不正确"),
    C_1011("1011","上传的文件太大"),
    C_1014("1014","抱歉，接口已停止服务。"),
    C_1019("1019", "该账号没有绑定手机或邮箱"),
    C_1020("1020", "您绑定的手机或邮箱不正确"),
    C_1021("1021", "同一业务同一邮箱或手机1分钟内只能发送一次！"),
    C_1022("1022", "抱歉，此邮件或手机验证码已过期，请重新申请！"),
    C_1023("1023", "缓存异常"),
    C_1024("1024", "验证码不正确") ,
    C_1025("1025", "该手机号已被其他账户设为登录手机号"),
    C_1026("1026", "该邮箱已被其他账户设为登录邮箱"),
    C_1027("1027", "同一业务同一账号1天只能发送5次！"),
    C_1028("1028", "需设为登录账号的手机号与通行证账号不匹配！"),
    C_1029("1029", "需设为登录账号的邮箱与通行证账号不匹配！"),
    C_1033("1033", "验证码不存在或已过期") ,
    C_1051("1051", "您已经绑定了手机或邮箱"),
    C_1052("1052", "抱歉，您今天领取密保卡已经超过每天最多领取3张的限制啦，请明天再试吧！"),
    C_1055("1055", "您已经绑定了该手机或邮箱"),
    C_1056("1056", "手机号已达到绑定上限"),
    C_1066("1066", "手机号注册异常"),
    C_1067("1067", "用户登录问题，请联系客服！"),
    C_1068("1068", "您的账号已被注销！"),
    C_1069("1069", "需要附加其他登录信息"),
    C_1073("1073", "手机号对应的主账号id与用户表不一致"),
    C_1074("1074", "账户类型不正确"),
    C_1075("1075", "业务账户类型不匹配") ,
    C_1076("1076", "该账号未关联手机号码"),
    C_1077("1077", "该手机号已经关联其他"),
    C_1078("1078", "该手机号已经关联其他微信"),

    C_1080("1080", "不同业务账户类型不支持免登"),
    C_1081("1081", "singleBindToken不存在或已失效"),
    C_1082("1082", "您已经绑定了其他手机"),
    C_1083("1083", "您已经设置了其他手机作为登录账号"),
    C_1084("1084", "账号解绑手机登录失败"),
    C_1085("1085", "账号已被其他账号绑定"),
    C_1086("1086", "支付宝授权异常"),
    C_1087("1087", "支付宝获取用户信息异常"),
    C_1088("1088", "该用户不存在单账号关联"),
    C_1089("1089", "绑定的unionId不是第三方账号"),
    C_1090("1090", "第三方登录账号不允许解绑"),
    C_1091("1091", "该账号已经绑定其他手机号"),
    C_1092("1092", "微信授权相关异常"),
    C_1093("1093", "idToken验证异常"),
    C_1094("1094", "通行证账号与ID不匹配"),
    C_1095("1095", "一键登录获取手机失败"),
    C_1096("1096","业务配置不存在"),
    C_1097("1097","业务数据处理异常"),
    C_1098("1098","业务数据已失效"),
    C_1100("1100", "同一通行证账号每日调用接口验证次数不超过3次"),
    C_1101("1101", "同一身份证号每日调用接口验证次数不超过3次"),
    C_1102("1102","提交太频繁，请1分钟后再提交"),
    C_1103("1103","提交太频繁，请%d分钟后再提交"),
    C_1104("1104", "微信接口请求异常"),
    C_1200("1200", "文件已经存在"),
    C_1201("1201", "文件上传异常"),
    C_1202("1202", "文件不存在"),
    C_1203("1203", "该scene已经失效"),
    C_1204("1204", "该账号已被禁用"),
    C_1205("1205", "该账号已注销"),
    C_1206("1206", "今日短信验证码获取次数已达上限"),
    C_1207("1207", "账户注册异常"),
    C_1208("1208", "游戏实名异常"),
    C_1209("1209", "已存在一条处理中的实名认证记录请等待处理结果"),
    C_1210("1210", "同一身份证号请求太频繁"),
    C_1211("1211", "内部注册接口调用异常"),
    C_1212("1212", "时间跨度过大"),
    C_1213("1213", "登录信息不存在，免登失败"),
    ;

    private final String code;
    private final String description;

    ErrorCode(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
