<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="/common/taglibs.jsp" %>
<%@ page import="com.shunwang.basepassport.user.common.UserCheckUtil" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <script type="text/javascript">
        var actionError = "${actionErrors}";
    </script>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>顺网通行证-实名认证</title>
    <link rel="stylesheet" href="${staticServer}/scripts/common/calendar/calendar.css" type="text/css" />
    <style>
        .form_group td a {
            cursor: pointer;
        }
        .fancybox-img {
            display: inline-block;
            width: 125px;
            height: 90px;
            line-height: 90px;
            overflow: hidden;
            vertical-align: middle;
            cursor:pointer;
        }

        .fancybox-img img {
            max-width: 100%;
            max-height: 100%;
            vertical-align: middle;
        }

        .fancybox-wrap {
            display: none;
            position: fixed;
            z-index: 1050;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
        }

        .fancybox-wrap .mask {
            position: fixed;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            z-index: 1040;
            background-color: rgba(0, 0, 0, .5);
        }

        .fancybox-wrap .fancybox {
            position: fixed;
            z-index: 1050;
            width: 500px;
            height: 500px;
            top: 50%;
            right: 0;
            bottom: 0;
            left: 50%;
            margin-left: -250px;
            margin-top: -250px;
            text-align: center;
            background-color: #f5f5f5;
            border: 2px solid #ed7716;
        }
        .fancybox-bd {
            width: 500px;
            height: 500px;
            line-height: 500px;
        }
        .fancybox-wrap .fancybox img {
            max-width: 100%;
            max-height: 100%;
            vertical-align: middle;
        }

        .fancybox-wrap .close {
            display: block;
            position: absolute;
            top: -2px;
            right: -30px;
            width: 28px;
            height: 24px;
            font-size: 28px;
            color: #fff;
            text-align: center;
            background: #403b3b;
            text-decoration: none;
            cursor: pointer;
        }

        .step_bar li {
            width: 120px;
        }
    </style>

    <script type="text/javascript" src="${staticServer}/scripts/common/StringUtil.js"></script>
    <script type="text/javascript" src="${staticServer}/scripts/common/lockDiv.js"></script>
    <script type="text/javascript" src="${staticServer}/scripts/common/jquery.jSelectDate.js"></script>
    <script type="text/javascript" src="${staticServer}/scripts/front/common/commonCheck.js"></script>
    <script type="text/javascript" src="${staticServer}/scripts/front/actu/CafeActuInfo_cafeInfo_front.js"></script>
    <script type="text/javascript" src="${staticServer}/scripts/common/ajaxupload.js"></script>

</head>
<body>
<div class="shop_head">
    <strong>网吧实名认证</strong> 如需在顺网结算中心申请提现，请先通过实名认证。
</div>
<ul class="step_bar step_bar_4">
    <li><em>1</em> 认证手机号码</li>
    <li class="current"><em>2</em> 填写基本信息</li>
    <li><em>3</em> 上传基本资料</li>
    <li><em>4</em> 确认信息</li>
    <li><em>5</em> 申请完成</li>
</ul>
<form id="cafeOwnerActuInfoForm" action="${appServer }/front/actuality/confirmCafeActuInfo_front.htm"
      method="post">
    <input type="hidden" value="${tokenId}" name="tokenId" id="tokenId" />
    <div class="form_group">
        <table cellpadding="0" cellspacing="0">
            <tbody>
            <tr>
                <th class="w_md_eq"><span class="not_null">*</span>网吧（公司）名称：</th>
                <td>
                    <input name="cafeActuInfo.cafeActuInfoWrapper.compName" type="text" style="width:220px;"
                           class="form_input" id="compName" tabindex="2" value="${ cafeActuInfo.cafeActuInfoWrapper.compName}"
                           maxlength="128"/>
                    <span id="compNameErrorAlert" class="form_error"></span>
                    <div class="form_lh">
                        <span class="form_tip" id="compNameAlert" style="color:#F47607"></span>
                    </div>
                    <input name="flag" value="3" type="hidden"/>
                </td>
            </tr>
            <tr>
                <th class="w_md_eq"><span class="not_null">*</span>申请人姓名：</th>
                <td>
                    <input name="cafeActuInfo.cafeActuInfoWrapper.linkUser" type="text" style="width:220px;"
                           class="form_input" id="linkUser" tabindex="2" value="${ cafeActuInfo.cafeActuInfoWrapper.linkUser}"
                           maxlength="128"/>
                    <span id="linkUserError" class="form_error"></span>
                    <div class="form_lh">
                        <p class="form_tip" id="linkUserAlert">
                            <a href="http://bbs.sicent.com/forum.php?mod=viewthread&tid=1968" target="_blank">
                                <i class="wh"></i>申请人可以不是法人代表或网吧老板吗？
                            </a></p>
                    </div>
                </td>
            </tr>
            <tr>
                <th><span class="not_null">*</span>申请人身份证号：</th>
                <td>
                    <input name="cafeActuInfo.cafeActuInfoWrapper.idCardNo" type="text" style="width:220px;"
                           class="form_input" id="idCardNo" tabindex="2"
                           value="${ cafeActuInfo.cafeActuInfoWrapper.idCardNo}" maxlength="18"/>
                    <span id="idCardNoErrorAlert" class="form_error"></span>
                    <div class="form_lh">
                        <span class="form_tip">申请人的18位身份证号码</span>
                    </div>
                </td>
            </tr>
            <tr>
                <th><span class="not_null">*</span>统一社会信用代码：</th>
                <td>
                    <input name="cafeActuInfo.cafeActuInfoWrapper.cafeLicenceNo" type="text" style="width:220px;"
                           class="form_input" id="cafeLicenceNo" tabindex="2"
                           value="${ cafeActuInfo.cafeActuInfoWrapper.cafeLicenceNo}" maxlength="50"/>
                    <span id="cafeLicenceNoErrorAlert" class="form_error"></span>
                    <div class="form_lh">
                        <span class="form_tip">请填写营业执照上的统一社会信用代码或营业执照注册号</span>
                    </div>
                </td>
            </tr>
            <tr>
                <th><span class="not_null">*</span>法人姓名：</th>
                <td>
                    <input name="cafeActuInfo.cafeActuInfoWrapper.legalPersonName" type="text" style="width:220px;"
                           class="form_input" id="legalPersonName" tabindex="2"
                           value="${ cafeActuInfo.cafeActuInfoWrapper.legalPersonName}" maxlength="50"/>
                    <span id="legalPersonNameErrorAlert" class="form_error"></span>
                    <div class="form_lh">
                        <span class="form_tip">请填写营业执照上的法人姓名</span>
                    </div>
                </td>
            </tr>
            <tr>
                <th><span class="not_null">*</span>网吧地址：</th>
                <td>
                    <select name="prov" class="form_select" s-data="${cafeActuInfo.cafeActuInfoWrapper.provArea.areaId}">
                        <option value="-1">请选择</option>
                        <c:forEach var="item" items="${provList}">
                            <option value="${item.areaId}">${item.name}</option>
                        </c:forEach>
                    </select>
                    <select name="city" class="form_select" s-data="${cafeActuInfo.cafeActuInfoWrapper.cityArea.areaId}">
                        <option value="-1" selected="selected">请选择</option>
                    </select>
                    <select name="cafeActuInfo.cafeActuInfoWrapper.areaId" class="form_select" s-data="${cafeActuInfo.cafeActuInfoWrapper.distArea.areaId}">
                        <option value="-1" selected="selected">请选择</option>
                    </select>
                    <span id="cafeAddrAlert" class="form_error"></span>
                    <div class="form_lh">
                        <textarea name="cafeActuInfo.cafeActuInfoWrapper.cafeAddr" rows="" cols="" class="form_textarea">${cafeActuInfo.cafeActuInfoWrapper.cafeAddr}</textarea>
                        <br/><span class="form_tip">通过网吧地址寄送礼品或资料，最多可输入128个字符，请勿输入特殊字符</span>
                    </div>
                </td>
            </tr>
            </tbody>
            <tbody>
            <tr>
                <th></th>
                <td>
                    <div id="commonErrorAlert" style="color: red;font-size:12px;">${errorMsg }</div>
                    <a href="#" class="btn_default_lg" onclick="return goConfirmPage()" style="margin:0">下一步</a>
                </td>
            </tr>
            </tbody>
        </table>

    </div>
</form>
<script type="text/javascript">
    $('.sidebar .menu li:first').addClass('current');
    //var validPhoneRex = <%= UserCheckUtil.getValidPhoneNum()%>;
</script>

<div id="zoneDiv_20110829165708"></div>

<script src="${staticServer}/scripts/common/bootstrap-modal.js"></script>
<script type="text/javascript">
    function showInfo(showInfo, msg) {
        if (msg == "") {
            $("#" + showInfo).html("");
            return;
        }

        $("#" + showInfo).css("color", "red");
        $("#" + showInfo).removeClass("form_tip");
        $("#" + showInfo).addClass("col_red");
        $("#" + showInfo).html("<img src='${staticServer}/images/front/error.gif'/>" + msg);
    }
</script>
</body>
</html>
