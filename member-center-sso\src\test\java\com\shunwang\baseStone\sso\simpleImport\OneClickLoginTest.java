package com.shunwang.baseStone.sso.simpleImport;

import com.shunwang.basepassport.manager.util.HmacSHAUtil;
import com.shunwang.basepassport.manager.util.SignTool;
import com.shunwang.util.date.DateUtil;
import com.shunwang.util.encrypt.Md5Encrypt;
import com.shunwang.util.json.GsonUtil;
import com.shunwang.util.net.HttpClientUtils;
import org.junit.Test;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.Charset;
import java.util.*;

/**
 * User:lj.zeng
 * Date:2021/01/05
 * Time:16:24
 */
public class OneClickLoginTest {
	String siteId = "sw_pay" ;
	String md5Key = "123456" ;
	private final Map<String, String> header = new HashMap<>();
	{
		header.put("Accept","application/json");
	}
	@Test
	public void Test(){
		String timestamp = DateUtil.getCurrentDateStamp();
		Map<String, String> params1 = new HashMap<>();
		params1.put("siteId", siteId);
		params1.put("time", timestamp);
		params1.put("terminalType", "1");
		params1.put("supportChannel", "1");
		params1.put("signVersion","1.0");
		params1.put("sign", getSign(params1));

		String respond = HttpClientUtils.doPost("http://sso.kedou.com/createSceneCode.do", params1, header,null, Charset.defaultCharset());
		System.out.println(respond);

		Map<String, String> result = GsonUtil.fromJson(respond, HashMap.class);

		String token = "CM__1__aaefd4bc14eb492de7feaac3b4ab469d__2.3.5.1__1__STsid00000016097449577432PdnwQfXUbycjiwwKBcaurY8TKpYXqnW__NOTCUCC" ;
		timestamp = DateUtil.getCurrentDateStamp() ;
		String processId = "5286413d805a40d285a0125a7bce94b0";
		String authcode = "0000";
		Map<String, String> param = new HashMap<>();
		param.put("token", token);
		param.put("siteId", siteId);
		param.put("time", timestamp);
		param.put("processId", processId);
		param.put("authcode", authcode);
		param.put("sceneCode", result.get("sceneCode"));

		String source = SignTool.buildSignStringSortedMap(param,"sign", md5Key);
		String sign = Md5Encrypt.encrypt(source).toUpperCase() ;

		param.put("sign", sign);
//		respond = HttpClientUtils.doPost("http://sso.kedou.com/createByOneClickLogin.do", param);
		respond = HttpClientUtils.doPost("http://sso.kedou.com/createByOneClickLogin.do", param, header,null, Charset.defaultCharset());


		System.out.println(respond);
	}

	@Test
	public void testGeetestApi() {
		String respond = "";
		HttpURLConnection conn = null;

		try {

			String userId = "" +
					"CM__1__aaefd4bc14eb492de7feaac3b4ab469d__2.3.5.1__1__STsid00000016097449577432PdnwQfXUbycjiwwKBcaurY8TKpYXqnW__NOTCUCC" +
					"" ;
			String timestamp = Long.toString(System.currentTimeMillis());
			String processId = "5286413d805a40d285a0125a7bce94b0";
			String authcode = "0000";
			String appId = "aaefd4bc14eb492de7feaac3b4ab469d" ;
			String md5Key = "d7c1bf56ec483ea9aa8bd5bff06590e9" ;
			Map<String, Object> params = new HashMap<>();
			params.put("process_id", processId);
			params.put("token", userId);
			params.put("is_phone_encode", false);
			params.put("timestamp", timestamp);
			params.put("authcode", authcode);
			String plainText = appId+"&&"+timestamp ;
			String sign = HmacSHAUtil.sha256(plainText, md5Key);

			params.put("sign", sign);

			URL url = new URL("https://onelogin.geetest.com/check_phone");
			conn = (HttpURLConnection) url.openConnection();
			conn.setDoOutput(true);
			conn.setUseCaches(false);
			conn.setRequestMethod("POST");
			conn.setRequestProperty("Content-Type", "application/json");
			conn.getOutputStream().write((GsonUtil.toJson(params)).getBytes(Charset.defaultCharset()));
			conn.connect();

			BufferedReader reader = new BufferedReader(new InputStreamReader(conn.getInputStream(), Charset.defaultCharset()));
			String lines;

			while ((lines = reader.readLine()) != null) {
				respond += lines;
			}

			reader.close();
			conn.disconnect();
		} catch (Exception e) {
			//e.printStackTrace();
			respond = e.getMessage();
		} finally {
			if (conn != null) {
				conn.disconnect();
			}
		}

		System.out.println(respond);
	}

	@Test
	public void test2() {
		Map<String, String> header = new HashMap<>();
		header.put("Accept","application/x-www-form-urlencoded");

		String token = "CM__1__aaefd4bc14eb492de7feaac3b4ab469d__2.3.5.1__1__STsid00000016097449577432PdnwQfXUbycjiwwKBcaurY8TKpYXqnW__NOTCUCC" ;
		String timestamp = DateUtil.getCurrentDateStamp() ;
		String processId = "5286413d805a40d285a0125a7bce94b0";
		String authcode = "0000";
		Map<String, String> param = new HashMap<>();
		param.put("token", token);
		param.put("siteId", siteId);
		param.put("time", timestamp);
		param.put("processId", processId);
		param.put("authcode", authcode);

		String source = SignTool.buildSignStringSortedMap(param,"sign", md5Key);
		String sign = Md5Encrypt.encrypt(source).toUpperCase() ;

		param.put("sign", sign);
//		respond = HttpClientUtils.doPost("http://sso.kedou.com/createByOneClickLogin.do", param);
		String respond = HttpClientUtils.doPost("http://sso.kedou.com/createByH5OneClickLogin.do", param, header,null, Charset.defaultCharset());


		System.out.println(respond);
	}

	@Test
	public void testYiDunOneClickLogin() {
		String token = "token" ;
		String accessToken = "accessToken" ;
		String timestamp = DateUtil.getCurrentDateStamp() ;
		Map<String, String> param = new HashMap<>();
		param.put("token", token);
		param.put("accessToken", accessToken);
		param.put("siteId", siteId);
		param.put("time", timestamp);
		String respond = HttpClientUtils.doPost("http://sso.kedou.com/createByYiDunH5OneClickLogin.do", param);
		System.out.println(respond);
	}

	protected String getSign(Map<String,String> params){
		List<String> keys = new ArrayList<>(params.keySet());
		Collections.sort(keys);
		StringBuilder plainText = new StringBuilder();
		for (String key : keys) {
			if(key.equals("sign") || key.startsWith("t_")){
				//略过sign和以t_开头的参数
				continue;
			}
			plainText.append(params.get(key)).append("|");
		}
		plainText.append(md5Key);
		System.out.println(plainText.toString());
		return Md5Encrypt.encrypt(plainText.toString()).toUpperCase() ;
	}
}
