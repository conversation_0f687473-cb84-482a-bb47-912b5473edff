package com.shunwang.basepassport.util;

import com.shunwang.baseStone.context.BaseStoneContext;
import com.shunwang.basepassport.config.dao.WeakPasswordDao;
import com.shunwang.basepassport.config.pojo.WeakPasswordDO;

/**
 * <AUTHOR>
 * @Date 2022/6/27 13:41
 */
public class WeakPasswordUtil {
    public static Boolean weakPasswordCheck(String password) {
        BaseStoneContext context = BaseStoneContext.getInstance();
        WeakPasswordDao weakPasswordDao = (WeakPasswordDao) context.getBean("weakPasswordDao");
        WeakPasswordDO weakPassword = weakPasswordDao.findByPwdMd5(password);
        if (weakPassword != null) {
            return true;
        }
        return false;
    }
}
