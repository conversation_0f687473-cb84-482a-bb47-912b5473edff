var baseServer="http://base.kedou.com";
var zone_div_context=''+   
'<!---地区弹出层--->\n'
+'	<style type="text/css">'
+'	.wz_cityhint{ width:450px; position:absolute; background-color:#fff; border:1px solid #C4C4C4; position:relative; overflow:hidden;}\n'
+'	.wz_cityhint .wz_close,.wz_cityhint_sub .wz_close{ position:absolute; right:5px; top:3px;}\n'
+'	.wz_cityhint dl{ line-height:180%;clear:both;}\n'
+'	.fn-clear dt{ width:70px; font-weight:bold; text-align:right; float:left; color:#666;padding:1px 0;}\n'
+'	.fn-clear dt,.fn-clear dd{ height:25px;line-height:25px;display:block; }\n'
+'	.fn-clear dd{ float:left;display:inline; width:350px; margin-left:5px;\n}'
+'	.fn-clear dd a,.cityhint_subC a{color:#666; text-decoration:none; padding:1px 5px; white-space:nowrap;float:left;}\n'
+'	.fn-clear dd a:hover,.cityhint_subC a:hover{color:#666;text-decoration:none;background:#FFCC00;padding:1px 5px;white-space:nowrap;}\n'
+'	.wz_cityhint_sub{ background:#daf3ff; border:1px solid #ccc; line-height:180%; padding:10px; width:252px; height:auto; position:absolute;}\n'
+'	.z_mar10{ margin:5px;_margin:0px;overflow: hidden;}\n'
+'	</style>\n'
+'<div id="provinceDiv" class="wz_cityhint" style="display: none;position: absolute;z-index:200;"> \n'
+'		<iframe frameborder="0" id="provinceIframe" style="position:absolute;z-index:-1; width:450px;height:expression(this.nextSibling.offsetHeight);top:expression(this.nextSibling.offsetTop);left:expression(this.nextSibling.offsetLeft);left: 0px;" ></iframe>\n'
+'		<div id ="provinceInsertDiv" >\n'
+'		<span class="wz_close"><a href="javascript:void(0);" onclick="closeZone();"><img width="20" height="14" border="0"  src="'+baseServer+'/images/ico_27.gif" border="0" /></a></span>\n'
+'		<div id="provinceUl" class="z_mar10">\n'
+' 		</div>\n'
+'	</div>\n'
+'</div>\n'
+'<div id="cityDiv" class="wz_cityhint_sub" style="display: none;position: absolute;width: 300px;z-index:300;">\n'
+'<span class="wz_close"><a href="javascript:void(0);" onclick="$(\'#cityDiv\').hide();"><img width="20" height="14" border="0" src="'+baseServer+'/images/ico_27.gif" border="0" /></a></span>\n'
+'<div class="cityhint_subC" id="cityList" style="width: 290px;">\n'
+'</div>\n'
+'<script type="text/javascript" src="'+baseServer+'/js/common/zoneList.js"></script>\n'
+'<script type="text/javascript" src="'+baseServer+'/js/common/lockDiv.js"></script>\n'
+'<!-- 地区结束 -->';
var inputParam={};
var returnData={};
function showZone(id,event){
	event=event?event:window.event;
	if(inputParam.isNeedLockBack)
		lockDiv();
	var ul = $("#provinceUl");
	var provinceIframe=$("#provinceIframe");
	var provinceInsertDiv=$("#provinceInsertDiv");
	ul.html("");
	var str = "";
	for(var i=0; i < zoneList.length; i ++){
		var zone = zoneList[i];
		var width = zone.text + "";
		width = width.length * 20;
		str += ' <dl class="fn-clear"><dt>';
		str += zone.text;
		str += '</dt><dd>';
		var provs=getItemList(zoneList,zone.value);
		for(var j=0; j < provs.length; j ++){
			var prov = provs[j];
			var width = prov.text + "";
			width = width.length * 20;
			str += '<a href="javascript:void(0);" onclick="showCity(\''+id+'\',\''+zone.value+'\',\''+prov.text+'\',\'' + prov.value + '\', event)">' + prov.text + '</a>';
		}
		str += '</dd></dl>';
	}
	ul.append(str);
	var left = event.clientX - 100;
	if(left>275)
		left=275;
	var top = event.clientY - 140;
	$('#provinceDiv').show().css({'left':left + "px", 'top':top + "px"});
	provinceIframe.height(provinceInsertDiv.height()+20);
	$("#cityDiv").hide();
}

function showCity(id,areaId,name,value, event){
	var div = $("#cityList");
	div.html("");
	var str = '';
	var provs=getItemList(zoneList,areaId);
	var citys = getItemList(provs,value);
	for(var i=0; i < citys.length; i ++){
		var city = citys[i];
		var width = city.text + "";
		width = width.length * 20;
		str += '<a href="javascript:setZoneVal(\''+id+'\',\'' + name + '\',\'' + value+ '\',\''+ city.text + '\',\'' + city.value + '\');">' + city.text + '</a>';
	}
	div.append(str);

	var left = event.clientX - 160;
	if(left>420)
		left=420;
	var top = event.clientY + 10;
	$("#cityDiv").show().css({'left': left + "px", 'top':top + "px"});
}

function getItemList(list,value){
	for(var i=0; i < list.length; i ++){
		if(list[i].value == value)
			return list[i].items;
	}
}

function setZoneVal(id,pro_name,pro_value,city_name,city_value){
	returnData.proName=pro_name;
	returnData.proValue=pro_value;
	returnData.cityName=city_name;
	returnData.cityValue=city_value;
	closeZone();
	if(inputParam.callBackFun)
		inputParam.callBackFun.call(this,returnData);
}

function showPopTs(type, event){
	var ts = $("#pop_ts");
	if(type == "show"){
		ts.show().css({'position':'absolute','left':event.clientX - 10 + "px", 'top':event.clientY - 60 + "px"});
	}else{
		ts.hide();
	}
}
/**
 * 初始化地区 @YHJ
 * @param linkId 链接dom节点的id，用于绑定点击事件，触发地区控件的显示
 * @param isNeedLockBack 是否需要锁屏，true，锁屏，false，不需要 锁屏会导致除了地区控件以外所以的层用灰色笼罩，不可操作
 * @param callBackFun 回调函数的参数 是一个json格式  格式如下
 * {proName:"浙江省",proValue:"7",cityName:"杭州市",cityValue:"238"}
 * @return
 */
function initZone(linkId,isNeedLockBack,callBackFun){
	var linkDiv=$("#"+linkId);
	if(!linkDiv)
		return;
	linkDiv.bind("click",function(event){showZone(linkId,event);});
	getZoneDiv().html(zone_div_context);
	inputParam.isNeedLockBack=isNeedLockBack;
	inputParam.callBackFun=callBackFun;
}
function getZoneDiv(){
	var zoneDiv=document.getElementById("zoneDiv_20110829165708");
	if(!zoneDiv){
		$(document.body).append("<div id='zoneDiv_20110829165708'></div>");
	}
	zoneDiv=$("#zoneDiv_20110829165708");
	return zoneDiv;
}
function closeZone(){
	if(inputParam.isNeedLockBack)
		unlockDiv();
	$('#provinceDiv').hide();
	$('#cityDiv').hide();
}