package com.shunwang.basepassport.user.service;

import com.shunwang.basepassport.manager.request.geetest.CheckMobileRequest;
import com.shunwang.basepassport.manager.response.geetest.CheckMobileResponse;
import com.shunwang.basepassport.manager.service.geetest.CheckMobileServiceClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class GeeTestService {
    private static final Logger logger = LoggerFactory.getLogger(GeeTestService.class);
    /**
     *
     * @param token
     * @param processId
     * @param authcode
     * @return
     */
    public static CheckMobileResponse checkMobile(String token, String processId, String authcode, String sceneCode) {
        CheckMobileRequest request = new CheckMobileRequest();
        request.setAuthcode(authcode);
        request.setToken(token);
        request.setProcessId(processId);

        return CheckMobileServiceClient.execute(request, sceneCode);
    }

}
