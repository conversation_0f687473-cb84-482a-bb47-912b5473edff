package com.shunwang.baseStone.sso.weixin.oauth.service;

import com.google.gson.*;
import com.shunwang.baseStone.cache.CacheKeyConstant;
import com.shunwang.baseStone.context.RedisContext;
import com.shunwang.baseStone.context.RedisResourceOperation;
import com.shunwang.baseStone.sso.exception.WeixinServersideException;
import com.shunwang.baseStone.sso.weixin.oauth.util.WXBizMsgCrypt;
import com.shunwang.baseStone.sso.weixin.oauth.util.WeixinClientUtil;
import com.shunwang.baseStone.sso.weixin.oauth.util.XmlUtils;
import com.shunwang.baseStone.sso.weixin.pojo.*;
import com.shunwang.basepassport.config.dao.ConfigInterfaceDao;
import com.shunwang.basepassport.config.enums.AppidReplyEnum;
import com.shunwang.basepassport.config.pojo.ConfigInterface;
import com.shunwang.basepassport.manager.InterfaceConstant;
import com.shunwang.basepassport.rpc.WxCardMsgRequest;
import com.shunwang.basepassport.rpc.WxCardMsgResponse;
import com.shunwang.basepassport.rpc.WxCardMsgService;
import com.shunwang.util.IO.XmlUtil;
import com.shunwang.util.StringUtil;
import com.shunwang.util.json.GsonUtil;
import com.shunwang.util.math.RandomUtil;
import com.shunwang.util.net.HttpClientUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.*;

/**
 *
 * 微信开放平台的service
 * User:pf.ma
 * Date:2019/12/30
 * Time:9:58
 */
public class WeixinOpenService {

	private Logger logger = LoggerFactory.getLogger(WeixinOpenService.class) ;

	private ConfigInterfaceDao configInterfaceDao ;

	// 解码verifyTicket
	public WeixinComponentTicket decryptVerifyTicket(String encrypt){
		ConfigInterface configInterface = configInterfaceDao.findByInterfaceKey(InterfaceConstant.INTERFACE_KEY_WEIXIN_MSG_CRYPT) ;
		try {
			WXBizMsgCrypt pc = new WXBizMsgCrypt(configInterface.getInterfaceRemark(), configInterface.getInterfaceMd5Key(), configInterface.getInterfacePartnerId());
			String result = pc.decryptMsg(encrypt);
			logger.info("componentVerifyTicket解密结果为:{}", result) ;
			return new WeixinComponentTicket(result) ;//result 为XML
		}catch (Exception e){
			logger.error("verifyTicket解密异常", e) ;
			return null ;
		}
	}

	// 解码verifyTicket
	public WeixinOauthInfo decryptOauthCallback(String encrypt){
		ConfigInterface configInterface = configInterfaceDao.findByInterfaceKey(InterfaceConstant.INTERFACE_KEY_WEIXIN_MSG_CRYPT) ;
		try {
			WXBizMsgCrypt pc = new WXBizMsgCrypt(configInterface.getInterfaceRemark(), configInterface.getInterfaceMd5Key(), configInterface.getInterfacePartnerId());
			String result = pc.decryptMsg(encrypt);
			logger.info("授权回调解密结果为:{}", result) ;
			return new WeixinOauthInfo(result);//result 为XML
		}catch (Exception e){
			logger.error("verifyTicket解密异常", e) ;
			return null ;
		}
	}

    /**
     * https://developers.weixin.qq.com/doc/oplatform/Third-party_Platforms/api/component_access_token.html
     * 令牌（component_access_token）是第三方平台接口的调用凭据。令牌的获取是有限制的，每个令牌的有效期为 2 小时，请自行做好令牌的管理，在令牌快过期时（比如1小时50分），重新调用接口获取。
     * 如未特殊说明，令牌一般作为被调用接口的 GET 参数 component_access_token 的值使用
     * 请求数据 { "component_appid":  "appid_value" , "component_appsecret":  "appsecret_value", "component_verify_ticket": "ticket_value" }
     * 返回数据 { "component_access_token": "61W3mEpU66027wgNZ_MhGHNQDHnFATkDa9-2llqrMBjUwxRSNPbVsMmyD-yq8wZETSoE5NQgecigDrSHkPtIYA", "expires_in": 7200 }
     * @param verifyTicket
     * @return
     */
	public WeixinComponentToken getComponentAccessToken(String verifyTicket){
		ConfigInterface configInterface = configInterfaceDao.findByInterfaceKey(InterfaceConstant.INTERFACE_KEY_WEIXIN_GET_VERIFY_TOKEN) ;
		String url = configInterface.getInterfaceUrl1() ;
		Map<String,String> map = new HashMap<>() ;
		map.put("component_appid", configInterface.getInterfacePartnerId());
		map.put("component_appsecret", configInterface.getInterfaceMd5Key());
		map.put("component_verify_ticket", verifyTicket);
		byte[] bytes = HttpClientUtils.doPost(url, GsonUtil.toJson(map));
		String result =new String(bytes) ;
		logger.info("componentAccessToken响应:{}", result) ;
		return WeixinClientUtil.GSON.fromJson(result, WeixinComponentToken.class) ;//result 为json
	}

    /**
     * https://developers.weixin.qq.com/doc/oplatform/Third-party_Platforms/api/pre_auth_code.html
     * 预授权码（pre_auth_code）是第三方平台方实现授权托管的必备信息，每个预授权码有效期为 10 分钟。需要先获取令牌才能调用
     * 请求数据  {"component_appid": "appid_value"}
     * 返回数据  {"pre_auth_code": "Cx_Dk6qiBE0Dmx4EmlT3oRfArPvwSQ-oa3NL_fwHM7VI08r52wazoZX2Rhpz1dEw", "expires_in": 600}
     * @param verifyToken
     * @return
     */
	public String getPreAuthCode(String verifyToken) {
		ConfigInterface configInterface = configInterfaceDao.findByInterfaceKey(InterfaceConstant.INTERFACE_KEY_WEIXIN_GET_PRE_AUTH) ;
		String url = "https://api.weixin.qq.com/cgi-bin/component/api_create_preauthcode?component_access_token=" + verifyToken;

		Map<String,String> map = new HashMap<>() ;
		map.put("component_appid", configInterface.getInterfacePartnerId()) ;
		byte[] bytes = HttpClientUtils.doPost(url, GsonUtil.toJson(map));
		String result = new String(bytes) ;
		JsonObject jsonObject = JsonParser.parseString(result).getAsJsonObject();
		String preAuthCode = jsonObject.get("pre_auth_code").getAsString() ;
		logger.info("preAuthCode:{}",preAuthCode) ;
		return preAuthCode ;
	}

	public String getPreAuthUrl(String preAuthCode, String siteId, int type) throws UnsupportedEncodingException {
		ConfigInterface configInterface = configInterfaceDao.findByInterfaceKey(InterfaceConstant.INTERFACE_KEY_WEIXIN_GET_PRE_URL) ;
		String url = configInterface.getInterfaceUrl1() ;
		String callbackUrl = configInterface.getInterfaceReturnUrl1() ;
		StringBuilder callbackUrlSb = new StringBuilder(callbackUrl) ;
		if(callbackUrl.indexOf("?") >= 0){
			callbackUrlSb.append("&") ;
		}else{
			callbackUrlSb.append("?") ;
		}
		callbackUrlSb.append("site_id=").append(siteId) ;
		callbackUrlSb.append("&authType=").append(type);

		StringBuilder stringBuilder = new StringBuilder() ;
		stringBuilder.append(url).append("?")
				.append("component_appid=").append(configInterface.getInterfacePartnerId())
				.append("&pre_auth_code=").append(preAuthCode)
				.append("&redirect_uri=").append(URLEncoder.encode(callbackUrlSb.toString(), "UTF-8")) ;
		return stringBuilder.toString() ;
	}

	// 获取接入方AccessToken
	public WeixinAuthorizerToken getAuthorizerAccessToken(String authcode, String componentAccessToken) throws UnsupportedEncodingException {
		ConfigInterface configInterface = configInterfaceDao.findByInterfaceKey(InterfaceConstant.INTERFACE_KEY_WEIXIN_GET_AUTHORIZE_TOKEN) ;

		String url = configInterface.getInterfaceUrl1() + componentAccessToken ;
		Map<String,String> map = new HashMap<>() ;
		map.put("component_appid", configInterface.getInterfacePartnerId()) ;
		map.put("authorization_code", authcode) ;
		byte[] bytes = HttpClientUtils.doPost(url, new Gson().toJson(map));
		String result = new String(bytes, "UTF-8");
		logger.error("微信接入方authorizerAccessToken响应:{}", result) ;
        JsonObject json = JsonParser.parseString(result).getAsJsonObject();
        JsonObject jsonInfo = json.get("authorization_info").getAsJsonObject();
        String authorizer_appid = jsonInfo.get("authorizer_appid").getAsString();
        String authorizer_access_token = jsonInfo.get("authorizer_access_token").getAsString();
        Integer expires_in = jsonInfo.get("expires_in").getAsInt();
        String authorizer_refresh_token = jsonInfo.get("authorizer_refresh_token").getAsString();

		Date expireTime = DateUtils.addSeconds(new Date(), expires_in) ;
		WeixinAuthorizerToken weixinAuthorizerToken = new WeixinAuthorizerToken() ;
		weixinAuthorizerToken.setAuthorizerAppId(authorizer_appid) ;
		weixinAuthorizerToken.setAuthorizerAccessToken(authorizer_access_token);
		weixinAuthorizerToken.setAuthorizerRefreshToken(authorizer_refresh_token) ;
		weixinAuthorizerToken.setExpireTime(expireTime);
		return weixinAuthorizerToken ;
	}

	// 刷新接入方Token
	public WeixinAuthorizerToken freshAuthorizerAccessToken(String authorizerAppId, String componentAccessToken, String refreshCode) throws UnsupportedEncodingException {
		ConfigInterface configInterface = configInterfaceDao.findByInterfaceKey(InterfaceConstant.INTERFACE_KEY_WEIXIN_REFRESH_AUTHORIZE_TOKEN) ;

		String url = configInterface.getInterfaceUrl1() + componentAccessToken ;
		Map<String,String> map = new HashMap<>() ;
		map.put("component_appid", configInterface.getInterfacePartnerId());
		map.put("authorizer_appid", authorizerAppId);
		map.put("authorizer_refresh_token", refreshCode) ;

		byte[] bytes = HttpClientUtils.doPost(url, GsonUtil.toJson(map));
		String result = new String(bytes, "UTF-8") ;
		logger.info("刷新authorizerAccessToken响应:{}", result) ;
		return new WeixinAuthorizerToken(result) ;
	}

	// 生成二维码
	public WeixinQrcode createQrcode(String authorizerAccessToken, String cacheUserKey, Integer qrCodeExpireSeconds) throws IOException {
		ConfigInterface configInterface = configInterfaceDao.findByInterfaceKey(InterfaceConstant.INTERFACE_KEY_WEIXIN_CREATE_QRCODE) ;

		String url = configInterface.getInterfaceUrl1() + authorizerAccessToken ;
		// {"expire_seconds": 604800, "action_name": "QR_STR_SCENE", "action_info": {"scene": {"scene_str": "test"}}}
		Map<String,Object> map = new HashMap<>() ;
		map.put("expire_seconds", qrCodeExpireSeconds) ;
		map.put("action_name", "QR_STR_SCENE") ;
		Map<String,Object> actionInfo = new HashMap<>() ;
		Map<String,String> scene = new HashMap<>() ;
		scene.put("scene_str", cacheUserKey) ;
		actionInfo.put("scene", scene) ;
		map.put("action_info", actionInfo) ;
		byte[] bytes = HttpClientUtils.doPost(url, GsonUtil.toJson(map));
		if (bytes == null) {
			throw new IOException("微信创建二维码异常["+cacheUserKey+"]");
		}
		String result = new String(bytes, "UTF-8") ;
		logger.info("创建场景二维码响应:{}", result) ;
		return new WeixinQrcode(result) ;
	}

    /**
     * 生成小程序二维码
     * @param authorizerAccessToken
     * @param cacheUserKey
     * @param page
     * @return
     * @throws IOException
     */
    public WeixinQrcode createMiniQrcode(String authorizerAccessToken, String cacheUserKey, String page) throws IOException {
        ConfigInterface configInterface = configInterfaceDao.findByInterfaceKey(InterfaceConstant.INTERFACE_KEY_WEIXIN_MINI_CREATE_QRCODE) ;

        String url = configInterface.getInterfaceUrl1() + authorizerAccessToken ;
        Map<String,Object> map = new HashMap<>() ;
        map.put("scene", cacheUserKey) ;
        if (StringUtil.isNotBlank(page)) {
            map.put("page", page);//进入小程序后的页面，由后台配置，默认主页
        }
        map.put("width", 280) ;//设置小程序二维码大小，最小280,页面样式可控制大小
        map.put("auto_color", false);
        map.put("is_hyaline", true);//设置背景透明
        byte[] bytes = HttpClientUtils.doPost(url, new Gson().toJson(map));
        WeixinQrcode qrcode = new WeixinQrcode();
        try {
            if (bytes.length < 1000) {//图片流数组比较大超过1w，异常时数组较小一般在100内
                logger.error("获取小程序二维码失败[{}]", new String(bytes, "UTF-8"));
                return qrcode;
            }
            String result = Base64.getEncoder().encodeToString(bytes);
            qrcode.setQrcodeUrl("data:image/png;base64," + result);
            return qrcode;
        } catch (Exception e) {
            String result = new String(bytes, "UTF-8");
            logger.error("获取小程序二维码失败[{}]", result);
        }
        return qrcode ;
    }

	// 获取用户信息
	public WeixinUser queryWeixinUserInfo(String openId, String authorizerAccessToken) throws WeixinServersideException {
		ConfigInterface configInterface = configInterfaceDao.findByInterfaceKey(InterfaceConstant.INTERFACE_KEY_WEIXIN_GET_USER_INFO) ;
		String url = configInterface.getInterfaceUrl1() + authorizerAccessToken
				+ "&openid=" + openId;
		String result = HttpClientUtils.doGet(url, null) ;
		logger.info("获取用户信息响应:{}", result) ;
		JsonObject jsonObject = JsonParser.parseString(result).getAsJsonObject();
		Gson gson = new GsonBuilder().setFieldNamingPolicy(
				FieldNamingPolicy.LOWER_CASE_WITH_UNDERSCORES).create();

		if (jsonObject.has("errcode")) {
			WeixinErrorMsg msg = gson.fromJson(jsonObject, WeixinErrorMsg.class);
			throw new WeixinServersideException(msg);
		} else {
			WeixinUser weixinUser = gson.fromJson(jsonObject, WeixinUser.class);
			return weixinUser;
		}
	}

	/**
	 * 发送文本消息
	 */
	public void sendTextMsg(WeixinAuthorizerToken weixinAuthorizerToken, String openId, String content) throws WeixinServersideException {
		Map<String,Object> map = new LinkedHashMap<>() ;
		map.put("touser", openId) ;
		map.put("msgtype", "text") ;
		Map<String,String> contentMap = new HashMap<>() ;
		contentMap.put("content", content) ;
		map.put("text", contentMap) ;
		String json = new Gson().toJson(map) ;
		logger.info("消息内容为:{}",json) ;
		ConfigInterface configInterface = configInterfaceDao.findByInterfaceKey(InterfaceConstant.INTERFACE_KEY_WEIXIN_SEND_MESSAGE) ;
		String url = configInterface.getInterfaceUrl1() + weixinAuthorizerToken.getAuthorizerAccessToken() ;
		byte [] bytes = HttpClientUtils.doPost(url, json) ;
		String result = new String(bytes) ;
		logger.info("发送文本信息响应:{}", result) ;
		JsonParser jsonParser = new JsonParser();
		JsonObject jsonObject = (JsonObject)jsonParser.parse(result);
		Gson gson = new GsonBuilder().setFieldNamingPolicy(
				FieldNamingPolicy.LOWER_CASE_WITH_UNDERSCORES).create();

		if (jsonObject.has("errcode")) {
			WeixinErrorMsg msg = gson.fromJson(jsonObject, WeixinErrorMsg.class);
			throw new WeixinServersideException(msg);
		}
	}

	// 回复消息
	public String replyMsg(String reply, WeixinOauthInfo weixinOauthInfo, Integer contentType) throws Exception {
		ConfigInterface configInterface = configInterfaceDao.findByInterfaceKey(InterfaceConstant.INTERFACE_KEY_WEIXIN_MSG_CRYPT) ;

		String replyXml = null;
		Long timestamp = System.currentTimeMillis() ;
		if (contentType == AppidReplyEnum.ContentType.WORD.getValue()){//文链消息
			Map<String,String> map = new HashMap<>() ;
			map.put("ToUserName", weixinOauthInfo.getFromUserName()) ;
			map.put("FromUserName", weixinOauthInfo.getToUserName()) ;
			map.put("CreateTime", timestamp+"" ) ;
			map.put("MsgType", "text") ;
			map.put("Content", reply) ;
			replyXml = XmlUtil.toXml(map, "UTF-8", "UTF-8") ;//默认工具编码微信通知中文会乱码
		} else {//媒体消息（图片，音频等, 目前只有图片）
			Map<String,Object> map = new HashMap<>() ;
			map.put("ToUserName", weixinOauthInfo.getFromUserName()) ;
			map.put("FromUserName", weixinOauthInfo.getToUserName()) ;
			map.put("CreateTime", timestamp+"" ) ;
			map.put("MsgType", "image");
			Map<String, Object> image = new HashMap<>();
			image.put("MediaId", reply);
			map.put("Image", image) ;
			replyXml = XmlUtils.toXmlStr(map, "UTF-8", "UTF-8") ;
		}

		WXBizMsgCrypt pc = new WXBizMsgCrypt(configInterface.getInterfaceRemark(), configInterface.getInterfaceMd5Key(), configInterface.getInterfacePartnerId());
		String nonStr = RandomUtil.getRandomStr(16) ;
		String encryptMsg = pc.encryptMsg(replyXml, timestamp + "", nonStr);
		return encryptMsg ;
	}

	/**
	 * 发送卡片消息
	 * @param openId 当前微信用户的openid
	 * @param eventKey 当前场景值
	 * @param type 消息类型
	 */
	public boolean sendCardMsg(String openId, String eventKey, String type, String configGroup, String config, String extInfo) {
		Map<String, Integer> intMapConfig = RedisContext.getIntMapConfig(configGroup, config);
		Integer msgId = intMapConfig.get(type);
		return sendCardMsg(openId, eventKey ,msgId, extInfo);
	}

	/**
	 * 发送小程序卡片消息
	 * @param openId
	 * @param eventKey
	 * @param msgId
	 * @return
	 */
	public boolean sendCardMsg(String openId, String eventKey, Integer msgId, String extInfo){
		if (msgId == null) {
			return false;
		}
		try {
			WxCardMsgService wxCardMsgService = new WxCardMsgService();
			WxCardMsgRequest request = new WxCardMsgRequest();
			request.setOpenId(openId);
			request.setEventKey(eventKey);
			request.setMsgId(msgId);
			request.setExtInfo(extInfo);
			WxCardMsgResponse response = wxCardMsgService.execute(request);
			return response.isSuccess();
		} catch (Exception e) {
			logger.error("请求发送卡片消息异常[{}]", e.getMessage());
		}
		return false;
	}

	public ConfigInterfaceDao getConfigInterfaceDao() {
		return configInterfaceDao;
	}

	public void setConfigInterfaceDao(ConfigInterfaceDao configInterfaceDao) {
		this.configInterfaceDao = configInterfaceDao;
	}


}
