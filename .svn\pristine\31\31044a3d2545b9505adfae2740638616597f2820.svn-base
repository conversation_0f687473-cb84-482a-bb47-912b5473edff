package com.shunwang.basepassport.config.common;

import com.shunwang.baseStone.context.BaseStoneContext;
import com.shunwang.basepassport.config.dao.OauthDao;
import com.shunwang.basepassport.config.pojo.Oauth;

/**
 * @author: lj.zeng
 * @create: 2024-03-18 10:48:57
 * @Description:
 */
public class OauthUtil {

    private static OauthDao oauthDao;

    public static OauthDao getOauthDao() {
        if (oauthDao == null) {
            oauthDao = BaseStoneContext.getInstance().getBean(OauthDao.class);
        }
        return oauthDao;
    }

    public static Oauth loadOauth(String siteId, Integer type) {
        return getOauthDao().getBySiteAndType(siteId, type);
    }

    public static Oauth loadDefaultOauth(Integer type) {
        return getOauthDao().getDefaultByType(type);
    }
}
