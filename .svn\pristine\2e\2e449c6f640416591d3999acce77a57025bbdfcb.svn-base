package com.shunwang.basepassport.user.pojo;

import com.shunwang.baseStone.core.pojo.BaseStoneObject;

import java.io.Serializable;

/**
 * User:pf.ma
 * Date:2017/06/05
 * Time:17:11
 */
public class AuthorizedToken extends BaseStoneObject {

	private static final long serialVersionUID = 9006585321050566439L;
	private String memberId ;
	private String siteId ;
	private String authorizedToken ;

	public String getMemberId() {
		return memberId;
	}

	public void setMemberId(String memberId) {
		this.memberId = memberId;
	}

	public String getSiteId() {
		return siteId;
	}

	public void setSiteId(String siteId) {
		this.siteId = siteId;
	}

	public String getAuthorizedToken() {
		return authorizedToken;
	}

	public void setAuthorizedToken(String authorizedToken) {
		this.authorizedToken = authorizedToken;
	}

	@Override
	public Serializable getPk() {
		return null;
	}
}
