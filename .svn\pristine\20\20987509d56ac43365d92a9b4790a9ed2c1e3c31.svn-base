package com.shunwang.baseStone.cache.lock;

import com.shunwang.util.lang.StringUtil;

import java.time.Duration;

/**
 * 计数式缓存锁
 */
public class CounterLock extends TimedLock {
    private int maxValue;

    public CounterLock(String key, int maxCount, Duration duration) {
        super(key, duration);
        if (maxCount <= 0) {
            throw new IllegalArgumentException("maxCount 必须大于0");
        }
        this.maxValue = maxCount;
    }

    @Override
    public boolean tryLock() {
        String val = redisOperation.get(key);
        if (StringUtil.isEmpty(val)) {
            return super.tryLock();
        }
        int count = Integer.parseInt(val);
        if (count + 1 < maxValue) {
            return redisOperation.set(key, ++count, expire, timeUnit);
        }
        return false;
    }

    @Override
    public void unlock() {
        super.unlock();
    }
}
