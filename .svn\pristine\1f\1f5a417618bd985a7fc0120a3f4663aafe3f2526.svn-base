package com.shunwang.basepassport.binder.web.bind.processor;

import com.shunwang.baseStone.cache.lock.CounterLock;
import com.shunwang.baseStone.cache.service.CacheService;
import com.shunwang.baseStone.context.BaseStoneContext;
import com.shunwang.baseStone.context.IPContext;
import com.shunwang.baseStone.core.exception.BaseStoneException;
import com.shunwang.basepassport.binder.common.BinderConstants;
import com.shunwang.basepassport.binder.common.ErrorCode;
import com.shunwang.basepassport.binder.exception.ActiveNoErrorExp;
import com.shunwang.basepassport.binder.pojo.SendBinder;
import com.shunwang.basepassport.binder.processor.Processor;
import com.shunwang.basepassport.binder.processor.ProcessorContext;
import com.shunwang.basepassport.binder.response.PhoneLoginRespone;
import com.shunwang.basepassport.binder.web.send.bean.MobileBindBean;
import com.shunwang.basepassport.interc.SingleAccountBindService;
import com.shunwang.basepassport.key.common.SmsKeyUtil;
import com.shunwang.basepassport.manager.service.bo.ReportProcessor;
import com.shunwang.basepassport.user.common.MemberConstants;
import com.shunwang.basepassport.user.common.TicketUtil;
import com.shunwang.basepassport.user.common.UserLoginSessionUtil;
import com.shunwang.basepassport.user.pojo.Member;
import com.shunwang.basepassport.user.response.ValidateAgainResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Duration;

/**
 * 14: 登录
 *
 * <AUTHOR>
 * @date 2018/12/17
 **/
public class LoginProcessor implements Processor, ReportProcessor {

    protected static final Logger logger = LoggerFactory.getLogger(LoginProcessor.class);

    @Override
    public boolean matches(ProcessorContext context) {
        return BinderConstants.MOBLIE_LOGIN.equals(context.getSendBinder().getBusinessType());
    }

    @Override
    public boolean doProcess(ProcessorContext context) {

        SendBinder sendBinder = context.getSendBinder();
        MobileBindBean bean = (MobileBindBean) context.getT();

        CounterLock smsCheckLock = CacheService.newCounterLock("LOGIN_SMS_CODE_CONFIRM" + bean.getMobile(), 2, Duration.ofMinutes(1L));
        if (!smsCheckLock.tryLock()) {
            throw new BaseStoneException(ErrorCode.C_1102.getCode(), ErrorCode.C_1102.getDescription());
        }

        // 顺网云增加需求，根据终端类型验证短信验证码
        // 接口传终端类型参数需先验证短信记录中终端类型是否与接口传的类型一致
        if (SendBinder.checkNeedSpecialDeal(bean.getAccessSiteId())) {
            if (null == sendBinder.getTerminal() || null == bean.getTerminal() || !bean.getTerminal().equals(sendBinder.getTerminal())) {
                logger.error("验证码不同源验证错误，手机号{}", bean.getMobile());
                throw new ActiveNoErrorExp();
            }
        }

        sendBinder.validateAndUpdate(bean.getActiveNo());
        IPContext.setIp(bean.getLoginIp());
        Member member = bean.getMember();
        member.setLoginType(MemberConstants.LOGIN_TYPE_ACTIVE_NO);
        member.setEnv(bean.getEnvironment());
        member.setExtData(bean.getRemark());
        member.setAccessSiteId(bean.getAccessSiteId());
        member.setVrsion(bean.getVersion());
        member.loginWithNoPwd();

        reportLogin(bean.getSiteId(), member, bean.getReportData());
        UserLoginSessionUtil.saveSession(member.getMemberName(), UserLoginSessionUtil.LoginType.INTERFACE_SMS_LOGIN.getType(), null, sendBinder.getAccessSiteId());
        //一分钟内不能重复发送
        String key = SmsKeyUtil.buildSmsKey(sendBinder.getMemberId(), sendBinder.getSendNumber());
//        SmsKeyUtil.expireSmsCode(key, 1, TimeUnit.MINUTES);

        SmsKeyUtil.deleteSmsCode(key);

        String sign = getSingleAccountBindService().bindTable(member, bean.getBindType(),
                bean.getSingleAccountToken(), bean.getAccessSiteId());
        if (null != sign) {
            ValidateAgainResponse response = new ValidateAgainResponse();
            response.setMsgId("1019");
            response.setToken(sign);
            response.setMsg("请绑定手机号");
            context.put("response", response);
            return false;
        }

        PhoneLoginRespone response = new PhoneLoginRespone(member);
        response.setTicket(TicketUtil.buildLoginTicket(member));
        context.put("response", response);
        return false;
    }

    private SingleAccountBindService getSingleAccountBindService() {
        return (SingleAccountBindService) BaseStoneContext.getInstance().getBean("singleAccountBindService");
    }
}
