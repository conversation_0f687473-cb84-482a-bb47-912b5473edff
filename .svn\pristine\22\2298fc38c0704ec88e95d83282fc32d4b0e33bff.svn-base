<%@ page contentType="text/html; charset=UTF-8" %>
<%@ include file="../common/taglibs.jsp" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="Access-Control-Allow-Origin" content="*"/> 
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <title>顺网SSO 登录页</title>
    <c:choose>
        <c:when test="${not empty loginCss && not empty loginCss.cssURL }">
            <link href="${loginCss.cssURL}" rel="stylesheet" type="text/css"/>
        </c:when>
        <c:otherwise>
            <link href="${staticServer}/${cdnVersion}/style/default.css" rel="stylesheet" type="text/css"/>
        </c:otherwise>
    </c:choose>
    <script type="text/javascript" src="${staticServer}/${cdnVersion}/js/jquery-1.4.3.js"></script>
    <script type="text/javascript" src="${staticServer}/${cdnVersion}/js/jquery.slider.js"></script>
    <script type="text/javascript" src="${staticServer}/${cdnVersion}/login/js/StringUtil.js"></script>
    <script type="text/javascript" src="${staticServer}/${cdnVersion}/js/base.js"></script>
</head>
<body>
<%--   没有登录用户的情况  开始标记     --%>
<s:form id="cancel_form_" method="post" onsubmit="return false;">
    <!-- 撤销注销 -->
    <div class="alone-repeal-tip-content" >
        <div class="repeal-tip-box" style="display:block;">
            <div class="repeal-tip-title">
                <img src="${staticServer}/${cdnVersion}/images/warm_tip_icon.png" alt="">
                <div>温馨提示</div>
            </div>
            <div class="repeal-tip-desc">该账号已注销,如需登录请撤销
            </div>
            <div class="repeal-btn-group">
                <button class="btn-repeal">撤销注销</button>
                <button class="btn-cancel">取消</button>
            </div>
        </div>
    </div>
    <input type="hidden" name="optToken" value="${optToken}"/>
    <input type="hidden" name="site_id" value="${site_id}"/>
    <input type="hidden" name="memberName" value="${memberName}"/>
</s:form>

<script type="text/javascript">
    var callbackUrl = "${callbackUrl}";
    var refreshPage = "${refreshPage}";
    var tgtCancel = "${tgtCancel}";
    $(document).ready(function () {
        $(".btn-repeal").click(function() {
            var data = $("#cancel_form_").serialize();
            $.ajax({
                url:"/undoCancel.do",
                data:data,
                type: 'post',
                dataType: 'json',
                async: false, //同步
                success: function(data) {
                    if (data && refreshPage == 'true') {
                        if (!!tgtCancel && "self" == tgtCancel) {
                            location.href = callbackUrl;
                            return;
                        }
                        top.location.href = callbackUrl;
                        return;
                    }
                    location.reload(true);
                    return;
                }
            });
        });

        $(".btn-cancel").click(function() {
            if (top != self) {
                if (refreshPage == 'true') {
                    if (!!tgtCancel && "self" == tgtCancel) {
                        location.href = callbackUrl;
                        return;
                    }
                    top.location.href = callbackUrl;
                    return;
                }
                location.reload(true);
                return;
            }
            window.close();
        });

    });
</script>
</body>
</html>
