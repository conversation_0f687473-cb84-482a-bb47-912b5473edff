package com.shunwang.basepassport.bind.email;

import com.shunwang.basepassport.BaseTest;
import com.shunwang.util.date.DateUtil;

public class TestEmailBind extends BaseTest {
    @Override
    public void init() {
        params.put("siteId", "lz_test");
        //params.put("memberId", "91485505");
        //params.put("emailId", "leiz001");
        params.put("email", "<EMAIL>");
        params.put("activeNo", "186348");
        params.put("interfaceType","7");

        params.put("time", DateUtil.getCurrentDateStamp());
        params.put("signVersion", "1.0");
        params.put("outSign", getSign(params));
    }

    @Override
    protected String getUrl() {
        return "http://interface.kedou.com/front/interface/outCheckEmail.htm";
    }

    @Override
    protected String getMd5Key() {
        return "123456";
    }
}
