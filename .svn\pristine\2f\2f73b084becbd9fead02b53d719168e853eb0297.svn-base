package com.shunwang.baseStone.sso.geetest;

import com.shunwang.baseStone.cache.CacheKeyConstant;
import com.shunwang.baseStone.context.RedisContext;
import com.shunwang.baseStone.jms.CacheMessageBuilder;
import com.shunwang.baseStone.sender.context.SenderContext;
import com.shunwang.baseStone.sender.pojo.SendMsg;
import com.shunwang.basepassport.config.common.ResourcesUtil;
import com.shunwang.basepassport.config.pojo.ConfigResources;
import com.shunwang.framework.struts2.action.BaseAction;
import com.shunwang.util.StringUtil;
import com.shunwang.util.date.DateUtil;
import com.shunwang.util.json.GsonUtil;
import org.apache.struts2.ServletActionContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * date: 18-05-22
 * time: 下午5:02
 * since:
 */
public class GeetestAction extends BaseAction {

    private static final long serialVersionUID = 401664124883686182L;
    private static Logger log =  LoggerFactory.getLogger(GeetestAction.class);
    public final static String GT_EMAIL_CONTENT = "在$date$，1分钟内调极验验证码失败次数超$time$次，极验验证码总开关已关闭。";

    /**
     * 极验初始化
     * @return
     */
    public String gtRegister() {
        //进行验证预处理
        GeetestLibResult result = GeetestLib.register(ServletActionContext.getRequest());
        int gtServerStatus = result.getStatus();
        //极验请求异常时，程序主动关闭流程
        if (gtServerStatus == 0) {
            //每分钟一个key值，该key最长生成时间为2分钟，业务有效时间最长1分钟
            String key = GeetestLib.GT_SERVER_STATUS_ERR_TIMES_CACHE_KEY + DateUtil.dateFormat(DateUtil.getInstance(DateUtil.ymdhm_TIME_STAMP_FORMAT), new Date());
            String value = RedisContext.getRedisCache().get(key);
            if (value == null && RedisContext.getRedisCache().set(key, 1, 2L, TimeUnit.MINUTES)) {
                log.debug("极验初始化异常key{[]}存入缓存", key);
            } else {
                int times = Integer.parseInt(value);
                //默认异常阀值为10
                int threshold = RedisContext.getIntConfig(CacheKeyConstant.ConfigResourcesConstants.TYPE_BASE_CONFIG,
                        CacheKeyConstant.ConfigResourcesConstants.BASE_CONFIG_GT_ERROR_AUTO_CLOSE_THRESHOLD, 10);
                if (times > threshold) {
                    ConfigResources resources = new ConfigResources();
                    resources.setType(CacheKeyConstant.ConfigResourcesConstants.TYPE_BASE_CONFIG);
                    resources.setName(CacheKeyConstant.ConfigResourcesConstants.BASE_CONFIG_GT_SWITCH);
                    resources.setValue("0");
                    ResourcesUtil.updateResources(resources, CacheMessageBuilder.newPrefixDeleteArrayMessage(
                            CacheKeyConstant.EhcacheKey.CONFIG_RESOURCES + CacheKeyConstant.ConfigResourcesConstants.TYPE_BASE_CONFIG +
                                    CacheKeyConstant.CACHE_SPLIT + CacheKeyConstant.ConfigResourcesConstants.BASE_CONFIG_GT_SWITCH));

                    //极验异常时，加锁发送警报通知，1分钟发送一次
                    if (RedisContext.getRedisCache().setNx(CacheKeyConstant.GeetestLib.GT_ALARM_LOCK, "1", 60, TimeUnit.SECONDS)) {
                        gtSwitchAlarm(times);
                    }
                } else {
                    times++;
                    RedisContext.getRedisCache().set(key, times, 2L, TimeUnit.MINUTES);
                }
            }
        }
        outPrint(result.getData());
        return null;
    }

    /**
     * 极验开关自动关闭 报警通知
     *
     * @param times 次数
     */
    private void gtSwitchAlarm(int times) {
        String gtEmails = RedisContext.getStrConfig(CacheKeyConstant.ConfigResourcesConstants.TYPE_BASE_CONFIG,
                CacheKeyConstant.ConfigResourcesConstants.BASE_CONFIG_GT_ALARM_EMAILS, "");
        if (com.shunwang.util.StringUtil.isBlank(gtEmails)) {
            log.info("极验开关自动报警邮箱为空");
            return;
        }
        try {
            String[] emails = gtEmails.split("\\|");
            for (String email : emails) {
                if (StringUtil.isNotBlank(email)) {
                    SendMsg sendMsg = new SendMsg();
                    sendMsg.setNumber(email.trim());
                    sendMsg.setTitle("极验验证码超阈值");
                    String content = GT_EMAIL_CONTENT
                            .replace("$date$", DateUtil.getCurrentChineseDate())
                            .replace("$time$", String.valueOf(times));
                    sendMsg.setContent(content);
                    SenderContext.getEmailSender().doSend(sendMsg);
                    log.info("发送极验关闭报警邮件{},{}", sendMsg.getNumber(), sendMsg.getContent());
                }
            }
        } catch (Exception ex) {
            log.error("解析极验开关报警邮件异常", ex);
        }

    }

    /**
     * 极验校验
     * @return
     */
    public String gtValidate() {
        Map<String, String> res = new HashMap<>();
        HttpServletRequest request = ServletActionContext.getRequest();
        //进行验证预处理
        boolean result = isGtSwitchOpen() ?
                GeetestLib.validate(request) : GeetestLib.validateFail(request);
        if (result) {
            res.put("code", "0");
            res.put("msg","success");
        } else {
            res.put("code", "1");
            res.put("msg", "fail");
        }
        outPrint(GsonUtil.toJson(res));
        return null;
    }

    private boolean isGtSwitchOpen() {
        return RedisContext.getSwitchConfig(CacheKeyConstant.ConfigResourcesConstants.TYPE_BASE_CONFIG,
                CacheKeyConstant.ConfigResourcesConstants.BASE_CONFIG_GT_SWITCH, false);
    }

    /**
     * 极验开关
     * @return
     */
    public String gtSwitch() {
        Map<String, Object> res = new HashMap<>();
        //进行验证预处理
        boolean result = isGtSwitchOpen();
        if (result) {
            res.put("code", "0");
            res.put("msg","open");
            res.put("gtSwitch", 1);
        } else {
            res.put("code", "1");
            res.put("msg", "close");
            res.put("gtSwitch", 2);
        }
        outPrint(GsonUtil.toJson(res));
        return null;
    }

    private void outPrint(String msg) {
        HttpServletResponse response = getResponse();
        PrintWriter out = null;
        try {
            out = response.getWriter();
        } catch (IOException e) {
            log.error("输出失败", e);
        }
        out.println(msg);
    }

}
