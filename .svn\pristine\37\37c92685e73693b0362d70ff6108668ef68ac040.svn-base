package com.shunwang.basepassport.risk.response;

import com.google.gson.annotations.Expose;
import com.shunwang.baseStone.core.response.BaseStoneResponse;
import com.shunwang.basepassport.user.pojo.Member;
import com.shunwang.util.encrypt.Md5Encrypt;
import com.shunwang.xmlbean.annotation.XmlInit;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.UnsupportedEncodingException;

import static java.net.URLEncoder.encode;


public class CheckRiskResponse extends BaseStoneResponse {

	private final static Logger log = LoggerFactory.getLogger(CheckRiskResponse.class);
	@Expose
	private String userId;
	@Expose
	private String key;//签名的key


	public CheckRiskResponse(Member member) {
		this.userId = String.valueOf(member.getMemberId());
	}

	public CheckRiskResponse(Member member, String key) {
		this.userId = String.valueOf(member.getMemberId());
		this.key = key;
	}
	
	@XmlInit(isAttribute=true)
	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	@XmlInit(isAttribute=true,path="returnSign")
	@Override
	public String getSignString() {
		if(!isNeedSign()) return null;
		String ret = this.buildSign();
		ret = ret+"|"+key;
		log.warn("接口返回签名原字符串:" + ret);
		try {
			ret = encode(ret,"utf-8").toUpperCase();
		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
		}
		return Md5Encrypt.encrypt(ret).toUpperCase();
	}

	
}
