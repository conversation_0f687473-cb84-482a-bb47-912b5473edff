package com.shunwang.basepassport.manager.response.netbar;

import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.shunwang.basepassport.manager.IResponse;
import com.shunwang.util.json.GsonUtil;
import com.shunwang.util.lang.StringUtil;

import java.util.Map;

public class IdCardDesensitizationResponse implements IResponse {
    private Integer code;
    private String message;
    private String data;
    private String rawJson;
    private JsonObject jsonObject;

    private Integer dataCode;
    private String snbid;
    private String idCardNo;

    public static final int SUCCESS = 0;


    public boolean isSuccess() {
        return SUCCESS == code;
    }

    public boolean isDataSuccess() {
        return isSuccess() && SUCCESS == dataCode;
    }

    public IdCardDesensitizationResponse parse() {
        if (StringUtil.isBlank(rawJson)) {
            return null;
        }
        jsonObject = JsonParser.parseString(rawJson).getAsJsonObject();
        code = jsonObject.get("code").getAsInt();
        message = checkJsonItem(jsonObject, "message") ? jsonObject.get("message").getAsString() : "";
        if (isSuccess()) {
            data = jsonObject.get("data").getAsString();
            JsonArray array = JsonParser.parseString(data).getAsJsonArray();
            JsonObject dataJson = array == null ? new JsonObject() : array.get(0).getAsJsonObject();
            dataCode = dataJson.has("code") ? dataJson.get("code").getAsInt() : 1;
            idCardNo = dataJson.has("idcard") ? dataJson.get("idcard").getAsString() : "";
            snbid = dataJson.has("snbid") ? dataJson.get("snbid").getAsString() : "";
        }
        return this;
    }

    protected boolean checkJsonItem(JsonObject jsonObject,String itemName) {
        return jsonObject.get(itemName) != null;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getRawJson() {
        return rawJson;
    }

    public void setRawJson(String rawJson) {
        this.rawJson = rawJson;
    }

    public JsonObject getJsonObject() {
        return jsonObject;
    }

    public void setJsonObject(JsonObject jsonObject) {
        this.jsonObject = jsonObject;
    }

    public Integer getDataCode() {
        return dataCode;
    }

    public void setDataCode(Integer dataCode) {
        this.dataCode = dataCode;
    }

    public String getSnbid() {
        return snbid;
    }

    public void setSnbid(String snbid) {
        this.snbid = snbid;
    }

    public String getIdCardNo() {
        return idCardNo;
    }

    public void setIdCardNo(String idCardNo) {
        this.idCardNo = idCardNo;
    }
}
