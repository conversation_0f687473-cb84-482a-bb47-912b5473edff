package com.shunwang.basepassport.config.common;

import com.shunwang.baseStone.context.BaseStoneContext;
import com.shunwang.basepassport.config.dao.ServiceDao;
import com.shunwang.basepassport.config.pojo.Service;

/**
 * @author: lj.zeng
 * @create: 2024-03-18 10:48:57
 * @Description:
 */
public class ServiceUtil {

    private static ServiceDao serviceDao;

    public static ServiceDao getServiceDao() {
        if (serviceDao == null) {
            serviceDao = BaseStoneContext.getInstance().getBean(ServiceDao.class);
        }
        return serviceDao;
    }

    public static Service loadService(String serviceKey) {
        return getServiceDao().getByServiceKey(serviceKey);
    }

    public static boolean serviceIsOpen(String serviceKey) {
        Service service = getServiceDao().getByServiceKey(serviceKey);
        return service != null && service.isOpen();
    }
}
