package com.shunwang.basepassport.shorturl.response;

import com.google.gson.annotations.Expose;
import com.shunwang.baseStone.core.response.BaseStoneResponse;
import com.shunwang.baseStone.encrypt.Encrypt;
import com.shunwang.baseStone.encrypt.pojo.EncryptItem;
import com.shunwang.xmlbean.annotation.XmlInit;

/**
 * User:pf.ma
 * Date:2017/04/27
 * Time:16:38
 */
public class ShortUrlResponse extends BaseStoneResponse {
	@Expose
	private String  shortUrl;

	public ShortUrlResponse(String  shortUrl){
		this.shortUrl = shortUrl;
	}

	@Override
	protected String buildSign() {
		Encrypt encrypt=new Encrypt();
		encrypt.addItem(new EncryptItem(this.shortUrl));
		return encrypt.buildSign();
	}


	@XmlInit
	public String getShortUrl() {
		return shortUrl;
	}

	public void setShortUrl(String shortUrl) {
		this.shortUrl = shortUrl;
	}
}
