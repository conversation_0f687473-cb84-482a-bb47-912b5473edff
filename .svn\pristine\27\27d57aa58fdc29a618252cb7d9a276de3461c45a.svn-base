package com.shunwang.basepassport.user.dao;

import com.shunwang.baseStone.context.BaseStoneContext;
import com.shunwang.baseStone.context.IPContext;
import com.shunwang.baseStone.context.TransactionContext;
import com.shunwang.baseStone.core.dao.BaseStoneIbatisDao;
import com.shunwang.basepassport.actu.constant.ActuConstant;
import com.shunwang.basepassport.binder.common.ErrorCode;
import com.shunwang.basepassport.user.common.MemberConstants;
import com.shunwang.basepassport.user.exception.RegisterErrorExp;
import com.shunwang.basepassport.user.pojo.Member;
import com.shunwang.basepassport.user.pojo.MemberInfo;
import com.shunwang.basepassport.user.pojo.MemberOutSite;
import com.shunwang.framework.exception.WinterException;
import com.shunwang.util.lang.StringUtil;
import com.shunwang.util.net.HttpUtil;

/**
 * @Described：外部用户导入dao
 * <AUTHOR> create at 2012-2-1 下午01:41:50
 * @FileNmae com.shunwang.basepassport.user.dao.MemberOutSiteDao.java
 */
public class MemberOutSiteDao extends BaseStoneIbatisDao<MemberOutSite> {

	public MemberDao getMemberDao(){
		return (MemberDao)BaseStoneContext.getInstance().getBean("memberDao");
	}

	public Member login(MemberOutSite memberOutSite){
		Member member = getByOutMemberId(memberOutSite.getOutMemberId());
		if(null!=member){
            //外部用户的登录方式记录的是诸如 QQ,浙江电信,4008之类的来源
			member.setLoginType(memberOutSite.getMemberFrom());
            member.setVrsion(StringUtil.isNotBlank(memberOutSite.getVersion())? memberOutSite.getVersion() : "");
            member.setEnv(memberOutSite.getEnv());
            member.setExtData(memberOutSite.getRemark());
			member.loginWithNoPwd();
			return member;
		}else
			return null;
	}

	public Member findOrSave(MemberOutSite memberOutSite) throws WinterException {
		Member member = getByOutMemberId(memberOutSite.getOutMemberId());
		if(null!=member){
			member.setLoginType(MemberConstants.LOGIN_TYPE_ACCOUNT);
            member.setVrsion(StringUtil.isNotBlank(memberOutSite.getVersion())? memberOutSite.getVersion() : "");
            member.setEnv(memberOutSite.getEnv());
            member.setExtData(memberOutSite.getRemark());
			member.loginWithNoPwd();
			return member;
		}
		return register(memberOutSite);
	}

	public Member getByOutMemberId(String outMemberId) {
		return getMemberDao().getByOutMemberId(outMemberId);
	}

	public Member getByOutMemberName(String outMemberName) {
		return getMemberDao().getByOutMemberName(outMemberName);
	}

	public Member register(MemberOutSite memberOutSite) {
		return register(memberOutSite, IPContext.getIp());
	}

	public Member register(MemberOutSite memberOutSite, String ip) {
		Member member = null;
		try {
			TransactionContext.beginTransaction();
			member = buildMember(memberOutSite, ip).doRegist();
			memberOutSite.setMemberId(member.getMemberId());
			super.save(memberOutSite);
			TransactionContext.commitTran();
		} catch (Exception e) {
			logger.error("保存异常", e);
			TransactionContext.rollbackTran();
			throw new RegisterErrorExp(ErrorCode.C_1207.getCode(), ErrorCode.C_1207.getDescription());
		}
		return member;
	}

	/**
	 * 创建资金账号
	 * @param member
	 * <AUTHOR> create at 2012-2-20 下午04:44:59
	 */
	public void createAccount(Member member) {
		try {
			String str = "?userId="+member.getMemberId();
			HttpUtil.doGet(MemberConstants.CREATE_ACCOUNT_URL+str);
		} catch (Exception e) {
			logger.error("requestUrl:["+MemberConstants.CREATE_ACCOUNT_URL+"]，异常",e);
		}
	}

	public boolean checkUserName(String username) throws WinterException {
		return null==getMemberDao().getByName(username);
	}

	private Member buildMember(MemberOutSite memberOutSite, String ip) {
		Member member = new Member();
		member.setMemberName(memberOutSite.getMemberName());
		member.setRealName(memberOutSite.getRealName());
		member.setEmail(memberOutSite.getEmail());
		member.setMobile(memberOutSite.getMobile());
		member.setHeadImg(memberOutSite.getHeadImg());
		member.setMemberType(memberOutSite.getMemberType());
		member.setMemberState(memberOutSite.getMemberState());
		member.setNickName(memberOutSite.getNickName());
		member.setBindState(MemberConstants.MEMBER_STATE_NOTHING);
		member.setMemberType(MemberConstants.USER_TYPE_PERSONAL);
		member.setMemberState(MemberConstants.USER_NATURAL_STATE);
		member.setPersonCertState(Integer.parseInt(ActuConstant.INFO_STATE_UN_APPLY));
		member.setCompanyCertState(Integer.parseInt(ActuConstant.INFO_STATE_UN_APPLY));
		member.setCafeCertState(Integer.parseInt(ActuConstant.INFO_STATE_UN_APPLY));
		member.setSiteType(MemberConstants.SITE_TYPE_OUT);

		member.setMemberPwd(" ");
		MemberInfo info = new MemberInfo(memberOutSite.getRegFrom());
		info.setQq(memberOutSite.getQq());
		info.setFixedMobile(memberOutSite.getFixedMobile());
		info.setPostCode(memberOutSite.getPostCode());
		info.setLinkAddress(memberOutSite.getLinkAddress());

		//云海客户端用户保存
		/** 用户是通过云海什么版本登录的需求，增加VERSION字段，使用version参数 --> 记录在logon_version字段
		 增加用户从哪儿登录，网吧ID(BARID)  ，使用env参数--> 记录在logon_evnironment字段
		 用户从哪台机子登录的，GUID+mac地址，使用extData参数 --> 记录在remark字段 */
		info.setRemark(memberOutSite.getRemark());
		info.setRegVersion(memberOutSite.getVersion());
		info.setRegEnvironment(memberOutSite.getEnv());

		/** 设置注册来源ip */
		ip = StringUtil.isEmpty(ip) ? IPContext.getIp() : ip;
		info.setRegIp(ip);

		member.setMemberInfo(info);
		return member;
	}

	public MemberOutSite getMemberOutSiteByOutMemberId(String outMemberId){
		return (MemberOutSite) getSqlMapClientTemplate().queryForObject(getStatementNameWrap("getMemberOutSiteByOutMemberId"), outMemberId);
	}
	/**
	 * 根据outMemberName和memberForm查询通行证帐号
	 *
	 * @return
	 * <AUTHOR> 创建于2013-3-11 下午05:21:50
	 * @throws
	 */
	public MemberOutSite getMemberByOutMemberNameAndFrom(String outMemberName,Integer memberFrom){
		MemberOutSite outSite = new MemberOutSite();
		outSite.setOutMemberName(outMemberName);
		outSite.setMemberFrom(memberFrom.toString());
		return (MemberOutSite) getSqlMapClientTemplate().queryForObject(getStatementNameWrap("getMemberByOutMemberNameAndFrom"), outSite);
	}

    /**
	 * 根据memberId和memberForm查询外部接入的通行证账号
	 * @return
	 * @throws
	 */
	public MemberOutSite getMemberByMemberIdAndFrom(Integer memberId,Integer memberFrom){
		MemberOutSite outSite = new MemberOutSite();
		outSite.setMemberFrom(memberFrom.toString());
        outSite.setMemberId(memberId);
		return (MemberOutSite) getSqlMapClientTemplate().queryForObject(getStatementNameWrap("getMemberByMemberIdAndFrom"), outSite);
	}

	public MemberOutSite getByMemberName(String memberName) {
		return (MemberOutSite) getSqlMapClientTemplate().queryForObject(getStatementNameWrap("getByMemberName"), memberName);
	}

	/**
	 * 根据memberId和memberForm查询外部接入的通行证账号
	 * @return
	 * @throws
	 */
	public MemberOutSite getMemberByMemberId(Integer memberId){
		MemberOutSite outSite = new MemberOutSite();
		outSite.setMemberId(memberId);
		return (MemberOutSite) getSqlMapClientTemplate().queryForObject(getStatementNameWrap("getMemberByMemberId"), outSite);
	}
}
