package com.shunwang.basepassport.user.common;

import java.util.HashMap;
import java.util.Map;

/**
 * @Description:用户常量
 * <AUTHOR>  create at 2011-7-26 上午10:56:01
 * @FileName com.shunwang.basepassport.user.common.MemberConstants.java
 */
public class MemberConstants {

    /**
     * 性别:男
     */
    public static final String MALE = "1";
    /**
     * 性别:女
     */
    public static final String FEMALE = "0";
    /**
     * 性别:未知
     */
    public static final String UNKNOWN = "2";
    public static final int NUM_EIGHTEEN = 18;
    public static final int NUM_FIFTEEN = 15;
    public static final int NUM_NINE = 9;
    public static final int NUM_ZERO = 0;
    public static final int NUM_TEN = 10;
    public static final int NUM_ONE = 1;
    public static final String UPDATE_PWD = "1";   //修改密码
    public static final String RESET_PWD = "2";   //重置密码
    public static final String LOGIN_TYPE_ACCOUNT = "1";//账号
    public static final String LOGIN_TYPE_MOBILE = "2";//手机
    public static final String LOGIN_TYPE_EMAIL = "3";//邮箱
    public static final String LOGIN_TYPE_ACTIVE_NO = "4";//验证码
    public static final String LOGIN_TYPE_ONE_LOGIN = "5";//一键登录
    //以下是接口类型
    //查询手机号绑定的用户
    public static final String QUERY_USER_BY_BIND_MOBILE = "query_user_by_bind_mobile";
    public static final String UNQUINECHECK = "手机邮箱用户名唯一性验证";
    public static final String UPDATAPWD = "修改密码";
    public static final String LOGIN = "登录";
    public static final String LOGOUT = "接口登录注销";
    public static final String MEMBERQUERY = "查询";
    public static final String SIMPLE_MEMBERQUERY = "简单查询";
    public static final String MEMBER_VIP_QUERY = "用户VIP信息查询";
    public static final String LOGON_QUERY = "登录日志查询";
    public static final String HEAD_IMG_MODIFY = "用户头像修改";
    public static final String OUTSITE_MEMBER_REGISTER = "外部站点用户注册";
    public static final String MEMBER_MODIFY = "用户修改";
    /**
     * 用于一键登录，保存服务端数据
     */
    public static final String SINCE_CODE_CREATE = "创建场景值";
    public static final String MEMBER_CANCEL = "用户注销";//目前用于注销完成后通知接入方服务器退出用户
    public static final String MEMBER_UNDO_CANCEL = "撤销用户注销";
    public static final String MEMBER_CANCEL_NOTICE = "用户注销通知";
    public static final String CHECK_RISK = "风控检测";
    public static final String OA_GET_USER = "获取OA用户";

    public static final String MOBILE_CHANGE_NOTIFY = "mobile_change_notice";
    /**
     * 在线客服接口
     */
    public static final String CUSTOM_SERVICE = "CustomService";
    /**
     * 提供给手机的用户查询接口
     */
    public static final String MEMBER_QUERY_PROVIDE_TO_MOBILE = "提供给手机的用户查询接口";
    public static final String REGISTER = "注册";
    public static final String KEDOU_BAR_USER_REGISTER = "支付宝公众号用户注册";
    public static final String TMP_REGISTER = "注册临时通行证账户";
    public static final String MEMBERQUERY_BY_OUTUSER = "外部用户查询通行证账户";
    public static final String CHECK_LOGIN = "查询用户当天登录记录";
    public static final String SEND_SMS_CHECK_CODE = "顺令获取短信验证码";
    public static final String SHUN_LING_BING = "顺令绑定";
    public static final String SHUN_LING_UNBING = "顺令解绑";
    public static final String SHUN_LING_TIME_SYN = "顺令时间同步";
    //手机SDK用到的接口
    public static final String SDK_CODE_SEND = "手机SDK验证码发送";
    public static final String SDK_CODE_VERIFY = "手机SDK验证码认证";
    public static final String GET_DYNAMICKEY = "动态密钥";
    public static final String GET_BUSS_KEY = "免登录动态密钥";
    public static final String BUSINESS_QUERY_ALL = "查询所有站点信息";
    public static final String BUSINESS_QUERY_SINGLE = "查询单个站点信息";
    public static final String DYNAMIC_PASSWORD_SMS = "动态密码发送";
    public static final String DYNAMIC_SECURITY_CHECK = "动态密保验证";
    public static final String DYNAMIC_PASSWORD_SMS_INNER = "内部动态密码发送";
    public static final String DYNAMIC_SECURITY_CHECK_INNER = "内部动态密保验证";
    public static final String SMS_SEND = "手机短信发送";
    public static final String SMS_CHECK = "手机短信验证";
    public static final String EMAIL_SEND = "邮箱验证码发送";
    public static final String EMAIL_CHECK = "邮箱验证码验证";
    public static final String SAFE_NOTICE_SEND_SMS = "账户安全异动短信提醒";
    public static final String LOG_COLLECT = "用户行为日志分析";
    public static final String IDCARD_BIND = "身份证通行证账号绑定接口";
    public static final String IDCARD_UNBIND = "身份证通行证账号解绑接口";
    public static final String IDCARD_BIND_QUERY = "身份证通行证账号绑定查询接口";
    /**
     * 关联账号接口
     */
    public static final String CONNECTED_ACCOUNTS_BIND = "connectedAccounts";
    //加密方式
    public static final String DES = "DES";
    public static final String AES = "AES";
    public static final String RSA = "RSA";
    /**
     * 0什么都没绑
     **/
    public static final Integer MEMBER_STATE_NOTHING = 0;
    /**
     * 用户正常标识
     **/
    public static final Integer USER_NATURAL_STATE = 0;
    /**
     * 用户禁止账号
     **/
    public static final Integer USER_FORBID_STATE = 1;
    /**
     * 用户临时账号
     **/
    public static final Integer USER_TMP_STATE = 2;
    /**
     * 用户注销账号
     **/
    public static final int USER_CANCEL_STATE = 3;
    /**
     * 邮箱账号作为登录账户
     */
    public static final Integer MEMBER_STATE_MAIL_AS_LOGIN_ACCOUNT = 256;
    /**
     * 手机号码作为登录账户
     */
    public static final Integer MEMBER_STATE_PHONE_AS_LOGIN_ACCOUNT = 128;
    /**
     * 账户安全异动提醒
     **/
    public static final Integer MEMBER_STATE_SAFE_NOTICE = 64;
    /**
     * 手机顺令
     **/
    public static final Integer MEMBER_STATE_SHUN_LING = 32;
    /**
     * 手机动态验证码
     **/
    public static final Integer MEMBER_STATE_DYNAMIC_PWD = 16;
    /**
     * 密保问题
     **/
    public static final Integer MEMBER_STATE_PROBLEM = 8;
    /**
     * 密保卡
     **/
    public static final Integer MEMBER_STATE_SAFECARD = 4;
    /**
     * 邮箱
     **/
    public static final Integer MEMBER_STATE_MAIL = 2;
    /**
     * 手机
     **/
    public static final Integer MEMBER_STATE_PHONE = 1;
    /**
     * 临时账户密码
     **/
    public static final String TMP_MEMBER_PASSWORD = "000000";
    /**
     * 临时账号用户名
     **/
    public static final String TMP_NAME = "ww.temp.";
    /**
     * 注册来源通行证
     **/
    public static final String REG_FROM = "Passport";
    /**
     * 注册来源网维
     **/
    public static final String REG_FROM_ICAFE8 = "icafe8";
    /**
     * 创建资金账户URL
     **/
    public static final String CREATE_ACCOUNT_URL = "http://www.kedou.com/front/noLogin/createAccount_front.htm";
    /**
     * 登录类型备注用户注册
     **/
    public static final String USER_REGISTER_REMARK = "用户注册";
    /**
     * 登录类型：1用户名登录
     **/
    public static final String LOGON_TYPE_USERNAME = "1";
    /**
     * 会员日志表，logItem : 密码
     **/
    public static final String MEMBER_LOG_ITEM_PWD = "密码";
    /**
     * 查询类型,type="1"按id查
     **/
    public static final String QUERY_TYPE = "1";
    /**
     * 站内用户
     */
    public static final String SITE_TYPE_IN = "0";
    /**
     * 站外用户
     */
    public static final String SITE_TYPE_OUT = "1";
    /**
     * 修改密码获取邮件内容的KEY
     **/
    public static final String PWD_CHANGE_EMAIL_KEY = "PWD_CHANGE_EMAIL_KEY";
    /**
     * 修改密码获取短信内容的KEY
     **/
    public static final String PWD_CHANGE_MOBILE_KEY = "PWD_CHANGE_MOBILE_KEY";
    /**
     * 修改密保问题获取邮件内容的KEY
     **/
    public static final String PRO_CHANGE_EMAIL_KEY = "PRO_CHANGE_EMAIL_KEY";
    /**
     * 修改密保问题获取短信内容的KEY
     **/
    public static final String PRO_CHANGE_MOBILE_KEY = "PRO_CHANGE_MOBILE_KEY";
    /**
     * 设置密保问题获取邮件内容的KEY
     **/
    public static final String PRO_SET_EMAIL_KEY = "PRO_SET_EMAIL_KEY";
    /**
     * 设置密保问题获取短信内容的KEY
     **/
    public static final String PRO_SET_MOBILE_KEY = "PRO_SET_MOBILE_KEY";
    /**
     * 密码类型，顺令用户
     */
    @Deprecated
    public static final String PWD_TYPE_DYNAMIC_PASSWORD = "2";
    /**
     * 密码类型，动态验证码用户
     */
    public static final String PWD_TYPE_DYNAMIC_PWD = "1";
    /**百度OCR的KEY*/
    /**
     * 密码类型，顺令用户
     */
    public static final String PWD_TYPE_SHUN_LIING = "2";
    /**
     * 密码类型: 免登录跳转
     */
    public static final String PWD_TYPE_JUMPFROM = "3";
    /**
     * 密码类型: 互联组件(快速登录)
     */
    public static final String PWD_TYPE_QUICKLOGIN = "4";
    /**
     * 密码类型: 安全U盾
     */
    public static final String PWD_TYPE_U_SHIELD = "5";
    /**
     * 密码类型: Android一键登录
     */
    public static final String PWD_TYPE_ONE_LOGIN_ANDROID = "6";
    /**
     * 密码类型: IOS一键登录
     */
    public static final String PWD_TYPE_ONE_LOGIN_IOS = "7";
    /**
     * 密码类型: H5一键登录
     */
    public static final String PWD_TYPE_ONE_LOGIN_H5 = "8";
    /**
     * 登录步骤1
     */
    public static final String LOGIN_STEP = "1";
    /**
     * 登录步骤2
     */
    public static final String LOGIN_STEP_NEXT = "2";
    @SuppressWarnings("all")
    public static final HashMap<String, String> LOGIN_TYPE_MAP = new HashMap<String, String>();
    public static final HashMap<String, String> PWD_TYPE_MAP = new HashMap<String, String>();
    /**
     * 手机注册前缀 手机号注册生成规则见
     *
     * @see com.shunwang.basepassport.user.common.MemberUtil buildTmpMemberName()
     */
    public static final String MOBILE_REGISTER_PRE = "sj_";
    /**
     * 弱密码
     */
    public static final String WEAK_PWD_STATE_1 = "1";
    /**
     * 非弱密码
     */
    public static final String WEAK_PWD_STATE_2 = "2";
    /**
     * 宽松弱密码
     */
    public static final String WEAK_PWD_STATE_3 = "3";
    /**
     * @YHJ 用户类型 ：个人
     */
    public static Integer USER_TYPE_PERSONAL = 1;
    /**
     * @YHJ 用户类型 ：企业
     */
    public static Integer USER_TYPE_COMPANY = 2;
    /**
     * @YHJ 用户类型 ：网吧业主
     */
    public static Integer USER_TYPE_CAFE = 3;
    /**
     * @YHJ 企业实名认证dao的map
     */
    public static Map<Integer, String> ACTU_USER_TYPE_MAP = new HashMap<Integer, String>();
    /**
     * 账户疑似泄露表配置数据
     **/
    public static Integer MEMBERUNSAFE_MEMBERTYPE_UNSAFE = 1;   //账户已经判断泄露，未修改密码
    public static Integer MEMBERUNSAFE_MEMBERTYPE_PWDUPDED = 0; //密码已修改
    public static Integer MEMBERINIPWD_MEMBERTYPE_PWDUPDED = 2; //初始化密码未修改
    public static String MEMBERUNSAFE_SECURITYTYPE_NOMAL = "0"; //兼容模式
    public static String MEMBERUNSAFE_SECURITYTYPE_PRO = "1"; //开启安全加强模式
    public static String MEMBERUNSAFE_SECURITY_SALT = "Passport|"; //固定盐
    public static int MEMBERUNSAFE_PWD_BEGININDEX = 6; //密码截取的长度- 开始处的索引（包括）
    public static int MEMBERUNSAFE_PWD_ENDINDEX = 30; //密码截取的长度- 结尾处索引（不包括）
    /**
     * 网吧业主
     */
    public static Integer MEMBER_SPECIAL_TYPE_CAFE = 1;//网吧业主
    /**
     * @YHJ 个人实名认证dao的beanname
     */
    private static String PERSONAL_ACTU_DAO_BEANNAME = "personalActuDao";
    /**
     * @YHJ 企业实名认证dao的beanname
     */
    private static String COMPANY_ACTU_DAO_BEANNAME = "companyActuDao";
    /**
     * @YHJ 网吧业主实名认证dao的beanname
     */
    private static String CAFE_ACTU_DAO_BEANNAME = "cafeActuDao";

    static {
        ACTU_USER_TYPE_MAP.put(USER_TYPE_PERSONAL, PERSONAL_ACTU_DAO_BEANNAME);
        ACTU_USER_TYPE_MAP.put(USER_TYPE_COMPANY, COMPANY_ACTU_DAO_BEANNAME);
        ACTU_USER_TYPE_MAP.put(USER_TYPE_CAFE, CAFE_ACTU_DAO_BEANNAME);
    }

    static {
        LOGIN_TYPE_MAP.put("1", "用户名登录");
        LOGIN_TYPE_MAP.put("2", "手机登录");
        LOGIN_TYPE_MAP.put("3", "邮箱登录");
        LOGIN_TYPE_MAP.put("1000", "浙江电信");
        LOGIN_TYPE_MAP.put("1001", "海南电信");
        LOGIN_TYPE_MAP.put("1002", "四川电信");
        LOGIN_TYPE_MAP.put("1003", "4008棋牌");
        LOGIN_TYPE_MAP.put("1004", "QQ");
        LOGIN_TYPE_MAP.put("1005", "微信");
        LOGIN_TYPE_MAP.put("1006", "微博");
        LOGIN_TYPE_MAP.put("1008", "微信公众号");
        LOGIN_TYPE_MAP.put("1100", "老K游戏");
        LOGIN_TYPE_MAP.put("1013", "微信小程序");

        PWD_TYPE_MAP.put(MemberConstants.PWD_TYPE_DYNAMIC_PWD, "动态密码");
        PWD_TYPE_MAP.put(MemberConstants.PWD_TYPE_SHUN_LIING, "顺令");
        PWD_TYPE_MAP.put(MemberConstants.PWD_TYPE_JUMPFROM, "免登录跳转");
        PWD_TYPE_MAP.put(MemberConstants.PWD_TYPE_QUICKLOGIN, "互联组件");
        PWD_TYPE_MAP.put(MemberConstants.PWD_TYPE_U_SHIELD, "安全U盾");

    }

    public static class IdcardBind {
        public static class IdcardBindState {
            /**
             * 已绑定
             **/
            public static final Byte BINDED = 1;
            /**
             * 未已解绑
             **/
            public static final Byte UNBINDED = 0;
        }
    }

    public static class UserBankCard {
        public static class DefaultState {
            /**
             * 非默认银行卡
             **/
            public static final Byte NOT_DEFAULT_CARD = 0;
            /**
             * 默认银行卡
             **/
            public static final Byte DEFAULT_CARD = 1;
            /**
             * 已经删除
             **/
            public static final Byte DELETE_CARD = 2;
            /**
             * 待进件
             **/
            public static final Byte REPORTING = 3;
            /**
             * 进件后审核拒绝
             **/
            public static final Byte REJECTED = 4;
        }

        public static class WithdrawalsType {
            /**
             * 自动提现
             **/
            public static final String AUTO_OUT_CASH = "1";
            /**
             * 手动提现
             **/
            public static final String MANUAL_CASH = "2";
        }

        public static class UserCardType {
            /**
             * 个人
             **/
            public static final String PERSONAL = "1";
            /**
             * 企业
             **/
            public static final String COMPANY = "2";
        }

    }

    public static class MemberCancel {
        public static class NotifyType {
            /**
             * 非默认银行卡
             **/
            public static final Byte NOT_DEFAULT_CARD = 0;
            /**
             * 默认银行卡
             **/
            public static final Byte DEFAULT_CARD = 1;
            /**
             * 已经删除
             **/
            public static final Byte DELETE_CARD = 2;
            /**
             * 待进件
             **/
            public static final Byte REPORTING = 3;
            /**
             * 进件后审核拒绝
             **/
            public static final Byte REJECTED = 4;
        }

        public static class WithdrawalsType {
            /**
             * 自动提现
             **/
            public static final String AUTO_OUT_CASH = "1";
            /**
             * 手动提现
             **/
            public static final String MANUAL_CASH = "2";
        }

        public static class UserCardType {
            /**
             * 个人
             **/
            public static final String PERSONAL = "1";
            /**
             * 企业
             **/
            public static final String COMPANY = "2";
        }

    }

    public static class AuthFreeType {
        public static final String NORMAL = "1";
        public static final String AUTH = "2";
        public static final String UN_AUTH = "4";

    }
}
