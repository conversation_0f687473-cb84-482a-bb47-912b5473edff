$import('SFModel');
var NoticeModel = $createClass('NoticeModel',function(param){
	if(!param)
		param = {};
	param.name = 'notice'
	this.SFModel(param);
	this.buildAction('add');
	this.buildAction('del');
	this.buildAction('update');
//	this.buildAction('shenhe',null,true);
	this.buildAction('check');
    this.buildAction('refuse');
    this.buildAction('close');
    this.buildAction('open');

},'SFModel');

NoticeModel.prototype.xxx=function(){
	
}
