package com.shunwang.passport.common.pojo;

import java.io.Serializable;
import java.util.List;

public class Node implements Serializable{

	private static final long serialVersionUID = 7615420396319383623L;
	
	private String  name;
    private Integer code;
    private Integer parentId;
    private List<Node> sub;

    public Node() {
    }

    public Node(Integer code, String name, Integer parentId) {
        this.name = name;
        this.code = code;
        this.parentId = parentId;
    }

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Integer getCode() {
		return code;
	}

	public void setCode(Integer code) {
		this.code = code;
	}

	public Integer getParentId() {
		return parentId;
	}

	public void setParentId(Integer parentId) {
		this.parentId = parentId;
	}

	public List<Node> getSub() {
		return sub;
	}

	public void setSub(List<Node> sub) {
		this.sub = sub;
	}

	
	
	

}
