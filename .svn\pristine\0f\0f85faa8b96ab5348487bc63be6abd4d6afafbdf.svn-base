package com.shunwang.basepassport.actu.web;

import com.shunwang.basepassport.actu.common.IdentityCardUtil;
import com.shunwang.basepassport.actu.constant.ActuConstant;
import com.shunwang.basepassport.actu.dao.ActuChangeDao;
import com.shunwang.basepassport.actu.dao.ActuDao;
import com.shunwang.basepassport.actu.exception.OutActuExp;
import com.shunwang.basepassport.actu.pojo.ActuChangeInfo;
import com.shunwang.basepassport.actu.pojo.ActuInfo;
import com.shunwang.basepassport.commonExp.ParamFormatErrorExp;
import com.shunwang.basepassport.commonExp.ParamNotFoundExp;
import com.shunwang.basepassport.user.common.UserCheckUtil;
import com.shunwang.basepassport.user.exception.MsgNotFoundExp;
import com.shunwang.basepassport.user.web.MemberAction;
import com.shunwang.util.lang.StringUtil;

/**
 * User:pf.ma
 * Date:2017/06/26
 * Time:10:57
 */
public abstract class outActuChangeAction<Pojo extends ActuChangeInfo> extends MemberAction {

	private ActuChangeDao actuChangeDao;

	private String memberName ;
	private String applyUserName ;
	private String applyMobile ;
	private String changeReason ;
	private String idCardNo;

	private ActuInfo actuInfo ;


	@Override
	public void doProcess() {
		checkActuData();
		Pojo pojo = buildChangeInfo() ;
		pojo.setActuInfo(actuInfo) ;
		actuChangeDao.save(pojo) ;
	}

	protected abstract ActuDao getAuthDao() ;

	protected abstract Pojo buildChangeInfo() ;

	protected void checkActuData(){
		ActuChangeInfo actuChangeInfo = actuChangeDao.getByMemberName(getMember().getMemberName()) ;
		if(null != actuChangeInfo && actuChangeInfo.getInfoState() == Integer.parseInt(ActuConstant.INFO_STATE_UN_CHECK)){
			throw new OutActuExp("申请已提交，请等待审核") ;
		}
	}

	protected ActuInfo getActuInfo(){
		ActuInfo actuInfo = getAuthDao().getByMemberId(getMember().getMemberId());
		if(null == actuInfo){
			throw new OutActuExp("获取实名认证信息失败") ;
		}
		this.actuInfo = actuInfo ;
		return actuInfo ;
	}

	@Override
	public void checkParam() {
		if(!checkMemberIsExist()){
			throw new MsgNotFoundExp("通行证账号") ;
		}
		if(StringUtil.isBlank(applyUserName)){
			throw new ParamNotFoundExp("申请人名称") ;
		}
		if(UserCheckUtil.getStrRealLength(applyUserName) > 40){
			throw new ParamFormatErrorExp("申请人名称","不能超过40个字符") ;
		}
		if(StringUtil.isBlank(applyMobile)){
			throw new ParamNotFoundExp("手机号码") ;
		}
		if(!UserCheckUtil.checkMobile(applyMobile)){
			throw new ParamFormatErrorExp("手机号码") ;
		}
		if(StringUtil.isBlank(changeReason)){
			throw new ParamNotFoundExp("变更原因") ;
		}
		if(StringUtil.isNotBlank(idCardNo) && !IdentityCardUtil.validate(idCardNo)){
			throw new ParamFormatErrorExp("无效身份证号码,请输入18位有效身份证号");
		}
	}

	@Override
	public String getMemberName() {
		return memberName;
	}

	public void setMemberName(String memberName) {
		this.memberName = memberName;
	}

	public String getApplyUserName() {
		return applyUserName;
	}

	public void setApplyUserName(String applyUserName) {
		this.applyUserName = applyUserName;
	}

	public String getApplyMobile() {
		return applyMobile;
	}

	public void setApplyMobile(String applyMobile) {
		this.applyMobile = applyMobile;
	}

	public ActuChangeDao getActuChangeDao() {
		return actuChangeDao;
	}

	public void setActuChangeDao(ActuChangeDao actuChangeDao) {
		this.actuChangeDao = actuChangeDao;
	}

	public String getChangeReason() {
		return changeReason;
	}

	public void setChangeReason(String changeReason) {
		this.changeReason = changeReason;
	}

	public void setActuInfo(ActuInfo actuInfo) {
		this.actuInfo = actuInfo;
	}

	public String getIdCardNo() {
		return idCardNo;
	}

	public void setIdCardNo(String idCardNo) {
		this.idCardNo = idCardNo;
	}
}
