<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE struts PUBLIC "-//Apache Software Foundation//DTD Struts Configuration 2.0//EN" "http://struts.apache.org/dtds/struts-2.0.dtd">

<struts>
	
	<package name="actu" namespace="/front/actuality" extends="passport">
		<!--~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		未实名认证,根据用户身份跳转到实名认证页面;
		已实名认证的,根据用户身份跳转到实名认证结果页面
		~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~-->
		<action name="actu_checkResult_front" method="routeActuPage" class="companyActuAction" >
			<result name="goCafeResult" type="chain">
				<param name="actionName">cafeActuInfo_checkResult_front</param>
				<param name="namespace">/front/actuality</param>
			</result>
			<result name="goPersonResult" type="chain">
				<param name="actionName">personalActuInfo_checkResult_front</param>
				<param name="namespace">/front/actuality</param>
			</result>

			<result name="goCompanyResult" type="chain">
				<param name="actionName">companyActuInfo_checkResult_front</param>
				<param name="namespace">/front/actuality</param>
			</result>
		</action>

		<!-- 网吧实名认证结果 -->
		<action name="cafeActuInfo_checkResult_front" method="toRealActuPage" class="cafeActuAction" >
			<result name="success">/front/actu/cafeActuInfo_checkResult_front.jsp</result>
			<result name="unBind">/front/actu/validatePhoneForActu.jsp</result>
			<result name="notPermit" type="redirect">/front/member/securityCenter_index.htm</result>
			<!--通用返回结果 -->
			<result name="goPersonResult" type="chain">
				<param name="actionName">personalActuInfo_checkResult_front</param>
				<param name="namespace">/front/actuality</param>
			</result>
			<result name="goPersonActu" type="chain">
				<param name="actionName">goPersonalActuality_front</param>
				<param name="namespace">/front/actuality</param>
			</result>
			<result name="goCompanyResult" type="chain">
				<param name="actionName">companyActuInfo_checkResult_front</param>
				<param name="namespace">/front/actuality</param>
			</result>
			<result name="goCompanyActu" type="chain">
				<param name="actionName"> companyActu_goCompanyInfo_front</param>
				<param name="namespace">/front/actuality</param>
			</result>
		</action>

		<!-- 去网吧实名认证手机验证页面 -->
		<action name="cafeActu_goCafeInfo_front" class="cafeActuAction" method="findActuInfo">
			<result name="success">/front/actu/cafeActuInfo_checkResult_front.jsp</result>
			<result name="unBind">/front/actu/validatePhoneForActu.jsp</result>
			<result name="notPermit" type="redirect">/front/member/securityCenter_index.htm</result>

			<result name="goPersonResult" type="chain">
				<param name="actionName">personalActuInfo_checkResult_front</param>
				<param name="namespace">/front/actuality</param>
			</result>
			<result name="goPersonActu" type="chain">
				<param name="actionName">goPersonalActuality_front</param>
				<param name="namespace">/front/actuality</param>
			</result>
			<result name="goCompanyResult" type="chain">
				<param name="actionName">companyActuInfo_checkResult_front</param>
				<param name="namespace">/front/actuality</param>
			</result>
			<result name="goCompanyActu" type="chain">
				<param name="actionName"> companyActu_goCompanyInfo_front</param>
				<param name="namespace">/front/actuality</param>
			</result>
		</action>

		<!-- 个人实名认证结果页面 -->
		<action name="personalActuInfo_checkResult_front" method="toRealActuPage" class="personalActuAction" >
			<result name="success">/front/actu/personalActuInfo_checkResult_front.jsp</result>
			<result name="unBind">/front/actu/validatePhoneForActu.jsp</result>
			<result name="notPermit" type="redirect">/front/member/securityCenter_index.htm</result>
			<!--通用 -->
			<result name="goCafeResult" type="chain">
				<param name="actionName">cafeActuInfo_checkResult_front</param>
				<param name="namespace">/front/actuality</param>
			</result>
			<result name="goCafeActu" type="chain">
				<param name="actionName">cafeActu_goCafeInfo_front</param>
				<param name="namespace">/front/actuality</param>
			</result>
			<result name="goCompanyResult" type="chain">
				<param name="actionName">companyActuInfo_checkResult_front</param>
				<param name="namespace">/front/actuality</param>
			</result>
			<result name="goCompanyActu" type="chain">
				<param name="actionName"> companyActu_goCompanyInfo_front</param>
				<param name="namespace">/front/actuality</param>
			</result>
		</action>
		<!-- 去个人实名认证页面，新版本的是到手机验证页面 -->
		<action name="goPersonalActuality_front" method="findActuInfo" class="personalActuAction" >
			<result name="success">/front/actu/personalActuInfo_checkResult_front.jsp</result>
			<result name="unBind">/front/actu/validatePhoneForActu.jsp</result>
			<result name="notPermit" type="redirect">/front/member/securityCenter_index.htm</result>

			<result name="goCafeResult" type="chain">
				<param name="actionName">cafeActuInfo_checkResult_front</param>
				<param name="namespace">/front/actuality</param>
			</result>
			<result name="goCafeActu" type="chain">
				<param name="actionName">cafeActu_goCafeInfo_front</param>
				<param name="namespace">/front/actuality</param>
			</result>

			<result name="goCompanyResult" type="chain">
				<param name="actionName">companyActuInfo_checkResult_front</param>
				<param name="namespace">/front/actuality</param>
			</result>
			<result name="goCompanyActu" type="chain">
				<param name="actionName"> companyActu_goCompanyInfo_front</param>
				<param name="namespace">/front/actuality</param>
			</result>
		</action>

		<!-- 企业实名结果页面 -->
		<action name="companyActuInfo_checkResult_front" method="toRealActuPage" class="companyActuAction" >
			<result name="success">/front/actu/companyActuInfo_checkResult_front.jsp</result>
			<result name="unBind">/front/actu/validatePhoneForActu.jsp</result>
			<result name="notPermit" type="redirect">/front/member/securityCenter_index.htm</result>

			<!--通用-->
			<result name="goCafeActu" type="chain">
				<param name="actionName">cafeActu_goCafeInfo_front</param>
				<param name="namespace">/front/actuality</param>
			</result>
			<result name="goCafeResult" type="chain">
				<param name="actionName">cafeActuInfo_checkResult_front</param>
				<param name="namespace">/front/actuality</param>
			</result>

			<result name="goPersonResult" type="chain">
				<param name="actionName">personalActuInfo_checkResult_front</param>
				<param name="namespace">/front/actuality</param>
			</result>
			<result name="goPersonActu" type="chain">
				<param name="actionName">goPersonalActuality_front</param>
				<param name="namespace">/front/actuality</param>
			</result>
		</action>
		<!-- 去企业实名认证页面，新版本的是到手机验证页面 -->
		<action name="companyActu_goCompanyInfo_front" class="companyActuAction" method="findActuInfo">
			<result name="success">/front/actu/validatePhoneForActu.jsp</result>
			<result name="unBind">/front/actu/validatePhoneForActu.jsp</result>
			<result name="notPermit" type="redirect">/front/member/securityCenter_index.htm</result>

			<result name="goCafeResult" type="chain">
				<param name="actionName">cafeActuInfo_checkResult_front</param>
				<param name="namespace">/front/actuality</param>
			</result>
			<result name="goCafeActu" type="chain">
				<param name="actionName">cafeActu_goCafeInfo_front</param>
				<param name="namespace">/front/actuality</param>
			</result>

			<result name="goPersonResult" type="chain">
				<param name="actionName">personalActuInfo_checkResult_front</param>
				<param name="namespace">/front/actuality</param>
			</result>
			<result name="goPersonActu" type="chain">
				<param name="actionName">goPersonalActuality_front</param>
				<param name="namespace">/front/actuality</param>
			</result>
		</action>

		<!-- 企业实名认证同意页面,提供外部系统访问跳转-->
		<action name="companyActu_agreement" method="routeActuPage" class="companyActuAction" >
			<result name="goCafeResult" type="chain">
				<param name="actionName">cafeActuInfo_checkResult_front</param>
				<param name="namespace">/front/actuality</param>
			</result>
			<result name="goPersonResult" type="chain">
				<param name="actionName">personalActuInfo_checkResult_front</param>
				<param name="namespace">/front/actuality</param>
			</result>

			<result name="goCompanyResult" type="chain">
				<param name="actionName">companyActuInfo_checkResult_front</param>
				<param name="namespace">/front/actuality</param>
			</result>
		</action>

		<!-- 企业实名认证页面,系统中仍有访问,跳转到统一配置 -->
		<!-- 企业实名认证首页 -->
		<action name="goCompanyActuality_front" class="companyActuAction" method="goActuality">
			<result name="success">/front/actu/newCompanyActuInfo_comp_Info_front.jsp</result>
			<result name="input">/front/actu/newCompanyActuInfo_comp_Info_front.jsp</result>
			<result name="unBind">/front/actu/newCompanyActuInfo_comp_Info_front.jsp</result>
			<result name="notPermit" type="redirect">/front/member/securityCenter_index.htm</result>
			<result name="goPersonal" type="chain">
				<param name="actionName">goPersonalActuality_front</param>
				<param name="namespace">/front/actuality</param>
			</result>
			<result name="goCompany" type="chain">
				<param name="actionName">companyActu_goCompanyInfo_front</param>
				<param name="namespace">/front/actuality</param>
			</result>
			<result name="goToDoPage" type="chain">
				<param name="actionName">companyActu_goCompanyInfo_front</param>
				<param name="namespace">/front/actuality</param>
			</result>
			<result name="goDonePage" type="chain">
				<param name="actionName">companyActuInfo_checkResult_front</param>
				<param name="namespace">/front/actuality</param>
			</result>
			<result name="goCafe" type="chain">
				<param name="actionName">cafeActu_goCafeInfo_front</param>
				<param name="namespace">/front/actuality</param>
			</result>
		</action>

		<!-- 网吧实名认证首页 -->
		<action name="goCafeActuality_front" class="cafeActuAction" method="goActuality">
			<result name="success">/front/actu/CafeActuInfo_cafeInfo_front.jsp</result>
			<result name="input">/front/actu/CafeActuInfo_cafeInfo_front.jsp</result>
			<result name="unBind">/front/actu/CafeActuInfo_cafeInfo_front.jsp</result>
			<result name="notPermit" type="redirect">/front/member/securityCenter_index.htm</result>
			<result name="goPersonal" type="chain">
				<param name="actionName">goPersonalActuality_front</param>
				<param name="namespace">/front/actuality</param>
			</result>
			<result name="goCompany" type="chain">
				<param name="actionName">companyActu_goCompanyInfo_front</param>
				<param name="namespace">/front/actuality</param>
			</result>
			<result name="goToDoPage" type="chain">
				<param name="actionName">cafeActu_goCafeInfo_front</param>
				<param name="namespace">/front/actuality</param>
			</result>
			<result name="goDonePage" type="chain">
				<param name="actionName">cafeActuInfo_checkResult_front</param>
				<param name="namespace">/front/actuality</param>
			</result>
			<result name="goCafe" type="chain">
				<param name="actionName">cafeActu_goCafeInfo_front</param>
				<param name="namespace">/front/actuality</param>
			</result>
		</action>

		<action name="goActuality_front" method="goActuality" class="personalActuAction">
			<result name="success">/front/actu/newPersonalActuInfo_front.jsp</result>
			<result name="unBind">/front/actu/newPersonalActuInfo_front.jsp</result>
			<result name="notPermit" type="redirect">/front/member/securityCenter_index.htm</result>
			<result name="compActuPage" type="chain">
				<param name="tokenId">${tokenId}</param>
				<param name="actionName">goCompanyActuality_front</param>
				<param name="namespace">/front/actuality</param>
			</result>
			<result name="goPersonal" type="chain">
				<param name="actionName">goPersonalActuality_front</param>
				<param name="namespace">/front/actuality</param>
			</result>
			<result name="goCompany" type="chain">
				<param name="actionName">goCompanyActuality_front</param>
				<param name="namespace">/front/actuality</param>
			</result>
			<result name="goToDoPage" type="chain">
				<param name="actionName">goPersonalActuality_front</param>
				<param name="namespace">/front/actuality</param>
			</result>
			<result name="goDonePage" type="chain">
				<param name="actionName">personalActuInfo_checkResult_front</param>
				<param name="namespace">/front/actuality</param>
			</result>
			<result name="goCafe" type="chain">
				<param name="actionName">cafeActu_goCafeInfo_front</param>
				<param name="namespace">/front/actuality</param>
			</result>
		</action>
		<!--~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                结束: 未实名认证,根据用户身份跳转到实名认证页面;
                已实名认证的,根据用户身份跳转到实名认证结果页面
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~-->



		<!-- 实名的身份证OCR识别 -->
		<action name="actuIdCardOcr" method="actuIdCardOcr" class="personalActuAction">
		</action>
		<!-- 实名认证确认页面，新版本使用 -->
		<action name="confirmPersonActu" method="confirmActu" class="personalActuAction">
			<result name="success">/front/actu/newPersonalActuInfo_commit_front.jsp</result>
			<result name="input">/front/actu/newPersonalActuInfo_front.jsp</result>
            <!--地址栏访问时返回的结果-->
            <result name="goPersonal" type="chain">
                <param name="actionName">goPersonalActuality_front</param>
                <param name="namespace">/front/actuality</param>
            </result>
            <result name="goCompany" type="chain">
                <param name="actionName">goCompanyActuality_front</param>
                <param name="namespace">/front/actuality</param>
            </result>
            <result name="goToDoPage" type="chain">
                <param name="actionName">goPersonalActuality_front</param>
                <param name="namespace">/front/actuality</param>
            </result>
            <result name="goDonePage" type="chain">
                <param name="actionName">personalActuInfo_checkResult_front</param>
                <param name="namespace">/front/actuality</param>
            </result>
            <result name="goCafe" type="chain">
                <param name="actionName">cafeActu_goCafeInfo_front</param>
                <param name="namespace">/front/actuality</param>
            </result>
		</action>
		<!-- 实名认证确认页面，老版本使用 -->
		<action name="confirmPersonalActuInfo_front" method="confirm" class="personalActuAction" >
			<result name="success">/front/actu/personalActuInfo_commit_front.jsp</result>
			<result name="input">/front/actu/personalActuInfo_front.jsp</result>
		</action>
		<!-- 提交个人实名认证 -->
		<action name="personalActuInfo_commit_front" method="executeActu" class="personalActuAction" >
			<result name="success">/front/actu/personalActuInfo_success_front.jsp</result>
			<result name="input" type="chain">
				<param name="actionName">goToPersonalEdit</param>
				<param name="namespace">/front/actuality</param>
			</result>
			<result name="fail">/front/actu/personalActuInfo_fail_front.jsp</result>
		</action>
		<!-- 返回修改 -->
		<action name="goToPersonalEdit" method="goToEdit" class="personalActuAction" >
			<result name="input" type="redirect">/front/actuality/goPersonalActuality_front.htm</result>
			<result name="success">/front/actu/personalActuInfo_front.jsp</result>
			<result name="newSuccess">/front/actu/newPersonalActuInfo_front.jsp</result>
		</action>
		<!-- 升级到企业账号-->
    	<action name="goUpgradeToCompanyActu" method="upgradeToCompanyActu" class="personalActuAction" >
			<result name="success">/front/actu/validatePhoneForActu.jsp</result>
			<result name="showProgress">/front/actu/companyActuInfo_checkResult_front.jsp</result>
		</action>


    	<action name="checkPersonalCertState_front" method="checkActuState" class="personalActuAction" >
		</action>
		<!-- 撤销实名认证 -->
		<action name="personalActuInfo_repeal" method="repeal" class="personalActuAction" >
			<result name="success" type="chain">
                <param name="actionName">goPersonalActuality_front</param>
                <param name="namespace">/front/actuality</param>
			</result>
		</action>

		<!-- 企业实名上传认证图片页面 -->
		<action name="companyUploadActuInfo_front" method="uploadInfoForActu" class="companyActuAction">
			<result name="success">/front/actu/newCompanyActuInfo_upload_info_front.jsp</result>
			<result name="input">/front/actu/newCompanyActuInfo_comp_Info_front.jsp</result>
		</action>
		<!-- 企业实名提交确认页面，老版本使用
		<action name="confirmCompanyActuInfo_front" method="confirm" class="companyActuAction" >
			<result name="success">/front/actu/CompanyConfirm_front.jsp</result>
			<result name="error">/front/actu/CompanyActuInfo_compInfo_front.jsp</result>
			<result name="input">/front/actu/CompanyActuInfo_compInfo_front.jsp</result>
		</action> -->
		<!-- 企业实名提交确认页面，新版本使用 -->
		<action name="confirmCompanyActu" method="confirmActu" class="companyActuAction">
			<result name="success">/front/actu/newCompanyConfirm_front.jsp</result>
			<result name="input">/front/actu/newCompanyActuInfo_upload_info_front.jsp</result>
		</action>
		<!-- 返回修改 -->
		<action name="goToCompanyEdit" method="goToCompanyEdit" class="companyActuAction" >
			<result name="input" type="redirect">/front/actuality/companyActu_goCompanyInfo_front.htm</result>
			<result name="success">/front/actu/CompanyActuInfo_compInfo_front.jsp</result>
			<result name="newSuccess">/front/actu/newCompanyActuInfo_comp_Info_front.jsp</result>
		</action>
		<!-- 提交保存实名信息 -->
		<action name="companyActu_commit_front" method="executeActu" class="companyActuAction" >
			<result name="input" type="chain">
				<param name="actionName">goToCompanyEdit</param>
				<param name="namespace">/front/actuality</param>
		    </result>
			<result name="success">/front/actu/companyActuInfo_success_front.jsp</result>
			<result name="fail">/front/actu/companyActuInfo_fail_front.jsp</result>
		</action>
    	<action name="checkCompanyCertState_front" method="checkActuState" class="companyActuAction" >
		</action>
		<!-- 撤销实名认证 -->
		<action name="companyActu_repeal" class="companyActuAction" method="repeal">
			<result name="success" type="chain">
				<param name="namespace">/front/actuality</param>
				<param name="actionName">companyActu_goCompanyInfo_front</param>
			</result>
		</action>



		<!-- 提交到基本信息 -->
        <action name="confirmCafeActuInfo_front" method="confirmActu" class="cafeActuAction" >
            <result name="success">/front/actu/CafeActuInfo_cafeInfo_upload_front.jsp</result>
            <result name="error">/front/actu/CafeActuInfo_cafeInfo_front.jsp</result>
            <result name="input">/front/actu/CafeActuInfo_cafeInfo_front.jsp</result>

            <!--地址栏访问时返回的结果-->
            <result name="goPersonal" type="chain">
                <param name="actionName">goPersonalActuality_front</param>
                <param name="namespace">/front/actuality</param>
            </result>
            <result name="goCompany" type="chain">
                <param name="actionName">goCompanyActuality_front</param>
                <param name="namespace">/front/actuality</param>
            </result>
            <result name="goToDoPage" type="chain">
                <param name="actionName">cafeActu_goCafeInfo_front</param>
                <param name="namespace">/front/actuality</param>
            </result>
            <result name="goDonePage" type="chain">
                <param name="actionName">cafeActuInfo_checkResult_front</param>
                <param name="namespace">/front/actuality</param>
            </result>
            <result name="goCafe" type="chain">
                <param name="actionName">cafeActu_goCafeInfo_front</param>
                <param name="namespace">/front/actuality</param>
            </result>
        </action>
		<!-- 提交上传的图片信息-->
		<action name="confirmCafeActuInfoUpload_front" method="confirmUpload" class="cafeActuAction" >
			<result name="success">/front/actu/CafeConfirm_front.jsp</result>
			<result name="error">/front/actu/CafeActuInfo_cafeInfo_upload_front.jsp</result>
			<result name="input">/front/actu/CafeActuInfo_cafeInfo_upload_front.jsp</result>
		</action>
		<!-- 返回修改 -->
        <action name="goToCafeEdit" method="goToEdit" class="cafeActuAction" >
            <result name="success">/front/actu/CafeActuInfo_cafeInfo_front.jsp</result>
			<result name="newSuccess">/front/actu/CafeActuInfo_cafeInfo_front.jsp</result>
        </action>
		<!-- 提交网吧实名认证 -->
        <action name="cafeActu_commit_front" method="executeActu" class="cafeActuAction" >
            <result name="input" type="chain">
                <param name="actionName">cafeActu_goCafeInfo_front</param>
                <param name="namespace">/front/actuality</param>
            </result>
            <result name="fail">/front/actu/cafeActuInfo_fail_front.jsp</result>
        </action>

        <action name="checkCafeCertState_front" method="checkActuState" class="cafeActuAction" >
        </action>
		<!-- 撤销网吧实名认证 -->
        <action name="cafeActu_repeal" class="cafeActuAction" method="repeal">
            <result name="success">/front/actu/validatePhoneForActu.jsp</result>
        </action>
        <action name="cafeActu_agreement" class="cafeActuAction" method="agreement">
			<result name="success" type="chain">
				<param name="actionName">cafeActu_goCafeInfo_front</param>
				<param name="namespace">/front/actuality</param>
			</result>
        </action>
	</package>
	
</struts>
