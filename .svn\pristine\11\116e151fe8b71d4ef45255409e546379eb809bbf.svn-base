<%@ page contentType="text/html;charset=UTF-8"%>
<%@ include file="/common/taglibs.jsp"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="Keywords" content="顺网通行证 ，密码， 找回， 手机，邮箱，其他方式， 重置" />
    <meta name="Description" content="提供手机，邮箱，其他找回密码的方式。" />
    <title>顺网通行证-申诉进度查询</title>

    <script type="text/javascript" src="${staticServer}/scripts/common/jquery.js"></script>
    <script type="text/javascript" src="${staticServer}/scripts/front/common/commonCheck.js"></script>
    <script type="text/javascript" src="${staticServer}/scripts/common/safeenter.js"></script>
    <script type="text/javascript" src="${staticServer}/scripts/front/member/min/validation.min.js"></script>
    <script type="text/javascript" src="${staticServer}/scripts/front/find/pwdfind_all_front.js"></script>

  </head>
  <body>
  <div class="c_head">
      <i class="forget_icon"></i>
      <span class="title">申诉进度查询</span>
  </div>
  <div class="c_body forget_s02">
      <div class="form_group">
          <form class="passform mar60 mart50 f14px" action="/front/noLogin/pwdFind_appeal_query_progress.htm" id="form1"
                method="post">
              <table cellpadding="0" cellspacing="0">
                  <thead>
                  <tr>
                      <th class="w_lg"></th>
                      <td> 请输入要查询的顺网通行证账号
                      </td>
                  </tr>
                  </thead>

                  <tbody>
                  <tr>
                      <th class="w_md_eq"><span class="not_null">*</span> 用户名：</th>
                      <td>
                          <input id="memberName" name="memberName" value="${memberName}"
                                 placeholder="用户名/邮箱/手机号" type="text" class="form_input" style="width:220px;"
                                 tabindex="1" value="" >
                          <!-- 用户名错误提示 -->
                          <em class="form_error" id="memberNameMsg">${fieldErrors.memberName[0]}</em>
                      </td>
                  </tr>
                  <tr>
                      <th class="w_md_eq"> 验证码：</th>
                      <td>
                          <input id="checkCode" name="checkCode" type="text" class="form_input" style="width:220px;"
                                 tabindex="2" value="" maxlength="4" style="width:60px">
                          <img class="imag" id="checkCodeImg" src=""/> <a id="changeCheckCodeLink" href="###"
                                                                          class=" f12px a035">看不清？换一张</a>
                          <!-- 验证码错误提示 -->
                          <em id="checkCodeMsg" class="form_error">${fieldErrors.checkCode[0]}</em>
                      </td>
                  </tr>
                  <tr>
                      <th></th>
                      <td><a id="btnGoNextStep" href="javascript:;" class="btn_default_lg">查询</a></td>
                  </tr>

                  </tbody>
              </table>
          </form>
      </div>
  </div>
  </body>
  <script type="text/javascript" charset="utf-8">
    $(function(){
    if (!('placeholder' in document.createElement('input'))) {
        $('.placeholder input[placeholder], .placeholder textarea[placeholder]').each(function(k, v) {
            var $obj = $(v),
                val = $obj.val(),
                placeholder = $obj.attr('placeholder');
            
            if (val == '') {
                $obj.val(placeholder);                   
            }
            
            $obj.focus(function() {
                if ($obj.val() === placeholder) {
                    $obj.val('');
                }
            }).blur(function() {
                val = $obj.val();
                if (val == '' || val == placeholder) {
                    $obj.val(placeholder);
                }
            });
        });
    }
    });
  </script>
</html>
