package com.shunwang.basepassport.actu.response;

import com.google.gson.annotations.Expose;
import com.shunwang.baseStone.core.response.BaseStoneResponse;
import com.shunwang.basepassport.actu.constant.ActuConstant;
import com.shunwang.basepassport.actu.pojo.PersonalActuVerifyRecord;
import com.shunwang.xmlbean.annotation.XmlInit;

public class IdCardActuResponse extends BaseStoneResponse {

    @Expose
    public String result;
    @Expose
    private String idCardNo;
    @Expose
    private String realName;
    @Expose
    private String state;
    @Expose
    private String antiAdditionEnabled;

    public IdCardActuResponse(PersonalActuVerifyRecord record, boolean switchEnabled) {
        antiAdditionEnabled = Boolean.valueOf(switchEnabled).toString();
        result = Boolean.FALSE.toString();
        if (record == null) {
            this.setMsg("认证信息不存在");
        } else {
            this.state = String.valueOf(record.getResult());
            if (record.isMatch()) {
                result = Boolean.TRUE.toString();
                this.realName = record.getRealName();
                this.idCardNo = record.getIdCardNo();
            } else {
                this.setMsg(ActuConstant.ID_CARD_ACTU_STATE_MAP.get(record.getResult()));
            }
        }
        setNeedSign(false);
    }

    @XmlInit
    public String getResult() {
        return result;
    }

    @XmlInit
    public String getIdCardNo() {
        return idCardNo;
    }

    @XmlInit
    public String getRealName() {
        return realName;
    }

    @XmlInit
    public String getAntiAdditionEnabled() {
        return antiAdditionEnabled;
    }
    @XmlInit
    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }
}
