package com.shunwang.baseStone.sso.weixin.oauth.service;

import com.shunwang.baseStone.cache.CacheKeyConstant;
import com.shunwang.baseStone.context.RedisOperation;
import com.shunwang.baseStone.sso.weixin.pojo.WeixinAuthorizerToken;
import com.shunwang.basepassport.weixin.pojo.WeixinOauthToken;
import com.shunwang.basepassport.weixin.service.WeixinOauthTokenService;
import com.shunwang.util.json.GsonUtil;
import com.shunwang.util.trace.TraceUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;

/**
 * User:pf.ma
 * Date:2020/01/13
 * Time:10:51
 */
public class WeixinRefreshService implements Runnable{

	private final static Logger logger = LoggerFactory.getLogger(WeixinRefreshService.class) ;

	private RedisOperation redisOperation;
	private WeixinOpenService weixinOpenService ;
	private WeixinOauthTokenService weixinOauthTokenService ;
	private WeixinOauthToken componentOauthToken ;
	private WeixinOauthToken oauthToken ;

	public WeixinRefreshService(RedisOperation redisOperation, WeixinOpenService weixinOpenService, WeixinOauthTokenService weixinOauthTokenService, WeixinOauthToken componentOauthToken, WeixinOauthToken oauthToken){
		this.redisOperation = redisOperation ;
		this.weixinOpenService = weixinOpenService ;
		this.weixinOauthTokenService = weixinOauthTokenService ;
		this.componentOauthToken = componentOauthToken ;
		this.oauthToken = oauthToken ;
	}

	@Override
	public void run() {
		TraceUtil.traceStart();
		refreshAuthorizeAccessToken();
		TraceUtil.traceEnd();
	}

	public void refreshAuthorizeAccessToken(){
		String cacheKey = CacheKeyConstant.SSO.WX_REFRESH_TOKEN_TASK + oauthToken.getAppId() ;
		if(!redisOperation.setNx(cacheKey, "", 60)){
			logger.info("刷新[{}]token的任务已经存在", oauthToken.getAppId()) ;
			return ;
		}
		try{
			logger.info("刷新[{}]的token", oauthToken.getAppId());
			WeixinAuthorizerToken authorizerAccessToken = getWeixinOpenService().freshAuthorizerAccessToken(oauthToken.getAppId(), componentOauthToken.getAccessToken(), oauthToken.getRefreshToken());
			if(StringUtils.isBlank(authorizerAccessToken.getAuthorizerAccessToken()) || StringUtils.isBlank(authorizerAccessToken.getAuthorizerRefreshToken())){
				logger.error("授权信息获取失败[{}]:{}", oauthToken.getAppId(), GsonUtil.toJson(authorizerAccessToken)) ;
				return ;
			}
			oauthToken.setAccessToken(authorizerAccessToken.getAuthorizerAccessToken()) ;
			oauthToken.setRefreshToken(authorizerAccessToken.getAuthorizerRefreshToken()) ;
			oauthToken.setExpireTime(authorizerAccessToken.getExpireTime()) ;
			oauthToken.setTimeEdit(new Date());
			getWeixinOauthTokenService().updateToken(oauthToken) ;
		} catch (Exception e) {
			logger.error("刷新[{}]的token异常", oauthToken.getAppId(), e);
		} finally {
			redisOperation.del(cacheKey) ;
		}
	}

	public WeixinOauthToken getOauthToken() {
		return oauthToken;
	}

	public void setOauthToken(WeixinOauthToken oauthToken) {
		this.oauthToken = oauthToken;
	}

	public WeixinOpenService getWeixinOpenService() {
		return weixinOpenService;
	}

	public void setWeixinOpenService(WeixinOpenService weixinOpenService) {
		this.weixinOpenService = weixinOpenService;
	}

	public WeixinOauthTokenService getWeixinOauthTokenService() {
		return weixinOauthTokenService;
	}

	public void setWeixinOauthTokenService(WeixinOauthTokenService weixinOauthTokenService) {
		this.weixinOauthTokenService = weixinOauthTokenService;
	}

	public WeixinOauthToken getComponentOauthToken() {
		return componentOauthToken;
	}

	public void setComponentOauthToken(WeixinOauthToken componentOauthToken) {
		this.componentOauthToken = componentOauthToken;
	}

	public RedisOperation getRedisOperation() {
		return redisOperation;
	}

	public void setRedisOperation(RedisOperation redisOperation) {
		this.redisOperation = redisOperation;
	}
}
