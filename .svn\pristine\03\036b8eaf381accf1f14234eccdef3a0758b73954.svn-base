package com.shunwang.baseStone.bussiness.pojo;

import com.shunwang.baseStone.bussiness.constant.BussinessConstant;
import com.shunwang.baseStone.bussiness.dao.BussinessDao;
import com.shunwang.baseStone.context.BackUserContext;
import com.shunwang.baseStone.context.BaseStoneContext;
import com.shunwang.baseStone.core.detail.Detail;
import com.shunwang.baseStone.core.detail.DetailItem;
import com.shunwang.baseStone.core.detail.HasDetail;
import com.shunwang.baseStone.core.pojo.BaseStoneObject;
import com.shunwang.baseStone.editlog.pojo.EditLog;
import com.shunwang.framework.annotation.NoJsInit;
import com.shunwang.util.date.DateUtil;

import java.io.Serializable;
import java.util.List;

public class Bussiness extends BaseStoneObject implements HasDetail {

	private static final long serialVersionUID = 8188185714619303592L;

	private String bussinesskey;
	private String bussinessname;
	private Integer bussinessstate=0;
	private Integer passportid;
	private String passportname;
	private String bussinessprefix="";
	private java.util.Date timeadd;
	private java.util.Date timeedit;
	private String useradd;
	private String useredit;
	private String remark;
	private String categoryid;
	private String categoryname;
	private String businesscategoryid;
	private String businesscategoryname;
	private Integer shareloginelement;
	private Integer accountType ;

	public EditLog editLog = new EditLog();

	public Bussiness(){
		
	}
	public List<Bussiness>initAppealMsg(){
		return getDao().findAll();
	}
	@NoJsInit
	public static BussinessDao getDao(){
		return (BussinessDao)BaseStoneContext.getInstance().getBean("bussinessDao");
	}
	public void setBussinesskey(String value){
		this.bussinesskey=value;
	}
	public String getBussinesskey(){
		return this.bussinesskey;
	}
	public void setBussinessname(String value){
		this.bussinessname=value;
	}
	public String getBussinessname(){
		return this.bussinessname;
	}
	public void  setBussinessstate(Integer value){
		this.bussinessstate=value;
	}
	public Integer getBussinessstate(){
		return this.bussinessstate;
	}
	public void setPassportid(Integer value){
		this.passportid=value;
	}
	public Integer getPassportid(){
		return this.passportid;
	}
	public void setPassportname(String value){
		this.passportname=value;
	}
	public String getPassportname(){
		return this.passportname;
	}
	public void setBussinessprefix(String value){
		this.bussinessprefix=value;
	}
	public String getBussinessprefix(){
		return this.bussinessprefix;
	}
	public void setTimeadd(java.util.Date value){
		this.timeadd=value;
	}
	public String getTimeaddStr() {
		if(timeadd != null && !"".equals(timeadd))
			return DateUtil.ymdhmsFormat(timeadd);
		return "";
	}
	public java.util.Date getTimeadd(){
		return this.timeadd;
	}
	public void setTimeedit(java.util.Date value){
		this.timeedit=value;
	}
	public String getTimeeditStr() {
		if(timeedit != null && !"".equals(timeedit))
			return DateUtil.ymdhmsFormat(timeedit);
		return "";
	}
	public java.util.Date getTimeedit(){
		return this.timeedit;
	}
	public void setUseradd(String value){
		this.useradd=value;
	}
	public String getUseradd(){
		return this.useradd;
	}
	public void setUseredit(String value){
		this.useredit=value;
	}
	public String getUseredit(){
		return this.useredit;
	}
	public void setRemark(String value){
		this.remark=value;
	}
	public String getRemark(){
		return this.remark;
	}
	
	public String getCategoryid() {
		return categoryid;
	}
	public void setCategoryid(String categoryid) {
		this.categoryid = categoryid;
	}
	public String getCategoryname() {
		return categoryname;
	}
	public void setCategoryname(String categoryname) {
		this.categoryname = categoryname;
	}
	
	public Integer getShareloginelement() {
		return shareloginelement;
	}
	public void setShareloginelement(Integer shareloginelement) {
		this.shareloginelement = shareloginelement;
	}
	/**
	 * 关闭
	 */
	public void close(){
		this.bussinessstate=BussinessConstant.S_Close;
		editLog.addItem(new DetailItem("状态", "打开", "关闭"));
		this.getDao().update(this);
	}
	/**
	 * 打开
	 */
	public void open(){
		this.bussinessstate=BussinessConstant.S_Open;
		editLog.addItem(new DetailItem("状态", "关闭", "打开"));
		this.getDao().update(this);
	}
	
	public boolean isOpen(){
		return this.bussinessstate.intValue()!=BussinessConstant.S_Close;
	}
	public String getBusinesscategoryid() {
		return businesscategoryid;
	}
	public void setBusinesscategoryid(String businesscategoryid) {
		this.businesscategoryid = businesscategoryid;
	}
	public String getBusinesscategoryname() {
		return businesscategoryname;
	}
	public void setBusinesscategoryname(String businesscategoryname) {
		this.businesscategoryname = businesscategoryname;
	}
	@Override
	public Detail getDetail() {
		return editLog;
	}
	@Override
	public void beginBuildLog() {
		String sysclass = this.getClass().toString();
		String conkey = sysclass.substring(sysclass.lastIndexOf(".") + 1,
				sysclass.length());
		editLog.beginBuildLog(true);
		editLog.setEditItem("商户管理");
		editLog.setConfKey(conkey);
		editLog.setUserAdd(BackUserContext.getUserName());
	}

	public Integer getAccountType() {
		return accountType;
	}

	public void setAccountType(Integer accountType) {
		this.accountType = accountType;
	}

	/**
	 * 默认为多账号
	 * @return
	 */
	public String getAccountTypeStr(){
		String accountTypeStr = BussinessConstant.ACCOUNT_TYPE.TYPE_MAP.get(accountType != null ? accountType:BussinessConstant.ACCOUNT_TYPE.MULTIPLE);
		if(null == accountTypeStr){
			return "" ;
		}
		return accountTypeStr ;
	}

	public Boolean isSingleAccount(){
		if(null != accountType && accountType.equals(BussinessConstant.ACCOUNT_TYPE.SINGLE)){
			return true ;
		}
		return false ;
	}

	@Override
	public Serializable getPk() {
		return bussinesskey;
	}
	
}
