package com.shunwang.basepassport.config.pojo;

import com.shunwang.baseStone.core.pojo.BaseStoneObject;
import com.shunwang.basepassport.config.constants.ConfigEnum;

import java.io.Serializable;
import java.util.Date;

/**
 * 业务类
 */
public class ApiApp extends BaseStoneObject {

	private Integer id;
	private String appId;
	private String appName;
	private Integer state;
	private String publicKey;
	private String privateKey;
	private String merchantPublicKey;
	private String merchantPrivateKey;
	private String aesKey;
	private Date timeAdd;
	private String userAdd;
	private Date timeEdit;
	private String userEdit;
	private String remark;
	private Integer accountType;
	private String sensitiveDataPublicKey;
	private String sensitiveDataPrivateKey;
	private Integer businessLineId;
	private Integer shareBusinessLineConfig;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getAppId() {
		return appId;
	}

	public void setAppId(String appId) {
		this.appId = appId;
	}

	public String getAppName() {
		return appName;
	}

	public void setAppName(String appName) {
		this.appName = appName;
	}

	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}

	public String getPublicKey() {
		return publicKey;
	}

	public void setPublicKey(String publicKey) {
		this.publicKey = publicKey;
	}

	public String getPrivateKey() {
		return privateKey;
	}

	public void setPrivateKey(String privateKey) {
		this.privateKey = privateKey;
	}

	public String getMerchantPublicKey() {
		return merchantPublicKey;
	}

	public void setMerchantPublicKey(String merchantPublicKey) {
		this.merchantPublicKey = merchantPublicKey;
	}

	public String getMerchantPrivateKey() {
		return merchantPrivateKey;
	}

	public void setMerchantPrivateKey(String merchantPrivateKey) {
		this.merchantPrivateKey = merchantPrivateKey;
	}

	public String getAesKey() {
		return aesKey;
	}

	public void setAesKey(String aesKey) {
		this.aesKey = aesKey;
	}

	public Date getTimeAdd() {
		return timeAdd;
	}

	public void setTimeAdd(Date timeAdd) {
		this.timeAdd = timeAdd;
	}

	public String getUserAdd() {
		return userAdd;
	}

	public void setUserAdd(String userAdd) {
		this.userAdd = userAdd;
	}

	public Date getTimeEdit() {
		return timeEdit;
	}

	public void setTimeEdit(Date timeEdit) {
		this.timeEdit = timeEdit;
	}

	public String getUserEdit() {
		return userEdit;
	}

	public void setUserEdit(String userEdit) {
		this.userEdit = userEdit;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public Integer getAccountType() {
		return accountType;
	}

	public void setAccountType(Integer accountType) {
		this.accountType = accountType;
	}

	public String getSensitiveDataPublicKey() {
		return sensitiveDataPublicKey;
	}

	public void setSensitiveDataPublicKey(String sensitiveDataPublicKey) {
		this.sensitiveDataPublicKey = sensitiveDataPublicKey;
	}

	public String getSensitiveDataPrivateKey() {
		return sensitiveDataPrivateKey;
	}

	public void setSensitiveDataPrivateKey(String sensitiveDataPrivateKey) {
		this.sensitiveDataPrivateKey = sensitiveDataPrivateKey;
	}

	public Integer getBusinessLineId() {
		return businessLineId;
	}

	public void setBusinessLineId(Integer businessLineId) {
		this.businessLineId = businessLineId;
	}

	public Integer getShareBusinessLineConfig() {
		return shareBusinessLineConfig;
	}

	public void setShareBusinessLineConfig(Integer shareBusinessLineConfig) {
		this.shareBusinessLineConfig = shareBusinessLineConfig;
	}

	public Boolean isSingleAccount(){
		return null != accountType && ConfigEnum.AccountType.SINGLE.getType() == accountType;
	}

	public Boolean isShareBusinessLineConfig(){
		return null != shareBusinessLineConfig && ConfigEnum.ShareBusinessLineConfig.SHARED.getType() == shareBusinessLineConfig;
	}

	public boolean isOpen() {
		return null != state && ConfigEnum.ConfigSwitch.ONE_OPEN.getValue() == state;
	}
	@Override
	public Serializable getPk() {
		return id;
	}
}
