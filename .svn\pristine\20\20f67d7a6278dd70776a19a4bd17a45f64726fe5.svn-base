<?xml version="1.0" encoding="UTF-8"?>

<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">
	
  <!-- action 层 -->
  <bean id="weixinFacade" class="com.shunwang.passport.weixin.action.WeixinFacade" scope="prototype" >
		<property name="weixinConf" ref="weixinConf" />
		<property name="weixinMsgProcesser" ref="weixinMsgProcesser" />
  </bean>

  <bean id="weixinAuthAction" class="com.shunwang.passport.weixin.action.WeixinAuthAction" scope="prototype" >
		<property name="weixinRemoteCall" ref="weixinRemoteCall" />
		<property name="myAccountService" ref="myAccountService" />
  </bean>

  <bean id="myAccountAction" class="com.shunwang.passport.weixin.business.MyAccountAction" scope="prototype" >
		<property name="myAccountService" ref="myAccountService" />
  </bean>

  <bean id="mobileSecurityAction" class="com.shunwang.passport.weixin.business.MobileSecurityAction" scope="prototype" >
		<property name="myAccountService" ref="myAccountService" />
		<property name="mobileSecurityService" ref="mobileSecurityService" />
  </bean>

  <!-- action 层 结束 -->


  <!-- service 层 -->
  <bean id="weixinResourceService" class="com.shunwang.passport.weixin.service.WeixinResourceService" >
  </bean>

  <!-- 我的账户 -->
  <bean id="myAccountService" class="com.shunwang.passport.weixin.service.MyAccountService" >
		<property name="memberDao" ref="memberDao" />
		<property name="memberOutSiteDao" ref="memberOutSiteDao" />
		<property name="weixinRemoteCall" ref="weixinRemoteCall" />
  </bean>

  <!-- 手机密保相关业务 -->
  <bean id="mobileSecurityService" class="com.shunwang.passport.weixin.service.MobileSecurityService" >
		<property name="memberDao" ref="memberDao" />
		<property name="mobileBinderDao" ref="mobileBinderDao" />
		<property name="personalSendNumberDao" ref="personalSendNumberDao" />
  </bean>

  <!-- 签到 -->
  <bean id="signInService" class="com.shunwang.passport.weixin.service.SignInService" >
		<property name="memberDao" ref="memberDao" />
		<property name="weixinResourceService" ref="weixinResourceService" />
  </bean>

  <!-- 最近验证码查询 -->
  <bean id="verifyCodeQueryService" class="com.shunwang.passport.weixin.service.VerifyCodeQueryService" >
		<property name="memberDao" ref="memberDao" />
		<property name="mobileBinderDao" ref="mobileBinderDao" />
		<property name="mobileCheckCodeDao" ref="mobileCheckCodeDao" />
		<property name="regActiveNoDao" ref="regActiveNoDao" />
		<property name="queryPeriod" value="${verifycode.queryPeriod}" />
		<property name="weixinResourceService" ref="weixinResourceService" />
  </bean>

  <!-- 微信消息处理 -->
  <bean id="weixinMsgProcesser" class="com.shunwang.passport.weixin.service.WeixinMsgProcesser" >
		<property name="signInService" ref="signInService" />
		<property name="verifyCodeQueryService" ref="verifyCodeQueryService" />
		<property name="weixinResourceService" ref="weixinResourceService" />
  </bean>

  <!-- 微信接口调用 -->
  <bean id="weixinRemoteCall" class="com.shunwang.passport.weixin.service.WeixinRemoteCall" >
		<property name="weixinConf" ref="weixinConf" />
		<property name="weixinTokenInfoDao" ref="weixinTokenInfoDao" />
  </bean>
  <!-- service 层 结束-->

	
  <!-- 微信接口等的配置 -->
  <bean id="weixinConf" class="com.shunwang.passport.weixin.pojo.WeixinConf" >
		<property name="appid" value="${weixin.appid}" />
		<property name="appsecret" value="${weixin.appsecret}" />
		<property name="submitToken" value="${weixin.submit.token}" />
		<property name="domain" value="${weixin.domain}" />
  </bean>

</beans>
