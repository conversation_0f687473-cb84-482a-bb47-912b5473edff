<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">
	<bean id="sysconfigDbDao" class="com.shunwang.baseStone.sysconfig.dao.SysconfigDbDao"  >
		<property name="sqlMapClient" ref="baseStone.sqlMapClient"></property>
	</bean>
	<bean id="sysconfigCacheDao" class="com.shunwang.baseStone.sysconfig.dao.SysconfigCacheDao">
	</bean>
	<bean id="sysconfigDao" class="com.shunwang.baseStone.sysconfig.dao.SysconfigDao"  >
		<property name="dbDao" ref="sysconfigDbDao"></property>
		<property name="cacheDao" ref="sysconfigCacheDao"></property>
	</bean>

</beans>
