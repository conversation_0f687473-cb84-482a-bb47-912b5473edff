package com.shunwang.basepassport.business.response;

import com.google.gson.annotations.Expose;
import com.shunwang.baseStone.bussiness.pojo.Bussiness;
import com.shunwang.baseStone.core.response.BaseStoneResponse;
import com.shunwang.xmlbean.annotation.XmlInit;
import org.apache.commons.lang.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.jdom.Document;
import org.jdom.output.Format;
import org.jdom.output.XMLOutputter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;

/**
 * 站点信息响应
 *
 * <AUTHOR>
 * @since 2013-10-31
 */
public abstract class BusinessResponse extends BaseStoneResponse {

    protected final Logger LOG = LoggerFactory.getLogger(getClass());

    protected static final String FORMAT = "yyyy-MM-dd HH:mm:ss";

    protected BusinessItem convert(Bussiness bussiness) {
        BusinessItem item = new BusinessItem();
        item.setKey(bussiness.getBussinesskey());
        item.setName(bussiness.getBussinessname());
        item.setState(bussiness.getBussinessstate());
        if (bussiness.getTimeadd() != null) {
            item.setTimeAdd(DateFormatUtils.format(bussiness.getTimeadd(), FORMAT));
        } else {
            item.setTimeAdd("");
        }
        if (bussiness.getTimeedit() != null) {
            item.setTimeEdit(DateFormatUtils.format(bussiness.getTimeedit() , FORMAT));
        } else {
            item.setTimeEdit("");
        }

        return item;
    }

    public void process(HttpServletRequest request,HttpServletResponse response){
        super.noNeedSignWithSignVersion(request);
        if (request != null && TYPE_JSON.equals(request.getHeader("Accept"))) {
            super.process(request, response);
        } else {
            response.setContentType("text/xml;charset=UTF-8");
            response.setCharacterEncoding("UTF-8");
            try {
                OutputStream out = response.getOutputStream();
                Document doc = processDocument();

                XMLOutputter xmlOutput = new XMLOutputter();
                xmlOutput.setFormat(Format.getPrettyFormat());
                xmlOutput.output(doc, out);
            } catch (IOException e) {
                LOG.error("output xml response error: " , e);
            }
        }
    }

    protected abstract Document processDocument();

    class BusinessItem {
        @Expose
        private String key; // 站点ID
        @Expose
        private String name; // 站点名称
        @Expose
        private int state;   // 启用状态：0=启用，1=停用
        @Expose
        private String timeAdd; // 添加时间
        @Expose
        private String timeEdit; // 修改时间

        @XmlInit
        public String getKey() {
            return key;
        }

        public void setKey(String key) {
            this.key = key;
        }

        @XmlInit
        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        @XmlInit
        public int getState() {
            return state;
        }

        public void setState(int state) {
            this.state = state;
        }

        @XmlInit
        public String getTimeAdd() {
            return timeAdd;
        }

        public void setTimeAdd(String timeAdd) {
            this.timeAdd = timeAdd;
        }

        @XmlInit
        public String getTimeEdit() {
            return timeEdit;
        }

        public void setTimeEdit(String timeEdit) {
            this.timeEdit = timeEdit;
        }
    }
}
