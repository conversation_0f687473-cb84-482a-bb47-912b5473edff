<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd" >
<sqlMap namespace="com.shunwang.baseStone.sensitiveword.pojo.SensitiveWord">
<resultMap class="com.shunwang.baseStone.sensitiveword.pojo.SensitiveWord" id="BaseResultMap">
	<result property="wordId" column="wordId" jdbcType="int"/>
	<result property="word" column="word" jdbcType="varchar"/>
	<result property="state" column="state" jdbcType="tinyint"/>
	<result property="timeAdd" column="timeAdd" jdbcType="datetime"/>
	<result property="timeEdit" column="timeEdit" jdbcType="datetime"/>
	<result property="userAdd" column="userAdd" jdbcType="varchar"/>
	<result property="userEdit" column="userEdit" jdbcType="varchar"/>
	<result property="remark" column="remark" jdbcType="varchar"/>
</resultMap>
<select id="find" resultMap="BaseResultMap" parameterClass="com.shunwang.framework.ibatis.query.ConditionQuery" >
	SELECT
		t.wordId,
		t.word,
		t.state,
		t.timeAdd,
		t.timeEdit,
		t.userAdd,
		t.userEdit,
		t.remark	from config_sensitiveWord t
	<isParameterPresent >
	<include refid="Example_Where_Clause" />
	</isParameterPresent>
	order by
	<isNotNull property="orderCol" >
		$orderCol$
	</isNotNull>
	<isNull property="orderCol" >
		wordId desc 
	</isNull>
	<isNotEqual property="rp" compareValue="0" >
	    limit #firstResult#, #rp#
	</isNotEqual>
</select>
<insert id="insert" parameterClass="com.shunwang.baseStone.sensitiveword.pojo.SensitiveWord" >
	insert into config_sensitiveWord (
		word,
		state,
		timeAdd,
		timeEdit,
		userAdd,
		userEdit,
		remark	)values(
		#word:varchar#,
		#state:tinyint#,
		#timeAdd:datetime#,
		#timeEdit:datetime#,
		#userAdd:varchar#,
		#userEdit:varchar#,
		#remark:varchar#	)
	<selectKey resultClass="java.lang.Integer" keyProperty="wordId" >
	    SELECT LAST_INSERT_ID()
	</selectKey>
</insert>
<update id="update" parameterClass="com.shunwang.baseStone.sensitiveword.pojo.SensitiveWord" >
	update config_sensitiveWord set
	<isNotEmpty  property="word"> word=#word:varchar# </isNotEmpty>	
	<isNotEmpty prepend=","  property="state"> state=#state:tinyint# </isNotEmpty>	
	<isNotEmpty prepend="," property="timeAdd"> timeAdd=#timeAdd:datetime# </isNotEmpty>	
	<isNotEmpty prepend="," property="timeEdit"> timeEdit=#timeEdit:datetime# </isNotEmpty>	
	<isNotEmpty prepend="," property="userAdd"> userAdd=#userAdd:varchar# </isNotEmpty>	
	<isNotEmpty prepend="," property="userEdit"> userEdit=#userEdit:varchar# </isNotEmpty>		
	<isNotEmpty prepend="," property="remark"> remark=#remark:varchar# </isNotEmpty>		
		 where wordId = #wordId:int#
</update>
<delete id="delete" parameterClass="com.shunwang.baseStone.sensitiveword.pojo.SensitiveWord" >
	delete from config_sensitiveWord where wordId=#wordId:int#
</delete>
<select id="get" resultMap="BaseResultMap" parameterClass="Integer">
	select
	wordId,
	word,
	state,
	timeAdd,
	timeEdit,
	userAdd,
	userEdit,
	remark	from config_sensitiveWord
	where wordId = #value#
</select>
<select id="findCnt" parameterClass="com.shunwang.framework.ibatis.query.ConditionQuery" resultClass="java.lang.Integer" >
	select count(*) from config_sensitiveWord t
	<include refid="Example_Where_Clause" />
</select>
<select id="getsensitivWordBysWord" resultMap="BaseResultMap" parameterClass="java.lang.String">
	select
	wordId,
	word,
	state,
	timeAdd,
	timeEdit,
	userAdd,
	userEdit,
	remark	from config_sensitiveWord
	where word = #word:varchar#
</select>
</sqlMap>