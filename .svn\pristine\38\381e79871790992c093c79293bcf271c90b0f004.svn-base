<%@ page contentType="text/html;charset=UTF-8" language="java" trimDirectiveWhitespaces="true" %>
<%@ include file="/common/taglibs.jsp"%>
<!DOCTYPE html>
<html>
<head>
    <title>换绑<c:if test="${target=='Mobile'}">手机</c:if><c:if test="${target=='Email'}">邮箱</c:if></title>
</head>
<body>
<content tag="header_title">换绑<c:if test="${target=='Mobile'}">手机</c:if><c:if test="${target=='Email'}">邮箱</c:if></content>
<div class="nav">
	<ul>
		<li>选择验证方式</li>
		<li class="active">安全验证</li>
		<li>换绑<c:if test="${target=='Mobile'}">手机</c:if><c:if test="${target=='Email'}">邮箱</c:if></li>
	</ul>
</div>
<div class="tips-error" style="display: none;">
	<p>${msg}</p>
</div>
<form action="${appServer}/front/swpaysdk/change${target}ByMobile.htm" method="post" id="js-form" class="form-box">
	<div class="form-group inline-group">
		<input type="label" name="oldNumber" value="${member.mobileSecurityPolicyStr}" readonly="readonly" class="form-control">
		<button id="sent-code" type="button" class="btn btn-primary btn-mini">发送验证码</button>
	</div>
	<div class="form-group"><input type="tel" name="activeNo" placeholder="请输入手机短信中的验证码" class="form-control"></div>
	<div class="other-group btn-box"><button id="verity" type="submit" class="btn btn-primary">验证</button></div>
</form>
<content tag="scripts">
	<script src="${staticServer}/scripts/front/swpaysdk/member/src/verifyOldMobile.js" type="text/javascript" ></script>
	<script src="${staticServer}/scripts/front/member/gt.js" type="text/javascript" ></script>
	<script src="${staticServer}/scripts/front/member/gtUtil.js" type="text/javascript" ></script>
	<script>
		//以下极验业务---------------------------
		var showGt = "true" == '${showGt}';
		var gtRegisterUrl = "/front/common/gtRegister.htm?r=" + new Date().getTime();
		var bindGt = $.gtUtil({
					"showGt": showGt,
					"formId": "js-form",
					"gtRegisterUrl": gtRegisterUrl,
					"btnId": "sent-code"
				},
				function () {
					sendCode();
				},
				function() {
					return true;
				},
				function (msg) {
					$('#tips-error p').html(msg);
					$('#tips-error').show();
				}
		);
	</script>
</content>
</body>
</html>
