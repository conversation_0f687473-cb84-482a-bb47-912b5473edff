$import('SFGridView');
$import('SFGrid');
$import('SFInput');
$import('SFEditor');
$import('SFWindow');
$import('SFFormWindow');
$import('SFBusUtil');
$import('SFTextArea');
$import('ValidateRule');
$import('SFSelect');
$import('AjaxUtil');

var grid;
var updateWin;
//控件
var PageHomeView = $createClass('PageHomeView',function(){	
	this.pk =  new SFInput({field:'moduleKey',name:'moduleKey'});
	this.moduleName= new SFInput({field:'moduleName',name:'模块名称',readOnly:true});	
	
	//搜索条件
	var schModuleName=new SFSelect({field:'schModuleName','name': "模块名称:",needdefault:true,items:''});	
	var schSiteType=new SFSelect({field:'schSiteType','name': "所属平台:",
				needdefault:true,items:pageHomeUtil.getSiteTypeItems()
				,onchange:function(event){ 																
							schModuleName.setItems(pageHomeUtil.getSiteItems(schSiteType.getValue())); 
										}  
									});
     		
	this.pojoControls = [
						    this.moduleName,   
							new SFInput({field:'siteType',name:'所属平台',readOnly:true}),	
							new SFEditor({
								field:'moduleContentNew',
								uploadUrl:'ckEditor.do?fileDir=back',
					            name: '内容编辑'              
					        }),
					        new SFTextArea({field:'remark',name:'备注', width:"380px",rules:[	new CheckMaxLength({length:10})]})
					    ];
	this.schControl =  [	                   
	                    schSiteType,schModuleName             
	                    ];
	this.pojoHidden=['moduleKey'];
	this.SFGridView()	
	},
	'SFGridView');

//表格
PageHomeView.prototype.buildGrid = function(){
	 grid = new SFGrid({
		url:'listPageHome.do',
		col:[
		    {id:'moduleName',text:'模块名称'},		
			{id:'siteTypeShow',text:'所属平台'},
			{id:'moduleStateShow',text:'状态'},
			{id:'userCheck',text:'审核人'},
			{id:'timeCheck',text:'审核时间'},	
			{id:'userAdd',text:'添加人'},
			{id:'timeAdd',text:'添加时间'},
			{id:'userEdit',text:'编辑人'},
			{id:'timeEdit',text:'编辑时间'},
			{id:'rejectRemarkShow',text:'拒绝理由',jsfun:'viewRefuse'},			
			{id:'remarkShow',text:'备注'}
			
			],
		linebutton:[
			//this.createUpdateBtn(),
			this.createCheckBtn(),
			this.createUpdateBtn2(),
			this.doPreview()
					]
	 				});
	return grid;
}

//审核面板
PageHomeView.prototype.createCheckBtn = function(){
	 var self = this;
    return {    
               text:'审核', onclick:function(data){    	
                   checkWin = new SFFormWindow({                  
                   title: '审核',
                   btns: [
                       {text: '审核通过', onclick: function() {self.checkPass(data,checkWin.getValue());checkWin.closeWin(); }},
                       {text: '拒绝通过', onclick: function(){self.refuseFront(data);}},
                       {text: '预览', onclick: function(){self.noSavePreview(checkWin.getValue(),'check')}},
                       {text: '取消', onclick:function(){checkWin.closeWin();}}
                   ]
                   });
                   checkWin.add( new SFInput({field:'moduleName',name:'模块名称',readOnly:true}));
                   checkWin.add( new SFInput({field:'siteTypeShow',name:'所属平台',readOnly:true}));
                   checkWin.add( new SFInput({field:'filePath',name:'预览路径',readOnly:true}));
                   checkWin.add( new SFInput({field:'pageType',name:'唯一标识',readOnly:true}));
                   checkWin.add(
                		   new SFEditor({
								field:'moduleContentNew',
								uploadUrl:'ckEditor.do?fileDir=back',
					            name: '内容编辑'					           
					        })                		  
                   );
                   checkWin.add(                		 
					        new SFTextArea({field:'remark',name:'备注',width:"380px",height:"50px"})						
                   );
                    checkWin.show();
                    checkWin.setValue(data);
               },
               
               showFun:function(data){
               	if(data.moduleState==1)
					return true;
               	else
               		return false;
				}
           }
}


//修改面板
PageHomeView.prototype.createUpdateBtn2 = function(){
	 var self = this;	 
   return {    
              text:'修改', onclick:function(data){				   
                  updateWin = new SFFormWindow({                  
                  title: '修改',
                  btns: [
                      {text: '提交审核', onclick: function(){self.update(updateWin.getValue());}},
                      {text: '恢复', onclick: function(){
							                    	  if(data.moduleState=='2'||data.moduleState=='3'){     
							                    	      alert('内容已通过审核或已拒绝，不能进行恢复！');
							                    	   }
							                    	  else if(data.moduleContentNew==null||""==data.moduleContentNew){
							                    		  alert('内容没被编辑过，不能进行恢复！');
							                    	  }
							                    	  else{
							                    		if(confirm("恢复后你的数据将恢复到当前网站发布的内容，确定要进行恢复操作？"))
							                    		{							                    	    
							                    			updateWin.setValue({moduleContentCur:data.moduleContentCur})
							                    		}
							                    }                    	  												
                    					}
                      },
                      {text: '预览',onclick: function(){self.noSavePreview(updateWin.getValue(),'update')}},
                      {text: '取消', onclick:function(){updateWin.closeWin();}}
                  ]
                  });
                  updateWin.add(new SFInput({field:'moduleName',name:'模块名称',readOnly:true}));
                  updateWin.add(new SFInput({field:'moduleKey',name:'模块编号',readOnly:true}));
                  updateWin.add( new SFInput({field:'siteTypeShow',name:'所属平台',readOnly:true}));
                  updateWin.add( new SFInput({field:'filePath',name:'预览路径',readOnly:true}));
                  updateWin.add( new SFInput({field:'pageType',name:'唯一标识',readOnly:true}));
                  updateWin.add(
                		  new SFEditor({
                				field:'moduleContentCur',
                				uploadUrl:'ckEditor.do?fileDir=back',
                		        name: '内容编辑'					           
                		    }) 
                  );
                  updateWin.add(                		 
					        new SFTextArea({field:'remark',name:'备注',width:"380px",height:"50px"})						
                  );                 
                  updateWin.show();                
                  updateWin.setValue(data);
                  if(data.moduleState=='1'&&null!=data.moduleContentNew||""!=data.moduleContentNew){                	  
					   updateWin.setValue({moduleContentCur:data.moduleContentNew});
			 	   }
                  if(data.moduleState=='3'){                	  
                	  updateWin.setValue({moduleContentCur:data.moduleContentCur});
                  }
                  
              }            
          }
}


//拒绝
PageHomeView.prototype.refuseFront = function(data){
	var self = this;
    var refuseWin = new SFFormWindow({
        title: '拒绝',
        btns: [
		          {text: '确定', onclick:function(){
		        	  self.refuse(data, refuseWin.getValue());
		        	  refuseWin.closeWin();
		        	  checkWin.closeWin();
		        	  }
		          },
		          {text: '取消', onclick:function(data){refuseWin.closeWin();checkWin.closeWin()}}
              ]
        });
        refuseWin.add(
        	new SFSelect({field:'reasonId','name': "拒绝理由:",needdefault:true,items:pageHomeUtil.getRefuseItems()})        
        );
        refuseWin.show();
}

PageHomeView.prototype.preview = function() {
	window.location = "http://passport.kedou.com//";
}

//拒绝操作
PageHomeView.prototype.refuse = function(data,value){		
	value['moduleKey'] = data['moduleKey'];	
	value['pageType'] = data['pageType'];	
	value['reasonId'] = value.reasonId;	
    this.trigger('refuse', value);
    
}
//修改
PageHomeView.prototype.update = function(data){		
	if(data.remark.length>80){
		alert("备注长度不能超80个字符");
		return false;
	}else{
		updateWin.closeWin();
		this.trigger('update', data);	}
	
}

//审核通过
PageHomeView.prototype.checkPass = function(data,value){	
	value['moduleKey'] = data['moduleKey'];	
	value['remark'] = value.remark;		
	value['moduleContentNew'] = value.moduleContentNew;		
	this.trigger('checkPass', value);
	
}

//查看拒绝理由
function viewRefuse(i){
	var self = this;
	var data =grid.getData(i);	
    var viewRefuseWin = new SFFormWindow({
        title: '查看拒绝理由',
        btns: [
		          {text: '确定', onclick:function(){
		        	  viewRefuseWin.closeWin();		        	
		        	  }
		          }
              ]
        });
    viewRefuseWin.add(
        	new SFTextArea({field:'reasonId','name': "拒绝理由:",value:data.content, width:"380px"})        
        );
        viewRefuseWin.show();
	
}

//预览按键
PageHomeView.prototype.doPreview = function(){
	var self=this;	
	return { text:'预览',onclick: function(data){self.noSavePreview(data,'other')} }	
}

PageHomeView.prototype.preview = function(data){
    this.trigger('preview', data);
}

//预览操作
PageHomeView.prototype.noSavePreview = function(data,type){
	var form=document.getElementById("form1").action=data.filePath+"?PageHome_preview=true&previewKey="+data.pageType;
	if(data.pageType == 'login_service' || data.pageType == 'login_pay') {
		if(type != 'other'){
			document.getElementById("form1").action = document.getElementById("form1").action+"&previewSave=no";
			this.preview(data);
        }
    }else{
    	if(type=='update')
    		saveTmp(data,data.moduleContentCur);
    	else if(type=='check')
    		saveTmp(data,data.moduleContentNew);
    	else {
    		document.getElementById("form1").action=document.getElementById("form1").action+"&tmp=no";
    		
    	}
    }
	document.getElementById("form1").submit();	
}

function saveTmp(data,value){
	var dataSubmit = {"pageType":data.pageType,"moduleContentCur":value};
	$.ajax( {
		url :"/pageHome/previewPageHome.do",
		data : dataSubmit,
		dataType : 'json',
		type : 'post',
		async:false,
		traditional:true,
		success : function() {
		},
		error : function() {
		}
	});
}



