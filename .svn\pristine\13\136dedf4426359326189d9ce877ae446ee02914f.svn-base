package com.shunwang.passport.common.action;

import com.google.gson.Gson;
import com.shunwang.baseStone.cache.CacheKeyConstant;
import com.shunwang.baseStone.context.RedisContext;
import com.shunwang.baseStone.richText.pojo.RichText;
import com.shunwang.common.StringUtils;
import com.shunwang.framework.struts2.action.BaseAction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 所有协议访问，协议内容数据加载
 **/
public class AgreementAction extends BaseAction {
    private static final Logger LOG = LoggerFactory.getLogger(AgreementAction.class);


    static Gson gson = new Gson();

    private RichText agreement;


    static RichText closed = new RichText();

    static {
        closed.setState(0);
    }

    /**
     * 顺网通行证隐私政策
     *
     * @return
     */
    public String privacy() {
        loadAgreement(CacheKeyConstant.SiteId.PASSPORT, CacheKeyConstant.Agreement.PRIVACY,
                CacheKeyConstant.Platform.WEB);
        return SUCCESS;
    }


    /**
     * 顺网通行证隐私政策
     *
     * @return
     */
    public String plainPrivacy() {
        loadAgreement(CacheKeyConstant.SiteId.PASSPORT, CacheKeyConstant.Agreement.PRIVACY,
                CacheKeyConstant.Platform.WEB);
        return SUCCESS;
    }

    /**
     * pay隐私政策
     *
     * @return
     */
    public String payPrivacy() {
        loadAgreement(CacheKeyConstant.SiteId.PAY, CacheKeyConstant.Agreement.PRIVACY, CacheKeyConstant.Platform.WEB);
        return SUCCESS;
    }


    /**
     * pay隐私政策
     *
     * @return
     */
    public String payPlainPrivacy() {
        loadAgreement(CacheKeyConstant.SiteId.PAY, CacheKeyConstant.Agreement.PRIVACY,
                CacheKeyConstant.Platform.WEB);
        return SUCCESS;
    }

    /**
     * 用户注册服务协议
     *
     * @return
     */
    public String userRegister() {
        loadAgreement(CacheKeyConstant.SiteId.PASSPORT, CacheKeyConstant.Agreement.USER_REGISTER,
                CacheKeyConstant.Platform.WEB);
        return SUCCESS;
    }

    /**
     * 用户注册服务协议h5
     *
     * @return
     */
    public String userAgreementH5() {
        loadAgreement(CacheKeyConstant.SiteId.PASSPORT, CacheKeyConstant.Agreement.USER_REGISTER,
                CacheKeyConstant.Platform.h5);
        return SUCCESS;
    }

    /**
     * 顺网通行证注销协议
     *
     * @return
     */
    public String memberCancel() {
        loadAgreement(CacheKeyConstant.SiteId.PASSPORT, CacheKeyConstant.Agreement.MEMBER_CANCEL,
                CacheKeyConstant.Platform.WEB);
        return SUCCESS;
    }

    /**
     * 顺网通行证注销协议
     *
     * @return
     */
    public String memberCancelH5() {
        loadAgreement(CacheKeyConstant.SiteId.PASSPORT, CacheKeyConstant.Agreement.MEMBER_CANCEL,
                CacheKeyConstant.Platform.h5);
        return SUCCESS;
    }

    /**
     * 顺网通行证实名认证服务协议
     *
     * @return
     */
    public String actuality() {
        loadAgreement(CacheKeyConstant.SiteId.PASSPORT, CacheKeyConstant.Agreement.ACTUALITY,
                CacheKeyConstant.Platform.WEB);
        return SUCCESS;
    }

    /**
     * 顺网通行证实名认证服务协议
     *
     * @return
     */
    public String actualityPlain() {
        loadAgreement(CacheKeyConstant.SiteId.PASSPORT, CacheKeyConstant.Agreement.ACTUALITY,
                CacheKeyConstant.Platform.WEB);
        return SUCCESS;
    }

    /**
     * 顺网通行证实名认证变更服务协议
     *
     * @return
     */
    public String actualityChange() {
        loadAgreement(CacheKeyConstant.SiteId.PASSPORT, CacheKeyConstant.Agreement.ACTUALITY_CHANGE,
                CacheKeyConstant.Platform.WEB);
        return SUCCESS;
    }

    /**
     * 顺网通行证防沉迷认证服务协议
     *
     * @return
     */
    public String antiAddition() {
        loadAgreement(CacheKeyConstant.SiteId.PASSPORT, CacheKeyConstant.Agreement.ANTI_ADDITION,
                CacheKeyConstant.Platform.WEB);
        return SUCCESS;
    }

    private void loadAgreement(String siteId, String agreement, String platform) {
        String agreementConfig = RedisContext.getAgreementCache().getAgreementConfig(siteId, agreement, platform);
        if (LOG.isDebugEnabled()) {
            LOG.debug("[{}],[{}]获取的协议[{}]", agreement, platform, agreementConfig);
        }

        if (StringUtils.isBlank(agreementConfig)) {
            this.agreement = closed;
            LOG.info("{},{}协议配置为空", agreement, platform);
        }
        try {
            this.agreement = gson.fromJson(agreementConfig, RichText.class);
        } catch (Exception e) {
            this.agreement = closed;
            LOG.error("解析配置错误{},{}", agreement, platform, e);
        }
    }


    public RichText getAgreement() {
        return agreement;
    }

    public void setAgreement(RichText agreement) {
        this.agreement = agreement;
    }
}
