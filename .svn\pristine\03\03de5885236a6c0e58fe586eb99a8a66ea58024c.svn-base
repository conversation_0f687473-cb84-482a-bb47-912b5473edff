package com.shunwang.basepassport.email.dao;

import com.shunwang.baseStone.core.dao.BaseStoneIbatisDao;
import com.shunwang.basepassport.email.pojo.EmailCheckCode;
import com.shunwang.framework.exception.WinterException;

public class EmailCheckCodeDao extends BaseStoneIbatisDao<EmailCheckCode> {

	
    public EmailCheckCode findByNameAndType(EmailCheckCode emailCheckCode) {
		return (EmailCheckCode) getSqlMapClientTemplate().queryForObject(getStatementNameWrap("findByNameAndType"), emailCheckCode);
	}
    
    public Integer findCntByTime(EmailCheckCode emailCheckCode) {
		return (Integer) getSqlMapClientTemplate().queryForObject(getStatementNameWrap("findCntByTime"), emailCheckCode);
	}
	public EmailCheckCode getLast(EmailCheckCode checkCode) {
		return (EmailCheckCode) getSqlMapClientTemplate().queryForObject(getStatementNameWrap("getLast"), checkCode);
	}
	public Integer getCntByDay(EmailCheckCode checkCode) {
		return (Integer) getSqlMapClientTemplate().queryForObject(getStatementNameWrap("getCntByDay"), checkCode);
	}
    /**
     * 按时间排序,得到最近发送的验证码
    */
    public EmailCheckCode getRecent(Integer memberId) {
		return (EmailCheckCode) getSqlMapClientTemplate().queryForObject(getStatementNameWrap("getRecent"),memberId);
	}

	public EmailCheckCode updateToUse(EmailCheckCode p) throws WinterException {
		this.getSqlMapClientTemplate().update(this.getStatementNameWrap("updateToUse"), p);
		return p;
	}
}
