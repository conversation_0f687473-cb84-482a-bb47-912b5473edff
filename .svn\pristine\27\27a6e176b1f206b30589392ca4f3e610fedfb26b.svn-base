package com.shunwang.basepassport.user.exception;

import com.shunwang.baseStone.core.exception.BaseStoneException;
/**
 * @Description:用户名为敏感词异常
 * <AUTHOR>  create at 2011-7-26 上午11:05:30
 * @FileName com.shunwang.basepassport.user.exception.UserNameSensitiveExp.java
 */
public class UserNameSensitiveExp extends BaseStoneException {

	/**
	 * <AUTHOR> create at 2011-7-26 上午11:05:51 
	 */
	private static final long serialVersionUID = 3152204502347855648L;
	
	public UserNameSensitiveExp() {
		super("2002", "用户名不能为敏感词！");
	}

	public UserNameSensitiveExp(String errorMsg) {
		super("2002", errorMsg);
	}

}
