package com.shunwang.basepassport.config.common;

import com.shunwang.baseStone.context.BaseStoneContext;
import com.shunwang.basepassport.config.dao.BusinessLineDao;
import com.shunwang.basepassport.config.pojo.ApiApp;
import com.shunwang.basepassport.config.pojo.BusinessLine;

/**
 * @author: lj.zeng
 * @create: 2024-03-18 10:48:57
 * @Description:
 */
public class BusinessLineUtil {

    private static BusinessLineDao businessLineDao;

    public static BusinessLineDao getBusinessLineDao() {
        if (businessLineDao == null) {
            businessLineDao = BaseStoneContext.getInstance().getBean(BusinessLineDao.class);
        }
        return businessLineDao;
    }

    public static BusinessLine loadBusinessLineByAppId(String appId) {
        ApiApp apiApp = ApiAppUtil.loadApiApp(appId);
        if (apiApp == null) {
            return null;
        }
        return getBusinessLineDao().getById(apiApp.getBusinessLineId());
    }
}
