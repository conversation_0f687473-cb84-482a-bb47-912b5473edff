package com.shunwang.basepassport.jms.param;

/**
 *
 * <AUTHOR>
 * @date 2019/3/11
 **/
public class StringParam implements Param {
    private String param;

    public StringParam() {
    }

    public StringParam(String param) {
        this.param = param;
    }

    @Override
    public int getType() {
        return TYPE_STRING;
    }

    @Override
    public String getValue() {
        return param;
    }

    public void setParam(String param) {
        this.param = param;
    }

    @Override
    public String toString() {
        return "type:TYPE_STRING,value:" + getType() + ",paramValue:" + getValue();
    }
}
