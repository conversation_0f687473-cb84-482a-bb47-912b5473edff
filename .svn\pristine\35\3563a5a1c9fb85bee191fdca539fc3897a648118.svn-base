package com.shunwang.basepassport.manager.request.report;

import com.shunwang.basepassport.config.pojo.ConfigInterface;
import com.shunwang.basepassport.manager.HttpMethod;
import com.shunwang.basepassport.manager.request.BaseRequest;
import com.shunwang.basepassport.manager.response.report.ReportResponse;
import com.shunwang.util.encrypt.Md5Encrypt;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * 大数据统一采集服务接口
 */
public abstract class BaseReportRequest extends BaseRequest<ReportResponse> {
    private static final Logger LOGGER = LoggerFactory.getLogger(BaseReportRequest.class);

    protected String projectId;
    protected String businessId;
    protected String secret;

    protected Map<String, String> param;

    @Override
    public Map<String, String> getHeaders() {
        try {
            String signSource = secret + projectId + "|" + businessId + "|" + getBody() + secret;
            String sign = Md5Encrypt.encrypt(signSource, StandardCharsets.UTF_8.name()).toUpperCase();

            addHeader("sign", sign);
            addHeader("projectId", projectId);
            addHeader("businessId", businessId);

            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("签名源串:" + signSource);
                LOGGER.debug("签名:" + sign);
            }
        } catch (Exception e) {
            LOGGER.error("设置header错误", e);
        }

        return super.getHeaders();
    }

    @Override
    public Map<String, String> buildParams() {
        return param;
    }

    public abstract String getBody();

    @Override
    public Class<ReportResponse> getResponseClass() {
        return ReportResponse.class;
    }

    @Override
    public void doInterfaceSetting(ConfigInterface setting) {
        setUrl(setting.getInterfaceUrl1());
        setProjectId(setting.getInterfacePartnerId());
        setBusinessId(setting.getInterfaceEmail());
        setSecret(setting.getInterfaceMd5Key());
    }

    @Override
    public HttpMethod getHttpMethod() {
        return HttpMethod.POST;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getBusinessId() {
        return businessId;
    }

    public void setBusinessId(String businessId) {
        this.businessId = businessId;
    }

    public String getSecret() {
        return secret;
    }

    public void setSecret(String secret) {
        this.secret = secret;
    }

    public Map<String, String> getParam() {
        return param;
    }

    public void setParam(Map<String, String> param) {
        this.param = param;
    }
}
