<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd" >

<sqlMap namespace="com.shunwang.basepassport.mobile.pojo.RegActiveNo">

  <typeAlias alias="regActiveNo" type="com.shunwang.basepassport.mobile.pojo.RegActiveNo"/>

  <resultMap class="com.shunwang.basepassport.mobile.pojo.RegActiveNo" id="regActiveNoMap">
    <result column="id" property="id" jdbcType="int"/>
    <result column="uuid" property="uuid" jdbcType="varchar"/>
    <result column="site_id" property="siteId" jdbcType="varchar"/>
    <result column="send_mobile" property="sendMobile" jdbcType="varchar"/>
    <result column="mobile_active_no" property="activeNo" jdbcType="varchar"/>
    <result column="mobile_send_time" property="sendTime" jdbcType="datetime"/>
    <result column="mobile_active_time" property="activeTime" jdbcType="datetime"/>

    <result column="send_mobile_coded" property="sendMobileCoded" jdbcType="varchar"/>
  </resultMap>

  <select id="find" resultMap="regActiveNoMap" parameterClass="com.shunwang.framework.ibatis.query.ConditionQuery" >
    SELECT
	    t.id,
	    t.uuid,
	    t.site_id,
	    t.send_mobile,
	    t.mobile_active_no,
	    t.mobile_send_time,
	    t.mobile_active_time,
	    t.send_mobile_coded
    from personal_register_active_no t
    ORDER BY t.id DESC
    limit #firstResult#, #rp#
  </select>

  <insert id="insert" parameterClass="regActiveNo" >
    insert into personal_register_active_no (
        uuid,
        site_id,
        send_mobile,
        mobile_active_no,
        mobile_send_time,
        mobile_active_time,
        send_mobile_coded
        )
    values(
      #uuid:varchar#,
      #siteId:varchar#,
      #sendMobile:varchar#,
      #activeNo:varchar#,
      #sendTime:datetime#,
      #activeTime:datetime#,
      #sendMobileCoded:varchar#
      )
  </insert>

  <update id="update" parameterClass="regActiveNo" >
    update personal_register_active_no
    set
        site_id = #siteId:VARCHAR#,
        send_mobile = #sendMobile:VARCHAR#,
        mobile_send_time = #sendTime:DATETIME# ,
        uuid = #uuid:VARCHAR#,
        mobile_active_no = #activeNo:VARCHAR# ,
        mobile_active_time = #activeTime:DATETIME#,
        send_mobile_coded = #sendMobileCoded:VARCHAR#
    where id = #id:int#
  </update>

  <delete id="delete" parameterClass="regActiveNo" >
    delete from personal_register_active_no where id=#id:int#
  </delete>

  <select id="get" resultMap="regActiveNoMap" >
    select
      id,
      uuid,
      site_id,
      send_mobile,
      mobile_active_no,
      mobile_send_time,
      mobile_active_time,
      send_mobile_coded
    from personal_register_active_no
    where id = #value#
  </select>

  <select id="getBySiteIdAndMobile" resultMap="regActiveNoMap" parameterClass="regActiveNo" >
   select
      id,
      uuid,
      site_id,
      send_mobile,
      mobile_active_no,
      mobile_send_time,
      mobile_active_time,
      send_mobile_coded
    from personal_register_active_no
    WHERE site_id = #siteId:INTEGER#
    <isNotEmpty prepend=" and" property="sendMobile"> send_mobile = #sendMobile:varchar#</isNotEmpty>
    <isEmpty prepend=" and" property="sendMobile"> send_mobile_coded = #sendMobileCoded:varchar#</isEmpty>
</select>


</sqlMap>
