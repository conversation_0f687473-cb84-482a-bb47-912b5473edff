package com.shunwang.basepassport.jms.action;

import com.shunwang.basepassport.jms.mode.Mode;
import com.shunwang.basepassport.jms.param.Param;
import com.shunwang.basepassport.jms.param.StringArrayParam;
import com.shunwang.basepassport.jms.param.StringParam;
import net.sf.ehcache.Ehcache;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.Cache;

import java.util.List;
import java.util.Map;

/**
 * 查询
 *
 * <AUTHOR>
 * @date 2019/3/18
 **/
public class QueryAction implements Action<Param> {
    protected final Logger logger = LoggerFactory.getLogger(QueryAction.class);

    @Override
    public int getType() {
        return QUERY;
    }

    @Override
    public void doAction(Cache cache, Mode mode, Param param) {
        Object nativeCache = cache.getNativeCache();
        Ehcache ehcache = nativeCache instanceof Ehcache ? ((Ehcache) nativeCache) : null;
        if (ehcache == null) {
            return;
        }

        List<String> keys = (List<String>) ehcache.getKeys();

        if (param instanceof StringParam) {
            String value = ((StringParam) param).getValue();
            mode.set(value);
        } else if (param instanceof StringArrayParam) {
            String[] values = ((StringArrayParam) param).getValue();
            mode.set(values);
        }

        logger.info("查询缓存key开始...");
        for (String key : keys) {
            if (mode.match(key)) {
                Cache.ValueWrapper valueWrapper = cache.get(key);
                if (valueWrapper == null || valueWrapper.get() == null) {
                    logger.info("查询缓存key:{},value:{}", key, null);
                } else if (valueWrapper.get() instanceof Map) {
                    StringBuilder sb = new StringBuilder();
                    for (Object o : ((Map) valueWrapper.get()).entrySet()) {
                        sb.append(o).append("\t");
                    }
                    logger.info("查询缓存key:{},value:{}", key, sb.toString());
                } else if (valueWrapper.get() instanceof List) {
                    StringBuilder sb = new StringBuilder();
                    for (Object o : ((List) valueWrapper.get())) {
                        sb.append(o);
                    }
                    logger.info("查询缓存key:{},value:{}", key, sb.toString());
                } else {
                    logger.info("查询缓存key:{},value:{}", key, valueWrapper.get());
                }
            }
        }
        logger.info("查询缓存key结束...");
    }
}
