package com.shunwang.baseStone.core.dao;

import java.io.Serializable;

import com.shunwang.baseStone.core.pojo.BaseStoneObject;
import com.shunwang.framework.ibatis.dao.ConditionQueryDao;
import com.shunwang.framework.ibatis.query.ConditionQuery;
import com.shunwang.framework.ibatis.query.condition.ConditionFactory;



/**
 * ****************************
 * 版权所有：顺网科技 保留所有权利
 * 创建日期: 2011 Jul 13, 2011 10:23:30 AM
 * 创建作者：zhangjp
 * 文件名称：IbatisBaseDao.java
 * 版本： 1.0
 * 功能：dao基础类
 * 最后修改时间：
 * 修改记录：
 ****************************************
 */
public abstract class BaseStoneIbatisDao<Pojo extends BaseStoneObject> extends ConditionQueryDao<Pojo> implements IDao<Pojo>{


	
	public String getKey(Serializable  id){
		StringBuffer bf = new StringBuffer();
		bf.append(getClassT().getName());
		bf.append("[");
		bf.append(id);
		bf.append("]");
		return bf.toString();
	}

	public Integer findCnt(String columnName,Object columnValue){
		ConditionQuery query = new ConditionQuery();
		query.add(ConditionFactory.buildSingleValueCondition(columnName, "=", columnValue));
		return (Integer) getSqlMapClientTemplate().queryForObject(getStatementNameWrap("findCnt"),query);
	}
	


}
