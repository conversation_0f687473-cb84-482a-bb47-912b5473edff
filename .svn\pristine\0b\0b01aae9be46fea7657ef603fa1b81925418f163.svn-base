<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<%@ page contentType="text/html;charset=UTF-8"%>
<%@ include file="/common/taglibs.jsp"%>
<html>
<head>
    <title>登录记录 - 顺网通行证</title>
    <link rel="stylesheet" type="text/css" href="${staticServer }/styles/public.css" />
    <script src="${staticServer }/scripts/common/jquery.js"></script>

</head>
<body>

            <div class="shop_head" id="logon_log">
                <strong>登录记录</strong>
                系统会记录您最近10条登录日志，若存在异常情况，请尽快修改密码
            </div>
            <c:choose>
            <c:when test="${empty logonLogs}">
            <div class="tip_box">
                <p class="tip_warn">您的账号近期未登录，暂无登录记录。</p>
            </div>
            </c:when>
            <c:otherwise>
            <div class="records">
                <table cellpadding="0" cellspacing="0">
                    <thead>
                    <tr>
                        <td>时间</td>
                        <td>登录来源</td>
                        <td>地区</td>
                        <td>IP</td>
                    </tr>
                    </thead>
                    <tbody>
                    <c:forEach items="${logonLogs}" var="logonLog">
                        <tr>
                            <td>${logonLog.timeAddStr}</td>
                            <td>${logonLog.loginFromDescStr}</td>
                            <td>${logonLog.clientAreaDescStr}</td>
                            <td>${logonLog.clientIpStr}</td>
                        </tr>

                    </c:forEach>




                    </tbody>
                </table>
            </div>
            </c:otherwise>
            </c:choose>
    <script type="text/javascript">
        $("#logon_log").attr("class", "current");
    </script>

</body>
</html>