package com.shunwang.basepassport.user.response;

import com.google.gson.annotations.Expose;
import com.shunwang.baseStone.core.response.BaseStoneResponse;
import com.shunwang.basepassport.config.constants.AreaConstants;
import com.shunwang.basepassport.config.pojo.Area;
import com.shunwang.xmlbean.annotation.XmlInit;

import java.util.Collections;
import java.util.Objects;
import java.util.function.Function;

/**
 * 进件地址转换
 **/
public class AddressConversionResponse extends BaseStoneResponse {
    private static final Function<Area, String> hlbIdSupplier = Area::getHlbAreaId;
    private static final Function<Area, String> hlbNameSupplier = Area::getHlbName;

    private static final Function<Area, String> shengPayIdSupplier = Area::getShengpayAreaId;
    private static final Function<Area, String> shengPayNameSupplier = Area::getShengpayName;

    private static final Function<Area, String> sxfIdSupplier = Area::getSxfAreaId;
    private static final Function<Area, String> sxfNameSupplier = Area::getSxfName;

    public AddressConversionResponse(Area province, Area city, Area area, Integer type) {
        AreaResult areaResult = new AreaResult();
        Function<Area, String> idSupplier;
        Function<Area, String> nameSupplier;
        if (Objects.equals(type, AreaConstants.ConvertAreaType.HLB_TYPE)) {
            idSupplier = hlbIdSupplier;
            nameSupplier = hlbNameSupplier;
        } else if (Objects.equals(type, AreaConstants.ConvertAreaType.YZSP_TYPE)) {
            idSupplier = shengPayIdSupplier;
            nameSupplier = shengPayNameSupplier;
        } else if (Objects.equals(type, AreaConstants.ConvertAreaType.SXF_TYPE)) {
            idSupplier = sxfIdSupplier;
            nameSupplier = sxfNameSupplier;
        } else {
            throw new IllegalStateException("不正确的type");
        }

        if (province != null) {
            areaResult.setProvinceId(idSupplier.apply(province));
            areaResult.setProvince(nameSupplier.apply(province));
        }
        if (city != null) {
            areaResult.setCityId(idSupplier.apply(city));
            areaResult.setCity(nameSupplier.apply(city));
        }
        if (area != null) {
            areaResult.setAreaId(idSupplier.apply(area));
            areaResult.setArea(nameSupplier.apply(area));
        }
        setItems(Collections.singletonList(areaResult));
    }

    public static class AreaResult {
        @Expose
        private String provinceId;
        @Expose
        private String province;
        @Expose
        private String cityId;
        @Expose
        private String city;
        @Expose
        private String areaId;
        @Expose
        private String area;

        @XmlInit
        public String getProvinceId() {
            return provinceId;
        }

        public void setProvinceId(String provinceId) {
            this.provinceId = provinceId;
        }

        @XmlInit
        public String getProvince() {
            return province;
        }

        public void setProvince(String province) {
            this.province = province;
        }

        @XmlInit
        public String getCityId() {
            return cityId;
        }

        public void setCityId(String cityId) {
            this.cityId = cityId;
        }

        @XmlInit
        public String getCity() {
            return city;
        }

        public void setCity(String city) {
            this.city = city;
        }

        @XmlInit
        public String getAreaId() {
            return areaId;
        }

        public void setAreaId(String areaId) {
            this.areaId = areaId;
        }

        @XmlInit
        public String getArea() {
            return area;
        }

        public void setArea(String area) {
            this.area = area;
        }
    }

}
