<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE struts PUBLIC "-//Apache Software Foundation//DTD Struts Configuration 2.0//EN" "http://struts.apache.org/dtds/struts-2.0.dtd">

<struts>

	
    <package name="mobilePwd" namespace="/front/noLogin" extends="nologinpassport">
        <!-- 手机找回  -->
        <action name="sendMobileMsg" method="sendMobileMsg" class="findPwdMobileAction">
            <result name="success" type="chain">
                <param name="namespace">/front/noLogin</param>
                <param name="actionName">toSendMobileMsg</param>
            </result>			
            <result name="input" >/front/find/pwdFind_all_front.jsp</result>		  
        </action>

        <action name="toSendMobileMsg" method="send" class="mobileSendWebAction">
            <result name="success" >/front/find/phone_front.jsp</result>
            <result name="input" >/front/find/phone_error.jsp</result>		  
        </action>

        <action name="applyActiveGetCodeAgain_front" method="getCodeAgain" class="findPwdMobileAction">
            <result name="success" >/front/find/phone_front.jsp</result>
            <result name="input" >/front/find/phone_front.jsp</result>
        </action>	

        <action name="pwdFind_mobile_front" method="checkMobileActiveNo" class="findPwdMobileAction">
            <result name="input" >/front/find/phone_front.jsp</result>
            <result name="success">/front/find/phone2_front.jsp</result>
        </action>

        <action name="pwdFind_mobile_success_front" method="mobileResetPwd" class="findPwdMobileAction">
            <result name="input" >/front/find/phone2_front.jsp</result>
        </action>

		<action name="pwdFind_success_result" method="showSuccResult" class="findPwdMobileAction">
			<result name="success">/front/find/phone3_front.jsp</result>
		</action>
		<action name="getAllquestions_front" method="getAllquestions" class="findPwdQueAction">
			<result name="success">/front/find/memberQuestionSet_common.jsp</result>
			<result name="input">/front/find/memberQuestionSet_front.jsp</result>
		</action>
    </package>
	
	
	<!-- 手机找回 密保问题 -->		
	<package name="findQue" namespace="/front/login" extends="passport">
	<action name="toSendPhoneMsg_front"  method="toSendPhoneMsg" class="findPwdMobileAction">			
		<result name="success">/front/find/toSendPhoneMsg_front.jsp</result>
		<result name="input">/front/find/chooseFindProblem_front.jsp</result>
	</action>
	
	
	<action name="findProblemSendPhone_front"  method="sendMobileMsg" class="findPwdMobileAction">
		<result name="success" type="chain">
			 <param name="namespace">/front/login</param>
			 <param name="actionName">toSendMobileMsg</param>
			  </result>			
		<result name="overtime">/front/find/checkError_front.jsp</result>
		<result name="input">/front/find/findProblemSendPhone_front.jsp</result>
	</action>
	
	<action name="toSendMobileMsg" method="send" class="mobileSendWebAction">
		<result name="success">/front/find/findProblemSendPhone_front.jsp</result>
		<result name="input">/front/find/toSendPhoneMsg_front.jsp</result>
	</action>
	
	
	<action name="goPhoneReset_front" method="checkMobileActiveNo" class="findPwdMobileAction" >
		<result name="success">/front/find/goReset_front.jsp</result>
		<result name="overtime">/front/find/checkError_front.jsp</result>
		<result name="input">/front/find/findProblemSendPhone_front.jsp</result>
	</action>
	
	<action name="getAllquestions_front" method="getAllquestions" class="findPwdQueAction">
		<result name="success">/front/find/memberQuestionSet_common.jsp</result>
		<result name="input">/front/find/memberQuestionSet_front.jsp</result>
	</action>
	
	<action name="doResetProblem" method="doResetProblem" class="findPwdQueAction">
	<result name="success" type="chain">
			 <param name="namespace">/front/login</param>
			 <param name="actionName">doResetProblem_front</param>
			  </result>	  		
		<result name="input">/front/find/goReset_front.jsp</result>
	</action>
	
	<action name="doResetProblem_front" method="checkActiveCodAgian" class="findPwdMobileAction">	
		<result name="success">/front/find/doResetProblem_front.jsp</result>
		<result name="input">/front/find/goReset_front.jsp</result>
	</action>
	
	</package>
	
	
</struts>
