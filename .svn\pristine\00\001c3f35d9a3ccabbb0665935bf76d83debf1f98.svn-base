package com.shunwang.baseStone.sso.intfc.pojo;

import java.util.Date;

/**
 * 动态密码发送短信接口的响应对象
 */
public class SmsCode {

    /**
     * 是否实际发送了短信
     */
    private boolean send;
    private String validTime;
    private long sendTime;

    public boolean isSend() {
        return send;
    }

    public void setSend(boolean send) {
        this.send = send;
    }

    public String getValidTime() {
        return validTime;
    }

    public void setValidTime(String validTime) {
        this.validTime = validTime;
    }

    public long getSendTime() {
        return sendTime;
    }

    public void setSendTime(long sendTime) {
        this.sendTime = sendTime;
    }

    public Date getSendTimeAsDate() {
        return new Date(sendTime);
    }
}
