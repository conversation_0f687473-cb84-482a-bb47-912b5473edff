package com.shunwang.baseStone.context;
/**
 * @Description:IP上下文
 * <AUTHOR>  create at 2011-8-25 下午05:22:11
 * @FileName com.shunwang.baseStone.context.IPContext.java
 */
public class IPContext {
	/**
	 * 本地IP线程库
	 */
	private static ThreadLocal<String> ip = new ThreadLocal<String>();
	/**
	 * 设置IP
	 * @param value
	 * <AUTHOR> create at 2011-8-25 下午05:23:53
	 */
	public static void setIp(String value){
		ip.set(value);
	}
	/**
	 * 取IP
	 * @return
	 * <AUTHOR> create at 2011-8-25 下午05:24:02
	 */
	public static String getIp(){
		return ip.get();
	}

	/**
	 * 清除线程ip
	 */
	public static void cleanIp() {
		ip.remove();
	}
}
