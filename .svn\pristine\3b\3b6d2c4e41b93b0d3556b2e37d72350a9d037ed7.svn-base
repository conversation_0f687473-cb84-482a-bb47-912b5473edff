package com.shunwang.basepassport.user.response;

import com.google.gson.annotations.Expose;
import com.shunwang.baseStone.core.response.BaseStoneResponse;
import com.shunwang.baseStone.useroutinterface.pojo.OutOauthDir;
import com.shunwang.baseStone.useroutinterface.pojo.UseroutInterface;
import com.shunwang.xmlbean.annotation.XmlInit;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by bd.fang on 2016/3/8.
 */
public class OutOauthDirResponse extends BaseStoneResponse {

    private Integer size;
    private List<Dir> dirList;
    public OutOauthDirResponse(List<OutOauthDir> outOauthDirList) {
        if(outOauthDirList != null && outOauthDirList.size() > 0) {
            dirList = new ArrayList<Dir>();
            for(int i = 0; i< outOauthDirList.size() ; i++) {
                Dir dir = new Dir(outOauthDirList.get(i).getOutOauthInterfaces().get(0).getInerfaceId(),
                        outOauthDirList.get(i).getDirName(), outOauthDirList.get(i).getDirImg());
                dirList.add(dir);
            }
        }
        setItems(dirList);
    }

    public class Dir {
        @Expose
        private Integer inerfaceId;
        @Expose
        private String dirName;
        @Expose
        private String dirImg;
        private List<UseroutInterface> outOauthInterfaces;

        public Dir(Integer inerfaceId, String dirName, String dirImg) {
            this.inerfaceId = inerfaceId;
            this.dirName = dirName;
            this.dirImg = dirImg;
        }

        @XmlInit(path="inerfaceId")
        public Integer getInerfaceId() {
            return this.inerfaceId;
        }

        public void setInerfaceId(Integer inerfaceId) {
            this.inerfaceId = inerfaceId;
        }

        @XmlInit(path="dirName")
        public String getDirName() {
            return this.dirName;
        }

        public void setDirName(String dirName) {
            this.dirName = dirName;
        }

        @XmlInit(path="dirImg")
        public String getDirImg() {
            return dirImg;
        }

        public void setDirImg(String dirImg) {
            this.dirImg = dirImg;
        }


    }
}
