package com.shunwang.basepassport.manager.response.logon;

import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.shunwang.basepassport.manager.IResponse;
import com.shunwang.util.lang.StringUtil;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class LogonResponse implements IResponse {

    public static final int SUCCESS_CODE = 0;

    private Integer code;

    private String message;

    private String json;

    private JsonObject response;

    public LogonResponse() {
    }

    public LogonResponse parse() {
        if (StringUtil.isBlank(json)) {
            return null;
        }
        response = JsonParser.parseString(json).getAsJsonObject();
        code = response.get("code").getAsInt();
        message  = response.get("message").getAsString();
        return this;
    }

    public boolean isSuccess() {
        return code != null && code == SUCCESS_CODE;
    }
}
