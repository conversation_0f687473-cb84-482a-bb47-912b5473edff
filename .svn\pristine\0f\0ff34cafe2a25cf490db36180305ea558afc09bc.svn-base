package com.shunwang.basepassport.actu.pojo;

import com.shunwang.baseStone.core.pojo.BaseStoneObject;
import com.shunwang.basepassport.actu.constant.ActuConstant;
import com.shunwang.basepassport.user.common.SensitiveInfoUtil;
import com.shunwang.encrypt.core.annotation.EncryptEnabled;
import com.shunwang.encrypt.core.annotation.EncryptField;

import java.io.Serializable;
import java.util.Date;

@EncryptEnabled
public class PersonalActuVerifyRecord extends BaseStoneObject {
    private Integer id;
    private Integer memberId;
    private String memberName;
    private String siteId;
    private Integer categoryId ;//作业单元
    private String categoryName ;
    @EncryptField(ref = "realNameCoded")
    private String realName;
    private String realNameCoded;
    @EncryptField(ref = "idCardNoCoded")
    private String idCardNo;
    private String idCardNoCoded;
    //是否收费，枚举值：1 ：收费 0：不收费
    private Integer chargeStatus;
    /**
     * @see com.shunwang.basepassport.actu.constant.ActuConstant#IS_MATCH
     */
    private Integer result;
    private String remark;
    /**
     * @see com.shunwang.basepassport.actu.constant.ActuConstant#ORIGIN_FROM_WANSHU
     */
    private Integer originFrom;
    private Date createTime;

    @Override
    public Serializable getPk() {
        return id;
    }

    public String getRealNameShow() {
        return SensitiveInfoUtil.realNameStarReplace(realName);
    }
    public String getIdCardNoShow() {
        return SensitiveInfoUtil.idCardNoStarReplace(idCardNo);
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getMemberId() {
        return memberId;
    }

    public void setMemberId(Integer memberId) {
        this.memberId = memberId;
    }

    public String getMemberName() {
        return memberName;
    }

    public void setMemberName(String memberName) {
        this.memberName = memberName;
    }

    public String getSiteId() {
        return siteId;
    }

    public void setSiteId(String siteId) {
        this.siteId = siteId;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getIdCardNo() {
        return idCardNo;
    }

    public void setIdCardNo(String idCardNo) {
        this.idCardNo = idCardNo;
    }

    public String getIdCardNoCoded() {
        return idCardNoCoded;
    }

    public void setIdCardNoCoded(String idCardNoCoded) {
        this.idCardNoCoded = idCardNoCoded;
    }

    public Integer getChargeStatus() {
        return chargeStatus;
    }

    public void setChargeStatus(Integer chargeStatus) {
        this.chargeStatus = chargeStatus;
    }

    public Integer getResult() {
        return result;
    }

    public void setResult(Integer result) {
        this.result = result;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getOriginFrom() {
        return originFrom;
    }

    public void setOriginFrom(Integer originFrom) {
        this.originFrom = originFrom;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public boolean isMatch() {
        return result != null && ActuConstant.IS_MATCH == result;
    }

    public boolean isNotMatch() {
        return result != null && ActuConstant.IS_NOT_MATCH == result;
    }

    public Integer getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Integer categoryId) {
        this.categoryId = categoryId;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public String getRealNameCoded() {
        return realNameCoded;
    }

    public void setRealNameCoded(String realNameCoded) {
        this.realNameCoded = realNameCoded;
    }
}
