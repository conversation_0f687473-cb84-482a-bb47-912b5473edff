#顺令配置
dynamicMd5Key=123456
dynamicAesKey=0123456789012345
##顺令密码生成个数
effectiveTime=60

#头像接口限制上传头像大小（KB）
headImgMaxSize=800

sensitive.file.path=D:/working_branches/com.shunwang.member.center/member-center-service/src/main/resources/sensitive.txt
ip.location.file.path=F:/ideaws/com.shunwang.member.center/member-center-service/src/main/resources/qqwry.dat

interface.siteId=Interface_inner
interface.sendbinder.url=http://interface.kedou.com/front/interface/sendBinderUpdate.htm
interface.sendbinder.md5Key=123456
interface.confirmForInnerLogin.url=http://interface.kedou.com/front/interface/confirmForInnerLogin.htm
interface.confirmForInnerLogin.md5Key=123456
interface.mobileCheckCodeUpdate.url=http://interface.kedou.com/front/interface/mobileCheckCodeUpdate.htm
interface.mobileCheckCodeUpdate.md5Key=123456
interface.singleAccountBindExt.url=http://interface.kedou.com/front/interface/singleAccountBindExt.htm
interface.singleAccountBindExt.md5Key=123456

#手机短信验证码缓存时间 单位:分钟
sms.cache.time=60
#注册后用户缓存时间 单位:分钟
member.cache.time=10


##==========================MQ配置==========================
activemq.brokerURL=failover\:(tcp\://***************:61616?wireFormat.maxInactivityDuration=30000&trace=true)&maxReconnectDelay=10000
cache.topic=cache_topic
##==========================MQ配置==========================

#共享上传图片读取地址
assets.server=http\://static.kedou.com/passport/
assets.path=static
assets.version=1.0.0
actualityImgServer=https://interface.kedou.com/file/view

db.queryWithCipherColumn=true
db.aes.password.basePassport=a63e0bf1901e81a6
db.aes.password.baseConfig=
db.aes.password.basePassportLog=a63e0bf1901e81a6


bigData.report.url=https://beehive.swdcmg.com/commonReport/report
bigData.report.businessId=paymes
bigData.report.projectId=minipay
bigData.report.secret=********************

