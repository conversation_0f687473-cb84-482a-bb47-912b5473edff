/*
 * ******************************
 * ** 版权所有：顺网科技 保留所有权利 *******************************
 */

package com.shunwang.basepassport.user.dao;

import com.shunwang.baseStone.context.BaseStoneContext;
import com.shunwang.baseStone.core.detail.HasDetailDao;
import com.shunwang.basepassport.actu.pojo.PersonalActuInfo;
import com.shunwang.basepassport.user.common.MemberConstants;
import com.shunwang.basepassport.user.common.UserCheckUtil;
import com.shunwang.basepassport.user.exception.MsgNotFoundExp;
import com.shunwang.basepassport.user.pojo.Member;
import com.shunwang.basepassport.user.pojo.MemberInfo;

import java.util.Date;
import java.util.List;

/**
 * @Description:会员dao
 * <AUTHOR> create at 2011-7-25 下午01:44:22
 * @FileName com.shunwang.basepassport.user.dao.MemberDao.java
 */
public class MemberDao extends HasDetailDao<Member> {

    public Member getMember(String loginStr) {
        if (UserCheckUtil.checkEmail(loginStr)) {
            return getByEmail(loginStr);
        }
        if (UserCheckUtil.checkMobile(loginStr)) {
            return getByMobile(loginStr);
        }
        return getByName(loginStr);
    }

    public List<Member> getListMember(String mobileOrEmail) {
        if (UserCheckUtil.checkEmail(mobileOrEmail)) {
            return getListByEmail(mobileOrEmail);
        }
        if (UserCheckUtil.checkMobile(mobileOrEmail)) {
            return getListByMobile(mobileOrEmail);
        }
        return null;
    }

    /**
     * @Description:根据用户名查找用户信息
     * <AUTHOR> create at 2011-7-25 下午03:17:33
     */
    public Member getByName(String name) {
        Member ret =
                (Member) getSqlMapClientTemplate().queryForObject(
                        getStatementNameWrap("findByName"), name);
        if (ret != null) {
            ret.setLoginType(MemberConstants.LOGIN_TYPE_ACCOUNT);
        }
        return ret;
    }


    public Member getByOutMemberId(String outMemberId) {
        return (Member) getSqlMapClientTemplate().queryForObject(
                getStatementNameWrap("getByOutMemberId"), outMemberId);

    }

    public Member getByOutMemberName(String outMemberName) {
        return (Member) getSqlMapClientTemplate().queryForObject(
                getStatementNameWrap("getByOutMemberName"), outMemberName);

    }

    /**
     * @Description:根据手机查询用户信息
     * Modified By: min.da
     * Modified Date: 2014年9月30日14:47:58
     * Modified Desc: 一个手机号码允许绑定多个通行证,但只能有一个通行证能使用手机登录,所以这里改成查询手机号码被设为登录账号的那一个。(SQL: bindState & 128)
     *
     * <AUTHOR> create at 2011-7-28 上午08:45:57
     */
    public Member getByMobile(String moblie) {
        Member query = new Member();
        query.setMobile(moblie);
        Member ret =
                (Member) getSqlMapClientTemplate().queryForObject(getStatementNameWrap("findByMobile"), query);
        if (ret != null) {
            ret.setLoginType(MemberConstants.LOGIN_TYPE_MOBILE);
        }
        return ret;
    }

    /**
     * 查询该手机绑定过的通行证数量
     *
     * <AUTHOR>
     * @date 2014-09-30
     * @param mobile
     * @return
     */
    public Integer getCntByMobile(String mobile) {
        Member query = new Member();
        query.setMobile(mobile);
        return (Integer) getSqlMapClientTemplate().queryForObject(getStatementNameWrap("findCntByMobile"), query);
    }

    public List<Member> getCouldBindListByMobile(String mobile) {
        Member query = new Member();
        query.setMobile(mobile);
        return getSqlMapClientTemplate().queryForList(getStatementNameWrap("findCouldBindListByMobile"), query);
    }


    /**
     * @Description:根据邮箱查询用户信息
     * Modified By: min.da
     * Modified Date: 2014年9月30日14:48:18
     * Modified Desc: 一个邮箱允许绑定多个通行证,但只能有一个通行证能使用邮箱登录,所以这里改成查询邮箱被设为登录账号的那一个。(SQL: bindState & 256)
     *
     * <AUTHOR> create at 2011-7-28 上午08:51:02
     */
    public Member getByEmail(String email) {
        Member query = new Member();
        query.setEmail(email);
        Member ret = (Member) getSqlMapClientTemplate().queryForObject(getStatementNameWrap("findByEmail"), query);
        if (ret != null) {
            ret.setLoginType(MemberConstants.LOGIN_TYPE_EMAIL);
        }
        return ret;
    }

    /**
     * 查询该邮箱绑定过的通行证数量
     *
     * <AUTHOR>
     * @date 2014-09-30
     * @param email
     * @return
     */
    public Integer getCntByEmail(String email) {
        Member query = new Member();
        query.setEmail(email);
        return (Integer) getSqlMapClientTemplate().queryForObject(getStatementNameWrap("findCntByEmail"), query);
    }

    /**
     * @Description:根据用户扩展信息插叙用户信息
     * <AUTHOR> create at 2011-7-28 上午08:52:17
     */
    public Member getByMemberInfo(MemberInfo memberInfo) {
        if (null == memberInfo) {
            throw new MsgNotFoundExp("用户");
        }
        Member member = (Member) this.getById(memberInfo.getMemberId());
        member.setMemberInfo(memberInfo);
        return member;
    }

    /**
     * 根据memberid查询用户数据
     */
    public Member getByMemberId(Integer memberId) {
        Member member = (Member) this.getById(memberId);
        return member;
    }

    /**
     * @Description:
     * <AUTHOR> create at 2011-7-28 上午08:46:38
     */
    @SuppressWarnings("unused")
    private MemberInfoDao getMemberInfoDao() {
        return (MemberInfoDao) BaseStoneContext.getInstance().getBean("memberInfoDao");
    }

    /**
     * @return 功能：根据用户ID和 密码错误次数查询用户
     * @创建日期: 2011-8-1 上午10:56:44
     * @创建作者：chenjh
     */
    public Member getByIdAndTime(Integer memberId, Date pwdErrTime) {
        Member query = new Member();
        query.setMemberId(memberId);
        query.setPwdErrTime(pwdErrTime);
        return (Member) getSqlMapClientTemplate().queryForObject(
                getStatementNameWrap("getByIdAndTime"), query);
    }

    @SuppressWarnings("unchecked")
    public List<Member> getByIDCardNo(String idCardNo) {
        PersonalActuInfo query = new PersonalActuInfo();
        query.setIdCardNo(idCardNo);
        return getSqlMapClientTemplate().queryForList(getStatementNameWrap("getByIDCardNo"), query);
    }


    /**
     *根据邮箱查询帐号下所有用户
     * @param email
     * @return
     */
    public List<Member> getListByEmail(String email) {
        Member query = new Member();
        query.setEmail(email);
        return getSqlMapClientTemplate().queryForList(getStatementNameWrap("findMemberByEmail"), query);
    }

    /**
     * 根据手机号查询帐号下所有用户
     * @param moblie
     * @return
     */
    public List<Member> getListByMobile(String moblie) {
        Member query = new Member();
        query.setMobile(moblie);
        return getSqlMapClientTemplate().queryForList(getStatementNameWrap("findMemberByMobile"), query);
    }
}
