package com.shunwang.basepassport.user.service;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.shunwang.baseStone.cache.CacheKeyConstant;
import com.shunwang.baseStone.context.RedisContext;
import com.shunwang.basepassport.manager.bean.RiskParam;
import com.shunwang.basepassport.manager.enums.RiskPartnerServiceEnum;
import com.shunwang.basepassport.manager.request.risk.RiskRequest;
import com.shunwang.basepassport.manager.response.risk.RiskResponse;
import com.shunwang.basepassport.manager.service.risk.RiskServiceClient;
import com.shunwang.basepassport.user.pojo.LogonLog;
import com.shunwang.util.lang.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.concurrent.*;

public class RiskService {
    private static final Logger logger = LoggerFactory.getLogger(RiskService.class);

    private static Executor executor = Executors.newCachedThreadPool();
    /**
     * 用于100ms超时异步线程
     */
    private static final ScheduledExecutorService scheduler =
            Executors.newScheduledThreadPool(
                    2,
                    new ThreadFactoryBuilder()
                            .setDaemon(true)
                            .setNameFormat("failAfter-%d")
                            .build());
    /**
     *
     * @param psEnum
     * @param userName
     * @param ip
     * @param mac
     * @param imei
     * @return 当风控关闭，或者请求异常时，仅返回null,保证不影响业务
     */
    public static RiskResponse checkRisk(RiskPartnerServiceEnum.PartnerService psEnum, String userName, String ip, String mac, String imei) {
        if (!riskIsOpen(psEnum)) {
            return null;//风控关闭，直接返回null
        }
        RiskRequest riskRequest = new RiskRequest();
        RiskParam riskParam = new RiskParam(psEnum);
        riskParam.setUserName(userName);
        riskParam.setIp(ip);
        riskParam.setMac(mac);
        riskParam.setImei(imei);
        riskRequest.setRiskParam(riskParam);

        CompletableFuture<RiskResponse> responseFuture = CompletableFuture.supplyAsync(
                () -> RiskServiceClient.execute(riskRequest), executor);

        try {
            return responseFuture.get(110, TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            logger.error("风控获取结果失败[{}]", e.getMessage());
        }
        return null;
    }

    /**
     *
     * @param psEnum
     * @param log
     */
    public static void reportRisk(RiskPartnerServiceEnum.PartnerService psEnum, LogonLog log) {
        CompletableFuture.runAsync(
                () -> {
                    if (!riskIsOpen(psEnum)) {
                        return;//风控关闭
                    }
                    List<String> excludeConfig = reportExcludeConfig();
                    String logonWay = log.getLogonType() + ":" + (StringUtil.isBlank(log.getPwdType()) ? StringUtil.EMPTY_STRING : log.getPwdType());
                    if (excludeConfig.contains(logonWay)) {
                        return;
                    }
                    RiskRequest riskRequest = new RiskRequest();
                    RiskParam riskParam = new RiskParam(psEnum);
                    riskParam.setUserName(log.getMemberName());
                    riskParam.setIp(log.getClientIp());
                    riskRequest.setRiskParam(riskParam);
                    RiskServiceClient.execute(riskRequest);
                    }, executor);

    }

    public static boolean riskIsOpen(RiskPartnerServiceEnum.PartnerService psEnum) {
        String value = RedisContext.getResourceCache().getResourceValue(CacheKeyConstant.ConfigResourcesConstants.RISK_CONFIG, psEnum.getConfigKey());
        if (StringUtil.isBlank(value)) {
            return false;
        }
        return value.equalsIgnoreCase("y");
    }

    /**
     *  loginType:pwdType,loginType:pwdType
     */
    public static List<String> reportExcludeConfig() {
        return RedisContext.getStringListConfig(CacheKeyConstant.ConfigResourcesConstants.RISK_CONFIG, CacheKeyConstant.ConfigResourcesConstants.RISK_SSO_LOGIN_REPORT_EXCLUDE_MAP_CONFIG);
    }
}
