package com.shunwang.basepassport.manager.service.report;

import com.shunwang.basepassport.config.dao.ConfigInterfaceDao;
import com.shunwang.basepassport.config.pojo.ConfigInterface;
import com.shunwang.basepassport.manager.request.report.ReportRequest;
import com.shunwang.basepassport.manager.response.report.ReportResponse;
import com.shunwang.util.date.DateUtil;
import com.shunwang.util.net.HttpClientUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.lang.reflect.Constructor;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

public class ReportServiceClient {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReportServiceClient.class);

    private static ConfigInterfaceDao configInterfaceDao;

    public void setConfigInterfaceDao(ConfigInterfaceDao configInterfaceDao) {
        ReportServiceClient.configInterfaceDao = configInterfaceDao;
    }

    public static ReportResponse execute(ReportRequest request) {
        try {
            ConfigInterface setting = configInterfaceDao.findByInterfaceKey(request.getInterfaceKey());
            request.doInterfaceSetting(setting);
            String url = request.buildUrl();
            request.buildParams();
            Map<String, String> headers = request.getHeaders();
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("----------------------------------------");
                LOGGER.debug("url:{}", url);
                LOGGER.debug("method:{}", request.getHttpMethod());
                LOGGER.debug("body:{}", request.getBody());
                LOGGER.debug("headers:{}", headers);
                LOGGER.debug("----------------------------------------");
            }

            byte[] responseBytes = HttpClientUtils.doPost(url, request.getBody(), headers, StandardCharsets.UTF_8);
            if (responseBytes == null) {
                throw new IOException("大数据上报网络异常");
            }
            String response = new String(responseBytes, StandardCharsets.UTF_8);

            Class<ReportResponse> responseClass = request.getResponseClass();
            Constructor constructor = responseClass.getConstructor();
            ReportResponse resp = (ReportResponse) constructor.newInstance();
            resp.setJson(response);
            return resp.parse();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static void main(String[] args) {
        //            ConfigInterface setting = new ConfigInterface();
        //            setting.setInterfaceUrl1("https://datapolaris.shunwang.com/commonReport/report");
        //            setting.setInterfacePartnerId("swpassport");
        //            setting.setInterfaceEmail("reglogin");
        //            setting.setInterfaceMd5Key("B3VEY4AC3RUXRRA84DTE");
        ReportRequest reportRequest = new ReportRequest();
        Map<String, String> param = new HashMap<>();
        param.put("clientIP", "127.0.0.1");
        param.put("serverTime", DateUtil.getCurrentDateStamp());
        param.put("guid", "");
        param.put("wwType", "");
        param.put("wwAccount", "");
        param.put("billingType", "");
        param.put("billlingAccount", "");
        param.put("mac", "");
        param.put("pageURL", "http://localhost");
        param.put("siteId", "sso");
        param.put("interfaceType", "login");

        reportRequest.setParam(param);

        ReportResponse response = ReportServiceClient.execute(reportRequest);

        if (response.isSuccess()) {
            System.out.println("success");
        }
    }

}
