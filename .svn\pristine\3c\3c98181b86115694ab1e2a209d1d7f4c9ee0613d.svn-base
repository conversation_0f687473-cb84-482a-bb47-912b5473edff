package com.shunwang.baseStone.tag;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.jsp.JspWriter;
import javax.servlet.jsp.tagext.TagSupport;

public abstract class SWTag extends TagSupport {
	

	/**
	 * <AUTHOR> create at 2011-10-17 下午02:17:45 
	 */
	private static final long serialVersionUID = -6044566282795959148L;
	public ServletRequest getReq() {
		return this.pageContext.getRequest();
		
	}

	public ServletResponse getResp() {
		return this.pageContext.getResponse();
		
	}

	public JspWriter getWriter() {
		return this.pageContext.getOut();
		
	}

	protected void print(String str){
		try{
			this.getWriter().print(str);
			this.getWriter().flush();
		}catch(Exception e){
			e.printStackTrace();
		}
	}
	
	protected void print(int str){
		try{
			this.getWriter().print(str);
		}catch(Exception e){
			
		}
	}
	
	protected void print(double str){
		try{
			this.getWriter().print(str);
		}catch(Exception e){
			
		}
	}
	protected void setRequestAttribute(String key,Object value){
		this.pageContext.getRequest().setAttribute(key, value);
		
	}
	
	protected Object getRequestAttribute(String key){
		return this.pageContext.getRequest().getAttribute(key);
		
	}
	protected String getParameter(String key){
		return this.pageContext.getRequest().getParameter(key);
		
	}
}
