package com.shunwang.basepassport.rpc;

import com.shunwang.toolbox.rpc.*;
import com.shunwang.toolbox.tracing.TracingRestTemplateInterceptor;
import org.springframework.beans.factory.FactoryBean;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.web.client.RestTemplate;

import java.util.List;

public class RpcConfiguration implements FactoryBean<RestRpcOperations> {

    TokenProvider tokenProvider;

    @Override
    public RestRpcOperations getObject() throws Exception {
        RestTemplateBuilder restTemplateBuilder = new RestTemplateBuilder();
        RestTemplate restTemplate = restTemplateBuilder.build();
        List<ClientHttpRequestInterceptor> interceptors = restTemplate.getInterceptors();
        interceptors.add(new TokenInterceptor(tokenProvider));
        interceptors.add(new TracingRestTemplateInterceptor());
        List<HttpMessageConverter<?>> messageConverters = restTemplate.getMessageConverters();
        messageConverters.add(0, new RpcRequestMessageConvertor());
        messageConverters.add(0, new RpcResponseMessageConvertor());
        restTemplate.setMessageConverters(messageConverters);
        RestRpcOperations restRpcOperations = new RestRpcOperations();
        restRpcOperations.setRestTemplate(restTemplate);
        return restRpcOperations;
    }

    @Override
    public Class<?> getObjectType() {
        return RestRpcOperations.class;
    }

    public void setTokenProvider(TokenProvider tokenProvider) {
        this.tokenProvider = tokenProvider;
    }
}