<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ include file="../common/taglibs.jsp" %>
<%
    // 针对IE 9关闭其跨站脚本筛选器（Cross-Site Scripting Filter），否则参数值变化，签名错误
    response.setHeader("X-XSS-Protection","0");
%>
<%@page import="com.shunwang.util.net.RequestUtil"%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
 <meta http-equiv="Cache-Control" content="no-cache" />
 <meta http-equiv="Pragma" CONTENT="no-cache"/>
 <meta http-equiv="Expires" content="0" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <style type="text/css">
        .waitbg{width:100%;text-align:center }
        .waitwapper{margin:auto; width:460px;}
        .waitbg .waitbox{ margin:100px 0 100px 0px; width:460px; height:240px; border:1px solid #94bcdf; background-color:#f5f9fc;}
        .waitbg .waittitle{ text-align:left;height:25px; line-height:25px; background-color:#cde7f6; color:#1971b8; padding-left:10px; font-weight:700;}
        .waitbg .waitpic{ float:left; margin:60px 0 0 90px;*margin:60px 0 0 90px!important;*margin:60px 0 0 40px;width:47px; height:47px;}
        .waitbg .waittext{ float:left;margin:60px 0 0 20px; line-height:47px; font-weight:700; font-size:14px;}
    </style>
      <c:if test="${not empty loginCss && not empty loginCss.cssUrl }">
          <link href="${loginCss.cssUrl}" rel="stylesheet" type="text/css"/>
      </c:if>
  <title>正在跳转，请稍候...</title>
  </head>
  <body onload="go();">
  <div class="waitbg">
  <div class="waitwapper">
  <div class="waitbox">
  <div class="waittitle">跳转提示</div>
  <div class="waitpic"><img src="${staticServer}/${cdnVersion}/images/waiting.gif" width="47" height="47" /></div>
   <div class="waittext">页面正在跳转中，请稍等...</div>
  </div>
  </div>

</div>
  
<%--  用于post请求验票据时跳转请稍候...--%>
  <form id="forward_20101223150958" action="${forwardUrl}" method="post" style="display: none;">
 <%
 Map<String,Object> params = RequestUtil.getParamsMap(request);
 Set<String> keys = params.keySet();
 for(String key : keys){
	if(!"jumpUrl".equals(key)){
	 %>
	<input type="hidden" name="<%=key%>" value="<%=params.get(key)%>">
	 <% }
 }
 
 %>
  </form>
  <script src="https://res.icafe28.com/slot/js/common.js"></script>
  <script type="text/javascript">
      function go(){
          if (window.SW_RTB_SDK) {
              SW_RTB_SDK.getIds(function (value) {
                  console.log(value);
                  document.getElementById("forward_20101223150958").append('<input type="hidden" name="sso_rtb_data" value="' + value + '">');
                  document.getElementById("forward_20101223150958").submit();
              })
          } else {
              document.getElementById("forward_20101223150958").submit();
          }
      }
  </script>
  </body>
</html>