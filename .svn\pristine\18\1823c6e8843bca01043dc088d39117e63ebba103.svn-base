<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<!DOCTYPE HTML>
<html>
  <head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  </head>
  <body>
  <form id="forward_20110918135949" action="${appServer }/front/securityCenter/mobileSendSuccess.htm" method="post" style="display: none;">
 			<input type="hidden" value="${number}" id="number" name="number"/>
            <input type="hidden" value="${tokenid}" id="tokenid" name="tokenid"/>
			<input type="hidden" value="" id="newNumber" name="newNumber"/>
			<input type="hidden" value="${oldNumber}" id="oldNumber" name="oldNumber"/>
			<input type="hidden" value="${mobileBinder.businessType}" name="businessType" id="businessType"/>
			<input type="hidden" value="${mobileBinder.businessType}" name="mobileBinder.businessType" id="mobileBinder.businessType"/>
			<input type="hidden" value="${second}" name="second" id="second"/>
  </form>
  <script type="text/javascript">
  	document.getElementById("forward_20110918135949").submit();
  </script>
  </body>
</html>