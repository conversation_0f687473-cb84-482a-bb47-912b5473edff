<%@ page contentType="text/html; charset=UTF-8" %>
<%@ include file="../common/taglibs.jsp" %>
<%@ page import="com.shunwang.basepassport.user.common.UserCheckUtil" %>
<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta name="format-detection" content="telephone=no">
    <title></title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=3.0, minimum-scale=1.0, user-scalable=no">
    <link href="${staticServer}/${cdnVersion}/style/public-gray.css" rel="stylesheet" type="text/css"/>
    <c:choose>
        <c:when test="${not empty html5Css && not empty html5Css.cssUrl }">
            <link href="${html5Css.cssUrl}" rel="stylesheet" type="text/css"/>
        </c:when>
        <c:otherwise>
            <link href="${staticServer}/${cdnVersion}/style/defaultForHtml5.css" rel="stylesheet" type="text/css"/>
        </c:otherwise>
    </c:choose>
    <%
		String validPhone = UserCheckUtil.getValidPhoneNum();
	%>
</head>

<body id="dialog-checkcode" class="login-mobile">
<c:if test="${!(hideHeader == 'true')}">
<header class="header-bar header-bar-sublevel">
    <h1 class="title"><b class="ico-logo"></b>顺网通行证</h1><button type="button" class="back"></button>
</header>
</c:if>
<div role="main" class="main">
    <div class="tips-error" id="tips-error" style="display: none;">
        <p>${msg}</p>
    </div>
    <div class="tab">
        <ul class="tabs noslide">
            <li <c:if test="${empty mobileLogin}">class="active" </c:if>><a class="react">通行证账号登录</a></li>
            <li <c:if test="${not empty mobileLogin}">class="active" </c:if>><a class="react">手机快速登录</a></li>
        </ul>
        <div class="slide"></div>
    </div>
    <form id="login-form" action="" method="get">
        <s:hidden name="callbackUrl"/>
        <s:hidden name="site_id" id="site_id"/>
        <s:hidden name="cssSiteId"/>
        <s:hidden name="css"/>
        <s:hidden name="version"/>
        <s:hidden name="env"/>
        <s:hidden name="needCode" />
        <s:hidden name="extData"/>
        <s:hidden name="userName" id="userName"/>
        <s:hidden name="password" id="password"/>
        <s:hidden name="weakPwdState" id="weakPwdState"/>
        <s:hidden name="smsCheckCode" id="smsCheckCode"/>
        <s:hidden name="checkCode" id="checkCode"/>
        <s:hidden name="md5" id="md5"/>
        <s:hidden name="loginSiteId" id="loginSiteId"/>
        <s:hidden name="token" id="token"/>
        <s:hidden name="loginType" id="loginType" value="quickLogin"/>
        <s:hidden name="loginPage" value="inline"/>
        <s:hidden name="unionid" />
        <s:hidden name="openId" />
        <s:hidden name="sign" />

        <s:hidden name="number" id="mobile"/>
        <s:hidden name="mobileActiveNo" id="mobileActiveNo"/>
        <s:hidden name="token_site_id" id="tokenSiteId"/>
        <s:hidden name="mobileValifyTokenId"/>

        <s:hidden name="tgt" id="oauthTgt"/>
        <s:hidden name="linkTgt" id="linkTgt" />
        <s:url var="regUrlTag" escapeAmp="false" value="%{#attr.regLink}">
            <s:param name="returnUrl" value="%{callbackUrl}"/>
            <s:param name="site_id" value="%{site_id}" />
            <s:param name="regVersion" value="%{version}"/>
            <s:param name="env" value="%{env}" />
        </s:url>
    </form>

    <div class="login-form form-box" <c:if test="${not empty mobileLogin}">  style="display:none" </c:if> id="login_form_">
        <div class="form-group placeholder"><input type="text" id="userNameInp" placeholder="通行证账号/手机号/邮箱" class="form-control"></div>
        <div class="form-group placeholder inline-group"><input type="password" placeholder="请输入登录密码" id="passwordInp" class="form-control"><input type="checkbox" id="password-show" class="weui_switch"></div>
        <div class="other-group btn-box"><button type="button" id="sso-login" class="btn btn-primary" >登录</button></div>
    </div>
    <%--</form>--%>

    <div id="tel-login-form" <c:if test="${empty mobileLogin}">  style="display:none" </c:if> class="login-form form-box">
        <div class="form-group placeholder inline-group"><input id="number" type="tel" placeholder="请输入手机号码"  value="${number}" class="form-control"><button type="button" id="sent-code" class="btn btn-primary btn-mini">发送验证码</button></div>
        <div class="form-group placeholder"><input type="tel" id="mobileCheckCode" placeholder="请输入手机短信中的验证码" class="form-control"></div>

        <div class="protocol-tip" style=" padding: 10px 0 0 10px;">
            <p>
                <input type="checkbox" class="form_check" tabindex="7" id="agreementCheckbox"/>
                <span class="agreement_chk">我已阅读并接受</span>
                <c:if test="${not empty userAgreementCss.cssUrl}"><a href="${userAgreementCss.cssUrl}" target="_blank" class="a035">《${userAgreementCss.remark}》</a></c:if>
                <c:if test="${not empty privacyCss.cssUrl}"> 及<a href="${privacyCss.cssUrl}"  target="_blank" class="a035">《隐私政策》</a></c:if>
            </p>
        </div>
        <div class="other-group btn-box"><button type="submit" id="tel-login" class="btn btn-primary">登录</button></div>
    </div>

    <div class="login-btns clearfix">
        <c:if test="${needImmediatelyReg == true }">
            <a href="<s:url value="%{regUrlTag}" />" class="link-reg" target="${linkTgt}">立即注册</a>
        </c:if>
        <c:if test="${needRetrievePwd == true }">
            <a href="${identityServer}/findPwd/step1" class="link-forget-password" target="${linkTgt}" >找回密码</a>
        </c:if>
    </div>

    <div class="login-others">
        <div class="hd">第三方登录</div>
        <div class="bd">
            <c:forEach items="${outOauthDirs}" var="oauthDir">
                    <c:forEach items="${oauthDir.outOauthInterfaces}" var="useroutInterface">
                            <a class="drop drop${oauthDir.linkId}" href="###" onclick="${useroutInterface.linkUrl}">
                                <img alt="使用${useroutInterface.serviceProvider}登录"
                                     src="${staticServer}/${cdnVersion}/images/outOauth/${useroutInterface.serviceBigImg}"/>
                            </a>
                    </c:forEach>
            </c:forEach>
        </div>
    </div>

    <div id="modal_select" style="display:none" class="modal">
        <div class="modal-content">
            <header class="header-bar">
                <h1 class="title">选择关联通行证账号</h1><button type="button" class="close"></button></header>
            <div class="modal-body">
                <div class="modal-logout-message">
                    <section>
                        <ul id="choisedUsername" class="user-name"></ul>
                    </section>
                </div>
                <div class="btn-box"><button type="button" id="choiseAccount" class="btn btn-primary">确认</button></div>
            </div>
        </div>
    </div>

    <div id="captcha" style="display:none"></div>

    <script type="text/javascript" src="${staticServer}/${cdnVersion}/js/jquery-2.2.0.min.js"></script>
    <script type="text/javascript" src="${staticServer}/${cdnVersion}/login/js/jsencrypt.js"></script>
    <script type="text/javascript" src="${staticServer}/${cdnVersion}/login/js/loginForHtml5.js?v=1"></script>
    <script type="text/javascript" src="${staticServer}/${cdnVersion}/js/md5.js"></script>
    <script type="text/javascript" src="${staticServer}/${cdnVersion}/js/gt.js"></script>
    <script type="text/javascript" src="${staticServer}/${cdnVersion}/js/gtUtil.js"></script>

    <script type="text/javascript" src="${staticServer}/${cdnVersion}/login/js/pwdUtil.js"></script>
    <script>
        // 查询兄弟元素函数
        function siblings(elm, className) {
            var a = [];
            var p = className ? elm.parentNode.querySelectorAll(className): elm.parentNode.children;
            for (var i = 0, pl = p.length; i < pl; i++) {
                if (p[i] !== elm) a.push(p[i]);
            }
            return a;
        }
        //tab 切换
        var $tab = document.querySelector('.tabs'),
                $formBox = document.querySelectorAll(".form-box"),
                $slide = document.querySelector('.tab .slide'),
                $li = $tab.querySelectorAll('li'),
                len = $li.length;

        for (var i = 0; i < len; i++) {
            (function(i) {
                $li[i].onclick = function() {
                    var $this = this,
                            $thisForm = $formBox[i];
                    $this.className = 'active';
                    $thisForm.style.display = "block";
                    $slide.style.left = 50 * i + "%";

                    // 给兄弟元素删除class
                    var _sib = siblings(this),
                            _sibForm = siblings($thisForm,'.form-box');
                    for (var j = 0; j < _sib.length; j++) {
                        // 删除样式
                        _sib[j].removeAttribute("class");
                        _sibForm[j].style.display = 'none';
                    };
                }
            })(i);
        }
    </script>
    <script>
        var SSO_SERVER = "${ssoServer}";
        var interfaceServer = "${interfaceServer}";
        var needCheckCode = "${needCheckCode}" == 'true';
        var needSmsCheckCode = "${needSmsCheckCode}" == 'true';
        var validPhoneRex = <%=validPhone%>;
        var smsSendExpTime = '${smsSendExpTime}';

        var errorMsg = "${msg}";
        if(errorMsg =='密码不正确！')
            $('#tips-error p').html('账号或密码错误，是否 <s:a href="%{urlTag}" cssClass="link-forget-password">找回密码</s:a>')
        if(errorMsg !='')
            $('#tips-error').show();

        $(function () {
            var mobileLogin = "${mobileLogin}";
            if(mobileLogin == "MOBILE_LOGIN") {
                $(".tab .slide").css({
                    "left": "50%"
                })
            } else {
                $(".tab .slide").css({
                    "left": "0px"
                })
            }

            if ($(".tips-info").is(":visible")) {
                $("body").on("click", function () {
                    tipClose()
                })
            }

            var username = [${userNameList}];
            var usernameStr = "";
            if(username.length > 0) {
                for (var i = 0; i < username.length; i++) {
                    usernameStr += "<li><label><input type='radio' name='userName' value='" + username[i] + "'>" + username[i]
                    "</label></li>";
                }
                $("#choisedUsername").html(usernameStr);
                $("#modal_select").show();
                $(".modal").on("click", ".close", function () {
                    $(this).parents(".modal").hide();
                })
            }

            $(document).click(cleanTip);
        })

        function optAfter() {
            if ($("#login_form_").css("display") == 'none') {
                sendAgain(function () {
                    if (!!smsLoginGt.captchaObj) {
                        smsLoginGt.captchaObj.reset();
                    }
                })
            } else {
                loginFormLogin();
            }
        }
        var loginGt = $.gtUtil({
                "showGt": needCheckCode,
                "btnId": "sso-login",
                "formId": "login-form",
                'bussSend': optAfter,
                'checkParam': function () {
                    return loginFormLoginCheck(false);
                },
                'showGtMsg': function (msg) {
                    showNormalLoginError(msg);
                }
            }
        );
        var smsLoginGt = $.gtUtil({
                "showGt": needSmsCheckCode,
                "btnId": "sent-code",
                "formId": "login-form",
                'bussSend': optAfter,
                'checkParam': numberInputCheck,
                'showGtMsg': function (msg) {
                    showSmsLoginError(msg);
                }
            }
        );
    </script>
</div>

</body>

</html>