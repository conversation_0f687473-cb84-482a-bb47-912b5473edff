package com.shunwang.basepassport.user.web;

import com.shunwang.baseStone.context.BaseStoneContext;
import com.shunwang.baseStone.core.action.BaseStoneAction;
import com.shunwang.baseStone.encrypt.Encrypt;
import com.shunwang.baseStone.encrypt.pojo.EncryptItem;
import com.shunwang.basepassport.commonExp.ParamNotFoundExp;
import com.shunwang.basepassport.user.common.MemberConstants;
import com.shunwang.basepassport.user.common.UserCheckUtil;
import com.shunwang.basepassport.user.dao.MemberDao;
import com.shunwang.basepassport.user.exception.EmailNotAsLoginAccountExp;
import com.shunwang.basepassport.user.exception.MobileNotAsLoginAccountExp;
import com.shunwang.basepassport.user.exception.MsgNotFoundExp;
import com.shunwang.basepassport.user.pojo.Member;
import com.shunwang.basepassport.user.response.MemberQueryFullRespone;
import com.shunwang.util.lang.StringUtil;

/**
 * @Described：用户VIP信息查询接口  目前仅有yh非主要业务在调用，未查到具体业务2021.1.20
 * <AUTHOR> create at 2012-6-14 上午10:25:53
 * @FileNmae com.shunwang.basepassport.user.web.MemberQueryFullAction.java
 */
public class MemberQueryFullAction extends BaseStoneAction {

	private static final long serialVersionUID = -1773767901913308915L;

	public String userName;
	/**查询类型,type="1"按id查**/
	public String type;
	public MemberDao memberDao;

	@Override
	public String buildSignString() {
		Encrypt  encrypt=new Encrypt();
		encrypt.addItem(new EncryptItem(super.getSiteId()));
		encrypt.addItem(new EncryptItem(userName));
		encrypt.addItem(new EncryptItem(super.getTime()));
		return encrypt.buildSign();
	}

	@Override
	public String getSiteName() {
		return MemberConstants.MEMBER_VIP_QUERY;
	}

	/**
	 * (non-Javadoc)
	 *  @see com.shunwang.baseStone.core.action.BaseStoneAction#process()
	 *  <AUTHOR> create at 2012-6-14 上午10:29:04
	 */
	@Override
	public void process() {
		Member member = findMember();
		if(null != member) {
			member.setMemberInfo(member.loadMemberInfo());
		}
		MemberQueryFullRespone response = new MemberQueryFullRespone(member);
		this.setBaseResponse(response);
	}



	private Member findMember() {
		if(null != type && type.equals(MemberConstants.QUERY_TYPE)) {
			return getMemberById();
		} else {
			return getMember();
		}
	}

	private Member getMemberById() {
		Member member = null;
		member = getMemberDao().getById(userName);
		if(null == member)
			throw new MsgNotFoundExp(userName);
		return member;
	}

	/**
	 * 分类查询用户信息
	 * @return
	 * <AUTHOR> create at 2012-6-14 上午10:27:01
	 */
	private Member getMember() {
		Member member = null;
		if(UserCheckUtil.checkEmail(userName)) {
			member = getMemberDao().getByEmail(userName);
            if (null == member) {
                Integer bindCnt = getMemberDao().getCntByEmail(userName); //查询该邮箱有没有绑定过通行证
                if (null != bindCnt && bindCnt != 0) { //有绑定过,说明没有一个通行证将该邮箱设为登录账号。
                    throw new EmailNotAsLoginAccountExp();
                }
            }
		} else if(UserCheckUtil.checkMobile(userName)) {
			member = getMemberDao().getByMobile(userName);
            if (null == member) {
                Integer bindCnt = getMemberDao().getCntByMobile(userName); //查询该手机号码有没有绑定过通行证
                if (null != bindCnt && bindCnt != 0) { //有绑定过,说明没有一个通行证将该手机号码设为登录账号。
                    throw new MobileNotAsLoginAccountExp();
                }
            }
		} else {
			member = getMemberDao().getByName(userName);
		}
		if(null == member)
			throw new MsgNotFoundExp(userName);
		return member;
	}

	public MemberDao getMemberDao() {
		if(null == memberDao)
			return (MemberDao)BaseStoneContext.getInstance().getBean("memberDao");
		return memberDao;
	}

	@Override
	public void checkParam(){
		if(StringUtil.isBlank(userName))
			throw new ParamNotFoundExp("用户名");
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = doUrlEncode(userName);
	}

	public void setMemberDao(MemberDao memberDao) {
		this.memberDao = memberDao;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

}
