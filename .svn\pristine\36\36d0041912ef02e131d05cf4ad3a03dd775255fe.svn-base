/*
 * $Id$
 * Copyright (c)  by iCafeMavin Information Technology Inc. All right reserved.
 */
package com.shunwang.basepassport.safeNotice.pojo;

import com.shunwang.baseStone.context.RedisContext;
import com.shunwang.basepassport.safeNotice.constants.SafeNoticeConstants;
import com.shunwang.util.lang.StringUtil;

/******************************
** 版权所有：顺网科技 保留所有权利
 ** 创建日期: 2013-12-15上午02:19:33
 ** 创建作者: <PERSON><PERSON> (<EMAIL>)   
 ** 版本:  2.0
 ** 功能: 
 ** 最后修改时间:
 ** 修改记录:
 ********************************/

public class SendSmsSafeNoticeEmailChange extends SendSmsSafeNotice{

	/**
	 * 
	 */
	private static final long serialVersionUID = 7981975473357585870L;

	@Override
	public String getSmsContent() {
		String content = RedisContext.getResourceCache().getResourceValue(SafeNoticeConstants.SAFE_NOTICE_CONFIG, SafeNoticeConstants.EMAIL_CHANGE_NOTICE_SMS_CONTENT);
		if(StringUtil.isEmpty(content)) {
			content = "";
		}
		return content;
	}
}
