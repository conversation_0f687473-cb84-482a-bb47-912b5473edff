package com.shunwang.baseStone.core.dao;

import java.util.List;

import com.shunwang.baseStone.core.pojo.BaseStoneObject;
import com.shunwang.framework.inf.crudinf.ConditionCrud;


/******************************
 * 版权所有：顺网科技 保留所有权利
 * 创建日期: 2011 Jul 19, 2011 2:20:16 PM
 * 创建作者：zhangjp
 * 文件名称：IibatisDao.java
 * 版本： 1.0
 * 功能：dao接口
 * 最后修改时间：
 * 修改记录：
 *****************************************/

public interface IDao<Pojo extends BaseStoneObject> extends ICrud<Pojo>,ConditionCrud<Pojo> {


	/**
	 * ****************************
	 * 创建日期: 2011 Jul 19, 2011 8:23:04 PM
	 * 创建作者：zhangjp
	 * 功能：查询所有
	 ****************************************
	 */
	public List<Pojo> findAll();
	/**
	 * ****************************
	 * 创建日期: 2011 Jul 19, 2011 8:23:16 PM
	 * 创建作者：zhangjp
	 * 功能：根据列进行查询后统计条数
	 ****************************************
	 */
	public Integer findCnt(String columnName,Object columnValue);
	/**
	 * ****************************
	 * 创建日期: 2011 Jul 19, 2011 8:23:21 PM
	 * 创建作者：zhangjp
	 * 功能：根据列进行查询
	 ****************************************
	 */
	public List<Pojo> find(String columnName,Object columnValue);


}
