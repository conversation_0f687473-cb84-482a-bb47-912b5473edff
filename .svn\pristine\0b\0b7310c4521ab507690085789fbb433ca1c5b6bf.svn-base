package com.shunwang.basepassport.binder.web.bind.processor;

import com.shunwang.basepassport.binder.common.BinderConstants;
import com.shunwang.basepassport.binder.pojo.SendBinder;
import com.shunwang.basepassport.binder.processor.Processor;
import com.shunwang.basepassport.binder.processor.ProcessorContext;
import com.shunwang.basepassport.binder.web.send.bean.MobileBindBean;
import com.shunwang.basepassport.user.common.PwdUtil;
import com.shunwang.basepassport.user.pojo.Member;


/**
 * 2 短信验证：找回密码
 *
 * <AUTHOR>
 * @date 2018/12/18
 **/
public class FindPwdProcessor implements Processor {
    @Override
    public boolean matches(ProcessorContext context) {
        return BinderConstants.FINDPWD.equals(context.getSendBinder().getBusinessType());
    }

    @Override
    public boolean doProcess(ProcessorContext context) {
        SendBinder sendBinder = context.getSendBinder();
        MobileBindBean bean = (MobileBindBean) context.getT();

        sendBinder.setNumber(bean.getNumber());
        sendBinder.validate(bean.getActiveNo());
        Member member = sendBinder.getMember();
        member.setMemberPwd(PwdUtil.convertMd5(bean.getPassword()));
        member.update();
        return false;
    }
}
