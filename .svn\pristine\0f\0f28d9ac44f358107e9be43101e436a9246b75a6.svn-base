package com.shunwang.baseStone.sso.apapter;

import com.shunwang.baseStone.sso.constant.UserOutsiteConstant;
import com.shunwang.baseStone.config.constants.ConfigOauthConstant;
import com.shunwang.basepassport.user.pojo.MemberAccountBind;

public class ConnectedWeiboUIDAdapter extends AbstractConnectedAccountsAdapter {

    private static final long serialVersionUID = -3006925934796862703L;
    @Override
    protected MemberAccountBind getByUnionId() {
        return memberAccountBindDao.getByWeibo(member.getMemberId());
    }

    @Override
    protected void initBindId(MemberAccountBind memberAccountBind, Integer memberId) {
        memberAccountBind.setWeibo(memberId);
    }
    @Override
    protected int getAdapterType() {
        return ConfigOauthConstant.TYPE.WEIBO.getInt();
    }

    /**
     * @return
     */
    @Override
    protected String generalMemberName() {
        return useroutInterface.getPrefixName() + getUserId();
    }

    @Override
    protected String getOutMemberName() {
        return getUserId();
    }

    @Override
    public String getInterfaceId() {
        return UserOutsiteConstant.WEIBO_INTERFACE_ID;
    }

}
