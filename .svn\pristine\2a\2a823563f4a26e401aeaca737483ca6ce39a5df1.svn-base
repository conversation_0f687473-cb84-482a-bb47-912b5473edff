/**
 * Created by min.da on 15-5-08.
 */

var $form = $('.form-box'),
    $password = $form.find('input[name="password"]'),
    $tokenid = $form.find('input[name="valifyTokenId"]'),
    $confirmPwd = $form.find('input[name="confirmPwd"]'),
    $submit = $form.find('.btn.btn-primary');

var MSG = {
    C1001: '请输入新密码',
    C1002: '请输入6-16个字符的密码',
    C1003: '密码需包含数字、字母、标点符号的组合',
    C1004: '密码不能全为同一字符',
    C1006: '请输入确认密码',
    C1007: '两次输入的密码不一致',
        C1040: "密码不能与用户名一致。"
    },
    ACTION = {
        '手机': $("#mobileResetPwdUrl").val(),
        '邮箱': $("#emailResetPwdUrl").val(),
        '密保问题': $("#questionResetPwdUrl").val()
    };

function checkPwd() {
    var pv = $.trim($password.val());

    var len = pv.length;
    if (len == 0) {
        showTips(MSG.C1001);
        return false;
    }
    if (len < 6 || len > 16) {
        showTips(MSG.C1002);
        return false;
    }

    if (!/^[A-Za-z0-9`~!@#\$%\^&\*\(\)\-_=\+\[\{\]\}\\\|;:'"",<.>\/\?]+$/.test(pv)) {
        showTips(MSG.C1003);
        return false;
    }
    if (!/^(?![A-Za-z]+$)(?!\d+$)(?![`~!@#\$%\^&\*\(\)\-_=\+\[\{\]\}\\\|;:'"",<.>\/\?]+$)\S{2,16}$/.test(pv)) {
        showTips(MSG.C1003);
        return false;
    }
    var un = $submit.attr('un-data');
    if (pv == un) {
        showTips(MSG.C1040);
        return false;
    }
    if (weakPwdCheck(pv)) {
        showTips(MSG.C1003);
        return false;
    }

    if (/^(\w)\1+$/.test(pv)) {
        showTips(MSG.C1004);
        return false;
    }

    return true;
}

function weakPwdCheck(pwd) {
    if (/^[a-zA-Z]+$/.test(pwd) || /^[0-9]+$/.test(pwd) || /^[_#]+$/.test(pwd)) {
        return true;
    }
    return false;
}
function checkConfirmPwd() {
    var cpv = $.trim($confirmPwd.val());

    if (cpv.length == 0) {
        showTips(MSG.C1006);
        return false;
    }

    if (cpv != $.trim($password.val())) {
        showTips(MSG.C1007);
        return false;
    }

    return true;
}
function showTips(msg) {
    $('.tips-error p').html(msg);
    $('.tips-error').show();
}

function validate() {
    return checkPwd() && checkConfirmPwd();
}
var submitted = false;
function submit() {
    if (validate() && !submitted) {
        submitted = true;

        $.ajax({
            url: ACTION[$submit.attr('t-data')],
            type: 'post',
            data: {'memberName': $submit.attr('un-data'), 'password': hex_md5($password.val()), 'confirmPwd': hex_md5($confirmPwd.val()),'valifyTokenId':$tokenid.val()},
            dataType: 'json',
            success: function(json) {
                if (json.result) {
                    window.MobileBase.close();
                    if(json.returnUrl != undefined && json.returnUrl.length > 0){
                        var returnUrl = json.returnUrl ;
                        returnUrl = (returnUrl.indexOf("?") >= 0 ? (returnUrl+"&") : (returnUrl+"?")) ;
                        top.location = returnUrl+"msg="+encodeURI("重置登录密码成功");
                    }else {
                        top.location = $CONFIG.appServer + '/front/swpaysdk/member/login.jsp?msg=' + encodeURI("重置登录密码成功");
                    }
                } else {
                    showTips(json.msg);
                    submitted = false;
                }
            },
            error: function(e) {
                submitted = false;
                showTips(e);
            }
        });
    }
}

function init() {
    $submit.on('click', function() {
        submit();
        return false;
    });

    $form.find('input.form-control').on('keyup', function() {
        $.trim($password.val()).length != 0 && $.trim($confirmPwd.val()).length != 0
        ? $submit.removeAttr('disabled') : $submit.attr('disabled', true);
    });

    //override
    window.MobileBase.close = function() {
        try {
            window.SWMobileSDK.onBack();
        } catch (e) {
            try{
                window.webkit.messageHandlers.resetSucc.postMessage({body:'重置登录密码成功'});
            }catch (ex){
                console.error(ex);
            }
        }
    }
}

init();
