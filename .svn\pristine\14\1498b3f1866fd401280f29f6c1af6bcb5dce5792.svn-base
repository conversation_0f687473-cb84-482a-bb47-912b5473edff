package com.shunwang.baseStone.config.constants;

/**
 * User:pf.ma
 * Date:2020/07/07
 * Time:10:53
 */
public class ConfigOauthConstant {


	// 第三方账号类型
	public enum TYPE{
		NORMAL(0),
		QQ(1),
		WEIXIN(2),
		WEIBO(3),
		APPLE(4),
		ALIPAY(5),
		ID_CARD(6);

		private Integer typeId ;
		TYPE(Integer typeId){
			this.typeId = typeId ;
		}

		public Integer getInt(){
			return this.typeId ;
		}

		public static String getTypeStr(Integer typeId){
			if (typeId == 1) {
				return "QQ账号" ;
			} else if(typeId == 2) {
				return "微信账号" ;
			} else if(typeId == 3) {
                return "微博账号" ;
            } else if(typeId == 4) {
				return "Apple账号" ;
			} else if(typeId == 5) {
				return "支付宝账号" ;
			} else if(typeId == 6) {
				return "身份证账号" ;
			} else {
				return "账号" ;
			}
		}
	}

	// 第三方默认状态
	public enum DEFAULT_STATE{
		YES(1) ;

		private Integer typeId ;
		DEFAULT_STATE(Integer typeId){
			this.typeId = typeId ;
		}

		public Integer getInt(){
			return this.typeId ;
		}

		public static String getDefaultStateType(Integer defaultState){
			if(null != defaultState && defaultState == 1){
				return "默认";
			}
			return "" ;
		}
	}
}
