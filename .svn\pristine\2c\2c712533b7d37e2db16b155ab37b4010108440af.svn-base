package com.shunwang.basepassport.actu.exception;

import com.shunwang.baseStone.core.exception.BaseStoneException;
import com.shunwang.basepassport.binder.common.ErrorCode;

public class TimeExceedExp extends BaseStoneException {

    public TimeExceedExp(ErrorCode errorCode) {
        this(errorCode.getCode(), errorCode.getDescription());
    }

    public TimeExceedExp(String msgId, String msg) {
        super(msgId, msg);
    }
}
