package com.shunwang.baseStone.sender;

import junit.framework.TestCase;

import org.springframework.context.support.ClassPathXmlApplicationContext;

import com.shunwang.baseStone.context.BaseStoneContext;
import com.shunwang.baseStone.sender.context.SenderContext;
import com.shunwang.baseStone.sender.pojo.SendMsg;

public class EmailSender extends TestCase{
	
	public void testSend(){
		BaseStoneContext.getInstance().setFileName("/baseStoneContext.xml");
		new ClassPathXmlApplicationContext("/baseStoneContext.xml");
		SendMsg sendMsg = new SendMsg();
		sendMsg.setContent("1234");
		sendMsg.setNumber("<EMAIL>");
		sendMsg.setTitle("dfsdfsdf");
		SenderContext.getEmailSender().doSend(sendMsg);
	}

}
