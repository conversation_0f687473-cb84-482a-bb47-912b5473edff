package com.shunwang.baseStone.tag;

import javax.servlet.jsp.JspException;

import com.shunwang.baseStone.sysconfig.context.SysConfigContext;

public class ImgUrl extends SWTag {
	/**
	 * <AUTHOR> create at 2011-10-17 下午02:17:04 
	 */
	private static final long serialVersionUID = -7875175740786281006L;
	private String value;

	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}
	@Override
	public int doStartTag() throws JspException {
		// TODO Auto-generated method stub
		StringBuffer sb = new StringBuffer();
		sb.append(SysConfigContext.getImgUrl());
		sb.append("/");
		sb.append(value);
		print(sb.toString());
		return SKIP_BODY;
	}

}
