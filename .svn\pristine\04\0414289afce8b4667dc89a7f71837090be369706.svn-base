var swfuBack;
var swfuFront;
var buttonTextBack ="";
var buttonTextFront="";
		window.onload = function() {		
			var settingsFront = {
				flash_url : $CONFIG.staticServer+"/plugins/swfupload/2.5.0/swfupload.swf",
				upload_url: $CONFIG.appServer+"/front/noLogin/uploadImage.htm?idCardType=front",
				post_params: {},
				file_size_limit : "2 MB",
				file_types : "*.jpg;*.gif;*.jpeg;*.png",
				file_types_description : "*.jpg;*.gif;*.jpeg;*.png",
				file_upload_limit : 1000,
				file_queue_limit : 0,
				file_post_name:"attachment",//加了这个，在webwork的action中要用
				debug: false,

				// Button settings
				button_image_url: $CONFIG.staticServer+"/images/front/v2/bt_bg17.gif",
				button_width: "95",
				button_height: "26",
				button_placeholder_id: "spanButtonPlaceHolder2",
				button_text: buttonTextFront,
				button_text_style: ".theFont { font-size: 16; }",
				button_text_left_padding: 23,
				button_text_top_padding: 3,
                swfupload_preload_handler : preLoadShow,
				file_queue_error_handler : uploadStartFront,
			  //  file_dialog_start_handler:isLoginFront,
				file_dialog_complete_handler : fileDialogCompleteFront,
				upload_error_handler : errorFront,
				upload_success_handler : successFront
			};
			swfuFront = new SWFUpload(settingsFront);
			setAlign();
	     };

function fileDialogCompleteBack(){ 
		try {
		$.post(
            $CONFIG.appServer+"/front/actuality/isLogin.htm",
		{},  
		 function(json){
		 if(json.flag!=true){
		 	location.reload();
		 	return false;
		 } 
		 var userId = json.userId;
		 var rand = json.rand;
		 var sign =json.sign;
		 var data ={"member.memberId":userId,"rand":rand,"uploadSign":sign};
		 swfuBack.setPostParams(data);
		 swfuBack.startUpload();
		 return true;
		 },
		"json"
		); 
	} catch (ex)  {
      //  this.debug(ex);
	} 
}
function successFront(file, serverData,responseReceived){
    swfuFront.setButtonText("");
	setShowMsg("uploadMsg1","您已成功上传");
	var idCardImg11 = serverData.split("|")[1];
	var idCardImg1Url = serverData.split("|")[2];	
	$("#idCardImg1").val(idCardImg11);
	$("#idCardImgUrlActu").val(idCardImg1Url+"/"+idCardImg11);	
	parent.$("#idCardImg1Error").html("");	
  	parent.$("#imageFile1Show").show();
}
function uploadStartFront(file, errorCode, message){	
	if(errorCode==SWFUpload.QUEUE_ERROR.FILE_EXCEEDS_SIZE_LIMIT)
	{setShowErrorMsg("idCardImg1Error","图片超过2M,请重新上传");
	parent.$("#uploadMsg1").html("");
	return false;
	}else if(errorCode == SWFUpload.QUEUE_ERROR.ZERO_BYTE_FILE){
		setShowErrorMsg("uploadMsg1","图片为0字节");
		parent.$("#idCardImg1Error").html("");
		return false;
	}
	
}
function errorFront(){
	setShowErrorMsg("uploadMsg1","图片上传失败");
}

function fileDialogCompleteFront(){ 
		
	//	 var data ={"member.memberId":userId,"rand":rand,"uploadSign":sign};
		// swfuFront.setPostParams(data);
		 swfuFront.startUpload();
		 return true;
		
}
function setAlign(){
    $("#SWFUpload_0").attr("align","absmiddle");
    $("#SWFUpload_1").attr("align","absmiddle");
}
function preLoadShow() {
	if (!this.support.loading) {
		$("#flashEditerFront").show();		
		return false;
	}
}