<%@ page import="com.shunwang.basepassport.user.common.UserCheckUtil" %>
<%@ page import="com.shunwang.basepassport.user.pojo.Member" %>
<%@ page import="com.shunwang.basepassport.context.UserContext" %>
<%@ page import="com.shunwang.common.StringUtils" %>
<%@ page contentType="text/html;charset=UTF-8"%>
<%@ include file="/common/taglibs.jsp"%>

<%
    Member member = UserContext.getMember();
    String lastLog = UserContext.getLogonLogForLast();
    String[] lastLogArray = StringUtils.isNotBlank(lastLog) ? lastLog.split("\\|") : null;
    String timeLogon = UserContext.getTimeLogon();
%>
<!DOCTYPE HTML>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title><decorator:title/></title>
    <link rel="stylesheet" type="text/css" href="${staticServer}/styles/public.css" />
    <script src="${staticServer}/scripts/common/jquery.js"></script>
    <script type="text/javascript">
        var $CONFIG = {};
        $CONFIG['SSO_SERVER'] = '${SSO_SERVER}';
        $CONFIG['appServer'] = '${appServer}';
        $CONFIG['kedouServer'] = '${kedouServer}';
        $CONFIG['baseServer'] = '${baseServer}';
        $CONFIG['staticServer'] = '${staticServer}';
        var validPhoneRex = <%= UserCheckUtil .getValidPhoneNum()%>;
    </script>
    <decorator:head/>
</head>
<body <decorator:getProperty property="body.onload" writeEntireProperty="true"/>>
<jsp:include page="/common/banner_js.jsp"></jsp:include>
<jsp:include page="/common/head.jsp"></jsp:include>
<!-- 公告 start -->
<jsp:include page="/common/notice.jsp"></jsp:include>
<!-- 公告 end -->
<div class="wrap" id="wrap">
    <!-- user start -->
    <jsp:include page="/common/user_group.jsp"></jsp:include>
    <!-- user end -->
    <div class="container">
        <jsp:include page="/common/frontLeftMenu.jsp"></jsp:include>
        <div class="shop">
            <decorator:body/>
        </div>
        <div class="service_tel">客服热线：400-8559558</div>
    </div>
</div>
<!-- footer start -->
<jsp:include page="/common/foot.jsp"></jsp:include>
<!-- footer end -->
<script type="text/javascript">
	var staticServer = '${staticServer}';
	var appServer = '${appServer}';
</script>
<script src="${staticServer}/scripts/front/topic1111.js"></script>
<script src="${staticServer}/scripts/front/base.min.js"></script>
<script src="${staticServer}/scripts/front/member/base.js"></script>
<script src="${staticServer}/scripts/front/notice.min.js"></script>
<script type="text/javascript" src="${staticServer}/scripts/front/member/index.js"></script>

</body>
</html>
			
