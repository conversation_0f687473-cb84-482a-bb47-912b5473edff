package com.shunwang.basepassport.rpc;

import com.shunwang.toolbox.rpc.RpcRequest;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: lj.zeng
 * @create: 2023-10-31 14:08:09
 * @Description:
 */
public class WxCardMsgRequest implements RpcRequest {

    private String openId;
    /**
     * 场景值
     */
    private String eventKey;
    /**
     * 模板消息id
     */
    private Integer msgId;

    public Map<String, String> getParamMap() {
        Map<String, String> map = new HashMap<>();
        map.put("openId", openId);
        map.put("eventKey", eventKey);
        map.put("msgId", msgId + "");
        return map;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public String getEventKey() {
        return eventKey;
    }

    public void setEventKey(String eventKey) {
        this.eventKey = eventKey;
    }

    public Integer getMsgId() {
        return msgId;
    }

    public void setMsgId(Integer msgId) {
        this.msgId = msgId;
    }
}
