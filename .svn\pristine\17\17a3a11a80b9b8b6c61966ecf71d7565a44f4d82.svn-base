<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	   xmlns:aop="http://www.springframework.org/schema/aop"
	   xsi:schemaLocation="http://www.springframework.org/schema/beans
	http://www.springframework.org/schema/beans/spring-beans.xsd
    http://www.springframework.org/schema/aop
	http://www.springframework.org/schema/aop/spring-aop.xsd">

	<bean id="configurer"
		class="org.springframework.beans.factory.config.PropertyPlaceholderConfigurer">
		<property name="locations">
			<list>
			    <value>file:${BASE_KEDOU_CONFIG_HOME}/cache.properties</value>
                <value>file:${BASE_KEDOU_CONFIG_HOME}/baseStoneDb.properties</value>
                <value>file:${BASE_KEDOU_CONFIG_HOME}/email.properties</value>
                <value>file:${BASE_KEDOU_CONFIG_HOME}/domain.properties</value>
				<value>file:${BASE_KEDOU_CONFIG_HOME}/server.properties</value>
			</list>
		</property>
	</bean>
	
	<import resource="classpath*:/cookie.xml"/>
	<import resource="classpath*:/baseStone.xml"/> 
	<import resource="classpath*:/baseStoneCache/spring/cache.xml"/>
	<import resource="classpath*:/baseStone/selfspring/*.xml"/>

	<aop:aspectj-autoproxy proxy-target-class="true"/>

	<bean id="encryptAspect" class="com.shunwang.basepassport.aspect.EncryptAspect">
		<property name="queryWithCipherColumn" value="${db.queryWithCipherColumn}"/>
		<property name="databaseKeyMapping">
			<map>
				<!--key为数据源的name-->
				<entry key="mysql-base-config" value="${db.aes.password.baseConfig}"/>
			</map>
		</property>
	</bean>

</beans>