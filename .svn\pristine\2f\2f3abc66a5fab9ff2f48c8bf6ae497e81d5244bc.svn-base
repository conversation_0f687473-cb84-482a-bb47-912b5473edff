package com.shunwang.basepassport.detail.pojo;

import java.io.Serializable;
import java.util.Date;

import com.shunwang.baseStone.context.BaseStoneContext;
import com.shunwang.baseStone.core.dao.BaseStoneIbatisDao;
import com.shunwang.baseStone.core.detail.Detail;
import com.shunwang.basepassport.user.pojo.Member;
import com.shunwang.util.date.DateUtil;
import com.shunwang.util.lang.StringUtil;

/**
 * 用户日志
 * <AUTHOR>
 *
 */
public class PersonalEditLog extends Detail{

	private static final long serialVersionUID = 5260240915717512056L;
	
	private Member member;

	private Integer editId;   //

    @SuppressWarnings("unused")
	private Integer memberId;   //

    @SuppressWarnings("unused")
	private String memberName;   //

    private String editItem;   //

    @SuppressWarnings("unused")
	private String valuePre;   //

    @SuppressWarnings("unused")
	private String valueCur;   //

    private Date timeAdd;   //

    private String userAdd;   //
    
    private String timeAddShow;

    private String area;

    private String clientIp;
    
    public Member getMember() {
		return member;
	}

	public void setMember(Member member) {
		this.member = member;
	}
    
    public Integer getEditId() {
		return editId;
	}

	public void setEditId(Integer editId) {
		this.editId = editId;
	}

	public Integer getMemberId() {
		return member.getMemberId();
	}

	public void setMemberId(Integer memberId) {
		this.memberId = memberId;
	}

	public String getMemberName() {
		return member.getMemberName();
	}

	public void setMemberName(String memberName) {
		this.memberName = memberName;
	}

	public String getEditItem() {
		return editItem;
	}

	public void setEditItem(String editItem) {
		this.editItem = editItem;
	}

	public String getValuePre() {
		return this.getPreString();
	}

	public void setValuePre(String valuePre) {
		this.valuePre = valuePre;
	}

	public String getValueCur() {
		return this.getCurString();
	}

	public void setValueCur(String valueCur) {
		this.valueCur = valueCur;
	}

	public Date getTimeAdd() {
		return timeAdd;
	}
    public String getTimeAddStr(){
        if(timeAdd!=null)
            return   DateUtil.dateFormat(DateUtil.getInstance(DateUtil.ymdhms_DATE_FORMAT), timeAdd);
        return StringUtil.EMPTY_STRING;
    }

    public String getClientIpStr() {
        if(StringUtil.isNotBlank(clientIp)) {

            String[] a= clientIp.split("\\.");
            String clintIpStr= a[0]+"."+a[1]+".*.*";
            return clintIpStr ;

        }
        return "未知";
    }

    public String getAreaStr(){
        if(StringUtil.isBlank(area)){
            return "未知";
        }
        return area;
    }


	public void setTimeAdd(Date timeAdd) {
		this.timeAdd = timeAdd;
	}

	public String getUserAdd() {
		return userAdd;
	}

	public void setUserAdd(String userAdd) {
		this.userAdd = userAdd;
	}

	public String getTimeAddShow() {
		return timeAddShow;
	}

	public void setTimeAddShow(String timeAddShow) {
		this.timeAddShow = timeAddShow;
	}

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getClientIp() {
        return clientIp;
    }

    public void setClientIp(String clientIp) {
        this.clientIp = clientIp;
    }

    public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	private String type;
	@Override
	public Serializable getPk() {
		return this.editId;
	}

	@SuppressWarnings("unchecked")
	@Override
	public BaseStoneIbatisDao<Detail> getDao() {
		return (BaseStoneIbatisDao<Detail>) BaseStoneContext.getInstance().getBean("personalEditLogDao");
	}

}
