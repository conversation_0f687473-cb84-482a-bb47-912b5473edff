package com.shunwang.basepassport.manager.service.netbar;

import com.shunwang.basepassport.config.dao.ConfigInterfaceDao;
import com.shunwang.basepassport.config.pojo.ConfigInterface;
import com.shunwang.basepassport.manager.HttpMethod;
import com.shunwang.basepassport.manager.request.netbar.BaseNetbarRequest;
import com.shunwang.basepassport.manager.response.netbar.BaseNetbarResponse;
import com.shunwang.util.net.HttpClientUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Constructor;
import java.nio.charset.StandardCharsets;
import java.util.Map;

public class NetbarServiceClient {

    private static final Logger LOGGER = LoggerFactory.getLogger(NetbarServiceClient.class);

    private static ConfigInterfaceDao configInterfaceDao;

    public void setConfigInterfaceDao(ConfigInterfaceDao configInterfaceDao) {
        NetbarServiceClient.configInterfaceDao = configInterfaceDao;
    }

    public static <T extends BaseNetbarResponse> T execute(BaseNetbarRequest<T> request) {
        try {
            ConfigInterface setting = configInterfaceDao.findByInterfaceKey(request.getInterfaceKey());
            request.doInterfaceSetting(setting);
            String response = null;
            String url = request.buildUrl();
            Map<String, String> requestParams = request.buildCommonParams();
            Map<String, String> headers = request.getHeaders();
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("----------------------------------------");
                LOGGER.debug("url:{}", url);
                LOGGER.debug("method:{}", request.getHttpMethod());
                LOGGER.debug("requestParams:{}", requestParams);
                LOGGER.debug("headers:{}", headers);
                LOGGER.debug("----------------------------------------");
            }

            if (request.getHttpMethod() == HttpMethod.GET) {
                response = HttpClientUtils.doGet(url, requestParams, headers, null, StandardCharsets.UTF_8);
            } else {
                response = HttpClientUtils.doPost(url, requestParams, headers, null, StandardCharsets.UTF_8);
            }

            Class<T> responseClass = request.getResponseClass();
            Constructor<T> constructor = responseClass.getConstructor();
            T resp = constructor.newInstance();
            return (T) resp.parse();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

}
