package com.shunwang.basepassport.manager.enums;


import com.shunwang.baseStone.cache.CacheKeyConstant;

public interface RiskPartnerServiceEnum {

    enum PartnerService {
        SSO_LOGIN("1","100", "9001", CacheKeyConstant.ConfigResourcesConstants.RISK_SSO_LOGIN_IS_OPEN),
        INTERFACE_LOGIN("2", "200", "9002", CacheKeyConstant.ConfigResourcesConstants.RISK_INTERFACE_LOGIN_IS_OPEN),
        INTERFACE_REG("3", "200", "9003", CacheKeyConstant.ConfigResourcesConstants.RISK_INTERFACE_REG_IS_OPEN),
        INTERFACE_MODIFY("4", "200", "9004", CacheKeyConstant.ConfigResourcesConstants.RISK_INTERFACE_MODIFY_IS_OPEN),
        INTERFACE_IMG("5", "200", "9005", CacheKeyConstant.ConfigResourcesConstants.RISK_INTERFACE_IMAGE_IS_OPEN),
        INTERFACE_QUERY_USER_BY_MOBILE("6", "200", "9006", CacheKeyConstant.ConfigResourcesConstants.RISK_INTERFACE_QUERY_USER_BY_MOBILE_IS_OPEN),
        ;
        PartnerService(String type, String partnerId, String serviceId, String configKey) {
            this.type = type;
            this.partnerId = partnerId;
            this.serviceId = serviceId;
            this.configKey = configKey;
        }

        private String type;
        private String partnerId;
        private String serviceId;
        private String configKey;

        public static PartnerService getEnum(String type) {
            for (PartnerService value : PartnerService.values()) {
                if (value.getType().equals(type)) {
                    return value;
                }
            }
            return null;
        }
        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public String getPartnerId() {
            return partnerId;
        }

        public void setPartnerId(String partnerId) {
            this.partnerId = partnerId;
        }

        public String getServiceId() {
            return serviceId;
        }

        public void setServiceId(String serviceId) {
            this.serviceId = serviceId;
        }

        public String getConfigKey() {
            return configKey;
        }

        public void setConfigKey(String configKey) {
            this.configKey = configKey;
        }
    }


}
