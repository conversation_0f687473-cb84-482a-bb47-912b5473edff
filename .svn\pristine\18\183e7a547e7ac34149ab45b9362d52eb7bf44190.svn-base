package com.shunwang.baseStone.cache.keygenerator;

import com.shunwang.baseStone.cache.CacheKeyConstant;
import org.jetbrains.annotations.NotNull;

import java.lang.reflect.Method;

/**
 * resource
 *
 * <AUTHOR>
 * @date 2019/3/12
 **/
public class ServerLoginElementKeyGenerator extends BaseKeyGenerator {

    @NotNull
    @Override
    public Object generate(@NotNull Object target, @NotNull Method method, @NotNull Object... params) {
        return generate(CacheKeyConstant.EhcacheKey.SERVER_LOGIN_ELEMENT, params);
    }
}
