package com.shunwang.baseStone.sso.apapter;

import com.google.gson.Gson;
import com.shunwang.baseStone.cache.CacheKeyConstant;
import com.shunwang.baseStone.context.RedisContext;
import com.shunwang.baseStone.sso.constant.UserOutsiteConstant;
import com.shunwang.baseStone.sso.pojo.QrCodeResponse;
import com.shunwang.baseStone.sso.weixin.oauth.service.WeixinRemoteCall;
import com.shunwang.baseStone.sso.weixin.pojo.*;
import com.shunwang.basepassport.manager.bean.ReportEntity;
import com.shunwang.basepassport.user.common.MemberConstants;
import com.shunwang.basepassport.user.common.MemberUtil;
import com.shunwang.basepassport.user.common.UserLoginSessionUtil;
import com.shunwang.basepassport.user.dao.MemberMultipleAccountBindDao;
import com.shunwang.basepassport.user.dao.MemberOutSiteDao;
import com.shunwang.basepassport.user.pojo.Member;
import com.shunwang.basepassport.user.pojo.MemberAccountBind;
import com.shunwang.basepassport.user.pojo.MemberMultipleAccountBind;
import com.shunwang.basepassport.user.service.DcReportService;
import com.shunwang.basepassport.user.service.MultipleAccountBindService;
import com.shunwang.basepassport.weixin.constant.WeixinConstant;
import com.shunwang.basepassport.weixin.exception.WeixinOauthException;
import com.shunwang.basepassport.weixin.pojo.WeixinOauth;
import com.shunwang.basepassport.weixin.pojo.WeixinOauthToken;
import com.shunwang.util.StringUtil;
import com.shunwang.util.date.DateUtil;
import com.shunwang.util.math.RandomUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.math.BigInteger;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * User:pf.ma
 * Date:2019/12/30
 * Time:14:09
 */
public class WeixinMiniAdapter extends WeixinAdapter{
    private static Logger log = LoggerFactory.getLogger(WeixinMiniAdapter.class);

    private String code;
    private final static Gson GSON = new Gson() ;
    private WeixinRemoteCall weixinRemoteCall;
    private Integer qrCodeExpireSeconds ;//场景值二维码过期时间

    private String unionid;
    private String scene;
    private String avatarUrl;

    private String authFreeType;

    private MultipleAccountBindService multipleAccountBindService;
    private MemberMultipleAccountBindDao memberMultipleAccountBindDao;

    @Override
    public String getInterfaceId() {
        return UserOutsiteConstant.MINI_INTERFACE_ID;
    }

    @Override
    protected String getUserKeyPrefix() {
        WeixinOauth weixinOauth = weixinOauthService.getBySiteId(site_id, WeixinConstant.TYPE.AUTHORIZER_MINI);
        Objects.requireNonNull(weixinOauth);
        return WeixinConstant.QRSCENE_MINI_PRE + weixinOauth.getChannel() + "_";
    }

    @Override
    protected String buildCacheUserKey() {
        return getUserKeyPrefix() + new BigInteger(DateUtil.getCurrentDateStamp().substring(4) + RandomUtil.getRandomStr(4)).toString(36);
    }

    @Override
    public String goToOauth() {
        return null;
    }

    @Override
    public Map<String, Object> getOauth() {
        WeixinOauthToken weixinOauthToken;
        // 创建一个随机场景值，用来查询是否已经关注
        String cacheUserKey;
        String path;
        if (StringUtil.isNotBlank(innerScene)) {
            String appid = RedisContext.getResourceCache().getResourceValue(CacheKeyConstant.ConfigResourcesConstants.NET_BAR_FREE_LOGIN_CONFIG,
                    CacheKeyConstant.ConfigResourcesConstants.DEFAULT_APPID_MINI);
            weixinOauthToken = weixinOauthTokenService.getByAppIdAndType(appid, WeixinConstant.TYPE.AUTHORIZER_MINI);
            path = RedisContext.getResourceCache().getResourceValue(CacheKeyConstant.ConfigResourcesConstants.NET_BAR_FREE_LOGIN_CONFIG,
                    CacheKeyConstant.ConfigResourcesConstants.DEFAULT_APPID_MINI_PATH);
            cacheUserKey = innerScene;
        } else {
            // 接入方信息
            weixinOauthToken = getBySiteIdAndType(site_id, WeixinConstant.TYPE.AUTHORIZER_MINI);
            cacheUserKey = buildCacheUserKey();
            WeixinOauth weixinOauth = weixinOauthService.getBySiteId(site_id, WeixinConstant.TYPE.AUTHORIZER_MINI) ;
            path = weixinOauth.getPagePath();
        }
        //确保token有效
        checkOauthToken(weixinOauthToken);
        try {
            WeixinQrcode weixinQrcode = weixinOpenService.createMiniQrcode(weixinOauthToken.getAccessToken(), cacheUserKey, path);
            cacheExtData(cacheUserKey, qrCodeExpireSeconds);
            return respQrcode(weixinQrcode, cacheUserKey, qrCodeExpireSeconds) ;
        }catch (Exception e){
            log.error("获取场景值二维码异常", e) ;
            throw new WeixinOauthException("获取场景值二维码异常") ;
        }
    }

    /**
     * 用于小程序测试，参数code由小程序获得
     * 需要appId及secret
     * 参数，小程序传入code，返回unionId及openId
     * @return
     */
    public String pullUnionId() {
        log.info("微信登录回调，code:[{}],siteId:[{}]", code, site_id);
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("code", "0");
        resultMap.put("msg", "ok");
        WeixinOauth oauth = weixinOauthService.getBySiteId(site_id, WeixinConstant.TYPE.AUTHORIZER_MINI);
        if (oauth == null) {
            setErrorMsg("微信小程序未配置");
            return respError();
        }
        WeixinCode2Session result = weixinRemoteCall.jscode2Session(oauth.getAppId(), code, oauth.getEmail());
        if (result == null) {
            setErrorMsg("微信小程序授权获取openId失败");
            return respError();
        }
        resultMap.put("unionid", result.getUnionid());
        resultMap.put("openid", result.getOpenid());
        return outMsg(resultMap);
    }
    @Override
    public String oauthCallback() {
        //还原接入方透传的参数
        pullCacheExtData(scene);
        initContextInfo();
//        loadPrivacyCss();
        try {
            site_id = redisOperation.get(WeixinConstant.EXT_SCENE_KEY + scene);
            if (StringUtil.isBlank(site_id)) {
                LOG.error("{}授权登录请求参数site_id过期或不存在,对应场景值[{}]", getOutOauthLogName(), scene);
                return respError();
            }

            Map<String, String> resultMap = new HashMap<>();

            WeixinUser weixinUser = new WeixinUser();
            weixinUser.setHeadimgurl(avatarUrl);
            weixinUser.setUnionid(unionid);
            weixinUser.setNickname(nickName);

            memberOutSite = buildMemberOutSite(weixinUser, UserOutsiteConstant.MINI_INTERFACE_ID, appId);
            if (memberOutSite == null) {
                respError();
            }
            MemberOutSiteDao memberOutSiteDao = memberOutSite.getDao();

            member = memberOutSiteDao.getByOutMemberId(memberOutSite.getOutMemberId());
            // 外部用户不存在
            if (member == null) {
                // sso 第三方微信登录
                member = outSiteMemberRegister(memberOutSite);
            } else {
                updateHeadAndNick(weixinUser);
            }
            //获取请求二维码的原始客户端ip到登录日志
            member.setClientIp(clientIp);
            MemberUtil.doLogin(member, memberOutSite);
            memberName = member.getMemberName();
            UserLoginSessionUtil.saveSession(memberName, UserLoginSessionUtil.LoginType.SSO_OUT.getType() + getInterfaceId(), null, site_id);
            doReport(member, ReportEntity.InterfaceType.login);

            boolean ssoUnAuthFreeLoginSwitch = UserLoginSessionUtil.ssoUnAuthFreeLoginSwitch(site_id);
            if (MemberConstants.AuthFreeType.AUTH.equalsIgnoreCase(authFreeType) && ssoUnAuthFreeLoginSwitch) {
                multipleAccountBindService.bindAccountIfNecessary(idCardName, member, site_id, unionid);
            }
            initLoginElement(super.getSite_id());
            if (isSingleAccount) {
                if (INNER_SCAN.equals(inner)) {
                    return innerScanProcess(weixinUser.getOpenid());
                }
                if (getSingleAccount() == null) {
                    resultMap.put("singleBindSign", createSingleBindSign());
                    return GSON.toJson(resultMap);
                }
                UserLoginSessionUtil.saveMasterSession(memberName, member.getMemberName(), UserLoginSessionUtil.LoginType.SSO_OUT.getType() + getInterfaceId(), null, site_id);
            }
            login(member);
            resultMap.put("ticket", getTicket());
            resultMap.put("clientTicket", getClientTicket());
            resultMap.put("tockenId", getTockenId());
            // 向缓存中添加，通过场景值，缓存1分钟
            redisOperation.set(scene, resultMap, 1, TimeUnit.MINUTES) ;
            Map<String, Object> miniMap = new HashMap<>();
            miniMap.put("code", "0");
            miniMap.put("msg", "ok");
            return outMsg(miniMap);
        } catch (Exception e) {
            log.error("{}登录异常:", getOutOauthLogName(), e);
        }
        return respError();

    }

    /**
     * 用于小程序读取配置用，目前用在扫码后检测是否需要弹窗授权
     */
    public String loadMiniConfig() {
        Map<String, Object> miniMap = new HashMap<>();
        miniMap.put("code", 0);
        miniMap.put("showAgreement", false);
        try {
            pullCacheExtData(scene);
            if (StringUtil.isNotBlank(site_id) && StringUtil.isNotBlank(idCardName) && UserLoginSessionUtil.ssoUnAuthFreeLoginSwitch(site_id)) {
                MemberMultipleAccountBind multipleAccountBindDb = memberMultipleAccountBindDao.getByMemberName(idCardName);
                miniMap.put("showAgreement", multipleAccountBindDb == null);
            }
        } catch (Exception e) {
            log.error("{}登录异常:", getOutOauthLogName(), e);
        }
        return outMsg(miniMap);
    }

    private String respError() {
        Map<String, Object> param = new HashMap<>();
        param.put("code", "-1");
        param.put("msg","fail");
        return outMsg(param);
    }

    private String outMsg(Map<String, Object> result) {
        PrintWriter out = null;
        try {
            out = this.getResponse().getWriter();
            getResponse().setContentType("text/json;charset=UTF-8");
            out.write(GSON.toJson(result));
        } catch (IOException e) {
            log.error("系统异常：",e);
            out.write("{\"errorMsg\":\"" + "系统异常，请稍候再试！" + "\"}");
        } finally {
            out.close();
        }
        return null;
    }

    /**
     * 内部扫码逻辑，目前仅计费上机使用，所以未对业务类型判断，若后续有新业务需要新增判断逻辑
     */
    private String innerScanProcess(String openId) {
        QrCodeResponse result = new QrCodeResponse();
        Map<String, String> extInfo = redisOperation.getMap(CacheKeyConstant.SSO.INNER_SCAN + scene, String.class, String.class);
        Member netBarMember = null;
        if (extInfo == null) {
            result = new QrCodeResponse(QrCodeResponse.TypeStage.ERROR);
            result.setErrorMsg("数据已过期");
        } else {
            netBarMember = member.getDao().getByName(extInfo.get("memberName"));
        }
        if (netBarMember == null) {
            result = new QrCodeResponse(QrCodeResponse.TypeStage.ERROR);
            result.setErrorMsg("会员数据异常");

        }
        if (QrCodeResponse.TypeStage.ERROR.getStage().equals(result.getStage())) {
            redisOperation.set(scene, result, 1, TimeUnit.MINUTES);
            Map<String, Object> miniMap = new HashMap<>();
            miniMap.put("code", "0");
            miniMap.put("msg", "ok");
            return outMsg(miniMap);
        }

        MemberAccountBind temp = memberAccountBindDao.getByIdCard(netBarMember.getMemberId());
        if (temp != null) {
            MemberAccountBind byWeixin = null;
            //身份会员有单帐号，微信存在单帐号关系，或不存在但是微信已设手机为登录帐号等
            if (temp.getWeixin() == null &&
                    (!member.getMobileAsLoginAccount() || temp.getPhone().equals(member.getMobile()))) {
                byWeixin = memberAccountBindDao.getByWeixin(member.getMemberId());
                if (byWeixin == null) {
                    temp.beginBuildLog("WEIXIN帐号绑定");
                    temp.setWeixin(member.getMemberId());
                    singleAccountUpdateExt(temp);
                    DcReportService.freeLoginReport(member.getMemberName(), netBarMember.getMemberName(), DcReportService.OptTypeAndExtInfo.WX_MINI_BIND_WX_SUCCESS, getSite_id());
                }
            }
            if (byWeixin != null || !member.getMemberId().equals(temp.getWeixin())) {
                result = new QrCodeResponse(QrCodeResponse.TypeStage.ERROR);
                result.setErrorMsg("已绑定其他帐号");
                DcReportService.freeLoginReport(member.getMemberName(), netBarMember.getMemberName(), DcReportService.OptTypeAndExtInfo.WX_MINI_BIND_WX_FAIL, getSite_id());
            } else {
                member = memberDao.getByMemberId(temp.getMemberId());
                bindNetBarAuth(extInfo.get("memberName"), netBarMember.getMemberId(), member.getMemberName(), DcReportService.OptTypeAndExtInfo.WX_MINI_BAR_AUTH);
                wxLogin(result, openId);
                UserLoginSessionUtil.saveMasterSession(memberName, member.getMemberName(), UserLoginSessionUtil.LoginType.SSO_OUT.getType() + getInterfaceId(), null, site_id);
                DcReportService.freeLoginReport(member.getMemberName(), netBarMember.getMemberName(), DcReportService.OptTypeAndExtInfo.WX_MINI_LOGIN_SUCCESS, getSite_id());
            }
        } else {
            MemberAccountBind singleAccount = getSingleAccount();
            if (singleAccount == null) {
                result = new QrCodeResponse(QrCodeResponse.TypeStage.ERROR);
                result.setErrorMsg("未绑定手机号");
                DcReportService.freeLoginReport(member.getMemberName(), netBarMember.getMemberName(), DcReportService.OptTypeAndExtInfo.WX_MINI_BIND_MOBILE_FAIL, getSite_id());
            } else {
                result.setType(QrCodeResponse.TypeStage.LOGIN_SUCCESS.getType());
                if (singleAccount.getIdCard() == null || singleAccount.getIdCard() == 0) {
                    singleAccount.setIdCard(netBarMember.getMemberId());
                    singleAccount.setTimeEdit(new Date());
                    memberAccountBindDao.update(singleAccount);
                    DcReportService.freeLoginReport(member.getMemberName(), netBarMember.getMemberName(), DcReportService.OptTypeAndExtInfo.WX_MINI_BIND_ID_CARD_SUCCESS, getSite_id());
                }
                if (!singleAccount.getIdCard().equals(netBarMember.getMemberId())) {
                    result = new QrCodeResponse(QrCodeResponse.TypeStage.ERROR);
                    result.setErrorMsg("已绑定其他会员帐号");
                    DcReportService.freeLoginReport(member.getMemberName(), netBarMember.getMemberName(), DcReportService.OptTypeAndExtInfo.WX_MINI_BIND_ID_CARD_FAIL, getSite_id());
                } else {
                    bindNetBarAuth(extInfo.get("memberName"), netBarMember.getMemberId(), member.getMemberName(), DcReportService.OptTypeAndExtInfo.WX_MINI_BAR_AUTH);
                    wxLogin(result, openId);
                    UserLoginSessionUtil.saveMasterSession(memberName, member.getMemberName(), UserLoginSessionUtil.LoginType.SSO_OUT.getType() + getInterfaceId(), null, site_id);
                    DcReportService.freeLoginReport(member.getMemberName(), netBarMember.getMemberName(), DcReportService.OptTypeAndExtInfo.WX_MINI_LOGIN_SUCCESS, getSite_id());
                }
            }
        }
        redisOperation.set(scene, result, 1, TimeUnit.MINUTES);
        Map<String, Object> miniMap = new HashMap<>();
        miniMap.put("code", "0");
        miniMap.put("msg", "ok");
        return outMsg(miniMap);
    }

    @Override
    protected String getOutOauthLogName() {
        return "微信小程序";
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public WeixinRemoteCall getWeixinRemoteCall() {
        return weixinRemoteCall;
    }

    public void setWeixinRemoteCall(WeixinRemoteCall weixinRemoteCall) {
        this.weixinRemoteCall = weixinRemoteCall;
    }

    public Integer getQrCodeExpireSeconds() {
        return qrCodeExpireSeconds;
    }

    public void setQrCodeExpireSeconds(Integer qrCodeExpireSeconds) {
        this.qrCodeExpireSeconds = qrCodeExpireSeconds;
    }

    public String getUnionid() {
        return unionid;
    }

    public void setUnionid(String unionid) {
        this.unionid = unionid;
    }

    public String getScene() {
        return scene;
    }

    public void setScene(String scene) {
        this.scene = scene;
    }

    public String getAvatarUrl() {
        return avatarUrl;
    }

    public void setAvatarUrl(String avatarUrl) {
        this.avatarUrl = avatarUrl;
    }

    public MemberMultipleAccountBindDao getMemberMultipleAccountBindDao() {
        return memberMultipleAccountBindDao;
    }

    public void setMemberMultipleAccountBindDao(MemberMultipleAccountBindDao memberMultipleAccountBindDao) {
        this.memberMultipleAccountBindDao = memberMultipleAccountBindDao;
    }

    public MultipleAccountBindService getMultipleAccountBindService() {
        return multipleAccountBindService;
    }

    public void setMultipleAccountBindService(MultipleAccountBindService multipleAccountBindService) {
        this.multipleAccountBindService = multipleAccountBindService;
    }

    public String getAuthFreeType() {
        return authFreeType;
    }

    public void setAuthFreeType(String authFreeType) {
        this.authFreeType = authFreeType;
    }
}
