<%@ page contentType="text/html;charset=UTF-8"%>
<%@ include file="/common/taglibs.jsp"%>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta name="Keywords" content="顺网通行证、 修改手机号码 、申诉" />
<meta name="Description" content="实名认证修改手机号码需要先填写基本资料。" />

<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>顺网通行证-修改手机号码-实名认证修改</title>
<script type="text/javascript" src="${staticServer}/scripts/front/changeMobile/changeMobile.js"></script>
</head>
<body>
<%@ include file="/front/changeMobile/global_nav.jsp" %>
<div class="c_body forget_s03">
    <ul class="step_bar">
        <li><em>1</em> 填写账号信息</li>
        <li><em>2</em> 设置新手机</li>
    </ul>
    <div class="forget_send">
        <i class="f_card"></i>
        <c:choose>
            <c:when test="${memberType == 'cafe'}">
                <p class="tip">您已通过实名认证，请上传手持证件照片进行找回</p>
                <p>请上传手持身份证及营业执照副本原件（或证明材料）头部照，必须与实名认证的手持证件照内容一致。</p>
            </c:when>
            <c:otherwise>
                <p class="tip">您已通过实名认证，请上传身份证照片进行找回</p>
                <p>请上传能表明真实性的身份证彩色原件电子版，必须与申请时使用的身份证件一致。</p>
            </c:otherwise>
        </c:choose>
    </div>
    <div class="form_group">
              <form class="mar40" id="changeMobile" action="/front/changeMobile/goActuChangeMobileNext_front.htm?idCardImgUrlActu=" method="post">
                <input  type="hidden"  id="positionOrg1" name="appeal.questionIsBind"  value="${questionIsBind}" />
                <input  type="hidden"  id="positionOrg2" name="appeal.safeCardIsBind"  value="${safeCardIsBind}" />

                <input type="hidden" name="memberName"  value="${memberName }"/> 
                <input type="hidden" name="appeal.userName" id="userName" value="${appeal.userName }"/> 
                <input type="hidden" name="appeal.realName" id="realName" value="${appeal.realName }"/> 
                <input type="hidden" name="appeal.idCardNo" id="userIdCard" value="${appeal.idCardNo}"/> 
                <input type="hidden" name="appeal.email" id="email" value="${appeal.email }"/> 
                <input type="hidden" name="appeal.mobile" id="mobile" value="${appeal.mobile }"/> 

                <%@ include file="/front/find/globalActu_front_a2.jsp" %>
              </form>
              </div>
            </div>

</body>
</html>
