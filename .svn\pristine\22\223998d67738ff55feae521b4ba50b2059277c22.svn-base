package com.shunwang.basepassport.actu.common;

import java.text.ParseException;
import java.text.SimpleDateFormat;

/**
 * 
 * <AUTHOR> 2014年6月4日下午7:21:55
 */
public class IdentityCardUtil {

    private static final String IDENTITY_NO_REGEX = "([\\d]{15})|([\\d]{17}(\\d|[xX]))";
    private static final String DATE_YYYYMMDD = "yyyyMMdd";
    private static char[] VALIDATE_CODE_ARR = {'1', '0', 'X', '9', '8', '7', '6', '5', '4', '3',
            '2'};
    private static int[] POWSER_ARR = {7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2};
    private static int[] AREA_CODE_ARR = new int[100];

    static {
        for (int i = 0; i < 100; i++) {
            AREA_CODE_ARR[i] = 0;
        }
        AREA_CODE_ARR[11] = 1;// 北京
        AREA_CODE_ARR[12] = 1;// 天津
        AREA_CODE_ARR[13] = 1;// 河北
        AREA_CODE_ARR[14] = 1;// 山西
        AREA_CODE_ARR[15] = 1;// 内蒙古
        AREA_CODE_ARR[21] = 1;// 辽宁
        AREA_CODE_ARR[22] = 1;// 吉林
        AREA_CODE_ARR[23] = 1;// 黑龙江
        AREA_CODE_ARR[31] = 1;// 上海
        AREA_CODE_ARR[32] = 1;// 江苏
        AREA_CODE_ARR[33] = 1;// 浙江
        AREA_CODE_ARR[34] = 1;// 安徽
        AREA_CODE_ARR[35] = 1;// 福建
        AREA_CODE_ARR[36] = 1;// 江西
        AREA_CODE_ARR[37] = 1;// 山东
        AREA_CODE_ARR[41] = 1;// 河南
        AREA_CODE_ARR[42] = 1;// 湖北
        AREA_CODE_ARR[43] = 1;// 湖南
        AREA_CODE_ARR[44] = 1;// 广东
        AREA_CODE_ARR[45] = 1;// 广西
        AREA_CODE_ARR[46] = 1;// 海南
        AREA_CODE_ARR[50] = 1;// 重庆
        AREA_CODE_ARR[51] = 1;// 四川
        AREA_CODE_ARR[52] = 1;// 贵州
        AREA_CODE_ARR[53] = 1;// 云南
        AREA_CODE_ARR[54] = 1;// 西藏
        AREA_CODE_ARR[61] = 1;// 陕西
        AREA_CODE_ARR[62] = 1;// 甘肃
        AREA_CODE_ARR[63] = 1;// 青海
        AREA_CODE_ARR[64] = 1;// 宁夏
        AREA_CODE_ARR[65] = 1;// 新疆
        AREA_CODE_ARR[71] = 1;// 台湾
        AREA_CODE_ARR[81] = 1;// 香港
        AREA_CODE_ARR[82] = 1;// 澳门
        AREA_CODE_ARR[91] = 1;// 国外
    }

    /**
     * 功能：身份证的有效验证
     * 
     * @param identityNo
     * @return true/false
     */
    public static boolean validate(String identityNo) {
        if (identityNo == null) {
            return false;
        }
        if (!identityNo.matches(IDENTITY_NO_REGEX)) {
            return false;
        }
        if (AREA_CODE_ARR[Integer.parseInt(identityNo.substring(0, 2))] == 0) {
            return false;
        }

        String date =
                identityNo.length() == 15 ? "19" + identityNo.substring(6, 12) : identityNo
                        .substring(6, 14);

        SimpleDateFormat sdf = new SimpleDateFormat(DATE_YYYYMMDD);
        sdf.setLenient(false);
        try {
            sdf.parse(date);
        } catch (ParseException e) {
            return false;
        }

        if (identityNo.length() == 15) {
            return true;
        }

        int checksum = 0;
        for (int i = 0; i < 17; i++) {
            checksum = checksum + Character.digit(identityNo.charAt(i), 10) * POWSER_ARR[i];
        }
        char verifyCode = VALIDATE_CODE_ARR[checksum % 11];

        if (identityNo.charAt(17) != verifyCode) {
            return verifyCode == 'X' ? identityNo.charAt(17) == 'x' : false;
        }

        return true;
    }

}
