package com.shunwang.baseStone.notice.dao;

import com.google.gson.Gson;
import com.shunwang.baseStone.context.RedisContext;
import com.shunwang.baseStone.core.dao.CachedDao;
import com.shunwang.baseStone.notice.pojo.Notice;
import com.shunwang.util.lang.StringUtil;

import java.io.Serializable;

public class NoticeCacheDao extends CachedDao<Notice>{
	public Notice getByPageType(Serializable id) {

		return  RedisContext.getRedisCache().get(getKey(id),Notice.class);
	}

	public void deleteByPageType(Serializable id){
		RedisContext.getRedisCache().del(getKey(id));
	}
}
