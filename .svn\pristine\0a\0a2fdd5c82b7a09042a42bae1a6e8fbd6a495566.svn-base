﻿<%@ page contentType="text/html;charset=UTF-8"%>
<%@ include file="/common/taglibs.jsp"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="Keywords" content="顺网通行证 ，密码， 找回， 手机，邮箱，其他方式， 重置" />
    <meta name="Description" content="提供手机，邮箱，其他找回密码的方式。" />
    <title>顺网通行证-找回账号-找回账号</title>

    <script type="text/javascript" src="${staticServer}/scripts/common/jquery.js"></script>
    <script type="text/javascript" src="${staticServer}/scripts/front/common/commonCheck.js"></script>
    <script type="text/javascript" src="${staticServer}/scripts/common/safeenter.js"></script>
    <script type="text/javascript" src="${staticServer}/scripts/front/member/min/validation.min.js"></script>
    <script type="text/javascript" src="${staticServer}/scripts/front/find/findBack_account.js"></script>

</head>
<body>
<div class="c_head">
    <i class="forget_icon"></i>
    <span class="title">找回账号</span>
</div>
<div class="c_body forget_s01">
    <div class="form_group">
        <form class="passform mar60 mart50 f14px" action="/front/noLogin/selectBackAccNum.htm" id="formBackAcc" method="post">
            <table cellpadding="0" cellspacing="0">
                <tbody>
                <tr>
                    <th class="w_lg"></th>
                    <td>您可以根据绑定的手机或邮箱找回所属的所有账号</td>
                </tr>
                <tr>
                    <th><span style="color:red;">*</span>手机/邮箱：</th>
                    <td>
                        <input id="mobileOrEmail" name="mobileOrEmail" value="${mobileOrEmail}"  type="text" class="form_input" tabindex="1" maxlength="20" style="width:220px;"/>
                        <span class="form_error" id="mobileOrEmailMsg">${fieldErrors.mobileOrEmailMsg[0]}</span>
                    </td>
                </tr>
                <tr>
                    <th>验证码：</th>
                    <td>
                        <input id="checkCode" name="checkCode" type="text" class="form_input" tabindex="2" value="" maxlength="4" style="width:80px" />
                        <img id="checkCodeImg" src="" alt=""  />
                        <a href="###" id="changeCheckCodeLink">看不清？换一张</a>
                        <span class="form_error" id="checkCodeMsg">${fieldErrors.checkCode[0]}</span>
                    </td>
                </tr>
                <tr>
                    <th></th>
                    <td>
                        <a href="###" id="queryMissAccoutNum" class="btn_default_lg" tabindex="3">查询</a>
                        <br/>
                    </td>

                </tr>

                </tbody>
            </table>
        </form>
    </div>
</div>

</body>
<script type="text/javascript" charset="utf-8">
    $(function(){
        if (!('placeholder' in document.createElement('input'))) {
            $('.placeholder input[placeholder], .placeholder textarea[placeholder]').each(function(k, v) {
                var $obj = $(v),
                        val = $obj.val(),
                        placeholder = $obj.attr('placeholder');

                if (val == '') {
                    $obj.val(placeholder);
                }

                $obj.focus(function() {
                    if ($obj.val() === placeholder) {
                        $obj.val('');
                    }
                }).blur(function() {
                            val = $obj.val();
                            if (val == '' || val == placeholder) {
                                $obj.val(placeholder);
                            }
                        });
            });
        }
    });
</script>
</html>
