<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd" >
<sqlMap namespace="com.shunwang.baseStone.category.pojo.BusinessCategory">
<resultMap class="com.shunwang.baseStone.category.pojo.BusinessCategory" id="BaseResultMap">
	<result property="categoryid" column="categoryid" jdbcType="Integer"/>
	<result property="categoryname" column="categoryname" jdbcType="varchar"/>
	<result property="timeadd" column="timeadd" jdbcType="datetime"/>
	<result property="timeedit" column="timeedit" jdbcType="datetime"/>
	<result property="useradd" column="useradd" jdbcType="varchar"/>
	<result property="useredit" column="useredit" jdbcType="varchar"/>
	<result property="remark" column="remark" jdbcType="varchar"/>
</resultMap>

<resultMap class="com.shunwang.baseStone.category.pojo.BusinessCategory" id="SubBaseResultMap" extends="BaseResultMap">
    <result property="count" column="count" jdbcType="Integer"/>
</resultMap>

<select id="find" resultMap="SubBaseResultMap" parameterClass="com.shunwang.framework.ibatis.query.ConditionQuery">
	SELECT
		t.categoryid,
		t.categoryname,
		t.timeadd,
		t.timeedit,
		t.useradd,
		t.useredit,
		t.remark,
		b.count
	from config_businessCategory t left join   
    (select businessCategoryId, COUNT(1) count from config_bussiness group by businessCategoryId) b
    on categoryid = b.businessCategoryId
	<isParameterPresent >
	<include refid="Example_Where_Clause" />
	</isParameterPresent>
	order by
	<isNotNull property="orderCol" >
		$orderCol$
	</isNotNull>
	<isNull property="orderCol" >
		t.categoryid desc 
	</isNull>
	<isNotEqual property="rp" compareValue="0" >
	    limit #firstResult#, #rp#
	</isNotEqual>
</select>

<select id="findAll" resultMap="BaseResultMap"  >	
	select 
	t.categoryid,
	t.categoryname,
	t.timeadd,
	t.timeedit,
	t.useradd,
	t.useredit,
	t.remark	from config_businessCategory t
</select>


<insert id="insert" parameterClass="com.shunwang.baseStone.category.pojo.BusinessCategory" >
	insert into config_businessCategory (
		categoryid,
		categoryname,
		timeadd,
		timeedit,
		useradd,
		useredit,
		remark	)values(
		#categoryid:int#,
		#categoryname:varchar#,
		#timeadd:datetime#,
		#timeedit:datetime#,
		#useradd:varchar#,
		#useredit:varchar#,
		#remark:varchar#	)
</insert>
<update id="update" parameterClass="com.shunwang.baseStone.category.pojo.BusinessCategory" >
	update config_businessCategory set
		categoryid=#categoryid:int#,
		categoryname=#categoryname:varchar#,
		timeadd=#timeadd:datetime#,
		timeedit=#timeedit:datetime#,
		useradd=#useradd:varchar#,
		useredit=#useredit:varchar#,
		remark=#remark:varchar# where categoryid = #categoryid:int#
</update>
<delete id="delete" parameterClass="com.shunwang.baseStone.category.pojo.BusinessCategory" >
	delete from config_businessCategory where categoryid=#categoryid:int#
</delete>
<select id="get" resultMap="BaseResultMap" parameterClass="String">
	select
	categoryid,
	categoryname,
	timeadd,
	timeedit,
	useradd,
	useredit,
	remark	from config_businessCategory
	where categoryid = #value#
</select>
<select id="findCnt" parameterClass="com.shunwang.framework.ibatis.query.ConditionQuery" resultClass="java.lang.Integer" >
	select count(*) from config_businessCategory t
	<include refid="Example_Where_Clause" />
</select>
</sqlMap>