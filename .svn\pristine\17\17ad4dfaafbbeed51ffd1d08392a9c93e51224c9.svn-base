package com.shunwang.baseStone.context;

import com.shunwang.basepassport.config.dao.ConfigResourcesDao;
import org.springframework.cache.annotation.Cacheable;

/**
 * resource操作
 *
 * <AUTHOR>
 * @since 2014-11-03
 */
public class RedisResourceOperation {

	private ConfigResourcesDao configResourcesDao;

	@Cacheable(value = "cache", keyGenerator = "serverResourceKeyGenerator", unless = "#result == null or #result.length() == 0 ")
	public String getResourceValue(String type, String name) {
	    return configResourcesDao.getResourceValue(type, name);
	}

	public void setConfigResourcesDao(ConfigResourcesDao configResourcesDao) {
		this.configResourcesDao = configResourcesDao;
	}

}
