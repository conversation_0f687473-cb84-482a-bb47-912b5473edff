package com.shunwang.basepassport.asynctask;

import com.shunwang.baseStone.cache.CacheKeyConstant;
import com.shunwang.baseStone.config.constants.MemberEnum;
import com.shunwang.baseStone.context.RedisContext;
import com.shunwang.basepassport.jms.NoticeMqMessageUtil;
import com.shunwang.basepassport.config.pojo.SiteInterface;
import com.shunwang.basepassport.user.dao.ServiceNotifyDao;
import com.shunwang.basepassport.user.pojo.Member;
import com.shunwang.basepassport.user.pojo.ServiceNotify;
import com.shunwang.baseStone.siteinterface.context.SiteContext;
import com.shunwang.basepassport.user.common.MemberConstants;
import com.shunwang.util.json.GsonUtil;
import com.shunwang.util.lang.StringUtil;
import com.shunwang.util.trace.TraceUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * ****************************
 * 版权所有：顺网科技 保留所有权利
 * 创建日期: 2020-02-20
 * 创建作者：zlj
 * 版本： 1.0
 * 功能：用户注销通知Job
 * 最后修改时间：
 * 修改记录：
 * *****************************                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  *********
 */
public class MemberCancelNoticeJob implements Runnable {

    private static final Logger LOG = LoggerFactory.getLogger(MemberCancelNoticeJob.class);

    private final static String NOTIFY_GROUP = "cancel";
    private ServiceNotifyDao dao;
    private final Member member;
    private final Integer type;

    public MemberCancelNoticeJob(ServiceNotifyDao dao, Member member, Integer type) {
        this.member = member;
        this.type = type;
        this.dao = dao;
    }


    public void run() {
        TraceUtil.spanStart();
        List<Map<String,String>> list = getNoticeUrls();
        LOG.info("用户[{}]注销通知开始", member.getMemberName());
        //先作废之前的消息
        ServiceNotify temp = new ServiceNotify();
        temp.setMemberId(member.getMemberId());
        temp.setNotifyGroup(NOTIFY_GROUP);
        dao.invalidExNotify(temp);
        for (Map<String,String> config : list) {
            try {
                doNotice(config);
            } catch (Exception e) {
                LOG.error("通知入库时异常", e);
            }
        }
        LOG.info("用户[{}]注销通知完成", member.getMemberName());
        TraceUtil.spanEnd();
    }

    private void doNotice(Map<String,String> config){
        SiteContext.setSiteId(config.get("siteId"));
        SiteContext.setSiteName(MemberConstants.MEMBER_CANCEL);
        SiteInterface siteInterface = SiteContext.getSiteInterface();
        if (siteInterface == null) {
            LOG.error("siteID[{}]未开通用户注销接口权限，不回调", config.get("siteId"));
            return;
        }
        String url = config.get("url");
        String siteId = config.get("siteId");
        ServiceNotify notify = new ServiceNotify();
        notify.setNotifyUrl(url);
        notify.setMemberId(member.getMemberId());
        notify.setMemberName(member.getMemberName());
        notify.setSiteId(siteId);
        notify.setNotifyGroup(NOTIFY_GROUP);
        notify.setSiteName(MemberConstants.MEMBER_CANCEL);

        Map<String, String> params = new HashMap<>();
        params.put("siteId", siteId);
        params.put("service", getServiceByType(type));
        params.put("memberId", String.valueOf(member.getMemberId()));
        params.put("memberName", member.getMemberName());
        notify.setJsonData(GsonUtil.toJson(params));
        notify = dao.save(notify);
        NoticeMqMessageUtil.doMqNotice(notify.getId());
    }

    private List<Map<String,String>> getNoticeUrls() {
        List<Map<String,String>> list = new ArrayList<>();
        String urlsStr = RedisContext.getResourceCache().getResourceValue(
                CacheKeyConstant.ConfigResourcesConstants.COMMON_CONFIG, getConfigKeyByType(type));
        if (StringUtil.isBlank(urlsStr)) {
            return list;
        }
        String[] cofigs = urlsStr.split("\\|");
        for (String config : cofigs) {
            String[] s = config.split(",");
            if (s.length != 2) {
                continue;
            }
            Map<String, String> map = new HashMap<>();
            map.put("siteId", s[0].trim());
            map.put("url", s[1].trim());
            list.add(map);
        }
        return list;
    }

    private String getConfigKeyByType(Integer type) {
        if (MemberEnum.CancelNotifyType.TYPE_COMMIT_CANCEL.getType() == type) {
            return CacheKeyConstant.ConfigResourcesConstants.MEMBER_CANCEL_NOTICE_URLS;
        }
        if (MemberEnum.CancelNotifyType.TYPE_REVOKE_CANCEL.getType() == type) {
            return CacheKeyConstant.ConfigResourcesConstants.MEMBER_REVOKE_CANCEL_NOTICE_URLS;
        }
        if (MemberEnum.CancelNotifyType.TYPE_REAL_CANCEL.getType() == type) {
            return CacheKeyConstant.ConfigResourcesConstants.MEMBER_REAL_CANCEL_NOTICE_URLS;
        }
        throw new IllegalArgumentException("通知类型");
    }

    private String getServiceByType(Integer type) {
        if (MemberEnum.CancelNotifyType.TYPE_COMMIT_CANCEL.getType() == type) {
            return MemberEnum.CancelNotifyService.SERVICE_COMMIT_CANCEL.getName();
        }
        if (MemberEnum.CancelNotifyType.TYPE_REVOKE_CANCEL.getType() == type) {
            return MemberEnum.CancelNotifyService.SERVICE_REVOKE_CANCEL.getName();
        }
        if (MemberEnum.CancelNotifyType.TYPE_REAL_CANCEL.getType() == type) {
            return MemberEnum.CancelNotifyService.SERVICE_REAL_CANCEL.getName();
        }
        throw new IllegalArgumentException("通知类型");
    }
}

