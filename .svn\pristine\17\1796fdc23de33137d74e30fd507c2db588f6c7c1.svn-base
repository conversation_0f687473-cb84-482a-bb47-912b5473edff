var $tips = $(".tips-error"),
	$postCode = $("#postCode"),
	$form = $('.form-box');

var MSG={
		C1001:'邮编不能为空',
		C1002:'必须为6位数字'
	}

function showTips(msg) {
	$tips.find("p").html(msg) ;
	$tips.show() ;
}

$form.on('submit',function(){
	var cv = $.trim($postCode.val());
	if(!/[\d]{6}/.test(cv)){
		showTips(MSG.C1002) ;
		return false ;
	}
	return true ;
});

function init(){
	var msg =  $('.tips-error p').text();
	if(msg) {
		$('.tips-error').show();
	}
	
	if($tips.find("p").html()=="") {
		$tips.hide();
	}
}

init();