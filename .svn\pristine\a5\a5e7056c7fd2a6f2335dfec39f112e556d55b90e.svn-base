package com.shunwang.basepassport.user.service;

import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.shunwang.baseStone.cache.CacheKeyConstant;
import com.shunwang.baseStone.context.IPContext;
import com.shunwang.baseStone.context.RedisContext;
import com.shunwang.basepassport.manager.request.report.WxWorkReportRequest;
import com.shunwang.basepassport.manager.service.report.DcReportServiceClient;
import com.shunwang.basepassport.util.UserAgentUtil;
import com.shunwang.basepassport.weixin.constant.WeixinConstant;
import com.shunwang.toolbox.tracing.TraceUtil;
import com.shunwang.util.date.DateUtil;
import com.shunwang.util.json.GsonUtil;
import com.shunwang.util.lang.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.concurrent.Executor;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

public class DcReportForWxWorkService {
    private static final Logger logger = LoggerFactory.getLogger(DcReportForWxWorkService.class);

    private static final Executor executor = new ThreadPoolExecutor(5, 20,
            60L, TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(1000),
            new ThreadPoolExecutor.DiscardOldestPolicy());

    public static void wxWorkReport(String scene, OptTypeAndExtInfo optTypeAndExtInfo) {
        wxWorkReport(scene, optTypeAndExtInfo, null);
    }
    /**
     * 上报数据
     * @param scene 场景值
     * @param optTypeAndExtInfo 上报类型
     */
    public static void wxWorkReport(String scene, OptTypeAndExtInfo optTypeAndExtInfo, String memberName) {
        boolean reportSwitch = RedisContext.getSwitchConfig(CacheKeyConstant.ConfigResourcesConstants.BAR_LOGIN_CONFIG,
                CacheKeyConstant.ConfigResourcesConstants.WX_WORK_REPORT_SWITCH, false);
        if (!reportSwitch) {
            logger.debug("企微上报开关关闭，不上报");
            return;
        }
        String ip = IPContext.getIp();
        final String terminal = UserAgentUtil.getTerminal();
        executor.execute(() -> runWxWorkReport(scene, optTypeAndExtInfo, ip, memberName, terminal));
    }

    public static void runWxWorkReport(String scene, OptTypeAndExtInfo optTypeAndExtInfo, String ip, String memberName, String terminal) {
        try {
            TraceUtil.nestedSpanStart();
            if (StringUtil.isBlank(scene)) {
                logger.warn("数据缺失，不上报");
                return;
            }
            Map<String, String> extMap = RedisContext.getRedisCache().getMap(WeixinConstant.REQS_EXT_DATA_KEY + scene, String.class, String.class);
            if (extMap == null) {
                logger.warn("ext数据缺失，不上报");
                return;
            }
            String extData = extMap.get("extData");
            if (StringUtil.isBlank(extData)) {
                logger.info("无extData数据，不上报");
                return;
            }
            JsonObject jsonObject = JsonParser.parseString(extData).getAsJsonObject();
            Map<String, String> reportMap = new LinkedHashMap<>();
            reportMap.put("optType", optTypeAndExtInfo.optType);
            reportMap.put("baid", GsonUtil.getStringFromJsonObject(jsonObject, "barid"));
            reportMap.put("guid", GsonUtil.getStringFromJsonObject(jsonObject, "guid"));
            reportMap.put("areaId", GsonUtil.getStringFromJsonObject(jsonObject, "areaId"));
            reportMap.put("report_time", DateUtil.getCurrentDateStamp());
            if (StringUtil.isNotBlank(memberName)) {
                reportMap.put("memberName", memberName);
            }
            reportMap.put("extInfo", optTypeAndExtInfo.extInfo);
            reportMap.put("token", scene);
            reportMap.put("ip", ip);
            reportMap.put("terminal", terminal);
            WxWorkReportRequest request = new WxWorkReportRequest();
            request.setParam(reportMap);
            DcReportServiceClient.execute(request);
        } catch (Exception e) {
            logger.error("企微数据上报异常[{}]", e.getMessage());
        } finally {
            TraceUtil.nestedSpanEnd();
        }
    }

    public enum OptTypeAndExtInfo {
        GET_WX_WORK_QRCODE("20", "1", "获取企微码"),
        SCAN_WX_WORK_QRCODE("20", "2", "扫企微码"),
        ROUT_TO_XING_YUN("20", "3", "路由星云"),
        GO_TO_WX_AUTH("20", "4", "微信授权页"),
        CALLBACK_ROUT_TO_BIND_ID_CARD("20", "5", "授权成功-绑身份证"),
        CALLBACK_ROUT_TO_RESULT("20", "6",  "授权成功-结果页"),
        BIND_ID_CARD_SUCCESS("20", "7",  "绑身份证成功"),
        SHOW_RESULT("20", "8",  "开机成功"),
        ;

        OptTypeAndExtInfo(String optType, String extInfo, String des) {
            this.extInfo = extInfo;
            this.optType = optType;
            this.des = des;
        }

        private String optType;
        private String extInfo;
        private String des;

        public String getOptType() {
            return optType;
        }

        public String getExtInfo() {
            return extInfo;
        }

        public String getDes() {
            return des;
        }

    }
}
