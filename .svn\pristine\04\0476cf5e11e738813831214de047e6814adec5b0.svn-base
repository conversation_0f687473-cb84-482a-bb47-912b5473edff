package com.shunwang.basepassport.mobile.web;

import com.shunwang.baseStone.bussiness.dao.BussinessDao;
import com.shunwang.baseStone.bussiness.pojo.Bussiness;
import com.shunwang.baseStone.category.dao.CategoryDao;
import com.shunwang.baseStone.category.pojo.Category;
import com.shunwang.baseStone.context.RedisContext;
import com.shunwang.baseStone.core.response.BaseStoneResponse;
import com.shunwang.basepassport.commonExp.ParamFormatErrorExp;
import com.shunwang.basepassport.commonExp.ParamNotFoundExp;
import com.shunwang.basepassport.config.dao.ConfigResourcesDao;
import com.shunwang.basepassport.config.dao.SmsConfigDao;
import com.shunwang.basepassport.config.pojo.ConfigResources;
import com.shunwang.basepassport.config.pojo.SmsConfig;
import com.shunwang.basepassport.interc.InterInterfaceService;
import com.shunwang.basepassport.key.common.SmsKeyUtil;
import com.shunwang.basepassport.mobile.constants.MobileCheckCodeConstants;
import com.shunwang.basepassport.mobile.dao.MobileCheckCodeDao;
import com.shunwang.basepassport.mobile.pojo.MobileCheckCode;
import com.shunwang.basepassport.user.common.MemberConstants;
import com.shunwang.basepassport.user.exception.UserLoginException;
import com.shunwang.basepassport.user.web.MemberAction;
import com.shunwang.sms.utils.SMSSenderUtil;
import com.shunwang.util.date.DateUtil;
import com.shunwang.util.lang.StringUtil;
import com.shunwang.util.math.RandomUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;
import java.util.concurrent.TimeUnit;

import static com.shunwang.basepassport.mobile.constants.MobileCheckCodeConstants.SMS_SEND_PLAIN;

/**
 * 纯发送短信接口
 **/
public class MobileSendAction extends MemberAction {

    private static final Logger logger= LoggerFactory.getLogger(MobileSendAction.class);

    private SmsConfigDao smsConfigDao;
    private CategoryDao categoryDao;
    private BussinessDao bussinessDao;

    private String mobile;
    private String type;
    private String businessType;
    /**
     * 支持占位参数 memberName,activeNo, time
     * 短信内容，格式如xxx$memberName$xxx$activeNo$xxxx$time$xxx位置可以掉换，占位内容可0到多个
     */
    private String content;

    private MobileCheckCodeDao mobileCheckCodeDao;
    private InterInterfaceService interfaceService;

    @Override
    public void checkParam() {
        if (StringUtil.isEmpty(mobile)) {
            throw new ParamNotFoundExp("mobile");
        }
        if (StringUtil.isNotBlank(type) &&
                !MobileCheckCodeConstants.NEED_USER_TYPE.equalsIgnoreCase(type) &&
                !MobileCheckCodeConstants.NO_NEED_USER_TYPE.equalsIgnoreCase(type)) {
            throw new ParamFormatErrorExp("type");
        }
    }

    @Override
    public String getMemberName() {
        return null;
    }

    @Override
    public void process() throws Exception {
        //未传businessType的统一处理为SMS_SEND_PLAIN
        businessType = StringUtil.isBlank(businessType) ? SMS_SEND_PLAIN : businessType;
        if (StringUtil.isBlank(type) || MobileCheckCodeConstants.NEED_USER_TYPE.equalsIgnoreCase(type)) {
            super.process();
        }
        if (MobileCheckCodeConstants.NO_NEED_USER_TYPE.equalsIgnoreCase(type)) {
            doProcess();
        }

    }

    @Override
    public void doProcess() {
        SmsConfig smsConfig = smsConfigDao.findBySiteIdAndBusinessType(getSiteId(), businessType);
        MobileCheckCode temp = getMobileCheckCode();
        MobileCheckCode mobileCheckCode = mobileCheckCodeDao.getLast(temp);
        if (null != mobileCheckCode) {
            long sendTime = DateUtil.compare(new Date(), mobileCheckCode.getSendTime(), DateUtil.ONE_MINUTE);
            if (StringUtil.isNotBlank(mobileCheckCode.getCheckCode()) && sendTime < MobileCheckCodeConstants.SEND_AGAIN_INTERVAL_DEFAULT) {
                logger.warn(mobile + "一分钟内只能获取一次验证码");
                throw new UserLoginException(UserLoginException.DYNAMIC_PWD_ONLY_ONE, "一分钟内只能获取一次验证码");
            }
            int cnt = mobileCheckCodeDao.getCntByDay(temp);
            int dayLimit = smsConfig == null ? getDayLimit() : smsConfig.getLimitOfDay();

            if (cnt >= dayLimit) {
                logger.warn("[{}]一天内只能获取[{}]次验证码", mobile, dayLimit);
                throw new UserLoginException(UserLoginException.DYNAMIC_PWD_ONLY_ONE, "一天内只能获取" + dayLimit + "次验证码");
            }
        }

        send(temp, smsConfig);

        BaseStoneResponse response = new BaseStoneResponse();
        response.setMsgId("0");
        response.setMsg("操作成功");
        setBaseResponse(response);
    }

    @Override
    public String getSiteName() {
        return MemberConstants.SMS_SEND;
    }

    protected MobileCheckCode getMobileCheckCode() {
        MobileCheckCode paramMobileCheckCode = new MobileCheckCode();
        paramMobileCheckCode.setMemberName(member == null ? null : member.getMemberName());
        paramMobileCheckCode.setMemberId(member == null ? null : member.getMemberId());
        paramMobileCheckCode.setFromSiteId(this.getSiteId());
        paramMobileCheckCode.setMobile(mobile);
        paramMobileCheckCode.setType(businessType);
        return paramMobileCheckCode;
    }


    private void send(MobileCheckCode mobileCheckCode, SmsConfig smsConfig) {
        String pwd = RandomUtil.getRandomStr(6, true);
        if (StringUtil.isBlank(content) && smsConfig != null
                && StringUtil.isNotBlank(smsConfig.getContent())) {
            content = smsConfig.getContent();
        }
        String content = getSmsContent(pwd);
        String validTime = smsConfig == null ? getValidTime()
                : smsConfig.getValidTime().toString();
        content = content.replace("$time$", validTime);

        try {
            //1、存缓存
            mobileCheckCode.setCheckCode(pwd);
            mobileCheckCode.setSendTime(new Date());

            String key = SmsKeyUtil.buildSendPlainKey(
                    MobileCheckCodeConstants.NO_NEED_USER_TYPE.equalsIgnoreCase(type) ? mobile : member.getMemberName(),
                    StringUtil.isBlank(businessType) ? SMS_SEND_PLAIN : businessType);
            SmsKeyUtil.putDynamicPwd(key, mobileCheckCode, Long.parseLong(validTime), TimeUnit.MINUTES);

            Bussiness bussiness = bussinessDao.findBybussinesskey(this.getSiteId());
            if (bussiness == null) {
                throw new RuntimeException("根据"+this.getSiteId()+"未查询到对应的类别信息!");
            }
            Category category = categoryDao.getById(bussiness.getCategoryid());
            if (category == null) {
                throw new RuntimeException("根据"+bussiness.getCategoryid()+"未查询到对应的部门信息!");
            }
            if (StringUtil.isBlank(category.getSmsAppid()) || StringUtil.isBlank(category.getSmsSecret())) {
                throw new RuntimeException("根据"+bussiness.getCategoryid()+"查询到对应的部门SMS配置信息!");
            }
            //2、发短信
            SMSSenderUtil.sendMsgWithSiteid(category.getSmsAppid(), category.getSmsSecret(), mobile, content);
            //3、入库
//            getMobileCheckCodeDao().save(mobileCheckCode);
        } catch (Exception e) {
            logger.error("短信发送异常：", e);
            throw new UserLoginException(UserLoginException.DYNAMIC_PWD_SMS_ERROR, "发送失败");
        }
        try {
            if (mobileCheckCode != null) {
                //3、入库
                interfaceService.updateMobileCheckCode(mobileCheckCode);
            }
        } catch (Exception e) {
            logger.error("调用接口保存mobileCheckCode失败", e);
        }
    }


    public String getSmsContent(String pwd) {
        if (StringUtil.isNotBlank(content)) {
            if (member != null) {
                content = content.replace("$memberName$", member.getMemberName());
            }
            return content.replace("$activeNo$", pwd);
        }
        ConfigResources configResources = new ConfigResources();
        ConfigResourcesDao resourcesDao = configResources.getConfigResourcesDao();
        configResources.setType(MobileCheckCodeConstants.SMS_CONFIG);
        configResources.setName(MobileCheckCodeConstants.SMS_CONTENT);
        ConfigResources contentResources = resourcesDao.findResourcesByTypeAndName(configResources);

        String configContent = "";
        if (null != contentResources && !StringUtil.isEmpty(contentResources.getValue())) {
            configContent = contentResources.getValue().replace("$activeNo$", pwd);
        }

        return configContent;
    }

    public String getValidTime() {
        String validTime = RedisContext.getResourceCache()
                .getResourceValue(MobileCheckCodeConstants.SMS_CONFIG, MobileCheckCodeConstants.VALID_TIME);

        if (StringUtil.isNotEmpty(validTime) && StringUtil.isNumer(validTime)) {
            return validTime;
        }

        return MobileCheckCodeConstants.VALID_TIME_DEFAULT;
    }

    public int getDayLimit() {
        String validTime = RedisContext.getResourceCache()
                .getResourceValue(MobileCheckCodeConstants.SMS_CONFIG, MobileCheckCodeConstants.SMS_SEND_PLAIN_DAY_LIMIT);

        if (StringUtil.isNotEmpty(validTime) && StringUtil.isNumer(validTime)) {
            return Integer.parseInt(validTime);
        }

        return MobileCheckCodeConstants.SMS_SEND_PLAIN_DAY_LIMIT_DEFAULT;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public MobileCheckCodeDao getMobileCheckCodeDao() {
        return mobileCheckCodeDao;
    }

    public void setMobileCheckCodeDao(MobileCheckCodeDao mobileCheckCodeDao) {
        this.mobileCheckCodeDao = mobileCheckCodeDao;
    }

    public InterInterfaceService getInterfaceService() {
        return interfaceService;
    }

    public void setInterfaceService(InterInterfaceService interfaceService) {
        this.interfaceService = interfaceService;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public SmsConfigDao getSmsConfigDao() {
        return smsConfigDao;
    }

    public void setSmsConfigDao(SmsConfigDao smsConfigDao) {
        this.smsConfigDao = smsConfigDao;
    }

    public CategoryDao getCategoryDao() {
        return categoryDao;
    }

    public void setCategoryDao(CategoryDao categoryDao) {
        this.categoryDao = categoryDao;
    }

    public BussinessDao getBussinessDao() {
        return bussinessDao;
    }

    public void setBussinessDao(BussinessDao bussinessDao) {
        this.bussinessDao = bussinessDao;
    }
}
