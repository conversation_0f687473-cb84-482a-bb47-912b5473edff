package com.shunwang.basepassport.config;

import com.shunwang.baseStone.core.action.BaseStoneAction;
import com.shunwang.basepassport.commonExp.ParamNotFoundExp;
import com.shunwang.basepassport.config.common.RichTextUtil;
import com.shunwang.basepassport.config.pojo.RichText;
import com.shunwang.basepassport.config.response.AgreementResponse;

/**
 * 查询协议
 */
public class AgreementQueryAction extends BaseStoneAction {
    private Integer agreementId;

    @Override
    public void process() throws Exception {
        RichText richText = RichTextUtil.loadRichText(agreementId);
        this.setBaseResponse(new AgreementResponse(richText));
    }

    @Override
    public void checkParam() {
        if (agreementId == null) {
            throw new ParamNotFoundExp("协议id(agreementId)");
        }
    }

    @Override
    public String getSiteName() {
        return "协议查询";
    }

    public Integer getAgreementId() {
        return agreementId;
    }

    public void setAgreementId(Integer agreementId) {
        this.agreementId = agreementId;
    }
}
