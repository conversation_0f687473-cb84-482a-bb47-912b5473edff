package com.shunwang.baseStone.cache.keygenerator;

import com.shunwang.baseStone.cache.CacheKeyConstant;
import com.shunwang.util.lang.StringUtil;
import org.apache.logging.log4j.util.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.interceptor.KeyGenerator;

import java.util.Arrays;

/**
 * ehcache相关缓存Key
 **/
public abstract class BaseKeyGenerator implements KeyGenerator {

    private final Logger log = LoggerFactory.getLogger(this.getClass());

    public Object generate(CacheKeyConstant.EhcacheKey ehcacheKey, Object... params) {
        if (params.length != ehcacheKey.getParamLength()) {
            String paramsInfo = Arrays.toString(params);
            log.error("错误调用:{}", paramsInfo);
            throw new IllegalArgumentException("错误调用");
        }
        StringBuilder sb = new StringBuilder(ehcacheKey.getKey());
        for (int i = 0; i < params.length; i++) {
            sb.append(trimAndToLowerCase(params[i].toString()))
                    .append(i > 0 ? CacheKeyConstant.CACHE_SPLIT : Strings.EMPTY);
        }
        String key = sb.toString();
        if (log.isDebugEnabled()) {
            log.debug("生成key:{}\t param={}", key, Arrays.toString(params));
        }
        return key;
    }

    public static String trimAndToLowerCase(String key) {
        if (StringUtil.isBlank(key)) {
            return "";
        }
        return key.trim().toLowerCase();
    }
}
