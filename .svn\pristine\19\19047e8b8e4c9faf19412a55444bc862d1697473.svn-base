<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="/common/taglibs.jsp" %>
<script type="text/javascript" src="${staticServer }/scripts/common/md5.js"></script>
<script type="text/javascript" src="${staticServer }/scripts/common/jquery.js"></script>

    <div class="shop_head">
        <strong>密保问题</strong>
        设置后可以通过密保问题找回密码。
    </div>
    <div class="form_group">
<form id="questionBindForm20110902141116" name='questionBindForm20110902141116'  method="post"
      action="${appServer}/front/securityCenter/questionBind.htm">

    <table cellpadding="0" cellspacing="0">
        <thead>
        <tr>
            <th class="w_lg"></th>
            <td><div id="errorMessagesShowSpan" class="form_error"><c:if test="${!empty errorMsg}">
                ${errorMsg}
            </c:if></div><span class="form_tip_lg" style="margin: 8px 0 5px;">请选择三个问题并填入答案，并请牢记该问题的答案，方便今后使用。</span></td>
        </tr>
        </thead>
        <tbody>
        <c:forEach var="question" begin="0" end="2" step="1" varStatus="status">

            <tr>
                <th> <label style="margin-right:0px;" id="questionName${status.index}">问题：</label></th>
                <td>
                    <select name="questionKeys" class="form_select" style="width:232px;" id="question${status.index}" onchange="doChange();">
                        <option value="0">--请选择密保问题--</option>
                    </select>
                </td>
            </tr>
            <tr>
                <th>答&nbsp;&nbsp;案：</th>
                <td>
                    <input type="hidden" name="answers" id="answer${status.index}" value="" />
                    <input name="answerShow" type="text" class="form_input" style="width:220px;" id="answerShow${status.index}" tabindex="1"
                           maxlength="64" value="${answerShow[status.index]}" />
                    <em class="form_tip" id="errorSpan${status.index}">64个字符以内</em>
                </td>
            </tr>
    </c:forEach>
       <tr>

           <th></th>
           <td>
               <input type="hidden" name="returnUrl" value="" id="returnUrl"/>
               <input type="hidden" id="businessType" name="questionBinder.businessType" value=""/>
               <a href="###" onclick="return bind()" class="btn_default_lg" >保存</a>
               <a href="###" class="btn_cancel_lg" onclick="parent.location.href='${appServer}/front/member/securityCenter_index.htm'">取消</a>
           </td>
       </tr>

        </tbody>
    </table>
</form>
    </div>
<!-- 提示 -->
<div id="msg-modal" tabindex="-1" role="dialog" aria-hidden="" data-delay="2000" class="modal hide fade alert">
    <div class="modal-dialog"></div>
</div>
<script type="text/javascript">
    $("#returnUrl").val(parent.$("#returnUrlParent").val());
    $("#businessType").val(parent.$("#businessTypeParent").val());
    $("#answerShow0").val("");
    var questions = new Array();
    <c:forEach var="all" items="${questionBinder.protectedQuestionAll}">
    questions.push({questionId: '${all.questionKey}', questionContext: '${all.question}'});
    </c:forEach>
    //初始化问题

    function bind() {

        if (check()){
                    var postData = {'questionKeys0': question[0].value,'questionKeys1': question[1].value,'questionKeys2': question[2].value,
                        'answers0': answer[0].value,'answers1': answer[1].value,'answers2': answer[2].value,
                        'answerShow0': answerShow[0].value,'answerShow1': answerShow[1].value,'answerShow2': answerShow[2].value,
                        'questionBinder.businessType':parent.$("#businessTypeParent").val(),'returnUrl':parent.$("#returnUrlParent").val()};
                    $.ajax({
                        url: $CONFIG.appServer + "/front/securityCenter/asyncUpdateQuestion_front.htm",
                        type: 'post',
                        dataType: 'json',
                        data: postData,
                        success: function(json){
                            var msg = "";
                            if (json.result) {
                                msg = "保存成功";
                                setTimeout(function(){
                                    window.location.href = $CONFIG.appServer + "/front/member/securityCenter_index.htm"
                                }, 2500);
                            } else{
                                msg = "保存失败";
                            }
                            $('#msg-modal').children('.modal-dialog').text(msg).parent().modal('show');
                        },
                        error: function(){
                        }
                    });
                }
        }
            //$("#questionBindForm20110902141116").submit();
</script>
<script type="text/javascript">
    $('.sidebar .menu li:first').addClass('current');
</script>
<script type="text/javascript" src="${staticServer }/scripts/front/member/questionCommon.js"></script>
<script type="text/javascript" src="${staticServer}/scripts/common/bootstrap-modal.js"></script>
<script type="text/javascript" src="${staticServer}/scripts/common/bootstrap-tooltip.js"></script>
<script type="text/javascript">doInit(questions);</script>
