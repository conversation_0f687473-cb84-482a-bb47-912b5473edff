<%@ page contentType="text/html;charset=utf-8" %>
<%@ include file="/common/taglibs.jsp" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>

    <meta name="Keywords" content="顺网通行证,密保问题,手机绑定,找回,验证码"/>
    <meta name="Description" content="用户需要输入收到的手机验证码来继续操作。"/>
    <title>顺网通行证-找回密保问题-用手机找回</title>

</head>

<body>

<div class="c_head">
    <i class="forget_icon"></i>
    <span class="title">找回密保问题</span>
    <span class="desc">找回密保问题-用手机找回</span>
</div>
<div class="c_body forget_s03">
    <ul class="step_bar">
        <li><em>1</em> 选择找回方式</li>
        <li class="current"><em>2</em> 进行安全验证</li>
        <li><em>3</em> 设置新密保问题</li>
    </ul>
    <div class="forget_send">
        <i class="f_tel"></i>
        <p class="tip">通过手机${maskedMobileNumber}找回</p>
        <p>我们已经发送了一条手机短信到您的手机上，请注意查收！</p>
    </div>

    <div class="form_group">
        <form name="mainForm" id="mainForm" method="post" action="/front/login/goPhoneReset_front.htm">
            <table cellpadding="0" cellspacing="0">
                <tbody>
                <tr>
                    <th class="w_md">验证码：</th>
                    <td>
                        <input name="activeNo" type="text" class="form_input" style="width:220px;" id="activeNo"
                               tabindex="1" value="" maxlength="20"/>

                        <input type="hidden" name="memberName" value="${mobileBider.number}"/>
                        <a href="###" id="sendAgain" class="btn_default_md" onclick="getCodeAgain();"></a> <span id="msgEm" class="form_error"></span>
                    </td>
                </tr>
                <tr>
                    <th></th>
                    <td>
                        <a href="###" class="btn_default_lg" onclick="tosubmit();">下一步</a></td>
                </tr>
                </tbody>
            </table>

    </div>
</div>
</form>
<script type="text/javascript">
    var sec =${sec}; //倒计时的秒数
    <c:if test="${!empty errorMsg}">
    var errorMsg = "${errorMsg}";
    $("#msgEm").text(errorMsg);
    </c:if>
    showSendAgain();
    function showSendAgain(){
        var sendAgain=document.getElementById("sendAgain");
        if(sec>0){
            sendAgain.disabled=true;
            sendAgain.className="chk_time";
            $("#sendAgain").html(sec.toString()+"后重新获取");
            sendAgain.style.color="#494949";
            sec = sec-1;
            setTimeout("showSendAgain()",1000);
        }else{
            sendAgain.className="btn_default_md";
            sendAgain.style.color="#FFFFFF";
            sendAgain.disabled=false;
            $("#sendAgain").html("重新获取验证码 ");
        }
    }
    function tosubmit() {
        document.forms[0].submit();
    }
    function getCodeAgain(){
        document.forms[0].action="<c:url value='/front/login/findProblemSendPhone_front.htm?sendType=1'/>";

        var sendAgain=document.getElementById("sendAgain");
        sendAgain.disabled=true;
        document.forms[0].submit();
    }
</script>
</body>

</html>

