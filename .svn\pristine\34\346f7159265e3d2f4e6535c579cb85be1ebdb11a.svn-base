package com.shunwang.basepassport.weixin.constant;

public interface BarLoginEnum {

    enum ModeEnum {
        NET_BAR(1, "网吧"),
        E_SPORT_HOTEL(2, "电竞酒店"),
        SCANNING_CODE(3, "业务扫码"),
        SCANNING_NO_LOGIN_CODE(4, "业务扫码非登录"),
        NET_BAR_FREE_LOGIN(5, "计费免登"),
        ;

        ModeEnum(Integer value, String name) {
            this.value = value;
            this.name = name;
        }

        private Integer value;
        private String name;

        public Integer getValue() {
            return value;
        }

        public void setValue(Integer value) {
            this.value = value;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }

    enum BindStateEnum {
        BIND_MSG(1, "绑定消息"),
        NO_BIND_MSG(2, "未绑定消息"),
        LOGIN_SUCC(3, "登录成功"),
        BIND_MOBILE(4, "绑定手机"),
        BUSINESS_SCAN(5, "业务扫码"),
        WX_BIND_MOBILE(6, "微信绑手机"),
        ;

        BindStateEnum(Integer value, String name) {
            this.value = value;
            this.name = name;
        }

        private int value;
        private String name;

        public int getValue() {
            return value;
        }

        public void setValue(int value) {
            this.value = value;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }

    enum CardMsgType {
        BIND_MOBILE("bindMobile"),
        AUTO_LOGIN("autoLogin"),
        LOGIN_FAIL("loginFail"),
        ;

        CardMsgType(String name) {
            this.name = name;
        }

        private String name;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }
}
