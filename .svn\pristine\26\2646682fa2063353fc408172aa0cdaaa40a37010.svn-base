package com.shunwang.basepassport.manager.service.bo.impl;


import com.shunwang.basepassport.manager.request.swpay.CafeActualityRequest;
import com.shunwang.basepassport.manager.request.swpay.GetMemDepositTypeRequest;
import com.shunwang.basepassport.manager.request.swpay.MoneySelectRequest;
import com.shunwang.basepassport.manager.request.swpay.MoneyUnfrozeRequest;
import com.shunwang.basepassport.manager.response.swpay.GetMemDepositTypeResponse;
import com.shunwang.basepassport.manager.response.swpay.MoneySelectResponse;
import com.shunwang.basepassport.manager.service.bo.SwpayInterfaceBo;
import com.shunwang.basepassport.manager.service.swpay.SwpayServiceClient;

public class SwpayInterfaceImpl implements SwpayInterfaceBo {
    /**
     * json解析目前有问题，子商户未处理
     * @param memberId
     * @return
     */
    @Override
    public boolean isHlbAccount(Integer memberId) {
        GetMemDepositTypeRequest request = new GetMemDepositTypeRequest();
        request.setMemberId(memberId);
        GetMemDepositTypeResponse response = SwpayServiceClient.execute(request);
        if (response == null || !response.isSuccess()) {
            throw new RuntimeException("系统异常，请稍后再试。");
        }
        return response.isHlb();
    }

    @Override
    public void doMoneyUnFroze(Integer memberId) {
        MoneyUnfrozeRequest request = new MoneyUnfrozeRequest();
        request.setMemberId(memberId);
        SwpayServiceClient.execute(request);
    }

    @Override
    public void doCafeActualityNotice(Integer memberId) {
        CafeActualityRequest request = new CafeActualityRequest();
        request.setMemberId(memberId);
        SwpayServiceClient.execute(request);
    }

    @Override
    public MoneySelectResponse doMoneySelect(Integer memberId) {
        MoneySelectRequest request = new MoneySelectRequest();
        request.setMemberId(memberId);
        return SwpayServiceClient.execute(request);
    }
}
