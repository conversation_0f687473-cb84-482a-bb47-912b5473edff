package com.shunwang.baseStone.reason.pojo;

import java.io.Serializable;
import java.util.Date;

import com.shunwang.baseStone.context.BackUserContext;
import com.shunwang.baseStone.context.BaseStoneContext;
import com.shunwang.baseStone.core.detail.Detail;
import com.shunwang.baseStone.core.detail.DetailItem;
import com.shunwang.baseStone.core.detail.HasDetail;
import com.shunwang.baseStone.core.pojo.BaseStoneObject;
import com.shunwang.baseStone.editlog.pojo.EditLog;
import com.shunwang.baseStone.reason.dao.ReasonDao;

public class Reason extends BaseStoneObject implements HasDetail{
	private static final long serialVersionUID = -2350052328299426913L;
	public EditLog editLog = new EditLog();
	private Integer reasonId;
	private String siteType;
	private String moduleType;
	private String content;
	private Integer state;
	private Date timeAdd;
	private Date timeEdit;
	private String userAdd;
	private String userEdit;

	
	public Reason(){
		
	}
	public void add() {
		getDao().save(this);
	}
	
	public void update() {
		getDao().update(this);
	}
	
	public ReasonDao getDao() {
		return (ReasonDao)BaseStoneContext.getInstance().getBean("reasonDao");
	}
	
	public void setReasonId(Integer value){
		this.reasonId=value;
	}
	public Integer getReasonId(){
		return this.reasonId;
	}
	
	public void setSiteType(String value){
		editLog.addItem(new DetailItem("所属平台",siteType, value));
		this.siteType=value;
	}
	public String getSiteType(){	
		return this.siteType;
	}
	public void setModuleType(String value){
		editLog.addItem(new DetailItem("模块名称",moduleType, value));		
		this.moduleType=value;
	}
	public String getModuleType(){
		return this.moduleType;
	}
	public void setContent(String value){
		editLog.addItem(new DetailItem("备注",content, value));
		this.content=value;
	}
	public String getContent(){
		return this.content;
	}
	public void setState(Integer value){
		editLog.addItem(new DetailItem("状态(0: 启用    1：  禁用)",state, value));
		this.state=value;
	}
	public Integer getState(){
		return this.state;
	}
	public void setTimeAdd(Date value){
		this.timeAdd=value;
	}
	public Date getTimeAdd(){
		return this.timeAdd;
	}
	public void setTimeEdit(Date value){
		this.timeEdit=value;
	}
	public Date getTimeEdit(){
		return this.timeEdit;
	}
	public void setUserAdd(String value){
		this.userAdd=value;
	}
	public String getUserAdd(){
		return this.userAdd;
	}
	public void setUserEdit(String value){
		this.userEdit=value;
	}
	public String getUserEdit(){
		return this.userEdit;
	}
	public String getStateName() {
		return this.state == 0?"启用":"禁用";
	}

	@Override
	public Detail getDetail() {
		// TODO Auto-generated method stub
		return editLog;
	}

	@Override
	public void beginBuildLog() {
		// TODO Auto-generated method stub
		String sysclass = this.getClass().toString();
		String conkey = sysclass.substring(sysclass.lastIndexOf(".") + 1,
				sysclass.length());
		editLog.beginBuildLog(true);
		editLog.setEditItem("拒绝理由管理");
		editLog.setConfKey(conkey);
		editLog.setUserAdd(BackUserContext.getUserName());
	}
	@Override
	public Serializable getPk() {
		// TODO Auto-generated method stub
		return reasonId;
	}

}
