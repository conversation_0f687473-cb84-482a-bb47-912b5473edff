package com.shunwang.basepassport.binder.processor;

import com.shunwang.basepassport.binder.pojo.SendBinder;

import java.util.HashMap;
import java.util.Map;

/**
 * 业务处理类上下文
 * not thread safe
 *
 * <AUTHOR>
 * @date 2018/12/17
 **/
public class ProcessorContext<T> {

    private SendBinder sendBinder;
    /**
     * 存放actionBean
     */
    private T t;
    /**
     * 上下文容器
     * late init
     */
    private Map<String, Object> context;

    public ProcessorContext(SendBinder sendBinder) {
        this.sendBinder = sendBinder;
    }

    public ProcessorContext(SendBinder sendBinder, T t) {
        this.sendBinder = sendBinder;
        this.t = t;
    }

    public T getT() {
        return t;
    }

    public SendBinder getSendBinder() {
        return sendBinder;
    }

    public void put(String key, Object value) {
        if (context == null) {
            context = new HashMap<>();
        }
        context.put(key, value);
    }

    public Object get(String key) {
        if (context == null) {
            return null;
        }
        return context.get(key);
    }

}
