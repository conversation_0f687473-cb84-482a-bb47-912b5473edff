package com.shunwang.basepassport.asynctask;

import com.shunwang.util.encrypt.Md5Encrypt;
import com.shunwang.util.net.HttpClientUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

public class BigDataReportService {

    protected final Logger log = LoggerFactory.getLogger(BigDataReportService.class);

    private String bigDataReportUrl;
    private String projectId;
    private String secret;
    private String businessId;

    private Map<String, String> getHeaders(String body) {
        Map<String, String> headers = new HashMap<>();
        try {
            String signSource = secret + projectId + "|" + businessId + "|" + body + secret;
            String sign = Md5Encrypt.encrypt(signSource, StandardCharsets.UTF_8.name()).toUpperCase();

            headers.put("sign", sign);
            headers.put("projectId", projectId);
            headers.put("businessId", businessId);

            if (log.isDebugEnabled()) {
                log.debug("签名源串:" + signSource);
                log.debug("签名:" + sign);
            }
        } catch (Exception e) {
            log.error("设置header错误", e);
        }

        return headers;
    }

    /**
     * 上报大数据
     */
    public String toReport(String body) {
        byte[] responseBytes = HttpClientUtils.doPost(bigDataReportUrl, body, getHeaders(body), StandardCharsets.UTF_8);
        String response = new String(responseBytes, StandardCharsets.UTF_8);
        log.debug("上报大数据结果：[{}]", response);
        return response;
    }

    public void setBigDataReportUrl(String bigDataReportUrl) {
        this.bigDataReportUrl = bigDataReportUrl;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public void setSecret(String secret) {
        this.secret = secret;
    }

    public void setBusinessId(String businessId) {
        this.businessId = businessId;
    }
}
