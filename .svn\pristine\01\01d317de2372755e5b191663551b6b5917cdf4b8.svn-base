package com.shunwang.passport.weixin.interceptor;

import org.apache.commons.lang3.StringUtils;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import com.shunwang.passport.weixin.exceptions.WeixinNoBindingUserException;
import com.shunwang.passport.weixin.business.WeixinBaseAction;
import com.shunwang.framework.struts2.action.BaseAction;
import com.shunwang.passport.weixin.exceptions.WeixinBaseException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.opensymphony.xwork2.Action;
import com.opensymphony.xwork2.ActionInvocation;
import com.opensymphony.xwork2.interceptor.AbstractInterceptor;
import com.shunwang.baseStone.core.exception.BaseStoneException;

/**
 * 微信相关业务的统一异常处理
 *
 * <AUTHOR>
 * @since 2014-04
 *
 */
public class WeixinExceptionInterceptor extends AbstractInterceptor {
	
	private static final long serialVersionUID = 6856129853549527802L;
	private final static Logger log = LoggerFactory.getLogger(WeixinExceptionInterceptor.class);

	@Override
	public String intercept(ActionInvocation invoke) {
		try {
			return invoke.invoke();
        } catch (WeixinNoBindingUserException e) { //没有绑定用户转到绑定页
            return "showBind";
		} catch (Exception e) {
            processError(invoke,e);
		}
		return Action.INPUT;
	}

    private void processError(ActionInvocation invoke, Exception e) {

        /* --------------- 设置页面的提示消息 -------------------*/
        String promptErrorMsg = "";
        if (e instanceof WeixinBaseException || e instanceof BaseStoneException ) {
            promptErrorMsg = e.getMessage();
        } else {
            promptErrorMsg = "未知错误";
        }
        BaseAction action = (BaseAction)invoke.getAction();
        action.setErrorMsg(promptErrorMsg);

        /* ---------------  向log4j记录日志   -------------------------*/
        if (action instanceof WeixinBaseAction) {
            WeixinBaseAction ta = (WeixinBaseAction)action;
            ta.setOpResult("出错");
            if (StringUtils.isBlank(ta.getOpName())) {
                logStackAndContext(invoke,e);
                return;
            }
            StringBuilder logMsg  = new StringBuilder(ta.getOpName());
            logMsg.append(ta.getOpResult()).append("\n");
            logMsg.append(buildContextStr(ta)).append("\n");
            logMsg.append(buildExceptionMsg(e));
            log.warn(logMsg.toString());
        } else {
            logStackAndContext(invoke,e);
        }
    }

    /**
     * 对于业务异常,(比如手机号已绑定,用户已存在等),只打印三行堆栈信息
     * 这样可以节省日志的空间,避免无用的堆栈信息占满日志
     */
    private String buildExceptionMsg(Exception e) {
        StringBuffer sb = new StringBuffer("出错消息:");
        sb.append(e.toString());
        sb.append("\n");

        StackTraceElement[] stack = e.getStackTrace();
        int c = stack.length >= 3 ? 3 : stack.length;
        for (int i=0; i<c; i++) {
            sb.append("    at ").append(stack[i].toString()).append("\n");
        }
        return sb.toString();
    }

    @SuppressWarnings("all")
    private String buildContextStr(BaseAction ta) {
        HttpServletRequest req = ((BaseAction)ta).getRequest();
        Map<String,Object> paramMap = req.getParameterMap();
        StringBuffer sb = new StringBuffer("请求参数:\n    [");
        if (paramMap != null ) {
            for (String key : paramMap.keySet()) {

                String[] values = (String[])paramMap.get(key);
                String value = "" ;
                if (values.length == 1) {
                    value = values[0];
                } else {
                    value= "[";
                    for(String v : values){
                        value += v+",";
                    }
                    if (value.endsWith(",")) value = value.substring(0,value.length()-1);
                    value = value + "]";
                }

                sb.append(key).append(":");
                sb.append(value).append(",");
            }
            if (paramMap.size() > 0) {
                sb.deleteCharAt(sb.length()-1);
            }
        }
        sb.append("]");
        return sb.toString();
    }

    private void logStackAndContext(ActionInvocation invoke, Exception e) {
        Object action = invoke.getAction();
        String uri = "未知URL";
        if (action instanceof BaseAction) {
            HttpServletRequest req = ((BaseAction)action).getRequest();
            uri=req.getRequestURI();
        } 
        StringBuilder sb = new StringBuilder("访问"+uri+"出错,");
        if (action instanceof BaseAction) {
            sb.append(buildContextStr((BaseAction)action));
        }
        log.warn(sb.toString(),e);
    }
    
}
