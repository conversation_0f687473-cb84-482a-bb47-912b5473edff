<%@ page contentType="text/html;charset=UTF-8"%>
<%@ include file="/common/taglibs.jsp"%>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name="Keywords" content="顺网通行证、 修改邮箱 、申诉" />
<meta name="Description" content="填写申诉理由，填写新邮箱等待审核。" />
<title>顺网通行证-修改邮箱-申诉修改</title>

<script type="text/javascript" src="${staticServer}/scripts/front/member/min/validation.min.js"></script>
<script type="text/javascript" src="${staticServer}/scripts/front/common/citys.js" ></script>
<script type="text/javascript" src="${staticServer}/scripts/front/common/calendar.js"></script>
<script type="text/javascript" src="${staticServer}/scripts/common/md5.js"></script>
<script type="text/javascript" src="${staticServer}/scripts/front/find/globalAppeal_front.js"></script>
<script type="text/javascript" src="${staticServer}/scripts/front/changeEmail/changeEmail.js"></script>

</head>
<body>
<%@ include file="/front/changeEmail/global_nav.jsp" %>
<div class="c_body forget_s04">
    <ul class="step_bar">
        <li class="current"><em>1</em> 填写账号信息</li>
        <li><em>2</em> 设置新邮箱</li>
    </ul>
    <%--<div class="form_group">--%>

                <form id="gotoResetForm" action="<c:url value='/front/noLogin/goAppealChangeEmailReset_front.htm'/>"  method="post">
                    <input  type="hidden"  name="memberName"  value="${memberName}" />
                    <input type="hidden" name="appeal.userName" id="userName" value="${memberName}"/>
                    <input type="hidden" name="appeal.realName" id="realName" value="${appeal.realName}"/>
                    <input type="hidden" name="appeal.idCardNo" id="userIdCard" value="${appeal.idCardNo}"/>
                    <%@include file="/front/find/globalAppeal_front.jsp" %>
                </form>
    <%--</div>--%>
</div>
<script type="text/javascript" >
    var selectPro = document.getElementById("selectPro");
    var selectPro1 = document.getElementById("selectPro1");
    doInitPro(selectPro, "${usedProvince}");
    doChangeCity("${usedProvince}", document.getElementById('selectCity'), "${usedCity}");
    doInitPro(selectPro1, "${regProvince}");
    doChangeCity("${regProvince}", document.getElementById('selectCity1'), "${regCity}");
    doInitForError();
</script>
</body>
</html>
