function chooseTypeHide2(){
	$("#text1").hide();
	if($("#idCardType").val()!=1){
		showOrHideWord();
	}
	$("#idCardType").val("1");
	$("#idCardTypeError").html("");
	try {
		showBuuton();
	    swfuFront.setButtonText(buttonTextFront);
		swfuBack.setButtonText(buttonTextBack);
	}catch (ex) {
		
	}
}
function chooseTypeHide3(){
	$("#upload_idcard_side").hide();
	if($("#idCardType").val()!=3){
		showOrHideWord();
	}
	$("#idCardType").val("3");
	$("#idCardTypeError").html("");
	try {
		showBuuton();
	    swfuFront.setButtonText(buttonTextFront);
		swfuBack.setButtonText(buttonTextBack);
	}catch (ex) {
		
	}
}
function chooseTypeShow(){
	$("#upload_idcard_side").show();
	if($("#idCardType").val()!=2){
		showOrHideWord();
	}
	$("#idCardType").val("2");
	$("#idCardTypeError").html("");
	try {
		showBuuton();
	    swfuFront.setButtonText(buttonTextFront);
		swfuBack.setButtonText(buttonTextBack);
	}catch (ex) {
		
	}
}
function change(){
	var a=document.getElementById("text2");
	if(a.checked==true){
		$("#endDate").css({"color":"gray","text-decoration":"none"});
		$("#endDate").attr("disabled", true);
		$("#endDate").val("请选择截止时间");
		$("#idCardEndtime").val("1970-01-01");
        $("#endDate").val("1970-01-01");
		$("#idCardEndTimeError").html("");
        $("#selYear1").val("0").attr("disabled",true);
        $("#selMonth1").val("0").attr("disabled",true);
        $("#selDay1").val("0").attr("disabled",true);
    }
	else{
		$("#endDate").css({"color":"#155499","text-decoration":""});
        $("#selYear1").attr("disabled",false);
        $("#selMonth1").attr("disabled",false);
        $("#selDay1").attr("disabled",false);
		$("#idCardEndtime").val("");
        $("#endDate").val("");
		$("#endDate").mouseover(function(event){
    	$("#endDate").css({"color":"#ff6600"});
    	});
    	$("#endDate").mouseout(function(event){
    		$("#endDate").css({"color":"#155499"});
    	});
    }
}
//检验真实姓名
function realNameCheck(){
	changeColorOther("realName");
	var realName=doTrim("realName");
	if(realName==""){
		setShowErrorMsg("realNameError", "真实姓名不能为空，请输入。");
		return false;
	}
	else if(!checkRealNameCommon(realName)){
		setShowErrorMsg("realNameError", "输入不能超过20个字符，请重新输入。");
		return false;
	}
	else if(!validateRealName(realName)){
		setShowErrorMsg("realNameError", "真实姓名请使用中文，2-10个汉字。");
		return false;
	}
//	else if(isSensitive(realName)){
//		setShowErrorMsg("realNameError", "真实姓名不能包含禁用词语。");
//		return false;
//	}
	setShowMsg("realNameError", "请输入真实姓名，最多输入10个汉字。");
    changeColorOtherTip("realName");
	$("#realName").val(realName);
	return true;
}
//检验 联系电话
function linkPhoneCheck(){
	changeColorOther("linkPhone");
  //如果界面上没显示linkPhone输入框,则不需要检查
  if ($("#linkPhone").length == 0 ) return true;

	var linkPhone=doTrim("linkPhone");
	if(linkPhone==""){
		setShowErrorMsg("linkPhoneError", "联系电话不能为空，请输入。");
		return false;
	}
	else if(calLength(linkPhone)>20){
		setShowErrorMsg("linkPhoneError", "最多可输入20位数字，请重新输入。");
		return false;
	}
	
	else if(!(checkLinkPhoneCommon(linkPhone)||checkMobileNoCommon(linkPhone))){
		setShowErrorMsg("linkPhoneError", "联系电话不合法，请重新输入。");
		return false;
	}
	setShowMsg("linkPhoneError", "最多输入20位数字，固定电话请写区号。");
    changeColorOtherTip("linkPhone");
	$("#linkPhone").val(linkPhone);
	return true;
}
//检验联系地址
function linkAddrCheck() {

	var areaId = $('#areaId').val();
	if (areaId == '') {
		setShowErrorMsg("linkAreaError", "请选择所在区。");
		return false ;
	} else {
		setShowMsg("linkAreaError", "区必选");
	}


    var reg=/[()<&{\/\'\"]/;
	changeColorOther("linkAddr");
	var linkAddr=doTrim("linkAddr");
	if(!linkAddr){
		setShowErrorMsg("linkAddrError", "联系地址不能为空，请输入。");
		return false;
	}
	else if(!checkCompAddrCommon(linkAddr)){
		setShowErrorMsg("linkAddrError", "最多可输入128个字符，请重新输入。");
		return false;
	}
    else if(reg.test(linkAddr)){
        setShowErrorMsg("linkAddrError", "联系地址不能有特殊字符，请重新输入。");
        return false;
    }
	setShowMsg("linkAddrError", "最多可输入128个字符。");
	$("#linkAddr").val(linkAddr);
    changeColorOtherTip("linkAddr");
	return true;	
}
//检验身份证
function idCardNoCheck(){
	changeColorOther("idCardNoShow");
	var idCardNo=doTrim("idCardNo");
	if(doTrim("idCardNo")==""){
		setShowErrorMsg("idCardNoError", "身份证号码不能为空，请输入18位身份证号码。");
		return false;
	}
	var result = validateIdCard(idCardNo);
	if(result==1){
		setShowErrorMsg("idCardNoError", "无效身份证号码,请输入18位有效身份证号。");
		return false;
	}
	else if(result==2){
		setShowErrorMsg("idCardNoError", "非法地区。");
		return false;
	}
	else if(result==3){
		setShowErrorMsg("idCardNoError", "非法生日。");
		return false;
	}
	setShowMsg("idCardNoError", "请输入18位有效身份证号。");
    changeColorOtherTip("idCardNoShow");
	$("#idCardNo").val(($("#idCardNo").val().trim()));
	return true;	
}
function idCardNoShowCheck(){
	var idCardNoStr=$("#idCardNoShow").val();
	$("#idCardNo").val($("#idCardNoShow").val());
	$("#idCardNoIsFull").val("tmd");
	idCardNoCheck();
}
function doSubmit(){
	var a=true; //检验身份证有效期
	var b=true; //检验身份证类型
	var c=true; //请上传身份证正面扫描件
	var d=true; //请上传身份证背面扫描件
	var flag=true;
	flag=flag&realNameCheck();
	flag=flag&linkPhoneCheck();
	flag=flag&linkAddrCheck();
	flag=flag&idCardNoCheck();
    $("#idCardEndtime").val($("#endDate").val());
	//检验身份证有效期
	if($("#idCardEndtime").val() == '' || $("#idCardEndtime").val() == '请选择截止时间'){
		setShowErrorMsg("idCardEndTimeError", "请选择身份证有效截止时间。");
		a=false;
	}
	else if(!$("#text2").prop("checked")){
		a=checkIdCardEndTime();
	} 
	else{
		$("#idCardEndTimeError").html("");
	}
	//检验身份证类型
	var idCardType=$("#idCardType").val();	
	if(!idCardType){
		setShowErrorMsg("idCardTypeError", "请选择身份证类型。");
		b=false;
	}
	//身份证正面
	if(!$("#idCardImg1").val()){
		setShowErrorMsg("idCardImg1Error", "请上传身份证正面扫描件。");
		c=false;
	}
	//身份证背面
	if(idCardType == 2 && !$("#idCardImg2").val()){
		setShowErrorMsg("idCardImg2Error", "请上传身份证背面扫描件。");
		d=false;
	}
	if(idCardType==2){
		if(!(flag&a&b&c&d)){
			return false;
		}
	}
	else{
		if(!(flag&a&b&c)){
			return false;
		}
	}
	$("#confirmPersonalActuForm").submit()
}
function setShowErrorMsg(id,msg){
	$("#"+id).css("color","red");
	$("#"+id).html(msg);
}

function setShowMsg(id,msg){
	$("#"+id).css("color","gray");
	$("#"+id).html(msg);
}
function changeColorOther(id){
	$("#"+id).css({"border-color":"#FE0000"});
}
function changeColorOtherTip(id){
    $("#"+id).css({"border-color":"#979797"});
}

function showFrontPic(event,id){
	var name='/images/actuality/person/idcardFront/'+$("#"+id).val();
    $("#show").css("top","-200px");
    $("#show").css("left","100px");
	showPersonal(event,name);
}

function showBackPic(event,id){
	var name='/images/actuality/person/idcardBack/'+$("#"+id).val();
    $("#show").css("top","-150px");
    $("#show").css("left","100px");
	showPersonal(event,name);
}

function showOrHideWord(){
	//$("#idCardImg1").val("");
	//$("#idCardImg2").val("");
	//$("#imageFile1Show").hide();
	//$("#imageFile2Show").hide();
	//$("#uploadMsg1").html("");
	//$("#uploadMsg2").html("");
}
function checkIdCardEndTime()
{
    if($("#endDate").val()=="0-0-0" || $("#endDate").val()=="年-月-日"){
        setShowErrorMsg("idCardEndTimeError", "请选择身份证有效期。");
        return false;
    }
    else if(!checkDate($("#endDate").val())){
		$("#idCardEndtime").val($("#endDate").val());
		setShowErrorMsg("idCardEndTimeError", "身份证有效截止时间已过期。");
		return false;
	}
	else{
		$("#idCardEndtime").val($("#endDate").val());
		$("#idCardEndTimeError").html("")
		return true;
	}
}
$(document).ready(function(){

    var $form = $('.form_group');
    var $prov = $form.find('select[name="prov"]'),
        $city = $form.find('select[name="city"]'),
        $dist = $form.find('select[name="personalActuInfo.areaId"]');
    var provVal = $prov.attr('s-data');
    var cityVal = $city.attr('s-data');
    var distVal = $dist.attr('s-data');


    var loadAreaJSON = function(callback, data) {
        $.ajax({
            url: $CONFIG.appServer + "/front/common/queryArea.htm",
            type: 'post',
            dataType: 'json',
            data: {pid : data.id, r: Math.random()},
            success: function(json){
                var dataJson = null;
                if (json.result) {
                    dataJson = json.data;
                }
                callback(dataJson);
            },
            error: function(){
            }
        });
    };

    $prov.on('change', function() {
        $city.empty();
        $city.append('<option value="" selected="selected">请选择</option>');
        if ($(this).val() == "") {
            $city.change();
            return;
        }

        loadAreaJSON(function(json) {
            for (var i = 0; i < json.length; i++) {
                if ($prov.val() == json[i].parentId) {
                    $city.append('<option value="' + json[i].areaId + '" ' + ((cityVal == json[i].areaId) ? "selected=selected" : "") + ' >' + json[i].name + '</option>');
                }
            }
            $city.change();
            cityVal = null;
        }, {id: $prov.val()});
    });

    $city.on('change', function() {
        $dist.empty();
        $dist.append('<option value="" selected="selected">请选择</option>');
        if ($(this).val() == "") return;
        loadAreaJSON(function(json) {
            for (var i = 0; i < json.length; i++) {
                if ($city.val() == json[i].parentId) {
                    $dist.append('<option value="' + json[i].areaId + '" ' + ((distVal == json[i].areaId) ? "selected=selected" : "") + ' >' + json[i].name + '</option>');
                }
            }
            distVal = null;
        }, {id: $city.val()})
    });

    if (provVal != "") {
        $prov.val(provVal);
        $prov.change();
    };
});
