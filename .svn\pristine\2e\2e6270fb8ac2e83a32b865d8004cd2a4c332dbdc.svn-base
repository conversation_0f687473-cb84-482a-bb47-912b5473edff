<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <bean id="noticeDestination" class="org.apache.activemq.command.ActiveMQQueue">
        <constructor-arg index="0" value="cancel_notice_queue"/>
    </bean>

    <!-- producer -->
    <bean id="noticeMessageProducer" class="com.shunwang.baseStone.jms.TextMessageProducer">
        <property name="destination" ref="noticeDestination"/>
        <property name="template" ref="jmsTemplate"/>
    </bean>

    <bean id="noticeMessageConsumer" class="org.springframework.jms.listener.DefaultMessageListenerContainer">
        <property name="connectionFactory" ref="activeMQConnectionFactory" />
        <property name="destination" ref="noticeDestination"/>
        <property name="messageListener">
            <bean class="org.springframework.jms.listener.adapter.MessageListenerAdapter">
                <constructor-arg>
                    <bean class="com.shunwang.baseStone.jms.NoticeMessageConsumer">
                        <property name="serviceNotifyDao" ref="serviceNotifyDao"/>
                    </bean>
                </constructor-arg>
                <property name="defaultListenerMethod" value="handleMessage" />
                <property name="messageConverter" ref="messageConverter" />
            </bean>
        </property>
    </bean>
    <bean id="messageConverter"
          class="org.springframework.jms.support.converter.SimpleMessageConverter" />

</beans>