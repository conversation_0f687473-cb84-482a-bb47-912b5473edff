/*!
 * jKongyi JavaScript Library v1.0
 *
 * <AUTHOR>
 * @since 2015-04-29
 */
!function(window,undefined){function sibling(a,b){for(;(a=a[b])&&1!==a.nodeType;);return a}var document=window.document,location=window.location,version="1.0",jKongyi=function(a,b){return new jKongyi.fn.init(a,b)},rtrim=/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,rquickExpr=/^(?:(<[\w\W]+>)[^>]*|#([\w-]*))$/;jKongyi.fn=jKongyi.prototype={jkongyi:version,constructor:j<PERSON><PERSON>yi,selector:"",length:0,init:function(a,b){var c,d,e;if(!a)return this;if("string"==typeof a)if(c="<"===a.charAt(0)&&">"===a.charAt(a.length-1)&&a.length>=3?[null,a,null]:rquickExpr.exec(a),!c||!c[1]&&b){for(d=document.querySelectorAll(a),e=0;e<d.length;e++)this[e]=d[e];this.length=d.length,this.context=document,this.selector=a}else c[1]||(d=document.getElementById(c[2]),d&&d.parentNode&&(this.length=1,this[0]=d),this.context=document,this.selector=a);else if(a.length){for(d=a,e=0;e<d.length;e++)this[e]=d[e];this.length=d.length,this.context=document,this.selector=a}return this},pushStack:function(a){var b=jKongyi.merge(this.constructor(),a);return b.prevObject=this,b.context=this.context,b},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},eq:function(a){var b=this.length,c=+a+(0>a?b:0);return this.pushStack(c>=0&&b>c?[this[c]]:[])},next:function(){return sibling(this[0],"nextSibling")},prev:function(){return sibling(this[0],"previousSibling")}},jKongyi.fn.init.prototype=jKongyi.fn,jKongyi.fn.forEach=function(a){return this.map(a),this},jKongyi.fn.map=function(a){var b,d,c=[];for(d=0;d<this.length&&(b=a.call(this,this[d],d),c.push(b),b!==!1);d++);return c},jKongyi.fn.mapOne=function(a){var b=this.map(a);return b.length>1?b:b[0]},jKongyi.fn.find=function(a){return jKongyi(this.selector+" "+a)},jKongyi.merge=function(a,b){for(var c=+b.length,d=0,e=a.length;c>d;d++)a[e++]=b[d];return a.length=e,a},jKongyi.create=function(a,b){var d,c=jKongyi([document.createElement(a)]);if(b){b.className&&(c.addClass(b.className),delete b.className),b.text&&(c.text(b.text),delete b.text);for(d in b)b.hasOwnProperty(d)&&c.attr(d,b[d])}return c},jKongyi.trim=function(a){return null==a?"":(a+"").replace(rtrim,"")},jKongyi.ajax=function(a){return(new jKongyi.ajax.fn.init).doAjax(a)},jKongyi.ajax.fn=jKongyi.ajax.prototype={constructor:jKongyi.ajax,retryDelay:5,isDisp:!1,cacheXMLHttp:[],lastModified:[],getFreeXMLHttp:function(){var a,b,c;for(a=0,b=this.cacheXMLHttp.length;b>a;a++)if(c=this.cacheXMLHttp[a],0===c.readyState||4===c.readyState)return c;return this.cacheXMLHttp[this.cacheXMLHttp.length]=this.createStandardXHR(),this.cacheXMLHttp[this.cacheXMLHttp.length-1]},createStandardXHR:function(){try{return new window.XMLHttpRequest}catch(a){}},makeURL:function(a){var b;return b=a.indexOf("?")>0?"&":"?",a+=b+"rand-num-url="+Math.random()},setRequestHeader:{html:"text/html",xml:"application/xml,text/xml",json:"application/json,text/javascript",javascript:"text/javascript,application/javascript",text:"text/plain"},isNotLastModify:function(a,b){var c,d;if(4===a.readyState)return c=a.getResponseHeader("Last-Modified"),d=a.getResponseHeader("Etag"),this.lastModified[b]=[c,d],this.lastModified[b]},parseJSON:function(data){return data&&"string"==typeof data?eval("("+data+")"):null},conEval:function(a){var b,c;return a&&(b=document.getElementsByTagName("head")[0].lastChild||document.documentElement.firstChild,c=document.createElement("script"),c.type="text/javascript",c.appendChild(document.createTextNode(a)),this.insertAfter(c,b)),a},insertAfter:function(a,b){var c=b.parentNode;c.lastChild==b?c.appendChild(a):c.insertBefore(a,b.nextSibling)},echoType:function(a,b){var h,c=a.getResponseHeader("Content-Type")||"",d="xml"===b||c.indexOf("xml")>=0,e="json"===b||c.indexOf("json")>=0,f="javascript"===b||c.indexOf("javascript")>=0,g=d?a.responseXML:a.responseText;return d&&(h=g.documentElement.nodeName,"parsererror"==h&&this.error("XML parsererror.")),"string"==typeof g&&(e&&(g=this.parseJSON(g)),f&&(g=this.conEval(g))),g},error:function(a){throw a},setTimeOut:function(a,b){setTimeout(b,1e3*a)},doAjax:function(a){var l,m,n,b=a.type.toUpperCase()||"GET",c=a.url,d="POST"==b?this.hybridPar(a.data):a.data,e=(a.dataType||"text").toLowerCase(),f=a.timer,g=a.async,h=a.success,i=a.error,j=this.getFreeXMLHttp(),k=c;"GET"==b&&(c=this.makeURL(c)),l=this,m=!0,n=function(){var b;4===j.readyState&&4===j.readyState&&(m=!1,200===j.status||304===j.status&&l.lastModified[k]?(l.isNotLastModify(j,k),"undefined"!=typeof h&&(200===j.status&&h(l.echoType(j,e)),null!=f&&(b=function(){a.url=k,l.doAjax(a)},l.setTimeOut(f,b)))):"undefined"!=typeof i&&(i(j.status),l.isDisp&&(b=function(){a.url=k,a.timer=null,l.doAjax(a)},l.setTimeOut(f,b))))},g&&(j.onreadystatechange=n),j.open(b,c,g),!this.lastModified[k]||"xml"!=e&&"html"!=e&&"text"!=e&&"javascript"!=e||(j.setRequestHeader("If-Modified-Since",this.lastModified[k][0]),j.setRequestHeader("If-None-Match",this.lastModified[k][1])),j.setRequestHeader("X-Requested-With","XMLHttpRequest"),"GET"===b&&(d=null),"POST"==b?j.setRequestHeader("Content-type","application/x-www-form-urlencoded"):j.setRequestHeader("Content-type",this.setRequestHeader[e]),this.isNotLastModify(j,k),j.send(d),g||n()},hybridPar:function(a){var b,c;if("object"==typeof a){b="";for(c in a)b=b+c+"="+a[c]+"&";return b!==undefined?b.indexOf("&")>=0&&(b=b.substr(0,b.length-1)):b=null,b}return"string"==typeof a?a:void 0},init:function(){return this}},jKongyi.ajax.fn.init.prototype=jKongyi.ajax.fn,jKongyi.fn.val=function(a){return arguments.length?this.forEach(function(b){b.value=a}):this.mapOne(function(a){return a.value})},jKongyi.fn.text=function(a){return"undefined"!=typeof a?this.forEach(function(b){"string"==typeof b.textContent?b.textContent=a:b.innerText=a}):this.mapOne(function(a){return"string"==typeof a.textContent?a.textContent:a.innerText})},jKongyi.fn.html=function(a){return"undefined"!=typeof a?this.forEach(function(b){b.innerHTML=a}):this.mapOne(function(a){return a.innerHTML})},jKongyi.fn.addClass=function(a){return this.forEach(function(b){if("string"!=typeof a)for(var c=0;c<a.length;c++)b.classList.add(a[c]);else b.classList.add(a)})},jKongyi.fn.removeClass=function(a){return this.forEach(function(b){b.classList.remove(a)})},jKongyi.fn.attr=function(a,b){return"undefined"!=typeof b?this.forEach(function(c){c.setAttribute(a,b)}):this.mapOne(function(b){return b.getAttribute(a)})},jKongyi.fn.append=function(a){return this.forEach(function(b,c){a.forEach(function(a){b.appendChild(c>0?a.cloneNode(!0):a)})})},jKongyi.fn.prepend=function(a){return this.forEach(function(b,c){for(var d=a.length-1;d>-1;d--)b.insertBefore(c>0?a[d].cloneNode(!0):a[d],b.firstChild)})},jKongyi.fn.remove=function(){return this.forEach(function(a){return a.parentNode.removeChild(a)})},jKongyi.fn.on=function(){return document.addEventListener?function(a,b){return this.forEach(function(c){c.addEventListener(a,b,!1)})}:document.attachEvent?function(a,b){return this.forEach(function(c){c.attachEvent("on"+a,b)})}:function(a,b){return this.forEach(function(c){c["on"+a]=b})}}(),jKongyi.fn.off=function(){return document.removeEventListener?function(a,b){return this.forEach(function(c){c.removeEventListener(a,b,!1)})}:document.detachEvent?function(a,b){return this.forEach(function(c){c.detachEvent("on"+a,b)})}:function(a){return this.forEach(function(b){b["on"+a]=null})}}(),window.jKongyi=window.$=jKongyi,"function"==typeof define&&define.amd&&define("jkongyi",[],function(){return jKongyi}),document.querySelectorAll||(document.querySelectorAll=function(a){var d,b=document.createElement("style"),c=[];for(document.documentElement.firstChild.appendChild(b),document._qsa=[],b.styleSheet.cssText=a+"{x-qsa:expression(document._qsa && document._qsa.push(this))}",window.scrollBy(0,0),b.parentNode.removeChild(b);document._qsa.length;)d=document._qsa.shift(),d.style.removeAttribute("x-qsa"),c.push(d);return document._qsa=null,c}),document.querySelector||(document.querySelector=function(a){var b=document.querySelectorAll(a);return b.length?b[0]:null})}(window);