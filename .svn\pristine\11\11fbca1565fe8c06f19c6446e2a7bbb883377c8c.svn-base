function checkAnswer(){var a=!0;return $.each($answers,function(b){return 0==$.trim($(this).val()).length?(showTips("请回答密保问题"+(b+1)),a=!1,!1):void 0}),a}function encryptAnswer(){$.each($answers,function(a){$md5Answers.eq(a).val(hex_md5($(this).val()))})}function showTips(a){$tips.find("p").html(a),$tips.show()}function init(){var a=$(".tips-error p").text();a&&$(".tips-error").show(),""==$tips.find("p").html()&&$tips.hide()}var $form=$(".form-box"),$answers=$(".form-control"),$md5Answers=$(".md5Answer"),$submit=$("#verify"),$tips=$(".tips-error");$form.on("submit",function(a){return checkAnswer()?(encryptAnswer(),void 0):(a=a||window.event,a.preventDefault?a.preventDefault():window.event.returnValue=!1,!1)}),init();