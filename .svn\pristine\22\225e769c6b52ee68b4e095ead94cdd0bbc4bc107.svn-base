package com.shunwang.baseStone.bussiness.web;

import java.util.Date;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.json.JSONException;
import org.json.JSONObject;

import com.shunwang.baseStone.bussiness.pojo.Bussiness;
import com.shunwang.baseStone.category.dao.CategoryDao;
import com.shunwang.baseStone.category.pojo.Category;
import com.shunwang.baseStone.context.BackUserContext;
import com.shunwang.baseStone.core.detail.DetailItem;
import com.shunwang.baseStone.loginelement.constants.LoginElementConstant;
import com.shunwang.baseStone.loginelement.dao.BaseLoginElementDao;
import com.shunwang.baseStone.loginelement.dao.LoginElementDao;
import com.shunwang.baseStone.loginelement.pojo.BaseLoginElement;
import com.shunwang.baseStone.loginelement.pojo.LoginElement;
//import com.shunwang.baseStone.siteinterface.dao.SiteInterfaceDao;
//import com.shunwang.baseStone.siteinterface.dao.SiteInterfaceDbDao;
import com.shunwang.baseStone.useroutinterface.dao.OutOauthDirDao;
import com.shunwang.baseStone.useroutinterface.pojo.OutOauthDir;
import com.shunwang.framework.ibatis.annotation.SingleValue;

public class BussinessAction extends com.shunwang.framework.struts2.action.ConditionCrudAction<com.shunwang.baseStone.bussiness.pojo.Bussiness,com.shunwang.baseStone.bussiness.dao.BussinessDao>{

	private static final long serialVersionUID = 2414695541721430659L;
	private Integer OUT_OAUTH_DIR_DEFAULT_STATE = 0 ;
	private Integer LOGIN_ELEMENT_DEFAULT_STATE = 0 ;

	private static final Logger log = LoggerFactory.getLogger(BussinessAction.class);
	
	private String bussinesskey;
	private String bussinessname;
	private Integer passportid;
	private Integer bussinessstate;	
	private String passportname;
	private String remark;
	private String categoryid;
	private String businesscategoryid;
	private Integer accountType ;
	private String schBussinesskey;
	private String schBussinessName;
	private String schBussinessstate;
	private String schCategoryid;
	private String schBusinessCategoryid;
	private Integer schShareLoginElement;
	private CategoryDao categoryDao;
	private BaseLoginElementDao baseLoginElementDao;
	private LoginElementDao loginElementDao;
	private OutOauthDirDao outOauthDirDao;
//	private SiteInterfaceDao siteInterfaceDao;
//	private SiteInterfaceDbDao siteInterfaceDbDao;
	
	@Override
	public String list() {
		List<Bussiness> list = getSchbo().find(super.buildConditionQuery());
		int cnt = getSchbo().findCnt(super.buildConditionQuery());
		setTotal(cnt);
		setSchResult(list);
		this.setNeedCloth(true);
		return SUCCESS;
	}
	
	@Override
	public String add() {
		Boolean update = false ;
		bussinesskey = bussinesskey.trim();
		Bussiness  bussiness=getCrudbo().getById(bussinesskey);
		if(null!=bussiness&&bussiness.getBussinesskey().equals(bussinesskey)){
			update = true ;
		}
		
		Category category = categoryDao.getById(categoryid);
		
		Bussiness bus;
		try {
			bus = new Bussiness();
			bus.beginBuildLog();		
			bus.setBussinesskey(bussinesskey.trim());
			bus.setBussinessname(bussinessname.trim());
			bus.setCategoryid(categoryid);
			bus.setBusinesscategoryid(businesscategoryid);
			bus.setShareloginelement(1);
			bus.setRemark(remark);
			bus.setAccountType(accountType) ;
			String businfo ="\n"+"商户Key:"+bussinesskey+"\n"+"商户名称："+bussinessname+"\n"+"备注："+remark+"\n"+"状态(0:打开 1：关闭)："+bus.getBussinessstate()+"账号体系(0:单账号 1：多账号)："+bus.getAccountType();
			if(update){
				bus.editLog.addItem(new DetailItem("修改商户","",businfo));
				this.getCrudbo().update(bus) ;
			}else {
				bus.editLog.addItem(new DetailItem("添加商户","",businfo));
				this.getCrudbo().save(bus);
				addLoginElementConfig(bus, category);
				addOutOauthDirConfig(bus, category);
			}

		} catch (Exception e) {
			log.error("", e);
		}	    
		return SUCCESS;
	}
	
	/**
	 * **
	 * 创建日期: 2013-12-16
	 * 创建作者：JINBAO
	 * @param 
	 * @return void
	 * 功能：添加作业单元时增加登录可用功能配置
	 *************
	 */
	private void addLoginElementConfig(Bussiness buss, Category category) {
		List<BaseLoginElement> baseLoginElementList = baseLoginElementDao.findAll();
		for (BaseLoginElement baseLoginElement : baseLoginElementList) {
			LoginElement loginElement = new LoginElement();
			loginElement.setType(LoginElementConstant.LOGIN_ELEMENT_CONFIG);
			loginElement.setTypeName(LoginElementConstant.LOGIN_ELEMENT_NAME_CONFIG);
			loginElement.setCategoryId(Integer.valueOf(buss.getCategoryid()));
			loginElement.setCategoryName(category.getCategoryname());
			loginElement.setBussinessKey(buss.getBussinesskey());
			loginElement.setBussinessName(buss.getBussinessname());
			loginElement.setName(baseLoginElement.getBaseLoginElementName());
			loginElement.setNameCn(baseLoginElement.getBaseLoginElementNameCn());
			if(baseLoginElement.getBaseLoginElementName().equals(LoginElementConstant.CHECK_CODE)) {
				loginElement.setValue("3");
			}
			if(null != baseLoginElement.getDefaultState()){
				loginElement.setState(baseLoginElement.getDefaultState());
			}else {
				loginElement.setState(LOGIN_ELEMENT_DEFAULT_STATE);
			}
			loginElement.setShowOrderBy(baseLoginElement.getShowOrderBy());
			loginElement.setTimeAdd(new Date());
			loginElement.setUserAdd(BackUserContext.getUserName());
			loginElement.setTimeEdit(new Date());
			loginElement.setUserEdit(BackUserContext.getUserName());
			loginElementDao.save(loginElement);
		}
	}
	
	/**
	 * **
	 * 创建日期: 2013-12-16
	 * 创建作者：JINBAO
	 * @param 
	 * @return void
	 * 功能：添加作业单元信息时增加第三方登录配置
	 *************
	 */
	private void addOutOauthDirConfig(Bussiness buss, Category category) {
		List<OutOauthDir> outOauthDirList = outOauthDirDao.findAll();
		for (OutOauthDir outOauthDir : outOauthDirList) {
			LoginElement loginElement = new LoginElement();
			loginElement.setType(LoginElementConstant.PARTLY_ACCESS_CONFIG);
			loginElement.setTypeName(LoginElementConstant.PARTLY_ACCESS_NAME_CONFIG);
			loginElement.setCategoryId(Integer.valueOf(buss.getCategoryid()));
			loginElement.setCategoryName(category.getCategoryname());
			loginElement.setBussinessKey(buss.getBussinesskey());
			loginElement.setBussinessName(buss.getBussinessname());
			loginElement.setName(outOauthDir.getDirName());
			loginElement.setNameCn(outOauthDir.getDirName());
			if(null != outOauthDir.getDefaultState()){
				loginElement.setState(outOauthDir.getDefaultState());
			}else {
				loginElement.setState(OUT_OAUTH_DIR_DEFAULT_STATE);
			}
			loginElement.setShowOrderBy(outOauthDir.getDirOrderBy());
			loginElement.setTimeAdd(new Date());
			loginElement.setUserAdd(BackUserContext.getUserName());
			loginElement.setTimeEdit(new Date());
			loginElement.setUserEdit(BackUserContext.getUserName());
			loginElementDao.save(loginElement);
		}
	}
	

	@Override
	public String del() {
		try {
//			siteInterfaceDao.refreshCachePojo(bussinesskey);
//			siteInterfaceDbDao.deleteByBussinessKey(bussinesskey);
			
			Bussiness bus=getCrudbo().getById(bussinesskey);
			bus.beginBuildLog();
			String businfo ="\n"+"商户Key："+bus.getBussinesskey()+"\n"+"商户名称："+bus.getBussinessname()+"\n"+"备注："+bus.getRemark()+"\n"+"状态(0:打开 1：关闭)："+bus.getBussinessstate();
			bus.editLog.addItem(new DetailItem("删除商户",businfo,""));
		    this.getCrudbo().delete(bus);
		    
		    if(null != bus.getCategoryid() && null != bussinesskey) {
		    	LoginElement emement = new LoginElement();
			    emement.setCategoryId(Integer.valueOf(bus.getCategoryid()));
			    emement.setBussinessKey(bussinesskey);
			    
			    loginElementDao.delete(emement);
		    }
		} catch (Exception e) {
			log.error("删除商户异常",e);
		}
		return SUCCESS;
	}

	@SingleValue
	public String getSchBussinessstate() {
		return schBussinessstate;
	}

	public void setSchBussinessstate(String schBussinessstate) {
		this.schBussinessstate = schBussinessstate;
	}

	@SingleValue(equal="like")
	public String getSchBussinesskey() {
		if(schBussinesskey!=null && !"".equals(schBussinesskey))
			return schBussinesskey+"%";
		return schBussinesskey;
	}

	public void setSchBussinesskey(String schBussinesskey) {
		this.schBussinesskey = schBussinesskey;
	}
	@SingleValue(equal="like")
	public String getSchBussinessName() {
		if(schBussinessName!=null && !"".equals(schBussinessName))
			return schBussinessName+"%";
		return schBussinessName;
	}
	public Integer getBussinessstate() {
		return bussinessstate;
	}

	public void setBussinessstate(Integer bussinessstate) {
		this.bussinessstate = bussinessstate;
	}
	public void setSchBussinessName(String schBussinessName) {
		this.schBussinessName = schBussinessName;
	}

	public BussinessAction(){
		this.setNeedCheckExist(true);
	}
	
	public void setBussinesskey(String value){
		this.bussinesskey=value;
	}
	public String getBussinesskey(){
		return this.bussinesskey;
	}
	protected java.io.Serializable getPk() {
		return bussinesskey;
	}
	public void setBussinessname(String value){
		this.bussinessname=value;
	}
	public String getBussinessname(){
		return this.bussinessname;
	}
	public void setPassportid(Integer value){
		this.passportid=value;
	}
	public Integer getPassportid(){
		return this.passportid;
	}
	public void setPassportname(String value){
		this.passportname=value;
	}
	public String getPassportname(){
		return this.passportname;
	}
	public void setRemark(String value){
		this.remark=value;
	}
	public String getRemark(){
		return this.remark;
	}		
	
	public String getCategoryid() {
		return categoryid;
	}

	public void setCategoryid(String categoryid) {
		this.categoryid = categoryid;
	}

	@SingleValue
	public String getSchCategoryid() {
		return schCategoryid;
	}
	
	public void setSchCategoryid(String schCategoryid) {
		this.schCategoryid = schCategoryid;
	}

	@SingleValue
	public Integer getSchShareLoginElement() {
		return schShareLoginElement;
	}

	public void setSchShareLoginElement(Integer schShareLoginElement) {
		this.schShareLoginElement = schShareLoginElement;
	}

	@Override
	public boolean isNeedCloth() {		
		return true;
	}
	@Override
	public JSONObject wearCloth(Bussiness pojo, JSONObject jsonObject) {
		String bussinessstateshow=null;
		try {
		if(pojo.getBussinessstate()==0)
			bussinessstateshow="打开";
		if(pojo.getBussinessstate()==1)
			bussinessstateshow="关闭";		
			jsonObject.put("bussinessstateshow", bussinessstateshow);
		} catch (JSONException e) {
			log.error("JSON对象生成异常", e);
		}
		return super.wearCloth(pojo, jsonObject);
	}

	public String close(){
		Bussiness bussiness = this.getCrudbo().getById(this.getPk());
		if(bussiness != null)
			bussiness.beginBuildLog();
			bussiness.close();
		return  SUCCESS;
	}
	
	public String open(){
		Bussiness bussiness = this.getCrudbo().getById(this.getPk());
		if(bussiness != null)
			bussiness.beginBuildLog();
			bussiness.open();
		return SUCCESS;
	}
	
	public String getBusinesscategoryid() {
		return businesscategoryid;
	}

	public void setBusinesscategoryid(String businesscategoryid) {
		this.businesscategoryid = businesscategoryid;
	}
	
	@SingleValue
	public String getSchBusinessCategoryid() {
		return schBusinessCategoryid;
	}

	public void setSchBusinessCategoryid(String schBusinessCategoryid) {
		this.schBusinessCategoryid = schBusinessCategoryid;
	}

	public CategoryDao getCategoryDao() {
		return categoryDao;
	}

	public void setCategoryDao(CategoryDao categoryDao) {
		this.categoryDao = categoryDao;
	}

	public BaseLoginElementDao getBaseLoginElementDao() {
		return baseLoginElementDao;
	}

	public void setBaseLoginElementDao(BaseLoginElementDao baseLoginElementDao) {
		this.baseLoginElementDao = baseLoginElementDao;
	}

	public LoginElementDao getLoginElementDao() {
		return loginElementDao;
	}

	public void setLoginElementDao(LoginElementDao loginElementDao) {
		this.loginElementDao = loginElementDao;
	}

	public OutOauthDirDao getOutOauthDirDao() {
		return outOauthDirDao;
	}

	public void setOutOauthDirDao(OutOauthDirDao outOauthDirDao) {
		this.outOauthDirDao = outOauthDirDao;
	}

//	public SiteInterfaceDao getSiteInterfaceDao() {
//		return siteInterfaceDao;
//	}
//
//	public void setSiteInterfaceDao(SiteInterfaceDao siteInterfaceDao) {
//		this.siteInterfaceDao = siteInterfaceDao;
//	}
//
//	public SiteInterfaceDbDao getSiteInterfaceDbDao() {
//		return siteInterfaceDbDao;
//	}
//
//	public void setSiteInterfaceDbDao(SiteInterfaceDbDao siteInterfaceDbDao) {
//		this.siteInterfaceDbDao = siteInterfaceDbDao;
//	}

	@SingleValue(column="account_type")
	public Integer getAccountType() {
		return accountType;
	}

	public void setAccountType(Integer accountType) {
		this.accountType = accountType;
	}
}
