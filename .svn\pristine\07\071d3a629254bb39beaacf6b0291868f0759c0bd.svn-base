<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd" >
<sqlMap namespace="com.shunwang.basepassport.site.pojo.SiteInterface" >
<typeAlias alias="site" type="com.shunwang.basepassport.site.pojo.SiteInterface"/>
<typeAlias alias="siteQuery" type="com.shunwang.baseStone.site.query.SiteQuery"/>





<select id="getBySiteIdAndName" resultClass="site" parameterClass="siteQuery">
	SELECT 
        a.interface_key as interfaceKey,
        a.interface_state as interfacState,
        a.interface_site_id as interfaceSiteId,
        a.interface_site_name as interfaceSiteName,
        a.interface_md5_key as interfaceMd5Key,
        a.interface_permit_ip as interfacePermitIp,
        a.interface_return_url as interfaceReturnUrl,
        a.interface_remark as interfaceRemark,
        a.interface_timeout as interfaceTimeout,
        a.interface_request_method as interfaceRequestMethod,
        a.interface_url as interfaceUrl
   FROM config_site_interface a 
   WHERE a.interface_site_id = #siteId:String# and a.interface_site_name=#siteName#
</select>




</sqlMap>
