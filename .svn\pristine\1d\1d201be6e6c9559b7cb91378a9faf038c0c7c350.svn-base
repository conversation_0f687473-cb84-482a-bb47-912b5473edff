package com.shunwang.baseStone.jms;

import com.shunwang.baseStone.config.constants.MemberEnum;
import com.shunwang.baseStone.core.util.SignTool;
import com.shunwang.baseStone.siteinterface.context.SiteContext;
import com.shunwang.baseStone.siteinterface.pojo.SiteInterface;
import com.shunwang.basepassport.user.dao.ServiceNotifyDao;
import com.shunwang.basepassport.user.pojo.ServiceNotify;
import com.shunwang.util.date.DateUtil;
import com.shunwang.util.encrypt.Md5Encrypt;
import com.shunwang.util.json.GsonUtil;
import com.shunwang.util.lang.StringUtil;
import com.shunwang.util.net.HttpClientUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;
import java.util.Map;

/**
 * 具体接收缓存操作消息，对缓存做具体操作，只能操作一个cacheName的缓存
 * Cache cache = cacheManager.getCache(cacheName)
 *
 * <AUTHOR>
 * @date 2019/3/11
 **/
public class NoticeMessageConsumer extends MessageConsumer<String> {
    protected final Logger logger = LoggerFactory.getLogger(NoticeMessageConsumer.class);

    private ServiceNotifyDao serviceNotifyDao;

    @Override
    public void handleMessage(String notifyId) {
        try {
            if (notifyId == null) {
                logger.error("消息为空");
                return;
            }
            logger.info("消息体是[{}]", notifyId);
            doNotice(Integer.parseInt(notifyId));
            logger.info("消费消息结束:{}", notifyId);
        } catch (Exception e) {
            logger.error("处理消息出错", e);
        }
    }

    private void doNotice(Integer notifyId) {
        ServiceNotify notify = serviceNotifyDao.getById(notifyId);
        if (notify == null) {
            logger.info("通知notifyId[{}]不存在", notifyId);
            return;
        }
        if (MemberEnum.CancelNotifyState.NOTIFY_STATE_SUCCESS.getState() == notify.getState()) {

            return ;
        }
        SiteContext.setSiteId(notify.getSiteId());
        SiteContext.setSiteName(notify.getSiteName());
        SiteInterface siteInterface = SiteContext.getSiteInterface();
        if (siteInterface == null) {
            logger.error("siteID[" + notify.getSiteId() + "]未开通[" + notify.getSiteName() + "]接口权限，不回调");
            return;
        }
        Map<String, String> params = GsonUtil.jsonToMap(notify.getJsonData(), String.class);
        params.put("time", DateUtil.getCurrentDateStamp());
        String signSource = SignTool.buildSignStringSorted(params, "sign",siteInterface.getMd5Key());
        String sign = Md5Encrypt.encrypt(signSource).toUpperCase();
        params.put("sign", sign);
        String result = HttpClientUtils.doPost(notify.getNotifyUrl(), params);
        notify.setNotifyTimes(notify.getNotifyTimes() + 1);
        notify.setTimeEdit(new Date());
        if (StringUtil.isBlank(result)) {
            notify.setRemark("timeout");
        }
        if ("ok".equalsIgnoreCase(result)) {
            notify.setState(MemberEnum.CancelNotifyState.NOTIFY_STATE_SUCCESS.getState());
            notify.setRemark(result);
        }
        serviceNotifyDao.update(notify);
    }

    public ServiceNotifyDao getServiceNotifyDao() {
        return serviceNotifyDao;
    }

    public void setServiceNotifyDao(ServiceNotifyDao serviceNotifyDao) {
        this.serviceNotifyDao = serviceNotifyDao;
    }
}
