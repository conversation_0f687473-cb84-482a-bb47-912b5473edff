package com.shunwang.baseStone.cache;

import com.shunwang.baseStone.sysconfig.constant.SysconfigConstant;
import com.shunwang.util.lang.StringUtil;

/**
 * 缓存key生成工具类
 *
 * <AUTHOR>
 * @date 2019/3/7
 **/
public final class CacheKeyGenerator {

    public static String getSiteInterfaceKey(String businessKey, String serviceKey) {
        return CacheKeyConstant.SiteInterfaceConstants.BUS_SITE_INTERFACE_CACHE_KEY + trimAndToLowerCase(businessKey) + CacheKeyConstant.CACHE_SPLIT + trimAndToLowerCase(serviceKey);
    }

    public static String getLoginConfigKey(String businessKey, String name) {
        return CacheKeyConstant.LoginConfigConstants.BUS_LOGIN_ELEMENT_CACHE_KEY + trimAndToLowerCase(businessKey) + CacheKeyConstant.CACHE_SPLIT + trimAndToLowerCase(name);
    }

    public static String getOutOauthDirKey(String businessKey) {
        return CacheKeyConstant.OutOauthDirConstants.BUS_OUT_OAUTH_DIR_CACHE_KEY + trimAndToLowerCase(businessKey);
    }

    public static String getConfigResourceKey(String type, String name) {
        return CacheKeyConstant.ResourceConstants.RESOURCES_PREFIX + trimAndToLowerCase(type) + CacheKeyConstant.CACHE_SPLIT + trimAndToLowerCase(name);
    }

    public static String getCssConfigKey(String siteId, String bussCode) {
        return CacheKeyConstant.CssConstants.BUS_CSS_CONFIG_CACHE_KEY + trimAndToLowerCase(siteId) + CacheKeyConstant.CACHE_SPLIT + trimAndToLowerCase(bussCode);
    }

    public static String getAgreement(String siteId, String type, String platform) {
        return CacheKeyConstant.Agreement.AGREEMENT + "_" + trimAndToLowerCase(siteId) + "_" + trimAndToLowerCase(type) +
                "_" + trimAndToLowerCase(platform);
    }

    public static String getAgreementBase(String type, String platform) {
        return CacheKeyConstant.Agreement.AGREEMENT + "_" + trimAndToLowerCase(type) + "_" + trimAndToLowerCase(platform);
    }

    /**
     * 只缓存 CacheKeyConstant.SysConfigConstants.BUS_VALID_PHONE_CACHE_KEY
     * CacheKeyConstant.SysConfigConstants.BUS_GT_SWITCH_CACHE_KEY
     *
     * @param sysKey
     * @return
     */
    public static String getSysConfigKey(String sysKey) {
        if (SysconfigConstant.S_VALID_PHONE_NUM.equals(sysKey)) {
            return CacheKeyConstant.SysConfigConstants.BUS_VALID_PHONE_CACHE_KEY;
        }
        if (SysconfigConstant.S_GT_SWITCH.equals(sysKey)) {
            return CacheKeyConstant.SysConfigConstants.BUS_GT_SWITCH_CACHE_KEY;
        }
        return "";
    }

    public static String getWeixinOauthTokenKey(String appId, Integer type) {
        return CacheKeyConstant.SSO.WX_OAUTH_TOKEN + appId + "_" + type;
    }

    public static String getWeixinOauthKey(String siteId) {
        return CacheKeyConstant.SSO.WX_OAUTH + siteId;
    }

    public static String getSmsConfigKey(String siteId, String businessType) {
        return CacheKeyConstant.InterfaceSmsKey.SMS_CONFIG_CACHE + siteId + "_" + businessType;
    }
    public static String getEmailConfigKey(String siteId, String businessType) {
        return CacheKeyConstant.InterfaceSmsKey.EMAIL_CONFIG_CACHE + siteId + "_" + businessType;
    }

    public static String getAppidReplyKey(String appid,Integer mode, Integer bindState) {
        return CacheKeyConstant.SSO.WX_REPLY + appid + "_" + mode + "_" + bindState;
    }

    /**
     * 去除结尾 空字符并转换成小写
     *
     * @param key
     * @return
     */
    public static String trimAndToLowerCase(String key) {
        if (StringUtil.isBlank(key)) {
            return "";
        }
        return key.trim().toLowerCase();
    }
}
