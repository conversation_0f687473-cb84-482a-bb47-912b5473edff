package com.shunwang.passport.struts;

import org.apache.commons.lang.StringUtils;
import org.apache.struts2.ServletActionContext;
import org.apache.struts2.dispatcher.DefaultStaticContentLoader;

import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;

/**
 * Copyright© 2005-2014 shunwang. All Rights Reserved.
 * author: <EMAIL>
 * date: 14-12-8
 * time: 下午12:48
 * since:
 */
@Deprecated
public class PassportStaticContentLoader extends DefaultStaticContentLoader {

    @Override
    public void findStaticResource(String path, HttpServletRequest request, HttpServletResponse response) throws IOException {
        String name = cleanupPath(path);
        for (String pathPrefix : this.pathPrefixes)
        {
            URL resourceUrl = findResource(buildPath(name, pathPrefix));
            if (resourceUrl != null)
            {
                InputStream is = null;
                try
                {
                    String pathEnding = buildPath(name, pathPrefix);
                    if (resourceUrl.getFile().endsWith(pathEnding)) {
                        is = resourceUrl.openStream();
                    }
                }
                catch (IOException ex) {}
                continue;

            }
        }
        response.sendError(404);
    }

    @Override
    public boolean canHandle(String resourcePath) {
        return (this.serveStatic) && ((resourcePath.startsWith("/struts/")) || (resourcePath.startsWith("/static/")));
//        return (this.serveStatic) && ((resourcePath.startsWith("/struts/")));
    }

    @Override
    protected String cleanupPath(String path) {
        ServletContext servletContext = ServletActionContext.getServletContext();
        String staticVersion = String.valueOf(servletContext.getAttribute("staticVersion"));
        String fullPath = String.format("%s/%s", servletContext.getAttribute("staticPath"), staticVersion);
        if (path.startsWith(fullPath))
            return StringUtils.remove(path, fullPath);
        else
        return super.cleanupPath(path);
    }
}
