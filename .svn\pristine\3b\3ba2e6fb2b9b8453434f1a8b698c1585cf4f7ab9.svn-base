package com.shunwang.basepassport.jms.mode;

/**
 * 完全匹配模式
 * <AUTHOR>
 * @date 2019/3/11
 **/
public class ExactMode implements Mode {

    private String[] patterns;

    public ExactMode(String... patterns) {
        this.patterns = patterns;
    }

    @Override
    public int getType() {
        return MODE_EXACT;
    }

    @Override
    public boolean match(String key) {
        for (String pattern : patterns) {
            if (pattern.equals(key)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public void set(String... args) {
        patterns = args;
    }


    @Override
    public String toString() {
        return "type:MODE_EXACT,value=" + getType();
    }
}
