package com.shunwang.baseStone.notice.context;

import com.shunwang.baseStone.context.BaseStoneContext;
import com.shunwang.baseStone.context.RedisContext;
import com.shunwang.baseStone.notice.common.NoticeConstants;
import com.shunwang.baseStone.notice.dao.NoticeDao;
import com.shunwang.baseStone.notice.pojo.Notice;
import com.shunwang.util.lang.StringUtil;

public class NoticeContext {
	/**
	 * 
	 * 获取当前公告的内容
	 * @return
	 * <AUTHOR> 创建于 2011-10-31 下午03:49:06
	 * @throws
	 */
	public static String getNoticeCur(String pageType){
		Notice notice = getNoticeDao().getByPageType(pageType);
		if(null == notice||!notice.getIsOpen())
			return "";
		return notice.getModuleContentCur();
	}
	/**
	 * 
	 * 获取预览的内容
	 * @return
	 * <AUTHOR> 创建于 2011-10-28 下午03:43:42
	 * @throws
	 */
	public static String getNoticePrivew(String pageType,String isTmp){
		if(!StringUtil.isBlank(isTmp)&&isTmp.equals("no")){
			Notice notice = getNoticeDao().getByPageType(pageType);
			if(null == notice)
				return "";
			if(notice.getModuleState().equals(NoticeConstants.NUM_ONE))
				return notice.getModuleContentNew();
			return notice.getModuleContentCur();
		}
		return getNoticeNoSavePrivew(pageType);
		
	}
	/**
	 * 获取修改的临时内容
	 * 
	 * @return
	 * <AUTHOR> 创建于 2011-10-28 下午03:44:01
	 * @throws
	 */
	public static String getNoticeNoSavePrivew(String pageType){
		return RedisContext.getRedisCache().get(pageType+"_tmp");
	}
	
	public static NoticeDao getNoticeDao(){
		return (NoticeDao) BaseStoneContext.getInstance().getBean("noticeDao");
	}
}
