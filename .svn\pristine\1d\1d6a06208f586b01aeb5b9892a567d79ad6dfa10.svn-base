package com.shunwang.basepassport.user.web;

import com.shunwang.baseStone.cache.CacheKeyConstant;
import com.shunwang.baseStone.cache.lock.CounterLock;
import com.shunwang.baseStone.cache.service.CacheService;
import com.shunwang.baseStone.context.RedisContext;
import com.shunwang.baseStone.context.RedisOperation;
import com.shunwang.baseStone.core.action.BaseStoneAction;
import com.shunwang.baseStone.core.exception.BaseStoneException;
import com.shunwang.basepassport.actu.pojo.PersonalGameActuVerifyRecord;
import com.shunwang.basepassport.actu.service.GameIdCardVerifyBo;
import com.shunwang.basepassport.binder.common.ErrorCode;
import com.shunwang.basepassport.commonExp.*;
import com.shunwang.basepassport.user.common.IDCardCheckUtil;
import com.shunwang.basepassport.user.dao.MemberDao;
import com.shunwang.basepassport.user.pojo.Member;
import com.shunwang.basepassport.user.response.GameActuQueryRespone;
import com.shunwang.util.lang.StringUtil;

import java.time.Duration;

/**
 * Created by bao.jin on 2021/3/31.
 */
public class GameActuAction extends BaseStoneAction {

    private Integer memberId;
    private String memberName;
    private String realName;
    private String idCardNo;
    private String bizid;
    private String clientIp;
    private GameIdCardVerifyBo gameIdCardVerifyBo;
    private MemberDao memberDao;
    private RedisOperation redisOperation;

    @Override
    public void process() throws Exception {

        try {
            Member member = null;
            if (null != memberId) {
                member = memberDao.getByMemberId(memberId);
            } else if (null != memberName) {
                member = memberDao.getByName(memberName);
            }

            if (member == null) {
                throw new BaseStoneException( ErrorCode.C_1006 );
            }

            if (idCardNo.substring(idCardNo.length() - 1).equals("x")) {
                setIdCardNo(idCardNo.toUpperCase());
            }

            setIdCardNo(getIdCardNo().replaceAll(" ", ""));
            setRealName(getRealName().replaceAll(" ", ""));

            if (!redisOperation.setNx(idCardNo, idCardNo, 2)){
                throw new FrequentlyExp();
            }
            //同一姓名限定配置时间内配置次数
            String lockKey = "ANTI_GAME_COUNT_" + realName;
            Integer limitCount = RedisContext.getIntConfig(CacheKeyConstant.ConfigResourcesConstants.ANTI_ADDITION_CONFIG, CacheKeyConstant.ConfigResourcesConstants.ANIT_ADDITION_CONFIG_GAME_REAL_NAME_COUNT_LIMIT, 10);
            Integer limitTime = RedisContext.getIntConfig(CacheKeyConstant.ConfigResourcesConstants.ANTI_ADDITION_CONFIG, CacheKeyConstant.ConfigResourcesConstants.ANIT_ADDITION_CONFIG_GAME_REAL_NAME_TIME_LIMIT, 10);
            CounterLock lock = CacheService.newCounterLock(lockKey, limitCount, Duration.ofMinutes(limitTime));
            if (!lock.tryLock()) {
                throw new FrequentlyExp("同一姓名请求太频繁");
            }
            //同一身份证限定10分钟5次
            lockKey = "ANTI_GAME_COUNT_" + idCardNo;
            lock = CacheService.newCounterLock(lockKey, 5, Duration.ofMinutes(10));
            if (!lock.tryLock()) {
                throw new FrequentlyExp();
            }

            PersonalGameActuVerifyRecord record = gameIdCardVerifyBo.gameActu(member.getMemberId(), member.getMemberName(), idCardNo, realName, bizid, getSiteId());
            GameActuQueryRespone response = new GameActuQueryRespone(record);
            this.setBaseResponse(response);
        } catch (GameActuExp e) {
            e.setMsg("调用第三方游戏实名接口异常");
            throw e;
        }
    }

    @Override
    public void checkParam() {
        if (null == getMemberId() && StringUtil.isBlank(getMemberName())) {
            throw new ParamNotFoundExp("memberId");
        }
        if (StringUtil.isBlank(getRealName())) {
            throw new ParamNotFoundExp("realName");
        }
        if (getRealName().length() > 20) {
            throw new ParamLenErrorExp("realName");
        }
        if (StringUtil.isBlank(getIdCardNo())) {
            throw new ParamNotFoundExp("idCardNo");
        }
        if (StringUtil.isBlank(getBizid())) {
            throw new ParamNotFoundExp("bizid");
        }
        if (getIdCardNo().length() != 18 ) {
            throw new UserFormateErrorExp("身份证号码");
        }
        if (!IDCardCheckUtil.isAvailableIDCard(getIdCardNo())) {
            throw new UserFormateErrorExp("身份证号码");
        }
    }


    @Override
    public String getSiteName() {
        return "防沉迷认证接口";
    }

    public Integer getMemberId() {
        return memberId;
    }

    public void setMemberId(Integer memberId) {
        this.memberId = memberId;
    }

    public String getMemberName() {
        return memberName;
    }

    public void setMemberName(String memberName) {
        this.memberName = memberName;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getIdCardNo() {
        return idCardNo;
    }

    public void setIdCardNo(String idCardNo) {
        this.idCardNo = idCardNo;
    }

    public String getBizid() {
        return bizid;
    }

    public void setBizid(String bizid) {
        this.bizid = bizid;
    }

    public GameIdCardVerifyBo getGameIdCardVerifyBo() {
        return gameIdCardVerifyBo;
    }

    public void setGameIdCardVerifyBo(GameIdCardVerifyBo gameIdCardVerifyBo) {
        this.gameIdCardVerifyBo = gameIdCardVerifyBo;
    }

    public MemberDao getMemberDao() {
        return memberDao;
    }

    public void setMemberDao(MemberDao memberDao) {
        this.memberDao = memberDao;
    }

    public RedisOperation getRedisOperation() {
        return redisOperation;
    }

    public void setRedisOperation(RedisOperation redisOperation) {
        this.redisOperation = redisOperation;
    }

    public String getClientIp() {
        return clientIp;
    }

    public void setClientIp(String clientIp) {
        this.clientIp = clientIp;
    }
}
