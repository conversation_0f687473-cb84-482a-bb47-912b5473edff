<?xml version="1.0" encoding="UTF-8"?>

<!DOCTYPE struts PUBLIC 
    "-//Apache Software Foundation//DTD Struts Configuration 2.0//EN"
    "http://struts.apache.org/dtds/struts-2.0.dtd">

<struts>
	<constant name="struts.action.extension" value="htm"/>
	<constant name="com.opensymphony.xwork2.ObjectFactory" value="spring" />
	<constant name="struts.devMode" value="false" />
	<constant name="struts.configuration.xml.reload" value="false" />
	<constant name="struts.multipart.maxSize" value="5242880" />
    <!--<constant name="struts.staticContentLoader" value="com.shunwang.passport.struts.PassportStaticContentLoader" />-->


	<!-- base	-->
	<package name="passport-base" extends="struts-default">
		<interceptors>
		<interceptor name="exceptionProcesser" class="com.shunwang.passport.common.interceptor.ExceptionInterceptor"></interceptor>
		<interceptor name="transactionProcesser" class="com.shunwang.passport.common.interceptor.TransactionInterceptor"></interceptor>
		<interceptor name="ipcontextProcesser" class="com.shunwang.passport.common.interceptor.IpContextInterceptor"></interceptor>
		<interceptor-stack name="passportBaseStack">
			<interceptor-ref name="defaultStack" />
			<interceptor-ref name="exceptionProcesser" />	
			<interceptor-ref name="transactionProcesser" />	
			<interceptor-ref name="ipcontextProcesser" />	
		</interceptor-stack>
		</interceptors>
		<global-results>
            <result name="error">/front/common/500.jsp</result>
		</global-results>
		<global-exception-mappings>
			<exception-mapping result="error" exception="java.lang.Exception"></exception-mapping> 
		</global-exception-mappings>
	</package>
	
	<!-- nologin	-->
	<package name="nologinpassport" extends="passport-base">
		<interceptors>
			<interceptor-stack name="nologinDefaultStack">
				<interceptor-ref name="passportBaseStack" />
			</interceptor-stack>
		</interceptors>
		<default-interceptor-ref name="nologinDefaultStack"/>
		 
		<action name="checkCode" class="com.shunwang.baseStone.checkCode.web.CheckCodeImageAction"></action>
		 
		<!-- 验证码 
		<action name="checkCode" class="com.shunwang.passport.member.web.CaptchaImageAction"></action>   
		-->
	</package>
	
	<!-- login	-->
	<package name="passport" extends="passport-base">
		<interceptors>
			<interceptor name="loginChecked" class="com.shunwang.passport.common.interceptor.UserLoginCheckInterceptor"></interceptor>
			<interceptor-stack name="loginDefaultStack">
				<interceptor-ref name="passportBaseStack" />
				<interceptor-ref name="loginChecked" />	
			</interceptor-stack>
		</interceptors>
		<default-interceptor-ref name="loginDefaultStack"/>
	</package>

    <!-- SWPaySDK login	-->
    <package name="swpaySDKPassport" extends="passport-base">
        <interceptors>
            <interceptor name="swPaySdkloginChecked" class="com.shunwang.passport.common.interceptor.SWPaySDKUserLoginCheckInterceptor"></interceptor>
            <interceptor-stack name="loginDefaultStack">
                <interceptor-ref name="passportBaseStack" />
                <interceptor-ref name="swPaySdkloginChecked" />
            </interceptor-stack>
        </interceptors>
        <default-interceptor-ref name="loginDefaultStack"/>
    </package>

	<package name="login" extends="struts-default">
	
		<interceptors>
			<interceptor-stack name="swDefaultStack">
				<interceptor-ref name="conversionError"/>
				<interceptor-ref name="defaultStack" />
				 <interceptor-ref name="loginCheck" /> 
			</interceptor-stack>
			 <interceptor name="loginCheck" class="com.shunwang.baseStone.interceptor.LoginInterceptor"></interceptor>
		</interceptors>
		
	<default-interceptor-ref name="swDefaultStack" />
		<global-results>
			<result name="error">ispError.jsp</result>
			<result name="login" type="">/result.jsp</result>
		</global-results>
		<global-exception-mappings>
			<exception-mapping result="error" exception="java.lang.Exception"></exception-mapping>
		</global-exception-mappings>
	</package>
	<include file="baseStone/struts/Sysconfig_struts.xml" />
	
	
    <!-- @YHJ 通配所有struts下面的xml文件	大家不要再重复加载进去了  -->
	<include file="basepassportWeb/struts/*" />


</struts>
