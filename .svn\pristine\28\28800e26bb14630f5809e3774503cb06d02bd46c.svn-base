package com.shunwang.basepassport.config.dao;

import com.shunwang.baseStone.core.dao.BaseStoneIbatisDao;
import com.shunwang.basepassport.config.pojo.Service;
import org.springframework.cache.annotation.Cacheable;

public class ServiceDao extends BaseStoneIbatisDao<Service>{

	/**
	 * @see com.shunwang.baseStone.cache.keygenerator.ConfigServiceKeyGenerator
	 * @param serviceKey 缓存key
	 */
	@Cacheable(value = "cache", keyGenerator = "configServiceKeyGenerator", unless = "#result==null")
	public Service getByServiceKey(String serviceKey) {
		return (Service)getSqlMapClientTemplate().queryForObject(getStatementNameWrap("get"), serviceKey);
	}
}
