package com.shunwang.baseStone.sso.apapter;

import com.shunwang.baseStone.config.constants.ConfigOauthConstant;
import com.shunwang.baseStone.sso.constant.UserOutsiteConstant;
import com.shunwang.basepassport.user.pojo.MemberAccountBind;
import com.shunwang.util.math.RandomUtil;

/**
 * <AUTHOR>
 * @since 2016-03-23
 */
public class ConnectedAppleUnionIDAdapter extends AbstractConnectedAccountsAdapter {


    @Override
    protected MemberAccountBind getByUnionId() {
        return memberAccountBindDao.getByApple(member.getMemberId());
    }

    @Override
    protected int getAdapterType() {
        return ConfigOauthConstant.TYPE.APPLE.getInt();
    }

    @Override
    protected void initBindId(MemberAccountBind memberAccountBind, Integer memberId) {
        memberAccountBind.setApple(memberId);
    }

    protected String generalMemberName() {
        return useroutInterface.getPrefixName() + getUserId().substring(0, 13) + RandomUtil.getRandomStr(6);
    }

    @Override
    protected String getOutMemberName() {
        return getNickName();
    }

    @Override
    public String getInterfaceId() {
        return UserOutsiteConstant.APPLE_INTERFACE_ID;
    }

}
