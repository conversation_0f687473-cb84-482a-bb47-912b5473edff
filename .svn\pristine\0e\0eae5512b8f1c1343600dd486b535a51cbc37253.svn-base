<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMapConfig
        PUBLIC "-//iBATIS.com//DTD SQL Map Config 2.0//EN"
        "http://www.ibatis.com/dtd/sql-map-config-2.dtd">
<sqlMapConfig>
    <settings cacheModelsEnabled="true" enhancementEnabled="false" lazyLoadingEnabled="false" maxRequests="3000"
              maxSessions="3000" maxTransactions="3000" useStatementNamespaces="true"/>

    <sqlMap resource="basepassport/ibatis/weakPassword_SqlMap.xml"/>

    <sqlMap resource="basepassport/ibatis/WhereClause.xml"/>
    <sqlMap resource="basepassport/ibatis/bank_sqlMap.xml"/>
    <sqlMap resource="basepassport/ibatis/city_SqlMap.xml"/>
    <sqlMap resource="basepassport/ibatis/personalSendNumber_SqlMap.xml"/>
    <sqlMap resource="basepassport/ibatis/member_SqlMap.xml"/>
    <sqlMap resource="basepassport/ibatis/logonLog_SqlMap.xml"/>  <!--以后迁库全部改完记得删掉 -->
    <sqlMap resource="basepassport/ibatis/memberProtectedQuestion_SqlMap.xml"/>
    <sqlMap resource="basepassport/ibatis/protectedQuestion_SqlMap.xml"/>
    <sqlMap resource="basepassport/ibatis/ConfigInterface_SqlMap.xml"/>
    <sqlMap resource="basepassport/ibatis/ConfigOneLogin_SqlMap.xml"/>
    <sqlMap resource="basepassport/ibatis/configAreaAppid_SqlMap.xml"/>
    <sqlMap resource="basepassport/ibatis/configAppidReply_SqlMap.xml"/>
    <sqlMap resource="basepassport/ibatis/configBarBlackCard_SqlMap.xml"/>

    <sqlMap resource="basepassport/ibatis/personalActu_SqlMap.xml"/>
    <sqlMap resource="basepassport/ibatis/companyActu_SqlMap.xml"/>
    <sqlMap resource="basepassport/ibatis/cafeActu_SqlMap.xml"/>
    <sqlMap resource="basepassport/ibatis/cafeActuChange_SqlMap.xml"/>
    <sqlMap resource="basepassport/ibatis/personalEditLog_SqlMap.xml"/>
    <sqlMap resource="basepassport/ibatis/memberEmail_SqlMap.xml"/>
    <sqlMap resource="basepassport/ibatis/memberMobile_SqlMap.xml"/>
    <sqlMap resource="basepassport/ibatis/memberInfo_SqlMap.xml"/>
    <sqlMap resource="basepassport/ibatis/personalActuReport_SqlMap.xml"/>

    <sqlMap resource="basepassport/ibatis/sql_Appeal.xml"/>
    <sqlMap resource="basepassport/ibatis/sql_Appeal_ext.xml"/>
    <sqlMap resource="basepassport/ibatis/sql_question.xml"/>
    <sqlMap resource="basepassport/ibatis/memberAccountBind_SqlMap.xml"/>
    <sqlMap resource="basepassport/ibatis/memberAnswer_SqlMap.xml"/>
    <sqlMap resource="basepassport/ibatis/memberLog_SqlMap.xml"/>
    <sqlMap resource="basepassport/ibatis/memberOutSide_SqlMap.xml"/>
    <sqlMap resource="basepassport/ibatis/personalMobileCheckCode_SqlMap.xml"/>
    <sqlMap resource="basepassport/ibatis/personalEmailCheckCode_SqlMap.xml"/>
    <sqlMap resource="basepassport/ibatis/memberSafeNotice_SqlMap.xml"/>
    <sqlMap resource="basepassport/ibatis/regActiveNo_SqlMap.xml"/>
    <sqlMap resource="basepassport/ibatis/memberToken_SqlMap.xml"/>
    <sqlMap resource="basepassport/ibatis/sql_member_unsafe.xml"/>
    <sqlMap resource="basepassport/ibatis/configArea_SqlMap.xml"/>
    <sqlMap resource="basepassport/ibatis/configResources_SqlMap.xml"/>
    <sqlMap resource="basepassport/ibatis/memberHeadImg_SqlMap.xml"/>
    <sqlMap resource="basepassport/ibatis/idcardBind_SqlMap.xml"/>
    <sqlMap resource="basepassport/ibatis/personalOneLoginInfo_SqlMap.xml"/>
    <sqlMap resource="basepassport/ibatis/configSmsConfig_SqlMap.xml"/>
    <sqlMap resource="basepassport/ibatis/configEmailConfig_SqlMap.xml"/>
    <sqlMap resource="basepassport/ibatis/user_bank_card_sqlMap.xml"/>
    <sqlMap resource="basepassport/ibatis/configWxBindAd_Sqlmap.xml"/>
    <sqlMap resource="basepassport/ibatis/wxTemplateMsg_sqlMap.xml"/>

    <sqlMap resource="basepassport/ibatis/weixinOauth_SqlMap.xml"/>
    <sqlMap resource="basepassport/ibatis/weixinOauthToken_SqlMap.xml"/>
    <sqlMap resource="basepassport/ibatis/weixinClientSecret_SqlMap.xml"/>

    <sqlMap resource="basepassport/ibatis/actuVerifyRecord_SqlMap.xml"/>
    <sqlMap resource="basepassport/ibatis/agreementRecord_SqlMap.xml"/>
    <sqlMap resource="basepassport/ibatis/gameActuVerifyRecord_SqlMap.xml"/>
    <sqlMap resource="basepassport/ibatis/wxIdCardBind_SqlMap.xml"/>
    <sqlMap resource="basepassport/ibatis/serviceNotify_sqlMap.xml"/>
    <sqlMap resource="basepassport/ibatis/weixinOpenIdUnionId_sqlMap.xml"/>
    <sqlMap resource="basepassport/ibatis/barFreeLoginAuth_SqlMap.xml"/>

    <sqlMap resource="basepassport/ibatis/memberMultipleAccountBind_SqlMap.xml"/>

</sqlMapConfig>
