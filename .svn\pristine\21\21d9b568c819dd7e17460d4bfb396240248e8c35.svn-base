function time(a){0>=a?($sentcode.removeClass("disabled").addClass("btn-primary").prop("disabled",!1),$sentcode.text("发送验证码"),a=60):($sentcode.removeClass("btn-primary").addClass("disabled").prop("disabled",!0),$sentcode.text(a+MSG.C1002),a--,setTimeout(function(){time(a)},1e3))}function showTips(a){$tips.find("p").html(a),$tips.show()}function getKey(a){var b=$email.val();return"voe"+b+a}function init(){var b,d,e,a=$(".tips-error p").text();a&&$(".tips-error").show();try{b=window.sessionStorage.getItem(getKey("_sendStatus_"))}catch(c){console.error(c)}"true"==b?$activeNo.removeAttr("disabled"):$activeNo.attr("disabled","true"),""==$tips.find("p").html()&&$tips.hide();try{d=window.sessionStorage.getItem(getKey("_voe_"))}catch(c){console.error(c)}e=Math.round(((new Date).getTime()-d)/1e3),60>e&&(wait=60-e,time(wait))}var $form=$(".form-box"),$sentcode=$("#sent-code"),$email=$form.find("input[name='oldNumber']"),$activeNo=$form.find("input[name='activeNo']"),$tips=$(".tips-error"),CONSTANTS={SEC:60},MSG={C1001:"重新获取",C1002:"秒后重新获取",C1003:"邮箱邮件验证码不能为空"};$form.on("submit",function(){return 0==$activeNo.val().length?(showTips(MSG.C1003),!1):!0}),$sentcode.on("click",function(){var a=$email.val();time(CONSTANTS.SEC);try{window.sessionStorage.setItem(getKey("_voe_"),(new Date).getTime()),window.sessionStorage.setItem(getKey("_sendStatus_"),"true")}catch(b){console.error(b)}$activeNo.removeAttr("disabled"),$tips.hide(),$.ajax({url:$CONFIG.appServer+"/front/swpaysdk/sendEmailActiveNo.htm",data:{oldNumber:a},dataType:"json",type:"post",success:function(a){!a.result&&a.msg&&showTips(a.msg)},error:function(a){showTips(a)}})}),init();