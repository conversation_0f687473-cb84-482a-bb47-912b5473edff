package com.shunwang.basepassport.binder.exception;

import java.util.HashMap;

import com.shunwang.baseStone.core.exception.BaseStoneException;
import com.shunwang.basepassport.binder.common.BinderConstants;
import static com.shunwang.basepassport.binder.common.ErrorCode.*;
/**
 * ****************************
 * 版权所有：顺网科技 保留所有权利
 * 创建日期: Jul 21, 2011 5:28:35 PM
 * 创建作者：xiangjie
 * 文件名称：
 * 版本： 1.0
 * 功能：时间超时异常
 * 最后修改时间：
 * 修改记录：
****************************************
 */
public class TimeOutAndInvalidExp extends BaseStoneException{

	/**
	 * 
	 */
	private static final long serialVersionUID = -3134165272605758874L;
	
	public TimeOutAndInvalidExp(String type) {
		// TODO Auto-generated constructor stub
		super(C_1022.getCode(), EXP_MAP.get(type));
	}
	
	private static HashMap<String, String> EXP_MAP = new HashMap<String, String>();
	static{
		EXP_MAP.put(BinderConstants.EMAIL, "抱歉，此邮件链接已过期，请重新申请！");
		EXP_MAP.put(BinderConstants.MOBILE, "验证码已失效！");
	}

}
