<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd" >
<sqlMap namespace="com.shunwang.baseStone.richText.pojo.RichText">
    <resultMap class="com.shunwang.baseStone.richText.pojo.RichText" id="BaseResultMap">
        <result property="id" column="id" jdbcType="int"/>
        <result property="name" column="name" jdbcType="varchar"/>
        <result property="nameEn" column="name_en" jdbcType="varchar"/>
        <result property="terminal" column="terminal" jdbcType="varchar"/>
        <result property="type" column="type" jdbcType="varchar"/>
        <result property="state" column="state" jdbcType="int"/>
        <result property="link" column="link" jdbcType="varchar"/>
        <result property="content" column="content" jdbcType="text"/>
        <result property="version" column="version" jdbcType="varchar"/>
        <result property="timeAdd" column="time_add" jdbcType="datetime"/>
        <result property="timeEdit" column="time_edit" jdbcType="datetime"/>
        <result property="userAdd" column="user_add" jdbcType="varchar"/>
        <result property="userEdit" column="user_edit" jdbcType="varchar"/>
        <result property="remark" column="remark" jdbcType="varchar"/>
    </resultMap>
	<resultMap class="com.shunwang.baseStone.richText.pojo.RichText" id="basicBaseResultMap">
		<result property="id" column="id" jdbcType="int"/>
		<result property="name" column="name" jdbcType="varchar"/>
		<result property="nameEn" column="name_en" jdbcType="varchar"/>
		<result property="terminal" column="terminal" jdbcType="varchar"/>
		<result property="type" column="type" jdbcType="varchar"/>
		<result property="state" column="state" jdbcType="int"/>
		<result property="link" column="link" jdbcType="varchar"/>
		<result property="version" column="version" jdbcType="varchar"/>
		<result property="timeAdd" column="time_add" jdbcType="datetime"/>
		<result property="timeEdit" column="time_edit" jdbcType="datetime"/>
		<result property="userAdd" column="user_add" jdbcType="varchar"/>
		<result property="userEdit" column="user_edit" jdbcType="varchar"/>
		<result property="remark" column="remark" jdbcType="varchar"/>
	</resultMap>
<select id="find" resultMap="BaseResultMap" parameterClass="com.shunwang.framework.ibatis.query.ConditionQuery">
	SELECT
		t.id,
		t.name,
		t.name_en,
		t.terminal,
		t.type,
		t.state,
		t.link,
		t.content,
		t.version,
		t.remark,
		t.time_add,
		t.time_edit,
		t.user_add,
		t.user_edit
	from config_rich_text t
	<isParameterPresent>
		<include refid="Example_Where_Clause"/>
	</isParameterPresent>
	order by
	<isNotNull property="orderCol">
		$orderCol$
	</isNotNull>
	<isNull property="orderCol">
		t.time_edit desc ,t.time_add desc
	</isNull>
	<isNotEqual property="rp" compareValue="0">
		limit #firstResult#, #rp#
	</isNotEqual>
</select>


<select id="findAll" resultMap="BaseResultMap">
	select
		id,
		name,
		name_en,
		terminal,
		type,
		state,
		link,
		content,
		version,
		remark,
		time_add,
		time_edit,
		user_add,
		user_edit
	from config_rich_text
</select>


<insert id="insert" parameterClass="com.shunwang.baseStone.richText.pojo.RichText">
	insert into config_rich_text (
			name,
			name_en,
			terminal,
			type,
			state,
			link,
			content,
			version,
			remark,
			time_add,
			time_edit,
			user_add,
			user_edit
		)values(
			#name:varchar#,
			#nameEn:varchar#,
			#terminal:varchar#,
			#type:varchar#,
			#state:int#,
			#link:varchar#,
			#content:text#,
			#version:text#,
			#remark:varchar#,
			#timeAdd:datetime#,
			#timeEdit:datetime#,
			#userAdd:varchar#,
			#userEdit:varchar#
		)
</insert>
<update id="update" parameterClass="com.shunwang.baseStone.richText.pojo.RichText">
	update config_rich_text set
			name = #name#,
			name_en = #nameEn#,
			terminal = #terminal#,
			type = #type#,
			state = #state#,
			link = #link#,
			content = #content#,
			version = #version#,
			remark = #remark#,
			time_add = #timeAdd#,
			time_edit = #timeEdit#,
			user_add = #userAdd#,
			user_edit = #userEdit#
		where id = #id:integer#
</update>

<delete id="delete" parameterClass="com.shunwang.baseStone.richText.pojo.RichText">
	delete from config_rich_text where id=#id:int#
</delete>
<select id="get" resultMap="BaseResultMap" parameterClass="java.lang.Integer">
	select
		id,
		name,
		name_en,
		terminal,
		type,
		state,
		link,
		content,
		version,
		remark,
		time_add,
		time_edit,
		user_add,
		user_edit
	from config_rich_text
	where id = #value#
</select>

<select id="getBasicById" resultMap="basicBaseResultMap" parameterClass="java.lang.Integer">
	select
		id,
		name,
		name_en,
		terminal,
		type,
		state,
		link,
		version,
		remark,
		time_add,
		time_edit,
		user_add,
		user_edit
	from config_rich_text
	where id = #value#
</select>

<select id="getByNameEnAndTerminal" resultMap="BaseResultMap" parameterClass="com.shunwang.baseStone.richText.pojo.RichText">
	select
		id,
		name,
		name_en,
		terminal,
		type,
		state,
		link,
		content,
		version,
		remark,
		time_add,
		time_edit,
		user_add,
		user_edit
	from config_rich_text
	where name_en = #nameEn#
	and terminal = #terminal#
</select>

<select id="findCnt" parameterClass="com.shunwang.framework.ibatis.query.ConditionQuery" resultClass="java.lang.Integer">
	select count(*) from config_rich_text t
	<include refid="Example_Where_Clause"/>
</select>
</sqlMap>