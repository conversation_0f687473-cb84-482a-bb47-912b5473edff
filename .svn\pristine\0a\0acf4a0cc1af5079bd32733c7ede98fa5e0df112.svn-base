package com.shunwang.baseStone.sysconfig.context;

import com.shunwang.baseStone.context.BaseStoneContext;
import com.shunwang.baseStone.sysconfig.dao.SysconfigDao;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Deprecated
public class SysConfigContext {

    private final static Logger log = LoggerFactory.getLogger(SysConfigContext.class);

    public final static String GT_EMAIL_CONTENT = "在$date$，1分钟内调极验验证码失败次数超$time$次，极验验证码总开关已关闭。";

    private static SysconfigDao getDao() {
        return (SysconfigDao) BaseStoneContext.getInstance().getBean("sysconfigDao");
    }


}
