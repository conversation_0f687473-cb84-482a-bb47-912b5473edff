<%@ page contentType="text/html;charset=UTF-8"%>
<%@ include file="/common/taglibs.jsp"%>

<table cellpadding="0" cellspacing="0">
    <tbody>
    <tr>
        <th class="w_lg"></th>
        <td>
            <span id="showInfo" class="form_error_bar">
                <c:if test="${!empty errorMsg}">
                    <img src="<c:url value='/images/front/error.gif'/>" /><font color="red">${errorMsg}</font>
                </c:if>

                <c:if test="${!empty msg}">${msg }
                    <img src="<c:url value='/images/front/error.gif'/>" /><font color="red">${msg}</font>
                </c:if>
            </span>
        </td>
    </tr>
    <tr>
        <th><span class="not_null">*</span> 姓名：</th>
        <td>
            <input name="appeal.realName" type="text" class="form_input" style="width:220px;" id="realName"   value="${appeal.realName}" maxlength="20" onkeyup="unableSpaceKey('realName');"/>
            <span class="form_tip" id="realNameMsg">中文，2~10个汉字</span>
        </td>
    </tr>
    <tr>
        <th><span class="not_null">*</span> 身份证：</th>
        <td>
            <input name="appeal.idCardNo" type="text" class="form_input" style="width:220px;" id="userIdCard"   value="${appeal.idCardNo}" maxlength="20" onkeyup="unableSpaceKey('userIdCard');"/>
            <span class="form_tip" id="userIdCardMsg">18位合法身份证号码</span>
        </td>
    </tr>
     <tr>
        <th>选择联系方式：</th>
        <td>
            <label>
               <input type="radio" name="choseWay" value="1" checked="checked" onclick="to_change(value);"/>通过手机短信
            </label>
            <label>
               <input type="radio" name="choseWay" value="0" onclick="to_change(value);"/>通过电子邮件
            </label>
            <span class="form_tip" id="choseWayMsg">申诉结果将通过您填写的联系方式通知到您</span>
       </td>
    </tr>
    <tr id="phoneNum">
        <th>手机号码：</th>
        <td>
            <input name="appeal.mobile" type="text" class="form_input" style="width:220px;" id="mobile"   value="${appeal.mobile}" maxlength="20" onkeyup="unableSpaceKey('mobile');"/>
            <span class="form_tip" id="mobileMsg"></span>
        </td>
    </tr>
    <tr id="emailAddress" style="display:none;">
        <th>邮箱地址：</th>
        <td>
            <input name="appeal.email" type="text"  class="form_input" style="width:220px;"  id="email"   value="${appeal.email}" maxlength="320" onkeyup="unableSpaceKey('email');"/>
            <span class="form_tip" id="emailMsg"></span>
        </td>
    </tr>
    <tr>
        <th></th>
        <td>
            <a href="###" onclick="return toSubmit();" class="btn_default_lg" >下一步</a>
        </td>
    </tr>
    </tbody>
</table>
<script type="text/javascript">
	appealInit();
	function unableSpaceKey(id){
        var obj = document.getElementById(id);
        var reg = /(\s)+/;
        if( reg.test(obj.value) )
            obj.value = obj.value.replace(reg, "");
	}
	function to_change(value) {
		if (value == 0) {
			$("#emailAddress").show();
			$("#mobile").val("");
			$("#phoneNum").hide();
		} else {
			$("#emailAddress").hide();
			$("#email").val("");
			$("#phoneNum").show();
		}
	}
</script>
