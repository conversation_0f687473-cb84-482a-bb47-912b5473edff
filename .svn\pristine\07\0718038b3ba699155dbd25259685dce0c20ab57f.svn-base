<%@ page contentType="text/html;charset=UTF-8"%>
<%@ include file="/common/taglibs.jsp"%>
<%
String path = request.getContextPath();
String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
		<meta name="Keywords" content="顺网通行证， 密保问题 ，安全 " />
<meta name="Description" content="设置密保问题，提高账户安全级别。" />
		
		<title>顺网通行证-安全中心-密保问题设置</title>
	</head>
	<body>
			<h3 class="h3_title posr">
				密保问题设置-设置
				<span class="posa"></span>
			</h3>
						<div id="errorMessagesShowSpan" class="fB col_f00" style="margin-left:360px;"><c:if test="${!empty errorMsg}">
							<img src="<c:url value='/images/front/error.gif'/>" /><font color="red">${errorMsg}</font>
						</c:if>	</div> 
			<div class="detail authentication wid795">

				<form class="passform mar100" method="post" action="front/memberQuestion/bindMemberQuestion_front.htm" onsubmit="return check();">
					<s:action namespace="/front/noLogin" flush="true"   name="getAllquestions_front" executeResult="true"></s:action>
					<p>
						<label>
							&nbsp;
						</label>
						<input  type="hidden"  id="positionOrg1" name="position1"  value="${position1 }" />
						<input  type="hidden"  id="positionOrg2" name="position2"  value="${position2 }" />
						<input type="hidden" name="indexs" value="${indexs}" />
						<input type="submit" class="sure_enter marT" onmouseover="this.className='sure_enter_hover marT'" onmouseout="this.className='sure_enter marT'" value="确 定"/>
						&nbsp;
						<input type="button" class="sure_enter marT" onmouseover="this.className='sure_enter_hover marT'" onmouseout="this.className='sure_enter marT'" value="取 消"  onclick="location.href='<c:url value='front/memberQuestion/toMemberQuestion_front.htm'/>'" />
					</p>
				</form>
				<br />

		</div>
		<div class="clear"></div>
				<script type="text/javascript">

  function doClickToCookie(){
  	
  	var iKnow = document.getElementById("iKnow");
  	if(iKnow.checked){
  	    cookie.Set("noaccountAlert", "Iknow", 30, "/");
  	}
  	
  	document.getElementById('noaccountAlert').style.display = 'none';
  }
</script>
</html>