package com.shunwang.passport.member.web;

import com.shunwang.baseStone.core.exception.BaseStoneException;
import com.shunwang.basepassport.binder.common.ErrorCode;
import com.shunwang.basepassport.context.UserContext;
import com.shunwang.basepassport.detail.common.DetailContants;
import com.shunwang.basepassport.user.common.MemberConstants;
import com.shunwang.basepassport.user.pojo.MemberAccountBind;
import com.shunwang.passport.common.context.DomainContext;
import org.apache.struts2.ServletActionContext;

import java.io.IOException;
import java.net.URLEncoder;
import java.util.UUID;

/**
 * User:pf.ma
 * Date:2018/06/13
 * Time:13:46
 */
public class WeixinBindManageAction extends BindManageAction {

	private static final String WEIXIN_OAUTH2_URL = "https://open.weixin.qq.com/connect/oauth2/authorize";
	private static final String WEIXIN_OAUTH2_QR_URL = "https://open.weixin.qq.com/connect/qrconnect";
	public static final String WEIXIN_WEB_QR_APPID = "wxcef14f86471120d9";

	@Override
	public void toBind() throws IOException {
		String userAgent = ServletActionContext.getRequest().getHeader("User-Agent");
		StringBuilder sb = new StringBuilder();
		String appid = "" ;
		String scope = "";
		if(userAgent == null) {//当pc端处理
			scope = "snsapi_login";
			appid = WEIXIN_WEB_QR_APPID;
			sb.append(WEIXIN_OAUTH2_QR_URL);
		}else if(userAgent.contains("MicroMessenger")) { //微信内嵌浏览器打开
			scope = "snsapi_userinfo";
			sb.append(WEIXIN_OAUTH2_URL);

		} else if(!userAgent.contains("Mobile")){//PC端
			scope = "snsapi_login";
			appid = WEIXIN_WEB_QR_APPID;
			sb.append(WEIXIN_OAUTH2_QR_URL);
		} else {//其他情况也默认为PC端
			scope = "snsapi_login";
			appid = WEIXIN_WEB_QR_APPID;
			sb.append(WEIXIN_OAUTH2_QR_URL);
		}
		sb.append("?");
		sb.append("appid=").append(appid).append("&");
		// 回调地址需要是SSO
		String redirectUrl = DomainContext.getSSO_Server()+"/oauth/nologinAuth.do?visitType=bind&site_id="+ MemberConstants.REG_FROM+"&callbackUrl="+ URLEncoder.encode(DomainContext.getAppServer()+"/front/wxBindManage/callback.htm", "UTF-8") ;
		sb.append("redirect_uri=").append(URLEncoder.encode(redirectUrl, "UTF-8")).append("&");
		sb.append("response_type=").append("code").append("&");
		sb.append("scope=").append(scope).append("&");
		sb.append("state=").append(UUID.randomUUID()).append("&");
		sb.append("#wechat_redirect");
		getResponse().sendRedirect(sb.toString()) ;
	}

	public void doUnbind(MemberAccountBind memberAccountBind){
		if(memberAccountBind.getWeixin().equals(memberAccountBind.getMemberId())){
			logger.info("微信账号[memberId:{}]是主体账号，不允许解绑",memberAccountBind.getMemberId()) ;
			throw new BaseStoneException(ErrorCode.C_1090.getCode(),ErrorCode.C_1090.getDescription());
		}
		memberAccountBind.beginBuildLog("微信账号解绑");
		memberAccountBind.getPersonalEditLog().setMember(UserContext.getMember()) ;
		memberAccountBind.getPersonalEditLog().setUserAdd(UserContext.getUserName());
		memberAccountBind.getPersonalEditLog().setType(DetailContants.FRONT);
		memberAccountBind.setWeixin(null); ;
		getMemberAccountBindDao().unbindWeixin(memberAccountBind) ;
	}

	public String callback(){
		if(!validateSign()){
			setErrorMsg("系统异常，请稍后重试");
			return SUCCESS ;
		}
		MemberAccountBind accountBind = getMemberAccountBindDao().getByWeixin(unionId) ;
		if(null != accountBind){
			if(!accountBind.getMemberId().equals(UserContext.getUserId())){
				setErrorMsg("该微信账号已经绑定其他账号") ;
				return SUCCESS ;
			}
			return SUCCESS ;
		}
		// 开始绑定
		accountBind = getMemberAccountBindDao().getByMemberId(UserContext.getUserId()) ;
		accountBind.beginBuildLog("微信账号绑定");
		accountBind.getPersonalEditLog().setMember(UserContext.getMember()) ;
		accountBind.getPersonalEditLog().setUserAdd(UserContext.getUserName());
		accountBind.getPersonalEditLog().setType(DetailContants.FRONT);
		if(null != unionId && unionId.equals(accountBind.getWeixin())){
			return SUCCESS ;
		}
		accountBind.setWeixin(unionId) ;
		bind(accountBind) ;
		return SUCCESS ;
	}
}
