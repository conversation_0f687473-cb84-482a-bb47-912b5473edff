package com.shunwang.baseStone.sso.apapter;

import com.shunwang.baseStone.bussiness.dao.BussinessDao;
import com.shunwang.baseStone.bussiness.pojo.Bussiness;
import com.shunwang.baseStone.context.BaseStoneContext;
import com.shunwang.baseStone.context.IPContext;
import com.shunwang.baseStone.siteinterface.context.SiteContext;
import com.shunwang.baseStone.sso.constant.UserOutsiteConstant;
import com.shunwang.baseStone.sso.constant.UserTockenConstant;
import com.shunwang.baseStone.sso.exception.QQServersideException;
import com.shunwang.baseStone.sso.pojo.ClientInfo;
import com.shunwang.baseStone.sso.util.ClientInfoBuilder;
import com.shunwang.baseStone.useroutinterface.context.UseroutInterfaceContext;
import com.shunwang.baseStone.config.constants.ConfigOauthConstant;
import com.shunwang.basepassport.config.pojo.Oauth;
import com.shunwang.basepassport.user.common.MemberConstants;
import com.shunwang.basepassport.user.common.MemberUtil;
import com.shunwang.basepassport.user.common.UserLoginSessionUtil;
import com.shunwang.basepassport.user.dao.MemberDao;
import com.shunwang.basepassport.user.pojo.Member;
import com.shunwang.basepassport.user.pojo.MemberAccountBind;
import com.shunwang.basepassport.user.pojo.MemberOutSite;
import com.shunwang.basepassport.weixin.constant.WeixinConstant;
import com.shunwang.util.date.DateUtil;
import com.shunwang.util.encrypt.Md5Encrypt;
import com.shunwang.util.lang.StringUtil;
import com.shunwang.util.math.RandomUtil;
import com.shunwang.util.net.HttpClientUtils;
import com.shunwang.util.net.IpUtil;
import org.apache.struts2.ServletActionContext;
import org.jdom.Document;
import org.jdom.Element;
import org.jdom.JDOMException;
import org.jdom.input.SAXBuilder;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.StringReader;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;
import java.util.UUID;

/**
 * @Described: QQ用户登录适配器
 * <AUTHOR> create at 2013-05-21
 *
 * 接入文档参见 http://wiki.open.qq.com/wiki/website/%E7%BD%91%E7%AB%99%E6%8E%A5%E5%85%A5%E6%B5%81%E7%A8%8B
 *
 */
public class QqAdapter extends UserOutsiteApapter {

    private final static Logger log = LoggerFactory.getLogger(QqAdapter.class);
    private static final long serialVersionUID = -2254178647806419652L;

    private String accessToken;
    private String unionid;
    private String openid;
    private String qqNickName;
    private String directLogin;

    private String passportUserName;
    private String passportUserPwd;

    private String siteName;

    private String checkCode = "";
    private String outMemberId;
    private String getTicketWithoutLoginUrl;
    private String getTicketWithoutLoginMd5Key;
    private String siteId;//访问interface时使用的siteid
    /**
     * 云海登录做不同处理,值为字符串形式的"true"或"false"
     */
    private String yhLogin;
    private ClientInfo clientInfo = new ClientInfo();   //返回给客户端的数据

    @Override
    public String getInterfaceId() {
        return UserOutsiteConstant.QQ_INTERFACE_ID;
    }

    /**
     * 云海专用的
     */
    @Override
    public String goToOauthYh() {
        this.yhLogin = "true";
        return goToOauth();
    }

    @Override
    public Map<String, Object> getOauthByServer() throws Exception {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("oauthUrl", goToOauth());
        return resultMap;
    }

    @Override
    public String goToOauth() {
        Oauth configOauth = getOauthConfig() ;
        if(null == configOauth){
            log.info("第三方配置获取出现异常,[siteId:{}]", site_id) ;
            throw new RuntimeException("获取第三方配置异常") ;
        }
        //以下redirectUri是QQ回调SSO的地址
        //callbackUrl是SSO登录成功后,回调应用(如passport)的地址
		useroutInterface=UseroutInterfaceContext.getUseroutInterfaceById(getInterfaceId());
        String userSceneKey = com.shunwang.util.StringUtil.isBlank(innerScene) ? buildCacheUserKey() : innerScene;
        cacheExtData(userSceneKey, 30 * 60);
        String redirectUri = useroutInterface.getCallbackURL()
            + "?site_id=" + site_id
            + "&yhLogin=" + yhLogin
            + "&needCode=" + needCode
            + "&userSceneKey=" + userSceneKey
            + "&cacheResult=" + (isMultiTerminalLoginJump() ? "1" : "0")
            + "&tgt=" + (tgt==null?"":tgt)
            + (StringUtil.isNotBlank(callbackUrl) ? "&callbackUrl=" + callbackUrl : "");

        try {
            redirectUri = URLEncoder.encode(redirectUri, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            log.info("goToOauth生成重定向url时产生非法编码错误");
        }

        //如果云海用户登录,则需要用https协议
        if (yhLogin != null && yhLogin.equals("true") && !redirectUri.contains("https")) {
            redirectUri = redirectUri.replaceFirst("http", "https");
        }

        serviceUrl = useroutInterface.getServiceURL();
        serviceUrl = serviceUrl
                + (serviceUrl.contains("?") ? "&" : "?")
                + "client_id=" + configOauth.getAppId() + "&"
                + "state=" + UUID.randomUUID() + "&"
                + "redirect_uri=" + redirectUri;

        return serviceUrl;

    }

    private void fetchOpenIdAndUnionId(Oauth configOauth) throws Exception {
        String redirectUri = useroutInterface.getCallbackURL()
                + "?site_id=" + site_id
                + (StringUtil.isNotBlank(callbackUrl) ? "&callbackUrl=" + callbackUrl : "");

        String code = this.getRequest().getParameter("code");
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("grant_type", "authorization_code");
        paramMap.put("client_id", configOauth.getAppId());
        paramMap.put("client_secret", configOauth.getAppSecret());
        paramMap.put("code", code);
        paramMap.put("state", UUID.randomUUID().toString());

        URLEncoder.encode(redirectUri, "UTF-8");
        paramMap.put("redirect_uri", "fake.kedou.com");

        //step 1, return "access_token=YOUR_ACCESS_TOKEN&expires_in=3600"
        String getAccessTokenUrl="https://graph.qq.com/oauth2.0/token";
        String result = HttpClientUtils.doPost(getAccessTokenUrl, paramMap);
        log.info("QQaccessToken返回数据:" + result + ",code," + code);
        if(StringUtil.isBlank(result) || result.contains("error")) {
            throw new QQServersideException(StringUtil.isBlank(result) ? "请求QQ服务网络异常" : result);
        }
        int startIndex = result.indexOf("access_token");
        int endIndex = result.indexOf("&");
        if (endIndex <0 ) {
            endIndex = result.length();
        }
        accessToken = result.substring(startIndex, endIndex).split("=")[1];
        //step 2, return callback( {"client_id":"YOUR_APPID","openid":"YOUR_OPENID"} ); 
        String getUnionUrl = "https://graph.qq.com/oauth2.0/me?access_token=" +
                accessToken + "&unionid=1";
        paramMap = new HashMap<>();
        paramMap.put("access_token", accessToken);
        result = HttpClientUtils.doGet(getUnionUrl, null);
        log.info("QQunion返回数据:" + result + ",accessToken," + accessToken);
        if(StringUtil.isBlank(result)) {
            throw new QQServersideException("请求QQunion服务网络异常");
        }
        int jsonStart = result.indexOf("{");
        int jsonEnd = result.indexOf("}");
        String jsonObjStr = result.substring(jsonStart, jsonEnd + 1);
        JSONObject jsonObject = new JSONObject(jsonObjStr);

        unionid = jsonObject.getString("unionid");
        openid = jsonObject.getString("openid");
    }

    @Override
    public String oauthCallback() {
        pullCacheExtData(userSceneKey);
        //用来记录异常时的上下文信息
        HashMap<String, String> executContext = new HashMap<>();
        executContext.put("site_id", site_id);
        executContext.put("callbackUrl", callbackUrl);
        executContext.put("yhLogin", yhLogin);

        loadPrivacyCss();
        initContextInfo();

        // 读取第三方配置
        Oauth configOauth = getOauthConfig() ;
        if(null == configOauth){
            log.info("第三方配置获取出现异常,[siteId:{}]", site_id) ;
            throw new RuntimeException("获取第三方配置异常") ;
        }
        try {
            //读取QQ接口的openId
            fetchOpenIdAndUnionId(configOauth);
            memberOutSite = new MemberOutSite();

            //云海特殊字段 开始
            memberOutSite.setVersion(version);
            memberOutSite.setEnv(env);
            memberOutSite.setRemark(extData);
            //云海特殊字段 结束　

            memberOutSite.setOutMemberId(openid);
            memberOutSite.setMemberFrom(UserOutsiteConstant.QQ_INTERFACE_ID);
            member = queryMemberBy(memberOutSite.getOutMemberId());

            //如果没有创建过用户, 则创建
            if (member == null) {
                memberOutSite.setOutMemberId(unionid);
                memberOutSite.setMemberFrom(UserOutsiteConstant.QQ_INTERFACE_ID);
                member = queryMemberBy(memberOutSite.getOutMemberId());
                if (member == null) {
                    Object[] t = buildFromQQUserInfo(configOauth);
                    memberOutSite = (MemberOutSite) t[0];
                    String qqHeadUrl = (String) t[1];
                    member = bindNewUser(qqHeadUrl);
                    qqNickName = memberOutSite.getNickName();
                    outMemberId = memberOutSite.getOutMemberId();
                } else if (member.getMemberState() == MemberConstants.USER_CANCEL_STATE) {
                    setMsg("您的账号已被注销！");
                    memberName = member.getMemberName();
                    return inputCancelView();
                } else if (StringUtil.isBlank(member.getHeadImg())) {//根据运营需求，头像为空，要去获取QQ头像
                    saveHeadImage(member, configOauth);
                }
            } else if (member.getMemberState() == MemberConstants.USER_CANCEL_STATE) {
                setMsg("您的账号已被注销！");
                memberName = member.getMemberName();
                return inputCancelView();
            } else { //将outMemberId更新为unionId
                if (StringUtil.isBlank(member.getHeadImg())) {//根据运营需求，头像为空，要去获取QQ头像
                    saveHeadImage(member, configOauth);
                }
                memberOutSite.setNewOutMemberId(unionid);
                memberOutSite.setOldOutMemberId(openid);
                interfaceService.updateMemberOutSite(memberOutSite);
            }
            member.setOpenId(openid);
            //处理登录来源
            //获取请求二维码的原始客户端ip到登录日志
            member.setClientIp(clientIp);
            //处理登录来源
            if (com.shunwang.util.StringUtil.isNotBlank(cacheResult) && cacheResult.equals("1")) {
                memberOutSite.setMemberFrom(UserOutsiteConstant.QQ_QR_INTERFACE_ID);
            }
            MemberUtil.doLogin(member, memberOutSite);
            initCss(getSite_id());
            if (StringUtil.isNotBlank(visitType) && visitType.equals("bind")) {
                responseVisitType();
                return SUCCESS;
            }
            UserLoginSessionUtil.saveSession(member.getMemberName(), UserLoginSessionUtil.LoginType.SSO_OUT.getType() + getInterfaceId(), null, site_id);
            initLoginElement(super.getSite_id());
            if (isSingleAccount) {
                String toBind = goSingleBind(memberOutSite.getNickName(), memberOutSite.getHeadImg());
                if (!NO_RETURN.equalsIgnoreCase(toBind)) {
                    return toBind;
                }
            }

            login(member);

            if (yhLogin != null && yhLogin.equals("true")) {
                clientInfo = ClientInfoBuilder.setSSoLoginSuccInfo(member, "FALSE",
                        this.getTicket(), this.getTockenId(), UserTockenConstant.MD5KEY_CLIENT_YUNHAI);
                clientInfo.setSiteId(site_id);
                this.setClientInfo(clientInfo);
                return "yhSuccess";
            }
            return WeixinConstant.MULTI_TERMINAL_LOGIN_JUMP.equals(cacheResult) ? qrSuccess() : SUCCESS;

        } catch (QQServersideException e) {
            log.warn("\n\n处理QQ回调异常,上下文信息为" + executContext + "\n");
            setMsg("QQ返回数据存在异常");
            try {
                getResponse().sendRedirect(callbackUrl);
            } catch (IOException e1) {
                log.error("跳转[{}]异常", callbackUrl, e);
            }
            return INPUT;
        } catch (Exception e) {
            log.warn("\n\n处理QQ回调异常,上下文信息为" + executContext + "\n");
            proessErrorLog(e);
            setMsg("QQ返回数据存在异常");
            return INPUT;
        }
    }

    @Override
    protected MemberAccountBind getByMemberId() {
        return memberAccountBindDao.getByQq(member.getMemberId());
    }

    @Override
    protected Integer getOutOauthType() {
        return ConfigOauthConstant.TYPE.QQ.getInt();
    }

    @Override
    protected String getOutOauthLogName() {
        return "QQ";
    }

    @Override
    protected MemberAccountBind buildMemberAccountBind() {
        MemberAccountBind memberAccountBind = super.buildMemberAccountBind();
        memberAccountBind.beginBuildLog("QQ帐号绑定");
        memberAccountBind.setQq(member.getMemberId());
        return memberAccountBind;
    }

    private void initContextInfo() {
        //初始 ip context 和 siteContext
        IPContext.setIp(IpUtil.getIpAddress(ServletActionContext.getRequest()));
        SiteContext.setSiteId(site_id);

        //初始化 siteName
        BussinessDao bussinessDao = (BussinessDao) BaseStoneContext.getInstance().getBean("bussinessDao");
        Bussiness buss = bussinessDao.getById(site_id);
        siteName = (null == buss) ? "非法数据" : buss.getBussinessname();
    }


    private String postGetQQUserInfo(Oauth configOauth) {
        String getUserInfoUrl = "https://graph.qq.com/user/get_user_info";
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("access_token", accessToken);
        paramMap.put("openid", openid);
        paramMap.put("oauth_consumer_key", configOauth.getAppId());
        String result = HttpClientUtils.doPost(getUserInfoUrl, paramMap);
        log.debug("请求QQ用户的信息返回的原始数据:"+result);
        return result;
    }

    private Object[] buildFromQQUserInfo(Oauth configOauth) throws Exception {

        String result = postGetQQUserInfo(configOauth);

        JSONObject jsonObject = new JSONObject(result);

        //figureurl_qq_1 	大小为40×40像素的QQ头像URL。
        //figureurl_qq_2 	大小为100×100像素的QQ头像URL。需要注意，不是所有的用户都拥有QQ的100x100的头像，但40x40像素则是一定会有。 
        //为了保存取到真实头像, 只使用40*40像素头像
        String qqHeadUrl = jsonObject.getString("figureurl_qq_1");


        MemberOutSite memberOutSite = new MemberOutSite();
        memberOutSite.setOutMemberId(unionid);
        memberOutSite.setMemberName(genMemberName());
        memberOutSite.setOutMemberName(memberOutSite.getMemberName());

        memberOutSite.setNickName("qq_" + unionid.substring(32));
        memberOutSite.setRegFrom(site_id);
        memberOutSite.setMemberFrom(UserOutsiteConstant.QQ_INTERFACE_ID);

        //云海特殊字段 开始
        memberOutSite.setVersion(version);
        memberOutSite.setEnv(env);
        memberOutSite.setRemark(extData);
        //云海特殊字段 结束　

        return new Object[]{memberOutSite, qqHeadUrl};
    }

    private Member bindNewUser(String qqHeadUrl) {
        //因为QQ的头像地址换头像地址不变，直接存地址
        memberOutSite.setHeadImg(qqHeadUrl);
        member = memberOutSite.getDao().getByOutMemberId(memberOutSite.getOutMemberId());
        if (null != member) {
            member.setLoginType(MemberConstants.LOGIN_TYPE_ACCOUNT);
            member.setVrsion(StringUtil.isNotBlank(memberOutSite.getVersion()) ? memberOutSite.getVersion() : "");
            member.setEnv(memberOutSite.getEnv());
            member.setExtData(memberOutSite.getRemark());
            member.loginWithNoPwd();
        }

        member = outSiteMemberRegister(memberOutSite);
        if (member == null) {
            throw new RuntimeException("QQ用户内部注册异常") ;
        }
        //外部用户登录方式为 外部用户的来源,如QQ,4008等
        member.setVrsion(version);
        member.setEnv(env);
        member.setExtData(extData);
        member.setLoginType(UserOutsiteConstant.QQ_INTERFACE_ID);
        member.loginWithNoPwd();
        member.setMemberName(memberOutSite.getMemberName());

        return member;
    }

    private void saveHeadImage(Member member, Oauth configOauth) throws Exception {
        String result = postGetQQUserInfo(configOauth);
        JSONObject jsonObject = new JSONObject(result);
        //figureurl_qq_1 	大小为40×40像素的QQ头像URL。
        //figureurl_qq_2 	大小为100×100像素的QQ头像URL。需要注意，不是所有的用户都拥有QQ的100x100的头像，但40x40像素则是一定会有。
        //为了保存取到真实头像, 只使用40*40像素头像
        String qqHeadUrl = jsonObject.getString("figureurl_qq_1");
        member.setHeadImg(qqHeadUrl);
        interfaceService.updateMember(member);
    }

    @Override
    public String getFmtCallback(){
        if(StringUtil.isBlank(callbackUrl)) {
            return null;
        }
        String split = "?";
        if (callbackUrl.contains("?")) {
            split = "&";
        }
        StringBuilder sb = new StringBuilder();
        sb.append(callbackUrl);
        if ("1".equals(needCode)) {
            String result;
            try {
                result = HttpClientUtils.doPost(getGetTicketWithoutLoginUrl(),buildParams(member.getMemberId()));
                String code = parseResult(result);
                if(StringUtil.isNotBlank(code)) {
                    sb.append(split).append("msg=succ&code=").append(code);
                } else {
                    sb.append(split).append("msg=").append(getErrorMsg());
                }
                split = "&";
            } catch (Exception e) {
                log.error("获取app免登code失败", e);
            }

        }

        sb.append(split);
        if (ticket != null) {
            sb.append("ticketId=").append(ticket);
        }

        if (tockenId != null) {
            sb.append("&tockenId=").append(tockenId);
        }
        sb.append("&version=").append(version).append("&from=qq");
        return sb.toString();
    }

    private String parseResult(String xmlText) {
        String code = "";
        SAXBuilder builder = new SAXBuilder();
        StringReader strReader = new StringReader(xmlText);
        try {
            Document document = builder.build(strReader);
            Element rootNode = document.getRootElement();
            String msgId = ((Element) (rootNode.getChildren("Result").get(0))).getAttributeValue("msgId");
            setErrorMsg(((Element) (rootNode.getChildren("Result").get(0))).getAttributeValue("msg"));
            if ("0".equals(msgId))
                code = ((Element) (rootNode.getChildren("Result").get(0))).getChildText("ticket");
        } catch (IOException | JDOMException e) {
            log.error("解析消息出错", e);
        }
        return code;
    }

    private Map<String, String> buildParams(Integer memberId) {
        String time = DateUtil.getCurrentDateStamp();
        Map<String, String> paramMap = new TreeMap<>();
        paramMap.put("memberId", String.valueOf(memberId));
        paramMap.put("siteId", getSiteId());
        paramMap.put("time", time);
        paramMap.put("sign", buildSign(paramMap));
        return paramMap;
    }

    private String buildSign(Map<String, String> paramMap) {
        StringBuilder stringBuffer = new StringBuilder();
        for (String keySet : paramMap.keySet()) {
            stringBuffer.append(paramMap.get(keySet)).append("|");
        }
        stringBuffer.append(getGetTicketWithoutLoginMd5Key());
        String sign = "";
        try {
            sign = URLEncoder.encode(stringBuffer.toString(), "utf-8").toUpperCase();
        } catch (UnsupportedEncodingException e) {
            log.error("对签名转码错误", e);
        }
        sign = Md5Encrypt.encrypt(sign).toUpperCase();
        if (log.isInfoEnabled())
            log.info("getTicketWithoutLogin:" + stringBuffer + ",sign：" + sign);
        return sign;
    }

    protected String genMemberName() {
        MemberDao memberDao = (MemberDao)BaseStoneContext.getInstance().getBean("memberDao");
        String memberName;
        int i = 0;
        do {
            memberName = generalMemberName();
            if (memberDao.getByName(memberName) != null) {
                i++;
            } else {
                i = 4;
            }
        } while (i < 4);
        return memberName;
    }

    private String generalMemberName() {
        return "qq_" + unionid.substring(unionid.length() - 13) + RandomUtil.getRandomStr(6);
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }

    public String getAccessToken() {
        return this.accessToken;
    }

    public void setOpenid(String openid) {
        this.openid = openid;
    }

    public String getOpenid() {
        return this.openid;
    }

    public void setQqNickName(String qqNickName) {
        this.qqNickName = qqNickName;
    }

    public String getQqNickName() {
        return this.qqNickName;
    }

    public void setDirectLogin(String directLogin) {
        this.directLogin = directLogin;
    }

    public String getDirectLogin() {
        return this.directLogin;
    }

    public void setPassportUserName(String passportUserName) {
        this.passportUserName = passportUserName;
    }

    public String getPassportUserName() {
        return this.passportUserName;
    }

    public void setPassportUserPwd(String passportUserPwd) {
        this.passportUserPwd = passportUserPwd;
    }

    public String getPassportUserPwd() {
        return this.passportUserPwd;
    }

    public void setSiteName(String siteName) {
        this.siteName = siteName;
    }

    public String getSiteName() {
        return this.siteName;
    }

    public void setYhLogin(String yhLogin) {
        this.yhLogin = yhLogin;
    }

    public String getYhLogin() {
        return this.yhLogin;
    }

    public void setClientInfo(ClientInfo clientInfo) {
        this.clientInfo = clientInfo;
    }

    public ClientInfo getClientInfo() {
        return this.clientInfo;
    }

    public void setCheckCode(String checkCode) {
        this.checkCode = checkCode;
    }

    public String getCheckCode() {
        return this.checkCode;
    }

    public void setOutMemberId(String outMemberId) {
        this.outMemberId = outMemberId;
    }

    public String getOutMemberId() {
        return this.outMemberId;
    }

    public String getGetTicketWithoutLoginMd5Key() {
        return getTicketWithoutLoginMd5Key;
    }

    public void setGetTicketWithoutLoginMd5Key(String getTicketWithoutLoginMd5Key) {
        this.getTicketWithoutLoginMd5Key = getTicketWithoutLoginMd5Key;
    }

    public String getGetTicketWithoutLoginUrl() {
        return getTicketWithoutLoginUrl;
    }

    public void setGetTicketWithoutLoginUrl(String getTicketWithoutLoginUrl) {
        this.getTicketWithoutLoginUrl = getTicketWithoutLoginUrl;
    }

    public String getSiteId() {
        return siteId;
    }

    public void setSiteId(String siteId) {
        this.siteId = siteId;
    }

}