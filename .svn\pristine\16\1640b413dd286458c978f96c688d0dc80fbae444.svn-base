package com.shunwang.basepassport.user.exception;

import com.shunwang.baseStone.core.exception.BaseStoneException;
/**
 * @Description:会员不存在异常
 * <AUTHOR>  create at 2011-7-25 下午02:46:22
 * @FileName com.shunwang.basepassport.user.exception.MemberNotFoundExp.java
 */
public class MsgNotFoundExp extends BaseStoneException {

	/**
	 * <AUTHOR> create at 2011-7-25 下午02:46:19 
	 */
	private static final long serialVersionUID = -7912153128330270598L;
	
	public MsgNotFoundExp(String name) {
		super("1006", name+"不存在！");
	}
}
