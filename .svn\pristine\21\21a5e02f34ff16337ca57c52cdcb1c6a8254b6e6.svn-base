package com.shunwang.basepassport.filesystem.service;


import com.shunwang.baseStone.sysconfig.context.SysConfigContext;
import com.shunwang.basepassport.filesystem.client.FileClient;
import com.shunwang.basepassport.filesystem.client.FileEncryptionClient;
import com.shunwang.basepassport.filesystem.dto.FileOptions;
import com.shunwang.basepassport.filesystem.exception.FileUploadException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;

public class FileServiceImpl implements FileService {

    private final static Logger log = LoggerFactory.getLogger(FileServiceImpl.class);

    private volatile FileClient fileClient;

    private String encryptKey;


    @Override
    public String uploadFile(String path, MultipartFile[] files) {
        for (MultipartFile file : files) {
            try (InputStream in = file.getInputStream()) {
                getFileClient().putObject(path, in);
            } catch (IOException e) {
                log.error("文件写入异常", e);
                throw new FileUploadException();
            }
        }
        return path;
    }

    @Override
    public String uploadFile(String path, File file) {
        try (FileInputStream in = new FileInputStream(file)) {
            getFileClient().putObject(path, in);
        } catch (IOException e) {
            log.error("文件写入异常", e);
            throw new FileUploadException();
        }
        return path;
    }

    @Override
    public boolean deleteFile(String path) {
        log.info(path);
        return false;
    }

    @Override
    public boolean isExistFile(String path) {
        return getFileClient().doesObjectExist(path);
    }

    @Override
    public InputStream readFile(String path) {
        return getFileClient().getObject(path);
    }

    public FileClient getFileClient() {
        if (fileClient == null) {
            //图片文件根目录
            String directoryRootPath = SysConfigContext.getEncryptImgDir();
            log.info("加密图片目录[{}]", directoryRootPath);

            //Objects.requireNonNull(directoryRootPath, "加密图片目录不能为空");
            FileOptions fileOptions = new FileOptions();
            fileOptions.setRootPath(directoryRootPath);
            fileOptions.setEncryptKey(encryptKey);

            fileClient = new FileEncryptionClient();
            fileClient.setOptions(fileOptions);
        }
        return fileClient;
    }

    public String getEncryptKey() {
        return encryptKey;
    }

    public void setEncryptKey(String encryptKey) {
        this.encryptKey = encryptKey;
    }
}
