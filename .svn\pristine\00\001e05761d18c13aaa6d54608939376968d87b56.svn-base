<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd" >
<sqlMap namespace="com.shunwang.basepassport.config.pojo.WxTemplateMsg" >
<typeAlias alias="wxTemplateMsg" type="com.shunwang.basepassport.config.pojo.WxTemplateMsg"/>
<resultMap id="wxTemplateMsgMap" class="com.shunwang.basepassport.config.pojo.WxTemplateMsg" >
    <result property="id" column="id" jdbcType="INTEGER"/>
    <result property="type" column="type" jdbcType="VARCHAR"/>
    <result property="appid" column="appid" jdbcType="VARCHAR"/>
    <result property="templateId" column="template_id" jdbcType="VARCHAR"/>
    <result property="name" column="name" jdbcType="VARCHAR"/>
    <result property="state" column="state" jdbcType="VARCHAR"/>
    <result property="delayedTime" column="delayed_time" jdbcType="INTEGER"/>
    <result property="explain" column="explain" jdbcType="VARCHAR"/>
    <result property="url" column="url" jdbcType="VARCHAR"/>
    <result property="first" column="first" jdbcType="VARCHAR"/>
    <result property="firstColor" column="first_color" jdbcType="VARCHAR"/>
    <result property="keyword1" column="keyword1" jdbcType="VARCHAR"/>
    <result property="keyword1Color" column="keyword1_color" jdbcType="VARCHAR"/>
    <result property="keyword2" column="keyword2" jdbcType="VARCHAR"/>
    <result property="keyword2Color" column="keyword2_color" jdbcType="VARCHAR"/>
    <result property="keyword3" column="keyword3" jdbcType="VARCHAR"/>
    <result property="keyword3Color" column="keyword3_color" jdbcType="VARCHAR"/>
    <result property="keyword4" column="keyword4" jdbcType="VARCHAR"/>
    <result property="keyword4Color" column="keyword4_color" jdbcType="VARCHAR"/>
    <result property="keyword5" column="keyword5" jdbcType="VARCHAR"/>
    <result property="keyword5Color" column="keyword5_color" jdbcType="VARCHAR"/>
    <result property="remark" column="remark" jdbcType="VARCHAR"/>
    <result property="remarkColor" column="remark_color" jdbcType="VARCHAR"/>
    <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
    <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
    <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    <result property="partner" column="partner" jdbcType="VARCHAR"/>
    <result property="miniprogramAppid" column="miniprogram_appid" jdbcType="VARCHAR"/>
    <result property="miniprogramPagepath" column="miniprogram_pagepath" jdbcType="VARCHAR"/>
    <result property="noticeType" column="notice_type" jdbcType="VARCHAR"/>
</resultMap>
    <sql id="baseColumn">
        id,
        type,
        appid,
        template_id,
        name,
        state,
        delayed_time,
        `explain`,
        url,
        first,
        first_color,
        keyword1,
        keyword1_color,
        keyword2,
        keyword2_color,
        keyword3,
        keyword3_color,
        keyword4,
        keyword4_color,
        keyword5,
        keyword5_color,
        remark,
        remark_color,
        create_user,
        create_time,
        update_user,
        update_time,
        partner,
        miniprogram_appid,
        miniprogram_pagepath,
        notice_type
    </sql>

<!-- 查询 -->
<select id="getByType" resultMap="wxTemplateMsgMap">
    SELECT *
    FROM config_wx_template_msg t
    WHERE t.state = 1 and t.type = #type# limit 0,1
</select>
    <select id="get" resultMap="wxTemplateMsgMap">
        SELECT
        <include refid="baseColumn"/>
        FROM config_wx_template_msg
        WHERE  id = #id#
    </select>

    <!-- 查询 -->
    <select id="getByTypeAndPartner" resultMap="wxTemplateMsgMap">
        SELECT
            <include refid="baseColumn"/>
        FROM config_wx_template_msg
        WHERE state = 1
          and type = #type#
          and partner = #partner#
        order by id
        limit 0,1
    </select>

</sqlMap>
