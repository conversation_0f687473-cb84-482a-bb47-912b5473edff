package com.shunwang.baseStone.listener;

import com.shunwang.baseStone.context.BaseStoneContext;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.web.context.ContextLoaderListener;
import org.springframework.web.context.support.WebApplicationContextUtils;

import java.io.InputStream;
import java.util.Properties;

import javax.servlet.ServletContext;
import javax.servlet.ServletContextEvent;

/**
 * 根据当前应用加载spring上下文
 *
 * <AUTHOR>
 * @since 2016-01-06
 */
public class ApplicationContextInitListener extends ContextLoaderListener {

    protected final Logger LOGGER = LoggerFactory.getLogger(getClass());

    protected ServletContext servletContext;
    protected ApplicationContext ctx = null;

    @Override
    public void contextInitialized(ServletContextEvent event) {
        super.contextInitialized(event);
        servletContext = event.getServletContext();
        ctx = WebApplicationContextUtils.getRequiredWebApplicationContext(servletContext);
        initApplication();
        printVersion(servletContext);
    }

    @Override
    public void contextDestroyed(ServletContextEvent event) {
        super.contextDestroyed(event);
        servletContext = null;
    }

    protected void initApplication() {
        LOGGER.info("系统初始化开始......");
        LOGGER.debug("设置BaseStoneContext的spring上下文");
        BaseStoneContext.getInstance().setSpringContext(ctx);
    }

    private void printVersion(ServletContext application) {
        try {
            InputStream in = application.getResourceAsStream("/META-INF/MANIFEST.MF");
            if (in != null) {
                Properties manifest = new Properties();
                manifest.load(in);

                String version = manifest.getProperty("Specification-Version", "Unknown");
                String revision = manifest.getProperty("Build-Revision", "Unknown");
                String time = manifest.getProperty("Build-Time", "Unknown");
                LOGGER.info("当前系统版本：Version({}) Build-Revision({}) Build-Time({})", new Object[]{version, revision, time});
            }
        } catch (Exception e) {
            LOGGER.warn("加载系统版本信息错误：", e);
        }
        LOGGER.info("系统初始化完成......");
    }
}
