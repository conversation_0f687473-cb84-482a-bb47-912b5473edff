html {
    -webkit-user-select: none;
    -webkit-touch-callout: none;
}
body,ul,p,h1,h2,h3,h4,h5{margin:0;padding:0;}
a,*[onclick]{-webkit-tap-highlight-color:rgba(0,0,0,0);}
body,html{text-align:left; font-size:62.5%}
li{list-style:none;}
a,a:visited{text-decoration:none;}
.detail h3  a{color:#000; }
.fl{float:left;}
.fr{float:right;}
.bold{font-weight:bold;}
.m5{margin:5px;}
.m10{margin:10px;}
.cf:after{content:"";display:table;clear:both}
.cf{*zoom:1}
a{outline:none;}
 .clear{
     clear: both;}

.currentElem {
    -webkit-backface-visibility: hidden;
    -webkit-transform-style: preserve-3d;
    z-index:3 !important;
}
.colOrg{ color:orange}
.col666{ color: #999}
.textR{ text-align:right}
.textC{ text-align:center}
.mt10{ margin-top:10px;}
.mt15{ margin-top:15px;}
.mt20{ margin-top:20px;}

html{width:100%;height:100%; }
body{background:url(img/bg_body.png) no-repeat bottom center #bae4f0; background-size:100%}
.content{ padding:20px 15px; font-family:"微软雅黑"}
.loginBox .loginCon,.conBox{border:1px solid #8bbecd;border-radius: 5px;box-shadow:inset 0px 0 0px #fff, /*右边阴影*/ inset 0px 0 0px yellow, /*左右边阴影*/  inset 0 0px 0px blue, /*底边阴影*/  inset 0 5px 10px #e6e6e6; /*顶部阴影*/ background:#fff}

.loginCon input{ padding:12px 0;font-size:2.6em; width:100%; border:0; font-family:"微软雅黑"; background:none}
.loginCon .identifying{ position:relative;}
.loginCon .identifying a{ display:block; position:absolute; right:5px; top:5px; padding:8px 10px;text-align:center; background:#a2a2a2; font-family:"微软雅黑"; font-size:2.4em; color:#fff}
.loginCon .identifying a:active{background: #fe8918}
.loginCon .identifying input{ width:60%}
.loginCon .text-1,.loginCon .text-2{ border-bottom:1px solid #8bbecd;padding:0 5px; }
.loginCon .text-3{padding:0 5px;}
.zt1{ line-height:2.4em; font-size:2.2em; margin-bottom:5px; color:#2d2b2a}
.zt1 a{ color:#ff7c19; text-decoration:underline}
.zt2{ font-size:2.8em; margin-top:30px;}

.btn-submit{ width:100%;border-radius: 5px; height:45px; line-height:45px; text-align:center; font-size:3.0em; font-family:"微软雅黑"; color:#fff; display:block;background:-webkit-gradient(linear, 0 0, 0 100%, from(#ffb235), to(#ff681e)); border:1px solid #ff3600; margin-top:20px;background-image: -moz-linear-gradient(top, #ffb235, #ff681e)}
.btn-submit:active{ width:100%;border-radius: 5px; height:45px; line-height:45px; text-align:center; font-size:3.0em; font-family:"微软雅黑"; color:#fff; display:block;background:-webkit-gradient(linear, 0 0, 0 100%, from(#fe5514), to(#ff681e)); border:1px solid #ff3600; margin-top:20px;background-image: -moz-linear-gradient(top, #fe5514, #ff681e)}

.message{ background:url(img/bg_1.png) no-repeat center bottom; background-size:100%; width:100%; overflow:hidden; height:98px; padding-top:20px; overflow:hidden; position:relative; font-family:"微软雅黑"}
.message .portrait{ border:3px solid #38b4d4;box-shadow:0px 0px 15px #666; float:left; margin:0 10px; font-size:0px; overflow:hidden}
.message .portrait img{ width:45px; height:45px; border:0; }
.message .name{ font-size:2.6em; color:#fff; margin:15px 0 3px 0px;}
.message .asset{ background: url(img/bg_tm.png) repeat;position:absolute; bottom:0; width:100%; color:#fff; font-size:2.6em;}
.message .asset-t1,.asset-t2{ width:49%; display:inline-block; text-align:center; margin:8px 0}
.message .asset-t1{ border-right:1px dashed #0a4b58}
.message .asset-t2{ border-left:1px dashed #b0d2d8}
.explicit{color: #149bba; font-size: 0.8em}

.status{ border:1px solid #ceced2; border-bottom:0; background:#fff}
.status a{clear:both; display:block;border-bottom:1px solid #ceced2; line-height:24px;font-size:2.4em;padding:10px; color:#464646;}
.status a b{ vertical-align:middle; display:inline-block; margin-right:5px;width:24px; height:25px; background-size:100%}
.ct1{ background:url(img/ico_1.png) no-repeat; }
.ct2{ background:url(img/ico_2.png) no-repeat; }
.ct3{ background:url(img/ico_3.png) no-repeat;}
.ico1,.ico2{ display: inline-block; height:28px; width:28px;border-radius: 50%; vertical-align:middle;box-shadow:0px 0px 10px #666; margin-right:10px; border:5px solid #e5f5f9; }
.ico3{ display: inline-block; height:9px; width:6px; margin-left:8px}
.ico1{ background:url(img/ico_4.png) no-repeat center;background-size:100%}
.ico2{ background:url(img/ico_5.png) no-repeat center;background-size:100%}
.ico3{ background:url(img/ico_7.png) no-repeat center;background-size:100%}


.conBox input { display: none }
.conBox{ font-size: 2.8em; padding: 5px 10px; color: #454545;position: relative}
.conBox .minSize{ font-size: 0.8em; color: #bfbfbf}
.current{ border:1px solid #ff781a}
.current span{ width: 21px; height: 21px; background: url("img/ico_6.png") no-repeat;background-size:100%; position: absolute; top:0;right:0; display:block}

.error{ font-size:2.0em; color:red; margin-top:10px;}
.info{ font-size:2.0em; margin-top:10px;}

.download{}
.download .tips{
    background: #2AA238;
    border-radius: 5px;
    padding: 5px 5px ;
    height: 24px;
    position: relative;
}
.download .tips em{
    width: 15px;
    height: 15px;
    display: block;
    float: left;
    background: url("img/ico-warn.png") no-repeat;
    background-size:100%;
    margin-right: 6px;
    margin-top: 4px;
}
.download .tips b{
    width:12px;
    height:7px;
    background: url("img/ico-10.png") no-repeat;
    position: absolute;
    top:-7px;
    background-size:100%;
    right: 10px;
      }
.download .tips p{
    line-height: 24px;
    color: #FFFFFF;
    font-size:1.9em;
    float: left;
}
.download .tips{
    margin-bottom: 25px;
}
.download-soft{
    margin-bottom: 20px;
    height: 100%;
    overflow: hidden;
}
.download .download-soft .ico{
    width: 45px;
    height: 45px;
    background: url("img/ico-ling.png") no-repeat;
    background-size:100%;
    float: left;
    margin-right: 5px;
}
.download .download-soft p.title{
   font-size: 3em;
    line-height: 1.6;
    font-weight: normal;
}
.download .download-soft p.p2{
    font-size: 2em;
}
.download .download-btn a{
 width:45%;border-radius: 5px; height:36px; text-align:center;
    font-size:2.4em; font-family:"微软雅黑"; color:#fff; display:block;
    background:-webkit-gradient(linear, 0 0, 0 100%, from(#ffb235), to(#ff681e));
    border:1px solid #ff3600; margin-top:20px;background-image: -moz-linear-gradient(top, #ffb235, #ff681e);
    float: left;


}
.download .download-btn a .txt{
    vertical-align: middle;
    text-shadow: 1px 1px 1px #ccc;
    line-height:36px;
}
.download .download-btn a:first-child{
    margin-right: 5%;
}
.btn-submit:active{ background:-webkit-gradient(linear, 0 0, 0 100%, from(#fe5514), to(#ff681e)); background-image: -moz-linear-gradient(top, #fe5514, #ff681e)}

.download .download-btn  a span.ico{
    width: 20px;
    height:24px;
    display: inline-block;
    vertical-align: middle;
    margin-right: 10px;
}

.download .download-btn .btn1 span.ico{
    background: url("img/ico_8.png") no-repeat;
    background-size:100%;
}
.download .download-btn .btn2 span.ico{
    background: url("img/ico_9.png") no-repeat;
    background-size:100%;
}
