package com.shunwang.basepassport.manager.response.geetest;

import com.shunwang.basepassport.manager.IResponse;
import com.shunwang.util.json.GsonUtil;

/**
 * {
 *     "status": 200,
 *     "result": "13888888888",
 *     "success": true,
 *     "charge": true,
 *     "operator": "CM"
 * }
 */
public class CheckMobileH5Response implements IResponse {

    public static final Integer SUCCESS_CODE = 200;
    //{"charge":false,"operator":"cm","result":"get phone failed","status":500}
    private Integer status;
    private String result;
    private String success;
    private boolean charge;
    private String operator;
    private String error_msg;

    private String rawJson;

    public boolean isSuccess() {
        return SUCCESS_CODE.equals(status);
    }

    public CheckMobileH5Response parse() {
        return GsonUtil.fromJson(rawJson, CheckMobileH5Response.class);
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public boolean isCharge() {
        return charge;
    }

    public void setCharge(boolean charge) {
        this.charge = charge;
    }

    public String getRawJson() {
        return rawJson;
    }

    public void setRawJson(String rawJson) {
        this.rawJson = rawJson;
    }

    public String getSuccess() {
        return success;
    }

    public void setSuccess(String success) {
        this.success = success;
    }

    public String getError_msg() {
        return error_msg;
    }

    public void setError_msg(String error_msg) {
        this.error_msg = error_msg;
    }
}
