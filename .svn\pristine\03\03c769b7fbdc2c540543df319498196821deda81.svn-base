/**
 * Created by min.da on 14-12-22.
 */
void function() {
    $('.sidebar .menu li :eq(1)').addClass('current');
    var $form = $('.form_group');
    var $nickName = $form.find('input[name="member.nickName"]:text');
    var $realName = $form.find('input[name="member.realName"]:text');
    var $idCardNo = $form.find('input[name="member.memberInfo.idCardNo"]:text');
    var $barId = $form.find('input[name="barId"]');
    var $companyName = $form.find('input[name="member.companyName"]:text');
    var $linkMan = $form.find('input[name="member.memberInfo.linkMan"]:text');
    var $qq = $form.find('input[name="member.memberInfo.qq"]:text');
    var $postCode = $form.find('input[name="member.memberInfo.postCode"]:text');
    var $prov = $form.find('select[name="prov"]'),
        $city = $form.find('select[name="city"]'),
        $dist = $form.find('select[name="member.memberInfo.areaId"]');
    var provVal = $prov.attr('s-data');
    var cityVal = $city.attr('s-data');
    var distVal = $dist.attr('s-data');
    var $linkAddress = $form.find('textarea[name="member.memberInfo.linkAddress"]');
    var isPersonal = $nickName.length == 0 ? false : true;
    var $antiAdditionCheck = $form.find("#antiAdditionCheck")

    var message = {
        c1001: "输入不超过12个字符，中文占两个字符",
        c1002: "昵称不能包含英文单双引号，反斜杠和左右尖括号",
        c1003: "昵称不能包含禁用词语",
        c1004: "输入不超过12个字符",
        c1016: "请输入姓名",
        c1017: "真实姓名请使用中文，2-32个汉字，支持半角·",
        c1018: "真实姓名请使用中文，2-32个汉字，支持半角·",
        c1019: "真实姓名和身份证号码不能同时为空",
        c1020: "无效身份证号码,请输入18位有效身份证号",
        c1022: "注册用户须年满18周岁",
        c1023: "输入18位身份证号码",
        c1024: "请填写公司名称",
        c1025: "公司名称最多可输入128个字符",
        c1026: "最多输入128个字符",
        c1027: "请填写联系人",
        c1028: "联系人最多可输入16个字符",
        c1029: "最多输入16个字符",
        c1034: "必须为5至20位数字",
        c1035: "必须为6位数字",
        c1036: "最多可输入128个字符",
		c1037:"请同意《顺网通行证防沉迷认证服务协议》"
    };

    var loadAreaJSON = function(callback, data) {
        $.ajax({
            url: $CONFIG.appServer + "/front/common/queryArea.htm",
            type: 'post',
            dataType: 'json',
            data: {pid : data.id, r: Math.random()},
            success: function(json){
                var dataJson = null;
                if (json.result) {
                    dataJson = json.data;
                }
                callback(dataJson);
            },
            error: function(){
            }
        });
    };

    $prov.on('change', function() {
        $city.empty();
        $city.append('<option value="-1" selected="selected">请选择</option>');
        if ($(this).val() == "") {
            $city.change();
            return;
        }

        loadAreaJSON(function(json) {
            for (var i = 0; i < json.length; i++) {
                if ($prov.val() == json[i].parentId) {
                    $city.append('<option value="' + json[i].areaId + '" ' + ((cityVal == json[i].areaId) ? "selected=selected" : "") + ' >' + json[i].name + '</option>');
                }
            }
            $city.change();
            cityVal = null;
        }, {id: $prov.val()});
    });

    $city.on('change', function() {
        $dist.empty();
        $dist.append('<option value="-1" selected="selected">请选择</option>');
        if ($(this).val() == "") return;
        loadAreaJSON(function(json) {
            for (var i = 0; i < json.length; i++) {
                if ($city.val() == json[i].parentId) {
                    $dist.append('<option value="' + json[i].areaId + '" ' + ((distVal == json[i].areaId) ? "selected=selected" : "") + ' >' + json[i].name + '</option>');
                }
            }
            distVal = null;
        }, {id: $city.val()})
    });

    if (provVal != "") {
        $prov.val(provVal);
        $prov.change();
    };

    var checkNickName = function() {
        var nnv = $.trim($nickName.val());

        if (nnv.length == 0) {
            showTips($nickName, message.c1004, true);
            return true;
        }

        if (getBytes(nnv) > 12) {
            showTips($nickName, message.c1001);
            return false;
        }

        if(/[\\<\\>\'\"\\\\()]+/.test(nnv)){
            showTips($nickName, message.c1002);
            return false;
        }

        if(isSensitive(nnv)){
            showTips($nickName, message.c1003);
            return false;
        }

        showTips($nickName, message.c1004, true);
        return true;
    };

    var checkRealName = function() {
        var rnv = $.trim($realName.val());

        if (rnv.length == 0) {
            showTips($realName, message.c1018, true);
            return true;
        }

        if(!/^[\u4e00-\u9fa5|\u00b7]{2,32}$/.test(rnv)) {
            showTips($realName, message.c1017);
            return false;
        }

        showTips($realName, message.c1018, true);
        return true;
    };

    var checkIDCard = function() {
        var idv = $.trim($idCardNo.val());

        if (idv.length == 0) {
            showTips($idCardNo, message.c1023, true);
            return true;
        }

        var result = validateIdCard($idCardNo[0]);
        if (result == 1) {
            showTips($idCardNo, message.c1020);
            return false;
        } else if (result == 3) {
            showTips($idCardNo, message.c1020);
            return false;
        } else if (result == 4) {
            showTips($idCardNo, message.c1022);
            return false;
        }

        showTips($idCardNo, message.c1023, true);
        return true;
    };

    var checkCompanyName = function() {
        var cnv = $.trim($companyName.val());

        if (cnv.length == 0) {
            showTips($companyName, message.c1026, true);
            return true;
        }

        var byteslen = getBytes(cnv);
        if (byteslen.length > 128) {
            showTips($companyName, message.c1025);
            return false;
        }

        showTips($companyName, message.c1026, true);
        return true;
    };

    var checkLinkMan = function() {
        var lmv = $.trim($linkMan.val());

        if (lmv.length == 0) {
            showTips($linkMan, message.c1029, true);
            return true;
        }

        var bytesize = getBytes(lmv);
        if (bytesize.length > 16) {
            showTips($linkMan, message.c1028);
            return false;
        }

        showTips($linkMan, message.c1029, true);
        return true;
    };

    var checkQQ = function() {
        var qv = $.trim($qq.val());

        if (qv.length == 0) {
            showTips($qq, message.c1034, true);
            return true;
        }

        if (!/^\d{5,20}$/.exec(qv)) {
            showTips($qq, message.c1034);
            return false;
        }

        showTips($qq, message.c1034, true);
        return true;
    };

    var checkPostCode = function() {
        var cv = $.trim($postCode.val());

        if (cv.length == 0) {
            showTips($postCode, message.c1035, true);
            return true;
        }

        if (!/[\d]{6}/.test(cv)) {
            showTips($postCode, message.c1035);
            return false;
        }

        showTips($postCode, message.c1035, true);
        return true;
    };

    var checkArea = function(event) {
        var isChange = (typeof event !== 'undefined' && event.type === 'change') || false;
        if ($prov.val() == "-1" && !isChange) {
            $prov.addClass('form_item_error');
            return false;
        } else {
            $prov.removeClass('form_item_error')
        }
        if ($city.val() == "-1" && !isChange) {
            $city.addClass('form_item_error');
            return false;
        } else {
            $city.removeClass('form_item_error');
        }
        if ($dist.val() == "-1" && !isChange) {
            $dist.addClass('form_item_error');
            return false;
        } else {
            $dist.removeClass('form_item_error');
        }
        return true;
    };

    var checkLinkAddress = function() {
        var lav = $.trim($linkAddress.val());

        if (lav.length == 0) {
            showTips($linkAddress, message.c1036, true);
            return true;
        }

        if (lav.length > 128) {
            showTips($linkAddress, message.c1036);
            return false;
        }

        showTips($linkAddress, message.c1036, true);
        return true;
    };

    var validate = function() {
        if (isPersonal) {
            if (!checkNickName()) return false;
            if ($realName.length != 0 && !checkRealName()) return false;
            if ($idCardNo.length != 0 && !checkIDCard()) return false;
            if ($realName.length != 0 && $idCardNo.length == 0) {
                showTips($idCardNo, message.c1019, true);
                return false;
            }
            if ($realName.length == 0 && $idCardNo.length !=0) {
                showTips($realName, message.c1019, true);
                return false;
            }
        } else {
            if ($companyName.length != 0 && !checkCompanyName()) return false;
            if ($linkMan.length != 0 && !checkLinkMan()) return false;
        }
        if (!checkQQ()) return false;
        if (!checkPostCode()) return false;
//        if (!checkArea()) return false;
        if (!checkLinkAddress()) return false;
        if($antiAdditionCheck.length > 0 && !$antiAdditionCheck.is(":checked")){
			$antiAdditionCheck.nextAll(".form_tip").addClass("form_error").html(s.c1037);
			return false;
        }else{
			$antiAdditionCheck.nextAll(".form_tip").removeClass("form_error").html("");
		}
        return true;
    };

    var submit = function() {
        if (validate() && !submitted) {
            submitted = true;

            var areaId = $dist.val();
            if (areaId == "-1") {
                areaId = $city.val();
                if (areaId == "-1") {
                    areaId = $prov.val();
                }
            }
            var postData = {'member.memberInfo.qq': $qq.val(), 'member.memberInfo.postCode': $postCode.val(), 'member.memberInfo.linkAddress': $linkAddress.val(), 'member.memberInfo.areaId': areaId};
            if (isPersonal) {
                postData['member.nickName'] = $nickName.val();
                postData['member.realName'] = $realName.val();
                postData['member.memberInfo.idCardNo'] = $idCardNo.val();
                postData['barId'] = $barId.val();
            } else {
                postData['member.companyName'] = $companyName.val();
                postData['member.memberInfo.linkMan'] = $linkMan.val();
            }
            $.ajax({
                url: $CONFIG.appServer + "/front/member/asyncUpdateMember_front.htm",
                type: 'post',
                dataType: 'json',
                data: postData,
                success: function(json){
                    var msg = json.msg;
                    if (json.result) {
                        msg = "保存成功";
                        setTimeout(function(){
                            window.location.href = $CONFIG.appServer + "/front/member/securityCenter_index.htm"
                        }, 2500);
                    } else{
                        if(msg == ""){
							msg = "保存失败";
                        }
                        submitted = false;
                    }
                    $('#msg-modal').children('.modal-dialog').text(msg).parent().modal('show');
                },
                error: function(){
                    submitted = false;
                }
            });
        }
    }

    var $btn = $form.find('.btn_default_lg');
    var submitted = false;
    $btn.on('click', function() {
        submit();
    });

    var showTips = function(target, message, isOk) {
        if (typeof isOk != 'undefined' && isOk) {
            target.removeClass('form_item_error').next().attr("class", "form_tip").html(message);
        } else {
//            target.focus();
            target.addClass('form_item_error').next().attr("class", "form_error").html(message);
        }
    }

    $nickName.on('blur', function(){checkNickName()}).on('focus', function(){showTips($(this), message.c1004, true)});
    $realName.on('blur', function(){checkRealName()}).on('focus', function(){showTips($(this), message.c1018, true)});
    $idCardNo.on('blur', function(){checkIDCard()}).on('focus', function(){showTips($(this), message.c1023, true)});
    $companyName.on('blur', function(){checkCompanyName()}).on('focus', function(){showTips($(this), message.c1026, true)});
    $linkMan.on('blur', function(){checkLinkMan()}).on('focus', function(){showTips($(this), message.c1029, true)});
    $qq.on('blur', function(){checkQQ()}).on('focus', function(){showTips($(this), message.c1034, true)});
    $postCode.on('blur', function(){checkPostCode()}).on('focus', function(){showTips($(this), message.c1035, true)});
//    $prov.add($city).add($dist).on('change', function(event) {checkArea(event)});
    $linkAddress.on('blur', function(){checkLinkAddress()}).on('focus', function(){showTips($(this), message.c1036, true)});

    var getBytes = function(str) {
        return str.replace(/[\u0391-\uFFE5]/g,"dm").length;
    }

    var len = function(s) {//获取字符串的字节长度
            s = String(s);
            return s.length+(s.match(/[^\x00-\xff]/g) ||"").length;//加上匹配到的全角字符长度
        },
        limitDo=function(limit){
            var val=this.value;
            if(len(val)>limit) {
                //val=val.substr(0,limit);
                while(len(val=val.substr(0,val.length-1))>limit);
                this.value=val;
            }
        };
    $form.find('[name="member.memberInfo.linkAddress"]').on('keyup', function() {
        var val=this.value;
        //val=val.substr(0,limit);
        while(val.length>128){
            val=val.substr(0,val.length-1);
            this.value = val;
        }
    });

    $form.find('img[name="un-id-auth"]').tooltip({
        html: true,
        title: function() {
            if ($('.tooltip').length == 0) {
                return $(this).next().html()
            }
        }
    }).off('mouseleave').parent().on('mouseleave', function() {
        $(this).children('img[name="un-id-auth"]').tooltip('hide')
    });
}();