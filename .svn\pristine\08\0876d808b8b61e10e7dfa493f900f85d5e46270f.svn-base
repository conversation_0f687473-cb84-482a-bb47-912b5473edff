<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE struts PUBLIC "-//Apache Software Foundation//DTD Struts Configuration 2.0//EN" "http://struts.apache.org/dtds/struts-2.0.dtd">

<struts>

    <!-- 登录状态下找回修改手机号, decorator不同 -->
	<package name="changeEmail-logged" namespace="/front/changeEmail" extends="passport" >			
        <action name="goAppealChangeEmail_front" method="appealInputInfo" class="appealChangeEmailAction">
            <result name="nonRealNameAppeal" >/front/changeEmail/logged/appeal_changeEmailNext_front.jsp</result>
            <result name="realNameAppeal" >/front/changeEmail/logged/actu_changeEmail_front.jsp</result>
            <result name="appealResult" >/front/find/appeal_progress_query_result.jsp</result>
            <result name="input" >/front/find/pwdFind_all_front.jsp</result>
            <result name="bind" >/front/find/bind_email_first.jsp</result>
        </action>


        
        <action name="goAppealChangeEmailReset_front" method="appealResetEmail" class="appealChangeEmailAction">
            <result name="success" >/front/changeEmail/logged/appeal_changeEmailReset_front.jsp</result>
            <result name="input" >/front/changeEmail/logged/appeal_changeEmailNext_front.jsp</result>
        </action>
        
        <action name="appeal_changeEmailSuccess_front" method="appealChangeEmailSuccess" class="appealChangeEmailAction">
            <!--<result name="success" >/front/changeEmail/logged/appeal_changeEmailSuccess_front.jsp</result>-->
            <result name="success" type="redirect">/front/noLogin/appeal_query_progress.htm?queryType=email&amp;memberName=${memberName}</result>
            <result name="input" >/front/changeEmail/logged/appeal_changeEmailReset_front.jsp</result>
        </action>

        <action name="goActuChangeEmailNext_front" method="actuResetEmail" class="actuChangeEmailAction">
            <result name="success" >/front/changeEmail/logged/actu_changeEmailNext_front.jsp</result>
            <result name="input" >/front/changeEmail/logged/actu_changeEmail_front.jsp</result>
        </action>
        
        <action name="actu_changeEmailSuccess_front" method="actuChangeEmailSuccess" class="actuChangeEmailAction">
            <!--<result name="success" >/front/changeEmail/logged/appeal_changeEmailSuccess_front.jsp</result>-->
            <result name="success" type="redirect">/front/noLogin/appeal_query_progress.htm?queryType=email&amp;memberName=${memberName}</result>

            <result name="input" >/front/changeEmail/logged/actu_changeEmailNext_front.jsp</result>
        </action>
        

    </package>

    <!--未登录时可访问-->
	<package name="changeEmail" namespace="/front/noLogin" extends="nologinpassport">
		
		<action name="sendEmailCheckCode_front" method="sendCheckCode" class="changeEmailAction">
        </action>
		
		<action name="goQuestionChangeEmail_front" method="goQuestionChangeEmail" class="questionChangeEmailAction">
            <result name="success">/front/changeEmail/question_changeEmailfront.jsp</result>
            <result name="input">/front/changeEmail/question_changeEmailfront.jsp</result>
        </action>
        
        <action name="goQuestionChangeEmailNext_front" method="questionResetEmail" class="questionChangeEmailAction">
            <result name="success" >/front/changeEmail/question_changeEmailNextfront.jsp</result>
            <result name="input" >/front/changeEmail/question_changeEmailfront.jsp</result>
        </action>
        
	    <action name="questionChangeEmailSuccess_front" method="questionChangeEmailSuccess" class="questionChangeEmailAction">
	         <result name="success">/front/changeEmail/question_changeEmailSuccess_front.jsp</result>
	         <result name="input">/front/changeEmail/question_changeEmailNextfront.jsp</result>
	    </action>
	    
	    

        
        <action name="goActuChangeEmail_front" method="goActuChangeEmail" class="actuChangeEmailAction">
            <result name="success">/front/changeEmail/actu_changeEmail_front.jsp</result>
            <result name="input">/front/changeEmail/actu_changeEmail_front.jsp</result>
        </action>
        
        <action name="goActuChangeEmailNext_front" method="actuResetEmail" class="actuChangeEmailAction">
            <result name="success" >/front/changeEmail/actu_changeEmailNext_front.jsp</result>
            <result name="input" >/front/changeEmail/actu_changeEmail_front.jsp</result>
        </action>
        
        <action name="actu_changeEmailSuccess_front" method="actuChangeEmailSuccess" class="actuChangeEmailAction">
            <!--<result name="success" >/front/changeEmail/appeal_changeEmailSuccess_front.jsp</result>-->
            <result name="success" type="redirect">/front/noLogin/appeal_query_progress.htm?queryType=email&amp;memberName=${memberName}</result>
            <result name="input" >/front/changeEmail/actu_changeEmailNext_front.jsp</result>
        </action>
        
        <action name="goAppealChangeEmail_front" method="appealInputInfo" class="appealChangeEmailAction">
            <result name="nonRealNameAppeal" >/front/changeEmail/logged/appeal_changeEmailNext_front.jsp</result>
            <result name="realNameAppeal" >/front/changeEmail/logged/actu_changeEmail_front.jsp</result>
            <result name="appealResult" >/front/find/appeal_progress_query_result.jsp</result>
            <result name="input" >/front/find/pwdFind_all_front.jsp</result>
            <result name="bind" >/front/find/bind_email_first.jsp</result>
        </action>



        <action name="goAppealChangeEmailReset_front" method="appealResetEmail" class="appealChangeEmailAction">
            <result name="success" >/front/changeEmail/appeal_changeEmailReset_front.jsp</result>
            <result name="input" >/front/changeEmail/appeal_changeEmailNext_front.jsp</result>
        </action>

        <action name="appeal_changeEmailSuccess_front" method="appealChangeEmailSuccess" class="appealChangeEmailAction">
            <result name="success" type="redirect">/front/noLogin/appeal_query_progress.htm?queryType=email&amp;memberName=${memberName}</result>
            <result name="input" >/front/changeEmail/appeal_changeEmailReset_front.jsp</result>
        </action>

	</package>
	
	
</struts>
