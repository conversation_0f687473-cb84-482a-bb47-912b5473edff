package com.shunwang.basepassport.oa.web;

import com.shunwang.baseStone.core.action.BaseStoneAction;
import com.shunwang.baseStone.core.response.BaseStoneResponse;
import com.shunwang.basepassport.commonExp.ParamNotFoundExp;
import com.shunwang.basepassport.manager.request.oa.GetUserRequest;
import com.shunwang.basepassport.manager.response.oa.OaResponse;
import com.shunwang.basepassport.manager.service.oa.OaServiceClient;
import com.shunwang.basepassport.user.common.MemberConstants;
import com.shunwang.util.StringUtil;
import com.shunwang.util.json.GsonUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * ****************************
 * 风控检测
 * 2020.09.24
 ***************************************
 */
public class OaGetUserAction extends BaseStoneAction{

	private static final long serialVersionUID = 1L;
    private String email;
    private String idCard;

	@Override
	public void process(){
        GetUserRequest request = new GetUserRequest();
        request.setEmail(email);
        request.setIdCard(idCard);
        OaResponse response = OaServiceClient.execute(request);
        BaseStoneResponse baseStoneResponse = getBaseResponse();
        if (response == null || !response.isSuccess()) {
            baseStoneResponse.setMsgId("9999");
            baseStoneResponse.setMsg("oa接口异常");
        } else {
            List list = new ArrayList();
            list.add(GsonUtil.toJson(response.getMessage()));
            baseStoneResponse.setItems(list);
        }
        this.setBaseResponse(baseStoneResponse);
	}
	 /**
     * ***********
      * 创建日期: Jul 26, 2011 1:43:15 PM
      * 创建作者：chenjh
      * @return 
      * 功能：检查参数是否为空
      *************
     */
	public void checkParam(){
		if(StringUtil.isBlank(email) && StringUtil.isBlank(idCard))
			throw new ParamNotFoundExp("email,idcard二选一");
		if (StringUtil.isNotBlank(email) && StringUtil.isNotBlank(idCard)) {
            throw new ParamNotFoundExp("email,idcard二选一");
        }
	}

	@Override
	public String getSiteName() {		
		return MemberConstants.OA_GET_USER;
	}

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getIdCard() {
        return idCard;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }
}
