package com.shunwang.basepassport.outActu;

import com.shunwang.util.date.DateUtil;
import com.shunwang.util.encrypt.Md5Encrypt;
import com.shunwang.util.net.HttpUtil;
import org.junit.Test;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Map;
import java.util.TreeMap;

/**
 * User:lj.zeng
 * Date:2019/04/28
 * Time:19:26
 */
public class TestOutActualNew {

	private static TreeMap<String, String> requestMap = new TreeMap<>();

	@Test
	public void test() throws Exception {
		TreeMap<String, String> paramMap = new TreeMap<>();
		String md5Key = "123456" ;
		paramMap.put("siteId","Passport");
		paramMap.put("time",DateUtil.getCurrentDateStamp());
		paramMap.put("signVersion", "1.0");
		paramMap.put("userName","safiad");
		paramMap.put("compName","网鱼网咖");
		paramMap.put("linkUser","刘六");
		paramMap.put("linkMobile","13858411987");
		paramMap.put("cafeAddr","杭州市西湖区");
		paramMap.put("cafeLicenceImg","http://www.huian168.com/userfiles/2014/04/1397736607.jpg");
		paramMap.put("idcardNo","342623199301186338");
		paramMap.put("idCardImg1","http://uploads.yjbys.com/allimg/150608/3-15060P9230a31.jpg");
		paramMap.put("idCardImg2","http://uploads.yjbys.com/allimg/150608/3-15060P92211Z4.jpg");
		paramMap.put("idCardImg3","http://www.wdc66.cn/template/default/img/bangzhuzhongxin/sfz3.jpg");
		paramMap.put("facadeImg","http://images.v007.net/uploadfiles/others/200910/2009102315354425.jpg");
		paramMap.put("legalPersonName","刘六");
		paramMap.put("businessPlaceImg","http://images.v007.net/uploadfiles/others/200910/2009102315354425.jpg");

		String plainText = TestOutActualNew.buildSignStringSorted(paramMap,md5Key) ;
		String sign = Md5Encrypt.encrypt(plainText).toUpperCase();
		System.out.println(plainText);
		requestMap.put("sign", sign);
		String url = "http://interface.kedou.com/front/interface/outActuality.htm";
		String respond = HttpUtil.doPost(url, requestMap, ENCODE);
		System.out.println(respond);
	}

	/**
	 * 参数分隔符
	 */
	public final static String PARAM_SPLIT = "|";

	public static final String ENCODE = "UTF-8";

	/**
	 * 获取签名原串:按字典排序参数值+key
	 * <AUTHOR> create at 2018年05月17日 14:43:05
	 * @return
	 */
	public static  String buildSignStringSorted(TreeMap<String, String> paramMap, String md5_key) throws NullPointerException {
		StringBuilder sb = new StringBuilder();
		for (Map.Entry<String, String> entry : paramMap.entrySet()) {
			sb.append(entry.getValue()).append(PARAM_SPLIT);
			requestMap.put(entry.getKey(), entry.getValue());
		}
		sb.append(md5_key);
		return sb.toString();
	}
}
