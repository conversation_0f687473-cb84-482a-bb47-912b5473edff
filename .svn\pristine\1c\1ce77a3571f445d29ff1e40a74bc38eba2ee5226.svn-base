package com.shunwang.baseStone.sso.filter;

import com.shunwang.baseStone.cache.CacheKeyConstant;
import com.shunwang.baseStone.context.RedisContext;
import com.shunwang.baseStone.sso.context.SsoDomainContext;
import com.shunwang.util.lang.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

/**
 * <AUTHOR> create at 2019-04-22 上午10:17:59
 * @Described：检测XSS漏洞过滤器
 * @FileNmae com.shunwang.baseStone.sso.filter.XSSFilter.java
 */
public class XSSFilter implements Filter {

    private static final Logger logger = LoggerFactory.getLogger(XSSFilter.class);

    @Override
    public void destroy() {

    }

    @SuppressWarnings("unchecked")
    @Override
    public void doFilter(ServletRequest arg0, ServletResponse arg1,
                         FilterChain chain) throws IOException, ServletException {
        if (!getXssIsOpen()) {
            chain.doFilter(arg0, arg1);
            return;
        }
        HttpServletRequest request = (HttpServletRequest) arg0;
        HttpServletResponse response = (HttpServletResponse) arg1;
        Map<String, String[]> paramMap = request.getParameterMap();

        //参数白名单
        String value = getExcludeParamConfig();
        String[] paramWhiteList = StringUtil.isBlank(value) ? new String[]{} : value.split("\\|");

        for (Entry<String, String[]> entry : paramMap.entrySet()) {
            String[] values = entry.getValue();
            String k = entry.getKey();
            boolean isDanger = false;

            if (isXSS(k) || isXSS(URLDecoder.decode(k, "UTF-8"))) {
                isDanger = true;
            }
            if (isExcludeParam(k, paramWhiteList)) {
                continue;
            }
            if (urlParamList.contains(k) && !checkUrlWhiteList(values)) {
                isDanger = true;
            }
            if (request.getRequestURI().equalsIgnoreCase("/checkJumpUrl.do")) {
                response.getWriter().write(isDanger ? "N" : "Y");
                return;
            }
            for (String v : values) {
                if (v != null && (isDanger || isXSS(v) || isXSS(URLDecoder.decode(v, "UTF-8")) || dangerousKey(v))) {
                    response.sendRedirect(SsoDomainContext.getSsoServer() + "/login/error.jsp");
                    return;
                }
            }
        }
        chain.doFilter(request, response);
    }

    /**
     * 判断是否是自身跳转
     * @param url
     * @return
     */
    private boolean isSelfVisit(String url) {
        try {
            //丢弃参数 防止乱码的参数导致解析出错
            URI uri = new URI(url.contains("?") ? url.substring(0, url.indexOf("?")) : url);
            //自身跳转无host信息
            if (StringUtil.isBlank(uri.getHost())){
                return true;
            }
        } catch (URISyntaxException e) {
            logger.error("解析地址异常{}",url, e);
        }
        return false;
    }

    /**
     * 处理 jumpUrl 中类似 jumpUrl=https://<EMAIL>
     * 直接跳转到 baidu 的情况
     * 所以获取 url 的实际 host
     * 再和 url 白名单比较
     * @param url
     * @return
     */
    private String getHost(String url) {
        try {
            if (StringUtil.isBlank(url)) {
                return null;
            }
            //丢弃参数 防止乱码的参数导致解析出错
            URI uri = new URI(url.contains("?") ? url.substring(0, url.indexOf("?")) : url);
            return uri.getHost();
        } catch (Exception ex) {
            logger.error("获取host[{}]异常", url, ex);
        }
        return null;
    }

    private boolean dangerousKey(String value) throws UnsupportedEncodingException {
        if (!getDangerCharIsOpen() || StringUtil.isBlank(value)) {
            return false;
        }
        value = value.toLowerCase();
        //获取危险关键词 onclick
        String configKeys = getDangerValueForXss();
        int count = getDangerCharLimitForXss();
        String[] keys = StringUtil.isBlank(configKeys) ? new String[]{} : configKeys.split("\\|");
        for (String key : keys) {
            if (value.contains(key)) {
                return countDangerChar(value, count);
            }
        }
        return false;
    }
    private boolean countDangerChar(String v, int limit) throws UnsupportedEncodingException {
        String chars = getDangerCharListForXss();
        v = URLDecoder.decode(v, "UTF-8");
        for (int i = 0, n = v.length(); i < n; i++) {
            for (int j = 0, m = chars.length(); j < m ; j++) {
                if (v.charAt(i) != chars.charAt(j)) {
                    continue;
                }
                limit --;
                if (limit == 0) {
                    return true;
                }
            }
        }
        return false;
    }
    private int getDangerCharLimitForXss() {
        String value = RedisContext.getResourceCache().getResourceValue(CacheKeyConstant.ConfigResourcesConstants.COMMON_CONFIG, CacheKeyConstant.ConfigResourcesConstants.DANGER_CHAR_LIMIT_FOR_XSS);
        if (StringUtil.isBlank(value)) {
            return 3;
        }
        return Integer.parseInt(value);
    }
    private boolean getDangerCharIsOpen() {
        String value = RedisContext.getResourceCache().getResourceValue(CacheKeyConstant.ConfigResourcesConstants.COMMON_CONFIG, CacheKeyConstant.ConfigResourcesConstants.DANGER_CHAR_IS_OPEN);
        if (StringUtil.isBlank(value)) {
            return false;
        }
        return value.equalsIgnoreCase("y");
    }
    private String getDangerValueForXss() {
        return RedisContext.getResourceCache().getResourceValue(CacheKeyConstant.ConfigResourcesConstants.COMMON_CONFIG, CacheKeyConstant.ConfigResourcesConstants.VALUE_DANGER_LIST_FOR_XSS);
    }
    private String getDangerCharListForXss() {
        return RedisContext.getResourceCache().getResourceValue(CacheKeyConstant.ConfigResourcesConstants.COMMON_CONFIG, CacheKeyConstant.ConfigResourcesConstants.DANGER_CHAR_LIST_FOR_XSS);
    }

    private String getExcludeParamConfig() {
        return RedisContext.getResourceCache().getResourceValue(CacheKeyConstant.ConfigResourcesConstants.COMMON_CONFIG, CacheKeyConstant.ConfigResourcesConstants.PARAMS_WHITE_LIST_FOR_XSS);
    }

    private boolean isExcludeParam(String queryParam, String[] paramWhiteList) {
        for (String param : paramWhiteList) {
            if (param.equals(queryParam)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 通过白名单过滤参数值类型为JSON的参数
     *
     * @param value
     * @param paramWhiteList
     * @return
     */
    private boolean excludeWhiteValue(String value, String[] paramWhiteList) {
        for (String param : paramWhiteList) {
            if (value.indexOf(param) != -1) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检测URL域是否在白名单内
     * 用于操作完后跳转用
     * @param urls
     * @return
     */
    private boolean checkUrlWhiteList(String[] urls) {
        if (urls == null || urls.length == 0) {
            return true;
        }
        String url = urls[0];
        if (isSelfVisit(url)) {
            return true;
        }
        String host = getHost(url);
        if (StringUtil.isBlank(host)) {
            return false;
        }
        String value = getUrlDomainsWhiteListConfig();
        String[] domains = StringUtil.isBlank(value) ? new String[]{} : value.split("\\|");
        for (String domain : domains) {
            if (host.contains(domain)) {
                return true;
            }
        }
        logger.debug("url{}不在域名白名单中", url);
        return false;
    }

    private String getUrlDomainsWhiteListConfig() {
        return RedisContext.getResourceCache().getResourceValue(CacheKeyConstant.ConfigResourcesConstants.COMMON_CONFIG, CacheKeyConstant.ConfigResourcesConstants.URL_DOMAINS_WHITE_LIST);
    }

    private boolean getXssIsOpen() {
        String value = RedisContext.getResourceCache().getResourceValue(CacheKeyConstant.ConfigResourcesConstants.COMMON_CONFIG, CacheKeyConstant.ConfigResourcesConstants.XSS_IS_OPEN);
        if (StringUtil.isBlank(value)) {
            return false;
        }
        return value.equalsIgnoreCase("y") ? true : false;
    }

    private boolean isXSS(String v) {
        return (v.indexOf("<") != -1 || v.indexOf(">") != -1 || v.indexOf("(") != -1 || v.indexOf(")") != -1);
    }

    private static List<String>  urlParamList = new ArrayList<>();
    @Override
    public void init(FilterConfig arg0) throws ServletException {
        //跳转参数不太会变动，故固定在filter内
        String[] needCheckDomainParams = {"callbackUrl", "redirect_uri","jumpUrl","returnUrl","redirectUri"};
        for (String param : needCheckDomainParams) {
            urlParamList.add(param);
        }
    }

}
