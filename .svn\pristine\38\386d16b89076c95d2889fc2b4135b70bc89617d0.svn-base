package com.shunwang.basepassport.manager.request.gameactu;

import com.shunwang.basepassport.config.pojo.ConfigInterface;
import com.shunwang.basepassport.manager.HttpMethod;
import com.shunwang.basepassport.manager.InterfaceConstant;
import com.shunwang.basepassport.manager.request.BaseRequest;
import com.shunwang.basepassport.manager.response.chuanglan.ChuanglanResponse;
import com.shunwang.basepassport.manager.response.gameactu.GameActuResponse;

import java.util.HashMap;
import java.util.Map;

public class GameActuQueryRequest extends BaseRequest<GameActuResponse> {
    private String appKey;
    private String appId;
    private String idCardNo;
    private String realName;

    @Override
    public Map<String, String> buildParams() {
        Map<String, String> param = new HashMap<>();
        param.put("appId", appId);
        param.put("appKey", appKey);
        param.put("name", realName);
        param.put("idNum", idCardNo);
        return param;
    }

    @Override
    public Class<GameActuResponse> getResponseClass() {
        return GameActuResponse.class;
    }

    @Override
    public Integer getInterfaceKey() {
        return InterfaceConstant.INTERFACE_KEY_GAME_IDCRAD_VERIFY_QUERY;
    }

    @Override
    public void doInterfaceSetting(ConfigInterface setting) {
        setUrl(setting.getInterfaceUrl1());
        setAppId(setting.getInterfacePartnerId());
        setAppKey(setting.getInterfaceMd5Key());
    }

    @Override
    public HttpMethod getHttpMethod() {
        return HttpMethod.POST;
    }

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getIdCardNo() {
        return idCardNo;
    }

    public void setIdCardNo(String idCardNo) {
        this.idCardNo = idCardNo;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }
}
