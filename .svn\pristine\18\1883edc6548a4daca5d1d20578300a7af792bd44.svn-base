/*
 * $Id$
 * Copyright (c)  by iCafeMavin Information Technology Inc. All right reserved.
 */
package com.shunwang.passport.safeNotice.service;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.Map;
import java.util.TreeMap;

import com.shunwang.util.net.HttpClientUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.json.JSONException;
import org.json.JSONML;
import org.json.JSONObject;

import com.shunwang.passport.safeNotice.exception.SafeNoticeSendSmsExp;
import com.shunwang.util.date.DateUtil;
import com.shunwang.util.encrypt.Md5Encrypt;

/******************************
** 版权所有：顺网科技 保留所有权利
 ** 创建日期: 2013-12-12下午04:34:24
 ** 创建作者: <PERSON><PERSON> (<EMAIL>)   
 ** 版本:  2.0
 ** 功能: 
 ** 最后修改时间:
 ** 修改记录:
 ********************************/

public class SafeNoticeSendSmsService {
	
	private final static Logger log = LoggerFactory.getLogger(SafeNoticeSendSmsService.class);
	
	private String siteId;
	private String sendUrl;
	private String md5Key;
	
	public void sendNoticeSMS(Integer memberId,String noticeType,String jsonValue){
		String result = "";
		try {
			result = HttpClientUtils.doPost(getSendUrl(),buildParams(memberId,noticeType,jsonValue));
		} catch (Exception e) {
			log.error("帐号安全异动短信接口调用异常");
			throw new SafeNoticeSendSmsExp("接口调用失败！");
		}
		parseResult(result,memberId,noticeType);
	}
	
	
	
	private Map<String, String> buildParams(Integer memberId,String noticeType,String jsonValue){
		String time = DateUtil.getCurrentDateStamp();
		Map<String, String> paramMap = new TreeMap<String, String>();
		paramMap.put("jsonValues", jsonValue == null?"":jsonValue);
		paramMap.put("noticeType", noticeType);
		paramMap.put("siteId", getSiteId());
		paramMap.put("time", time);
		paramMap.put("memberId", memberId == null?"":memberId.toString());
		paramMap.put("sign", buildSign(paramMap));
		return paramMap;
	}
	
	private String buildSign(Map<String, String> paramMap) {
		StringBuffer stringBuffer = new StringBuffer();
		for(String keySet: paramMap.keySet()){
			stringBuffer.append(paramMap.get(keySet)).append("|");
		}
		stringBuffer.append(getMd5Key());
		String sign = "";
		try{
			sign = URLEncoder.encode(stringBuffer.toString(),"utf-8").toUpperCase();
		}catch(UnsupportedEncodingException e){
			log.error("对签名转码错误", e);
		}
		sign = Md5Encrypt.encrypt(sign).toUpperCase();
		if(log.isInfoEnabled())
			log.info("账户安全异动提醒发送短信接口参数拼装:"+stringBuffer.toString()+",sign："+sign);
		return sign;
	}
	
	private void parseResult(String xmlText, Integer memberId, String noticeType) {
		try {
            JSONObject jsonObj = JSONML.toJSONObject(xmlText);
            String nodes = jsonObj.getString("childNodes");
            nodes = nodes.substring(1, nodes.length()-1);
            jsonObj = new JSONObject(nodes);
            int msgId = jsonObj.getInt("msgId");
            String msg = jsonObj.getString("msg");
            if (!(msgId == 0)) {
            	if(msgId == 1000||msgId == 1001||msgId==1002||msgId==1003||msgId==1004||msgId==1005){
            		log.error("账户安全异动提醒短息发送失败[memberId:" + memberId + ",noticeType:" + noticeType + ",errorMsg:" + msg +"]");
            	}else{
            		log.info("账户安全异动提醒短息发送失败[memberId:" + memberId + ",noticeType:" + noticeType + ",errorMsg:" + msg +"]");
            	}
            }else{
            	log.info("账户安全异动提醒短息发送成功[memberId:" + memberId + ",noticeType:" + noticeType + "]");
            }
        } catch (JSONException e) {
            log.error("json解析返回xml数据时出错：xmlText(" + xmlText + ")" + e.getMessage());
        }
    }

	public void setSiteId(String siteId) {
		this.siteId = siteId;
	}

	public String getSiteId() {
		return siteId;
	}

	public void setSendUrl(String sendUrl) {
		this.sendUrl = sendUrl;
	}

	public String getSendUrl() {
		return sendUrl;
	}

	public void setMd5Key(String md5Key) {
		this.md5Key = md5Key;
	}

	public String getMd5Key() {
		return md5Key;
	}
}

