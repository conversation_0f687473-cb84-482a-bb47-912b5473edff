//检查公司名称
function checkCompanyNameCommon(obj){
	 var reg = /^[^<>]{1,128}$/;
     if( reg.test(obj) )
         return true;
     return false;
}
//企业法人
function checkBussinessEntityCommon(obj){
	 var reg = /^[^<>]{1,128}$/;
     if( reg.test(obj) )
         return true;
     return false;
}
//检查手机号码
function checkMobileNoCommon(obj){
	var reg = "";
	if (typeof(validPhoneRex)==="undefined" || validPhoneRex == "" || validPhoneRex == null) {
		reg = /^1[34578]\d{9}$/;
	} else{
		reg = validPhoneRex;
	}
	if(reg.exec(obj)){
    	 return true;
    }
    return false;
}
//检查
function isEmailCommon(s){
	//var patn = /^[_a-zA-Z0-9\-]+(\.[_a-zA-Z0-9\-]*)*@[a-zA-Z0-9\-]+([\.][a-zA-Z0-9\-]+)+$/;
	//var reg = /^\w+((-\w+)(\.\w+))*\@[A-Za-z0-9]+((\.-)[A-Za-z0-9]+)*\.[A-Za-z0-9]+$/;	
	var reg = /^[_a-zA-Z0-9\-]+(\.[_a-zA-Z0-9\-]*)*@[a-zA-Z0-9\-]+([\.][a-zA-Z0-9\-]+)+$/;
	if(reg.exec(s)) return true;
	else return false;
}
//检查企业组织代码
function checkCompOrganizeCodeCommon(obj){

	var reg = /^[-a-zA-Z0-9]{10}$/;	
     if( reg.test(obj) ){
     	 return true;
     }
     return false;
}
//检查营业执照号,企业税务登记号 
function checkCompLicenceNoCommon(obj){
	 //var reg = /^[0-9X]{1,50}$/;
    var reg = /^[a-zA-Z0-9]{1,50}$/;
     if( reg.test(obj) ){
     	 return true;
     }
     return false;
}
//检查营业执照所在地
function checkCompLicenceAddrCommon(obj){
     if( obj.length<500 ){
     	 return true;
     }
     return false;
}
//检查联系地址
function checkCompAddrCommon(obj){
     if(obj.length<=128 ){
     	 return true;
     }
     return false;
}
function checkIphoneOrMobile(obj){
	var reg = "";
	if (typeof(validPhoneRex)==="undefined" || validPhoneRex == "" || validPhoneRex == null) {
		reg = /^1[34578]\d{9}$/;
	} else{
		reg = validPhoneRex;
	}
    var Iphone=/^(\d{3,4}-)?\d{7,8}(-\d{1,6})?$/;

    if(reg.exec(obj)||Iphone.test(obj)){
        return true;
    }
    return false;
}
//检查真实姓名
function checkRealNameCommon(obj){
	if( obj.length<=20 ){
    	 return true;
    }
    return false;
}
//检查联系人
function checkLinkUserCommon(obj){
     if( obj.length<=16 ){
     	 return true;
     }
     return false;
}
//检查联系电话
function checkLinkPhoneCommon(obj){

	var reg=/^(\d{3,4}-)?\d{7,8}(-\d{1,6})?$/;
     if(reg.test(obj) ){
     	 return true;
     }
     return false;
}
//检查传真
function checkFaxCommon(obj){
	var reg=/^\d{3}([-0-9]\d|\d[-0-9])\d{5,15}$/;
     if(reg.test(obj) ){
     	 return true;
     }
     return false;
}

//检查银行卡号
function checkCompBankNoCommon(obj){
	var reg = /^\d{6,25}$/;	
     if( reg.test(obj) ){
     	 return true;
     }
     return false;
}

function calLength(leng) {   
    var len = 0;   
    for (var i=0; i<leng.length; i++) {   
        if (leng.charCodeAt(i)>127 || leng.charCodeAt(i)==94) {   
            len += 2;   
        } else {   
            len ++;   
        }   
    }   
    return len;   
} 

//敏感词汇过滤
var regWord = /^.*(bitch|shit|falun|tianwang|cdjp|bignews|boxun|chinaliberal|chinamz|chinesenewsnet|cnd|creaders|dafa|dajiyuan|dfdz|dpp|falu|falundafa|flg|freechina|freenet|fuck|GCD|hongzhi|hrichina|huanet|hypermart|incest|jiangdongriji|lihongzhi|making|minghui|minghuinews|nacb|naive|nmis|paper|peacehall|playboy|renminbao|renmingbao|rfa|safeweb|sex|simple|svdc|taip|tibetalk|triangle|triangleboy|UltraSurf|unixbox|ustibet|voa|wangce|wstaiji|xinsheng|yuming|zhengjian|zhengjianwang|zhenshanren|zhuanfalun|xxx|anime|censor|hentai|hz|hz|av|av|sm|sm|porn|multimedia|toolbar|downloader|顶级|女優|小泽玛莉亚|强歼|乱交|色友|婊子|蒲团|美女|女女|喷尿|绝版|三級|武腾兰|凌辱|暴干|诱惑|阴唇|小泽圆|插插|坐交|長瀨愛|川島和津實|草莓牛奶|小澤園|飯島愛|星崎未來|及川奈央|朝河蘭|夕樹舞子|大澤惠|金澤文子|三浦愛佳|伊東|慰安妇|女教師|武藤蘭|学生妹|无毛|猛插|护士|自拍|A片|A级|喷精|偷窥|小穴|大片|被虐|黄色|被迫|被逼|强暴|口技|破处|精液|幼交|狂干|兽交|群交|叶子楣|舒淇|翁虹|大陆|露点|露毛|武藤兰|饭岛爱|波霸|少儿不宜|成人|偷情|叫床|上床|制服|亚热|援交|走光|情色|肉欲|美腿|自摸|18禁|捆绑|丝袜|潮吹|肛交|群射|内射|少妇|卡通|臭作|薄格|調教|近親|連發|限制|乱伦|母子|偷拍|更衣|無修正|一本道|1Pondo|櫻井|風花|夜勤病栋|菱恝|虐待|激情|麻衣|三级|吐血|三个代表|一党|多党|民主|专政|行房|自慰|吹萧|色狼|胸罩|内裤|底裤|私处|爽死|变态|妹疼|妹痛|弟疼|弟痛|姐疼|姐痛|哥疼|哥痛|同房|打炮|造爱|作爱|做爱|鸡巴|阴茎|阳具|开苞|肛门|阴道|阴蒂|肉棍|肉棒|肉洞|荡妇|阴囊|睾丸|捅你|捅我|插我|插你|插她|插他|干你|干她|干他|射精|口交|屁眼|阴户|阴门|下体|龟头|阴毛|避孕套|你妈逼|大鸡巴|高潮|政治|大法|弟子|大纪元|真善忍|明慧|洪志|红志|洪智|红智|法轮|法论|法沦|法伦|发轮|发论|发沦|发伦|轮功|轮公|轮攻|沦功|沦公|沦攻|论攻|论功|论公|伦攻|伦功|伦公|打倒|民运|六四|台独|王丹|柴玲|李鹏|天安门|江泽民|朱容基|朱镕基|李长春|李瑞环|胡锦涛|魏京生|台湾独立|藏独|西藏独立|疆独|新疆独立|警察|民警|公安|邓小平|毛泽东|大盖帽|革命|武警|黑社会|交警|消防队|刑警|夜总会|妈个|公款|首长|书记|坐台|腐败|城管|暴动|暴乱|李远哲|司法警官|高干|人大|尉健行|李岚清|黄丽满|于幼军|文字狱|宋祖英|自焚|骗局|猫肉|吸储|张五常|张丕林|空难|温家宝|吴邦国|曾庆红|黄菊|罗干|吴官正|贾庆林|专制|三個代表|一黨|多黨|專政|大紀元|紅志|紅智|法輪|法論|法淪|法倫|發輪|發論|發淪|發倫|輪功|輪公|輪攻|淪功|淪公|淪攻|論攻|論功|論公|倫攻|倫功|倫公|民運|台獨|李鵬|天安門|江澤民|朱鎔基|李長春|李瑞環|胡錦濤|臺灣獨立|藏獨|西藏獨立|疆獨|新疆獨立|鄧小平|大蓋帽|黑社會|消防隊|夜總會|媽個|首長|書記|腐敗|暴動|暴亂|李遠哲|高幹|李嵐清|黃麗滿|於幼軍|文字獄|騙局|貓肉|吸儲|張五常|張丕林|空難|溫家寶|吳邦國|曾慶紅|黃菊|羅幹|賈慶林|專制|八九|八老|巴赫|白立朴|白梦|白皮书|保钓|鲍戈|鲍彤|暴政|北大三角地论坛|北韩|北京当局|北京之春|北美自由论坛|博讯|蔡崇国|曹长青|曹刚川|常劲|陈炳基|陈军|陈蒙|陈破空|陈希同|陈小同|陈宣良|陈一谘|陈总统|程凯|程铁军|程真|迟浩田|持不同政见|赤匪|赤化|春夏自由论坛|达赖|大参考|大纪元新闻网|大纪园|大家论坛|大史|大史记|大史纪|大中国论坛|大中华论坛|大众真人真事|戴相龙|弹劾|登辉|邓笑贫|迪里夏提|地下教会|地下刊物|第四代|电视流氓|钓鱼岛|丁关根|丁元|丁子霖|东北独立|东方红时空|东方时空|东南西北论谈|东社|东土耳其斯坦|东西南北论坛|动乱|独裁|独夫|独立台湾会|杜智富|多维|屙民|俄国|发愣|发正念|反封锁技术|反腐败论坛|反攻|反共|反人类|反社会|方励之|方舟子|飞扬论坛|斐得勒|费良勇|分家在|分裂|粉饰太平|风雨神州|风雨神州论坛|封从德|封杀|冯东海|冯素英|佛展千手法|付申奇|傅申奇|傅志寰|高官|高文谦|高薪养廉|高瞻|高自联|戈扬|鸽派|歌功颂德|蛤蟆|个人崇拜|工自联|功法|共产|共党|共匪|共狗|共军|关卓中|贯通两极法|广闻|郭伯雄|郭罗基|郭平|郭岩华|国家安全|国家机密|国军|国贼|韩东方|韩联潮|何德普|何勇|河殇|红灯区|红色恐怖|宏法|洪传|洪吟|洪哲胜|胡紧掏|胡锦滔|胡锦淘|胡景涛|胡平|胡总书记|护法|花花公子|华建敏|华通时事论坛|华夏文摘|华语世界论坛|华岳时事论坛|黄慈萍|黄祸|黄菊　|黄翔|回民暴动|悔过书|鸡毛信文汇|姬胜德|积克馆|基督|贾廷安|贾育台|建国党|江core|江八点|江流氓|江罗|江绵恒|江青|江戏子|江则民|江泽慧|江贼|江贼民|江折民|江猪|江猪媳|江主席|姜春云|将则民|僵贼|僵贼民|讲法|酱猪媳|交班|教养院|接班|揭批书|金尧如|锦涛|禁看|经文|开放杂志|看中国|抗议|邝锦文|劳动教养所|劳改|劳教|老江|老毛|黎安友|李大师|李登辉|李红痔|李宏志|李洪宽|李继耐|李兰菊|李老师|李录|李禄|李少民|李淑娴|李旺阳|李文斌|李小朋|李小鹏|李月月鸟|李志绥|李总理|李总统|连胜德|联总|廉政大论坛|炼功|梁光烈|梁擎墩|两岸关系|两岸三地论坛|两个中国|两会|两会报道|两会新闻|廖锡龙|林保华|林长盛|林樵清|林慎立|凌锋|刘宾深|刘宾雁|刘刚|刘国凯|刘华清|刘俊国|刘凯中|刘千石|刘青|刘山青|刘士贤|刘文胜|刘晓波|刘晓竹|刘永川|流亡|龙虎豹|陆委会|吕京花|吕秀莲|抡功|轮大|罗礼诗|马大维|马良骏|马三家|马时敏|卖国|毛厕洞|毛贼东|美国参考|美国之音|蒙独|蒙古独立|密穴|绵恒|民国|民进党|民联|民意|民意论坛|民阵|民猪|民主墙|民族矛盾|莫伟强|木犀地|木子论坛|南大自由论坛|闹事|倪育贤|你说我说论坛|潘国平|泡沫经济|迫害|祁建|齐墨|钱达|钱国梁|钱其琛|抢粮记|乔石|亲美|钦本立|秦晋|轻舟快讯|情妇|庆红|全国两会|热比娅|热站政论网|人民报|人民内情真相|人民真实|人民之声论坛|人权|瑞士金融大学|善恶有报|上海帮|上海孤儿院|邵家健|神通加持法|沈彤|升天|盛华仁|盛雪|师父|石戈|时代论坛|时事论坛|世界经济导报|事实独立|双十节|水扁|税力|司马晋|司马璐|司徒华|斯诺|四川独立|宋平|宋书元|苏绍智|苏晓康|台盟|台湾狗|台湾建国运动组织|台湾青年独立联盟|台湾政论区|台湾自由联盟|太子党|汤光中|唐柏桥|唐捷|滕文生|天怒|天葬|童屹|统独|统独论坛|统战|屠杀|外交论坛|外交与方略|万润南|万维读者论坛|万晓东|汪岷|王宝森|王炳章|王策|王超华|王辅臣|王刚|王涵万|王沪宁|王军涛|王力雄|王瑞林|王润生|王若望|王希哲|王秀丽|王冶坪|网特|魏新生|温元凯|文革|无界浏览器|吴百益|吴方城|吴弘达|吴宏达|吴仁华|吴学灿|吴学璨|吾尔开希|五不|伍凡|西藏|洗脑|项怀诚|项小吉|小参考|肖强|邪恶|谢长廷|谢选骏|谢中之|辛灏年|新观察论坛|新华举报|新华内情|新华通论坛|新生网|新闻封锁|新语丝|信用危机|邢铮|熊炎|熊焱|修炼|徐邦秦|徐才厚|徐匡迪|徐水良|许家屯|薛伟|学潮|学联|学习班|学运|学自联|雪山狮子|严家其|严家祺|阎明复|颜射|央视内部晚会|杨怀安|杨建利|杨巍|杨月清|杨周|姚月谦|夜话紫禁城|一中一台|义解|亦凡|异见人士|异议人士|易丹轩|易志熹|尹庆民|由喜贵|游行|幼齿|于大海|于浩成|余英时|舆论|舆论反制|宇明网|圆满|远志明|岳武|在十月|则民|择民|泽民|贼民|曾培炎|张伯笠|张钢|张宏堡|张健|张林|张万年|张伟国|张昭富|张志清|赵海青|赵南|赵品潞|赵晓微|赵紫阳|哲民|真相|真象|镇压|争鸣论坛|正见网|正义党论坛|郑义|包夜|冰火|插B|操B|处女|打飞机|风骚|黄色电影|激情视频|叫春|狂插|狂操|狂搞|露乳|裸聊|裸体|屁股|强奸|三级片|色情|脱光|脱衣|性爱|性感|性高潮|性交|胸部|艳舞|一夜情|欲望|操你|你他妈|傻逼|傻B|TMD|TNND|TND|法轮功|江氏|李洪志|新唐人|淫靡|淫水|六四事件|迷药|迷昏药|窃听器|六合彩|买卖枪支|退党|三唑仑|麻醉药|麻醉乙醚|短信群发器|帝国之梦|毛一鲜|黎阳平|色情服务|对日强硬|出售枪支|摇头丸|西藏天葬|鬼村|军长发威|PK黑社会|恶搞晚会|枪决女犯|投毒杀人|强硬发言|出售假币|监听王|昏药|侦探设备|麻醉钢枪|反华|官商勾结|升达毕业证|手机复制|戴海静|自杀指南|自杀手册|张小平|佳静安定片|蒙汗药粉|古方迷香|强效失意药|迷奸药|透视眼镜|远程偷拍|自制手枪|子女任职名单|激情小电影|黄色小电影|色情小电影|天鹅之旅|盘古乐队|高校暴乱|高校群体事件|大学骚乱|高校骚乱|催情药|拍肩神药|春药|窃听器材|身份证生成器|枪决现场|出售手枪|麻醉枪|办理证件|办理文凭|疆独藏独|高干子弟|高干子女|枪支弹药|血腥图片|反政府|禁书|特码|成人片|成人电影|换妻|发票|增值税|迷幻药|隐形|耳机|法 轮 功|李 宏 志|真 善 忍|宏志|杀死|奖|发抡|拉登|拉丹|法抡|法囵|法仑|法纶|发仑|发囵|国研新闻邮件|自由运动|法轮大法|淫秽|E周刊|龙卷风|正法|三陪|嫖娼|静坐|政变|造反|独立|发轮功|功友|人民大众时事参考|示威|罢工|大法弟子|印尼伊斯兰祈祷团|中俄边界新约|政治运动|压迫|非典|共产党|反革命|十六大|江独裁|台湾|东突厥斯坦伊斯兰运动|一边一国|回民|中华民国|政治风波|古怪歌|突厥斯坦|简鸿章|联总之声传单|人民报讯|东突|人民真实报道|教徒|推翻|小灵通|操你奶奶|操你妈|fa lun|IP17908|fa|falong|陈水扁|主席|改革|他妈的|人民真实报导|开放|杂志|中俄|边界新约|（诽闻）|无能|印尼依斯兰祈祷团|东突厥斯坦依斯兰运动|本拉登|维吾尔自由运动|国际地质科学联合会|中国民主正义党|www\.cdjp\.org|民主中国|www\.chinamz\.org|中国民主同盟|支联会|天安门母亲|张戎|西藏流亡政府|邓力群|龙新民|www\.bignews\.org|www\.boxun\.com|也就是博讯|www\.cnd\.org|www\.chinesenewsnet\.com|纪元|www\.dajiyuan\.com|大纪元时报|自由亚洲|www\.rfa\.org |www\.renminbao\.com|维基百科|zh\.wikipedia\.org|根敦\.确吉尼玛|根敦\.确吉|确吉尼玛 |西藏论坛|www\.tibetalk\.com|破网软件|无界|自由门|花园网|我的奋斗).*$/ig;
function isSensitive(word){
	var ret=word.match(regWord);
	
	if(ret!=null){
		return true
	}else{
		return false ;
	}	
}

/**
 * 验证真实姓名
 */
function validateRealName(s){
	var reg = /^[\u4e00-\u9fa5]+$/;
    if(calLength(s)<4 || calLength(s)>20) {
		return false;
	} else if (!reg.exec(s)) {
			return false;
	} else {
		return true;
	}
}
//检查网吧业主名称
function validateCafeActualityRealName(s){
	var reg = /^[\u4e00-\u9fa5|\u00b7]+$/;
	if(calLength(s)<4 || calLength(s)>40) {
		return false;
	} else if (!reg.exec(s)) {
		return false;
	} else {
		return true;
	}
}

/*
功能：验证身份证号码是否有效
提示信息：未输入或输入身份证号不正确！
使用：validateIdCard(obj)
返回：0,1,2,3
*/
function validateIdCard(obj)
{
	var aCity={11:"北京",12:"天津",13:"河北",14:"山西",15:"内蒙古",21:"辽宁",22:"吉林",23:"黑龙江",31:"上海",32:"江苏",33:"浙江",34:"安徽",35:"福建",36:"江西",37:"山东",41:"河南",42:"湖北",43:"湖南",44:"广东",45:"广西",46:"海南",50:"重庆",51:"四川",52:"贵州",53:"云南",54:"西藏",61:"陕西",62:"甘肃",63:"青海",64:"宁夏",65:"新疆",71:"台湾",81:"香港",82:"澳门",91:"国外"};
    var iSum = 0;
	//var info = "";
	var strIDno = obj;
	var idCardLength = strIDno.length;
	if(!/^\d{17}(\d|x)$/i.test(strIDno))
        return 1; //非法身份证号

	if(aCity[parseInt(strIDno.substr(0,2))]==null)
	return 2;// 非法地区

       // 判断是否大于2078年，小于1900年
       var year =strIDno.substring(6,10);
       if (year<1900 || year>2078 )
           return 3;//非法生日

    //18位身份证处理

   //在后面的运算中x相当于数字10,所以转换成a
    strIDno = strIDno.replace(/x$/i,"a");

		sBirthday=strIDno.substr(6,4)+"-"+Number(strIDno.substr(10,2))+"-"+Number(strIDno.substr(12,2));
		var d = new Date(sBirthday.replace(/-/g,"/"))
		if(sBirthday!=(d.getFullYear()+"-"+ (d.getMonth()+1) + "-" + d.getDate()))
                return 3; //非法生日
    // 身份证编码规范验证
		for(var i = 17;i>=0;i --)
			iSum += (Math.pow(2,i) % 11) * parseInt(strIDno.charAt(17 - i),11);
		if(iSum%11!=1)
                return 1;// 非法身份证号

   // 判断是否屏蔽身份证
    var words = new Array();
    words = new Array("11111119111111111","12121219121212121");

    for(var k=0;k<words.length;k++){
        if (strIDno.indexOf(words[k])!=-1){
            return 1;
        }
    }

	return 0;
}

//15位转18位中,计算校验位即最后一位
function GetVerifyBit(id){
    var result;
    var nNum=eval(id.charAt(0)*7+id.charAt(1)*9+id.charAt(2)*10+id.charAt(3)*5+id.charAt(4)*8+id.charAt(5)*4+id.charAt(6)*2+id.charAt(7)*1+id.charAt(8)*6+id.charAt(9)*3+id.charAt(10)*7+id.charAt(11)*9+id.charAt(12)*10+id.charAt(13)*5+id.charAt(14)*8+id.charAt(15)*4+id.charAt(16)*2);
    nNum=nNum%11;
    switch (nNum) {
       case 0 :
          result="1";
          break;
       case 1 :
          result="0";
          break;
       case 2 :
          result="X";
          break;
       case 3 :
          result="9";
          break;
       case 4 :
          result="8";
          break;
       case 5 :
          result="7";
          break;
       case 6 :
          result="6";
          break;
       case 7 :
          result="5";
          break;
       case 8 :
          result="4";
          break;
       case 9 :
          result="3";
          break;
       case 10 :
          result="2";
          break;
    }
    //document.write(result);
    return result;
}

//上传原始图片
function doUpload(){   
    var image = document.getElementById("imageFile").value;
    if(image == ""){
         showInfo("请选择图片！");
         return false;
    }
    
    var reg = /(\.gif|\.png|\.jpg|\.jpeg)$/i;
    var rs = image.match(reg);
    if(rs == null || rs[0] == null){
        showInfo("图片格式仅支持jpg,jpeg,gif和png。");
        return false;
    }
    
    if(! checkFlashVersion()){
        return false;
    }
    
    document.forms[0].submit();
}


//与当前日期进行比较
function checkDate(obj){
	var d1Arr=obj.split('-');
	var ss1=new Date(d1Arr[0],d1Arr[1],d1Arr[2]);	
	var myDate = new Date();
	var year=myDate.getFullYear();
	var month=myDate.getMonth()+1;
	var date=myDate.getDate();
	var ss2=new Date(year,month,date);
	if(ss1<=ss2){
	 return false;
	}
	return true;
}

//与当前日期进行比较（3个月期限）
function checkDateForCafe(obj){
    var d1Arr=obj.split('-');
    var ss1=new Date(d1Arr[0],d1Arr[1],d1Arr[2]);
    var myDate = new Date();
    var year=myDate.getFullYear();
    var month=myDate.getMonth()+1;
    var date=myDate.getDate();
    var ss2=new Date(year,month,date);
    if((ss1-ss2)/(1000*3600*24)+1<90){
        return false;
    }
    return true;
}

//判断是否是汉字、字母、数字组成
function f_check_zhornumorlett(str){
    var regu = /^[0-9a-zA-Z\u4e00-\u9fa5]+$/; 
    if (regu.test(str)) {
      return true;
    }
    return false;
}

function f_check_companyname(str){
    var regu = /^[0-9a-zA-Z\u4e00-\u9fa5()（）]+$/;
    if (regu.test(str)) {
      return true;
    }
    return false;
}





//判断是否是汉字、字母、数字、下划线组成
function f_check_zhornumorlett_(str){
    var regu = /^(([0-9a-zA-Z\u4e00-\u9fa5])|(-))+$/; 
    if (regu.test(str)) {
      return true;
    }
    return false;
}

//判断是否是汉字、字母、数字和点号
function f_check_zhornumorlettdot(str){
	var regu = /^[0-9a-zA-Z\u4e00-\u9fa5|\u00b7]+$/;
	if (regu.test(str)) {
		return true;
	}
	return false;
}
