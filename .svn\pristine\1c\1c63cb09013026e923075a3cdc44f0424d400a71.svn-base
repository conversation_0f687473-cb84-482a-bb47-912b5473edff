var $form=$(".form-box"),$mobile=$form.find("input[name=mobile]"),$mobileActiveNo=$form.find("input[name='mobileActiveNo']"),$password=$form.find("input[name='passwordShow']"),$submit=$("#reg"),$sentcode=$("#sent-code"),$pwdshow=$("#password-show"),$tips=$(".tips-error"),$checkbox=$("#check-box");$realName=$("#realName");$idCard=$("#idCard");var MSG={C1001:"请输入您的手机号码",C1002:"手机号码格式错误，请重新输入",C1003:"请设置登录密码",C1004:"密码长度应在6~16个字符之间",C1005:"请输入手机短信验证码",C1006:"密码可包含数字、字母、标点符号",C1007:"密码不能全为同一字符",C1008:"请输入真实姓名",C1009:"请输入身份证号码",C1010:"真实姓名请使用中文，2-10个汉字",C1011:"身份证号码不合法",C1012:"注册用户须年满18周岁"};var CONSTANT={SEC:60};$pwdshow.on("click",function(){if($pwdshow.prop("checked")){$password.attr("type","text")}else{$password.attr("type","password")}});function showTips(msg){$tips.find("p").html(msg);$tips.show()}$checkbox.on("click",function(){if($checkbox.is(":checked")){$submit.removeAttr("disabled")}else{$submit.attr("disabled","disabled")}});function isMobileNo(s){var reg = "";if (typeof(validPhoneRex)==="undefined" || validPhoneRex == "" || validPhoneRex == null){reg = /^1[34578]\d{9}$/;}else{reg = validPhoneRex;} return reg.test(s)}function time(wait){if(wait<=0){$sentcode.removeClass("disabled").addClass("btn-primary");$sentcode.text("发送验证码");wait=60}else{$sentcode.removeClass("btn-primary").addClass("disabled");$sentcode.text(wait+"秒后重新获取");wait--;setTimeout(function(){time(wait)},1000)}}$sentcode.on("click",function(){var mobile=$mobile.val();var h5RegCheckCode = "";if (needSmsCheckCode == "true"){h5RegCheckCode = $("#h5RegCheckCode").val();if (!h5RegCheckCode){showTips("请先输入验证码");return false;}} if(mobile.length==0){showTips(MSG.C1001);return false}if(!isMobileNo(mobile)){showTips(MSG.C1002);return false}$.ajax({url:"/front/swpaysdk/sendRegActiveNo.htm",type:"POST",data:{"mobile":mobile,"h5RegCheckCode":h5RegCheckCode,"h5RegCheckCodeSiteId":h5RegCheckCodeSiteId},dataType:"json",success:function(json){if(!json.result&&json.msg){showTips(json.msg)}if(json.result){time(CONSTANT.SEC);try{window.sessionStorage.setItem(getKey("_reg_"),new Date().getTime());window.sessionStorage.setItem(getKey("_sendcode_"),"true");window.sessionStorage.setItem(getKey("_regmobile_"),mobile)}catch(e){console.error(e)}$mobileActiveNo.removeAttr("disabled");$tips.hide()}}})});function getKey(str){var pv=$password.val();return str+"_"+pv}$form.on("submit",function(){if(validate()){$("#password").val(hex_md5($password.val()));return true}$("#password").val("");return false});function validate(){var msg=$(".tips-error p").text();if(msg){$(".tips-error").show()}var uv=$.trim($mobile.val());if(uv.length==0){showTips(MSG.C1001);return false}var reg = "";if (!validPhoneRex){reg = /^1[34578]\d{9}$/;}else{reg = validPhoneRex;} if(!reg.test(uv)){showTips(MSG.C1002);return false}var mv=$.trim($mobileActiveNo.val());if(mv.length==0){showTips(MSG.C1005);return false}var pv=$.trim($password.val());if(pv.length==0){showTips(MSG.C1003);return false}if(pv.length<6||pv.length>16){showTips(MSG.C1004);return false}if(!/^[A-Za-z0-9`~!@#\$%\^&\*\(\)\-_=\+\[\{\]\}\\\|;:'"",<.>\/\?]+$/.test(pv)){showTips(MSG.C1006);return false}if(/^(\w)\1+$/.test(pv)){showTips(MSG.C1007);return false}var realName=$.trim($realName.val());if($realName.length>0){if(realName.length==0){showTips(MSG.C1008);return false}if(realName.length<2||realName.length>10){showTips(MSG.C1010);return false}if(!/^[\u4e00-\u9fa5]{2,10}$/.test(realName)){showTips(MSG.C1010);return false}}if($idCard.length>0){var idCard=$.trim($idCard.val());if(idCard.length==0){showTips(MSG.C1009);return false}var result=validateIdCard($idCard[0]);if(result==1){showTips(MSG.C1011);return false}else{if(result==3){showTips(MSG.C1011);return false}else{if(result==4){showTips(MSG.C1012);return false}}}}return true}function init(){if($("#errorMsg").val()!=""){$(".tips-error").show()}var send;try{$mobile.val(window.sessionStorage.getItem(getKey("_regmobile_")));send=window.sessionStorage.getItem(getKey("_sendcode_"))}catch(e){console.error(e)}if(send=="true"){$mobileActiveNo.removeAttr("disabled")}else{$mobileActiveNo.attr("disabled","true")}if($tips.find("p").html()==""){$tips.hide()}var sentTime;try{sentTime=window.sessionStorage.getItem(getKey("_reg_"))}catch(e){console.error(e)}var interval=Math.round((new Date().getTime()-sentTime)/1000);if(interval<60){wait=(60-interval);time(wait)}}init();