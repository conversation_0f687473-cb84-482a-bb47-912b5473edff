<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd" >
<sqlMap namespace="com.shunwang.basepassport.weixin.pojo.WxWorkUserBind">
    <typeAlias alias="wxWorkUserBind" type="com.shunwang.basepassport.weixin.pojo.WxWorkUserBind"/>
    <resultMap id="BaseResultMap" class="wxWorkUserBind">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="type" column="type" jdbcType="INTEGER"/>
        <result property="externalId" column="external_id" jdbcType="VARCHAR"/>
        <result property="memberId" column="member_id" jdbcType="INTEGER"/>
        <result property="scene" column="scene" jdbcType="VARCHAR"/>
        <result property="timeAdd" column="time_add" jdbcType="TIMESTAMP"/>
        <result property="timeEdit" column="time_edit" jdbcType="TIMESTAMP"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="baseColumn">
        id,
        type,
        external_id,
        member_id,
        scene,
        remark,
        time_add,
        time_edit
    </sql>

    <!-- 添加记录 -->
    <insert id="insert" parameterClass="wxWorkUserBind" >
        INSERT INTO personal_wx_work_user_bind(
            type,
            external_id,
            member_id,
            scene,
            remark,
            time_add,
            time_edit)
        VALUES (#type#,
                #externalId#,
                #memberId#,
                #scene#,
                #remark#,
                NOW(),
                NOW())
    </insert>

    <select id="getByExternalIdAndType" resultMap="BaseResultMap" parameterClass="wxWorkUserBind">
        SELECT
        <include refid="baseColumn"/>
        FROM personal_wx_work_user_bind
        WHERE  external_id = #externalId# and type = #type#
        limit 1
    </select>

    <select id="getByMemberIdAndType" resultMap="BaseResultMap" parameterClass="wxWorkUserBind">
        SELECT
        <include refid="baseColumn"/>
        FROM personal_wx_work_user_bind
        WHERE  member_id = #memberId# and type = #type#
        limit 1
    </select>

    <select id="get" resultMap="BaseResultMap" parameterClass="java.lang.Integer">
        SELECT
        <include refid="baseColumn"/>
        FROM personal_wx_work_user_bind
        WHERE  id = #id#
    </select>

    <update id="update" parameterClass="wxWorkUserBind">
        update personal_wx_work_user_bind
        set time_edit = now()
        <isNotEmpty property="scene" prepend=",">
            scene=#scene#
        </isNotEmpty>
        <isNotNull property="memberId" prepend=",">
            member_id = #memberId#
        </isNotNull>
        <isNotEmpty property="remark" prepend=",">
            remark=#remark#
        </isNotEmpty>
        <isNotNull property="type" prepend=",">
            type = #type#
        </isNotNull>
        where id = #id#
    </update>

    <update id="updateByScene" parameterClass="wxWorkUserBind">
        update personal_wx_work_user_bind
        set time_edit = NOW()
        <isNotNull property="memberId" prepend=",">
            member_id = #memberId#
        </isNotNull>
        where scene = #scene# and time_edit &gt; now() - interval 1 day
    </update>

</sqlMap>
