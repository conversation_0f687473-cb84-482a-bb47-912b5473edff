package com.shunwang.baseStone.sso.weixin.task;

import com.shunwang.baseStone.cache.CacheKeyConstant;
import com.shunwang.baseStone.context.RedisContext;
import com.shunwang.baseStone.sso.weixin.pojo.WeixinQrcode;
import com.shunwang.sms.utils.SMSInnerSenderUtil;
import com.shunwang.util.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class Alarm<PERSON>ob implements Runnable{

    private static final Logger logger = LoggerFactory.getLogger(AlarmJob.class);

    private String appId;

    public AlarmJob(String appId) {
        this.appId = appId;
    }

    @Override
    public void run() {
        String[] nums = getNoticeNums();
        for (String num : nums) {
            SMSInnerSenderUtil.sendMsg(num, "appid:" + appId + " 接口请求达到上限，请立即清零");
        }
    }

    /**
     * 微信异常报警模块，目前监听45009异常
     * {"errcode":45009,"errmsg":"reach max api daily quota limit rid: 61da75fb-74ab3f2b-62866569"}
     * @param weixinQrcode
     * @param appId
     */
    private void doAlarm(WeixinQrcode weixinQrcode, String appId) {

    }

    /**
     * 读取报警手机号
     * @return
     */
    private String[] getNoticeNums() {
        String value = RedisContext.getResourceCache().getResourceValue(CacheKeyConstant.ConfigResourcesConstants.BAR_LOGIN_CONFIG,
                CacheKeyConstant.ConfigResourcesConstants.WEIXIN_DAILY_LIMIT_ALARM_NUMS);
        if (StringUtil.isNotBlank(value)) {
            return value.split("\\|");
        }
        return new String[]{};
    }


}
