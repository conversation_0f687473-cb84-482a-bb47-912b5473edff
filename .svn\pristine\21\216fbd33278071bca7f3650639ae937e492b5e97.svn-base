$import('SFGridView');
$import('SFGrid');
$import('SFInput');
$import('ValidateRule');
$import('SFDirect');
$import('SFSelect');

var CategoryView = $createClass('CategoryView',function(){
	this.pk =  new SFInput({field:'categoryid'
		,name:'作业单元ID'
		,rules:[
			new CheckPlusInt(),
			new CheckEmpty(),
			new CheckMaxLength({length:9})
		]});
	this.pojoControls = [
		this.pk,
		new SFInput({field:'categoryname'
			,name:'作业单元名称'
			,rules:[
			new CheckEmpty(),
			new CheckMaxLength({length:64})
		]}),
		new SFSelect({field:'businessCategoryId','name': "部门:",
			needdefault:true,
			defaultValue:0,
			items:BussinessUtil.getBusinessCategoryItems(),
			rules:[
				new CheckEmpty()
			]
		}),
		new SFInput({field:'smsAppid'
			,name:'短信AppId'
			,rules:[
				new CheckEmpty(),
				new CheckMaxLength({length:64})
			]}),
		new SFInput({field:'smsSecret'
			,name:'短信Secret'
			,rules:[
				new CheckEmpty(),
				new CheckMaxLength({length:256})
			]}),
		new SFInput({field:'remark'
			,name:'备注'
			,rules:[
			new CheckMaxLength({length:100})
		]})
	];
	this.schControl =  [
		new SFInput({field:'schCategoryName',name:'名称'})
	];
	this.leftBtns = [this.createAddBtn()];
	this.SFGridView();
},'SFGridView');

CategoryView.prototype.buildGrid = function(){
	var self = this;
	var grid = new SFGrid({
		url:'listCategory.do',
		col:[
			{id:'categoryid',text:'作业单元ID'},
			{id:'categoryname',text:'作业单元名称'},
			{id:'businessCategoryName',text:'部门名称'},
			{id:'count',text:'应用总数'},
			{id:'smsAppid',text:'短信Appid'},
			{id:'useradd',text:'添加用户'},
			{id:'timeadd',text:'添加时间'},
			{id:'useredit',text:'修改用户'},
			{id:'timeedit',text:'修改时间'},
			{id:'remark',text:'备注'}],
		linebutton:[
			this.createUpdateBtn(),
			{
				text:'登录配置',
				onclick:function(pojo){
					top.location="../loginElement/loginElement.jsp?categoryId="+pojo.categoryid+'&categoryName='+pojo.categoryname;
				}
			}
		]
	});
	return grid;
}
