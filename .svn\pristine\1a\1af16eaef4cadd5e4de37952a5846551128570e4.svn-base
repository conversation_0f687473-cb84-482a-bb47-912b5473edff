<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ include file="/common/taglibs.jsp"%>
<!DOCTYPE html>
<html>
<head>
	<title>${member.isBindMobile?"换绑":"绑定"}手机</title>

</head>
<body>
<content tag="header_title">${member.isBindMobile?"换绑":"绑定"}手机</content>
<c:if test="${member.isBindMobile}">
	<div class="nav">
		<ul>
			<li class="active">选择验证方式</li>
			<li class="active">安全验证</li>
			<li>换绑手机</li>
		</ul>
	</div>
</c:if>
<div class="tips-error" style="display: none;">
	<p>${msg}</p>
</div>
<form action="${appServer}/front/swpaysdk/doBindMobile.htm" method="post" class="form-box" id="js-form">
	<div class="form-group inline-group">
		<input type="hidden" name="tokenid" value="${tokenid}" />
		<input type="hidden" name="source" value="${source}" />
		<input type="tel" name="newNumber" placeholder="请输入要绑定的手机" class="form-control" value="${newNumber}">
		<button type="button" id="sent-code" class="btn btn-primary btn-mini" id="sent-code">发送验证码</button></div>
	<div class="form-group"><input type="tel" name="activeNo" placeholder="请输入手机短信中的验证码" class="form-control"></div>
	<div class="other-group btn-box"><button type="submit" id="verity" class="btn btn-primary">确认绑定</button></div>
</form>
<content tag="scripts">
	<script type="text/javascript" src="${staticServer}/scripts/front/swpaysdk/member/src/mobile.js"></script>
	<script src="${staticServer}/scripts/front/member/gt.js" type="text/javascript" ></script>
	<script src="${staticServer}/scripts/front/member/gtUtil.js" type="text/javascript" ></script>
	<script>
		//以下极验业务---------------------------
		var showGt = "true" == '${showGt}';
		var gtRegisterUrl = "/front/common/gtRegister.htm?r=" + new Date().getTime();
		var sendGt = $.gtUtil({
					"showGt": showGt,
					"formId": "js-form",
					"gtRegisterUrl": gtRegisterUrl,
					"btnId": "sent-code"
				},
				function () {
					sendCode();
				},
				function() {
					return checkMobile();
				},
				function (msg) {
					$('#tips-error p').html(msg);
					$('#tips-error').show();
				}
		);
	</script>
</content>
</body>

</html>