package com.shunwang.baseStone.sso.filter;

import com.shunwang.baseStone.siteinterface.context.SiteContext;
import com.shunwang.basepassport.config.pojo.SiteInterface;
import com.shunwang.basepassport.config.common.SiteInterfaceUtil;
import com.shunwang.util.encrypt.Md5Encrypt;
import com.shunwang.util.encrypt.SignTool;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Objects;

import static com.shunwang.baseStone.core.util.SignTool.PARAM_SPLIT;

/**
 * 第三方登录
 */
public class OutOuathFilter implements Filter{

	private static final Logger logger = LoggerFactory.getLogger(OutOuathFilter.class);

	@Override
	public void destroy() {
		
	}

	@Override
	public void doFilter(ServletRequest req, ServletResponse resp,
			FilterChain chain) throws IOException, ServletException {
		
		HttpServletRequest request = (HttpServletRequest)req;
		HttpServletResponse response = (HttpServletResponse)resp;
		//签名校验
		String siteId = request.getParameter("site_id");
		String sign = request.getParameter("sign");
		if (Objects.isNull(siteId) || Objects.isNull(sign)) {
			response.getWriter().write("{\"stage\":\"error\",\"errorMsg\":\"siteId|sign不能为空\"}");
			return;
		}
		SiteContext.setSiteId(siteId);
		SiteContext.setSiteName("SSO");
		SiteInterface siteInterface = SiteInterfaceUtil.loadSiteInterface();
		if (Objects.isNull(siteInterface)) {
			response.getWriter().write("{\"stage\":\"error\",\"errorMsg\":\"siteId无接口权限\"}");
			return;
		}
		String md5Key = siteInterface.getMd5Key();
		String signSource = SignTool.buildSignStringSorted(request, "sign");
		String signStr = Md5Encrypt.encrypt(signSource + PARAM_SPLIT + md5Key).toUpperCase();
		logger.info("签名明文:" + signSource + "|md5Key, 签名结果: " + signStr);
		if (!sign.equalsIgnoreCase(signStr)) {
			response.getWriter().write("{\"stage\":\"error\",\"errorMsg\":\"签名不匹配\"}");
			return;
		}

		chain.doFilter(req, resp);
	}

	@Override
	public void init(FilterConfig arg0) throws ServletException {
		
	}

}
