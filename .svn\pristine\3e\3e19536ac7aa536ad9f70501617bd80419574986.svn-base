package com.shunwang.basepassport.binder.web.bind.processor;

import com.shunwang.basepassport.binder.pojo.SendBinder;
import com.shunwang.basepassport.binder.processor.Processor;
import com.shunwang.basepassport.binder.processor.ProcessorContext;
import com.shunwang.basepassport.binder.web.send.bean.MobileBindBean;

/**
 * 默认其他情况
 *
 * <AUTHOR>
 * @date 2018/12/18
 **/
public class DefaultProcessor implements Processor {
    @Override
    public boolean matches(ProcessorContext context) {
        SendBinder sendBinder = context.getSendBinder();
        MobileBindBean bean = (MobileBindBean) context.getT();

        return sendBinder.isCouldBind(bean.getNumber()) || sendBinder.isMemberBindNumber(bean.getNumber());
    }

    @Override
    public boolean doProcess(ProcessorContext context) {
        SendBinder sendBinder = context.getSendBinder();
        MobileBindBean bean = (MobileBindBean) context.getT();

        //当为绑定或更换操作时，number不能被其他用户绑定，当为找回时，必须要绑定number
        sendBinder.setNumber(bean.getNumber());
        sendBinder.validate(bean.getActiveNo());
        sendBinder.bind();

        return false;
    }
}
