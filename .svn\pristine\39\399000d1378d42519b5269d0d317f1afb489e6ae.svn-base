<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd" >
<sqlMap namespace="com.shunwang.basepassport.config.pojo.ConfigOneLogin" >
    <typeAlias alias="configOneLogin" type="com.shunwang.basepassport.config.pojo.ConfigOneLogin"/>
    <resultMap id="BaseResultMap" class="configOneLogin" >
        <result column="id" property="id" jdbcType="INTEGER" />
        <result column="terminal_type" property="terminalType" jdbcType="INTEGER" />
        <result column="channel" property="channel" jdbcType="INTEGER" />
        <result column="site_id" property="siteId" jdbcType="VARCHAR" />
        <result column="md5_key" property="md5Key" jdbcType="VARCHAR" />
        <result column="appid" property="appid" jdbcType="VARCHAR" />
        <result column="style" property="style" jdbcType="VARCHAR" />
        <result column="time_add" property="timeAdd" jdbcType="TIMESTAMP" />
    </resultMap>
    <select id="get" resultMap="BaseResultMap">
        SELECT
            id,terminal_type,channel,site_id,md5_key,appid,style,time_add
        FROM config_one_login
        WHERE id = #id:INTEGER #
    </select>

    <!-- 查询 -->
    <select id="findByPojo" resultMap="BaseResultMap">
        SELECT
            id,terminal_type,channel,site_id,md5_key,appid,style,time_add
        FROM config_one_login
        WHERE site_id = #siteId:varchar # and terminal_type = #terminalType:INTEGER #
        and channel = #channel:INTEGER #
        limit 1
    </select>

</sqlMap>