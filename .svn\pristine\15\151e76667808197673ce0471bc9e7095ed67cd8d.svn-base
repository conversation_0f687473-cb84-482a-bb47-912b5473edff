package com.shunwang.baseStone.sso.context;

import com.shunwang.baseStone.context.RedisContext;
import com.shunwang.baseStone.sso.exception.IdentityExp;
import com.shunwang.baseStone.sso.pojo.Identity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.Serializable;
import java.util.concurrent.TimeUnit;

/**
 * ****************************
 * 版权所有：顺网科技 保留所有权利
 * 创建日期: 2011-8-28
 * 创建作者：mj.guan
 * 文件名称：
 * 版本： 1.0
 * 功能：身份证的上下文
 * 最后修改时间：
 * 修改记录：
****************************************
 */
public class IdentityContext {
	
	private static final Logger logger = LoggerFactory.getLogger(IdentityContext.class);
	
	public IdentityContext(){
		
	}
	/**
		产生身份证
	 */
	public static Identity createIdentity(Integer userId){
		
		Identity ret = new Identity(userId);
		RedisContext.getRedisCache().set(getId(ret.getId()), ret, 5, TimeUnit.MINUTES);
		return ret;
	}
	
	protected static String getId(String id){
		StringBuffer sb = new StringBuffer();
		sb.append("Identity[");
		sb.append(id);
		sb.append("]");
		return sb.toString();
	}
	
	private static Identity getIdentityById (String id){
		return RedisContext.getRedisCache().get(getId(id),Identity.class);
	}
	/**
	 * 检验identity是否合法
	 * @param id
	 */
	public static Identity validateIdentity(String id){
		if(id==null) {
			logger.info("IdentityId is null");			
			throw new IdentityExp(id);
		}
		Identity ret = getIdentityById(id);
		
		if(ret==null){
			logger.info("Identity is null" + id);
			throw new IdentityExp(id);
		}
		RedisContext.getRedisCache().del(getId(id));
		return ret;
	}
	
}
