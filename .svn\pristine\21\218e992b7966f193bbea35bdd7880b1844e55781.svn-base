package com.shunwang.baseStone.cache.keygenerator;

import com.shunwang.baseStone.cache.CacheKeyGenerator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.interceptor.KeyGenerator;

import java.lang.reflect.Method;
import java.util.Arrays;

public class AgreementKeyGenerator implements KeyGenerator {
    private final static Logger log = LoggerFactory.getLogger(ConfigResourceKeyGenerator.class);

    @Override
    public Object generate(Object target, Method method, Object... params) {
        if (params.length != 3) {
            log.error("错误调用:{}", Arrays.toString(params));
            throw new IllegalArgumentException("错误调用");
        }
        String siteId = (String) params[0];
        String type = (String) params[1];
        String platform = (String) params[2];
        String siteInterfaceKey = CacheKeyGenerator.getAgreement(siteId, type, platform);
        log.debug("生成key: siteId={}\ttype={}\tplatform:{}\tplatform:{}", siteId, type, platform, siteInterfaceKey);
        return siteInterfaceKey;
    }
}
