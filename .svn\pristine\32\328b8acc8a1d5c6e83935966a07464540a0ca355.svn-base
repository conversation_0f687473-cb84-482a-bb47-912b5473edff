<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">
	<!--ehcache server层-->
	<bean id="serverResourceKeyGenerator" class="com.shunwang.baseStone.cache.keygenerator.ServerResourceKeyGenerator"/>
	<bean id="serverLoginElementKeyGenerator" class="com.shunwang.baseStone.cache.keygenerator.ServerLoginElementKeyGenerator"/>
	<bean id="serviceUserOutInterfaceKeyGenerator" class="com.shunwang.baseStone.cache.keygenerator.ServiceUserOutInterfaceKeyGenerator"/>
	<bean id="serverWeixinOauthKeyGenerator" class="com.shunwang.baseStone.cache.keygenerator.ServerWeixinOauthKeyGenerator"/>
	<bean id="serverWeixinOauthTokenKeyGenerator" class="com.shunwang.baseStone.cache.keygenerator.ServerWeixinOauthTokenKeyGenerator"/>

	<!-- ehcache db层 -->
	<bean id="configApiAppKeyGenerator" class="com.shunwang.baseStone.cache.keygenerator.ConfigApiAppKeyGenerator"/>
	<bean id="configApiAppListKeyGenerator" class="com.shunwang.baseStone.cache.keygenerator.ConfigApiAppListKeyGenerator"/>
	<bean id="configAppidReplyKeyGenerator" class="com.shunwang.baseStone.cache.keygenerator.ConfigAppidReplyKeyGenerator"/>
	<bean id="configAppLoginElementKeyGenerator" class="com.shunwang.baseStone.cache.keygenerator.ConfigAppLoginElementKeyGenerator"/>
	<bean id="configBussLineKeyGenerator" class="com.shunwang.baseStone.cache.keygenerator.ConfigBussLineKeyGenerator"/>
	<bean id="configBussLineLoginElementKeyGenerator" class="com.shunwang.baseStone.cache.keygenerator.ConfigBussLineLoginElementKeyGenerator"/>
	<bean id="configCssKeyGenerator" class="com.shunwang.baseStone.cache.keygenerator.ConfigCssKeyGenerator"/>
	<bean id="configEmailConfigKeyGenerator" class="com.shunwang.baseStone.cache.keygenerator.ConfigEmailConfigKeyGenerator"/>
	<bean id="configInterfaceKeyGenerator" class="com.shunwang.baseStone.cache.keygenerator.ConfigInterfaceKeyGenerator"/>
	<bean id="configOauthKeyGenerator" class="com.shunwang.baseStone.cache.keygenerator.ConfigOauthKeyGenerator"/>
	<bean id="configOauthDefaultKeyGenerator" class="com.shunwang.baseStone.cache.keygenerator.ConfigOauthDefaultKeyGenerator"/>
	<bean id="configResourceKeyGenerator" class="com.shunwang.baseStone.cache.keygenerator.ConfigResourceKeyGenerator"/>
	<bean id="configRichTextKeyGenerator" class="com.shunwang.baseStone.cache.keygenerator.ConfigRichTextKeyGenerator"/>
	<bean id="configRichTextIdKeyGenerator" class="com.shunwang.baseStone.cache.keygenerator.ConfigRichTextIdKeyGenerator"/>
	<bean id="configServiceKeyGenerator" class="com.shunwang.baseStone.cache.keygenerator.ConfigServiceKeyGenerator"/>
	<bean id="configSiteInterfaceKeyGenerator" class="com.shunwang.baseStone.cache.keygenerator.ConfigSiteInterfaceKeyGenerator"/>
	<bean id="configSiteInterfaceOpenKeyGenerator" class="com.shunwang.baseStone.cache.keygenerator.ConfigSiteInterfaceOpenKeyGenerator"/>
	<bean id="configSmsConfigKeyGenerator" class="com.shunwang.baseStone.cache.keygenerator.ConfigSmsConfigKeyGenerator"/>
	<bean id="configUserOutInterfaceKeyGenerator" class="com.shunwang.baseStone.cache.keygenerator.ConfigUserOutInterfaceKeyGenerator"/>
	<bean id="configUserOutInterfacePrefixListKeyGenerator" class="com.shunwang.baseStone.cache.keygenerator.ConfigUserOutInterfacePrefixListKeyGenerator"/>

</beans>
