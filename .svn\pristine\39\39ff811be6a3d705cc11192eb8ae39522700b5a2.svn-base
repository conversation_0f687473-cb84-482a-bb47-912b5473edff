package com.shunwang.basepassport.actu.web;

import com.shunwang.baseStone.context.BaseStoneContext;
import com.shunwang.baseStone.encrypt.Encrypt;
import com.shunwang.baseStone.encrypt.pojo.EncryptItem;
import com.shunwang.basepassport.actu.constant.ActuConstant;
import com.shunwang.basepassport.actu.dao.CafeActuDao;
import com.shunwang.basepassport.actu.exception.ActuNotFoundExp;
import com.shunwang.basepassport.actu.pojo.ActuInfo;
import com.shunwang.basepassport.actu.pojo.CafeActuInfo;
import com.shunwang.basepassport.actu.response.CafeActuResponse;
import com.shunwang.basepassport.actu.response.CompanyActuResponse;
import com.shunwang.basepassport.actu.response.PersonalActuResponse;
import com.shunwang.basepassport.user.common.MemberConstants;
import com.shunwang.basepassport.user.dao.UserBankCardDao;
import com.shunwang.basepassport.user.exception.MsgIllExp;
import com.shunwang.basepassport.user.pojo.UserBankCard;
import com.shunwang.basepassport.user.web.MemberAction;
import com.shunwang.basepassport.user.web.UserBankCardQueryAction;
import com.shunwang.util.StringUtil;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;

/**
 * @Description:实名认证查询action
 * <AUTHOR>  create at 2011-7-28 上午11:31:36
 * @FileName com.shunwang.basepassport.actu.web.ActuAction.java
 */
public class ActuAction extends MemberAction {

	private String userName;
	/**
	 * 网吧用户变更时，存在待上报状态的查询
	 */
	private String infoState;
	/**
	/**
	 * <AUTHOR> create at 2011-7-28 上午11:31:32 
	 */
	private static final long serialVersionUID = -3724972798607985439L;

	private UserBankCardDao userBankCardDao;

	@Override
	public void doProcess() {
		ActuInfo actuInfo;
		if (MemberConstants.MEMBER_SPECIAL_TYPE_CAFE.equals(getMemberInfo().getMemberSpecialType())) {
			CafeActuDao cafeActuDao = (CafeActuDao) BaseStoneContext.getInstance().getBean(
					MemberConstants.ACTU_USER_TYPE_MAP.get(MemberConstants.USER_TYPE_CAFE));
			if (StringUtil.isNotBlank(infoState)) {
				actuInfo = cafeActuDao.getByMemberId(getMemberInfo().getMemberId(), infoState);
			} else {
				//默认优先取非待进件状态中,最新一条
				CafeActuInfo query = new CafeActuInfo();
				query.setMemberId(getMemberInfo().getMemberId());
				query.setInfoState(ActuConstant.INFO_STATE_CHANGING);
				int cntByInfoState = cafeActuDao.getCntByInfoState(query);
				if (cntByInfoState > 0) {
					//存在变更中的数据,必存在'待进件'的数据
					actuInfo = cafeActuDao.getByMemberId(getMemberInfo().getMemberId(), ActuConstant.INFO_STATE_CHANGING);
				} else {
					actuInfo = cafeActuDao.getByMemberId(getMemberInfo().getMemberId());
				}
			}
		} else {
			actuInfo = getMemberInfo().loadActuInfo();
		}

		if(null == actuInfo)
			throw new ActuNotFoundExp();

		if (MemberConstants.MEMBER_SPECIAL_TYPE_CAFE.equals(getMemberInfo().getMemberSpecialType())) {
			setBaseResponse(new CafeActuResponse(getMemberInfo(), setUserBankCardMsg( getMemberInfo().getMemberName(),actuInfo)));
		} else {
			if (MemberConstants.USER_TYPE_PERSONAL.intValue()==getMemberInfo().getMemberType()) {
				setBaseResponse(new PersonalActuResponse(getMemberInfo(), setUserBankCardMsg( getMemberInfo().getMemberName(),actuInfo)));
			} else {
				setBaseResponse(new CompanyActuResponse(getMemberInfo(), setUserBankCardMsg( getMemberInfo().getMemberName(),actuInfo)));
			}
		}
	}

	@Override
	public String getMemberName() {
		return userName;
	}
	@Override
	public void checkParam() {
		try {
			if(StringUtil.isNotBlank(userName)) {
				userName = URLDecoder.decode(new String(userName.getBytes(StandardCharsets.ISO_8859_1), StandardCharsets.UTF_8),"utf-8");
			}
		} catch (UnsupportedEncodingException e) {
			throw new MsgIllExp("用户名");
		}
	}

	@Override
	public String buildSignString() {
		Encrypt encrypt = new Encrypt();
		encrypt.addItem(new EncryptItem(getSiteId()));
		encrypt.addItem(new EncryptItem(getUserName()));
		encrypt.addItem(new EncryptItem(getTime()));
		return encrypt.buildSign();
	}

	/**
	 * 将银行卡信息写入实名对象并返回
	 * @param memberName
	 * @param actuInfo
	 * @return
	 */
	private ActuInfo setUserBankCardMsg(String memberName, ActuInfo actuInfo){
		UserBankCard userBankCard = userBankCardDao.findByMemberNameAndCardType(memberName, null, MemberConstants.UserBankCard.DefaultState.DEFAULT_CARD.toString());
		if (userBankCard != null){
			actuInfo.setBankName( userBankCard.getBankName() );
			actuInfo.setBankBranchName( userBankCard.getSubBankName() );
			actuInfo.setBankNo( userBankCard.getCardNo() );
			actuInfo.setBankUser( userBankCard.getRealName() );
		}
		return actuInfo;
	}

	@Override
	public String getSiteName() {
		return "实名认证查询";
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getUserName() {
		return userName;
	}

	public String getInfoState() {
		return infoState;
	}

	public void setInfoState(String infoState) {
		this.infoState = infoState;
	}

	public UserBankCardDao getUserBankCardDao() {
		return userBankCardDao;
	}

	public void setUserBankCardDao(UserBankCardDao userBankCardDao) {
		this.userBankCardDao = userBankCardDao;
	}
}
