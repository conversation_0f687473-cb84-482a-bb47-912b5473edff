package com.shunwang.basepassport.user.action;

import com.shunwang.basepassport.BaseTest;
import com.shunwang.util.date.DateUtil;
import com.shunwang.util.encrypt.AesEncrypt;
import com.shunwang.util.encrypt.Md5Encrypt;

/**
 *
 */
public class TestOutUpdatePwd extends BaseTest{

	public void init() throws Exception {
		String oldPwd = "123456a";
		String newPwd = "123456";
		params.put("siteId", "sw_pay");
		params.put("time", DateUtil.getCurrentDateStamp());
		params.put("signVersion", "1.0");
		//business data
		params.put("userName", "safiad123");
		params.put("pwdType", "1");
		params.put("oldPassword", AesEncrypt.Encrypt(Md5Encrypt.encrypt(oldPwd),"6rV82fu_e3.tw5A8"));
		params.put("newPassword", AesEncrypt.Encrypt(newPwd,"6rV82fu_e3.tw5A8"));
		params.put("weakPwdState", "1");
		//sign
		params.put("sign", getSign(params));
	}

	@Override
	protected String getUrl() {
		return "http://interface.kedou.com/front/interface/outUpdatePwd.htm";
	}

	@Override
	protected String getMd5Key() {
		return "123456";
	}

	@Override
	protected boolean getJson() {
		return false ;
	}
}
