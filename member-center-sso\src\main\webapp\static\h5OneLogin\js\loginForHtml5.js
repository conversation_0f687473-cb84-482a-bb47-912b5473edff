/**
 * 刷新检验码
 */
function refreshCheckcode() {
  var nowTime = new Date();
  $("#checkCodeImg").attr("src", "checkCode.do?=t" + nowTime.getTime());
}

function refreshSmsCheckcode() {
  var nowTime = new Date();
  $("#checkCode2").attr("src", "smsCheckCode.do?=t" + nowTime.getTime());
}

/**
 * login 按钮被点击
 */
function loginDefaultClick(actionUrl) {}

function loginClick() {
  loginDefaultClick("/login.do");
}

function loginFormLogin() {
  if (!loginFormLoginCheck(true)) {
    return false;
  }
  $("#login-form").target = "";
  $("#login-form").attr("action", "/login.do");
  $("#login-form").submit();
}

function chkEmail(strEmail) {
  if (!/^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/.test(strEmail)) {
    return false;
  } else {
    return true;
  }
}

function loginFormLoginCheck(isCleanPwd) {
  var pswd = $("#password");
  var passwordInp = $("#passwordInp");
  var md5 = $("#md5");
  var userName = $("#userName");
  var userNameInp = $("#userNameInp");
  var agreementCheckbox = $("#agreementCheckbox");
  var pwdi = passwordInp.val();

  if (pwdi == "" || pwdi == "请输入登录密码") {
    showTipsError("请输入登录密码");
    return false;
  }
  if (isWeakPwd(pwdi) || pwdi == userName) {
    $("#weakPwdState").val("1");
  } else {
    $("#weakPwdState").val("2");
  }

  var hexMd5 = hex_md5(pwdi);
  var rsaEnc = RSAEnc(hexMd5);
  pswd.val(rsaEnc);
  if (isCleanPwd) {
    passwordInp.val("");
  }
  md5.val(true);

  var userNameVal = userNameInp.val();
  if (userNameVal == "" || userName.val() == "用户名/邮箱/手机号") {
    showTipsError("请输入用户名/邮箱/手机号");
    return false;
  }
  if (chkEmail(userNameVal)) {
    userNameInp.val(userNameVal.toLowerCase());
  }
  userName.val(userNameInp.val());

  if (agreementCheckbox.checked == false) {
    showTipsError("请确认相关条款");
    return false;
  }
  return true;
}

var CONSTANTS = {
  SEC: 60,
  SEND_ACTIVENO_TIME: "sso_login_mobile_send_time",
  HAS_SEND: false,
};

//倒计时
var wait = 60;
let waitTimer;
function time(o) {
  // TODO: 20250610 清除上一个定时器
  clearTimeout(waitTimer);
  // TODO: 20250610 这里的o可以是一个函数或者jQuery对象
  if (wait == 0) {
    typeof(o) === "function" 
    ?o(wait)
    :o.prop("disabled", false)
      .text("获取验证码")
      .removeClass("disabled")
      .addClass("btn-primary");
    wait = 60;
  } else {
    typeof(o) === "function" 
    ?o(wait)
    :o.prop("disabled", true)
      .text(wait + "秒后重新获取")
      .removeClass("btn-primary")
      .addClass("disabled");
    wait--;
    waitTimer = setTimeout(function () {
      time(o);
    }, 1000);
  }
}

function isMobileNo(s) {
  var reg = "";
  if (
    typeof validPhoneRex === "undefined" ||
    validPhoneRex == "" ||
    validPhoneRex == null
  ) {
    reg = /^1[34578]\d{9}$/;
  } else {
    reg = validPhoneRex;
  }

  if (reg.exec(s)) return true;
  else return false;
}

function checkMobileCode(mobileCheckCode) {
  var mobileCheckCode = $.trim(mobileCheckCode);
  if (mobileCheckCode.length == 0) {
    showTipsError("请输入短信验证码");
    return false;
  }
  var patn = /^[0-9]{6}$/;
  if (!patn.test(mobileCheckCode)) {
    showTipsError("验证码由6位数字组成");
    return false;
  }
  $("#mobileActiveNo").val(mobileCheckCode);
  return true;
}

function checkMobile(number) {
  var number = $.trim(number);

  if (number.length == 0) {
    showTipsError("请输入您的手机号码");
    return false;
  }
  if (!isMobileNo(number)) {
    showTipsError("手机号码格式错误，请重新输入");
    return false;
  }
  $("#mobile").val(number);
  return true;
}

function checkAgreement() {
  if ($("#agreementCheckbox").prop("checked") == true) return true;

  showTipsError("请确认相关条款");
  return false;
}

function showTipsError(msg) {
  $("#tips-error p").html(msg);
  $("#tips-error").show();
}

function hideTipsError() {
  $("#tips-error p").html("");
  $("#tips-error").hide();
}

function showTipsInfo(msg) {
  $("#tips-info").html(msg);
  $("#tips-info").show();
}

//密码明文显示
$("#password-show").on("click", function () {
  if ($("#password-show").prop("checked")) {
    $("#passwordInp").attr("type", "text");
  } else {
    $("#passwordInp").attr("type", "password");
  }
});

//隐私协议勾选框
$(".agreement_chk").on("click", function () {
  $("#agreementCheckbox").trigger("click");
});

function restoreSendState() {
  var hasSend = false;
  if (!!smsSendExpTime && smsSendExpTime > 0) {
    wait = smsSendExpTime;
    time($("#sent-code"));
    hasSend = true;
  }
  if (!hasSend) {
    $("#mobileCheckCode").attr("disabled", true);
  }
}

function numberInputCheck() {
  var number = $("#phoneNumber").val();
  if (!checkMobile(number)) {
    $("#phoneNumber").focus();
    return false;
  }
  hideTipsError();
  return true;
}


//短信登录验证
$("#tel-login").click(function (e) {
  var mobileCheckCode = $("#mobileCheckCode").val();
  var number = $("#phoneNumber").val();
  if (
    !checkAgreement() ||
    !checkMobile(number) ||
    !checkMobileCode(mobileCheckCode)
  )
    return false;
  e.preventDefault();

  $("#login-form").attr("action", "/outMobileConfirmNew.do");
  $("#login-form").submit();
});

$("header.header-bar .back").on("click", function () {
  try {
    window.SWMobileSDK.onClose();
  } catch (e) {
    try {
      window.webkit.messageHandlers.onClose.postMessage({ body: "" });
    } catch (ex) {
      window.history.back();
    }
  }
});

//短信登录验证
$("#choiseAccount").click(function (e) {
  $("#login-form").attr("action", "/bindAsLoginAccountNew.do");
  $("#login-form").submit();
});

// tips-info close
function tipClose() {
  $(".tips-info").fadeOut(300);
}

function cleanTip() {
  $(".drop").each(function () {
    $(this).removeClass("on");
  });
}
function bIsAndroid() {
  var sUserAgent = navigator.userAgent.toLowerCase();
  var bIsAndroid = sUserAgent.match(/android/i) == "android";
  return bIsAndroid;
}
//解决微信公账号打开登录页面白屏问题
function bIsWeixin() {
  var ua = window.navigator.userAgent.toLowerCase();
  if (ua.match(/MicroMessenger/i) == "micromessenger") {
    return true;
  } else {
    return false;
  }
}

function goToOtherSite(siteId) {
  cleanTip();
  document.getElementById("loginSiteId").value = siteId;
  document.getElementById("login-form").action = "/goOutOauth.do";
  if (bIsWeixin() || !bIsAndroid()) {
    //IOS缁熶竴鏂扮獥浣�
    document.getElementById("login-form").target = "_blank";
  } else {
    var linkTgt = $("#linkTgt").val();
    document.getElementById("login-form").target = linkTgt;
  }
  document.getElementById("login-form").submit();
}

/**
 * 检测是否为弱密码
 * @param pwdi
 * @returns {boolean}
 */
function isWeakPwd(pwdi) {
  if (
    !/^[A-Za-z0-9`~!@#\$%\^&\*\(\)\-_=\+\[\{\]\}\\\|;:'"",<.>\/\?]+$/.test(pwdi)
  ) {
    return true;
  }
  if (
    !/^(?![A-Za-z]+$)(?!\d+$)(?![`~!@#\$%\^&\*\(\)\-_=\+\[\{\]\}\\\|;:'"",<.>\/\?]+$)\S{2,16}$/.test(
      pwdi
    )
  ) {
    return true;
  }
  return false;
}

$(".btn")
  .on("touchstart", function () {
    $(this).addClass(".active");
  })
  .on("touchend", function () {
    $(this).removeClass(".active");
  });

restoreSendState();

// TODO: 20250610 新版本错误提示
function showToastError (msg) {
  const $toastError = $("#toastError");
  const $toastErrorText = $("#toastErrorText");

  $toastErrorText.text(msg);
  $toastError.show();
  setTimeout(() => {
    $toastError.hide();
  }, 2000);
};