<%@ page contentType="text/html;charset=utf-8" %>
<%@ include file="/common/taglibs.jsp" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta name="Keywords" content="顺网通行证,密保问题,找回,邮箱绑定"/>
    <meta name="Description" content="顺网通行证已将通知信发到用户邮箱中，用户需登陆邮箱按提示操作。"/>
    <title>顺网通行证-找回密保问题-用邮箱找回</title>
    <script type="text/javascript" src="${staticServer}/scripts/common/emailUrl.js"></script>
</head>

<body>
<div class="c_head">
    <i class="forget_icon"></i>
    <span class="title">找回密保问题</span>
    <span class="desc">找回密保问题-用邮箱找回</span>
</div>
<div class="c_body forget_s01">
    <ul class="step_bar">
        <li><em>1</em> 选择找回方式</li>
        <li class="current"><em>2</em> 进行安全验证</li>
        <li><em>3</em> 设置新密保问题</li>
    </ul>
        <form name="mainForm" id="mainForm" method="post"
              action="${appServer}/front/find/findProblemSendEmail_front.htm">
            <input type="hidden" id="member.linkEmail" value="${email}"/>
            <div class="forget_send">
                <i class="f_email"></i>
                <p class="tip">密保找回邮件 ${member.emailShow} 发送成功！</p>
                <p>请您注意<strong>接收邮件</strong>！</p>
            </div>
            <div class="forget_desc">
                <p>1.由于垃圾邮件没有明确的判断标准，如果您的收件箱没有收到我们发送的邮件，您可以查阅一下垃圾邮件箱。</p>
                <p>2.没有收到邮件？ <a href="#" id="resendLink">重新发送</a> 或者 <a href="${appServer}/front/login/chooseFindQuestion.htm">使用其它方式找回</a>。</p>
            </div>
        </form>
    </div>
<script type="text/javascript" charset="utf-8">
    <c:if test="${!empty errorMsg}">
    var errorMsg = "${errorMsg}";
    $("#msgEm").text(errorMsg);
    </c:if>
    $("#resendLink").click(function() {
        $("#mainForm")[0].action = "/front/login/toSendEmail_front.htm?flag=question";
        $("#mainForm").submit();
    });
</script>
<!-- contbox end -->
</body>

</html>

