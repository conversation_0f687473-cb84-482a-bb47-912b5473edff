package com.shunwang.basepassport.binder.pojo;

import com.shunwang.baseStone.context.BaseStoneContext;
import com.shunwang.baseStone.sender.ISender;
import com.shunwang.baseStone.sender.pojo.SendMsg;
import com.shunwang.basepassport.binder.common.BinderConstants;
import com.shunwang.basepassport.binder.common.EmailContents;
import com.shunwang.basepassport.binder.dao.BinderDao;
import com.shunwang.basepassport.binder.exception.TimeOutAndInvalidExp;
import com.shunwang.basepassport.user.common.MemberConstants;
import com.shunwang.basepassport.user.dao.MemberDao;
import com.shunwang.basepassport.user.pojo.Member;
import com.shunwang.util.date.DateUtil;
import com.shunwang.util.encrypt.Md5Encrypt;
import com.shunwang.util.lang.StringUtil;
import com.shunwang.util.math.RandomUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

public class EmailBinder extends SendBinder{
	

	/**
	 * 
	 */
	private static final long serialVersionUID = 7502832253256550290L;
	private final static Logger log = LoggerFactory.getLogger(EmailBinder.class);
	private String emailLink;
	private String emailTitle;
	private String sendFrom;
	private String sign;//邮件链接中的sign

	@Override
	public Serializable getPk() {
		// TODO Auto-generated method stub
		return this.getMemberId();
	}

	@SuppressWarnings("unchecked")
	@Override
	public BinderDao<Binder> getBinderDao() {
		// TODO Auto-generated method stub
		return (BinderDao<Binder>) BaseStoneContext.getInstance().getBean("emailBinderDao");
	}

	@Override
	public Member buildBindMember(boolean asLoginAccount) {
		// TODO Auto-generated method stub
		this.getMember().setIsBindEmail(true);
		this.getMember().setEmail(this.getNumber());
		if(asLoginAccount){
			this.getMember().setMailAsLoginAccount(true) ;
		}
		return this.getMember();
	}

	@Override
	protected Member buildUnBindMember() {
		// TODO Auto-generated method stub
		this.getMember().setIsBindEmail(false);
		this.getMember().setEmail(" ");
		return this.getMember();
	}

	@Override
	protected SendMsg buildSendMsg() {
		// TODO Auto-generated method stub
		SendMsg sendMsg = new SendMsg();
		sendMsg.setContent(this.getContent());
		sendMsg.setNumber(this.getNumber());
		sendMsg.setTitle(this.getEmailTitle());
		sendMsg.setSendFrom(this.getSendFrom());
		return sendMsg;
	}

	@Override
	protected void createActiveNo() {
		// TODO Auto-generated method stub
		StringBuffer stringBuffer = new StringBuffer();
		stringBuffer.append(DateUtil.getCurrentDateStamp()).append(RandomUtil.getRandomStr(8));
		this.setActiveNo(stringBuffer.toString());
	}

	@Override
	protected void createContent() {
		createSign();
		StringBuffer activeURL = new StringBuffer();
		activeURL.append(StringUtil.isBlank(this.getEmailLink())?EmailContents.EMAIL_LINK_MAP.get(this.getBusinessType()):this.getEmailLink())
		.append("?email=")
		.append(this.getNumber())
		.append("&activeNo=")
		.append(this.getActiveNo())
		.append("&sign=")
		.append(this.getSign())
		.append("&emailId=");
		if(this.getBusinessType().equals(BinderConstants.FINDPAYPWD))
			activeURL.append(UTFencode(this.getMemberName()));
		else 
			activeURL.append(this.getMemberId());
		if(this.getBusinessType().equals(BinderConstants.BINDNUMBER))
			activeURL.append("&from=bind");
		else if(this.getBusinessType().equals(BinderConstants.CHANGENUMBER))
			activeURL.append("&from=change");
		else if(this.getBusinessType().equals(BinderConstants.CHANGENUMBERFORSTEP2))
			activeURL.append("&from=changeStep2");
		else if(this.getBusinessType().equals(BinderConstants.REGISTER))
			activeURL.append("&from=register");
		if(StringUtil.isBlank(this.getContent()))
			this.setContent(this.getDefaultContent());
		String tmp = "";
		if(null != this.getContent()){
			tmp = this.getContent().replace("logName", this.getMemberName());
			tmp = tmp.replace("$EmailConfirmString$", activeURL);
		}
		this.setContent(tmp);
	}
	
	@Override
	public void send() {
		// TODO Auto-generated method stub
		createEmailTitle();
		super.send();
	}
	
	@Override
	public void validate(Object object) {
		// TODO Auto-generated method stub
		checkSign();
		super.validate(object);
	}

	/**
	 * 
	 * 设置邮件标题
	 * @return
	 * <AUTHOR> 创建于 2011-8-24 下午05:57:35
	 * @throws
	 */
	private void createEmailTitle(){
		if(StringUtil.isBlank(this.getEmailTitle())){
			this.setEmailTitle(EmailContents.EMAIL_TITLE_MAP.get(this.getBusinessType()));
		}
	}

	/**
	 * 生产邮件链接中的sign
	 */
	private void createSign(){
		this.setSign(Md5Encrypt.encrypt(buildSign()));
	}
	/**
	 * 构建邮件sign
	 * 
	 * @return
	 * <AUTHOR> 创建于 2011-8-28 下午12:16:00
	 * @throws
	 */
	private String buildSign(){
		StringBuffer stringBuffer = new StringBuffer();
		stringBuffer.append(this.getMemberId())
		.append(this.getNumber())
		.append(this.getActiveNo())
		.append(EmailContents.EMAIL_CONTENT_MD5_MAP.get(this.getBusinessType()));
		return stringBuffer.toString();
	}
	/**
	 * 检查邮件签名串
	 * 
	 * @return
	 * <AUTHOR> 创建于 2011-8-28 下午12:07:15
	 * @throws
	 */
	private void checkSign(){
		if(!Md5Encrypt.encrypt(buildSign()).equals(this.getSign())&&!StringUtil.isBlank(this.getSign()))
			throw new TimeOutAndInvalidExp(this.getType());
	}
	
	@Override
	public Integer getDoType() {
		// TODO Auto-generated method stub
		return BinderConstants.DOTYPE_EMAIL;
	}

    @Override
    public int getMaxBindLimit() {
        return BinderConstants.BINDER_LIMIT_EMAIL;
    }

    @Override
	protected ISender getSender() {
		// TODO Auto-generated method stub
		return (ISender) BaseStoneContext.getInstance().getBean("emailSender");
	}

	@Override
	public String getType() {
		// TODO Auto-generated method stub
		return BinderConstants.EMAIL;
	}

	@Override
	public boolean isBinded() {
		// TODO Auto-generated method stub
		return this.getMember().getIsBindEmail();
	}

	@Override
	public String getDefaultContent() {
		// TODO Auto-generated method stub
		return EmailContents.EMAIL_CONTENT_MAP.get(this.getBusinessType());
	}
	public String getEmailLink() {
		return emailLink;
	}

	public void setEmailLink(String emailLink) {
		this.emailLink = emailLink;
	}

	public String getEmailTitle() {
		return emailTitle;
	}

	public void setEmailTitle(String emailTitle) {
		this.emailTitle = emailTitle;
	}

	public String getSendFrom() {
		return sendFrom;
	}

	public void setSendFrom(String sendFrom) {
		this.sendFrom = sendFrom;
	}

	public String getSign() {
		return sign;
	}

	public void setSign(String sign) {
		this.sign = sign;
	}

	@Override
	public String getNumberShow() {
		// TODO Auto-generated method stub
		String emailShow = "";
		if(!StringUtil.isBlank(this.getNumber()) && this.getNumber().length()>2) {
			emailShow = this.getNumber().substring(0, 1)+"****"+this.getNumber().substring(this.getNumber().indexOf("@")-1, this.getNumber().length());
		}
		return emailShow;
	}
	
	public String UTFencode(String string){
		try {
			return  URLEncoder.encode(string, "UTF-8");
		} catch (UnsupportedEncodingException e) {
			log.error("toUTFencodeStr:["+string+"],字符串编码异常",e);
			return "";
		}
	}

    /**
     *
     * 邮箱绑定过的通行证数量是否超过限制
     *
     * <AUTHOR>
     * @return false超过限制 true没超过限制
     */
    @Override
    public boolean isCouldBind(String number) {
        Integer cnt = getMemberDao().getCntByEmail(number);
        if(null != cnt && cnt >= this.getMaxBindLimit())
            return false;
        return true;
    }

	protected MemberDao getMemberDao() {
		return (MemberDao) BaseStoneContext.getInstance().getBean("memberDao");
	}

	@Override
	public String getLoginType() {
		return MemberConstants.LOGIN_TYPE_EMAIL;
	}
}
