package com.shunwang.basepassport.mobile.web;

import java.util.Date;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.shunwang.basepassport.mobile.response.VerifyResultResponse;
import com.shunwang.basepassport.binder.common.BinderConstants;
import com.shunwang.basepassport.binder.exception.TimeOutAndInvalidExp;
import com.shunwang.basepassport.binder.exception.ActiveNoErrorExp;
import com.shunwang.basepassport.mobile.pojo.RegActiveNo;
import com.shunwang.baseStone.context.BaseStoneContext;
import com.shunwang.basepassport.mobile.dao.RegActiveNoDao;

import com.shunwang.baseStone.encrypt.Encrypt;
import com.shunwang.baseStone.encrypt.pojo.EncryptItem;
import com.shunwang.basepassport.commonExp.ParamNotFoundExp;
import com.shunwang.basepassport.user.common.MemberConstants;
import com.shunwang.util.StringUtil;
import com.shunwang.util.date.DateUtil;

/**
 * ****************************
 * 版权所有：顺网科技 保留所有权利
 * 创建日期: 2013-06-24
 * 创建作者：王松年
 * 文件名称：RegActNoVerifyAction.java
 * 版本： 1.0
 * 功能：手机验证码认证(手机SDK用)
 * 最后修改时间：
 * 修改记录：
 ***************************************
 */
public class RegActNoVerifyAction extends MobileSdkBaseAction {

	@SuppressWarnings("unused")
	private final static Logger log = LoggerFactory.getLogger(RegActNoVerifyAction.class);
	private static final long serialVersionUID = 1L;
   	

    private String uuid;
    private String activeNo;
    
	@Override
	public void process() throws Exception {		

        RegActiveNoDao regActiveNoDao = (RegActiveNoDao) BaseStoneContext.getInstance().getBean("regActiveNoDao");
        RegActiveNo regActiveNoInfo = regActiveNoDao.getBySiteIdAndMobile(this.getSiteId(), mobileNumber);

        //检查激活码
        checkActiveNo(regActiveNoInfo);
        
        //更新验证信息
        regActiveNoInfo.setActiveTime(new Date());
        regActiveNoDao.update(regActiveNoInfo);

        //结果处理
        VerifyResultResponse response = new VerifyResultResponse();
		this.setBaseResponse(response);

	}

    /**
      * 检查激活码
     */
    private void checkActiveNo(RegActiveNo regActiveNoInfo) {

        if (regActiveNoInfo == null || !activeNo.equals( regActiveNoInfo.getActiveNo())) {
            throw new ActiveNoErrorExp();
        }

		long timeDiff = DateUtil.compare(new Date(), regActiveNoInfo.getSendTime(), DateUtil.ONE_MINUTE);
		if(timeDiff>=60) {
			throw new TimeOutAndInvalidExp(BinderConstants.MOBILE);
        }
    }


	 /**
      * 检查参数是否为空
     */
	public void checkParam(){	
		if(StringUtil.isBlank(getSiteId()))
			throw new ParamNotFoundExp("siteId");
		if(StringUtil.isBlank(getTime()))
			throw new ParamNotFoundExp("time");
		if(StringUtil.isBlank(getSign()))
			throw new ParamNotFoundExp("sign");
        if (StringUtil.isBlank(mobileNumber)) 
			throw new ParamNotFoundExp("mobileNumber");
        if (StringUtil.isBlank(activeNo)) 
			throw new ParamNotFoundExp("activeNo");
	}
	
	/**
	 * ***********
	  * 创建日期: Jul 25, 2011 3:18:34 PM
	  * 创建作者：chenjh
	  * @return 
	  * 功能：获取未加密的 串 
	  *************
	 */
	public String buildSignString(){
		Encrypt  encrypt=new Encrypt();
		encrypt.addItem(new EncryptItem(getSiteId()));
		encrypt.addItem(new EncryptItem(getTime()));		
		return encrypt.buildSign();
	}


	
	/**
	 * ***********
	  * 创建日期: Jul 25, 2011 4:33:41 PM
	  * 创建作者：chenjh
	  * @return 
	  * 功能：获取站点siteName
	  *************
	 */
	@Override
	public String getSiteName() {		
		return MemberConstants.SDK_CODE_VERIFY;
	}
	
    public void setMobileNumber(String mobileNumber) {
        this.mobileNumber=mobileNumber;
    }
    public String getMobileNumber() {
        return this.mobileNumber;
    }


    public void setUuid(String uuid) {
        this.uuid=uuid;
    }
    public String getUuid() {
        return this.uuid;
    }

    public void setActiveNo(String activeNo) {
        this.activeNo=activeNo;
    }
    public String getActiveNo() {
        return this.activeNo;
    }

}
