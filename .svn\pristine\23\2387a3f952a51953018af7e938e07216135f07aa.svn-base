package com.shunwang.baseStone.sensitiveword.dao;

import java.util.Date;

import com.shunwang.baseStone.context.BackUserContext;
import com.shunwang.baseStone.sensitiveword.pojo.SensitiveWord;
import com.shunwang.framework.exception.WinterException;

public class SensitiveWordDao extends com.shunwang.framework.ibatis.dao.ConditionQueryDao<com.shunwang.baseStone.sensitiveword.pojo.SensitiveWord>{
	
	public SensitiveWord getsensitivWordBysWord(String word){		
		return (SensitiveWord) this.getSqlMapClientTemplate().queryForObject(getStatementNameWrap("getsensitivWordBysWord"), word);
	}
	@Override
	public SensitiveWord save(SensitiveWord p) throws WinterException {		
		p.setTimeAdd(new Date());
		p.setUserAdd(BackUserContext.getUserName());
		p.setTimeEdit(new Date());
		p.setUserEdit(BackUserContext.getUserName());
		return super.save(p);
	}
	@Override
	public SensitiveWord update(SensitiveWord p) throws WinterException {		
		p.setUserEdit(BackUserContext.getUserName());
		p.setTimeEdit(new Date());
		return super.update(p);
	}
	
    
}
