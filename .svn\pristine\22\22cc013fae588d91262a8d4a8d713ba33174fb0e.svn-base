package com.shunwang.basepassport.weixin.service;

import com.shunwang.basepassport.weixin.dao.WeixinOauthDao;
import com.shunwang.basepassport.weixin.pojo.WeixinOauth;
import org.springframework.cache.annotation.Cacheable;

/**
 * User:pf.ma
 * Date:2020/01/02
 * Time:10:18
 */
public class WeixinOauthService {

	private WeixinOauthDao weixinOauthDao ;

	@Cacheable(value = "cache", keyGenerator = "weixinOauthKeyGenerator", unless = "#result==null")
	public WeixinOauth getBySiteId(String siteId, Integer type) {
		WeixinOauth weixinOauth = new WeixinOauth() ;
		weixinOauth.setSiteId(siteId) ;
		weixinOauth.setType(type);
		return weixinOauthDao.findOne(weixinOauth) ;
	}

	public WeixinOauthDao getWeixinOauthDao() {
		return weixinOauthDao;
	}

	public void setWeixinOauthDao(WeixinOauthDao weixinOauthDao) {
		this.weixinOauthDao = weixinOauthDao;
	}
}
