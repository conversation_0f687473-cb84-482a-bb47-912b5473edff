package com.shunwang.passport.weixin.pojo;


/**
 * 回应给微信服务器的消息
 *
 * <AUTHOR>
 * @since 2014-04
 *
*/
public class WeixinResponseMsg extends WeixinBasicMsg {

    public WeixinResponseMsg () {
    }

    public WeixinResponseMsg(String toUserName,String fromUserName) {
        super(toUserName,fromUserName);
        this.createTime = String.valueOf(System.currentTimeMillis());
        this.msgType=WeixinBasicMsg.MSG_TYPE_TEXT; //默认为文本类型　
    }

    public WeixinResponseMsg(String toUserName,String fromUserName, String createTime, String msgType) {
        super(toUserName,fromUserName, createTime, msgType);
    }

    //图片消息
    private String picUrl;
    //语音和视频消息
    private String mediaId;
    //文本消息
    private String content;

    public void setPicUrl(String picUrl) {
        this.picUrl=picUrl;
    }
    public String getPicUrl() {
        return this.picUrl;
    }


    public void setMediaId(String mediaId) {
        this.mediaId=mediaId;
    }
    public String getMediaId() {
        return this.mediaId;
    }


    public void setContent(String content) {
        this.content=content;
    }
    public String getContent() {
        return this.content;
    }

}
