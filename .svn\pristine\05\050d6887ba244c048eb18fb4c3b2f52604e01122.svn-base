package com.shunwang.baseStone.sso.apapter;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.shunwang.baseStone.cache.CacheKeyConstant;
import com.shunwang.baseStone.context.RedisContext;
import com.shunwang.baseStone.core.exception.BaseStoneException;
import com.shunwang.baseStone.sso.context.SsoDomainContext;
import com.shunwang.basepassport.user.service.DcReportForWxWorkService;
import com.shunwang.basepassport.user.service.DcReportService;
import com.shunwang.basepassport.weixin.constant.BarLoginEnum;
import com.shunwang.basepassport.weixin.constant.BarLoginReportEnum;
import com.shunwang.baseStone.sso.constant.UserOutsiteConstant;
import com.shunwang.baseStone.sso.context.ClientTicketContext;
import com.shunwang.baseStone.sso.netbar.NetBarLoginJob;
import com.shunwang.baseStone.sso.netbar.NetBarService;
import com.shunwang.basepassport.weixin.pojo.BarLoginReportInfo;
import com.shunwang.baseStone.sso.pojo.QrCodeResponse;
import com.shunwang.baseStone.sso.weixin.pojo.*;
import com.shunwang.baseStone.sso.weixin.task.AlarmJob;
import com.shunwang.basepassport.asynctask.AsyncTaskExecutor;
import com.shunwang.basepassport.binder.common.ErrorCode;
import com.shunwang.basepassport.config.dao.AppidReplyDao;
import com.shunwang.basepassport.config.dao.AreaAppidDao;
import com.shunwang.basepassport.config.enums.AppidReplyEnum;
import com.shunwang.basepassport.config.pojo.AppidReply;
import com.shunwang.basepassport.config.pojo.AreaAppid;
import com.shunwang.basepassport.manager.bean.ReportEntity;
import com.shunwang.basepassport.user.common.MemberConstants;
import com.shunwang.basepassport.user.common.MemberUtil;
import com.shunwang.basepassport.user.common.UserLoginSessionUtil;
import com.shunwang.basepassport.user.dao.MemberOutSiteDao;
import com.shunwang.basepassport.user.dao.WxIdCardBindDao;
import com.shunwang.basepassport.user.pojo.Member;
import com.shunwang.basepassport.user.pojo.MemberAccountBind;
import com.shunwang.basepassport.user.pojo.WxIdCardBind;
import com.shunwang.basepassport.weixin.constant.WeixinConstant;
import com.shunwang.basepassport.weixin.exception.WeixinOauthException;
import com.shunwang.basepassport.weixin.pojo.WeixinOauth;
import com.shunwang.basepassport.weixin.pojo.WeixinOauthToken;
import com.shunwang.basepassport.weixin.pojo.WeixinOpenIdUnionId;
import com.shunwang.basepassport.weixin.service.WeixinMsgService;
import com.shunwang.util.IO.XmlUtil;
import com.shunwang.util.StringUtil;
import com.shunwang.util.date.DateUtil;
import com.shunwang.util.encrypt.AesEncrypt;
import com.shunwang.util.json.GsonUtil;
import com.shunwang.util.math.RandomUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Base64Util;
import org.dom4j.Document;
import org.dom4j.Element;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.math.BigInteger;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * User:pf.ma
 * Date:2019/12/30
 * Time:14:09
 */
public class WeixinOpenAuthAdapter extends WeixinAdapter{

	private static final Logger logger = LoggerFactory.getLogger(WeixinOpenAuthAdapter.class) ;
	private static final Logger reportLogger = LoggerFactory.getLogger("com.shunwang.baseStone.sso.netBarReportLog");
	private static final Gson GSON = new Gson() ;

	private static final String BOOT_PC_URL = SsoDomainContext.getSsoServer() + "/bootPc.do?uk=";

	private NetBarService          netBarService;
	private WxIdCardBindDao        wxIdCardBindDao;
	private AreaAppidDao           areaAppidDao;
	private AppidReplyDao          appidReplyDao;
	private WeixinMsgService weixinMsgService;

	//微信使用struts配置返回stream的方式已经失效，直接response.write(str) 2021.09.24
	private InputStream stream = new ByteArrayInputStream(SUCCESS.getBytes()) ;

	private String auth_code ;

	private String preAuthUrl ;

	private Integer authType;

	@Override
	public String goToOauth() {
		//此方法用于新开页面获取跳转地址使用，微信公众号为二维码，无需实现
		return null ;
	}

	@Override
	protected String getUserKeyPrefix() {
		return WeixinConstant.QRSCENE_PRE;
	}

	@Override
	protected String buildCacheUserKey(){
		String randomScene = new BigInteger(DateUtil.getCurrentDateStamp() + RandomUtil.getRandomStr(10)).toString(36);
		String userSceneKey = getUserKeyPrefix() + randomScene;
		if (StringUtil.isNotBlank(getSiteScene())) {
			userSceneKey = userSceneKey + "_" + getSiteScene();
		}
		return userSceneKey ;
	}

	// 用户二维码https://developers.weixin.qq.com/doc/offiaccount/Account_Management/Generating_a_Parametric_QR_Code.html
	@Override
	public Map<String, Object> getOauth() {
		return getOauth(null);
	}

	public Map<String, Object> getOauth(String mode) {
		WeixinOauthToken weixinOauthToken;
		// 创建一个随机场景值，用来查询是否已经关注
		String cacheUserKey;
		if (StringUtil.isNotBlank(innerScene)) {
			String appid = RedisContext.getResourceCache().getResourceValue(CacheKeyConstant.ConfigResourcesConstants.NET_BAR_FREE_LOGIN_CONFIG,
					CacheKeyConstant.ConfigResourcesConstants.DEFAULT_APPID);
			weixinOauthToken = weixinOauthTokenService.getByAppIdAndType(appid, WeixinConstant.TYPE.AUTHORIZER);
			cacheUserKey = innerScene;
		} else {
			// 接入方信息
			weixinOauthToken = getBySiteIdAndType(site_id, WeixinConstant.TYPE.AUTHORIZER);
			cacheUserKey = buildCacheUserKey();
		}
		//确保Token有效
		checkOauthToken(weixinOauthToken);
		Integer qrcodeExpireSeconds = weixinOauthToken.getQrcodeExpireSecondsWithDefault();
		// 保存该场景值对应的siteId
		try {
			WeixinQrcode weixinQrcode = weixinOpenService.createQrcode(weixinOauthToken.getAccessToken(), cacheUserKey, qrcodeExpireSeconds);
			//异常时不缓存数据，较少内存浪费
			if (StringUtil.isNotBlank(weixinQrcode.getQrcodeUrl())) {
				cacheExtData(cacheUserKey, qrcodeExpireSeconds);
				if (StringUtil.isNotBlank(mode)) {
					redisOperation.set(WeixinConstant.NET_BAR_LOGIN + cacheUserKey, mode, qrcodeExpireSeconds);
				}
			}
			return respQrcode(weixinQrcode, cacheUserKey, qrcodeExpireSeconds) ;
		}catch (Exception e){
			logger.error("获取场景值二维码异常", e) ;
			throw new WeixinOauthException("获取场景值二维码异常") ;
		}
	}

	// 用户二维码 用于服务端调用
	@Override
	public Map<String, Object> getOauthByServer() {
		JsonObject extJson = GsonUtil.jsonToBean(extData, JsonObject.class);
		String mode = GsonUtil.getStringFromJsonObject(extJson, "mode");
		//默认mode为网吧 即1
		if (StringUtil.isBlank(mode)) {
			mode = BarLoginEnum.ModeEnum.NET_BAR.getValue().toString();
		}
		if (StringUtil.isBlank(extData)) {
			throw new WeixinOauthException("extData缺失");
		}
		if (BarLoginEnum.ModeEnum.SCANNING_NO_LOGIN_CODE.getValue().toString().equals(mode)) {
			return buildBusinessQrCode(mode);
		} else if (BarLoginEnum.ModeEnum.SCANNING_LOGIN_CODE.getValue().toString().equals(mode)) {
			return getOauth();
		} else {
			if (!barLoginIsOpen(mode)) {
				throw new WeixinOauthException("微信公众号扫码上机暂时关闭");
			}
			String areaId = GsonUtil.getStringFromJsonObject(extJson, "areaId");
			String barid = GsonUtil.getStringFromJsonObject(extJson, "barid");
			String scene = GsonUtil.getStringFromJsonObject(extJson, "scene");
			return buildBarLoginQrCode(areaId, mode, barid, scene);
		}

	}

	/**
	 * 获取业务线业务码--不涉及登录
	 * @param mode 业务类型 用于后续消息通知
	 */
	private Map<String, Object> buildBusinessQrCode(String mode) {
		WeixinOauthToken weixinOauthToken = getBySiteIdAndType(site_id, WeixinConstant.TYPE.AUTHORIZER) ;
		// 确保Token有效
		checkOauthToken(weixinOauthToken);
		// 创建一个随机场景值，用来查询是否已经关注
		String cacheUserKey = buildCacheUserKey() ;
		Integer qrcodeExpireSeconds = weixinOauthToken.getQrcodeExpireSecondsWithDefault();
		try {
			WeixinQrcode weixinQrcode = weixinOpenService.createQrcode(weixinOauthToken.getAccessToken(), cacheUserKey, qrcodeExpireSeconds);
			if (StringUtil.isNotBlank(weixinQrcode.getQrcodeUrl())) {//异常时不缓存数据，较少内存浪费
				cacheExtData(cacheUserKey, qrcodeExpireSeconds);
				redisOperation.set(WeixinConstant.NET_BAR_LOGIN + cacheUserKey, mode, qrcodeExpireSeconds);
			}
			if (weixinQrcode.getErrcode() != null) {
				int seconds = getCacheSeconds();
				//基于appid的缓存锁，避免大量报警
				if (weixinQrcode.getErrcode().equals(45009) &&
						RedisContext.getRedisCache().setNx(CacheKeyConstant.SSO.WX_ALARM_CACHE + weixinOauthToken.getAppId(), "1", seconds)) {
					//异步调用报警
					AsyncTaskExecutor.submit(new AlarmJob(weixinOauthToken.getAppId()));
				}
				throw new BaseStoneException(ErrorCode.C_1104);
			}
			return respQrcode(weixinQrcode, cacheUserKey, qrcodeExpireSeconds) ;
		} catch (Exception e){
			logger.error("获取场景值二维码异常[S]", e) ;
			throw new WeixinOauthException("获取场景值二维码异常[S]") ;
		}
	}

	/**
	 * 网吧扫码上机或者电竞酒店上机使用，勿动
	 * @param areaId 地区码4位
	 * @param mode 类型
	 * @param barid 网吧id
	 * @param scene 场景值
	 */
	private Map<String, Object> buildBarLoginQrCode(String areaId, String mode, String barid, String scene) {
		if (StringUtil.isBlank(areaId)) {
			areaId = "310100";//因计费存在缺失情况，所以默认为杭州市
		} else {
			//地区码目前是城市级，计费传入4位，数据库存储为6位，城市编码后2位皆为0，所以做补零操作
			areaId = areaId + "00";
		}
		boolean ixWorkIsOpen = RedisContext.getSwitchConfig(CacheKeyConstant.ConfigResourcesConstants.BAR_LOGIN_CONFIG,
				CacheKeyConstant.ConfigResourcesConstants.XING_YUN_WXWORK_SWITCH, false);
		AreaAppid areaAppid = getByAreaId(Integer.valueOf(areaId), Integer.valueOf(mode));
		if (ixWorkIsOpen && areaAppid.getChannel() != null && BarLoginEnum.ChannelEnum.XY_WX_WORK.getValue().equals(areaAppid.getChannel())) {
			return buildBarLoginQrCodeByXingYun(areaAppid, mode);
		}
		return buildBarLoginQrCodeByOpen(areaAppid, areaId, mode, barid, scene);
	}

	/**
	 * 网吧扫码上机或者电竞酒店上机使用，勿动 公众号渠道
	 * @param areaId 地区码4位
	 * @param mode 类型
	 * @param barid 网吧id
	 * @param scene 场景值
	 */
	private Map<String, Object> buildBarLoginQrCodeByOpen(AreaAppid areaAppid, String areaId, String mode, String barid, String scene) {
		WeixinOauthToken weixinOauthToken = weixinOauthTokenService.getByAppIdAndType(areaAppid.getAppid(), WeixinConstant.TYPE.AUTHORIZER) ;
		// 确保Token有效
		checkOauthToken(weixinOauthToken);
		// 创建一个随机场景值，用来查询是否已经关注
		String cacheUserKey = buildCacheUserKey() ;
		// 保存该场景值对应的siteId
		BarLoginReportInfo reportInfo = new BarLoginReportInfo(BarLoginEnum.ModeEnum.NET_BAR.getValue().toString().equalsIgnoreCase(mode) ?
				BarLoginReportEnum.OptTypeEnum.OPT_GET_QRCODE : BarLoginReportEnum.OptTypeEnum.OPT_TYPE_GET_QRCODE_HOTEL, extData);
		reportInfo.setAppid(weixinOauthToken.getAppId());
		reportInfo.setBarid(barid);
		reportInfo.setAreaId(areaId);
		reportInfo.setScene(scene);
		try {
			int qrCodeServerExpireSeconds = getServerExpireSeconds(Integer.valueOf(mode));
			WeixinQrcode weixinQrcode = weixinOpenService.createQrcode(weixinOauthToken.getAccessToken(), cacheUserKey, qrCodeServerExpireSeconds);
			if (StringUtil.isNotBlank(weixinQrcode.getQrcodeUrl())) {//异常时不缓存数据，较少内存浪费
				cacheExtData(cacheUserKey, qrCodeServerExpireSeconds);
				redisOperation.set(WeixinConstant.NET_BAR_LOGIN + cacheUserKey, mode, qrCodeServerExpireSeconds);
			}
			if (weixinQrcode.getErrcode() != null) {
				int seconds = getCacheSeconds();
				//基于appid的缓存锁，避免大量报警
				if (weixinQrcode.getErrcode().equals(45009) &&
						RedisContext.getRedisCache().setNx(CacheKeyConstant.SSO.WX_ALARM_CACHE + weixinOauthToken.getAppId(), "1", seconds)) {
					//异步调用报警
					AsyncTaskExecutor.submit(new AlarmJob(weixinOauthToken.getAppId()));
				}
				throw new BaseStoneException(ErrorCode.C_1104);
			}
			reportInfo.setExtInfo(BarLoginReportEnum.ExtInfo.INFO_0.getValue());
			reportLogger.info(GsonUtil.toJson(reportInfo));
			return respQrcode(weixinQrcode, cacheUserKey, qrCodeServerExpireSeconds, areaAppid.getTips()) ;
		} catch (Exception e){
			logger.error("获取场景值二维码异常[S]", e) ;
			reportInfo.setExtInfo(BarLoginReportEnum.ExtInfo.INFO_1.getValue());
			reportLogger.info(GsonUtil.toJson(reportInfo));
			throw new WeixinOauthException("获取场景值二维码异常[S]") ;
		}
	}

	/**
	 * 网吧扫码上机星云企微使用，勿动 星云渠道
	 * @param areaAppid 地区公众号配置
	 * @param mode 类型
	 */
	private Map<String, Object> buildBarLoginQrCodeByXingYun(AreaAppid areaAppid, String mode) {
		// 创建一个随机场景值，用来查询是否已经关注
		String cacheUserKey = buildCacheUserKey() ;
		try {
			appId = areaAppid.getAppid();//用于后续授权取对应的公众号配置
			int qrCodeServerExpireSeconds = getServerExpireSeconds(Integer.valueOf(mode));
			cacheExtData(cacheUserKey, qrCodeServerExpireSeconds);
			redisOperation.set(WeixinConstant.NET_BAR_LOGIN + cacheUserKey, mode, qrCodeServerExpireSeconds);
			WeixinQrcode weixinQrcode = new WeixinQrcode();
			weixinQrcode.setQrcodeUrl(BOOT_PC_URL + cacheUserKey);
			DcReportForWxWorkService.wxWorkReport(cacheUserKey, DcReportForWxWorkService.OptTypeAndExtInfo.GET_WX_WORK_QRCODE);
			return respQrcode(weixinQrcode, cacheUserKey, qrCodeServerExpireSeconds, areaAppid.getTips()) ;
		} catch (Exception e){
			logger.error("获取场景值二维码异常[S]", e) ;
			throw new WeixinOauthException("获取场景值二维码异常[S]") ;
		}
	}

	/**
     *  主体accessToken微信主动推送
     *  拿到verifyTicket后
     *  1. 解密
     *  2. 换取componentAccessToken
     *  3. 换取componentToken
     **/
	public String verifyTicket(){
		String encryptVerifyTicket;
		try {
			String xmlText = getInputStreamToStr();
			Document document = XmlUtil.parseXml(xmlText);
			Element rootElement = document.getRootElement();
			encryptVerifyTicket = rootElement.elementTextTrim("Encrypt");
		}catch (Exception e){
			logger.error("解析xml格式异常", e) ;
			return SUCCESS ;
		}
		// 解密
		WeixinComponentTicket weixinComponentTicket = weixinOpenService.decryptVerifyTicket(encryptVerifyTicket);
		if(null == weixinComponentTicket){
			logger.error("componentVerifyTicket解密异常") ;
			return SUCCESS ;
		}
		// 获取componentVerifyToken
		WeixinComponentToken weixinComponentToken = weixinOpenService.getComponentAccessToken(weixinComponentTicket.getComponentVerifyTicket());
		weixinComponentToken.setComponentAppId(weixinComponentTicket.getAppId()) ;
		WeixinOauthToken weixinOauthToken = buildWeixinOauthToken(weixinComponentToken) ;
		weixinOauthTokenService.updateToken(weixinOauthToken) ;
		return SUCCESS ;
	}

    /**
     * 解析微信传入的数据
     */
	private String getInputStreamToStr(){
		try {
			StringBuilder xmlText = new StringBuilder();
			String line;
			InputStreamReader reader = new InputStreamReader(getRequest().getInputStream(), getRequest().getCharacterEncoding());
			BufferedReader br = new BufferedReader(reader);
			while ((line = br.readLine()) != null) {
				if (line.length() > 0) {
					xmlText.append(line.trim());
				}
			}
			return xmlText.toString();
		}catch (Exception e){
			logger.error("读取流数据异常", e) ;
			return null ;
		}
	}

    /**
     * 构建主体微信accessToken
     * @param weixinComponentToken
     */
	private WeixinOauthToken buildWeixinOauthToken(WeixinComponentToken weixinComponentToken){
		WeixinOauthToken weixinOauthToken = new WeixinOauthToken() ;
		weixinOauthToken.setAppId(weixinComponentToken.getComponentAppId()) ;
		weixinOauthToken.setAccessToken(weixinComponentToken.getComponentAccessToken()) ;
		weixinOauthToken.setExpireTime(weixinComponentToken.getExpireTime());
		weixinOauthToken.setType(WeixinConstant.TYPE.COMPONENT);
		weixinOauthToken.setTimeEdit(new Date());
		weixinOauthToken.setState(WeixinConstant.STATE.OPEN) ;
		return weixinOauthToken ;
	}

    /**
     * 微信预授权地址，可生成微信授权码，接入方接入时需接入方公众号管理员预授权
     *  授权完整流程  goPreAuth()
     *                  1. 获取预授权码
     *                  2. 获取授权地址
     *                浏览器请求授权地址得到授权二维码
     *                接入方授权appid管理员微信扫码授权
     *    页面重定向->preAuthCallback()
     *
     * @throws UnsupportedEncodingException
     */
	public String goPreAuth() throws UnsupportedEncodingException {
		if(StringUtils.isBlank(site_id)){
			this.setErrorMsg("授权方site_id为空") ;
			return ERROR ;
		}
		int type = null == authType ? WeixinConstant.TYPE.AUTHORIZER : authType;

		if (!canPreAuth(site_id, type)) {
		    return ERROR;
		}

		WeixinOauthToken weixinOauthToken = getBySiteIdAndType(getSsoSiteId(), WeixinConstant.TYPE.COMPONENT) ;
		if (null == weixinOauthToken) {
			return ERROR ;
		}
		String preAuthCode = weixinOpenService.getPreAuthCode(weixinOauthToken.getAccessToken()) ;
		// 回调地址
		preAuthUrl = weixinOpenService.getPreAuthUrl(preAuthCode, site_id, type) ;
		return SUCCESS ;
	}

    /**
     * 检测当前siteid能否预授权
     * @param siteId
     * @param type
     */
	private boolean canPreAuth(String siteId, Integer type) {
        WeixinOauthToken weixinOauthToken = getBySiteIdAndType(siteId, type) ;
        if (null != weixinOauthToken) {
            if (weixinOauthToken.isOpen()) {
                this.setErrorMsg("接入方授权信息已经存在，无须重复授权");
                return false;
            }
            if (weixinOauthToken.isClose()) {
                this.setErrorMsg("接入方授权信息已经关闭，请联系运营人员");
                return false;
            }
            if (!weixinOauthToken.isCREATE()) {
                this.setErrorMsg("接入方授权信息状态异常，请联系运营人员");
                return false;
            }
        }
        return true;
    }

    /**
     * 微信预授权后回调
     * 新增或更新接入方对应的accessToken及refreshToken数据
     * 完成后接入方可开启微信公众号登录功能
     */
	public String preAuthCallback() {
		if (StringUtils.isBlank(auth_code)) {
			this.setErrorMsg("回调授权code为空");
			return ERROR;
		}
		if (StringUtils.isBlank(site_id)) {
			this.setErrorMsg("授权方site_id为空");
			return ERROR;
		}
		if (authType == null) {
            this.setErrorMsg("授权方authType为空");
            return ERROR;
        }

		if (!canPreAuth(site_id, authType)) {
		    return ERROR;
		}
		WeixinOauthToken weixinComponentToken = getBySiteIdAndType(getSsoSiteId(), WeixinConstant.TYPE.COMPONENT) ;
		if (null == weixinComponentToken) {
			logger.error("主体授权信息不存在");
			setErrorMsg("系统异常，联系运营人员") ;
			return ERROR ;
		}
        WeixinOauth weixinOauth = weixinOauthService.getBySiteId(site_id, authType) ;
        if(null == weixinOauth){
            throw new WeixinOauthException("接入方授权信息不存在，或未开启") ;
        }
		try {
			WeixinAuthorizerToken authorizerAccessToken = weixinOpenService.getAuthorizerAccessToken(auth_code, weixinComponentToken.getAccessToken());
			if(StringUtils.isBlank(authorizerAccessToken.getAuthorizerAccessToken()) || StringUtils.isBlank(authorizerAccessToken.getAuthorizerRefreshToken())){
				logger.error("授权信息获取失败:{}", new Gson().toJson(authorizerAccessToken)) ;
				this.setErrorMsg("授权信息获取失败") ;
				return ERROR ;
			}
			if(!authorizerAccessToken.getAuthorizerAppId().equals(weixinOauth.getAppId())){
				logger.error("授权主体[{}]与授权信息[{}]不一致", authorizerAccessToken.getAuthorizerAppId(),weixinOauth.getAppId()) ;
				this.setErrorMsg("授权主体与授权信息不一致");
				return ERROR ;
			}
			WeixinOauthToken authorizerOauthToken = getBySiteIdAndType(site_id, authType);
			if(null == authorizerOauthToken){ //此时先创建
				authorizerOauthToken = new WeixinOauthToken() ;
				authorizerOauthToken.setTimeAdd(new Date()) ;
				authorizerOauthToken.setTimeEdit(new Date());
				authorizerOauthToken.setAppId(weixinOauth.getAppId()) ;
				authorizerOauthToken.setType(authType) ;
				authorizerOauthToken.setState(WeixinConstant.STATE.CREATE) ;
				weixinOauthTokenService.save(authorizerOauthToken) ;
			}
			authorizerOauthToken.setAccessToken(authorizerAccessToken.getAuthorizerAccessToken()) ;
			authorizerOauthToken.setRefreshToken(authorizerAccessToken.getAuthorizerRefreshToken()) ;
			authorizerOauthToken.setExpireTime(authorizerAccessToken.getExpireTime()) ;
			weixinOauthTokenService.openAuthorizer(authorizerOauthToken) ;

		}catch (Exception e){
			logger.error("授权处理异常", e) ;
			this.setErrorMsg("授权信息处理失败，请重新授权或联系运营人员") ;
			return ERROR ;
		}
		return SUCCESS ;
	}

    /**
     * 用户扫码后，微信服务端回调，
     * 处理完成后将ticket及tokenId写入缓存
     * 页面定时查询感知登录及获取登录票据完成登录跳转
     */
	@Override
	public String oauthCallback() {
		// 读取流获取数据
		String encryptResponse;
		try {
			String xmlText = getInputStreamToStr();
			logger.info("微信授权回调结果:{}", xmlText) ;
			assert xmlText != null;
			Document document = XmlUtil.parseXml(xmlText);
			document.setXMLEncoding("UTF-8");
			Element rootElement = document.getRootElement();
			encryptResponse = rootElement.elementText("Encrypt") ;
		}catch (Exception e){
			logger.error("解析微信授权回调异常", e);
			responseWrite(ERROR);
			return null;
		}
		// 解密参数
		WeixinOauthInfo weixinOauthInfo = weixinOpenService.decryptOauthCallback(encryptResponse);
		String cacheUserKey = weixinOauthInfo.getEventKey() ;
		if(StringUtils.isNotBlank(cacheUserKey)){
			site_id = redisOperation.get(WeixinConstant.EXT_SCENE_KEY + cacheUserKey) ;
            reportData = redisOperation.get(WeixinConstant.EXT_SCENE_KEY_REPORT_DATA + cacheUserKey) ;
        }
		initContextInfo();
		String result = SUCCESS;//默认返回success
		switch (weixinOauthInfo.getMsgType()){
			case WeixinConstant.MSG_TYPE.EVENT:
				if (StringUtil.isNotBlank(site_id) || WeixinConstant.EVENT_TYPE.UNSUBSCRIBE.equalsIgnoreCase(weixinOauthInfo.getEvent())) {
					result = processEvent(weixinOauthInfo);
				} else {
					logger.info("siteId为null，可能是单纯关注公众号");
				}
				break;
			case WeixinConstant.MSG_TYPE.TEXT :
				result = processText(weixinOauthInfo) ;
				break;
			default:
				break;
		}
		responseWrite(result);
		return null ;
	}

    /**
     *  处理事件
     */
	private String processEvent(WeixinOauthInfo weixinOauthInfo){
		switch (weixinOauthInfo.getEvent().toUpperCase()){
			case WeixinConstant.EVENT_TYPE.UNSUBSCRIBE:
				return processUnsubscribe(weixinOauthInfo);
			case WeixinConstant.EVENT_TYPE.SCAN:
			case WeixinConstant.EVENT_TYPE.SUBSCRIBE:
				return processSubscribe(weixinOauthInfo) ;
			default:
				return SUCCESS ;
		}
	}

    /**
     * 处理文本消息
     */
	private String processText(WeixinOauthInfo weixinOauthInfo){
		try {
			String content = weixinOauthInfo.getContent() ;
			if (StringUtil.isBlank(content)) {
				return SUCCESS;
			}
			if("TESTCOMPONENT_MSG_TYPE_TEXT".equals(content)){ // 全网发布微信测试消息
				// 需要加密返回
				String reply = "TESTCOMPONENT_MSG_TYPE_TEXT_callback" ;
				return weixinOpenService.replyMsg(reply, weixinOauthInfo, AppidReplyEnum.ContentType.WORD.getValue());
			}
			if(content.startsWith("QUERY_AUTH_CODE:")){ // 全网发布微信测试消息
				String queryAuthCode = content.substring(content.indexOf(":")+1) ;
				String openId = weixinOauthInfo.getFromUserName() ;
				WeixinOauthToken weixinComponentToken = getBySiteIdAndType(getSsoSiteId(), WeixinConstant.TYPE.COMPONENT) ;
				WeixinAuthorizerToken authorizerAccessToken = weixinOpenService.getAuthorizerAccessToken(queryAuthCode, weixinComponentToken.getAccessToken());
				// 异步发送文本消息
				AsyncTaskExecutor.submit(() -> {
					try {
						weixinOpenService.sendTextMsg(authorizerAccessToken, openId, queryAuthCode + "_from_api");
					}catch (Exception e){
						logger.error("发送文本消息异常", e) ;
					}
				});
			}
		} catch (Exception e) {
			logger.error("返回结果数据流异常", e);
		}
		return SUCCESS;
	}

	private String processUnsubscribe(WeixinOauthInfo weixinOauthInfo) {
		try {
			if (weixinOauthInfo.getFromUserName() != null) {
				WeixinOpenIdUnionId weixinOpenIdUnionId = weixinOpenIdUnionIdService.getByOpenId(weixinOauthInfo.getFromUserName());
				if (weixinOpenIdUnionId != null) {
					weixinOpenIdUnionIdService.delete(weixinOpenIdUnionId);
				}
			}
		} catch (Exception e) {
			LOG.error("删除微信openId与unionId关系异常", e);
		}
		return SUCCESS;
	}

    /**
     * 处理关注事件
     */
	private String processSubscribe(WeixinOauthInfo weixinOauthInfo){
		try {
			logger.info("处理关注事件:{}", GSON.toJson(weixinOauthInfo)) ;
			WeixinOauth weixinOauth = weixinOauthService.getBySiteId(site_id, WeixinConstant.TYPE.AUTHORIZER);
			WeixinOauthToken weixinOauthToken = weixinOauthTokenService.getByAppIdAndType(appId,WeixinConstant.TYPE.AUTHORIZER) ;
			WeixinUser weixinUser = weixinOpenService.queryWeixinUserInfo(weixinOauthInfo.getFromUserName(),weixinOauthToken.getAccessToken() ) ;
			logger.info(weixinUser.getNickname() + ' ' + weixinUser.getHeadimgurl());

			String sceneKey = weixinOauthInfo.getEventKey();
			//还原接入方透传的参数 专用
			pullCacheExtData(sceneKey);

            memberOutSite = buildMemberOutSite(weixinUser, UserOutsiteConstant.WXGZH_INTERFACE_ID, appId);
            if (memberOutSite == null) {
            	return ERROR;
			}
            MemberOutSiteDao memberOutSiteDao = memberOutSite.getDao();

            member = memberOutSiteDao.getByOutMemberId(memberOutSite.getOutMemberId());
            // 外部用户不存在
            if (member == null) {
                member = outSiteMemberRegister(memberOutSite);
            } else if (member.getMemberState() == MemberConstants.USER_CANCEL_STATE) {
                QrCodeResponse result = new QrCodeResponse(QrCodeResponse.TypeStage.USER_CANCEL);
                result.setMemberName(member.getMemberName());
                result.setSiteId(site_id);
                result.setOptToken(AesEncrypt.Encrypt(member.getMemberName() + site_id, MemberUtil.getKey()).substring(8));

                redisOperation.set(weixinOauthInfo.getEventKey(), result, 1, TimeUnit.MINUTES) ;
                return ERROR;
            } else {
                updateHeadAndNick(weixinUser);
            }
            //获取请求二维码的原始客户端ip到登录日志
            member.setClientIp(clientIp);
            MemberUtil.doLogin(member, memberOutSite);
            memberName = member.getMemberName();
			UserLoginSessionUtil.saveSession(memberName, UserLoginSessionUtil.LoginType.SSO_OUT.getType() + getInterfaceId(), null, site_id);

			WeixinOpenIdUnionId weixinOpenIdUnionId = new WeixinOpenIdUnionId();
			weixinOpenIdUnionId.setAppId(appId);
			weixinOpenIdUnionId.setUnionId(weixinUser.getUnionid());
			weixinOpenIdUnionId.setOpenId(weixinUser.getOpenid());
			weixinOpenIdUnionId.setOriginFrom(WeixinOpenIdUnionId.ORIGIN_FROM.WEIXIN_OPEN_AUTH.name());
			saveIfNotExist(weixinOpenIdUnionId);

            String mode = redisOperation.get(WeixinConstant.NET_BAR_LOGIN + sceneKey);
			QrCodeResponse result;
            if (StringUtil.isNotBlank(mode) && !BarLoginEnum.ModeEnum.SCANNING_LOGIN_CODE.getValue().toString().equals(mode)) {
            	if (BarLoginEnum.ModeEnum.SCANNING_NO_LOGIN_CODE.getValue().toString().equals(mode)) {
            		return businessScanProcess(weixinUser, weixinOauthInfo);
				}
				//走计费上机流程
            	return barLoginProcess(weixinUser, weixinOauthInfo);
			}

 			initLoginElement(super.getSite_id());
			if (Boolean.TRUE.equals(isSingleAccount)) {
				if (INNER_SCAN.equals(inner)) {
					return innerScanProcess(weixinOauthInfo, weixinUser.getOpenid());
				}
				if (getSingleAccount() == null) {
					doReport(member, ReportEntity.InterfaceType.login);
					setSingleBindSign(createSingleBindSign());

					//开关打开，发送小程序消息，进入小程序进行手机号绑定
					if ("y".equals(weixinOauth.getMiniLoginSwitch())){
						result = new QrCodeResponse(QrCodeResponse.TypeStage.INIT);
						result.setSingle(true);
						result.setSingleBindSign(getSingleBindSign());
						redisOperation.set(weixinOauthInfo.getEventKey(), result, 30, TimeUnit.MINUTES);
						Integer bindState = BarLoginEnum.BindStateEnum.WX_BIND_MOBILE.getValue();
						boolean sendResult = weixinOpenService.sendCardMsg(weixinOauthInfo.getFromUserName(), weixinOauthInfo.getEventKey(), super.getSite_id(),
								CacheKeyConstant.ConfigResourcesConstants.WEIXIN_LOGIN_REPLY_MSG, CacheKeyConstant.ConfigResourcesConstants.AI_WEIXIN_CARD_MSG, null);
						return sendResult ? SUCCESS : weixinOpenService.replyMsg(weixinMsgService.getWxLoginToBindMobileMsg(appId, bindState, weixinOauthInfo.getEventKey()), weixinOauthInfo, AppidReplyEnum.ContentType.WORD.getValue());
					}

					result = new QrCodeResponse(QrCodeResponse.TypeStage.SINGLE_ACCOUNT_BIND);
					result.setSingle(true);
					result.setSingleBindSign(getSingleBindSign());
					// 向缓存中添加，通过场景值，缓存1分钟
					redisOperation.set(weixinOauthInfo.getEventKey(), result, 1, TimeUnit.MINUTES);
					String wxLoginSingleSuccMsg = weixinMsgService.getWxLoginSingleSuccMsg(appId);
					return weixinOpenService.replyMsg(wxLoginSingleSuccMsg, weixinOauthInfo, AppidReplyEnum.ContentType.WORD.getValue());
				}
				if (member.getMemberState() == MemberConstants.USER_CANCEL_STATE) {
					doReport(member, ReportEntity.InterfaceType.login);
					result = new QrCodeResponse(QrCodeResponse.TypeStage.USER_CANCEL);
					result.setMemberName(member.getMemberName());
					result.setSiteId(site_id);
					result.setOptToken(AesEncrypt.Encrypt(member.getMemberName() + site_id, MemberUtil.getKey()).substring(8));

					redisOperation.set(sceneKey, result, 1, TimeUnit.MINUTES) ;
					return ERROR;
				}
				UserLoginSessionUtil.saveMasterSession(memberName, member.getMemberName(), UserLoginSessionUtil.LoginType.SSO_OUT.getType() + getInterfaceId(), null, site_id);
			}
			member.setOpenId( weixinUser.getOpenid() );
			login(member);
			result = new QrCodeResponse(QrCodeResponse.TypeStage.LOGIN_SUCCESS);
			result.setSingle(false);
			result.setTicket(getTicket());
			result.setTockenId(getToken());
			result.setClientTicket(getClientTicket());
			// 向缓存中添加，通过场景值，缓存1分钟
			redisOperation.set(weixinOauthInfo.getEventKey(), result, 1, TimeUnit.MINUTES);
			String wxLoginMultipleSuccMsg = weixinMsgService.getWxLoginMultipleSuccMsg(appId, member.getMemberName());
			return weixinOpenService.replyMsg(wxLoginMultipleSuccMsg, weixinOauthInfo, AppidReplyEnum.ContentType.WORD.getValue());

		} catch (Exception e) {
			logger.error("授权处理异常:[{}]", e.getMessage(), e);
			return ERROR ;
		}
	}

	private String businessScanProcess(WeixinUser weixinUser, WeixinOauthInfo weixinOauthInfo) throws Exception {
		JsonObject extJson = GsonUtil.jsonToBean(extData, JsonObject.class);
		String businessScene = GsonUtil.getStringFromJsonObject(extJson, "businessScene");
		String replyMsg = GsonUtil.getStringFromJsonObject(extJson, "replyMsg");
		QrCodeResponse result = new QrCodeResponse(QrCodeResponse.TypeStage.BUSINESS_SCAN);
		result.setUnionId(weixinUser.getUnionid());
		result.setOpenId(weixinUser.getOpenid());
		result.setMemberName(member.getMemberName());
		result.setMemberId(member.getMemberId());
		result.setBusinessScene(businessScene);

		redisOperation.set(weixinOauthInfo.getEventKey(), result, 1, TimeUnit.MINUTES);
		if (StringUtil.isBlank(replyMsg)) {
			replyMsg = weixinMsgService.getWxBusinessScanReplayMsg(appId, member.getMemberName());
		} else {
			replyMsg = replyMsg.replace("$account$", member.getMemberName()).replace( "$time$", DateUtil.ymdhmsFormat( new Date() ) ).replace( "$memberName$",
					Base64Util.encode(member.getMemberName()) );
		}
		return weixinOpenService.replyMsg(replyMsg, weixinOauthInfo, AppidReplyEnum.ContentType.WORD.getValue());
	}

	/**
	 * 内部扫码逻辑，目前仅计费上机使用，所以未对业务类型判断，若后续有新业务需要新增判断逻辑
	 * @param weixinOauthInfo 微信返回数据（已解密）
	 */
	private String innerScanProcess(WeixinOauthInfo weixinOauthInfo, String openId) throws Exception {
		String sceneKey = weixinOauthInfo.getEventKey();
		QrCodeResponse result = new QrCodeResponse();
		String replyMsg = "";
		boolean sendResult = false;
		Map<String, String> extInfo = redisOperation.getMap(CacheKeyConstant.SSO.INNER_SCAN + sceneKey, String.class, String.class);
		Member netBarMember = null;
		if (extInfo == null) {
			replyMsg = "数据已过期";
			result = new QrCodeResponse(QrCodeResponse.TypeStage.ERROR);
			result.setErrorMsg(replyMsg);
		} else {
			netBarMember = member.getDao().getByName(extInfo.get("memberName"));
		}
		if (netBarMember == null) {
			replyMsg = "会员数据异常";
			result = new QrCodeResponse(QrCodeResponse.TypeStage.ERROR);
			result.setErrorMsg(replyMsg);
		}
		if (QrCodeResponse.TypeStage.ERROR.getStage().equals(result.getStage())) {
			redisOperation.set(weixinOauthInfo.getEventKey(), result, 1, TimeUnit.MINUTES);
			return weixinOpenService.replyMsg(replyMsg, weixinOauthInfo, AppidReplyEnum.ContentType.WORD.getValue());
		}

		MemberAccountBind temp = memberAccountBindDao.getByIdCard(netBarMember.getMemberId());
		if (temp != null) {
			if (temp.getWeixin() == null) {
				temp.beginBuildLog("WEIXIN帐号绑定");
				temp.setWeixin(member.getMemberId());
				singleAccountUpdateExt(temp);
				DcReportService.freeLoginReport(member.getMemberName(), netBarMember.getMemberName(), DcReportService.OptTypeAndExtInfo.WX_OPEN_BIND_WX_SUCCESS, getSite_id());
			}
			if (!temp.getWeixin().equals(member.getMemberId())) {
				replyMsg = "已绑定其他微信帐号";
				result = new QrCodeResponse(QrCodeResponse.TypeStage.ERROR);
				result.setErrorMsg(replyMsg);
				DcReportService.freeLoginReport(member.getMemberName(), netBarMember.getMemberName(), DcReportService.OptTypeAndExtInfo.WX_OPEN_BIND_WX_FAIL, getSite_id());
			} else {
				result.setStage(QrCodeResponse.TypeStage.LOGIN_SUCCESS.getStage());
				replyMsg = weixinMsgService.buildWxBarFreeLoginNotice(appId, member.getMemberName(), BarLoginEnum.BindStateEnum.LOGIN_SUCC.getValue(), sceneKey);
				member = memberDao.getByMemberId(temp.getMemberId());
				bindNetBarAuth(extInfo.get("memberName"), netBarMember.getMemberId(), member.getMemberName(), DcReportService.OptTypeAndExtInfo.WX_OPEN_BAR_AUTH);
				wxLogin(result, openId);
				UserLoginSessionUtil.saveMasterSession(memberName, member.getMemberName(), UserLoginSessionUtil.LoginType.SSO_OUT.getType() + getInterfaceId(), null, site_id);
				DcReportService.freeLoginReport(member.getMemberName(), netBarMember.getMemberName(), DcReportService.OptTypeAndExtInfo.WX_OPEN_LOGIN_SUCCESS, getSite_id());
				sendResult = sendCardMsgForFreeLogin(openId, sceneKey, BarLoginEnum.CardMsgType.AUTO_LOGIN.getName(), false);
				DcReportService.freeLoginReport(member.getMemberName(), netBarMember.getMemberName(), sendResult ?
						DcReportService.OptTypeAndExtInfo.WX_CARD_MSG_SUB_SEND_SUCCESS : DcReportService.OptTypeAndExtInfo.WX_CARD_MSG_SUB_SEND_FAIL, getSite_id());

			}
		} else {
			MemberAccountBind singleAccount = getSingleAccount();
			if (singleAccount == null) {
				result.setStage(QrCodeResponse.TypeStage.INIT.getStage());
				int type = RedisContext.getIntConfig(CacheKeyConstant.ConfigResourcesConstants.NET_BAR_FREE_LOGIN_CONFIG,
						CacheKeyConstant.ConfigResourcesConstants.SINGLE_BIND_TYPE, BarLoginEnum.BindMsgType.MINI_MSG.getValue());
				if (BarLoginEnum.BindMsgType.MINI_MSG.getValue() == type) {//小程序
					sendResult = sendCardMsgForFreeLogin(openId, sceneKey, BarLoginEnum.CardMsgType.BIND_MOBILE.getName(), true);
					DcReportService.freeLoginReport(member.getMemberName(), netBarMember.getMemberName(), sendResult ?
							DcReportService.OptTypeAndExtInfo.WX_CARD_MSG_BIND_SEND_SUCCESS : DcReportService.OptTypeAndExtInfo.WX_CARD_MSG_BIND_SEND_FAIL, getSite_id());
				}
				if (!sendResult) {
					replyMsg = weixinMsgService.buildWxBarFreeLoginNotice(appId, member.getMemberName(), BarLoginEnum.BindStateEnum.BIND_MOBILE.getValue(), sceneKey);
					String singleBindSign = createSingleBindSign();
					replyMsg = replyMsg.replace("$singleBindSign$", singleBindSign)
							.replace("$site_id$", getSite_id())
							.replace("$businessScene$", sceneKey);
					result.setOpenId(openId);
				}
			} else {
				if (singleAccount.getIdCard() == null || singleAccount.getIdCard() == 0) {
					sendResult = sendCardMsgForFreeLogin(openId, sceneKey, BarLoginEnum.CardMsgType.AUTH_AGREE.getName(), true);
					if (!sendResult) {
						singleAccount.setIdCard(netBarMember.getMemberId());
						singleAccount.setTimeEdit(new Date());
						memberAccountBindDao.update(singleAccount);
					}
					DcReportService.freeLoginReport(member.getMemberName(), netBarMember.getMemberName(),
							sendResult ? DcReportService.OptTypeAndExtInfo.WX_CARD_MSG_AUTH_SEND_SUCCESS : DcReportService.OptTypeAndExtInfo.WX_OPEN_BIND_ID_CARD_SUCCESS, getSite_id());
				}
				if (sendResult) {
					result.setStage(QrCodeResponse.TypeStage.INIT.getStage());
				} else if (!singleAccount.getIdCard().equals(netBarMember.getMemberId())) {
					replyMsg = "已绑定其他会员帐号";
					result = new QrCodeResponse(QrCodeResponse.TypeStage.ERROR);
					result.setErrorMsg(replyMsg);
					DcReportService.freeLoginReport(member.getMemberName(), netBarMember.getMemberName(), DcReportService.OptTypeAndExtInfo.WX_OPEN_BIND_ID_CARD_FAIL, getSite_id());
				} else {
					result.setStage(QrCodeResponse.TypeStage.LOGIN_SUCCESS.getStage());
					replyMsg = weixinMsgService.buildWxBarFreeLoginNotice(appId, member.getMemberName(), BarLoginEnum.BindStateEnum.LOGIN_SUCC.getValue(), sceneKey);
					bindNetBarAuth(extInfo.get("memberName"), netBarMember.getMemberId(), member.getMemberName(), DcReportService.OptTypeAndExtInfo.WX_OPEN_BAR_AUTH);
					wxLogin(result, openId);
					UserLoginSessionUtil.saveMasterSession(memberName, member.getMemberName(), UserLoginSessionUtil.LoginType.SSO_OUT.getType() + getInterfaceId(), null, site_id);
					DcReportService.freeLoginReport(member.getMemberName(), netBarMember.getMemberName(), DcReportService.OptTypeAndExtInfo.WX_OPEN_LOGIN_SUCCESS, getSite_id());
					sendResult = sendCardMsgForFreeLogin(openId, sceneKey, BarLoginEnum.CardMsgType.AUTO_LOGIN.getName(), false);
					DcReportService.freeLoginReport(member.getMemberName(), netBarMember.getMemberName(), sendResult ?
							DcReportService.OptTypeAndExtInfo.WX_CARD_MSG_SUB_SEND_SUCCESS : DcReportService.OptTypeAndExtInfo.WX_CARD_MSG_SUB_SEND_FAIL, getSite_id());
				}
			}
		}
		redisOperation.set(weixinOauthInfo.getEventKey(), result, 30, TimeUnit.MINUTES);
		//如果卡片消息发送成功，则不发送其他消息
		if (sendResult) {
			return SUCCESS;
		}
		return weixinOpenService.replyMsg(replyMsg, weixinOauthInfo, AppidReplyEnum.ContentType.WORD.getValue());
	}

	/**
	 * 发送卡片消息
	 * @param openId 当前微信帐号的openId
	 * @param sceneKey 场景值
	 * @param type 消息类型
	 * @param showAgreement 是否需要加载协议，用于小程序交互
	 */
	private boolean sendCardMsgForFreeLogin(String openId, String sceneKey, String type, boolean showAgreement) {
		Map<String, Object> extInfoMap = new HashMap<>();
		extInfoMap.put("accessSiteId", getSite_id());
		extInfoMap.put("showAgreement", showAgreement);
		return weixinOpenService.sendCardMsg(openId, sceneKey, type, CacheKeyConstant.ConfigResourcesConstants.NET_BAR_FREE_LOGIN_CONFIG,
				CacheKeyConstant.ConfigResourcesConstants.CARD_MSG_ID, GsonUtil.toJson(extInfoMap));
	}

	public String canBindNetBar(String number, String sceneKey) {
		if (StringUtil.isBlank(sceneKey)) {
			return null;
		}
		MemberAccountBind temp = memberAccountBindDao.getByMobile(number);
		if (temp == null) {
			return null;
		}
		Map<String, String> extInfo = redisOperation.getMap(CacheKeyConstant.SSO.INNER_SCAN + sceneKey, String.class, String.class);
		Member netBarMember;
		if (extInfo == null) {
			return "数据已过期";
		} else {
			netBarMember = member.getDao().getByName(extInfo.get("memberName"));
		}
		if (netBarMember == null) {
			return "会员数据异常";
		}
		if (!temp.getIdCard().equals(netBarMember.getMemberId())) {
			return "已绑定其他身份证帐号";
		}
		return null;
	}

	/**
	 * 内部扫码逻辑，目前仅计费上机使用，所以未对业务类型判断，若后续有新业务需要新增判断逻辑
	 */
	public String bindNetBarProcess(Member member, String sceneKey, QrCodeResponse result, String siteId) {
		String replyMsg = "";
		boolean sendResult;
		this.site_id = siteId;
		String openId = result.getOpenId();
		Map<String, String> extInfo = redisOperation.getMap(CacheKeyConstant.SSO.INNER_SCAN + sceneKey, String.class, String.class);
		Member netBarMember = null;
		if (extInfo == null) {
			replyMsg = "数据已过期";
			result = new QrCodeResponse(QrCodeResponse.TypeStage.ERROR);
			result.setErrorMsg(replyMsg);
		} else {
			netBarMember = member.getDao().getByName(extInfo.get("memberName"));
		}
		if (netBarMember == null) {
			replyMsg = "会员数据异常";
			result = new QrCodeResponse(QrCodeResponse.TypeStage.ERROR);
			result.setErrorMsg(replyMsg);
		}
		if (QrCodeResponse.TypeStage.ERROR.getStage().equals(result.getStage())) {
			redisOperation.set(sceneKey, result, 1, TimeUnit.MINUTES);
			return replyMsg;
		}

		MemberAccountBind temp = memberAccountBindDao.getByIdCard(netBarMember.getMemberId());
		if (temp != null) {
			if (!temp.getMemberId().equals(member.getMemberId())) {
				replyMsg = "已绑定其他手机";
				result = new QrCodeResponse(QrCodeResponse.TypeStage.ERROR);
				result.setErrorMsg(replyMsg);
				DcReportService.freeLoginReport(member.getMemberName(), netBarMember.getMemberName(), DcReportService.OptTypeAndExtInfo.H5_BIND_BIND_MOBILE_FAIL, getSite_id());
			} else {
				result.setStage(QrCodeResponse.TypeStage.LOGIN_SUCCESS.getStage());
				member = memberDao.getByMemberId(temp.getMemberId());
				bindNetBarAuth(extInfo.get("memberName"), netBarMember.getMemberId(), member.getMemberName(), DcReportService.OptTypeAndExtInfo.H5_BIND_BAR_AUTH);
				DcReportService.freeLoginReport(member.getMemberName(), netBarMember.getMemberName(), DcReportService.OptTypeAndExtInfo.H5_BIND_LOGIN_SUCCESS, getSite_id());
				sendResult = sendCardMsgForFreeLogin(openId, sceneKey, BarLoginEnum.CardMsgType.AUTO_LOGIN.getName(), false);
				DcReportService.freeLoginReport(member.getMemberName(), netBarMember.getMemberName(), sendResult ?
						DcReportService.OptTypeAndExtInfo.WX_CARD_MSG_SUB_SEND_SUCCESS : DcReportService.OptTypeAndExtInfo.WX_CARD_MSG_SUB_SEND_FAIL, getSite_id());
			}
		} else {
			MemberAccountBind singleAccount = memberAccountBindDao.getByMemberId(member.getMemberId());
			if (singleAccount.getIdCard() == null || singleAccount.getIdCard() == 0) {
				singleAccount.setIdCard(netBarMember.getMemberId());
				singleAccount.setTimeEdit(new Date());
				memberAccountBindDao.update(singleAccount);
				DcReportService.freeLoginReport(member.getMemberName(), netBarMember.getMemberName(), DcReportService.OptTypeAndExtInfo.H5_BIND_BIND_ID_CARD_SUCCESS, getSite_id());
			}
			if (!singleAccount.getIdCard().equals(netBarMember.getMemberId())) {
				replyMsg = "已绑定其他会员帐号";
				result = new QrCodeResponse(QrCodeResponse.TypeStage.ERROR);
				result.setErrorMsg(replyMsg);
				DcReportService.freeLoginReport(member.getMemberName(), netBarMember.getMemberName(), DcReportService.OptTypeAndExtInfo.H5_BIND_BIND_ID_CARD_FAIL, getSite_id());
			} else {
				result.setStage(QrCodeResponse.TypeStage.LOGIN_SUCCESS.getStage());
				bindNetBarAuth(extInfo.get("memberName"), netBarMember.getMemberId(), member.getMemberName(), DcReportService.OptTypeAndExtInfo.H5_BIND_BAR_AUTH);
				DcReportService.freeLoginReport(member.getMemberName(), netBarMember.getMemberName(), DcReportService.OptTypeAndExtInfo.H5_BIND_LOGIN_SUCCESS, getSite_id());
				sendResult = sendCardMsgForFreeLogin(openId, sceneKey, BarLoginEnum.CardMsgType.AUTO_LOGIN.getName(), false);
				DcReportService.freeLoginReport(member.getMemberName(), netBarMember.getMemberName(), sendResult ?
						DcReportService.OptTypeAndExtInfo.WX_CARD_MSG_SUB_SEND_SUCCESS : DcReportService.OptTypeAndExtInfo.WX_CARD_MSG_SUB_SEND_FAIL, getSite_id());
			}
		}
		redisOperation.set(sceneKey, result, 30, TimeUnit.MINUTES);
		return replyMsg;
	}

	private String barLoginProcess(WeixinUser weixinUser, WeixinOauthInfo weixinOauthInfo) throws Exception {
		this.setClientTicket(ClientTicketContext.createClientTicket(member,site_id).toString());
		doReport(member, ReportEntity.InterfaceType.login);

		JsonObject extJson = GsonUtil.jsonToBean(extData, JsonObject.class);
		String mode = GsonUtil.getStringFromJsonObject(extJson, "mode");
		String areaId = GsonUtil.getStringFromJsonObject(extJson, "areaId");
		String barid = GsonUtil.getStringFromJsonObject(extJson, "barid");
		String scene = GsonUtil.getStringFromJsonObject(extJson, "scene");
		//默认mode为网吧 即1
		if (StringUtil.isBlank(mode)) {
			mode = BarLoginEnum.ModeEnum.NET_BAR.getValue().toString();
		}
		//上机扫码回调上报大数据
		BarLoginReportInfo reportInfo = new BarLoginReportInfo(BarLoginEnum.ModeEnum.NET_BAR.getValue().toString().equalsIgnoreCase(mode) ?
				BarLoginReportEnum.OptTypeEnum.OPT_QRCODE_CALLBACK : BarLoginReportEnum.OptTypeEnum.OPT_TYPE_QRCODE_CALLBACK_HOTEL, extData);
		reportInfo.setUnionId(weixinUser.getUnionid());
		reportInfo.setAppid(appId);
		reportInfo.setBarid(barid);
		reportInfo.setAreaId(areaId);
		reportInfo.setScene(scene);
		reportInfo.setExtInfo(weixinOauthInfo.getEvent().equalsIgnoreCase("subscribe") ?
				BarLoginReportEnum.ExtInfo.INFO_0.getValue() : BarLoginReportEnum.ExtInfo.INFO_1.getValue());
		if (reportLogger.isInfoEnabled()) {
			reportLogger.info(GsonUtil.toJson(reportInfo));
		}
		if (BarLoginEnum.ModeEnum.E_SPORT_HOTEL.getValue().toString().equalsIgnoreCase(mode)) {
			// 此段逻辑未构建QrCodeResponse 内容，所以不知道嵌入页的自动跳转，后续如需要，应把对应结果写入缓存
			reportInfo.setOptType(BarLoginReportEnum.OptTypeEnum.OPT_TYPE_REPLY_MSG_HOTEL.getValue());
			AppidReply appidReply = weixinMsgService.buildWxHotelLoginNotice(reportInfo, scene, appId, getClientTicket());
			if (appidReply.getContentType() == AppidReplyEnum.ContentType.WORD.getValue()){
				return weixinOpenService.replyMsg(appidReply.getReplyMsg(), weixinOauthInfo, AppidReplyEnum.ContentType.WORD.getValue());
			}

			return weixinOpenService.replyMsg(appidReply.getMediaId(), weixinOauthInfo, AppidReplyEnum.ContentType.IMAGE.getValue());
		}
		WxIdCardBind wxIdCardBind = wxIdCardBindDao.getByUnionId(weixinUser.getUnionid());
		QrCodeResponse result = new QrCodeResponse(QrCodeResponse.TypeStage.NET_BAR_LOGIN);
		result.setClientTicket(getClientTicket());
		result.setExtData(extData);
		result.setUnionId(weixinUser.getUnionid());
		if (wxIdCardBind == null || StringUtil.isBlank(wxIdCardBind.getIdCardNo())) {
			reportInfo.setOptType(BarLoginReportEnum.OptTypeEnum.OPT_TYPE_NEED_BIND.getValue());
			// 向缓存中添加，通过场景值，缓存30分钟 用于后续绑定身份证后完成上机请求 binded
			result.setOpenId(weixinUser.getOpenid());
			redisOperation.set(weixinOauthInfo.getEventKey(), result, 30, TimeUnit.MINUTES);
		} else {
			reportInfo.setOptType(BarLoginReportEnum.OptTypeEnum.OPT_TYPE_HAS_BIND.getValue());
			redisOperation.set(weixinOauthInfo.getEventKey(), result, 1, TimeUnit.MINUTES);
			//已绑定关系，则直接异步调用上机接口
			AsyncTaskExecutor.submitForBar(new NetBarLoginJob(weixinOauthInfo.getEventKey(), wxIdCardBind.getIdCardNoSend(), netBarService));
		}

		//sso发送微信扫码上机绑定身份证消息是否开启
		String openAppIds = weixinMsgService.getWxBindIdCardMsgSwitch();
		if (openAppIds.contains(appId)){
			return SUCCESS;
		}
		//如果轮询消息发送开关开启，并且已绑身份证则由wxmsg来发消息，否则发默认消息
		if (weixinMsgService.sendPollWxMsgIsOpen(appId) && wxIdCardBind != null){
			return SUCCESS;
		}
		String memberName = weixinUser.getNickname();

		AppidReply appidReply;
		if (reportInfo.getOptType().equals(BarLoginReportEnum.OptTypeEnum.OPT_TYPE_NEED_BIND.getValue())) {
			appidReply = weixinMsgService.buildWxBarBindNotice(reportInfo, appId, weixinOauthInfo.getEventKey(), barid);
		} else {
			assert wxIdCardBind != null;
			appidReply = weixinMsgService.buildWxBarLoginNotice(reportInfo, appId, memberName, wxIdCardBind.getTailNo());
		}
		return weixinOpenService.replyMsg(AppidReplyEnum.ContentType.WORD.getValue() == appidReply.getContentType() ?
					appidReply.getReplyMsg() : appidReply.getMediaId(), weixinOauthInfo, AppidReplyEnum.ContentType.WORD.getValue());
	}

	/**
	 * 读取计费上机是否开启
	 */
	private boolean barLoginIsOpen(String mode) {
		String value;
		if (BarLoginEnum.ModeEnum.NET_BAR.getValue() == Integer.parseInt(mode)) {
			value = RedisContext.getResourceCache().getResourceValue(CacheKeyConstant.ConfigResourcesConstants.BAR_LOGIN_CONFIG,
					CacheKeyConstant.ConfigResourcesConstants.BAR_LOGIN_IS_OPEN);
		} else {
			value = RedisContext.getResourceCache().getResourceValue(CacheKeyConstant.ConfigResourcesConstants.HOTEL_LOGIN_CONFIG,
					CacheKeyConstant.ConfigResourcesConstants.HOTEL_LOGIN_IS_OPEN);
		}
		return StringUtil.isNotBlank(value) && value.equalsIgnoreCase("y");
	}

	/**
	 * 通过地址编码取对应的appid配置
	 *   先查配置表，未查询到则取默认的
	 * @param areaId 地址编码 应为6位的数字
	 */
	public AreaAppid getByAreaId(Integer areaId, Integer mode) {
		AreaAppid temp = new AreaAppid();
		temp.setAreaId(areaId);
		temp.setMode(mode);
		AreaAppid areaAppid = areaAppidDao.findByAreaIdAndMode(temp);
		if (areaAppid == null) {
			String appId;
			String tips;
			if (Objects.equals(BarLoginEnum.ModeEnum.NET_BAR.getValue(), mode)) {
				appId = RedisContext.getResourceCache().getResourceValue(CacheKeyConstant.ConfigResourcesConstants.BAR_LOGIN_CONFIG,
								CacheKeyConstant.ConfigResourcesConstants.BAR_LOGIN_DEFAULT_APPID);
				tips = RedisContext.getResourceCache().getResourceValue(CacheKeyConstant.ConfigResourcesConstants.BAR_LOGIN_CONFIG,
						CacheKeyConstant.ConfigResourcesConstants.BAR_LOGIN_DEFAULT_TIPS);
			} else if (Objects.equals(BarLoginEnum.ModeEnum.E_SPORT_HOTEL.getValue(), mode)) {
				appId = RedisContext.getResourceCache().getResourceValue(CacheKeyConstant.ConfigResourcesConstants.HOTEL_LOGIN_CONFIG,
								CacheKeyConstant.ConfigResourcesConstants.HOTEL_LOGIN_DEFAULT_APPID);
				tips = RedisContext.getResourceCache().getResourceValue(CacheKeyConstant.ConfigResourcesConstants.HOTEL_LOGIN_CONFIG,
						CacheKeyConstant.ConfigResourcesConstants.HOTEL_LOGIN_DEFAULT_TIPS);
			} else {
				throw new WeixinOauthException("mode类型不支持");
			}
			areaAppid = new AreaAppid();
			areaAppid.setAppid(appId);
			areaAppid.setTips(tips);
		}
		return areaAppid;
	}

	/**
	 * 读取计费上机二维码生效时间，默认12个小时
	 */
	private int getServerExpireSeconds(Integer mode) {
		String value ;
		if (Objects.equals(BarLoginEnum.ModeEnum.NET_BAR.getValue(), mode)) {
			value = RedisContext.getResourceCache().getResourceValue(CacheKeyConstant.ConfigResourcesConstants.BAR_LOGIN_CONFIG,
					CacheKeyConstant.ConfigResourcesConstants.BAR_LOGIN_SERVER_EXPIRE_SECONDS);
		} else {
			value = RedisContext.getResourceCache().getResourceValue(CacheKeyConstant.ConfigResourcesConstants.HOTEL_LOGIN_CONFIG,
					CacheKeyConstant.ConfigResourcesConstants.HOTEL_LOGIN_SERVER_EXPIRE_SECONDS);
		}
		if (StringUtil.isNotBlank(value)) {
			return Integer.parseInt(value);
		}
		return 12 * 60 * 60;
	}

	/**
	 * 读取报警手机号
	 */
	private int getCacheSeconds() {
		String value = RedisContext.getResourceCache().getResourceValue(CacheKeyConstant.ConfigResourcesConstants.BAR_LOGIN_CONFIG,
				CacheKeyConstant.ConfigResourcesConstants.WEIXIN_DAILY_LIMIT_ALARM_CACHE_SECONDS);
		if (StringUtil.isNotBlank(value)) {
			return Integer.parseInt(value);
		}
		return 5 * 60;
	}

	protected void responseWrite(String msg) {
		try {
			getResponse().getWriter().write(msg);
		} catch (Exception e) {
			LOG.error("数据输出异常", e);
		}
	}

	@Override
	public String getInterfaceId() {
		return UserOutsiteConstant.WXGZH_INTERFACE_ID;
	}

	@Override
	protected String getOutOauthLogName() {
		return "微信公众号";
	}

	public InputStream getStream() {
		return stream;
	}

	public void setStream(InputStream stream) {
		this.stream = stream;
	}

	public String getPreAuthUrl() {
		return preAuthUrl;
	}

	public void setPreAuthUrl(String preAuthUrl) {
		this.preAuthUrl = preAuthUrl;
	}

	public String getAuth_code() {
		return auth_code;
	}

	public void setAuth_code(String auth_code) {
		this.auth_code = auth_code;
	}

    public Integer getAuthType() {
        return authType;
    }

    public void setAuthType(Integer authType) {
        this.authType = authType;
    }

	public NetBarService getNetBarService() {
		return netBarService;
	}

	public void setNetBarService(NetBarService netBarService) {
		this.netBarService = netBarService;
	}

	public WxIdCardBindDao getWxIdCardBindDao() {
		return wxIdCardBindDao;
	}

	public void setWxIdCardBindDao(WxIdCardBindDao wxIdCardBindDao) {
		this.wxIdCardBindDao = wxIdCardBindDao;
	}

	public AreaAppidDao getAreaAppidDao() {
		return areaAppidDao;
	}

	public void setAreaAppidDao(AreaAppidDao areaAppidDao) {
		this.areaAppidDao = areaAppidDao;
	}

	public AppidReplyDao getAppidReplyDao() {
		return appidReplyDao;
	}

	public void setAppidReplyDao(AppidReplyDao appidReplyDao) {
		this.appidReplyDao = appidReplyDao;
	}

	public WeixinMsgService getWeixinMsgService() {
		return weixinMsgService;
	}

	public void setWeixinMsgService(WeixinMsgService weixinMsgService) {
		this.weixinMsgService = weixinMsgService;
	}
}
