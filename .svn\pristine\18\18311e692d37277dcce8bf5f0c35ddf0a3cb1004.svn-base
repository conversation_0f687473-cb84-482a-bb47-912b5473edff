/*$(function () {*/
//    var uploadObj;
//    $('.js-file').on('change', function (e) {
//      var $this = $(this);
//      uploadObj = uploadObj || new uploadImg(function (data) {
//        if (data.error) {
//        	showTips(data.error);
//        	return;
//        } else {
//          // 成功
//          $.ajax({
//        	  url: $CONFIG.appServer + '/front/swpaysdk/uploadHeadImg.htm',
//        	  type: 'post',
//        	  dataType: 'json',
//        	  processData: false,
//        	  contentType: false,
//        	  data: data.formData,
//        	  success: function(json) {
//        	  },
//        	  error: function() {
//        		  showTips(json.error);
//              	  return;
//        	  }
//          });
//          $('#j-avatar').attr('src', data.dataURL);
//        }
//      }).handleInputChange(e);
//      // 上传完成后清空input
//      $('.js-file').val('');
//    })
    
/*    init();
});

	var $tips = $(".tips-error");

	function showTips(msg) {
		$tips.find("p").html(msg) ;
		$tips.show() ;
	}

	function init(){
		var msg =  $('.tips-error p').text();
		if(msg) {
			$('.tips-error').show();
		}

		if($tips.find("p").html()=="") {
			$tips.hide();
		}
	}*/
