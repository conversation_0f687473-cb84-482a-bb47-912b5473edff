package com.shunwang.basepassport.manager.service.risk;

import com.shunwang.basepassport.config.dao.ConfigInterfaceDao;
import com.shunwang.basepassport.config.pojo.ConfigInterface;
import com.shunwang.basepassport.manager.bean.RiskParam;
import com.shunwang.basepassport.manager.request.risk.RiskRequest;
import com.shunwang.basepassport.manager.response.risk.RiskResponse;
import com.shunwang.util.net.HttpClientUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Constructor;
import java.util.Map;

public class RiskServiceClient {
    private static final Logger LOGGER = LoggerFactory.getLogger(RiskServiceClient.class);

    private static ConfigInterfaceDao configInterfaceDao;

    public void setConfigInterfaceDao(ConfigInterfaceDao configInterfaceDao) {
        RiskServiceClient.configInterfaceDao = configInterfaceDao;
    }

    public static RiskResponse execute(RiskRequest request) {
        try {
            ConfigInterface setting = configInterfaceDao.findByInterfaceKey(request.getInterfaceKey());
            request.doInterfaceSetting(setting);
            String url = request.buildUrl();
            Map<String, String> requestParams = request.buildParams();
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("----------------------------------------");
                LOGGER.debug("url:{}", url);
                LOGGER.debug("method:{}", request.getHttpMethod());
                LOGGER.debug("body:{}", request.getRiskParam().toString());
                LOGGER.debug("----------------------------------------");
            }

            String response  = HttpClientUtils.doPost(url, requestParams);

            Class<RiskResponse> responseClass = request.getResponseClass();
            Constructor constructor = responseClass.getConstructor();
            RiskResponse resp = (RiskResponse) constructor.newInstance();
            resp.setJson(response);
            return resp.parse();
        } catch (Exception e) {
            LOGGER.error("请求风控平台异常[{}]", e.getMessage());
        }
        //异常是返回null,不影响主流程
        return null;
    }

    public static void main(String[] args) {
        RiskRequest request = new RiskRequest();
        RiskParam param = new RiskParam();
        param.setImei("");
        param.setIp("");
        param.setMac("");
        param.setUserName("safiad123");
        request.setRiskParam(param);

        RiskResponse response = RiskServiceClient.execute(request);

        if (response.isSuccess()) {
            System.out.println("success");
        }
    }

}
