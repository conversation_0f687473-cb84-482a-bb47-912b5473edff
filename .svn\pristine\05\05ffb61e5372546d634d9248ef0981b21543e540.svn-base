package com.shunwang.baseStone.sso.simpleImport;

import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.shunwang.util.date.DateUtil;
import com.shunwang.util.encrypt.Md5Encrypt;
import org.junit.Test;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.util.Random;

/**
 * User:pf.ma
 * Date:2017/04/05
 * Time:16:24
 */
public class sicentTest {

	@Test
	public void Test(){
		String respond = "";
		HttpURLConnection conn = null;


		/**
		 * userId	String	 	是	身份证号码
		 siteId	String	 	是	站点id
		 timestamp	String	 	是	当前时间,精确到秒，格式如：

		 sign	String	 	是	MD5签名串(见接口参数签名方式)
		 clientIp
		 String
		 否
		 用户IP
		 */
		try {
            String timestamp = DateUtil.getCurrentDateStamp() ;
			String userId = "sj_" + timestamp + new Random().nextInt(1000000);
			String siteId = "Passport" ;

			String md5Key = "123456" ;
			String clientIp = "***********" ;

			String plainText = userId+"|"+siteId+"|"+timestamp+"|"+md5Key ;
			String sign = Md5Encrypt.encrypt(URLEncoder.encode(plainText,"utf-8").toUpperCase()).toUpperCase() ;
			URL url = new URL("http://sso.kedou.com/sicentBind.do");
			conn = (HttpURLConnection) url.openConnection();
			conn.setDoOutput(true);
			conn.setUseCaches(false);
			conn.setRequestMethod("POST");
			conn.setRequestProperty("Accept", "application/xml");
			conn.getOutputStream().write(("userId="+userId+"&siteId="+siteId
					+ "&timestamp=" + timestamp+"&clientIp="+clientIp
					+"&sign="+sign).getBytes("utf-8"));
			conn.connect();

			BufferedReader reader = new BufferedReader(new InputStreamReader(conn.getInputStream(), "utf-8"));
			String lines = "";

			while ((lines = reader.readLine()) != null) {
				respond += lines;
			}

			reader.close();
			conn.disconnect();
		} catch (Exception e) {
			//e.printStackTrace();
			respond = e.getMessage();
		} finally {
			if (conn != null) {
				conn.disconnect();
			}
			conn = null;
		}

		// 导入后登录
		System.out.println(respond);
        JsonObject map = JsonParser.parseString(respond).getAsJsonObject();
        try {
            String time = DateUtil.getCurrentDateStamp() ;
            String ticketId = map.get("ticketId").getAsString();
            String token = map.get("tockenId").getAsString();

            String siteId = "identity" ;
            String md5Key = "123456" ;

            String plainText = siteId+"|"+time+"|"+md5Key ;
            String sign = Md5Encrypt.encrypt(URLEncoder.encode(plainText,"utf-8").toUpperCase()).toUpperCase() ;
            URL url = new URL("http://sso.kedou.com/ticketCheck.do");
            conn = (HttpURLConnection) url.openConnection();
            conn.setDoOutput(true);
            conn.setUseCaches(false);
            conn.setRequestMethod("POST");
            conn.setRequestProperty("Accept", "application/xml");
            conn.getOutputStream().write(("ticketId="+ticketId+"&siteId="+siteId
                    + "&time=" + time+"&token="+token
                    +"&sign="+sign).getBytes("utf-8"));
            conn.connect();

            BufferedReader reader = new BufferedReader(new InputStreamReader(conn.getInputStream(), "utf-8"));
            String lines = "";
            respond = "";
            while ((lines = reader.readLine()) != null) {
                respond += lines;
            }

            reader.close();
            conn.disconnect();
        } catch (Exception e) {
            //e.printStackTrace();
            respond = e.getMessage();
        } finally {
            if (conn != null) {
                conn.disconnect();
            }
            conn = null;
        }

        System.out.println(respond);
	}
}
