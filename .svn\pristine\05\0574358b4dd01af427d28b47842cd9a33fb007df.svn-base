package com.shunwang.basepassport.manager.request.report;

import com.shunwang.basepassport.config.pojo.ConfigInterface;
import com.shunwang.basepassport.manager.HttpMethod;
import com.shunwang.basepassport.manager.InterfaceConstant;
import com.shunwang.basepassport.manager.request.BaseRequest;
import com.shunwang.basepassport.manager.response.report.ReportResponse;
import com.shunwang.util.date.DateUtil;
import com.shunwang.util.encrypt.Md5Encrypt;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.StringJoiner;

/**
 * 大数据统一采集服务接口
 */
public class ReportRequest extends BaseRequest<ReportResponse> {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReportRequest.class);


    private String projectId;
    private String businessId;
    private String secret;

    /**
     * 包含参数
     * clientIP	客户机的外网IP
     * serverTime	上报时间
     * guid	开机唯一标识（无法得到则空字符串）
     * wwType	网维类型（无法得到则为空字符串）
     * wwAccount	网维账号 网维ID（无法得到则为空字符串）
     * billingType	计费类型（无法得到则为空字符串）
     * billlingAccount	计费账号 计费ID（无法得到则为空字符串）
     * mac	客户机的mac地址（无法得到则为空字符串）
     * pageURL	当前页面URL
     */
    private Map<String, String> param;

    private String clientIP;
    private String serverTime;
    private String guid;
    private String wwType;
    private String wwAccount;
    private String billingType;
    private String billingAccount;
    private String mac;
    private String pageURL;
    private String siteId;
    private String interfaceType;
    private String memberId;

    @Override
    public Map<String, String> getHeaders() {
        try {
            String signSource = secret + projectId + "|" + businessId + "|" + getBody() + secret;
            String sign = Md5Encrypt.encrypt(signSource, StandardCharsets.UTF_8.name()).toUpperCase();

            addHeader("sign", sign);
            addHeader("projectId", projectId);
            addHeader("businessId", businessId);

            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("签名源串:" + signSource);
                LOGGER.debug("签名:" + sign);
            }
        } catch (Exception e) {
            LOGGER.error("设置header错误", e);
        }

        return super.getHeaders();
    }

    @Override
    public Map<String, String> buildParams() {
        Map<String, String> request = new HashMap<>();
        setServerTime(DateUtil.getCurrentDateStr());
        setGuid(param.get("guid"));
        setWwType(param.get("wwType"));
        setWwAccount(param.get("wwAccount"));
        setBillingType(param.get("billingType"));
        setBillingAccount(param.get("billlingAccount") == null ?
                param.get("billingAccount") : param.get("billlingAccount"));
        setMac(param.get("mac"));
        setPageURL(param.get("pageURL"));
        setSiteId(param.get("siteId"));
        setInterfaceType(param.get("interfaceType"));
        setMemberId(param.get("memberId"));
        return request;
    }

    public String getBody() {
        StringJoiner joiner = new StringJoiner("\t");

        joiner.add(getClientIP()).add(getServerTime()).add(getGuid())
                .add(getWwType()).add(getWwAccount()).add(getBillingType())
                .add(getBillingAccount()).add(getMac()).add(getPageURL())
                .add(getSiteId()).add(getInterfaceType()).add(getMemberId());

        return joiner.toString();
    }

    @Override
    public Class<ReportResponse> getResponseClass() {
        return ReportResponse.class;
    }

    @Override
    public Integer getInterfaceKey() {
        return InterfaceConstant.INTERFACE_KEY_DC_REPORT;
    }

    @Override
    public void doInterfaceSetting(ConfigInterface setting) {
        setUrl(setting.getInterfaceUrl1());
        setProjectId(setting.getInterfacePartnerId());
        setBusinessId(setting.getInterfaceEmail());
        setSecret(setting.getInterfaceMd5Key());
    }

    @Override
    public HttpMethod getHttpMethod() {
        return HttpMethod.POST;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getBusinessId() {
        return businessId;
    }

    public void setBusinessId(String businessId) {
        this.businessId = businessId;
    }

    public String getSecret() {
        return secret;
    }

    public void setSecret(String secret) {
        this.secret = secret;
    }

    public Map<String, String> getParam() {
        return param;
    }

    public void setParam(Map<String, String> param) {
        this.param = param;
    }

    public String getClientIP() {
        return clientIP;
    }

    public void setClientIP(String clientIP) {
        this.clientIP = clientIP;
    }

    public String getServerTime() {
        return serverTime;
    }

    public void setServerTime(String serverTime) {
        this.serverTime = serverTime;
    }

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getWwType() {
        return wwType;
    }

    public void setWwType(String wwType) {
        this.wwType = wwType;
    }

    public String getWwAccount() {
        return wwAccount;
    }

    public void setWwAccount(String wwAccount) {
        this.wwAccount = wwAccount;
    }

    public String getBillingType() {
        return billingType;
    }

    public void setBillingType(String billingType) {
        this.billingType = billingType;
    }

    public String getBillingAccount() {
        return billingAccount;
    }

    public void setBillingAccount(String billingAccount) {
        this.billingAccount = billingAccount;
    }

    public String getMac() {
        return mac;
    }

    public void setMac(String mac) {
        this.mac = mac;
    }

    public String getPageURL() {
        return pageURL;
    }

    public void setPageURL(String pageURL) {
        this.pageURL = pageURL;
    }

    public String getSiteId() {
        return siteId;
    }

    public void setSiteId(String siteId) {
        this.siteId = siteId;
    }

    public String getInterfaceType() {
        return interfaceType;
    }

    public void setInterfaceType(String interfaceType) {
        this.interfaceType = interfaceType;
    }

    public String getMemberId() {
        return memberId;
    }

    public void setMemberId(String memberId) {
        this.memberId = memberId;
    }
}
