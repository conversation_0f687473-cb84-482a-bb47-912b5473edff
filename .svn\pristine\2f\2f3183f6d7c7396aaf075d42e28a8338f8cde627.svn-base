package com.shunwang.basepassport.user.web;

import com.shunwang.baseStone.core.action.BaseStoneAction;
import com.shunwang.baseStone.core.exception.BaseStoneException;
import com.shunwang.baseStone.core.response.BaseStoneResponse;
import com.shunwang.baseStone.encrypt.Encrypt;
import com.shunwang.baseStone.encrypt.pojo.EncryptItem;
import com.shunwang.basepassport.binder.common.ErrorCode;
import com.shunwang.basepassport.user.common.MemberConstants;
import com.shunwang.basepassport.user.dao.IdcardBindDao;
import com.shunwang.basepassport.user.dao.MemberDao;
import com.shunwang.basepassport.user.pojo.IdcardBind;
import com.shunwang.basepassport.user.pojo.Member;
import com.shunwang.util.lang.StringUtil;

import java.util.Date;

/**
 * Created by bao.jin on 2019/6/24.
 */
public class IdcardBindAction extends BaseStoneAction {

    private Integer memberId;
    private String idcard;
    private IdcardBindDao idcardBindDao;
    private MemberDao memberDao;

    @Override
    public void process() throws Exception {

        IdcardBind param = new IdcardBind();
        param.setIdcard(idcard);
        param.setState(MemberConstants.IdcardBind.IdcardBindState.BINDED);

        IdcardBind cardBind = idcardBindDao.getByIdcard(param);
        if(null != cardBind) {
            throw new BaseStoneException("4001", "身份证已被绑定");
        }

        param.setMemberId(memberId);
        param.setIdcard(null);
        param.setState(MemberConstants.IdcardBind.IdcardBindState.BINDED);
        cardBind = idcardBindDao.getByIdcard(param);

        if(null != cardBind) {
            throw new BaseStoneException("4002", "memberId已被绑定");
        }

        param.setMemberId(memberId);
        param.setIdcard(idcard);
        param.setState(MemberConstants.IdcardBind.IdcardBindState.UNBINDED);
        cardBind = idcardBindDao.getByIdcard(param);

        if(null != cardBind) {
            param.setMemberId(cardBind.getMemberId());
            param.setState(MemberConstants.IdcardBind.IdcardBindState.BINDED);
            param.setTimeEdit(new Date());
            idcardBindDao.update(param);
        } else {

            Member member = memberDao.getByMemberId(memberId);
            if (null == member) {
                throw new BaseStoneException("4003", "memberId不存在");
            }

            IdcardBind idcardBind = new IdcardBind();
            idcardBind.setIdcard(idcard);
            idcardBind.setMemberId(memberId);
            idcardBind.setState(MemberConstants.IdcardBind.IdcardBindState.BINDED);
            idcardBind.setTimeAdd(new Date());

            idcardBindDao.save(idcardBind);
        }

        BaseStoneResponse response = new BaseStoneResponse();
        setBaseResponse(response);
    }

    @Override
    public String buildSignString() {
        Encrypt encrypt = new Encrypt();
        encrypt.addItem(new EncryptItem(super.getSiteId()));
        encrypt.addItem(new EncryptItem(String.valueOf(memberId)));
        encrypt.addItem(new EncryptItem(String.valueOf(idcard)));
        encrypt.addItem(new EncryptItem(super.getTime()));
        return encrypt.buildSign();
    }

    @Override
    public void checkParam() {
        if (null == memberId) {
            throw new BaseStoneException(ErrorCode.C_1001.getCode(), "memberId不能为空");
        }
        if (StringUtil.isBlank(idcard)) {
            throw new BaseStoneException(ErrorCode.C_1001.getCode(), "idcard不能为空");
        }
    }

    @Override
    public String getSiteName() {
        return MemberConstants.IDCARD_BIND;
    }

    public Integer getMemberId() {
        return memberId;
    }

    public void setMemberId(Integer memberId) {
        this.memberId = memberId;
    }

    public String getIdcard() {
        return idcard;
    }

    public void setIdcard(String idcard) {
        this.idcard = idcard;
    }

    public IdcardBindDao getIdcardBindDao() {
        return idcardBindDao;
    }

    public void setIdcardBindDao(IdcardBindDao idcardBindDao) {
        this.idcardBindDao = idcardBindDao;
    }

    public MemberDao getMemberDao() {
        return memberDao;
    }

    public void setMemberDao(MemberDao memberDao) {
        this.memberDao = memberDao;
    }
}
