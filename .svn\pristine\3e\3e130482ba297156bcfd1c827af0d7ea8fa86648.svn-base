$import('SFInput');
$import('AjaxUtil');
$import('SFFormView');
$import('SFTextArea');
$import('SFRadioGroup');
var SysconfigView = $createClass('SysconfigView',function(){
	this.title="系统配置请不要随便乱改";
	this.controls = [
		new SFInput({
			name:'图片url',
			field:'imgUrl',
			width:'400px',
			msg:'访问上传图片的路径'
		}),
		new SFInput({
			name:'图片路径',
			field:'imgDir',
			width:'400px',
			msg:'保存上传图片的目录'
		}),
		new SFInput({
			name:'加密图片路径',
			field:'encryptImgDir',
			width:'400px',
			msg:'保存上传加密图片的目录'
		}),
		new SFTextArea({
			name:'实名认证审核通过短信内容',
			field:'actuCheckPassMobileMsg',
			width:'400px'
		}),
		new SFTextArea({
			name:'实名认证审核拒绝短信内容',
			field:'actuCheckRefuseMobileMsg',
			width:'400px'
		}),
		new SFTextArea({
			name:'默认短信内容',
			field:'mobileDefualtMsg',
			width:'400px',
			msg:'默认的短信内容'
		}),
		new SFInput({
			name:'手机号码段',
			field:'validPhoneNum',
			width:'400px',
			msg:'有效手机号码段'
		}),
		new SFRadioGroup({
			field:'gtSwitch',
			name:'极验验证码开关',
			items:[
				{text:"开",value:"1"},
				{text:"关",value:"0"}]
		}),
		new SFInput({
			name:'极验异常自动关闭阀值',
			field:'gtThreshold',
			width:'400px',
			msg:'1分钟内异常值'
		}),
		new SFInput({
			name:'邮件联系人',
			field:'gtEmails',
			width:'400px',
			msg:'多个收件人竖线分隔'
		}),
		new SFRadioGroup({
			field:'bankReservePhoneSwitch',
			name:'银行预留手机号开关',
			items:[
				{text:"开",value:"y"},
				{text:"关",value:"n"}]
		}),
	];
	var self = this;
	this.btns = [{
		text:'保存',
		onclick:function(){
			self.trigger("execute",self.panel.getValue());
		}}];
	this.SFFormView();
	var data = AjaxUtil.getObjByUrl("listSysconfig.do");
	this.panel.setValue(data.map);
	
},'SFFormView');

SysconfigView.prototype.set = function(data){
	alert("设置成功");
}

