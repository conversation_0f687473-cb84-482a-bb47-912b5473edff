<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE struts PUBLIC "-//Apache Software Foundation//DTD Struts Configuration 2.0//EN" "http://struts.apache.org/dtds/struts-2.0.dtd">

<struts>

	
	<package name="findActuPwd" namespace="/front/noLogin" extends="nologinpassport">
	
			
		<!-- 密码找回  -->
	   <action name="findPwd_byActu" method="goActuFind" class="appealActuPwdAction">
	    <result>/front/find/actu_front.jsp</result>
	   </action>
	   
	   
       <action name="pwdFind_actu_front" method="goNextStep" class="appealActuPwdAction">
           <result name="success">/front/find/actu_front_reset.jsp</result>	  
       </action>

       <action name="saveUserInfo" method="commitAppealMsg" class="appealActuPwdAction">
           <result name="input">/front/find/actu_front_reset.jsp</result>
		   <result name="appealResult" >/front/find/appeal_progress_query_result.jsp</result>
		   <result name="success" type="redirect">/front/noLogin/appeal_query_progress.htm?queryType=password&amp;memberName=${memberName}</result>
	   </action>

		<action name="uploadImage" method="uploadImage" class="appealActuPwdAction">
	   </action>
		<!-- 密码找回  结束-->

		<action name="quesFind_actu_front" method="findQueNext" class="appealActuPwdAction">
			<result name="findQuestion">/front/find/actu_frontQuestion_reset.jsp</result>
			<result name="input">/front/find/actu_frontQuestion.jsp</result>
		</action>

		<action name="commintMsgForQue" method="commintMsgActuFindQue" class="appealActuPwdAction">
			<result name="success" type="redirect">/front/noLogin/appeal_query_progress.htm?queryType=question&amp;memberName=${memberName}</result>
			<result name="input" >/front/find/appeal21_question_front.jsp</result>
		</action>
	</package>
</struts>
