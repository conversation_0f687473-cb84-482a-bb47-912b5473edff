package com.shunwang.baseStone.core.response;

import junit.framework.TestCase;

import java.util.*;

public class BaseStoneResponseTest extends TestCase {
	public void testToxml(){
		BaseStoneResponse response = new BaseStoneResponse();
		response.setMsg("设计错误");
		response.setMsgId("1000");
		Map<String, Object> item = new HashMap<String, Object>();
		item.put("fisrt name", "seven");
		item.put("last name", "david");
		List<Map<String, Object>> items = new ArrayList<Map<String, Object>>();
		items.add(item);
		response.setItems(items);
		System.out.println(response.toJson());
	}
}
