package com.shunwang.baseStone.backuser.bo;


import com.shunwang.baseStone.backuser.pojo.Backuser;
import com.shunwang.baseStone.backuser.pojo.OAuth2AccessTokenResp;
import com.shunwang.baseStone.backuser.pojo.OAuth2AuthorizeResp;
import com.shunwang.baseStone.backuser.pojo.OAuth2UserInfoResp;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @since 2015-10-10
 */
public interface IOAuth2Service {

    String buildAuthUrl(HttpServletRequest request);

    Backuser callback(HttpServletRequest request);

    OAuth2AuthorizeResp validAuthorizeCallback(HttpServletRequest request);

    OAuth2AccessTokenResp getAccessToken(String code);

    OAuth2UserInfoResp getUserInfo(String token);

    void handlerAccount(String username, String password);
}
