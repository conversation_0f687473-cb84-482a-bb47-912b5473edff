package com.shunwang.basepassport.actu.dao;

import java.util.Date;

import com.shunwang.basepassport.actu.constant.ActuConstant;
import com.shunwang.basepassport.actu.pojo.ActuInfo;
import com.shunwang.basepassport.actu.pojo.PersonalActuInfo;
import com.shunwang.basepassport.user.pojo.Member;
/**
 * @Description:个人实名认证信息
 * <AUTHOR>  create at 2011-7-25 下午04:17:44
 * @FileName com.shunwang.basepassport.actu.dao.PersonalActuDao.java
 */
public class PersonalActuDao extends ActuDao<PersonalActuInfo> {
	/**
	 * @Description:根据用户编号查询实名认证信息
	 * @param name
	 * @return
	 * <AUTHOR> create at 2011-7-25 下午04:18:58
	 */
	public PersonalActuInfo getByMemberId(Integer memberId){
		return (PersonalActuInfo)getSqlMapClientTemplate().queryForObject(getStatementNameWrap("getByMemberId"), memberId);
	}

	@Override
	public Integer findLatestedUnCheckCntByMemberId(Integer memberId) {
		return (Integer)getSqlMapClientTemplate().queryForObject(getStatementNameWrap("findLatestedUnCheckCntByMemberId"), memberId);
	}

	@Override
	public void repeal(Member member) {
		member.setTimeEdit(new Date());
		member.setPersonCertState(Integer.parseInt(ActuConstant.INFO_STATE_UN_APPLY));
		getSqlMapClientTemplate().update(getStatementNameWrap("repeal"), member.getMemberId());
		member.update();
	}

	@Override
	public ActuInfo loadActuInprocess(Integer memberId) {
		return (PersonalActuInfo)getSqlMapClientTemplate().queryForObject(getStatementNameWrap("getByMemberId"), memberId);
	}

    public void updateLinkPhone(Integer memberId, String phoneNumber) {
        PersonalActuInfo info = new PersonalActuInfo();
        info.setMemberId(memberId);
        info.setLinkPhone(phoneNumber);
		getSqlMapClientTemplate().update(getStatementNameWrap("updateLinkPhone"), info);
    }

	@Override
	public Integer updateState(PersonalActuInfo personalActuInfo) {
		//暂时网吧实名在使用，如需使用请扩展
		return null;
	}

}
