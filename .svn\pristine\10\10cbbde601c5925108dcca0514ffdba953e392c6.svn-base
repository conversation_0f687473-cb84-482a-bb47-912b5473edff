<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ include file="/common/taglibs.jsp"%>
<%@ page import="com.shunwang.passport.common.context.DomainContext"%>
<%@ page import="java.net.URLEncoder"%>
<%@ page import="com.shunwang.util.lang.StringUtil"%>
<%@ page import="com.shunwang.basepassport.context.UserContext"%>
<!DOCTYPE HTML>
<html>
<head>
    <title>登录 - 顺网通行证</title>
</head>
<body class="login_page">
<jsp:include page="/common/banner_js.jsp"></jsp:include>
<!-- 公告 start -->
<jsp:include page="/common/notice.jsp"></jsp:include>
<!-- 公告 end -->
<%
    StringBuilder iframeUrl=new StringBuilder();
    iframeUrl.append("?callbackUrl=");
    String returnUrl=StringUtil.isBlank(request.getParameter("reffer"))?DomainContext.getAppServer()+"/front/member/securityCenter_index.htm"+"?returnUrl="+StringUtil.trimNull(request.getParameter("return_url")):request.getParameter("reffer");
    if(null!=UserContext.getUser()) {
        //在已经登录情况下, 需要去除ticketId参数, 防止interceptor重复检查ticketId时出错,导致页面循环重定向
        if (returnUrl.indexOf("ticketId") > -1) {
            int ticketIdStart = returnUrl.indexOf("ticketId");
            int ticketIdEnd= 0;
            if (ticketIdStart > -1 ) {
                ticketIdEnd=returnUrl.indexOf("&",ticketIdStart);
                if (ticketIdEnd == -1 ) {
                    returnUrl = returnUrl.substring(0, ticketIdStart-1);
                } else {
                    returnUrl = returnUrl.substring(0, ticketIdStart-1) + returnUrl.substring(ticketIdEnd);
                }
            }
        }
        response.sendRedirect(returnUrl);
    }

    String errorMsg=StringUtil.isNotBlank(request.getParameter("msg"))?request.getParameter("msg"):StringUtil.EMPTY_STRING;
    String siteId =  StringUtil.isBlank(request.getParameter("site_id"))?"Passport":request.getParameter("site_id");
    iframeUrl.append(URLEncoder.encode(returnUrl,"UTF-8")).append("&errorMsg=")
            .append(URLEncoder.encode("loginFirst".equals(errorMsg)?"请先登录，继续您刚才的操作！":errorMsg,"UTF-8"))
            .append("&site_id=").append(siteId).append("&loginType=quickLogin");

%>
<div class="login_wrap" id="wrap">
    <div class="login_bg" style="background-image:url(${staticServer}/images/front/login_bg.jpg)"></div>
    <div class="login_box">
        <div class="login_con">
            <div class="login_fn">还没有账号？<a href="${appServer }/front/noLogin/goRegist_front.htm">立即免费注册</a></div>
            <div class="login_sso">
                <iframe frameborder="0" allowtransparency="true" scrolling="no" src="<%=DomainContext.getSsoLoginURL()+iframeUrl %>"></iframe>
            </div>
        </div>
    </div>
</div>
<script src="${staticServer}/scripts/front/notice.js"></script>
</body>
</html>
