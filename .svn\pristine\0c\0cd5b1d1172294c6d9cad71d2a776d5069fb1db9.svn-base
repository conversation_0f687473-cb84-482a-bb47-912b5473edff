package com.shunwang.baseStone.sso.util;

import com.shunwang.baseStone.sso.constant.HttpClientConstant;
import com.shunwang.util.StringUtil;
import org.apache.http.*;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

/**
 * @Title: HttpClientUtil.java
 * @Description: http请求工具类
 */
public class HttpClientUtil {
	private static final Logger LOGGER = LoggerFactory.getLogger( HttpClientUtil.class );

	private static final int CONNECT_TIMEOUT = 5 * 1000; // 连接超时时间
	private static final int SOCKET_TIMEOUT  = 30 * 1000; // 数据传输超时

	/**
	 * 执行Post请求
	 *
	 * @param url
	 * @param stringParam
	 * @return
	 */
	public static String doPost(String url, String stringParam) {
		StringEntity stringEntity = new StringEntity( stringParam, Consts.UTF_8 );
		return doPost( url, stringEntity, null, null, null, null );
	}

	/**
	 * 执行Post请求
	 *
	 * @param url
	 * @param stringParam
	 * @return
	 */
	public static String doPostJson(String url, String stringParam) {
		StringEntity stringEntity = new StringEntity( stringParam, Consts.UTF_8 );
		return doPost( url, stringEntity, HttpClientConstant.Accept.Json, HttpClientConstant.ContentType.APPLICATION_JSON, null, null );
	}

	/**
	 * 执行Post请求
	 *
	 * @param url
	 * @param httpPostParams
	 * @return
	 */
	public static String doPost(String url, Map<String, Object> httpPostParams) {
		StringEntity stringEntity = paramsToStringEntity( httpPostParams, Consts.UTF_8 );
		return doPost( url, stringEntity, null, null, null, null );
	}

	/**
	 * 执行Post请求
	 *
	 * @param url
	 * @param httpPostParams
	 * @return
	 */
	public static String doPostJson(String url, Map<String, Object> httpPostParams) {
		StringEntity stringEntity = paramsToStringEntity( httpPostParams, Consts.UTF_8 );
		return doPost( url, stringEntity, HttpClientConstant.Accept.Json, HttpClientConstant.ContentType.APPLICATION_JSON, null, null );
	}

	/**
	 * 执行Post请求
	 *
	 * @param url
	 * @param httpPostParams
	 * @param ip
	 * @return
	 */
	public static String doPost(String url, Map<String, Object> httpPostParams, String ip) {
		StringEntity stringEntity = paramsToStringEntity( httpPostParams, Consts.UTF_8 );
		return doPost( url, stringEntity, null, null, null, ip );
	}

	/**
	 * 执行Post请求
	 *
	 * @param url
	 * @param httpPostParams
	 * @param ip
	 * @return
	 */
	public static String doPostJson(String url, Map<String, Object> httpPostParams, String ip) {
		StringEntity stringEntity = paramsToStringEntity( httpPostParams, Consts.UTF_8 );
		return doPost( url, stringEntity, HttpClientConstant.Accept.Json, HttpClientConstant.ContentType.APPLICATION_JSON, null, ip );
	}

	/**
	 * 执行Post请求
	 *
	 * @param url
	 * @param stringEntity
	 * @param accept
	 * @param contentType
	 * @param userAgent
	 * @param ip
	 * @return
	 */
	public static String doPost(String url, StringEntity stringEntity, HttpClientConstant.Accept accept, HttpClientConstant.ContentType contentType,
			String userAgent, String ip) {
		CloseableHttpClient httpClient = null;
		CloseableHttpResponse response = null;
		try {
			RequestConfig.Builder builder = RequestConfig.custom();
			builder.setConnectTimeout( CONNECT_TIMEOUT );
			builder.setSocketTimeout( SOCKET_TIMEOUT );
			RequestConfig requestConfig = builder.build();

			HttpPost httpPost = new HttpPost( url );
			httpPost.setConfig( requestConfig );
			httpPost.setEntity( stringEntity );

			if (accept != null) {
				httpPost.addHeader( HttpClientConstant.Header.ACCEPT, accept.getContent() );
			}
			if (contentType != null) {
				httpPost.addHeader( HttpClientConstant.Header.CONTENT_TYPE, contentType.getContent() );
			}
			if (StringUtil.isNotBlank( userAgent )) {
				httpPost.addHeader( HttpClientConstant.Header.USER_AGENT, userAgent );
			}
			if (StringUtil.isNotBlank( ip )) {
				httpPost.addHeader( HttpClientConstant.Header.X_FORWARDED_FOR_1, ip );
				httpPost.addHeader( HttpClientConstant.Header.X_FORWARDED_FOR_2, ip );
			}
			httpClient = HttpClients.createDefault();
			response = httpClient.execute( httpPost );
			int statusCode = response.getStatusLine().getStatusCode();
			if (HttpStatus.SC_OK == statusCode) {
				return EntityUtils.toString( response.getEntity() );
			} else {
				LOGGER.error( "Http connection fail, response statusCode is " + statusCode );
			}
		} catch (Exception e) {
			LOGGER.error( "HttpClientUtil do post error", e );
		} finally {
			try {
				if (response != null) {
					response.close();
				}
				if (httpClient != null) {
					httpClient.close();
				}
			} catch (Exception e) {
				LOGGER.error( "HttpClientUtil do post close error", e );
			}
		}
		return null;
	}

	/**
	 * 执行get请求
	 *
	 * @param url
	 * @return
	 */
	public static String doGet(String url) {
		return doGet( url, null );
	}

	/**
	 * 执行get请求
	 *
	 * @param url
	 * @param httpGetParams
	 * @return
	 */
	public static String doGet(String url, Map<String, Object> httpGetParams) {
		StringEntity stringEntity = paramsToStringEntity( httpGetParams, Consts.UTF_8 );
		return doGet( url, stringEntity, null, null, null, null );
	}

	/**
	 * 执行get请求
	 *
	 * @param url
	 * @param httpGetParams
	 * @param ip
	 * @return
	 */
	public static String doGet(String url, Map<String, Object> httpGetParams, String ip) {
		StringEntity stringEntity = paramsToStringEntity( httpGetParams, Consts.UTF_8 );
		return doGet( url, stringEntity, null, null, null, ip );
	}

	/**
	 * 执行get请求
	 *
	 * @param url
	 * @param httpGetParams
	 * @return
	 */
	public static String doGet(String url, Map<String, Object> httpGetParams, Charset charset) {
		StringEntity stringEntity = paramsToStringEntity( httpGetParams, charset );
		return doGet( url, stringEntity, null, null, null, null );
	}

	/**
	 * 执行get请求,指定返回结果类型
	 *
	 * @param url
	 * @param stringEntity
	 * @param accept
	 * @param contentType
	 * @param userAgent
	 * @param ip
	 * @return
	 */
	public static String doGet(String url, StringEntity stringEntity, HttpClientConstant.Accept accept, HttpClientConstant.ContentType contentType,
			String userAgent, String ip) {
		CloseableHttpClient httpClient = null;
		CloseableHttpResponse response = null;
		try {
			HttpGet httpGet = null;
			if (stringEntity == null) {
				httpGet = new HttpGet( url );
			} else {
				httpGet = new HttpGet( url + "?" + EntityUtils.toString( stringEntity ) );
			}

			RequestConfig.Builder builder = RequestConfig.custom();
			builder.setConnectTimeout( CONNECT_TIMEOUT );
			builder.setSocketTimeout( SOCKET_TIMEOUT );

			RequestConfig requestConfig = builder.build();
			httpGet.setConfig( requestConfig );

			if (accept != null) {
				httpGet.addHeader( HttpClientConstant.Header.ACCEPT, accept.getContent() );
			}
			if (contentType != null) {
				httpGet.addHeader( HttpClientConstant.Header.CONTENT_TYPE, contentType.getContent() );
			}
			if (StringUtil.isNotBlank( userAgent )) {
				httpGet.addHeader( HttpClientConstant.Header.USER_AGENT, userAgent );
			}
			if (StringUtil.isNotBlank( ip )) {
				httpGet.addHeader( HttpClientConstant.Header.X_FORWARDED_FOR_1, ip );
				httpGet.addHeader( HttpClientConstant.Header.X_FORWARDED_FOR_2, ip );
			}

			httpClient = HttpClients.createDefault();
			response = httpClient.execute( httpGet );

			int statusCode = response.getStatusLine().getStatusCode();
			if (HttpStatus.SC_OK == statusCode) {
				return EntityUtils.toString( response.getEntity() );
			} else {
				LOGGER.error( "Http connection fail, response statusCode is " + statusCode );
			}
		} catch (Exception e) {
			LOGGER.error( "HttpClientUtil do get error", e );
		} finally {
			try {
				if (response != null) {
					response.close();
				}
				if (httpClient != null) {
					httpClient.close();
				}
			} catch (Exception e) {
				LOGGER.error( "HttpClientUtil do get close error", e );
			}
		}
		return null;
	}

	/**
	 * 请求参数转换
	 *
	 * @param httpPostParams
	 * @param charset
	 * @return
	 */
	private static StringEntity paramsToStringEntity(Map<String, Object> httpPostParams, Charset charset) {
		if (httpPostParams == null) {
			return null;
		}
		if (charset == null) {
			charset = Consts.UTF_8;
		}
		List<NameValuePair> listParams = buildNameValuePair( httpPostParams );
		return new UrlEncodedFormEntity( listParams, charset );
	}

	/**
	 * 构建post参数
	 *
	 * @param httpPostParams
	 * @return
	 */
	private static List<NameValuePair> buildNameValuePair(Map<String, Object> httpPostParams) {
		List<NameValuePair> listNameValuePair = new ArrayList<NameValuePair>();
		if (!httpPostParams.isEmpty()) {
			Iterator<Entry<String, Object>> iterator = httpPostParams.entrySet().iterator();
			while (iterator.hasNext()) {
				Entry<String, Object> entry = iterator.next();
				String key = entry.getKey();
				Object value = entry.getValue();
				listNameValuePair.add( new BasicNameValuePair( key, (value == null) ? "" : value.toString() ) );
			}
		}
		return listNameValuePair;
	}

	/**
	 * 以jsonString形式发送HttpPost的Json请求，String形式返回响应结果
	 *
	 * @param url
	 * @param jsonString
	 * @return
	 */
	public static String sendPostJsonStr(String url, String jsonString) {
		if (jsonString == null || jsonString.isEmpty()) {
			return sendPost( url );
		}
		String resp = "";
		StringEntity entityStr = new StringEntity( jsonString, ContentType.create( "text/plain", "UTF-8" ) );
		CloseableHttpClient httpClient = HttpClients.createDefault();
		HttpPost httpPost = new HttpPost( url );
		httpPost.setEntity( entityStr );
		CloseableHttpResponse response = null;
		try {
			response = httpClient.execute( httpPost );
			HttpEntity entity = response.getEntity();
			resp = EntityUtils.toString( entity, "UTF-8" );
			EntityUtils.consume( entity );
		} catch (ClientProtocolException e) {
			System.out.println(e.getMessage());
		} catch (IOException e) {
			System.out.println(e.getMessage());
		} finally {
			if (response != null) {
				try {
					response.close();
				} catch (IOException e) {
					System.out.println(e.getMessage());
				}
			}
		}
		if (resp == null || resp.equals( "" )) {
			return "";
		}
		return resp;
	}

	/**
	 * 发送不带参数的HttpPost请求
	 *
	 * @param url
	 * @return
	 */
	public static String sendPost(String url) {
		// 1.获得一个httpclient对象
		CloseableHttpClient httpclient = HttpClients.createDefault();
		// 2.生成一个post请求
		HttpPost httppost = new HttpPost(url);
		CloseableHttpResponse response = null;
		try {
			// 3.执行get请求并返回结果
			response = httpclient.execute(httppost);
		} catch (IOException e) {
			System.out.println(e.getMessage());
		}
		// 4.处理结果，这里将结果返回为字符串
		HttpEntity entity = response.getEntity();
		String result = null;
		try {
			result = EntityUtils.toString(entity);
		} catch (ParseException | IOException e) {
			System.out.println(e.getMessage());
		}
		return result;
	}

}
