package com.shunwang.basepassport.binder.web.send;

import com.shunwang.baseStone.bussiness.pojo.Bussiness;
import com.shunwang.baseStone.cache.CacheKeyConstant;
import com.shunwang.baseStone.config.constants.ConfigOauthConstant;
import com.shunwang.baseStone.context.RedisContext;
import com.shunwang.baseStone.core.exception.BaseStoneException;
import com.shunwang.basepassport.binder.common.BinderConstants;
import com.shunwang.basepassport.binder.common.ErrorCode;
import com.shunwang.basepassport.binder.pojo.SendBinder;
import com.shunwang.basepassport.commonExp.ParamFormatErrorExp;
import com.shunwang.basepassport.commonExp.ParamNotFoundExp;
import com.shunwang.basepassport.user.pojo.Member;
import com.shunwang.basepassport.user.pojo.SingleAccountToken;
import com.shunwang.util.lang.StringUtil;

/**
 * 17.  单账号发送手机验证码，后续可以用于绑定手机号码
 *
 * <AUTHOR>
 * @date 2018/12/17
 **/
public class SendForSingleAccountBindAction extends MobileSendBaseAction {

    @Override
    public void checkParam() {
        setInterfaceType(BinderConstants.SendConstants.SendInterfaceType.TYPE_MOBILE_SINGLE_ACCOUNT_BIND);
        super.checkParam();
        String realSiteId = StringUtil.isNotBlank(getAccessSiteId()) ? getAccessSiteId() : getSiteId();
        Bussiness bussiness = getBussinessDao().getById(realSiteId);
        if (!bussiness.isSingleAccount()) {
            throw new ParamFormatErrorExp("interfaceType");
        }
        if (StringUtil.isBlank(getSingleAccountToken())) {
            throw new ParamNotFoundExp("singleAccountToken");
        }
        if (StringUtil.isBlank(this.getMobile())) {
            throw new ParamNotFoundExp("mobile");
        }
    }

    @Override
    protected SendBinder buildSendBinder() {
        SendBinder sendBinder;
        String key = CacheKeyConstant.InterfaceToken.SINGLE_ACCOUNT_SIGN + getSingleAccountToken();
        SingleAccountToken signToken = RedisContext.getRedisCache().get(key, SingleAccountToken.class);

        if (signToken == null)
            throw new BaseStoneException(ErrorCode.C_1081.getCode(), ErrorCode.C_1081.getDescription());
        // 如果是普通账号，需要用这个账号来发
        if (signToken.getType().equals(ConfigOauthConstant.TYPE.NORMAL.getInt())) {
            Member normalMember = getDao().getByMemberId(signToken.getUnionId());
            setMember(normalMember);
            if (member.getIsBindMobile() && !member.getMobile().equals(getMobile())) { //绑定手机且不是这个手机
                throw new BaseStoneException(ErrorCode.C_1082.getCode(), ErrorCode.C_1082.getDescription());
            } else if (member.getMobileAsLoginAccount() && !member.getMobile().equals(getMobile())) {
                //该账号已经绑定了其他手机号码作为登录账号，一般逻辑走不到这里
                throw new BaseStoneException(ErrorCode.C_1083.getCode(), ErrorCode.C_1083.getDescription());
            }
            // 这个手机号码能拿到账号
            Member phoneMember = getDao().getByMobile(getNumber());
            if (null != phoneMember && !phoneMember.getMemberId().equals(signToken.getUnionId())) {
                throw new BaseStoneException(ErrorCode.C_1025.getCode(), ErrorCode.C_1025.getDescription());
            }
            // 未绑定手机或手机号码相同
            if (member.getIsBindMobile() || member.getMobileAsLoginAccount()) { //已经绑定且手机号码相同
                sendBinder = (SendBinder) getBinderDao().getById(getMember().getMemberId());
                setInterfaceType(BinderConstants.MOBLIE_LOGIN);
                sendBinder.setMember(getMember());
            } else { //未绑定
                sendBinder = createSendBinder();
                sendBinder.setMember(getMemberInfo());
                sendBinder.setMemberName(getMemberInfo().getMemberName());
                setInterfaceType(BinderConstants.BINDNUMBER);
            }
        } else { //三方账号
            Member phoneMember = getDao().getByMobile(getNumber());
            sendBinder = createSendBinder();
            if (null != phoneMember) {//用这个手机号码来发
                setMember(phoneMember);
                sendBinder.setMember(getMember());
                sendBinder.setMemberName(getMember().getMemberName());
                setInterfaceType(BinderConstants.MOBLIE_LOGIN);
            } else {
                setInterfaceType(BinderConstants.MOBLIE_REGISTER);
            }
        }
        return sendBinder;
    }
}
