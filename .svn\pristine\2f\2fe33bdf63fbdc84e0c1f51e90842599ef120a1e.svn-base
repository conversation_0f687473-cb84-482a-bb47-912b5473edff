<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd" >
<sqlMap namespace="com.shunwang.baseStone.reason.pojo.Reason">
<resultMap class="com.shunwang.baseStone.reason.pojo.Reason" id="BaseResultMap">
	<result property="reasonId" column="reasonId" jdbcType="int"/>
	<result property="siteType" column="siteType" jdbcType="varchar"/>
	<result property="moduleType" column="moduleType" jdbcType="varchar"/>
	<result property="content" column="content" jdbcType="varchar"/>
	<result property="state" column="state" jdbcType="tinyint"/>
	<result property="timeAdd" column="timeAdd" jdbcType="datetime"/>
	<result property="timeEdit" column="timeEdit" jdbcType="datetime"/>
	<result property="userAdd" column="userAdd" jdbcType="varchar"/>
	<result property="userEdit" column="userEdit" jdbcType="varchar"/>
</resultMap>
<select id="find" resultMap="BaseResultMap" parameterClass="com.shunwang.framework.ibatis.query.ConditionQuery" >
	SELECT
		t.reasonId,
		t.siteType,
		t.moduleType,
		t.content,
		t.state,
		t.timeAdd,
		t.timeEdit,
		t.userAdd,
		t.userEdit	from config_reason t
	<isParameterPresent >
	<include refid="Example_Where_Clause" />
	</isParameterPresent>
	order by
	<isNotNull property="orderCol" >
		$orderCol$
	</isNotNull>
	<isNull property="orderCol" >
		reasonId desc 
	</isNull>
	<isNotEqual property="rp" compareValue="0" >
	    limit #firstResult#, #rp#
	</isNotEqual>
</select>
<insert id="insert" parameterClass="com.shunwang.baseStone.reason.pojo.Reason" >
	insert into config_reason (
		siteType,
		moduleType,
		content,
		state,
		timeAdd,
		timeEdit,
		userAdd,
		userEdit	)values(
		#siteType:varchar#,
		#moduleType:varchar#,
		#content:varchar#,
		#state:tinyint#,
		#timeAdd:datetime#,
		#timeEdit:datetime#,
		#userAdd:varchar#,
		#userEdit:varchar#	)
	<selectKey resultClass="java.lang.Integer" keyProperty="reasonId" >
	SELECT LAST_INSERT_ID()
	</selectKey>
</insert>
<update id="update" parameterClass="com.shunwang.baseStone.reason.pojo.Reason" >
	update config_reason set
		siteType=#siteType:varchar#,
		moduleType=#moduleType:varchar#,
		content=#content:varchar#,
		state=#state:tinyint#,
		timeEdit=#timeEdit:datetime#,
		userEdit=#userEdit:varchar# where reasonId = #reasonId:int#
</update>
<delete id="delete" parameterClass="com.shunwang.baseStone.reason.pojo.Reason" >
	delete from config_reason where reasonId=#reasonId:int#
</delete>
<select id="get" resultMap="BaseResultMap" parameterClass="Integer">
	select
	reasonId,
	siteType,
	moduleType,
	content,
	state,
	timeAdd,
	timeEdit,
	userAdd,
	userEdit	from config_reason
	where reasonId=#reasonId:int#
</select>

<select id="findBySiteTypeAndModuleType" resultMap="BaseResultMap" parameterClass="com.shunwang.baseStone.reason.pojo.Reason">
	select
	reasonId,
	siteType,
	moduleType,
	content,
	state,
	timeAdd,
	timeEdit,
	userAdd,
	userEdit	from config_reason
	where 1=1
		<isNotEmpty prepend=" AND " property="siteType"> siteType=#siteType# </isNotEmpty>
	<isNotEmpty prepend=" AND " property="moduleType"> moduleType=#moduleType# </isNotEmpty>
	<isNotEmpty prepend=" AND " property="state"> state=#state# </isNotEmpty>
</select>

<select id="findModuleTypeBySiteType" resultClass="java.lang.String" parameterClass="java.lang.String">
	select distinct 
	moduleType	from config_reason
	where
	 siteType=#value:varchar#
</select>

<select id="findCnt" parameterClass="com.shunwang.framework.ibatis.query.ConditionQuery" resultClass="java.lang.Integer" >
	select count(*) from config_reason t
	<include refid="Example_Where_Clause" />
</select>
</sqlMap>