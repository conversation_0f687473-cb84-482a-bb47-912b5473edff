package com.shunwang.baseStone.sso.apapter;

import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import com.shunwang.baseStone.bussiness.dao.BussinessDao;
import com.shunwang.baseStone.bussiness.pojo.Bussiness;
import com.shunwang.baseStone.context.BaseStoneContext;
import com.shunwang.baseStone.context.IPContext;
import com.shunwang.baseStone.siteinterface.context.SiteContext;
import com.shunwang.baseStone.sso.constant.UserOutsiteConstant;
import com.shunwang.baseStone.sso.google.oauth.GoogleOauthService;
import com.shunwang.baseStone.sso.google.pojo.GoogleUser;
import com.shunwang.baseStone.sso.util.UploadFileUtil;
import com.shunwang.baseStone.useroutinterface.context.UseroutInterfaceContext;
import com.shunwang.basepassport.user.pojo.Member;
import com.shunwang.basepassport.user.pojo.MemberAccountBind;
import com.shunwang.basepassport.user.pojo.MemberOutSite;
import com.shunwang.util.StringUtil;
import com.shunwang.util.net.IpUtil;
import org.apache.struts2.ServletActionContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Type;
import java.net.URLDecoder;
import java.util.HashMap;
import java.util.Map;

public class GoogleAdapter extends UserOutsiteApapter {
    private static final Logger log = LoggerFactory.getLogger(GoogleAdapter.class);

    // Build flow and trigger user authorization request.
    private String idToken;
    private String code;
    Gson gson = new Gson();

    private GoogleOauthService googleOauthService;


    @Override
    public String getInterfaceId() {
        return UserOutsiteConstant.GOOGLE_INTERFACE_ID;
    }

    @Override
    public String goToOauth() throws Exception {
        cacheExtData(buildCacheUserKey(), 30 * 60);
        return googleOauthService.getOauthUrl(getRedirectUrl(), getRedirectUrlParam());
    }

    @Override
    public String oauthCallback() {
        try {
            pullCacheExtData(userSceneKey);
            initContextInfo();
            String idTokenString = googleOauthService.getIdTokenByCode(code);
            GoogleUser googleUser = googleOauthService.getGoogleUser(idTokenString);
            if (googleUser == null) {
                log.error("获取google用户信息为空");
                throw new NullPointerException();
            }

            memberOutSite = new MemberOutSite();

            //云海特殊字段 开始
            memberOutSite.setVersion(version);
            memberOutSite.setEnv(env);
            memberOutSite.setRemark(extData);
            //云海特殊字段 结束　

            memberOutSite = buildMemberOutSite(googleUser);
            member = queryMemberBy(memberOutSite.getOutMemberId());
            if (member == null) { // 外部用户不存在，为其生成
                member = outSiteMemberRegister(memberOutSite);
                //　此处保存头像
                member.setHeadImg(UploadFileUtil.saveNetHeadImgToPath(member.getMemberId(), googleUser.getPicture()));
                interfaceService.updateMember(member);
            } else {
                updateHeadAndNick(googleUser);
            }
            initLoginElement(super.getSite_id());
            if (isSingleAccount) {
                if (getSingleAccount() == null) {
                    initCss(site_id);
                    setSingleBindSign(createSingleBindSign());
                    return singleBindInputView();//单账号绑定
                }
            }
            login(member);
            return SUCCESS;

        } catch (Exception e) {
            log.warn("\n\n处理google回调异常,上下文信息为" + "\n");
            proessErrorLog(e);
            setMsg("google返回数据存在异常");
        }
        return INPUT;
    }

    private void initContextInfo() {
        //初始 ip context 和 siteContext
        IPContext.setIp(IpUtil.getIpAddress(ServletActionContext.getRequest()));
        SiteContext.setSiteId(site_id);

        //初始化 siteName
        BussinessDao bussinessDao = (BussinessDao) BaseStoneContext.getInstance().getBean("bussinessDao");
        Bussiness buss = bussinessDao.getById(site_id);
        String siteName = (null == buss) ? "非法数据" : buss.getBussinessname();
        SiteContext.setSiteName(siteName);
    }

    protected MemberOutSite buildMemberOutSite(GoogleUser googleUser) {
        MemberOutSite memberOutSite = new MemberOutSite();
        memberOutSite.setOutMemberId(googleUser.getUserId());

        int i = 0;
        do {
            memberOutSite.setMemberName(generalMemberName(googleUser.getUserId()));
            if (getMemberDao().getByName(memberOutSite.getMemberName()) != null) {
                i++;
            } else {
                i = 4;
            }
        } while (i < 4);

        memberOutSite.setOutMemberName(googleUser.getName());
        memberOutSite.setNickName(googleUser.getName());
        memberOutSite.setRegFrom(site_id);
        memberOutSite.setVersion(version);
        memberOutSite.setEnv(env);
        memberOutSite.setRemark(extData);
        memberOutSite.setMemberFrom(UserOutsiteConstant.GOOGLE_INTERFACE_ID);
        if (!StringUtil.isBlank(googleUser.getPicture())) {
            memberOutSite.setHeadImg(googleUser.getPicture());
        }

        return memberOutSite;
    }

    public String generalMemberName(String userId) {
        return "google_" + userId;
    }


    private void updateHeadAndNick(GoogleUser googleUser) {
        Member paramMember = null;
        if (StringUtil.isBlank(member.getHeadImg())) {
            paramMember = new Member();
            paramMember.setMemberId(member.getMemberId());
            member.setHeadImg(UploadFileUtil.saveNetHeadImgToPath(member.getMemberId(), googleUser.getPicture()));
            paramMember.setHeadImg(member.getHeadImg());
        }
        if (!StringUtil.isBlank(googleUser.getName()) && !googleUser.getName().equals(member.getNickName())) {
            if (null == paramMember) {
                paramMember = new Member();
                paramMember.setMemberId(member.getMemberId());
            }
            paramMember.setMemberName(member.getMemberName());
            paramMember.setNickName(googleUser.getName());
        }

        if (paramMember != null) {
            interfaceService.updateMember(paramMember);
        }
    }

    private String getRedirectUrlParam() {
        Map<String, String> params = new HashMap<>();
        params.put("site_id", site_id);
        params.put("callbackUrl", callbackUrl);
        params.put("tgt", tgt == null ? "" : tgt);
        return gson.toJson(params);
    }

    private String getRedirectUrl() {
        useroutInterface = UseroutInterfaceContext.getUseroutInterfaceById(getInterfaceId());
        log.info("Google登录方式从数据库中获取的回调地址是:" + useroutInterface.getCallbackURL());
        return useroutInterface.getCallbackURL();
    }

    @Override
    protected String getOutOauthLogName() {
        return null;
    }

    @Override
    protected MemberAccountBind getByMemberId() {
        return null;
    }

    @Override
    protected Integer getOutOauthType() {
        return null;
    }

    public String getIdToken() {
        return idToken;
    }

    public void setIdToken(String idToken) {
        this.idToken = idToken;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public GoogleOauthService getGoogleOauthService() {
        return googleOauthService;
    }

    public void setGoogleOauthService(GoogleOauthService googleOauthService) {
        this.googleOauthService = googleOauthService;
    }

    /**
     * state 是透传参数，因此在返回时setState方法里把原先的参数展开还原
     */
    public void setState(String state) {
        try {
            Type type = new TypeToken<Map<String, String>>() {
            }.getType();
            Map<String, String> params = gson.fromJson(state, type);
            setSite_id(params.get("site_id"));
            setCallbackUrl(URLDecoder.decode(params.get("callbackUrl"), "utf-8"));
            setTgt(params.get("tgt"));
        } catch (Exception e) {
            log.error("设置返回参数异常", e);
        }
    }
}
