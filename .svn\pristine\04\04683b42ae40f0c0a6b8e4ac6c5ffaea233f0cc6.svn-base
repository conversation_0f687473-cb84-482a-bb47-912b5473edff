package com.shunwang.baseStone.sso.apapter;

import com.shunwang.baseStone.bussiness.dao.BussinessDao;
import com.shunwang.baseStone.bussiness.pojo.Bussiness;
import com.shunwang.baseStone.cache.CacheKeyConstant;
import com.shunwang.baseStone.context.BaseStoneContext;
import com.shunwang.baseStone.context.LoginElementService;
import com.shunwang.baseStone.context.RedisContext;
import com.shunwang.baseStone.context.RedisOperation;
import com.shunwang.baseStone.core.detail.HasDetail;
import com.shunwang.baseStone.core.exception.BaseStoneException;
import com.shunwang.basepassport.config.common.CssUtil;
import com.shunwang.basepassport.config.common.OauthUtil;
import com.shunwang.basepassport.config.pojo.Css;
import com.shunwang.baseStone.loginelement.constants.LoginElementConstant;
import com.shunwang.baseStone.sso.constant.RandConstant;
import com.shunwang.baseStone.sso.context.ClientTicketContext;
import com.shunwang.baseStone.sso.context.SsoDomainContext;
import com.shunwang.baseStone.sso.context.TicketContext;
import com.shunwang.baseStone.sso.context.UserTockenContext;
import com.shunwang.baseStone.sso.intfc.InterfaceService;
import com.shunwang.baseStone.sso.pojo.QrCodeResponse;
import com.shunwang.baseStone.sso.util.CommonUtil;
import com.shunwang.baseStone.useroutinterface.context.UseroutInterfaceContext;
import com.shunwang.baseStone.useroutinterface.pojo.UseroutInterface;
import com.shunwang.basepassport.config.pojo.Oauth;
import com.shunwang.basepassport.detail.common.DetailContants;
import com.shunwang.basepassport.manager.bean.ReportEntity;
import com.shunwang.basepassport.manager.service.bo.ReportProcessor;
import com.shunwang.basepassport.user.common.MemberConstants;
import com.shunwang.basepassport.user.common.MemberUtil;
import com.shunwang.basepassport.user.dao.MemberAccountBindDao;
import com.shunwang.basepassport.user.dao.MemberDao;
import com.shunwang.basepassport.user.dao.MemberOutSiteDao;
import com.shunwang.basepassport.user.pojo.Member;
import com.shunwang.basepassport.user.pojo.MemberAccountBind;
import com.shunwang.basepassport.user.pojo.MemberOutSite;
import com.shunwang.basepassport.user.pojo.SingleAccountToken;
import com.shunwang.basepassport.weixin.constant.WeixinConstant;
import com.shunwang.basepassport.weixin.pojo.WeixinOpenIdUnionId;
import com.shunwang.basepassport.weixin.service.WeixinOpenIdUnionIdService;
import com.shunwang.framework.struts2.action.BaseAction;
import com.shunwang.util.OS.OsUtil;
import com.shunwang.util.date.DateUtil;
import com.shunwang.util.encrypt.AesEncrypt;
import com.shunwang.util.encrypt.Md5Encrypt;
import com.shunwang.util.lang.StringUtil;
import com.shunwang.util.math.RandomUtil;
import com.shunwang.util.net.IpUtil;
import org.apache.struts2.ServletActionContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigInteger;
import java.net.URLEncoder;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

public abstract class UserOutsiteApapter extends BaseAction implements ReportProcessor, OutSiteCacheable {

	private static final long serialVersionUID = -856673288062890255L;
	
	protected final Logger LOG = LoggerFactory.getLogger(this.getClass());
	protected static final String USER_AGENT = "User-Agent";

	protected static final String INNER_SCAN = "1";

	protected MemberAccountBindDao memberAccountBindDao ;
	protected MemberDao memberDao;
	private int rand = RandConstant.rand(20000);
	protected UseroutInterface useroutInterface;
	protected MemberOutSite memberOutSite;
	protected String site_id;
	protected String sign;
	protected String serviceUrl;
	protected String callbackUrl;
	protected String ticket;
	protected String tockenId;
	protected Member member;
	/** * 调用时指定公众号 */
	protected String appId;
	/**业务方透传场景值 用于业务方自己解析场景值做对应的业务，该值拼接在我方生成的场景值之后  userKey_xxxxxxx_siteScene **/
	protected String siteScene;
	/**QR_code扫码登录使用，内部产生*/
	protected String innerScene;
	protected String inner;
	/** 是否多端跳转 **/
	protected boolean multiTerminalLoginJump = false;

    protected String version;
    protected String env;
    protected String extData;
    protected String reportData;
	protected String needCode;

	protected String tgt ;// 是否覆盖父页面（默认为覆盖，如果传self则不覆盖）
	protected String tgtCancel;//
	protected String clientTicket;
	private Boolean isFreeLoginFlag = Boolean.FALSE;
	/**
	 * 网维客户端自动登录写数据开关
	 */
	private Boolean isFreeLoginWriteDataFlag = Boolean.FALSE;

	protected String cssSiteId ;//外部cssSiteId
	protected String loginCssUrl ;
	protected Css html5Css ;
	protected Css loginCss ;
	protected BussinessDao bussinessDao ;
	protected Boolean isSingleAccount = Boolean.FALSE;
	protected Boolean isOauth = Boolean.TRUE ;
	protected String visitType ;
	protected String href;
	//H5单帐号绑定页面显示使用
	protected String nickName;
	protected String headImage;

	private Boolean showGt;
	private Boolean needCheckCode;
	private Boolean singleGeetestSwitch = false;//单帐号开关
	private int count;

	/**
	 * 新注册用户存取缓存用,用户撤销注销
	 */
	protected String memberName;
	protected String idCardName;

	protected InterfaceService interfaceService;

	protected LoginElementService loginElementService;

	protected Css privacyCss; //读取隐私政策url配置
    protected Css userAgreementCss;

    protected String optToken;
    protected boolean refreshPage = false;//当前页面是否需要刷新（目前用于注销撤销页面的跳转）

	protected String clientIp;

	/**
	 * 用于缓存接入方透传数据，扫码后微信会返回该值
	 */
	protected String userSceneKey;

	//透传给微信，后续根据这个值判断是否对登录结果缓存数据 1 需要缓存 0 不需要, 默认不需要
	protected String cacheResult = "0";
	protected String singleBindSign;
	protected static final String NO_RETURN = "noReturn";

	protected RedisOperation redisOperation;

	protected WeixinOpenIdUnionIdService weixinOpenIdUnionIdService;

	/**
	 * 处理错误日志
	 * @param e 异常信息
	 */
	protected void proessErrorLog(Exception e) {
		LOG.error(e.getMessage()+"\n作业单元ID："+getInterfaceId(), e);

	}
	
	protected UserOutsiteApapter() {
		super();
		try{
			useroutInterface = UseroutInterfaceContext.getUseroutInterfaceById(getInterfaceId());
		}catch (Exception e) {
			LOG.error(e.getMessage()+"\n作业单元ID："+getInterfaceId(), e);
		}
	}
	/**
	 * 获取外部接口id
	 * @return 接口id
	 */
	public abstract String getInterfaceId();
	/**
	 * 去外部站点认证
	 * @return 跳转地址
	 */
	public abstract String goToOauth() throws Exception ;

	/**
	 *  ajax获取外部站点登录相关系信息
	 */
	public Map<String, Object> getOauth()  {
		throw new RuntimeException("未实现该接口") ;
	};

	/**
	 *  服务端获取外部站点登录相关系信息
	 */
	public Map<String, Object> getOauthByServer() throws Exception {
		throw new RuntimeException("未实现该接口") ;
	}
	
    /**
	 * 去外部站点认证(云海)
     * 需做特殊操作
	 */
	public String goToOauthYh() {
        throw new UnsupportedOperationException("非法操作");
    }

	protected MemberAccountBind getSingleAccount(){
		MemberAccountBind memberAccountBind = getByMemberId() ;
		if(null != memberAccountBind){
			Integer memberId = memberAccountBind.getMemberId() ;
			member = memberDao.getByMemberId(memberId) ;
			return memberAccountBind ;
		}
		//手机设为登录账号了
		if(member.getMobileAsLoginAccount()){
			memberAccountBind = memberAccountBindDao.getByMobile(member.getMobile());
			if (memberAccountBind != null) {
				if (memberAccountBind.getMemberId().equals(member.getMemberId())) {
					LOG.info("该{}账号[{}]绑定的手机已经存在关联表直接绑定",getOutOauthLogName(), member.getMemberName()) ;
					return memberAccountBind;
				}
				throw new BaseStoneException("", "绑定失败，数据异常");
			}
			memberAccountBind = buildMemberAccountBind();
			memberAccountBind.setPhone(member.getMobile());
			memberAccountBind.setTimeAdd(new Date());
//			boolean result = interfaceService.singleAccountBindExt(memberAccountBind);
			return singleAccountBindExt(memberAccountBind);
		}
		return null;
	}

	private MemberAccountBind singleAccountBindExt(MemberAccountBind memberAccountBind) {
		memberAccountBind.getPersonalEditLog().setMember(getMember());
		memberAccountBind.getPersonalEditLog().setUserAdd(getMember().getMemberName());
		memberAccountBind.getPersonalEditLog().setType(DetailContants.FRONT);
		memberAccountBind.setTimeEdit(new Date());
		try {
			memberAccountBindDao.save(memberAccountBind);
			((HasDetail)memberAccountBind).getDetail().save();
		} catch (Exception e) {
			LOG.error("绑定异常", e);
			throw new BaseStoneException("", "绑定异常");
		}
		return memberAccountBind;
	}
	protected MemberAccountBind singleAccountUpdateExt(MemberAccountBind memberAccountBind) {
		memberAccountBind.getPersonalEditLog().setMember(getMember());
		memberAccountBind.getPersonalEditLog().setUserAdd(getMember().getMemberName());
		memberAccountBind.getPersonalEditLog().setType(DetailContants.FRONT);
		memberAccountBind.setTimeEdit(new Date());
		try {
			memberAccountBindDao.update(memberAccountBind);
			((HasDetail)memberAccountBind).getDetail().save();
		} catch (Exception e) {
			LOG.error("绑定异常", e);
			throw new BaseStoneException("", "绑定异常");
		}
		return memberAccountBind;
	}

	/**
	 * 创建单帐号绑定页面的签名
     */
	protected String createSingleBindSign(){
		String bindSign = UUID.randomUUID().toString();
		String key = CacheKeyConstant.InterfaceToken.SINGLE_ACCOUNT_SIGN+ bindSign;
		SingleAccountToken singleAccountToken = new SingleAccountToken() ;
		singleAccountToken.setType(getOutOauthType());
		singleAccountToken.setUnionId(member.getMemberId()) ;
		RedisContext.getRedisCache().set(key , singleAccountToken, 30, TimeUnit.MINUTES);

		return bindSign ;
	}

	/**
	 * 获取对应的第三方配置
	 */
	protected Oauth getOauthConfig(){
		Integer type = getOutOauthType() ;
		if(null == type){ //没有相关类型，目前只有QQ、微信、微博会在此配置
			return null ;
		}
		// 获取对应site、type的记录
		Oauth configOauth = OauthUtil.loadOauth(site_id, type) ;
		if(null == configOauth){
			// 选取默认的配置
			configOauth = OauthUtil.loadDefaultOauth(type) ;
		}
		return configOauth ;
	}

	/**
	 * 第三方登录日志名称
     */
	protected abstract String getOutOauthLogName();

	/**
	 * 第三方登录获取绑定账号 byWeixi  byWeibo
     */
	protected abstract MemberAccountBind getByMemberId();
	/**
	 * 第三方登录获取绑定账号类型
	 */
	protected abstract Integer getOutOauthType();

	/**
	 * 场景值前缀，用于生成场景值
	 * @return 前缀字符串
	 */
	protected String getUserKeyPrefix() {
		return WeixinConstant.UK_PRE;
	}

	/**
	 * 构建场景值，默认生成为前缀 + BigInteger(时间+10位随机数组).toString(36)
	 * 优先传入的sceneKey
	 * @return 返回对应登录方式的场景值
	 */
	protected String buildCacheUserKey(){
		String randomScene = new BigInteger(DateUtil.getCurrentDateStamp() + RandomUtil.getRandomStr(10)).toString(36);
		return getUserKeyPrefix() + randomScene;
	}

	protected void setBindMemberId() {

	}
	/**
	 * 子类需设定特定id（子类若调用了getSingleAccount()方法必须重写）
     */
	protected MemberAccountBind buildMemberAccountBind() {
		MemberAccountBind memberAccountBind = new MemberAccountBind();
		memberAccountBind.setMemberId(member.getMemberId());
		memberAccountBind.setTimeEdit(new Date());
		return memberAccountBind;
	}
	/**
	 * 外部站点认证完毕后回调
	 */
	public abstract String oauthCallback();
	
	public String getSite_id() {
		return site_id;
	}
	public void setSite_id(String siteId) {
		site_id = siteId;
	}
	public String getServiceUrl() {
		return serviceUrl;
	}
	public void setServiceUrl(String serviceUrl) {
		this.serviceUrl = serviceUrl;
	}
	public String getCallbackUrl() {
		return callbackUrl;
	}
	public void setCallbackUrl(String callbackUrl) {
		this.callbackUrl = callbackUrl;
	}
	public void setUseroutInterface(UseroutInterface useroutInterface) {
		this.useroutInterface = useroutInterface;
	}
	public UseroutInterface getUseroutInterface() {
		return useroutInterface;
	}
	public String getFmtCallback(){
		if(StringUtil.isBlank(callbackUrl)) return null;
		String split = "?";
		if(callbackUrl.contains("?")){
			split = "&";
		}
		StringBuilder sb = new StringBuilder();
		sb.append(callbackUrl);
		if (StringUtil.isNotBlank(ticket)) {
			sb.append(split).append("ticketId=").append(ticket).append("&tockenId=").append(tockenId);
		}
		return sb.toString();
	}
	
	public void login(Member member){
		doReport(member, ReportEntity.InterfaceType.login);

		this.setTicket(TicketContext.createTicket(member, getSite_id()).toString());
		this.setTockenId(UserTockenContext.createTocken(member,site_id).toString());
		this.setClientTicket(ClientTicketContext.createClientTicket(member,site_id).toString());

		CommonUtil.addCookieAfterLogin(getRequest(), getResponse(), getTockenId(), member.getMemberId());
	}

    /**
     * 供子类调用
     * @param outMemberId 外部id
     */
	protected Member queryMemberBy(String outMemberId) {
		memberOutSite.setOutMemberId(outMemberId);
		return memberOutSite.getDao().getByOutMemberId(outMemberId);
	}

	protected Member outSiteMemberRegister(MemberOutSite memberOutSite) {
		Member regMember = interfaceService.outSiteMemberRegister(memberOutSite);
		doReport(regMember, ReportEntity.InterfaceType.reg);
		return regMember;
	}

	protected void saveIfNotExist(WeixinOpenIdUnionId weixinOpenIdUnionId) {
		try {
			if (weixinOpenIdUnionId == null) {
				return;
			}
			weixinOpenIdUnionIdService.saveIfNotExist(weixinOpenIdUnionId);
		} catch (Exception e) {
			LOG.error("保存微信openId与unionId关系异常", e);
		}
	}


	protected void doReport(Member member, ReportEntity.InterfaceType interfaceType) {
		Integer memberId = member == null ? null : member.getMemberId();
		reportData = redisOperation.get(WeixinConstant.EXT_SCENE_KEY_REPORT_DATA + userSceneKey);
		report(getSite_id(), memberId, interfaceType, reportData);
	}


	public String checkUserName() throws IOException{
		getResponse().setContentType("text/json;charset=UTF-8");
		getResponse().getWriter().write("{\"flag\":"+getMemberOutSiteDao().checkUserName(memberOutSite.getMemberName())+"}");
		return null;
	}
	
	public void initLoginElement(String siteId) {
		if (null == siteId) {
			return;
		}

		// 查询账号体系
		Bussiness bussiness = bussinessDao.getById(siteId) ;
		isSingleAccount = bussiness.isSingleAccount() ;

		// 初始化快速登录页
		Map<String, String> configMap = loginElementService.getLoginConfig(siteId, LoginElementConstant.QUICK_LOGIN);
		if (configMap == null) {
			return;
		}
		setIsFreeLoginFlag(loginElementService.configIsOpen(configMap,LoginElementConstant.STATE));

		// 初始化快速登录写共享登录数据开关
		Map<String, String> configMapWriteData = loginElementService.getLoginConfig(siteId, LoginElementConstant.QUICK_LOGIN_WRITE_DATA);
		if (configMapWriteData == null) {
			return;
		}
		setIsFreeLoginWriteDataFlag(loginElementService.configIsOpen(configMapWriteData, LoginElementConstant.STATE));
	}

	/**
	 * @param siteId 站点id
	 */
	protected void initCss(String siteId) {
		if(!StringUtil.isBlank(getCssSiteId())) {
			siteId = getCssSiteId();
		}
		if(StringUtil.isNotBlank(loginCssUrl)) {
			html5Css = new Css(siteId,loginCssUrl);
			loginCss = new Css(siteId,loginCssUrl) ;
		}else{
			loginCss = CssUtil.loadCss(siteId, CacheKeyConstant.Css.LOGIN);
			if (loginCss == null) {
				loginCss = new Css(siteId, SsoDomainContext.getStaticServer() + SsoDomainContext.getCdnVersion() + "/style/default.css");
			}
			html5Css = CssUtil.loadCss(siteId, CacheKeyConstant.Css.HTML5_CSS);
			if (html5Css == null) {
				html5Css = new Css(siteId, SsoDomainContext.getStaticServer() + SsoDomainContext.getCdnVersion() + "/style/defaultForHtml5.css");
			}
			loginCssUrl = loginCss.getCssUrl();
		}
	}

	protected String singleBindInputView(){
		//初始化极验信息
		initSingleGeetestSwitch();
		if(OsUtil.isMobile(getRequest().getHeader("User-Agent"))){
			return "singleAccountBindForH5";
		}
		return "singleAccountBind";
	}
	private boolean configIsOpen(Map<String, String> configMap) {
		if (null != configMap && null != configMap.get(LoginElementConstant.STATE)
				&& configMap.get(LoginElementConstant.STATE).equals("1")) {
			return Boolean.TRUE;
		}
		return Boolean.FALSE;
	}
	protected void initSingleGeetestSwitch() {
		setSingleGeetestSwitch(loginElementService.getConfigIsOpen(site_id, LoginElementConstant.SINGLE_GEETEST_SWITCH)
				&& RedisContext.getSwitchConfig(CacheKeyConstant.ConfigResourcesConstants.TYPE_BASE_CONFIG,
				CacheKeyConstant.ConfigResourcesConstants.BASE_CONFIG_GT_SWITCH, false));
	}

    protected String inputCancelView() {
        initCss(site_id);
        try {
            optToken = AesEncrypt.Encrypt(memberName + site_id, MemberUtil.getKey()).substring(8);
        } catch (Exception e) {
            LOG.error("已注销用户登录跳撤销页面数据异常");
        }
        refreshPage = true;
        if(OsUtil.isMobile(getRequest().getHeader("User-Agent"))){
            return "inputCancelForHtml5";
        }
        return "inputCancel";
    }

	protected String responseVisitType() throws UnsupportedEncodingException {
		String split = "?";
		if (callbackUrl.contains("?")) {
			split = "&";
		}

		String sourceStr = visitType + "|" + member.getMemberId();
		String sign =  Md5Encrypt.encrypt(URLEncoder.encode(sourceStr, "UTF-8").toUpperCase(), "UTF-8").toUpperCase();

		callbackUrl = callbackUrl + split + "visitType=bind&unionId=" + member.getMemberId()+ "&sign=" + sign;
		return SUCCESS ;
	}

	protected void cacheExtData(String key, Integer expireSeconds) {
		redisOperation.set(WeixinConstant.EXT_SCENE_KEY + key, site_id, expireSeconds);
		redisOperation.set(WeixinConstant.EXT_SCENE_KEY_REPORT_DATA + key, reportData == null ? "" : reportData, expireSeconds);
		Map<String, String> extMap = new HashMap<>();
		extMap.put("env", env);
		extMap.put("version", version);
		extMap.put("extData", extData);
		extMap.put("clientIp", IpUtil.getIpAddress(ServletActionContext.getRequest()));
		extMap.put("idCardName", idCardName);
		if (StringUtil.isNotBlank(innerScene)) {
			extMap.put("inner", INNER_SCAN);
		}
		redisOperation.setMap(WeixinConstant.REQS_EXT_DATA_KEY + key, extMap, expireSeconds, TimeUnit.SECONDS);
	}

	protected void pullCacheExtData(String key) {
		site_id = StringUtil.isNotBlank(site_id) ? site_id : redisOperation.get(WeixinConstant.EXT_SCENE_KEY + key);
		Map<String, String> extMap = redisOperation.getMap(WeixinConstant.REQS_EXT_DATA_KEY + key, String.class, String.class);
		if (extMap != null) {
			env =  extMap.get("env");
			version = extMap.get("version");
			extData  = extMap.get("extData");
			clientIp = extMap.get("clientIp");
			idCardName = extMap.get("idCardName");
			inner = extMap.get("inner");
		}
	}

	//针对扫码登录的一些方法封装 开始-----
	protected void setCacheData(Integer expireSeconds) {
		redisOperation.set(getSiteIdKey(), site_id, expireSeconds);
		redisOperation.set(getReportDataKey(), reportData == null ? "" : reportData, expireSeconds);
		Map<String, String> extMap = new HashMap<>();
		extMap.put("env", env);
		extMap.put("version", version);
		extMap.put("extData", extData);
		redisOperation.setMap(getExtDataKey(), extMap, expireSeconds, TimeUnit.SECONDS);
	}
	//针对扫码登录的一些方法封装 结束-----

	protected String goSingleBind(String nickName,String headImage) throws IOException {
		if (getSingleAccount() == null) {
			initCss(site_id);
			setSingleBindSign(createSingleBindSign());
			if(OsUtil.isMobile(getRequest().getHeader(USER_AGENT))){
				String redirectUrl = SsoDomainContext.getSsoServer() + "/single/goSingle.do?nickName=" + URLEncoder.encode(nickName, "UTF-8")
						+ "&headImage=" + URLEncoder.encode(headImage, "UTF-8")
						+ "&callbackUrl=" + URLEncoder.encode(callbackUrl, "UTF-8")
						+ "&singleBindSign=" + getSingleBindSign() + "&site_id=" + site_id + "&tgt=" + tgt + "&href=" + href;
				getResponse().sendRedirect(redirectUrl);
				return null;
			}
			initSingleGeetestSwitch();
			return "singleAccountBind";
		}
		if (member.getMemberState() == MemberConstants.USER_CANCEL_STATE) {
			setMsg("您的账号已被注销！");
			memberName = member.getMemberName();
			refreshPage = true;
			return inputCancelView();
		}
		return NO_RETURN;
	}

	protected String qrSuccess() {
		QrCodeResponse qrCodeResult = new QrCodeResponse(QrCodeResponse.TypeStage.LOGIN_SUCCESS);
		qrCodeResult.setSingle(false);
		//客户端登录使用新票据
		qrCodeResult.setTicket(TicketContext.createTicket(member, getSite_id()).toString());
		qrCodeResult.setTockenId(UserTockenContext.createTocken(member,site_id).toString());
		qrCodeResult.setClientTicket(ClientTicketContext.createClientTicket(member,site_id).toString());
		// 向缓存中添加，通过场景值，缓存1分钟
		redisOperation.set(userSceneKey, qrCodeResult, 1, TimeUnit.MINUTES);
		return "successQr";
	}

	protected void loadPrivacyCss(){
		setPrivacyCss(CssUtil.loadCss(site_id, CacheKeyConstant.Css.PRIVACY_AGREEMENT));
		//作业单元没有配置则读取passport的隐私政策
		if(getPrivacyCss() == null){
			setPrivacyCss(CssUtil.loadCss(CacheKeyConstant.SiteId.PASSPORT, CacheKeyConstant.Css.PRIVACY_AGREEMENT));
		}
        setUserAgreementCss(CssUtil.loadCss(site_id, CacheKeyConstant.Css.USER_AGREEMENT));
        //作业单元没有配置则读取passport的用户协议
        if(getUserAgreementCss() == null){
            setUserAgreementCss(CssUtil.loadCss(CacheKeyConstant.SiteId.PASSPORT, CacheKeyConstant.Css.USER_AGREEMENT));
        }
	}

	public void setTicket(String ticket) {
		this.ticket = ticket;
	}

	public String getTicket() {
		return ticket;
	}

	public void setMemberOutSite(MemberOutSite memberOutSite) {
		this.memberOutSite = memberOutSite;
	}

	public MemberOutSite getMemberOutSite() {
		return memberOutSite;
	}

	public void setSign(String sign) {
		this.sign = sign;
	}

	public String getSign() {
		return sign;
	}

	private MemberOutSiteDao getMemberOutSiteDao() {
		return (MemberOutSiteDao)BaseStoneContext.getInstance().getBean("memberOutSiteDao");
	}

	public String getTockenId() {
		return tockenId;
	}
	public String getToken() {
		return tockenId;
	}

	public void setTockenId(String tockenId) {
		this.tockenId = tockenId;
	}

	public void setMember(Member member) {
		this.member = member;
	}

	public Member getMember() {
		return member;
	}

	public void setRand(int rand) {
		this.rand = rand;
	}

	public int getRand() {
		return rand;
	}

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public void setEnv(String env) {
        this.env=env;
    }
    public String getEnv() {
        return this.env;
    }


    public void setExtData(String extData) {
        this.extData=extData;
    }
    public String getExtData() {
        return this.extData;
    }

	public String getNeedCode() {
		return needCode;
	}

	public void setNeedCode(String needCode) {
		this.needCode = needCode;
	}

	public String getTgt() {
		return tgt;
	}

	public void setTgt(String tgt) {
		this.tgt = tgt;
	}

	public String getClientTicket() {
		return clientTicket;
	}

	public void setClientTicket(String clientTicket) {
		this.clientTicket = clientTicket;
	}

	public Boolean getIsFreeLoginFlag() {
		return isFreeLoginFlag;
	}

	public void setIsFreeLoginFlag(Boolean isFreeLoginFlag) {
		this.isFreeLoginFlag = isFreeLoginFlag;
	}

	public Boolean getIsFreeLoginWriteDataFlag() {
		return isFreeLoginWriteDataFlag;
	}

	public void setIsFreeLoginWriteDataFlag(Boolean isFreeLoginWriteDataFlag) {
		this.isFreeLoginWriteDataFlag = isFreeLoginWriteDataFlag;
	}

	public BussinessDao getBussinessDao() {
		return bussinessDao;
	}

	public void setBussinessDao(BussinessDao bussinessDao) {
		this.bussinessDao = bussinessDao;
	}

	public String getCssSiteId() {
		return cssSiteId;
	}

	public void setCssSiteId(String cssSiteId) {
		this.cssSiteId = cssSiteId;
	}

	public Css getHtml5Css() {
		return html5Css;
	}

	public void setHtml5Css(Css html5Css) {
		this.html5Css = html5Css;
	}

	public Css getLoginCss() {
		return loginCss;
	}

	public void setLoginCss(Css loginCss) {
		this.loginCss = loginCss;
	}

	public String getLoginCssUrl() {
		return loginCssUrl;
	}

	public void setLoginCssUrl(String loginCssUrl) {
		this.loginCssUrl = loginCssUrl;
	}

	public Boolean getIsOauth() {
		return isOauth;
	}

	public void setIsOauth(Boolean oauth) {
		isOauth = oauth;
	}

	public Boolean getIsSingleAccount() {
		return isSingleAccount;
	}

	public void setIsSingleAccount(Boolean singleAccount) {
		isSingleAccount = singleAccount;
	}

	public String getVisitType() {
		return visitType;
	}

	public void setVisitType(String visitType) {
		this.visitType = visitType;
	}

	public String getHref() {
		return href;
	}

	public void setHref(String href) {
		this.href = href;
	}

	public MemberAccountBindDao getMemberAccountBindDao() {
		return memberAccountBindDao;
	}

	public void setMemberAccountBindDao(MemberAccountBindDao memberAccountBindDao) {
		this.memberAccountBindDao = memberAccountBindDao;
	}

	public MemberDao getMemberDao() {
		return memberDao;
	}

	public void setMemberDao(MemberDao memberDao) {
		this.memberDao = memberDao;
	}

    public InterfaceService getInterfaceService() {
        return interfaceService;
    }

    public void setInterfaceService(InterfaceService interfaceService) {
        this.interfaceService = interfaceService;
    }

	public String getMemberName() {
		return memberName;
	}

	public void setMemberName(String memberName) {
		this.memberName = memberName;
	}

	public LoginElementService getLoginElementService() {
		return loginElementService;
	}

	public void setLoginElementService(LoginElementService loginElementService) {
		this.loginElementService = loginElementService;
	}

	public Css getPrivacyCss() {
		return privacyCss;
	}

	public void setPrivacyCss(Css privacyCss) {
		this.privacyCss = privacyCss;
	}

    public String getOptToken() {
        return optToken;
    }

    public void setOptToken(String optToken) {
        this.optToken = optToken;
    }

    public boolean isRefreshPage() {
        return refreshPage;
    }

    public void setRefreshPage(boolean refreshPage) {
        this.refreshPage = refreshPage;
    }

	public String getReportData() {
		return reportData;
	}

	public void setReportData(String reportData) {
		this.reportData = reportData;
	}

    public Css getUserAgreementCss() {
        return userAgreementCss;
    }

    public void setUserAgreementCss(Css userAgreementCss) {
        this.userAgreementCss = userAgreementCss;
    }

    public Boolean getShowGt() {
        return showGt;
    }

    public void setShowGt(Boolean showGt) {
        this.showGt = showGt;
    }

    public Boolean getNeedCheckCode() {
        return needCheckCode;
    }

    public void setNeedCheckCode(Boolean needCheckCode) {
        this.needCheckCode = needCheckCode;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

	public RedisOperation getRedisOperation() {
		return redisOperation;
	}

	public void setRedisOperation(RedisOperation redisOperation) {
		this.redisOperation = redisOperation;
	}

	public String getTgtCancel() {
		return tgtCancel;
	}

	public void setTgtCancel(String tgtCancel) {
		this.tgtCancel = tgtCancel;
	}

	public Boolean getSingleGeetestSwitch() {
		return singleGeetestSwitch;
	}

	public void setSingleGeetestSwitch(Boolean singleGeetestSwitch) {
		this.singleGeetestSwitch = singleGeetestSwitch;
	}

	public String getSiteScene() {
		return siteScene;
	}

	public void setSiteScene(String siteScene) {
		this.siteScene = siteScene;
	}

	public String getAppId() {
		return appId;
	}

	public void setAppId(String appId) {
		this.appId = appId;
	}

	public String getClientIp() {
		return clientIp;
	}

	public void setClientIp(String clientIp) {
		this.clientIp = clientIp;
	}

	public boolean isMultiTerminalLoginJump() {
		return multiTerminalLoginJump;
	}

	public void setMultiTerminalLoginJump(boolean multiTerminalLoginJump) {
		this.multiTerminalLoginJump = multiTerminalLoginJump;
	}

	public String getUserSceneKey() {
		return userSceneKey;
	}

	public void setUserSceneKey(String userSceneKey) {
		this.userSceneKey = userSceneKey;
	}

	public void setWeixinOpenIdUnionIdService(WeixinOpenIdUnionIdService weixinOpenIdUnionIdService) {
		this.weixinOpenIdUnionIdService = weixinOpenIdUnionIdService;
	}

	public String getCacheResult() {
		return cacheResult;
	}

	public void setCacheResult(String cacheResult) {
		this.cacheResult = cacheResult;
	}

	public String getSingleBindSign() {
		return singleBindSign;
	}

	public void setSingleBindSign(String singleBindSign) {
		this.singleBindSign = singleBindSign;
	}

	public String getNickName() {
		return nickName;
	}

	public void setNickName(String nickName) {
		this.nickName = nickName;
	}

	public String getHeadImage() {
		return headImage;
	}

	public void setHeadImage(String headImage) {
		this.headImage = headImage;
	}

	public String getInnerScene() {
		return innerScene;
	}

	public void setInnerScene(String innerScene) {
		this.innerScene = innerScene;
	}

	public String getInner() {
		return inner;
	}

	public void setInner(String inner) {
		this.inner = inner;
	}

	public String getIdCardName() {
		return idCardName;
	}

	public void setIdCardName(String idCardName) {
		this.idCardName = idCardName;
	}
}
