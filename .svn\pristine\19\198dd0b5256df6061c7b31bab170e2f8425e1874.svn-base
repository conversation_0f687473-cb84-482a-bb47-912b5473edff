package com.shunwang.basepassport.actu.dao;

import com.shunwang.basepassport.actu.pojo.CafeActuChangeInfo;

/**
 * User:pf.ma
 * Date:2017/06/26
 * Time:11:50
 */
public class CafeActuChangeDao extends ActuChangeDao<CafeActuChangeInfo> {

    public CafeActuChangeInfo getByMemberId(Integer memberId, Integer infoState) {
        CafeActuChangeInfo temp = new CafeActuChangeInfo();
        temp.setMemberId(memberId);
        temp.setInfoState(infoState);
        return (CafeActuChangeInfo)getSqlMapClientTemplate().queryForObject(getStatementNameWrap("getByMemberIdOrName"), temp);
    }
}
