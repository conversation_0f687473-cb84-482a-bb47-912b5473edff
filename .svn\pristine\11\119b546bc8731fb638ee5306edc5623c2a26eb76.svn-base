package com.shunwang.basepassport.binder.pojo;

import com.shunwang.baseStone.context.BaseStoneContext;
import com.shunwang.baseStone.context.TransactionContext;
import com.shunwang.baseStone.core.detail.DetailItem;
import com.shunwang.baseStone.sender.context.SenderContext;
import com.shunwang.baseStone.sender.pojo.SendMsg;
import com.shunwang.baseStone.sysconfig.dao.SysconfigDbDao;
import com.shunwang.baseStone.sysconfig.pojo.Sysconfig;
import com.shunwang.basepassport.binder.common.BinderConstants;
import com.shunwang.basepassport.binder.dao.BinderDao;
import com.shunwang.basepassport.binder.dao.QuestionBinderDao;
import com.shunwang.basepassport.binder.exception.QuestionMsgExp;
import com.shunwang.basepassport.commonExp.ParamNotFoundExp;
import com.shunwang.basepassport.commonExp.SystemExp;
import com.shunwang.basepassport.context.UserContext;
import com.shunwang.basepassport.detail.common.DetailContants;
import com.shunwang.basepassport.user.common.MemberConstants;
import com.shunwang.basepassport.user.dao.ProtectedQuestionDao;
import com.shunwang.basepassport.user.exception.MsgNotFoundExp;
import com.shunwang.basepassport.user.pojo.Member;
import com.shunwang.basepassport.user.pojo.ProtectedQuestion;
import com.shunwang.sms.utils.SMSSenderUtil;
import com.shunwang.util.lang.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.Serializable;
import java.util.List;

/**
 * ****************************
 * 版权所有：顺网科技 保留所有权利
 * 创建日期: 2011-8-19 上午11:12:13
 * 创建作者：xiangjie
 * 文件名称：
 * 版本： 1.0
 * 功能：密保问题绑定
 * 最后修改时间：
 * 修改记录：
****************************************
 */
public class QuestionBinder extends Binder{

	/**
	 * 
	 */
	private static final long serialVersionUID = -2902205885099269699L;
	private ProtectedQuestion[] protectedQuestions=new ProtectedQuestion[3];
	private List<ProtectedQuestion> protectedQuestionAll;
	private final static Logger log = LoggerFactory.getLogger(QuestionBinder.class);
	@Override
	public void bind() {
		buildLog(this.getType()+BinderConstants.BUSSINESSTYPE_STR_MAP.get(this.getBusinessType()),DetailContants.FRONT);
		try {
			TransactionContext.beginTransaction();
			if(this.getBusinessType().equals(BinderConstants.CHANGENUMBER))
				getBinderDao().delete(this);
			getBinderDao().save(this);
			getMember().setIsBindQuestion(true);
			getMember().update();
			TransactionContext.commitTran();
			
			String title = "顺网通行证密保问题设置成功啦！";
			Member currentMember = UserContext.getUser();
			
			if(this.getBusinessType().equals(BinderConstants.CHANGENUMBER)) {
				title = "顺网通行证密保问题修改成功啦！";
				if(currentMember.getIsBindEmail()) {
					SenderContext.getEmailSender().doSend(buildEmailSendMsg(currentMember, title, MemberConstants.PRO_CHANGE_EMAIL_KEY));
				}

			} else {
				if(currentMember.getIsBindEmail()) {
					SenderContext.getEmailSender().doSend(buildEmailSendMsg(currentMember, title, MemberConstants.PRO_SET_EMAIL_KEY));
				}
				
				if(currentMember.getIsBindMobile()) {
					SendMsg msg = buildMobileSendMsg(currentMember, title, MemberConstants.PRO_SET_MOBILE_KEY);
					SMSSenderUtil.sendMsg(msg.getNumber(), msg.getContent());
				}
			}
		} catch (Exception e) {
			log.error("问题绑定异常",e);
			TransactionContext.rollbackTran();
			throw new SystemExp();
		}
	}
	
	/**
	 * ***********
	  * 创建日期: 2012-5-23
	  * 创建作者：JINBAO
	  * @param curMember
	  * @return 
	  * 功能：构建修改密码发送邮件内容
	  *************
	 */
	private SendMsg buildEmailSendMsg(Member curMember, String title, String key) {
		Sysconfig config = getSysconfigDbDao().getByConfigKey(key);
		SendMsg msg = new SendMsg();
		msg.setNumber(curMember.getEmail());
		msg.setContent(config.getSysValue().replace("logName", curMember.getMemberName()));
		msg.setTitle(title);
		return msg;
	}
	
	/**
	 * ***********
	  * 创建日期: 2012-5-23
	  * 创建作者：JINBAO
	  * @param currMember
	  * @return 
	  * 功能：构建修改密码发送短信内容
	  *************
	 */
	private SendMsg buildMobileSendMsg(Member currMember, String title, String key) {
		Sysconfig config = getSysconfigDbDao().getByConfigKey(key);
		SendMsg msg = new SendMsg();
		msg.setContent(config.getSysValue().replace("logName", currMember.getMemberName()));
		msg.setNumber(currMember.getMobile());
		msg.setTitle(title);
		return msg;
	}

	@Override
	public void send() {
		protectedQuestionAll = getProtectedQuestionDao().findAll();
	}

	@Override
	public void unBind() {
		
	}
	/**
	 * 
	 * 获取绑定的问题
	 * @return
	 * <AUTHOR> 创建于 2011-9-1 上午09:55:20
	 * @throws
	 */
	@SuppressWarnings("unchecked")
	public void getBinderContent(){
		this.protectedQuestions = ((QuestionBinderDao) getBinderDao()).getQuestionByMemberId(this.getMemberId());
	}
	
	
	@Override
	public Serializable getPk() {
		// TODO Auto-generated method stub
		return null;
	}

	@SuppressWarnings("unchecked")
	@Override
	public BinderDao<Binder> getBinderDao() {
		// TODO Auto-generated method stub
		return (BinderDao<Binder>) BaseStoneContext.getInstance().getBean("questionBinderDao");
	}

	@Override
	public void validate(Object object) {
		// TODO Auto-generated method stub
		checkIsAvailable((ProtectedQuestion[]) object);
		checkAnswer((ProtectedQuestion[]) object);
	}

	@Override
	public boolean isBinded() {
		// TODO Auto-generated method stub
		return this.getMember().getIsBindQuestion();
	}

	@Override
	public String getType() {
		// TODO Auto-generated method stub
		return BinderConstants.QUESTION;
	}
	/**
	 * 检查密保问题是否可用
	 * 
	 * @return
	 * <AUTHOR> 创建于 2011-9-1 上午08:29:42
	 * @throws
	 */
	public void checkIsAvailable(ProtectedQuestion[] protectedQuestions){
		for(ProtectedQuestion question:protectedQuestions){
			if(null == question||!question.isAvailable())
				throw new MsgNotFoundExp("密保问题编号");
			if(StringUtil.isBlank(question.getAnswer()))
				throw new ParamNotFoundExp("密保问题答案");
		}
	}
	/**
	 * 
	 * 
	 * @return
	 * <AUTHOR> 创建于 2011-9-1 上午08:46:33
	 * @throws
	 */
	public void checkAnswer(ProtectedQuestion[] protectedQuestions){
		if(protectedQuestions.length != this.protectedQuestions.length)
			throw new QuestionMsgExp();
		for(int i = 0; i< protectedQuestions.length;i++){
			if(!protectedQuestions[i].getQuestionKey().equals(this.protectedQuestions[i].getQuestionKey()))
				throw new QuestionMsgExp();
			if(!protectedQuestions[i].getAnswer().equals(this.protectedQuestions[i].getAnswer()))
				throw new QuestionMsgExp();
		}
	}
	
	public ProtectedQuestionDao getProtectedQuestionDao(){
		return (ProtectedQuestionDao) BaseStoneContext.getInstance().getBean("protectedQuestionDao");
	}

	public ProtectedQuestion[] getProtectedQuestions() {
		return protectedQuestions;
	}

	public void setProtectedQuestions(ProtectedQuestion[] protectedQuestions) {
		personalEditLog.addItem(new DetailItem(getType(), getValue(this.protectedQuestions), getValue(protectedQuestions)));
		this.protectedQuestions = protectedQuestions;
	}

	public List<ProtectedQuestion> getProtectedQuestionAll() {
		return protectedQuestionAll;
	}

	public void setProtectedQuestionAll(List<ProtectedQuestion> protectedQuestionAll) {
		this.protectedQuestionAll = protectedQuestionAll;
	}
	/**
	 * 
	 * 获取密保问题的问题
	 * @return
	 * <AUTHOR> 创建于 2011-9-3 下午05:32:56
	 * @throws
	 */
	private String getValue(ProtectedQuestion[] protectedQuestions){
		StringBuilder sb = new StringBuilder();
		if(protectedQuestions.length ==0 )
			return "";
		for (int i = 0; i < protectedQuestions.length; i++) {
			String question  = protectedQuestions[i].getQuestion();
			if(StringUtil.isBlank(question))
				question = getProtectedQuestionDao().getById(protectedQuestions[i].getQuestionKey()).getQuestion();
			sb.append(i + 1).append(".").append(question).append("\n");
		}
		return sb.toString().substring(0, sb.toString().length()-1);
	}

	public SysconfigDbDao getSysconfigDbDao() {
		return (SysconfigDbDao)BaseStoneContext.getInstance().getBean("sysconfigDbDao");
	}


}
