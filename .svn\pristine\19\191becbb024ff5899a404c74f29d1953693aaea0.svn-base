package com.shunwang.passport.common.interceptor;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.opensymphony.xwork2.Action;
import com.opensymphony.xwork2.ActionInvocation;
import com.opensymphony.xwork2.interceptor.AbstractInterceptor;
import com.shunwang.baseStone.core.exception.BaseStoneException;
import com.shunwang.framework.struts2.action.BaseAction;
/**
 * @Description:异常处理拦截器
 * <AUTHOR>  create at 2011-9-22 下午01:42:41
 * @FileName com.shunwang.passport.common.interceptor.ExceptionInterceptor.java
 */
public class ExceptionInterceptor extends AbstractInterceptor {

	/**
	 * <AUTHOR> create at 2011-9-22 下午01:42:29 
	 */
	private static final long serialVersionUID = 6856129853549527802L;
	
	private final static Logger log = LoggerFactory.getLogger(ExceptionInterceptor.class);

	@Override
	public String intercept(ActionInvocation arg0) throws Exception {
		try {
			return arg0.invoke();
		} catch (BaseStoneException e) {
			setBaseStoneErrorMsg(arg0,e);
		} catch (Exception e) {
			setErrorMsg(arg0,e,new Exception("系统异常或网络不稳定，请稍后再试！"));
		}
		return Action.ERROR;
	}

    /**
	 * 设置BaseStoneException错误信息, 不打印堆栈
	 * @param arg0
	 * @param e
	 * <AUTHOR> create at 2011-9-22 下午04:07:37
	 */
    private void setBaseStoneErrorMsg(ActionInvocation arg0,BaseStoneException e) {
        BaseAction action = (BaseAction)arg0.getAction();
		action.setErrorMsg(e.getMessage());
		log.error("An error has been happened, errorMsg is " + e.getMessage());
    }

	/**
	 * 设置错误信息
	 * @param arg0
	 * @param oldE
	 * <AUTHOR> create at 2011-9-22 下午04:07:37
	 */
	private void setErrorMsg(ActionInvocation arg0,Exception oldE,Exception newE){
		BaseAction action = (BaseAction)arg0.getAction();
		action.setErrorMsg(newE.getMessage());
		writeLog(oldE);
	}
	/**
	 * 写日志
	 * @param e
	 * <AUTHOR> create at 2011-9-22 下午04:07:50
	 */
	private void writeLog(Exception e){
		log.error("An error has been happened ! For the detail info you can see the exception stacks at \n", e);
	}
	
	

}
