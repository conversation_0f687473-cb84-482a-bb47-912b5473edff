package com.shunwang.basepassport.binder.exception;

import com.shunwang.baseStone.core.exception.BaseStoneException;
import com.shunwang.basepassport.binder.common.BinderConstants;

import static com.shunwang.basepassport.binder.common.ErrorCode.*;
/**
 * ****************************
 * 版权所有：顺网科技 保留所有权利
 * 创建日期: Jul 22, 2011 8:55:37 AM
 * 创建作者：xiangjie
 * 文件名称：
 * 版本： 1.0
 * 功能：number被他人绑定
 * 最后修改时间：
 * 修改记录：
****************************************
 */
public class BindByOtherExp extends BaseStoneException{

	/**
	 * 
	 */
	private static final long serialVersionUID = 8572555153151112871L;

	public BindByOtherExp(String type) {
        super(C_1023.getCode(), "同一" + type + "最多可绑定"+(type.equals(BinderConstants.MOBILE)?BinderConstants.getMobileBinderLimit():BinderConstants.BINDER_LIMIT_EMAIL)+"个通行证账号");
	}

}
