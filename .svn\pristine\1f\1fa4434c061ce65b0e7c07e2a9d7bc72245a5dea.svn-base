var src=document.getElementById('banner_js');
var srcParams = src.src.split('?')[1];
var params = srcParams.split('&');
var previewSave='';
var previewKey='';

for(var i=0; i<params.length; i++) {
    var param = params[i].split('=');
    if(param[0] == 'siteid') {
        siteId = param[1];
    }
    if(param[0] == 'previewSave') {
    	previewSave = param[1];
    }
    if(param[0] == 'previewKey') {
    	previewKey = param[1];
    }
    if(param[0] == 'needReturnUrl') {
    	returnUrl = encodeURIComponent(location.href);
    } else {
    	returnUrl = '';
    }
}
var path = "//passport.kedou.com/front/banner/banner.htm?siteId="+siteId+"&returnUrl="+returnUrl+"&previewSave="+previewSave+"&previewKey="+previewKey;

document.write("<script type='text/javascript' src="+path+">" + "</script>");

function logout(i) {
	document.location="//sso.kedou.com/logout.do?site_id=Passport&callbackUrl="+encodeURIComponent("https://passport.kedou.com/front/member/logout.htm?returnUrl="+location.href);
}

function setTitle(titleInfo) {

    if (! (titleInfo.userName == undefined || titleInfo.userName == "") ) {
        var content = "您好，<a id='showName' href='" + titleInfo.homepageUrl+"'>" + titleInfo.userName;
        content += "</a>&nbsp;&nbsp;&nbsp;&nbsp;<a id='logoutUrl' href='" + titleInfo.logoutUrl + "'>[退出]</a>";
        $("#greetSpan").html(content);
    } else {
        var content = "您好，欢迎来到顺网！&nbsp;&nbsp;&nbsp;&nbsp;<a id='regUrl' href='//passport.kedou.com/front/noLogin/goRegist_front.htm'>注册</a>"
         + " <a id='loginUrl' href='//passport.kedou.com/front/noLogin/goLoginPage.htm?site_id=Passport&return_url='+$CONFIG['appServer']>登录</a>";
        $("#greetSpan").html(content);

        if (! (titleInfo.regUrl == undefined || titleInfo.regUrl == "") ) {
          $("#regUrl").attr("href",titleInfo.regUrl);
        }
        if (! (titleInfo.loginUrl == undefined || titleInfo.loginUrl == "") ) {
          $("#loginUrl").attr("href",titleInfo.loginUrl);
        }
    }

    
    
}
