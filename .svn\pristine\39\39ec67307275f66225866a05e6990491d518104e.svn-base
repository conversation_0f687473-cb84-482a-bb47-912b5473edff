//使用时根据具体业务设置SEND_ACTIVENO_TIME的值
var  CONSTANTS = {
    SEC: 60,
    SEND_ACTIVENO_TIME: 'passport_reg_mobile_send_time',
    HAS_SEND:false
};

//倒计时
var wait=60;
function time(o) {
    if (wait == 0) {
        o.prop("disabled",false).val("获取验证码").removeClass("disabled").addClass("btn-primary");
        wait = 60;
    } else {
        o.prop("disabled", true).val(wait+"秒后重新获取").removeClass("btn-primary").addClass("disabled");
        wait--;
        setTimeout(function() {
            time(o)
        },1000)
    }

}

function isMobileNo(s){
	var reg = "";
	if (typeof(validPhoneRex)==="undefined" || validPhoneRex == "" || validPhoneRex == null) {
		reg = /^1[34578]\d{9}$/;
	} else{
		reg = validPhoneRex;
	}
    if(reg.exec(s)) return true;
    else return false;
}

function checkMobile(number) {
    var number = $.trim(number);

    if (number.length == 0) {
        showTipsError("请输入您的手机号码");
        return false;
    }
    if (!isMobileNo(number)) {
        showTipsError("手机号码格式错误，请重新输入");
        return false;
    }
    $("#mobile").val(number);
    return true;
}

function showTipsError(msg) {
    $("#tips-error p").html(msg);
    $('#tips-error').show();
}

function hideTipsError() {
    $("#tips-error p").html('');
    $('#tips-error').hide();
}

function showTipsInfo(msg) {
    $("#tips-info").html(msg);
    $("#tips-info").show();
}

function restoreSendState() {
    var sendTimestamp;
    var hasSend;
    try {
        sendTimestamp = window.sessionStorage.getItem(CONSTANTS.SEND_ACTIVENO_TIME);
        hasSend = window.sessionStorage.getItem(CONSTANTS.HAS_SEND);
    } catch (e) {
        console.error(e);
    }

    var nowTimestamp = Date.parse(new Date().toString());
    var sec = (parseInt(nowTimestamp) - parseInt(sendTimestamp)) / 1000;
    if (sendTimestamp && sec < CONSTANTS.SEC) {
        wait = CONSTANTS.SEC - sec;
        time($("#sent-code"));
    }
    if(!hasSend) {
        $('#mobileCheckCode').attr('disabled', true)
    }

}

function numberInputCheck() {
    var number = $("#mobile").val();
    if(!checkMobile(number)) {
        $("#mobile").focus();
        return false;
    }
    hideTipsError();
    return true;
}
function buildGtData(that, data) {
    data.geetest_challenge =  that.find("input[name='geetest_challenge']").val();
    data.geetest_validate = that.find("input[name='geetest_validate']").val();
    data.geetest_seccode = that.find("input[name='geetest_seccode']").val();
}
//发送验证码点击事件
function sendCode(callback){
    var number = $("#mobile").val();
    if(!checkMobile(number)) {
        $("#number").focus();
        return false;
    }

    hideTipsError();
    $("#sent-code").prop("disabled", true);
    var timestamp = Date.parse(new Date().toString());
    try {
        window.sessionStorage.setItem(CONSTANTS.SEND_ACTIVENO_TIME, timestamp);
        window.sessionStorage.setItem(CONSTANTS.HAS_SEND, true);
    } catch (e) {
        console.error(e);
    }
    $('#mobileActiveNo').removeAttr('disabled');
    var data = {"mobile":number};
    buildGtData($("#js-form"), data);
    var aj = $.ajax( {
        url:"/front/swpaysdk/sendRegActiveNo.htm",
        type:"POST",
        data: data,
        dataType:'json',
        success:function(data) {
            if(!data.result){
                if(data.msg=='同一业务同一手机号码1分钟内只能发一次短信验证码！'){
                    data.msg="短信发送过于频繁，请稍后再试";
                }
                showTipsError(data.msg);
                $("#sent-code").prop("disabled", false);
            } else {
            	time($("#sent-code"));
            }
            callback&&callback();
        },
        error : function(e) {
            // view("异常！");
            showTipsError("异常！");
            $("#sent-code").prop("disabled", true);
        }
    });

}

$(".btn").on("touchstart",function(){
    $(this).addClass(".active")
}).on("touchend",function(){
    $(this).removeClass(".active")
})

restoreSendState();

