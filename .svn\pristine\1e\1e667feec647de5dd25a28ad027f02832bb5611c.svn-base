package com.shunwang.basepassport.site;

import com.shunwang.baseStone.context.BaseStoneContext;
import com.shunwang.baseStone.siteinterface.context.SiteContext;

import com.shunwang.basepassport.config.dao.SiteInterfaceDao;
import com.shunwang.basepassport.config.pojo.SiteInterface;
import junit.framework.TestCase;

public class SiteContextTest extends TestCase {
	public void testGetSite(){
		BaseStoneContext.getInstance().setFileName("/basepassportContext.xml");
		SiteInterfaceDao dao = (SiteInterfaceDao)BaseStoneContext.getInstance().getBean("siteInterfaceDao");
		for(SiteInterface site:dao.findAll()){
			System.out.println(site.getServiceKey());
		}
		SiteContext.setSiteId("sw_pay");
		SiteContext.setSiteName("手机邮箱用户名唯一性验证");
		System.out.println(SiteContext.getSiteInterface());
		
	}
}
