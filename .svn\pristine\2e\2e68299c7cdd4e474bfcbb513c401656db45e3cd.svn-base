package com.shunwang.basepassport.user.service;

import com.shunwang.basepassport.config.dao.BarBlackCardDao;
import com.shunwang.basepassport.config.pojo.BarBlackCard;
import com.shunwang.basepassport.manager.request.dcapi.BlackCardRequest;
import com.shunwang.basepassport.manager.response.dcapi.BlackCardResponse;
import com.shunwang.basepassport.manager.service.dcapi.BlackCardServiceClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;
import java.util.concurrent.*;

public class BarBlackCardService {
    private static final Logger logger = LoggerFactory.getLogger(BarBlackCardService.class);

    BarBlackCardDao barBlackCardDao;

    private final Executor executor = Executors.newCachedThreadPool();
    /**
     * @param idCardNo 加密身份证
     */
    public boolean checkBlackCard(String idCardNo) {
        BarBlackCard barBlackCard = barBlackCardDao.findByIdCardNo(idCardNo);
        if (barBlackCard != null) {
            logger.warn("黑卡用户[{}]", idCardNo);
            return barBlackCard.getState().equals(2);
        }
        BlackCardRequest request = new BlackCardRequest();
        request.setIdCardNo(idCardNo);

        try {
            CompletableFuture<BlackCardResponse> responseFuture = CompletableFuture.supplyAsync(
                    () -> BlackCardServiceClient.execute(request), executor);
            BlackCardResponse response = responseFuture.get(200, TimeUnit.MILLISECONDS);
            if (response.isSuccess()) {
                if (response.isResult()) {
                    barBlackCard = new BarBlackCard();
                    barBlackCard.setIdCardNo(idCardNo);
                    barBlackCard.setState(1);
                    barBlackCard.setRemark("dc");
                    barBlackCard.setTimeAdd(new Date());
                    barBlackCard.setTimeEdit(new Date());
                    barBlackCardDao.save(barBlackCard);
                    logger.warn("黑卡用户入库[{}]", idCardNo);
                    return false;
                }
                return true;
            }
            //接口有异常时不允许登录
            return false;
        } catch (Exception e) {
            logger.error("黑卡获取结果失败[{}]", e.getMessage());
            return false;
        }
    }

    public BarBlackCardDao getBarBlackCardDao() {
        return barBlackCardDao;
    }

    public void setBarBlackCardDao(BarBlackCardDao barBlackCardDao) {
        this.barBlackCardDao = barBlackCardDao;
    }
}
