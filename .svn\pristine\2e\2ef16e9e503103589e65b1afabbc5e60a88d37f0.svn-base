<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <bean id="bankDao" class="com.shunwang.baseStone.config.dao.BankDao">
        <property name="sqlMapClientTemplate" ref="sqlMapClientTemplate"/>
    </bean>

    <bean id="cityDao" class="com.shunwang.basepassport.config.dao.CityDao">
        <property name="sqlMapClientTemplate" ref="sqlMapClientTemplate"/>
    </bean>

    <bean id="weixinTokenInfoDao" class="com.shunwang.basepassport.weixin.dao.WeixinTokenInfoDao">
        <property name="sqlMapClientTemplate" ref="sqlMapClientTemplate"/>
    </bean>

    <bean id="weixinOauthTokenDao" class="com.shunwang.basepassport.weixin.dao.WeixinOauthTokenDao">
        <property name="sqlMapClientTemplate" ref="sqlMapClientTemplate"/>
    </bean>

    <bean id="weixinOauthDao" class="com.shunwang.basepassport.weixin.dao.WeixinOauthDao">
        <property name="sqlMapClientTemplate" ref="sqlMapClientTemplate"/>
    </bean>

    <bean id="weixinOpenIdUnionIdDao" class="com.shunwang.basepassport.weixin.dao.WeixinOpenIdUnionIdDao">
        <property name="sqlMapClientTemplate" ref="sqlMapClientTemplate"/>
    </bean>

    <bean id="weixinOauthTokenService" class="com.shunwang.basepassport.weixin.service.WeixinOauthTokenService">
        <property name="weixinOauthTokenDao" ref="weixinOauthTokenDao"/>
        <property name="transactionTemplate" ref="basepassport.transactionTemplate"/>
        <property name="messageProducer" ref="cacheMessageProducer"/>
    </bean>

    <bean id="weixinOauthService" class="com.shunwang.basepassport.weixin.service.WeixinOauthService">
        <property name="weixinOauthDao" ref="weixinOauthDao"/>
    </bean>

    <bean id="weixinMsgService" class="com.shunwang.basepassport.weixin.service.WeixinMsgService">
        <property name="weixinOauthTokenService" ref="weixinOauthTokenService"/>
        <property name="appidReplyDao" ref="appidReplyDao"/>
        <property name="weixinOpenIdUnionIdService" ref="weixinOpenIdUnionIdService"/>
        <property name="memberAccountBindDao" ref="memberAccountBindDao"/>
        <property name="memberOutSiteDao" ref="memberOutSiteDao"/>
        <property name="wxTemplateMsgDao" ref="wxTemplateMsgDao"/>
        <property name="memberMultipleAccountBindDao" ref="memberMultipleAccountBindDao"/>
        <property name="weixinOauthService" ref="weixinOauthService"/>
    </bean>

    <bean id="weixinOpenIdUnionIdService" class="com.shunwang.basepassport.weixin.service.WeixinOpenIdUnionIdService">
        <property name="weixinOpenIdUnionIdDao" ref="weixinOpenIdUnionIdDao"/>
    </bean>

    <bean id="areaDao" class="com.shunwang.basepassport.config.dao.AreaDao">
        <property name="sqlMapClientTemplate" ref="sqlMapClientTemplate"/>
    </bean>

    <bean id="configResourcesDao" class="com.shunwang.basepassport.config.dao.ConfigResourcesDao">
        <property name="sqlMapClientTemplate" ref="sqlMapClientTemplate"/>
    </bean>
    <bean id="configInterfaceDao" class="com.shunwang.basepassport.config.dao.ConfigInterfaceDao">
        <property name="sqlMapClient" ref="sqlMapClient"/>
    </bean>

    <bean id="configOneLoginDao" class="com.shunwang.basepassport.config.dao.ConfigOneLoginDao">
        <property name="sqlMapClient" ref="sqlMapClient"/>
    </bean>
    <bean id="smsConfigDao" class="com.shunwang.basepassport.config.dao.SmsConfigDao">
        <property name="sqlMapClient" ref="sqlMapClient"/>
    </bean>
    <bean id="emailConfigDao" class="com.shunwang.basepassport.config.dao.EmailConfigDao">
        <property name="sqlMapClient" ref="sqlMapClient"/>
    </bean>
    <bean id="weakPasswordDao" class="com.shunwang.basepassport.config.dao.WeakPasswordDao">
        <property name="sqlMapClient" ref="sqlMapClient"/>
    </bean>
    <bean id="areaAppidDao" class="com.shunwang.basepassport.config.dao.AreaAppidDao">
        <property name="sqlMapClient" ref="sqlMapClient"/>
    </bean>
    <bean id="configWxBindAdDao" class="com.shunwang.basepassport.config.dao.ConfigWxBindAdDao">
        <property name="sqlMapClientTemplate" ref="sqlMapClientTemplate"/>
    </bean>
    <bean id="appidReplyDao" class="com.shunwang.basepassport.config.dao.AppidReplyDao">
        <property name="sqlMapClient" ref="sqlMapClient"/>
    </bean>
    <bean id="barBlackCardDao" class="com.shunwang.basepassport.config.dao.BarBlackCardDao">
        <property name="sqlMapClient" ref="sqlMapClient"/>
    </bean>
    <bean id="barBlackCardService" class="com.shunwang.basepassport.user.service.BarBlackCardService">
        <property name="barBlackCardDao" ref="barBlackCardDao"/>
    </bean>

    <!-- base库迁移 -->
    <bean id="richTextDao" class="com.shunwang.basepassport.config.dao.RichTextDao">
        <property name="sqlMapClient" ref="sqlMapClient"/>
    </bean>
    <bean id="serviceDao" class="com.shunwang.basepassport.config.dao.ServiceDao">
        <property name="sqlMapClient" ref="sqlMapClient"/>
    </bean>
    <bean id="cssDao" class="com.shunwang.basepassport.config.dao.CssDao">
        <property name="sqlMapClient" ref="sqlMapClient"/>
    </bean>
    <bean id="oauthDao" class="com.shunwang.basepassport.config.dao.OauthDao">
        <property name="sqlMapClient" ref="sqlMapClient"/>
    </bean>
    <bean id="userOutInterfaceDao" class="com.shunwang.basepassport.config.dao.UserOutInterfaceDao">
        <property name="sqlMapClient" ref="sqlMapClient"/>
    </bean>
    <bean id="siteInterfaceDao" class="com.shunwang.basepassport.config.dao.SiteInterfaceDao">
        <property name="sqlMapClient" ref="sqlMapClient"/>
    </bean>

</beans>


	
	
