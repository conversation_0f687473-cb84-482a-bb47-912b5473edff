package com.shunwang.baseStone.sso.apapter;

import com.shunwang.baseStone.sso.constant.UserOutsiteConstant;
import com.shunwang.baseStone.sso.util.UploadFileUtil;
import com.shunwang.baseStone.useroutinterface.context.UseroutInterfaceContext;
import com.shunwang.baseStone.config.constants.ConfigOauthConstant;
import com.shunwang.basepassport.config.pojo.Oauth;
import com.shunwang.basepassport.user.common.MemberConstants;
import com.shunwang.basepassport.user.common.MemberUtil;
import com.shunwang.basepassport.user.common.UserLoginSessionUtil;
import com.shunwang.basepassport.user.pojo.Member;
import com.shunwang.basepassport.user.pojo.MemberAccountBind;
import com.shunwang.basepassport.user.pojo.MemberOutSite;
import com.shunwang.basepassport.weixin.constant.WeixinConstant;
import com.shunwang.util.StringUtil;
import com.shunwang.util.net.HttpClientUtils;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

public class WeiboAuthAdapter extends UserOutsiteApapter {

    private static final long serialVersionUID = 1L;
    private static final Logger log = LoggerFactory.getLogger(WeiboAuthAdapter.class);

    public static final String WEIBO_OAUTH2_WEB_URL = "https://api.weibo.com/oauth2/authorize";
    public static final String WEIBO_ACCESS_TOKEN_URL = "https://api.weibo.com/oauth2/access_token";
    public static final String WEIBO_QUERY_USER_URL = "https://api.weibo.com/2/users/show.json";

    private String code;
    private String uid;
    private String accessToken;

    @Override
    public String getInterfaceId() {
        return UserOutsiteConstant.WEIBO_INTERFACE_ID;
    }

    @Override
    protected String getUserKeyPrefix() {
        return WeixinConstant.WEIBO_PRE;
    }

    @Override
    public Map<String, Object> getOauthByServer() throws Exception {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("oauthUrl", goToOauth());
        return resultMap;
    }

    /**
     * 第一步
     * 进入授权页面并获取到code
     */
    @Override
    public String goToOauth() throws Exception {
        Oauth configOauth = getOauthConfig() ;
        String userSceneKey = com.shunwang.util.StringUtil.isBlank(innerScene) ? buildCacheUserKey() : innerScene;
        cacheExtData(userSceneKey, 30 * 60);
        String redirectUri = getRedirectUrl(userSceneKey);

        return WEIBO_OAUTH2_WEB_URL + "?"
                + "client_id=" + configOauth.getAppId()
                + "&response_type=code"
                + "&redirect_uri=" + URLEncoder.encode(redirectUri, "UTF-8")
                + "&state=" + UUID.randomUUID();
    }

    @Override
    public String oauthCallback() {
        try {
            pullCacheExtData(userSceneKey);
            loadPrivacyCss();
            // 获取第三方配置
            Oauth configOauth = getOauthConfig() ;
            fetchUidAndAccessToken(configOauth);

            memberOutSite = buildMemberOutSite();
            member = queryMemberBy(memberOutSite.getOutMemberId());
            if (member == null) { // 外部用户不存在，为其生成
                member = outSiteMemberRegister(memberOutSite);
                //　此处保存头像
                member.setHeadImg(UploadFileUtil.saveNetHeadImgToPath(member.getMemberId(), headImage));
                interfaceService.updateMember(member);
            } else if (member.getMemberState() == MemberConstants.USER_CANCEL_STATE) {
                setMsg("您的账号已被注销！");
                memberName = member.getMemberName();
                return inputCancelView();
            } else {
                updateHeadAndNick();
            }
            member.setOpenId(uid);
            //处理登录来源
            //获取请求二维码的原始客户端ip到登录日志
            member.setClientIp(clientIp);
            //处理登录来源
            if (com.shunwang.util.StringUtil.isNotBlank(cacheResult) && cacheResult.equals("1")) {
                memberOutSite.setMemberFrom(UserOutsiteConstant.WEIBO_QR_INTERFACE_ID);
            }
            MemberUtil.doLogin(member, memberOutSite);
            memberName = member.getMemberName();
            UserLoginSessionUtil.saveSession(memberName, UserLoginSessionUtil.LoginType.SSO_OUT.getType() + getInterfaceId(), null, site_id);
            initCss(super.getSite_id());
            if (StringUtil.isNotBlank(visitType) && visitType.equals("bind")) {
                responseVisitType();
                return SUCCESS;
            }
            initLoginElement(super.getSite_id());
            if (isSingleAccount) {
                String toBind = goSingleBind(memberOutSite.getNickName(), memberOutSite.getHeadImg());
                if (!NO_RETURN.equalsIgnoreCase(toBind)) {
                    return toBind;
                }
                UserLoginSessionUtil.saveMasterSession(memberName, member.getMemberName(), UserLoginSessionUtil.LoginType.SSO_OUT.getType() + getInterfaceId(), null, site_id);
            }

            login(member);
            return WeixinConstant.MULTI_TERMINAL_LOGIN_JUMP.equals(cacheResult) ? qrSuccess() : SUCCESS;
        } catch (Exception e) {
            log.error("获取微博用户信息过程出错:", e);
            try {
                getResponse().sendRedirect(callbackUrl);
                return null;
            } catch (IOException e1) {
                log.error("跳转[{}]异常", callbackUrl, e1);
            }
        }
        return INPUT;
    }

	@Override
	protected MemberAccountBind getByMemberId() {
		return memberAccountBindDao.getByWeibo(member.getMemberId());
	}

	@Override
	protected Integer getOutOauthType() {
		return ConfigOauthConstant.TYPE.WEIBO.getInt();
	}

	@Override
	protected String getOutOauthLogName() {
		return "微博";
	}

	@Override
	protected MemberAccountBind buildMemberAccountBind() {
		MemberAccountBind memberAccountBind = super.buildMemberAccountBind();
        memberAccountBind.beginBuildLog("WEIBO帐号绑定");
		memberAccountBind.setWeibo(member.getMemberId());
		return memberAccountBind;
	}

    /**
     * 解析获取到的用户信息
     * @return 第三方账号
     */
    private MemberOutSite buildMemberOutSite() throws Exception {
        String result = getWeiBoUserInfo();
        JSONObject obj = new JSONObject(result);
        nickName = obj.getString("screen_name");
        headImage = obj.getString("profile_image_url");
        MemberOutSite memberOutSite = new MemberOutSite();
        memberOutSite.setOutMemberId(uid);
        memberOutSite.setMemberName("wb_" + uid);
        memberOutSite.setOutMemberName(uid);
        memberOutSite.setNickName(StringUtils.isBlank(nickName)?"请自定义昵称":nickName);
        memberOutSite.setRegFrom(site_id);
        memberOutSite.setVersion(version);
        memberOutSite.setEnv(env);
        memberOutSite.setRemark(extData);
        memberOutSite.setMemberFrom(UserOutsiteConstant.WEIBO_INTERFACE_ID);
        if (StringUtil.isNotBlank(headImage)) {
            memberOutSite.setHeadImg(headImage);
        }

        return memberOutSite;
    }

    /**
     * 第二步
     * 根据授权返回的code获取uid和access_token
     *
     */
    private void fetchUidAndAccessToken(Oauth configOauth) throws Exception {
        String getAccessTokenUrl = WEIBO_ACCESS_TOKEN_URL + "?" +
                "client_id=" + configOauth.getAppId() + "&" +
                "client_secret=" + configOauth.getAppSecret() + "&" +
                "grant_type=" + "authorization_code" + "&" +
                "code=" + code + "&" +
                "redirect_uri=" + URLEncoder.encode(getRedirectUrl(userSceneKey), "UTF-8");
        String result = HttpClientUtils.doPost(getAccessTokenUrl, new HashMap<>());
        log.info("微博获取accessToken返回数据:" + result + ",code," + code);
        if (StringUtil.isBlank(result)) {
            throw new IOException("请求微博获取用户信息网络异常");
        }
        JSONObject obj = new JSONObject(result);
        uid = obj.getString("uid");
        accessToken = obj.getString("access_token");
    }

    /**
     * 第三步
     * 根据获取到的uid和access_token获取用户信息
     *
     * @return 获取用户信息接口返回结果
     */
    private String getWeiBoUserInfo() {
        String getUserInfoUrl = WEIBO_QUERY_USER_URL + "?" +
                "access_token=" + accessToken + "&" +
                "uid=" + uid;
        String result = HttpClientUtils.doGet(getUserInfoUrl, null);
        log.info("获取微博用户信息返回数据:" + result + ",accessToken," + accessToken + ",uid" + uid);
        return result;
    }

    private void updateHeadAndNick() {
        Member paramMember = null;
        if (StringUtil.isBlank(member.getHeadImg())) {
            paramMember = new Member();
            paramMember.setMemberId(member.getMemberId());
            member.setHeadImg(UploadFileUtil.saveNetHeadImgToPath(member.getMemberId(), headImage));
            paramMember.setHeadImg(member.getHeadImg());
        }
        if (!StringUtil.isBlank(nickName) && !nickName.equals(member.getNickName())) {
            if (null == paramMember) {
                paramMember = new Member();
                paramMember.setMemberId(member.getMemberId());
            }
            paramMember.setMemberName(member.getMemberName());
            paramMember.setNickName(nickName);
        }

        if (paramMember != null) {
            interfaceService.updateMember(paramMember);
        }
    }
	
	private String getRedirectUrl(String userSceneKey) {
		return getWeiboWebCallbackUrl()
				+ "?site_id=" + site_id
                + "&userSceneKey=" + userSceneKey
                + "&cacheResult=" + (isMultiTerminalLoginJump() ? "1" : "0")
				+ "&callbackUrl=" + callbackUrl
		        + "&tgt=" + (tgt == null ? "" : tgt);
	}
	
	private String getWeiboWebCallbackUrl() {
		useroutInterface=UseroutInterfaceContext.getUseroutInterfaceById(getInterfaceId());
		log.info("微博登录方式从数据库中获取的回调地址是:" + useroutInterface.getCallbackURL());
		return useroutInterface.getCallbackURL();
	}
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }

}
