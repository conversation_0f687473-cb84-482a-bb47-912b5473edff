package com.shunwang.baseStone.sso.util;

import com.shunwang.baseStone.sso.pojo.ClientInfo;
import com.shunwang.basepassport.user.pojo.Member;
import org.apache.commons.codec.digest.DigestUtils;

public class ClientInfoBuilder {

    /**
     * *
     * 设置用户登录成功后信息（vip，积分）
     *
     * @param member
     * @param bFromCallback
     * @param ticket
     * @param token
     * @return
     */
    public static ClientInfo setSSoLoginSuccInfo(Member member, String bFromCallback, String ticket,String token,String md5key) {
        ClientInfo clientInfo = new ClientInfo();
        member.setMemberInfo(member.loadMemberInfo());

        clientInfo.setbFromCallback(bFromCallback);
        clientInfo.setTicket(ticket);
        clientInfo.setToken(token);
        clientInfo.setPassportID(member.getMemberId().toString());
        clientInfo.setNickName(member.getNickName());
        clientInfo.setStatus(member.getMemberState().toString());
        clientInfo.setUserName(member.getMemberName());
        clientInfo.setHeadImg(member.getHeadImg());
        clientInfo.setVipLevel("0");
        clientInfo.setVipName("0");
        clientInfo.setVipImg("");
        clientInfo.setCoin("0");
        StringBuffer sb = new StringBuffer();
        sb.append(member.getMemberId()).append("|")
                .append(md5key).append("|")
                //.append(member.getMemberName()).append("|").
                .append(clientInfo.getToken());
        clientInfo.setSign(DigestUtils.md5Hex(sb.toString()));
        return clientInfo;
    }

}
