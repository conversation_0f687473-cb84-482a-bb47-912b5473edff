package com.shunwang.basepassport.config.pojo;

import com.shunwang.baseStone.core.pojo.BaseStoneObject;

import java.io.Serializable;

/**
 * 省市区地区
 *
 * Copyright© 2005-2014 shunwang. All Rights Reserved.
 * author: <EMAIL>
 * date: 14-12-3
 * time: 下午5:26
 * since:
 */
public class Area extends BaseStoneObject {

    private static final long serialVersionUID = 3061394061411802775L;

    private Integer areaId;  //地区ID

    private String name;  //省市区名称

    private Integer parentId;  //父id

    private Integer sort;  //排序

    private String remark;  //备注

    private String hlbAreaId;

    private String hlbName;

    private String hlbParentId;
    //盛付通地区ID
    private String shengpayAreaId;
    //盛付通地区名
    private String shengpayName;
    //盛付通地区父级ID
    private String shengpayParentId;
    //随行付
    private String sxfAreaId;
    //随行付地区名
    private String sxfName;
    //随行付地区父级ID
    private String sxfParentId;

    public Serializable getPk() {
        return areaId;
    }

    public Integer getAreaId() {
        return areaId;
    }

    public void setAreaId(Integer areaId) {
        this.areaId = areaId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getParentId() {
        return parentId;
    }

    public void setParentId(Integer parentId) {
        this.parentId = parentId;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getHlbAreaId() {
        return hlbAreaId;
    }

    public void setHlbAreaId(String hlbAreaId) {
        this.hlbAreaId = hlbAreaId;
    }

    public String getHlbName() {
        return hlbName;
    }

    public void setHlbName(String hlbName) {
        this.hlbName = hlbName;
    }

    public String getHlbParentId() {
        return hlbParentId;
    }

    public void setHlbParentId(String hlbParentId) {
        this.hlbParentId = hlbParentId;
    }

    public String getShengpayAreaId() {
        return shengpayAreaId;
    }

    public void setShengpayAreaId(String shengpayAreaId) {
        this.shengpayAreaId = shengpayAreaId;
    }

    public String getShengpayName() {
        return shengpayName;
    }

    public void setShengpayName(String shengpayName) {
        this.shengpayName = shengpayName;
    }

    public String getShengpayParentId() {
        return shengpayParentId;
    }

    public void setShengpayParentId(String shengpayParentId) {
        this.shengpayParentId = shengpayParentId;
    }

    public String getSxfAreaId() {
        return sxfAreaId;
    }

    public void setSxfAreaId(String sxfAreaId) {
        this.sxfAreaId = sxfAreaId;
    }

    public String getSxfName() {
        return sxfName;
    }

    public void setSxfName(String sxfName) {
        this.sxfName = sxfName;
    }

    public String getSxfParentId() {
        return sxfParentId;
    }

    public void setSxfParentId(String sxfParentId) {
        this.sxfParentId = sxfParentId;
    }
}
