package com.shunwang.baseStone.sender.imp;

import java.util.concurrent.BlockingQueue;
import java.util.concurrent.Executor;

import javax.mail.internet.MimeMessage;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.mail.javamail.MimeMessageHelper;

import com.shunwang.baseStone.sender.ISender;
import com.shunwang.baseStone.sender.exp.SendExp;
import com.shunwang.baseStone.sender.pojo.SendMsg;



/**
 * ****************************
 * 版权所有：顺网科技 保留所有权利
 * 创建日期: Jul 22, 2011 8:44:14 PM
 * 创建作者：xiangjie
 * 文件名称：
 * 版本： 1.0
 * 功能：邮件发送
 * 最后修改时间：
 * 修改记录：
****************************************
 */
public class EmailSender implements ISender,Runnable{
	private final static Logger log = LoggerFactory.getLogger(EmailSender.class);
	private BlockingQueue<SendMsg> queue;
	private Executor executor;

	@Override
	public void doSend(SendMsg sendMsg) throws SendExp{
		queue.offer(sendMsg);//放入队列中
	}
	/**
	 * 从队列中取出sendMsg，进行发送
	 * @param sendMsg
	 */
	public void sendForQueue(SendMsg sendMsg){
		JavaMailBaseSender javaMailSender =  EmailSenderGenerator.buildJavaMailSender();
		MimeMessage mimeMsg = javaMailSender.createMimeMessage();
		try {
			if(sendMsg == null) {
				log.error("sendMsg is null");
				throw new SendExp();
			}
			MimeMessageHelper helper = new MimeMessageHelper(mimeMsg, true, "UTF-8");
			helper.setTo(sendMsg.getNumber());
			helper.setSubject(sendMsg.getTitle());
			helper.setFrom(javaMailSender.getFrom(),"顺网通行证");
			helper.setText(sendMsg.getContent(), true);
			javaMailSender.send(mimeMsg);
		} catch (Exception e) {
			log.error("sendNumber:["+sendMsg.getNumber()+"]," +
					"sendTitle:["+sendMsg.getTitle()+"]," +
					"sendText:["+sendMsg.getContent()+"],邮件发送异常",e);
			throw new SendExp();
		} 
	}

	/**
	 * 服务启动时开启线程
	 */
	public void init(){
		executor.execute(this);
	}
	
	public BlockingQueue<SendMsg> getQueue() {
		return queue;
	}
	public void setQueue(BlockingQueue<SendMsg> queue) {
		this.queue = queue;
	}

	public Executor getExecutor() {
		return executor;
	}

	public void setExecutor(Executor executor) {
		this.executor = executor;
	}

	@Override
	public void run() {
		while(true){
			try {
				SendMsg sendMsg = queue.take();//从队列中取出
				sendForQueue(sendMsg);
			} catch (Exception e) {
				log.error("邮件发送异常",e);
			}
		}
	}

}
