package com.shunwang.basepassport.filter;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.Iterator;
import java.util.Map;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.shunwang.util.net.IpUtil;
import org.slf4j.MDC;

/**
 * 接口日志记录器
 *
 * <AUTHOR>
 * @since 2011-8-4 上午09:41:24
 */
public class InterfaceLogFilter implements Filter {

	private final static Logger log = LoggerFactory.getLogger(InterfaceLogFilter.class);
	
	@Override
	public void destroy() {
	}

    @Override
    public void doFilter(ServletRequest arg0, ServletResponse arg1, FilterChain arg2)
            throws IOException, ServletException {
        HttpServletRequest req = (HttpServletRequest) arg0;
        HttpServletResponse res = (HttpServletResponse) arg1;
        setCharacterEncodingAndCache(req, res);
        createLog(req, res);
        arg2.doFilter(req, res);
    }

	/**
	 * 设置编码
     *
	 * @param req 请求对象
	 * @param res 响应对象
	 * @throws UnsupportedEncodingException
	 */
	private void setCharacterEncodingAndCache(HttpServletRequest req, HttpServletResponse res)
            throws UnsupportedEncodingException {
		req.setCharacterEncoding("UTF-8");
		res.setContentType("text/html;charset=UTF-8");
		res.setDateHeader("Expires", 0);   
		res.setHeader("Cache-Control", "no-cache");   
		res.setHeader("Pragma", "no-cache");   
	}

	/**
	 * 创建日志
     *
	 * @param req 请求对象
	 * @param res 响应对象
	 */
	@SuppressWarnings("unchecked")
	private void createLog(HttpServletRequest req, HttpServletResponse res) {
        StringBuilder bufMsg = new StringBuilder();
        bufMsg.append("{IP:").append(IpUtil.getIpAddress(req))
                .append(",").append("method:").append(req.getMethod())
                .append(",").append("queryStr:").append(req.getQueryString())
                .append(",params:{");
        Map paraMap = req.getParameterMap();
        Iterator it = paraMap.entrySet().iterator();
        while (it.hasNext()) {
            Map.Entry entry = (Map.Entry) it.next();
            String key = (String) entry.getKey();
            String[] values = (String[]) entry.getValue();
            String value = "[";
            for (String v : values) {
                value += v + ",";
            }
            value = (value.length() > 1 ? value.substring(0, value.length() - 1) : value) + "]";
            bufMsg.append(key).append(":").append(value).append(",");
        }
		MDC.put("service", req.getRequestURI());
		if (paraMap.get("accessSiteId") != null) {
			MDC.put("appId",((String[])paraMap.get("accessSiteId"))[0]);
		} else if (paraMap.get("site_id") != null) {
			MDC.put("appId",((String[])paraMap.get("site_id"))[0]);
		} else if (paraMap.get("siteId") != null) {
			MDC.put("appId",((String[])paraMap.get("siteId"))[0]);
		}
        String result = (paraMap.size() > 0 ? bufMsg.substring(0, bufMsg.length() - 1) : bufMsg) + "}}";
        log.info(result);
		MDC.remove("service");
		MDC.remove("appId");
	}

	@Override
	public void init(FilterConfig arg0) throws ServletException {
	}

}
