.avatar-box {
  padding: 15px 30px;
  border-left: 1px solid #D1D0CC;
  background-color: #F5F5F5;
}
.avatar-editor {
  margin-bottom: 130px;
}
.avatar-editor .hd {
  margin-bottom: 20px;
}
.avatar-editor .tip {
  font-size: 14px;
}
.avatar-editor .avatar {
  border: 1px solid #d1d0cc;
  background-color: #eae9e5;
}
.avatar-editor .native {
  width: 450px;
  height: 450px;
}
.avatar-editor .middle,
.avatar-editor .small {
  padding: 2px;
}
.avatar-editor .middle,
.avatar-editor .middle .preview {
  width: 120px;
  height: 120px;
}
.avatar-editor .small,
.avatar-editor .small .preview {
  width: 56px;
  height: 56px;
}
.avatar-editor .editor {
  width: 452px;
  position: relative;
  float: left;
}
.avatar-editor .result {
  float: left;
  margin-left: 26px;
  color: #9f9f9f;
}
.avatar-editor #upload-form {
  width: 238px;
  height: 72px;
  position: absolute;
  top: 50%;
  left: 50%;
  margin-top: -36px;
  margin-left: -119px;
  z-index: 22;
}
.avatar-editor iframe {
  display: none;
}
.avatar-editor .editor .native img {
  position: absolute;
  top: 50%;
  left: 50%;
  margin-top: -150px;
  margin-left: -150px;  
}
.avatar-editor .upload-icon,
.avatar-editor .upload-input {
  display: block;
  position: absolute;
  width: 238px;
  height: 72px;
  overflow: hidden;
  z-index: 2;
  font-size: 46px;
  cursor: pointer;
  zoom: 1;
}
.avatar-editor .upload-input {
  opacity: 0;
  filter: alpha(opacity=0);
  cursor:pointer;
}

.avatar-editor .upload-icon {
  overflow: hidden;
  font-size: 0;
  line-height: 99em;
  background: url(../images/front/upload-btn.gif) no-repeat 0 0;
  z-index: 1;
}
.avatar-editor .editor .act {
  text-align: center;
  margin-top: -40px;
}
.avatar-editor .editor .act a {
  color: #0000ff;
}
.avatar-editor .result .avatar {
  margin-top: 28px;
  position: relative;
}
.avatar-editor .result .avatar .preview {
  overflow: hidden;
}
.avatar-editor .result .avatar span {
  position: absolute;
  top: 50%;
  left: 0;
  margin-top: -12px;
}
.avatar-editor .result .middle span {
  left: 140px;
}
.avatar-editor .result .small span {
  left: 76px;
}
.avatar-editor .result .btn {
  margin-top: 76px;
  margin-left: 45px;
  padding-bottom:10px;
}
.avatar-editor .result .btn button {
  font-size: 20px;
  color: #fff;
  border: none;
  width: 150px;
  height: 42px;
  background-color: #f8742c;
  cursor: pointer;
}
#photo {
  border: none;
}