package com.shunwang.baseStone.sso.apapter;

import com.shunwang.baseStone.sso.constant.UserOutsiteConstant;
import com.shunwang.baseStone.sso.pojo.QrCodeResponse;
import com.shunwang.basepassport.user.pojo.MemberAccountBind;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * APP扫码登录
 */
public class AppAdapter extends UserOutsiteApapter {
    private static final Logger log = LoggerFactory.getLogger(AppAdapter.class);

    private String scene;

    @Override
    public String getInterfaceId() {
        return UserOutsiteConstant.APP_INTERFACE_ID;
    }

    @Override
    public String goToOauth() {
        throw new UnsupportedOperationException("不支持的方法");
    }

    /**
     * 生成 scene
     * 保存 参数 目标站点Id、上报数据、extData等数据
     * 返回 页面 expireSeconds、qrcodeUrl、key(即scene)
     *
     * @return 页面构建二维码数据
     */
    @Override
    public Map<String, Object> getOauth() {
        scene = buildCacheUserKey();
        log.info("app扫码生成siteId[{}],scene[{}]", site_id, scene);

        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("expireSeconds", cacheTimeoutSeconds());
        resultMap.put("qrcodeUrl", scene);
        resultMap.put("key", scene);

        setCacheData(cacheTimeoutSeconds());
        updateResult(new QrCodeResponse(QrCodeResponse.TypeStage.INIT));
        return resultMap;
    }

    @Override
    public String buildCacheUserKey() {
        return UUID.randomUUID().toString();
    }

    @Override
    public String getScene() {
        return scene;
    }

    @Override
    public String getType() {
        return "APP_SCAN_";
    }

    @Override
    public String oauthCallback() {
        throw new UnsupportedOperationException("无需回调");
    }

    @Override
    protected String getOutOauthLogName() {
        return "APP扫码";
    }

    @Override
    protected MemberAccountBind getByMemberId() {
        throw new UnsupportedOperationException("APP扫码不关联三账号表");
    }

    @Override
    protected Integer getOutOauthType() {
        throw new UnsupportedOperationException("APP扫码不是三方登录类型");
    }


}
