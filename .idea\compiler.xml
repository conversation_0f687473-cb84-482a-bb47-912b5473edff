<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="member-center-service" />
        <module name="member-center-base" />
        <module name="member-center-interface" />
        <module name="member-center-sso" />
      </profile>
    </annotationProcessing>
    <bytecodeTargetLevel>
      <module name="member-center-passport" target="1.8" />
    </bytecodeTargetLevel>
  </component>
  <component name="JavacSettings">
    <option name="ADDITIONAL_OPTIONS_OVERRIDE">
      <module name="member-center-passport" options="-bootclasspath $PROJECT_DIR$/../../java/jdk1.8/jre/lib/rt.jar:$PROJECT_DIR$/../../java/jdk1.8/jre/lib/jce.jar" />
    </option>
  </component>
</project>