String.prototype.getBytes = function() {    
    var cArr = this.match(/[^\x00-\xff]/ig);
    return this.length + (cArr == null ? 0 : cArr.length);
}

var appServer = "";

jQuery.fn.extend({
  setClass:function(val){
    return this.attr("class", "").addClass(val);
  },
  setHtml: function(val){
    var cl = this.attr("class");

    //position:absolute;top:-15px!important;top:-1px;
    // position:absolute;top:-15px!important;top:1px;
    
    var str = '<img src="' + appServer 
      + '/images/front/v2/tit2.gif" style="z-index:10; left:6px;top:-10px!important;top:4px;position:absolute"/>'
      + '<span style="border:1px solid #c6d7e0; width:256px; padding:3px; color:#999;font-size:12px;line-height:15px;'
      + ' position:absolute;top:-15px!important;top:1px; '
      +'  left:12px; background:#f5f5f5;">' 
      + val 
      + '</span>';

    str = (cl == "emInfo" ? str : val);
    return this.html(str);
  },
  setOk: function(){
    return this.attr("class", "").html("<img src='"+ appServer +"/images/front/v2/ok.gif'/>");
  }
});

var page = {
    emailOrMobileReg: "emailOrMobile",
    siteReg: "site",
    siteEmailOrMobileReg: this.emailOrMobileReg,
    regType: "emailOrMobile",   //emailOrMobile - 手机邮箱注册，site - 其他站点注册，siteEmailOrMobile - 其他站点需要手机邮箱注册
    personal:"1",
    company:"2",
    memberType:null, //personal-个人，company-企业
    memberName: null,
    memberNameEm:null,
    pwd1:null,
    pwd1Em:null,
    pwd2:null,
    pwd2Em:null,
    checkCode:null,
    checkCodeEm:null,
    
    emailOrMobile: null,
    emailOrMobileEm: null,

    realName:null,
    realNameEm:null,
    idCardNo:null,
    idCardNoEm:null,
    
    //-----------输入合法性标示
    memberNameFlag:false,
    pwd1Flag:false,
    pwd2Flag:false,
    checkCodeFlag:false,
    realNameFlag:false,
    idCardNoFlag:false,
    
    emailOrMobileFlag: false,
    companyNameFlag:false,
    linkManFlag:false,
    
    validateFlag:false,
    //------------显示提升信息
    memberNameInfo: function(){
        var memberName = page.memberName.val();
        if(memberName == ""){
            page.memberNameEm.setClass("emInfo").setHtml("4-16个字符，可用数字、中文、字母、下划线、\"#\"。");
        }
    },

    realNameInfo: function(){
        var realName = page.realName.val();
        if(realName == ""){
            page.realNameEm.setClass("emInfo").setHtml("真实姓名请使用中文，2-10个汉字。");
        }
    },

    idCardNoInfo: function(){
        var idCardNo = page.idCardNo.val();
        if(idCardNo == ""){
            page.idCardNoEm.setClass("emInfo").setHtml("输入18位身份证号码。");
        }
    },
    
    pwd1Info: function(){
        var pwd1 = page.pwd1.val();
        if(pwd1 == ""){
            page.pwd1Em.setClass("emInfo").setHtml("6-16个字符，可用数字，字母，下划线，#。");
        }
    },
    
    pwd2Info: function(){
        var pwd2 = page.pwd2.val();
        if(pwd2 == ""){
            page.pwd2Em.setClass("emInfo").setHtml("再输入一遍上面的密码。");
        }
    },
    
    emailOrMobileInfo: function(){
        var val = page.emailOrMobile.val();
        if(val == ""){
            page.emailOrMobileEm.setClass("emInfo").setHtml("输入您常用的邮箱或手机号码，方便找回密码。");
        }
    },
    linkManInfo: function(){
        var val = page.linkMan.val();
        if(val == ""){
            page.linkManEm.setClass("emInfo").setHtml("最多可输入16个字符。");
        }
    },
    companyNameInfo: function(){
        var val = page.companyName.val();
        if(val == ""){
            page.companyNameEm.setClass("emInfo").setHtml("最多可输入128个字符。");
        }
    },
    checkCodeInfo: function(){
        page.validateFlag = false;
        var checkCode = page.checkCode.val();
        if(checkCode == ""){
            page.checkCodeEm.setClass("emInfo").setHtml("请输入左侧的验证码。");
        }else{
            page.checkCodeEm.setClass("").setHtml("");
        }
    },
 
    
    //------检查输入
    
    checkMemberName: function(){
        page.memberNameFlag = false;
        var patn1 = /[\w_#\u4e00-\u9fa5]+/;
        var patn2 = /[\u4e00-\u9fa5]+/;
        var patn3 = /[\w_#]+/;
        var patn4 =/^[0-9]+$/;
        var memberName = page.memberName.val();
        if(memberName == ""){
            page.memberNameEm.setClass("aler2").setHtml("请输入账号。");
            return false;
        }
        if( patn4.test(memberName)){
            page.memberNameEm.setClass("aler2").setHtml("账号不能是纯数字。");
            return false;
        }
        if(memberName == memberName.match(patn2)){
            if(memberName.length > 8){
                page.memberNameEm.setClass("aler2").setHtml("纯中文账号不能超过8个汉字。");
                return false;
            }
            if(memberName.length < 2){
                page.memberNameEm.setClass("aler2").setHtml("纯中文账号不能少于2个汉字。");
                return false;
            }
        }
        if(memberName == memberName.match(patn3)){
            if(memberName.length > 16){
                page.memberNameEm.setClass("aler2").setHtml("长度不能超过16个字符，中文占两个字符。");
                return false;
            }
            if(memberName.length < 4){
                page.memberNameEm.setClass("aler2").setHtml("账号太短，请输入4-16个字符。");
                return false;
            }
        }
        if(memberName == memberName.match(patn1)){
            if(memberName.getBytes() > 16){
                page.memberNameEm.setClass("aler2").setHtml("长度不能超过16个字符，中文占两个字符。");
                return false;
            }
            if(memberName.getBytes() < 4){
                page.memberNameEm.setClass("aler2").setHtml("账号太短，请输入4-16个字符。");
                return false;
            }
        }
        if(memberName != memberName.match(patn1)){
            page.memberNameEm.setClass("aler2").setHtml("请使用数字、中文、字母、下划线、#。");
            return false;
        }
        
      /*  if(isSensitive(memberName)){
            page.memberNameEm.setClass("aler2").setHtml("用户名不能包含禁用词语。");
            return false;
        }
        */
        //用户名是否重复
        $.post("/front/noLogin/chkUserName.htm", 
               {"member.memberName" : memberName},
               function(json){
                   if(json.flag==1){
                       page.memberNameEm.setClass("aler2").setHtml("用户名不能包含禁用词语。");
                   }else if(json.flag==2){
                       page.memberNameEm.setClass("aler2").setHtml("该会员名已被注册，请重新输入其他会员名。");
                   }else{
                       page.memberNameFlag = true;
                       page.memberNameEm.setOk();
                   }
               },
               "json");
        
        return page.memberNameFlag;
    },
    checkPwd1: function(){
        page.pwd1Flag = false;
        var pwd1 = page.pwd1.val();
        if(pwd1 == ""){
            page.pwd1Em.setClass("aler2").setHtml("请输入密码。");
            return false;
        }
        if(pwd1.length>16){
            page.pwd1Em.setClass("aler2").setHtml("长度不能超过16个字符。");
            return false;
        }
        if(pwd1.length<6){
            page.pwd1Em.setClass("aler2").setHtml("密码太短，请输入6-16个字符。");
            return false;
        }
        var patn_0 = /^[\w_#]+$/;
        if(!patn_0.test(pwd1)){
            page.pwd1Em.setClass("aler2").setHtml("请使用数字、字母和标点符号。");
            return false;
        }
//      var patn_1 = /^[0-9]+$/;
//      if(patn_1.test(pwd1)){
//          page.pwd1Em.setClass("aler2").setHtml("不能使用纯数字。");
//          return false;
//      }
        if(!checkPwd(pwd1)){
            page.pwd1Em.setClass("aler2").setHtml("不能全为同一字符。");
            return false;
        }
//      if(! swPwd(pwd1)){
//          page.pwd1Em.setClass("aler2").setHtml("密码须数字、字母、#或_的组合，长度为6-16位。");
//          return false;
//      }
        page.pwd1Flag = true;
        page.pwd1Em.setOk();
        return true;
    },
    
    checkPwd2: function(){
        page.pwd2Flag = false;
        var pwd2 = page.pwd2.val();
        if(pwd2 == ""){
            page.pwd2Em.setClass("aler2").setHtml("请输入确认密码。");
            return false;
        }
//      if(!checkPwd(pwd2)){
//          page.pwd2Em.setClass("aler2").setHtml("6-16字符，允许字母数字#下划线，不能使用同一或连续字母，数字，符号。");
//          return false;
//      }
//      if(! swPwd(pwd2)){
//          page.pwd2Em.setClass("aler2").setHtml("密码须数字、字母、#或_的组合，长度为6-16位。");
//          return false;
//      }
        if(page.pwd1.val() != pwd2){
            page.pwd2Em.setClass("aler2").setHtml("两次输入的密码不一致。");
            return false;
        }
        page.pwd2Flag = true;
        page.pwd2Em.setOk();
        return true;
    },
    
    checkCheckCode: function(){
        var checkCode = page.checkCode.val();
        if(checkCode == ""){
            page.checkCodeEm.setClass("aler2").setHtml("请输入验证码。");
            page.checkCodeFlag = false;
            return false;
        }
        if(checkCode.match(/[\w]{4}/) != checkCode){
            page.checkCodeEm.setClass("aler2").setHtml("验证码格式不正确。");
            page.checkCodeFlag = false;
            return false;
        }
        if(!page.validateFlag){
        //验证码是否正确
        $.post("/front/noLogin/chkCode.htm", 
               {"checkCode" : checkCode},
               function(json){
                   if(json.flag){
                       page.checkCodeFlag = true;
                       page.checkCodeEm.setOk();
                   }else{
                       page.checkCodeEm.setClass("aler2").setHtml("验证码错误，请重新输入");
                       page.checkCodeFlag = false;
                   }
               },
               "json");
        }
        return page.checkCodeFlag;
    },
    
    keyUpCheckCode:function(){
        page.checkCodeFlag = false;
        var checkCode = page.checkCode.val();
        //验证码是否正确
        if(checkCode.length == 4){
        $.post("/front/noLogin/chkCode.htm", 
               {"checkCode" : checkCode},
               function(json){
                   if(json.flag){
                       page.checkCodeFlag = true;
                       page.checkCodeEm.setOk();
                       page.validateFlag = true;
                   }else{
                       page.checkCodeEm.setClass("aler2").setHtml("验证码错误，请重新输入");
                       page.checkCodeFlag = false;
                   }
               },
               "json");
        }
        return page.memberNameFlag;
    },
    
    
    checkEmailOrMobile: function(){
        page.emailOrMobileFlag = false;
        var val = page.emailOrMobile.val();
        if(val == ""){
            page.emailOrMobileEm.setClass("aler2").setHtml("输入您常用的邮箱或手机号码。");
            return false;
        }
        
        if(/@/.test(val) && ! isEmail(val)){
            page.emailOrMobileEm.setClass("aler2").setHtml("邮箱格式不正确，请重输。如**************");
            return false;
        }
        
        if(/^1[\d]+$/.test(val) && ! isMobileNo(val)){
            page.emailOrMobileEm.setClass("aler2").setHtml("手机格式不正确，请重输11位手机号码。");
            return false;
        }
        
        if(!isEmail(val) && ! isMobileNo(val)){
            page.emailOrMobileEm.setClass("aler2").setHtml("邮箱或手机号码格式不正确。");
            return false;
        }
        
        //检查邮箱是否被绑定
        if(isEmail(val)){
            $.post("/front/noLogin/chkEmail.htm", 
               {"member.email" : val},
               function(json){
                   if(json.flag){
                       page.emailOrMobileEm.setClass("aler2").setHtml("该邮箱已经绑定过5个通行证账号。");
                   }else{
                       page.emailOrMobileFlag = true;
                       page.emailOrMobileEm.setOk();
                   }
               },
               "json");
        }
        
        //检查手机号是否被绑定
        if(isMobileNo(val)){
            $.post("/front/noLogin/chkMobile.htm", 
               {"member.mobile" : val},
               function(json){
                   if(json.flag){
                       page.emailOrMobileEm.setClass("aler2").setHtml("绑定失败，该手机号已达到绑定上限");
                   }else{
                       page.emailOrMobileFlag = true;
                       page.emailOrMobileEm.setOk();
                   }
               },
               "json");
        }
        
        return page.emailOrMobileFlag;
    },
    
    checkCompanyName: function(){
        page.companyNameFlag = false;
        var val = page.companyName.val();
        if(val == ""){
            page.companyNameEm.setClass("aler2").setHtml("请填写公司名称。");
            return false;
        }
        
        if( val.length>128){
            page.companyNameEm.setClass("aler2").setHtml("公司名称最多可输入128个字符。");
            return false;
        }
       
        page.companyNameFlag = true;
        page.companyNameEm.setOk();
        return true;
    },
    
    checkLinkMan:function(){
        page.linkManFlag=false;
        var val = page.linkMan.val();
        if(val == ""){
            page.linkManEm.setClass("aler2").setHtml("请填写联系人。");
            return false;
        }
        
        if( val.length>16){
            page.linkManEm.setClass("aler2").setHtml("联系人最多可输入16个字符。");
            return false;
        }
       
        page.linkManFlag = true;
        page.linkManEm.setOk();
        return true;
    },

    checkRealName:function(){
        page.realNameFlag = false;
        var val = page.realName.val();
        if(val == ""){
            page.realNameEm.setClass("aler2").setHtml("请填写真实姓名。");
            return false;
        }

        if(!validateRealName(val)) {
            page.realNameEm.setClass("aler2").setHtml("真实姓名请使用中文，2-10个汉字。");
            return false;
        }
                  
        page.realNameFlag = true;
        page.realNameEm.setOk();
        return true;
    },

    checkIdCardNo:function(){
        page.idCardNoFlag=false;
        var val = page.idCardNo.val();
        if(val == ""){
            page.idCardNoEm.setClass("aler2").setHtml("请填写身份证号。");
            return false;
        }
        
        if( val.length>18){
            page.idCardNoEm.setClass("aler2").setHtml("身份证号最多可输入18个字符。");
            return false;
        }

        //最后的get()调用是为了得到dom对象
        var result = validateIdCardAge(page.idCardNo[0]);
        if(result==1){
          page.idCardNoEm.setClass("aler2").setHtml("无效身份证号码,请输入18位有效身份证号。");
          return false;
        }
        else if(result==3){
          page.idCardNoEm.setClass("aler2").setHtml("非法生日。");
          return false;
        }
        else if(result==4){
          page.idCardNoEm.setClass("aler2").setHtml("注册用户须年满18周岁。");
          return false;
        } 
        //业务需求变更,不再做身份证唯一性验证
        //ajaxCheckIdCardNo(val);
       
        page.idCardNoFlag = true;
        page.idCardNoEm.setOk();
        return true;
    }
}


function ajaxCheckIdCardNo(oldValue) {
  $.post("/front/noLogin/chkIdCardNo.htm", 
   {"idCardNo" : oldValue},
   function(json){
       if(json.flag){
           page.idCardNoEm.setClass("aler2").setHtml("该身份证号已被其他会员绑定，请换一个。");
       }else{
           //如果验证成功,比较下验证值和文本框最新的值,如果已经变动,需要重新验证
           newValue = page.idCardNo.val();
           if (oldValue != newValue) {
             page.checkIdCardNo();
             return;
           }
           page.idCardNoFlag = true;
           page.idCardNoEm.setOk();
       }
   }, "json");
}

//初始化
function init(){
    page.memberName = $("#memberName");
    page.memberNameEm = $("#memberNameEm");

    page.realName = $("#realName");
    page.realNameEm = $("#realNameEm");

    page.idCardNo = $("#idCardNo");
    page.idCardNoEm = $("#idCardNoEm");

    page.memberName = $("#memberName");
    page.memberNameEm = $("#memberNameEm");

    page.pwd1 = $("#pwd1");
    page.pwd1Em = $("#pwd1Em");
    page.pwd2 = $("#pwd2");
    page.pwd2Em = $("#pwd2Em");
    page.checkCode = $("#checkCode");
    page.checkCodeEm = $("#checkCodeEm");
    page.emailOrMobile = $("#emailOrMobile");
    page.emailOrMobileEm = $("#emailOrMobileEm");
    page.memberType=$("#memberType").val();;
    page.companyName=$("#companyName");
    page.companyNameEm=$("#companyNameEm");
    page.linkMan=$("#linkMan");
    page.linkManEm=$("#linkManEm");
    
    page.memberName.focus(page.memberNameInfo).blur(page.checkMemberName);

    page.realName.focus(page.realNameInfo).blur(page.checkRealName);
    page.idCardNo.focus(page.idCardNoInfo).blur(page.checkIdCardNo);

    page.pwd1.focus(page.pwd1Info).blur(page.checkPwd1).keyup(function(){pwdStrong(page.pwd1.val());});
    page.pwd2.focus(page.pwd2Info).blur(page.checkPwd2);
    
    page.emailOrMobile.focus(page.emailOrMobileInfo).blur(page.checkEmailOrMobile);
    page.checkCode.focus(page.checkCodeInfo).blur(function(){page.checkCheckCode();}).keyup(function(){page.keyUpCheckCode();});
    
    page.companyName.focus(page.companyNameInfo).blur(page.checkCompanyName);
    page.linkMan.focus(page.linkManInfo).blur(page.checkLinkMan);
    if(page.emailOrMobile.val() != "") {
        page.emailOrMobile.focus();
        page.emailOrMobile.blur();
    }
    if(page.companyName.val() != ""){
        page.companyName.focus();
        page.companyName.blur();
    }
    if(page.linkMan.val() != ""){
        page.linkMan.focus();
        page.linkMan.blur();
    }
    
    if(page.memberType==page.company){
        showCompany();
    }
    
    page.memberName.focus();
    page.regType = $("#returnCode").val();
    
    if($("#site_id").val() != ""&&$("#site_id").val()!='Passport'){
        $("#returnCode").val(page.siteReg);
    }
    
    showRegType();
}

function doSubmit(){
    $("#buttonCan").attr("disabled",true);
    if(! canSubmit()){
        $("#buttonCan").attr("disabled",false);
        return false;
    }
    
    var pwd = hex_md5(page.pwd1.val());
    $("#memberPwd").val(pwd);
    
    if(page.regType == page.siteReg){
        //其他站点注册
        $("#nextCode").val(page.siteReg);
        $("#returnCode").val(page.siteReg);
    }
    if($("#site_id").val() != ""&&$("#site_id").val()!='Passport'){
            $("#nextCode").val(page.siteReg);
        }
    
    if(page.regType == page.emailOrMobileReg || page.regType == page.siteEmailOrMobileReg ||page.memberType==page.company){
        var str = page.emailOrMobile.val();
        //邮箱注册
        if(isEmail(str)){
            $("#email").val(str);
            $("#nextCode").val("email");
            $("#returnCode").val(page.emailOrMobileReg);
        }
        //手机
        if(isMobileNo(str)){
            $("#mobile").val(str);
            $("#nextCode").val("mobile");
            $("#returnCode").val(page.emailOrMobileReg);
        }
    }
    page.pwd1.val("");
    page.pwd2.val("");
    document.forms[0].submit();
}

//是否可以提交
function canSubmit(){
    $("#showInfo").html("");
    page.checkPwd1();
    page.checkPwd2();
    //page.checkCheckCode()
    var flag = page.pwd1Flag && page.pwd2Flag && page.memberNameFlag && page.checkCodeFlag;
    if(page.memberType==page.company){
        page.checkLinkMan();
        page.checkCompanyName();
        if(!page.emailOrMobileFlag)
          page.checkEmailOrMobile();
        return flag && page.linkManFlag && page.companyNameFlag && page.emailOrMobileFlag;
    }
    if(page.regType == page.siteReg){
        return flag;
    }

    if(page.regType == page.emailOrMobileReg || page.regType == page.siteEmailOrMobileReg){
        if(!page.emailOrMobileFlag) {
          page.checkEmailOrMobile();
        }
        if(!page.realNameFlag) {
          page.checkRealName();
        }
        if(!page.idCardNoFlag) {
          page.checkIdCardNo();
        }
        return flag && page.emailOrMobileFlag && page.realNameFlag && page.idCardNoFlag;
    }

    return false;
}

//切换不同的注册方式
function toggleReg(){
    page.regType = (page.regType == "" ? page.quickReg : page.regType);
    page.regType = (page.regType == page.quickReg ? page.emailOrMobileReg : page.quickReg);
    showRegType();
}
//
//判断显示哪种注册方式
function showRegType(){
    page.memberName.focus();

    if(page.memberType==page.company){
        $("div.personalRegDiv").hide();
    } else {
        $("div.personalRegDiv").show();
    }

    if(page.regType == page.quickReg){
        $("#regBut").html("手机或邮箱注册");
        $("#regH").html("个人用户注册 - 快速注册");
        $("div.regByOther").hide();
    }
    if(page.regType == page.emailOrMobileReg){
        $("#regBut").html("快速注册");
        $("#regH").html("个人用户注册 - 手机或邮箱注册");
        $("div.regByOther").show();
    }
    if(page.regType == page.siteReg){
        $("#regH3").html("个人用户注册");
        $("div.regByOther").remove();
    }
    if(page.regType == page.siteEmailOrMobileReg){
        $("#regH3").html("个人用户注册");
        $("div.regByOther").show();
    }
}
//显示企业注册方式
function showCompany(){
    $("#regH3").html("企业用户注册");
    $("div.regByOther").show();
    $("div.regByCompany").show();
}
