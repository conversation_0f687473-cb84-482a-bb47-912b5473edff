package com.shunwang.basepassport.binder.web.bind.processor;

import com.shunwang.baseStone.cache.lock.CounterLock;
import com.shunwang.baseStone.cache.lock.TimedLock;
import com.shunwang.baseStone.cache.service.CacheService;
import com.shunwang.baseStone.context.BaseStoneContext;
import com.shunwang.baseStone.core.exception.BaseStoneException;
import com.shunwang.basepassport.binder.common.BinderConstants;
import com.shunwang.basepassport.binder.common.ErrorCode;
import com.shunwang.basepassport.binder.pojo.SendBinder;
import com.shunwang.basepassport.binder.processor.Processor;
import com.shunwang.basepassport.binder.processor.ProcessorContext;
import com.shunwang.basepassport.binder.response.PhoneLoginRespone;
import com.shunwang.basepassport.binder.web.send.bean.MobileBindBean;
import com.shunwang.basepassport.interc.SingleAccountBindService;
import com.shunwang.basepassport.key.common.UserRegisterKeyUtil;
import com.shunwang.basepassport.manager.service.bo.ReportProcessor;
import com.shunwang.basepassport.user.common.PwdUtil;
import com.shunwang.basepassport.user.common.TicketUtil;
import com.shunwang.basepassport.user.common.UserLoginSessionUtil;
import com.shunwang.basepassport.user.pojo.Member;
import com.shunwang.basepassport.user.response.ValidateAgainResponse;
import com.shunwang.util.lang.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Duration;

/**
 * 手机直接注册流程--电竞酒店业务专用
 *
 **/
public class HotelRegisterProcessor implements Processor, ReportProcessor {

    protected static final Logger logger = LoggerFactory.getLogger( HotelRegisterProcessor.class);

    @Override
    public boolean matches(ProcessorContext context) {
        return BinderConstants.HOTEL_MOBLIE_REGISTER.equals(context.getSendBinder().getBusinessType());
    }

    @Override
    public boolean doProcess(ProcessorContext context) {
        SendBinder sendBinder = context.getSendBinder();
        MobileBindBean bean = (MobileBindBean) context.getT();

        sendBinder.setNumber(bean.getNumber());

        //注册请求次数限制
        TimedLock regLock = CacheService.newTimedLock("REGISTER_SMS_CODE" + bean.getMobile(), Duration.ofMinutes(1L));
        if (!regLock.tryLock()) {
            throw new BaseStoneException(ErrorCode.C_1102.getCode(), ErrorCode.C_1102.getDescription());
        }

        CounterLock smsCheckLock = CacheService.newCounterLock("REGISTER_SMS_CODE_CONFIRM" + bean.getMobile(), 1, Duration.ofMinutes(1L));
        if (!smsCheckLock.tryLock()) {
            throw new BaseStoneException(ErrorCode.C_1102.getCode(), ErrorCode.C_1102.getDescription());
        }
        try {
            Member registerMember = Helper.buildRegisterMember(bean);
            registerMember.setLoginType(sendBinder.getLoginType());
            registerMember = registerMember.regist(false);

            reportReg(bean.getSiteId(), registerMember, bean.getReportData());

            if (StringUtil.isNotBlank(bean.getPassword())) {
                registerMember.setMemberPwd(PwdUtil.convertMd5(bean.getPassword()));
                registerMember.update();
            }
            registerMember.setEnv(bean.getEnvironment());
            registerMember.setExtData(bean.getRemark());
            registerMember.setAccessSiteId(bean.getAccessSiteId());
            registerMember.setVrsion(bean.getVersion());
            registerMember.loginWithNoPwd();
            String sign = getSingleAccountBindService().bindTable(registerMember, bean.getBindType(),
                    bean.getSingleAccountToken(), bean.getBussiness());

            UserRegisterKeyUtil.cacheMember(registerMember);
            if (null != sign) {
                ValidateAgainResponse response = new ValidateAgainResponse();
                response.setMsgId("1019");
                response.setToken(sign);
                response.setMsg("请绑定手机号");
                context.put("response", response);
                return false;
            }
            PhoneLoginRespone response = new PhoneLoginRespone(registerMember);
            response.setTicket(TicketUtil.buildLoginTicket(registerMember));
            context.put("response", response);
            UserLoginSessionUtil.saveSession(registerMember.getMemberName(), UserLoginSessionUtil.LoginType.INTERFACE_HOTEL.getType(), null, sendBinder.getAccessSiteId());

        } catch (BaseStoneException e) {
            regLock.unlock();
            throw e;
        }


        regLock.unlock();
        return false;
    }

    private SingleAccountBindService getSingleAccountBindService() {
        return (SingleAccountBindService) BaseStoneContext.getInstance().getBean("singleAccountBindService");
    }

}
