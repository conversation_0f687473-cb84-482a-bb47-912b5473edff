<%@ page contentType="text/html;charset=UTF-8"%>
<%@ include file="/common/taglibs.jsp"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name="Keywords" content="顺网通行证， 密保问题 ，安全" />
<%--<meta name="Description" content="密保问题还未设置，可能影响账户安全，建议尽快设置。" />--%>
<meta name="Description" content="密保问题设置/修改" />


<title>顺网通行证-安全中心-密保问题设置</title>
</head>
<body>
    <div class="shop_head">
        <strong>密保问题</strong>
        设置后可以通过密保问题找回密码。
    </div>

    <div class="c_body forget_s02">
        <p>尊敬的用户你好：</p>

        <div class="appeal_result">

            <p class="fB" style="color:blue;">${msg}</p>
            <c:if test="${member.isBindQuestion == true}">
                <p>您的通行证账户已经设置密保问题</p>
                <a href="###" onclick="toChange()">重置密保问题</a>
                &nbsp; <a href="${appServer }/front/login/chooseFindQuestion.htm">找回密保问题</a>
            </c:if>
            <c:if test="${member.isBindQuestion != true}">

                <p>您的通行证账户还未设置密保问题</p>
                <a href="###"onclick="toChange()">重置密保问题</a>
            </c:if>
            <form action="" method="post">
                <input type="hidden" id="businessType" name="questionBinder.businessType" value=""/>
                <input type="hidden" name="returnUrl" value="" id="returnUrl"/>
            </form>
        </div>
        </div>
        <!-- contbox end -->
<script type="text/javascript">
  function toSubmit(type,url){
	$("#businessType").val('3');
	$("#returnUrl").val('front/securityCenter/questionBind_front.jsp?questionBinder.businessType=3');
	document.forms[0].action = "${appServer}/front/securityCenter/toValidate.jsp?binderFrom=question";
	document.forms[0].submit();
  }
  function toChange(){
	$("#businessType").val('4');
	$("#returnUrl").val('front/securityCenter/questionBind_front.jsp?questionBinder.businessType=4');
	document.forms[0].action = "${appServer}/front/securityCenter/toValidate.jsp?binderFrom=question";
	document.forms[0].submit();
  }
</script>
<script type="text/javascript">
    $('.sidebar .menu li:first').addClass('current');
</script>
</body>
</html>

