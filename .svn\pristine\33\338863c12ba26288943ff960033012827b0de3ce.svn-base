package com.shunwang.baseStone.zone.generator;

import java.util.ArrayList;
import java.util.List;

import org.springframework.context.ApplicationContext;

import com.shunwang.baseStone.zone.constant.ZoneConstant;
import com.shunwang.baseStone.zone.dao.ZoneDao;
import com.shunwang.baseStone.zone.pojo.Zone;
import com.shunwang.util.IO.FileUtil;

/**
 * @Description:城市数据生成器
 * <AUTHOR>  create at 2011-8-30 下午01:28:38
 * @FileName com.shunwang.baseStone.zone.ZoneListGenerator.java
 */
public class ZoneListGenerator {
	
	private static ApplicationContext context;
	/**
	 * 生成json对象
	 * @param zones
	 * @return
	 * <AUTHOR> create at 2011-8-30 下午04:17:40
	 */
	private static String generateJson(List<Zone> zones){
		List<Integer> provinceIds=new ArrayList<Integer>();
		List<Integer> areaIds=new ArrayList<Integer>();
		StringBuffer sb=new StringBuffer(" ");
		for(int i=0;i<zones.size();++i){
			Zone zone=zones.get(i);
			if(areaIds.contains(zone.getAreaId())){
				if(provinceIds.contains(zone.getProvinceId())){
					sb.append("{text:'").append(zone.getCityName()).append("',value:'").append(zone.getCityId())
					.append("'},");
				}
				else{
					provinceIds.add(zone.getProvinceId());
					sb=new StringBuffer(sb.substring(0,sb.length()-1));
					sb.append("]},").append("{text:'"+zone.getProvinceName()+"',value:'"+zone.getProvinceId()+"',items:[{")
					.append("text:'").append(zone.getCityName()).append("',value:'").append(zone.getCityId())
					.append("'},");
				}
			}else{
				provinceIds.clear();
				areaIds.add(zone.getAreaId());
				provinceIds.add(zone.getProvinceId());
				sb=new StringBuffer(sb.substring(0,sb.length()-1));
				sb.append("]}]},{text:'").append(zone.getAreaName()).append("',value:'").append(zone.getAreaId())
				.append("',items:[{").append("text:'").append(zone.getProvinceName()).append("',value:'").append(zone.getProvinceId())
				.append("',items:[{").append("text:'").append(zone.getCityName()).append("',value:'").append(zone.getCityId())
				.append("'},");
			}

		}
		return sb.length()>1?"["+sb.substring(5,sb.length()-1)+"]}]}]":"[]";
	}
	/**
	 * 生成Js文件
	 * <AUTHOR> create at 2011-8-30 下午09:09:37
	 */
	private static void generateJsFile(List<Zone> zones){
		try {
//			FileUtil.writeStringToFile(ZoneConstant.ZoneJsPath, "var zoneList="+generateJson(zones), "UTF-8", false);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	/**
	 * 查找所有的区域信息
	 * @return
	 * <AUTHOR> create at 2011-8-30 下午09:13:44
	 */
	private static List<Zone> findAllZones(){
		ZoneDao zoneDao=(ZoneDao)context.getBean("zoneDao") ;
		return zoneDao.findAll();
	}
	/**
	 * 刷新区域信息，重新生成区域的js
	 * <AUTHOR> create at 2011-8-30 下午09:25:18
	 */
	public static void flushZoneData(){
		generateJsFile(findAllZones());
	}
	/**
	 * 设置上下文
	 * @param context
	 * <AUTHOR> create at 2011-8-30 下午09:53:44
	 */
	public static void setContext(ApplicationContext context) {
		ZoneListGenerator.context = context;
	}



}
