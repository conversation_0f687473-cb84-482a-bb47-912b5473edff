package com.shunwang.basepassport.config.pojo;

import com.shunwang.baseStone.core.pojo.BaseStoneObject;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2022/6/23 16:34
 */
public class WeakPasswordDO extends BaseStoneObject {
    /**
     * 主键
     */
    private String id;
    /**
     * 密码原文
     */
    private String pwd;
    /**
     * md5加密后的密文
     */
    private String pwdMd5;

    /**
     * 是否是弱密码:1-弱密码，2-不是弱密码
     */
    private Integer isWeakPassword;

    public WeakPasswordDO() {
    }

    @Override
    public Serializable getPk() {
        return id;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPwd() {
        return pwd;
    }

    public void setPwd(String pwd) {
        this.pwd = pwd;
    }

    public String getPwdMd5() {
        return pwdMd5;
    }

    public void setPwdMd5(String pwdMd5) {
        this.pwdMd5 = pwdMd5;
    }

    public Integer getIsWeakPassword() {
        return isWeakPassword;
    }

    public void setIsWeakPassword(Integer isWeakPassword) {
        this.isWeakPassword = isWeakPassword;
    }
}
