package com.shunwang.baseStone.useroutinterface.pojo;
import com.shunwang.baseStone.context.BackUserContext;
import com.shunwang.baseStone.context.BaseStoneContext;
import com.shunwang.baseStone.core.detail.Detail;
import com.shunwang.baseStone.core.detail.DetailItem;
import com.shunwang.baseStone.core.detail.HasDetail;
import com.shunwang.baseStone.core.pojo.BaseStoneObject;
import com.shunwang.baseStone.editlog.pojo.EditLog;
import com.shunwang.baseStone.useroutinterface.constant.UseroutConstant;
import com.shunwang.baseStone.useroutinterface.dao.UseroutInterfaceDao;

import java.io.Serializable;

/**
 * @Described：外部用户接入接口
 * <AUTHOR> create at 2013-7-30 下午03:59:22
 * @ClassNmae com.shunwang.baseStone.useroutinterface.pojo.UseroutInterface
 */
public class UseroutInterface extends BaseStoneObject implements HasDetail{

	public EditLog editLog = new EditLog();

	private static final long serialVersionUID = 1L;
	private Integer inerfaceId;
	private String outServiceId;
	private String serviceURL;
	private String callbackURL;
	private String serviceKey;
	private String serviceState;  //0、正常 1、关闭
	private String permitIp;
	private java.util.Date timeAdd;
	private String userAdd;
	private java.util.Date timeEdit;
	private String userEdit;
	private String remark;
	private  String prefixName;
	private  String serviceProvider;
	private String serviceImg;
	private String serviceBigImg;
	private Integer dirId;
	private Integer orderBy;
	private Integer autoLogin;

	public UseroutInterface(){

	}
	public void setInerfaceId(Integer value){
		editLog.addItem(new DetailItem("inerfaceId",inerfaceId, value));
		this.inerfaceId=value;
	}
	public Integer getInerfaceId(){
		return this.inerfaceId;
	}
	public void setOutServiceId(String value){
		editLog.addItem(new DetailItem("外部服务id",outServiceId, value));
		this.outServiceId=value;
	}
	public String getOutServiceId(){
		return this.outServiceId;
	}
	public void setServiceURL(String value){
		editLog.addItem(new DetailItem("服务url",serviceURL, value));
		this.serviceURL=value;
	}
	public String getServiceURL(){
		return this.serviceURL;
	}
	public String getPrefixName() {
		return prefixName;
	}
	public void setPrefixName(String value) {
		editLog.addItem(new DetailItem("用户名前缀",prefixName, value));
		this.prefixName = value;
	}
	public void setCallbackURL(String value){
		editLog.addItem(new DetailItem("回调url",callbackURL, value));
		this.callbackURL=value;
	}
	public String getCallbackURL(){
		return this.callbackURL;
	}
	public void setServiceKey(String value){
		editLog.addItem(new DetailItem("服务编码密钥",serviceKey, value));
		this.serviceKey=value;
	}
	public String getServiceKey(){
		return this.serviceKey;
	}
	public void setServiceState(String value){
		this.serviceState=value;
	}
	public String getServiceState(){
		return this.serviceState;
	}
	public void setPermitIp(String value){
		editLog.addItem(new DetailItem("限制IP",permitIp, value));
		this.permitIp=value;
	}
	public String getPermitIp(){
		return this.permitIp;
	}
	public void setTimeAdd(java.util.Date value){
		editLog.addItem(new DetailItem("添加时间",timeAdd, value));
		this.timeAdd=value;
	}
	public java.util.Date getTimeAdd(){
		return this.timeAdd;
	}
	public String getServiceProvider() {
		return serviceProvider;
	}
	public void setServiceProvider(String value) {
		editLog.addItem(new DetailItem("供应商名称",serviceProvider, value));
		this.serviceProvider = value;
	}
	public void setUserAdd(String value){
		editLog.addItem(new DetailItem("添加人",userAdd, value));
		this.userAdd=value;
	}
	public String getUserAdd(){
		return this.userAdd;
	}
	public void setTimeEdit(java.util.Date value){
		editLog.addItem(new DetailItem("修改时间",timeEdit, value));
		this.timeEdit=value;
	}
	public java.util.Date getTimeEdit(){
		return this.timeEdit;
	}
	public void setUserEdit(String value){
		editLog.addItem(new DetailItem("修改人",userEdit, value));
		this.userEdit=value;
	}
	public String getUserEdit(){
		return this.userEdit;
	}
	public void setRemark(String value){
		editLog.addItem(new DetailItem("备注",remark, value));
		this.remark=value;
	}
	public String getRemark(){
		return this.remark;
	}

	public UseroutInterfaceDao findDao(){
		return (UseroutInterfaceDao)BaseStoneContext.getInstance().getBean("useroutInterfaceDao");
	}
	/**
	 * 关闭
	 */
	public void close(){
		this.serviceState = UseroutConstant.S_Close;
		editLog.addItem(new DetailItem("状态","打开", "关闭"));
		this.findDao().update(this);
	}
	/**
	 * 打开
	 */
	public void open(){
		this.serviceState = UseroutConstant.S_Open;
		editLog.addItem(new DetailItem("状态","关闭", "打开"));
		this.findDao().update(this);
	}

	public boolean isOpen(){
		return this.serviceState != UseroutConstant.S_Close;
	}
	@Override
	public Detail getDetail() {
		return editLog;
	}
	@Override
	public void beginBuildLog() {
		String sysclass = this.getClass().toString();
		String conkey = sysclass.substring(sysclass.lastIndexOf(".") + 1,
				sysclass.length());
		editLog.beginBuildLog(true);
		editLog.setEditItem("用户导入管理");
		editLog.setConfKey(conkey);
		editLog.setUserAdd(BackUserContext.getUserName());
	}
	public String getLinkUrl() {
		return "javascript:goToOtherSite('"+inerfaceId+"')";
	}
	@Override
	public Serializable getPk() {
		return inerfaceId;
	}
	public void setServiceImg(String serviceImg) {
		this.serviceImg = serviceImg;
	}
	public String getServiceImg() {
		return serviceImg;
	}

	public String getServiceBigImg() {
		return serviceBigImg;
	}

	public void setServiceBigImg(String serviceBigImg) {
		this.serviceBigImg = serviceBigImg;
	}

	public void setDirId(Integer dirId) {
		this.dirId = dirId;
	}
	public Integer getDirId() {
		return dirId;
	}
	public void setOrderBy(Integer orderBy) {
		this.orderBy = orderBy;
	}
	public Integer getOrderBy() {
		return orderBy;
	}

	public Integer getAutoLogin() {
		return autoLogin;
	}

	public void setAutoLogin(Integer autoLogin) {
		this.autoLogin = autoLogin;
	}

	/**
	 * 直接写isAutoLogin会影响sqlMap取autoLogin（true）值，影响db的autoLogin更新
	 * @return
	 */
	public boolean isAutoLoginOpen() {
		return autoLogin != null && autoLogin.toString().equals(UseroutConstant.S_Open);
	}

	@Override
	public String toString() {
		return "UseroutInterface{" +
				"inerfaceId=" + inerfaceId +
				", outServiceId='" + outServiceId + '\'' +
				", serviceURL='" + serviceURL + '\'' +
				", callbackURL='" + callbackURL + '\'' +
				", serviceKey='" + serviceKey + '\'' +
				", serviceState='" + serviceState + '\'' +
				", permitIp='" + permitIp + '\'' +
				", userEdit='" + userEdit + '\'' +
				", remark='" + remark + '\'' +
				", prefixName='" + prefixName + '\'' +
				", serviceProvider='" + serviceProvider + '\'' +
				", serviceImg='" + serviceImg + '\'' +
				", serviceBigImg='" + serviceBigImg + '\'' +
				", dirId=" + dirId +
				", orderBy=" + orderBy +
				", autoLogin=" + autoLogin +
				'}';
	}
}
