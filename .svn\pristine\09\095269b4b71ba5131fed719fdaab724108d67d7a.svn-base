package com.shunwang.baseStone.sso.login;

import com.shunwang.basepassport.manager.response.inter.BaseXmlResponse;
import com.shunwang.basepassport.manager.service.inter.SaxParserHelper;
import com.shunwang.util.date.DateUtil;
import com.shunwang.util.encrypt.AesEncrypt;
import com.shunwang.util.encrypt.Md5Encrypt;
import com.shunwang.util.net.HttpClientUtils;
import org.junit.Test;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * User:lj.zeng
 * Date:2021/03/01
 * Time:16:24
 */
public class RegTest {

	@Test
	public void Test(){
		String respond = "";

		try {

			String userId = "25484927";
			String userName = "safiad123";
			String siteId = "Passport" ;
			String timestamp = DateUtil.getCurrentDateStamp() ;

			String md5Key = "96ED55F371064646984376EB1EC5295C" ;

			String md5Key2 = "96ED55F371064646" ;

			String plainText = siteId+"|"+timestamp+"|"+md5Key ;
			String sign = Md5Encrypt.encrypt(URLEncoder.encode(plainText,"utf-8").toUpperCase()).toUpperCase() ;
			URL url = new URL("http://sso.kedou.com/reg.do");

			Map<String, String> paramMap = new HashMap<>();
			paramMap.put("time", timestamp);
			paramMap.put("siteId", siteId);
			paramMap.put("userId", AesEncrypt.Encrypt(userId, md5Key2));
			paramMap.put("userName", AesEncrypt.Encrypt(userName, md5Key2));
			paramMap.put("sign", sign);
			respond = HttpClientUtils.doPost("http://sso.kedou.com/reg.do",paramMap);
		} catch (Exception e) {
			//e.printStackTrace();
			respond = e.getMessage();
		}

		System.out.println(respond);

		String regIdentity = respond.substring(respond.indexOf("<regIdentity>") + 13, respond.indexOf("</regIdentity>") );

		System.out.println(regIdentity);

		String jumpUrl = "http://i.kedou.com";
		String url = "http://sso.kedou.com/regTicketCreate.do?regidentityId=" + regIdentity + "&jumpUrl=" + URLEncoder.encode(jumpUrl) + "&siteId=Passport";


		System.out.println(url);
	}
}
