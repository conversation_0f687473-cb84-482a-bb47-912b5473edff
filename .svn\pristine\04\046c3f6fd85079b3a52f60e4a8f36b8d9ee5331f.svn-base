/*
 * 版权所有：顺网科技 保留所有权利
 * 创建日期: 2015-04-22
 * 创建作者：min.da
 * 文件名称：InterfaceLoginAuthorizeAction.java
 * 版本：
 * 功能：
 * 最后修改时间：2015-04-22 11:49:57
 * 修改记录：
 */

package com.shunwang.baseStone.sso.web;

import com.shunwang.baseStone.context.BaseStoneContext;
import com.shunwang.baseStone.core.exception.BaseStoneException;
import com.shunwang.baseStone.siteinterface.context.SiteContext;
import com.shunwang.baseStone.sso.constant.InterfaceConstant;
import com.shunwang.baseStone.sso.constant.UserTockenConstant;
import com.shunwang.baseStone.sso.context.SsoDomainContext;
import com.shunwang.baseStone.sso.context.TicketContext;
import com.shunwang.baseStone.sso.context.UserTockenContext;
import com.shunwang.baseStone.sso.exception.ValidateExp;
import com.shunwang.baseStone.sso.util.UrlUtil;
import com.shunwang.baseStone.useroutinterface.constant.UseroutConstant;
import com.shunwang.basepassport.binder.common.ErrorCode;
import com.shunwang.basepassport.config.pojo.SiteInterface;
import com.shunwang.basepassport.user.common.UserCheckUtil;
import com.shunwang.basepassport.user.common.UserLoginSessionUtil;
import com.shunwang.basepassport.user.dao.MemberDao;
import com.shunwang.basepassport.user.pojo.Member;
import com.shunwang.framework.struts2.action.BaseAction;
import com.shunwang.util.net.CookieUtil;
import com.shunwang.util.net.HttpUtil;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.jdom.Document;
import org.jdom.Element;
import org.jdom.JDOMException;
import org.jdom.input.SAXBuilder;
import org.xml.sax.InputSource;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.StringReader;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 通过interface登录授权
 *
 * <AUTHOR>
 * @since 2015-04-22
 */
public class InterfaceLoginAuthorizeAction extends BaseAction {

    private final static Logger log = LoggerFactory.getLogger(InterfaceLoginAuthorizeAction.class);

    private static final long serialVersionUID = -13773527542373641L;

    private String memberId;
    private String accessToken;
    private String siteId;
    private String time;
    private String sign;
    private String targetSiteId;
    private String redirectUri;

    @Override
    public String execute() throws Exception {
        String redirect = redirectUri;
        try {
            validParam();

            checkSite();

            String responseText = HttpUtil.doPost(InterfaceConstant.VERIFY_TOKEN_URL, buildParam(), InterfaceConstant.DEFAULT_CHARSET);

            handleResult(responseText);

            Member member = getMemberDao().getByMemberId(Integer.valueOf(memberId));
            if (member != null) {
                UserCheckUtil.checkMemberState(member.getMemberState(), member.getMemberName());
            }
            String ticket = TicketContext.createTicket(member,siteId, UserLoginSessionUtil.LoginType.SSO_FREE_INTERFACE.getType()).toString();
            String token = UserTockenContext.createTocken(member, targetSiteId).toString();
            CookieUtil.setCookieUpgrade(this.getResponse(), UserTockenConstant.S_UserTockenKey, token);
            redirect = UrlUtil.buildUrl(redirectUri, "ticketId=" + ticket, "tockenId=" + token);
        } catch (ValidateExp e) {
            if (log.isErrorEnabled())
                log.error(e.getMessage());

            if (StringUtils.isBlank(redirectUri))
                redirect = SsoDomainContext.getIdentityServer();
        } catch (BaseStoneException e) {
            if (log.isErrorEnabled())
                log.error(e.getMessage());
        } catch (Exception e) {
            if (log.isErrorEnabled())
                log.error(e.getMessage());
        }
        getResponse().sendRedirect(UrlUtil.handleProtocol(redirect));
        return null;
    }

    private void validParam() {
        if (StringUtils.isBlank(memberId) || StringUtils.isBlank(accessToken) || StringUtils.isBlank(siteId)
                || StringUtils.isBlank(time) || StringUtils.isBlank(sign) || StringUtils.isBlank(targetSiteId)
                || StringUtils.isBlank(redirectUri))
            throw new ValidateExp("缺少必要的参数");
    }

    private Map<String, String> buildParam() {
        Map<String, String> map = new HashMap<String, String>();
        map.put("memberId", memberId);
        map.put("accessToken", accessToken);
        map.put("siteId", siteId);
        map.put("time", time);
        map.put("sign", sign);
        return map;
    }

    private void handleResult(String content) throws JDOMException, IOException {
        if (log.isInfoEnabled())
            log.info("验证访问令牌接口响应:" + content);

        StringReader read = new StringReader(content);
        InputSource source = new InputSource(read);
        SAXBuilder builder = new SAXBuilder();
        Document document = builder.build(source);
        Element root = document.getRootElement();
        Element result = root.getChild("Result");
        String msgId = result.getAttributeValue("msgId");
        String msg = result.getAttributeValue("msg");
        if (!msgId.equals("0"))
            throw new ValidateExp(msg);
    }

    private void checkSite() {
        SiteContext.setSiteName("interLoginAuthorize");
        SiteContext.setSiteId(siteId);
        SiteInterface siteInterface = SiteContext.getSiteInterface();
        if (null == siteInterface || siteInterface.getState().equals(UseroutConstant.S_Close))
            throw new BaseStoneException(ErrorCode.C_1004);
    }

    protected MemberDao getMemberDao() {
        return (MemberDao) BaseStoneContext.getInstance().getBean("memberDao");
    }

    /**
     * 记录请求日志
     * <AUTHOR>
     * @param request
     */
    @SuppressWarnings("unchecked")
    private void logRequest(HttpServletRequest request) {
        if (!log.isInfoEnabled()) {
            return;
        }
        Map<String, String[]> paramMap = request.getParameterMap();
        List<String> paramList = new ArrayList<String>(paramMap.size());
        for (Map.Entry<String, String[]> entry : paramMap.entrySet()) {
            paramList.add(entry.getKey()+"="+StringUtils.join(entry.getValue()));
        }
        log.info(StringUtils.join(paramList.iterator(), " | "));
    }

    public String getSiteId() {
        return siteId;
    }

    public void setSiteId(String siteId) {
        this.siteId = siteId;
    }

    public String getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }

    public String getMemberId() {
        return memberId;
    }

    public void setMemberId(String memberId) {
        this.memberId = memberId;
    }

    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    public String getTargetSiteId() {
        return targetSiteId;
    }

    public void setTargetSiteId(String targetSiteId) {
        this.targetSiteId = targetSiteId;
    }

    public String getRedirectUri() {
        return redirectUri;
    }

    public void setRedirectUri(String redirectUri) {
        this.redirectUri = redirectUri;
    }
}
