package com.shunwang.basepassport.binder.web;

import com.shunwang.baseStone.context.BaseStoneContext;
import com.shunwang.baseStone.core.detail.DetailItem;
import com.shunwang.baseStone.encrypt.Encrypt;
import com.shunwang.basepassport.detail.common.DetailContants;
import com.shunwang.basepassport.detail.dao.PersonalEditLogDao;
import com.shunwang.basepassport.detail.pojo.PersonalEditLog;
import com.shunwang.basepassport.mobile.dao.MobileCheckCodeDao;
import com.shunwang.basepassport.user.common.MemberConstants;
import com.shunwang.basepassport.user.pojo.Member;

import java.util.Date;

public class ShunLingUnbinderAction extends ShunLingBinderAction {

	private static final long serialVersionUID = -6182722132948252951L;
	
	@Override
	public void process() {
		Member member = super.loadMember();
		if(member.getIsBindShunLing()) {
			getEditLogDao().save(buildEditLog(member));
			member.setIsBindShunLing(Boolean.FALSE);
			member.update();
		}
	}
	
	@Override
	public String buildSignString() {
		Encrypt encrypt = super.buildSignStr();
		return encrypt.buildSign();
	}
	
	public PersonalEditLogDao getEditLogDao() {
		return (PersonalEditLogDao) BaseStoneContext.getInstance().getBean("personalEditLogDao");
	}
	
	public PersonalEditLog buildEditLog(Member member) {
		DetailItem item = new DetailItem("绑定状态", String.valueOf(member.getBindState()), String.valueOf(member.getBindState() - MemberConstants.MEMBER_STATE_SHUN_LING));
		
		PersonalEditLog editLog = new PersonalEditLog();
		editLog.setMember(member);
		editLog.setType(DetailContants.FRONT);
		editLog.setUserAdd(member.getMemberName());
		editLog.setValuePre(String.valueOf(member.getMemberName()));
		editLog.setValueCur(String.valueOf(member.getBindState() + MemberConstants.MEMBER_STATE_SHUN_LING));
		editLog.setEditItem("解绑顺令");
		editLog.setDate(new Date());
		editLog.beginBuildLog(Boolean.TRUE);
		editLog.addItem(item);
		
		return editLog;
	}


	private MobileCheckCodeDao getMobileCheckCodeDao() {
		return (MobileCheckCodeDao)BaseStoneContext.getInstance().getBean("mobileCheckCodeDao");
	}

	@Override
	public String getSiteName() {
		return MemberConstants.SHUN_LING_UNBING;
	}

}
