package com.shunwang.basepassport.manager.response.yidun;

import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.shunwang.basepassport.manager.IResponse;
import com.shunwang.basepassport.manager.response.swpay.BaseSwpayJsonResponse;
import com.shunwang.util.json.GsonUtil;
import com.shunwang.util.lang.StringUtil;

/**
 * 网易H5一键登录接口返回
 * {
 *   "code": 200,
 *   "msg": "ok",
 *   "data": {
 *     "phone": "*****",
 *     "resultCode": "*****"
 *   }
 * }
 */
public class OneClickCheckResponse implements IResponse {

    public static final int SUCCESS = 200;
    private String rawJson;
    private Integer code;
    private String msg;
    private String phone;
    private String resultCode;

    private JsonObject jsonObject;

    public boolean isSuccess() {
        return code != null && SUCCESS == code;
    }

    public OneClickCheckResponse parse() {
        if (StringUtil.isBlank(rawJson)) {
            return null;
        }

        // 解析整个 JSON 字符串为 JsonObject
        jsonObject = JsonParser.parseString(rawJson).getAsJsonObject();

        // 直接从根对象中获取 code 和 msg
        if (jsonObject.has("code")) {
            code = jsonObject.get("code").getAsInt();
        }

        if (jsonObject.has("msg")) {
            msg = jsonObject.get("msg").getAsString();
        }

        // 解析 data 对象
        if (jsonObject.has("data")) {
            JsonObject dataObj = jsonObject.getAsJsonObject("data");
            if (dataObj.has("phone")) {
                phone = dataObj.get("phone").getAsString();
            }
            if (dataObj.has("resultCode")) {
                resultCode = dataObj.get("resultCode").getAsString();
            }
        }

        return this;
    }

    protected boolean checkJsonItem(JsonObject jsonObject,String itemName) {
        return jsonObject.get(itemName) != null;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getRawJson() {
        return rawJson;
    }

    public void setRawJson(String rawJson) {
        this.rawJson = rawJson;
    }

    public JsonObject getJsonObject() {
        return jsonObject;
    }

    public void setJsonObject(JsonObject jsonObject) {
        this.jsonObject = jsonObject;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getResultCode() {
        return resultCode;
    }

    public void setResultCode(String resultCode) {
        this.resultCode = resultCode;
    }
}
