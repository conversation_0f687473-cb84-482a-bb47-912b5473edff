package com.shunwang.basepassport.config.dao;


import com.shunwang.baseStone.core.dao.BaseStoneIbatisDao;
import com.shunwang.basepassport.config.enums.OneLoginEnum;
import com.shunwang.basepassport.config.pojo.ConfigInterface;
import com.shunwang.basepassport.config.pojo.ConfigOneLogin;

/**
 * 一键登录渠道及站点配置
 *
 */
public class ConfigOneLoginDao extends BaseStoneIbatisDao<ConfigOneLogin> {
	/**
	 * 查询接入方渠道配置
	 * @param siteId
	 * @param terminalType
	 * @return
	 */
	public ConfigOneLogin findBySiteIdAndTerminalType(String siteId, Integer terminalType){
		ConfigOneLogin configOneLogin = new ConfigOneLogin();
		configOneLogin.setSiteId(siteId);
		configOneLogin.setChannel(OneLoginEnum.Channel.JI_JIAN.getValue());
		configOneLogin.setTerminalType(terminalType);
		return (ConfigOneLogin)getSqlMapClientTemplate().queryForObject(getStatementNameWrap("findByPojo"), configOneLogin);
	}

	public ConfigOneLogin findBySiteIdAndChannel(String siteId, Integer channel, Integer terminalType){
		ConfigOneLogin configOneLogin = new ConfigOneLogin();
		configOneLogin.setSiteId(siteId);
		configOneLogin.setChannel(channel);
		configOneLogin.setTerminalType(terminalType);
		return (ConfigOneLogin)getSqlMapClientTemplate().queryForObject(getStatementNameWrap("findByPojo"), configOneLogin);
	}
}
