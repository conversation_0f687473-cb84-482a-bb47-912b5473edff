package com.shunwang.baseStone.sso.weixin.pojo;

import com.shunwang.util.IO.XmlUtil;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.Element;

/**
 * User:pf.ma
 * Date:2019/12/30
 * Time:15:27
 */
public class WeixinComponentTicket {

	private String appId ;
	private String infoType ;
	private String componentVerifyTicket ;

	public WeixinComponentTicket(String result) throws DocumentException {
		Document document = XmlUtil.parseXml(result);
		Element rootElement = document.getRootElement();
		appId = rootElement.elementTextTrim("AppId");
		componentVerifyTicket = rootElement.elementTextTrim("ComponentVerifyTicket");
		infoType = rootElement.elementTextTrim("InfoType");
	}

	public String getAppId() {
		return appId;
	}

	public void setAppId(String appId) {
		this.appId = appId;
	}

	public String getInfoType() {
		return infoType;
	}

	public void setInfoType(String infoType) {
		this.infoType = infoType;
	}

	public String getComponentVerifyTicket() {
		return componentVerifyTicket;
	}

	public void setComponentVerifyTicket(String componentVerifyTicket) {
		this.componentVerifyTicket = componentVerifyTicket;
	}
}
