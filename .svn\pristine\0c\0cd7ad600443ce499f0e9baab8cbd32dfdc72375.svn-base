<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd" >
<sqlMap namespace="com.shunwang.baseStone.useroutinterface.pojo.OutOauthDir">
<resultMap class="com.shunwang.baseStone.useroutinterface.pojo.OutOauthDir" id="BaseResultMap">
	<result property="dirId" column="dirId" jdbcType="int"/>
	<result property="dirName" column="dirName" jdbcType="varchar"/>
	<result property="dirImg" column="dirImg" jdbcType="varchar"/>
	<result property="bigImg" column="bigImg" jdbcType="varchar"/>
	<result property="dirOrderBy" column="dirOrderBy" jdbcType="int"/>
	<result property="state" column="state" jdbcType="varchar"/>
	<result property="remark" column="remark" jdbcType="varchar"/>
	<result property="defaultState" column="default_state" jdbcType="varchar" />
	<result property="outOauthInterfaces" column="dirId" select="com.shunwang.baseStone.useroutinterface.pojo.UseroutInterface.findByDirId"/>
</resultMap>
<select id="findAllOutOauthDir" resultMap="BaseResultMap">
	SELECT
	dirId,
	dirName,
	dirImg,
	bigImg,
	dirOrderBy,
	state,
	remark,
	default_state
	FROM
	config_outoauthdir
	where state = 0
	order by dirOrderBy
</select>

<select id="findAll" resultMap="BaseResultMap">
    SELECT
	    dirId,
	    dirName,
	    dirImg,
	    bigImg,
	    dirOrderBy,
	    state,
	    remark,
	    default_state
    FROM
        config_outoauthdir
</select>

<select id="findOutOauthDirByBus" resultMap="BaseResultMap" parameterClass="com.shunwang.baseStone.loginelement.pojo.LoginElement">
    SELECT
	    a.dirId,
	    a.dirName,
	    a.dirImg,
	    a.bigImg,
	    a.dirOrderBy,
	    a.state,
	    a.remark,
		default_state
    FROM
        config_outoauthdir a,
		config_login_element b
	where b.state = 1 and b.name = a.dirname
	<isNotEmpty prepend="AND" property="bussinessKey"> b.bussiness_key=#bussinessKey# </isNotEmpty>
	<isNotEmpty prepend="AND" property="categoryId"> b.category_id=#categoryId# </isNotEmpty>
    <isNotEmpty prepend="AND" property="type"> b.type=#type# </isNotEmpty>
    order by b.show_order_by ASC
</select>

<select id="findOutOauthDirByCategoryId" resultMap="BaseResultMap" parameterClass="com.shunwang.baseStone.loginelement.pojo.LoginElement">
    SELECT
	    a.dirId,
	    a.dirName,
	    a.dirImg,
	    a.bigImg,
	    a.dirOrderBy,
	    a.state,
	    a.remark,
		default_state
    FROM
        config_outoauthdir a,
		config_login_element b
	where b.state = 1 and b.name = a.dirname and b.bussiness_key is null
	<isNotEmpty prepend="AND" property="categoryId"> b.category_id=#categoryId# </isNotEmpty>
    <isNotEmpty prepend="AND" property="type"> b.type=#type# </isNotEmpty>
    order by b.show_order_by ASC
</select>

</sqlMap>