package com.shunwang.basepassport.user.service;

import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.shunwang.baseStone.cache.CacheKeyConstant;
import com.shunwang.baseStone.context.RedisContext;
import com.shunwang.basepassport.asynctask.AsyncTaskExecutor;
import com.shunwang.basepassport.user.common.UserLoginSessionUtil;
import com.shunwang.basepassport.user.dao.*;
import com.shunwang.basepassport.user.pojo.Member;
import com.shunwang.basepassport.user.pojo.MemberAccountBind;
import com.shunwang.basepassport.user.pojo.MemberMultipleAccountBind;
import com.shunwang.basepassport.user.pojo.MemberOutSite;
import com.shunwang.basepassport.weixin.service.WeixinMsgService;
import com.shunwang.toolbox.tracing.TraceableRunnable;
import com.shunwang.util.json.GsonUtil;
import com.shunwang.util.lang.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;

/**
 * @author: lj.zeng
 * @create: 2024-05-13 16:03:41
 * @Description:
 */
public class MultipleAccountBindService {
    private static final Logger log = LoggerFactory.getLogger(MultipleAccountBindService.class);
    private MemberMultipleAccountBindDao memberMultipleAccountBindDao;
    private BarFreeLoginAuthService barFreeLoginAuthService;
    private MemberDao memberDao;
    private MemberAccountBindDao memberAccountBindDao;
    private MemberOutSiteDao memberOutSiteDao;
    private WeixinMsgService weixinMsgService;

    private final static Integer MULTIPLE_BIND = 1;
    private final static Integer SINGLE_BIND = 2;
    private final static Integer NET_BAR_BIND = 3;
    private final static Integer WEIXIN_BIND = 4;

    /**
     * 用于用户主动绑定多账号关系
     * @param idCardName 网吧账号
     * @param member 需要绑定的账号
     * @param siteId 当前登录的站点
     */
    public void bindAccountIfNecessary(String idCardName, Member member, String siteId) {
        AsyncTaskExecutor.asyncTraceRun(new MultipleBindJob(idCardName, member, siteId, null, MULTIPLE_BIND));
    }

    /**
     * 用于用户微信绑定多账号关系
     * @param idCardName 网吧账号
     * @param wxMmember 需要绑定的账号
     * @param siteId 当前登录的站点
     */
    public void bindAccountIfNecessary(String idCardName, Member wxMmember, String siteId, String unionId) {
        AsyncTaskExecutor.asyncTraceRun(new MultipleBindJob(idCardName, wxMmember, siteId, WEIXIN_BIND, unionId));
    }

    /**
     * 用于单账号自动绑定多账号关系
     * @param memberId 主账号id
     * @param siteId 当前登录的站点
     */
    public void bindAccountIfNecessary(Integer memberId, String siteId) {
        try {
            Integer idCard;
            Integer wxMemberId;
            //优先取多账号绑定关系，不存在的情况下再取单账号的绑定关系来获取网吧账号
            MemberMultipleAccountBind multipleAccountBind = memberMultipleAccountBindDao.getByMemberId(memberId);
            if (multipleAccountBind != null) {
                wxMemberId = null;
                idCard = multipleAccountBind.getIdCard();
            } else {
                MemberAccountBind accountBind = memberAccountBindDao.getByMemberId(memberId);
                if (accountBind == null || accountBind.getIdCard() == null) {
                    return;
                }
                idCard = accountBind.getIdCard();
                wxMemberId = accountBind.getWeixin();
            }
            Member idCardMember = memberDao.getByMemberId(idCard);
            if (idCardMember == null) {
                return;
            }
            Member member = memberDao.getByMemberId(memberId);
            AsyncTaskExecutor.asyncTraceRun(new MultipleBindJob(idCardMember.getMemberName(), member, siteId, wxMemberId, SINGLE_BIND))
                    .thenRun(new TraceableRunnable(()->weixinMsgService.sendAutoLoginTemplateMsgForMultiple(memberId, member.getMemberName(), siteId)));
        } catch (Exception e) {
            log.error("多账号绑定网吧帐号异常[{}]", e.getMessage());
        }
    }

    /**
     * 用于超级会员绑定身份证后绑定多账号关系
     * @param idCardOutMemberName 网吧账号
     * @param unionId 微信unionId
     * @param barid 当前登录的网吧
     */
    public void bindAccountIfNecessary(String idCardOutMemberName, String unionId, String barid) {
        AsyncTaskExecutor.asyncTraceRun(new MultipleBindFromNetBarJob(idCardOutMemberName, unionId, barid));
    }

    public MemberMultipleAccountBindDao getMemberMultipleAccountBindDao() {
        return memberMultipleAccountBindDao;
    }

    public void setMemberMultipleAccountBindDao(MemberMultipleAccountBindDao memberMultipleAccountBindDao) {
        this.memberMultipleAccountBindDao = memberMultipleAccountBindDao;
    }

    public BarFreeLoginAuthService getBarFreeLoginAuthService() {
        return barFreeLoginAuthService;
    }

    public void setBarFreeLoginAuthService(BarFreeLoginAuthService barFreeLoginAuthService) {
        this.barFreeLoginAuthService = barFreeLoginAuthService;
    }

    public MemberDao getMemberDao() {
        return memberDao;
    }

    public void setMemberDao(MemberDao memberDao) {
        this.memberDao = memberDao;
    }

    public MemberAccountBindDao getMemberAccountBindDao() {
        return memberAccountBindDao;
    }

    public void setMemberAccountBindDao(MemberAccountBindDao memberAccountBindDao) {
        this.memberAccountBindDao = memberAccountBindDao;
    }

    public MemberOutSiteDao getMemberOutSiteDao() {
        return memberOutSiteDao;
    }

    public void setMemberOutSiteDao(MemberOutSiteDao memberOutSiteDao) {
        this.memberOutSiteDao = memberOutSiteDao;
    }

    public WeixinMsgService getWeixinMsgService() {
        return weixinMsgService;
    }

    public void setWeixinMsgService(WeixinMsgService weixinMsgService) {
        this.weixinMsgService = weixinMsgService;
    }

    /**
     * 处理多账号关系增加及网吧授权增加或更新
     */
    class MultipleBindJob implements Runnable {

        private final Logger logger = LoggerFactory.getLogger(MultipleBindJob.class);

        private String idCardName;
        private Member member;
        private String siteId;
        private Integer wxMemberId;
        private Integer importType;
        private String unionId;

        public MultipleBindJob(String idCardName, Member member, String siteId, Integer wxMemberId, Integer importType) {
            this.idCardName = idCardName;
            this.member = member;
            this.siteId = siteId;
            this.wxMemberId = wxMemberId;
            this.importType = importType;
        }

        public MultipleBindJob(String idCardName, Member member, String siteId, Integer importType, String unionId) {
            this.idCardName = idCardName;
            this.member = member;
            this.siteId = siteId;
            this.unionId = unionId;
            this.importType = importType;
        }

        @Override
        public void run() {
            try {
                String sessionExtData = UserLoginSessionUtil.getExtData(idCardName);
                if (StringUtil.isBlank(sessionExtData)) {
                    return ;
                }
                JsonObject jsonObject = JsonParser.parseString(sessionExtData).getAsJsonObject();
                String barId = GsonUtil.getStringFromJsonObject(jsonObject, "barId");
                if (StringUtil.isBlank(barId)) {
                    logger.error("extData数据不不正确，不保存多账号关系");
                    return ;
                }
                Member idCardMember = memberDao.getByName(idCardName);
                if (idCardMember == null) {
                    logger.error("网吧帐号不存在，不保存多账号关系");
                    return ;
                }
                if (wxMemberId != null) {
                    MemberOutSite memberOutSite = memberOutSiteDao.getMemberByMemberId(wxMemberId);
                    unionId = memberOutSite.getOutMemberId();
                }

                MemberMultipleAccountBind multipleAccountBindDb = memberMultipleAccountBindDao.getByMemberId(member.getMemberId());
                if (multipleAccountBindDb != null && !multipleAccountBindDb.getIdCard().equals(idCardMember.getMemberId())) {
                    logger.error("通行证已绑定其他网吧帐号，本次不绑定");
                    return;
                }

                MemberMultipleAccountBind multipleAccountBind = memberMultipleAccountBindDao.getByIdCard(idCardMember.getMemberId());
                if (multipleAccountBind == null) {
                    multipleAccountBind = new MemberMultipleAccountBind();
                    multipleAccountBind.setMemberId(member.getMemberId());
                    multipleAccountBind.setMemberName(member.getMemberName());
                    multipleAccountBind.setFrom(siteId);
                    multipleAccountBind.setTimeAdd(new Date());
                    multipleAccountBind.setTimeEdit(new Date());
                    multipleAccountBind.setIdCard(idCardMember.getMemberId());
                    multipleAccountBind.setUnionId(unionId);
                    multipleAccountBind.setImportType(importType);
                    memberMultipleAccountBindDao.save(multipleAccountBind);
                }
                barFreeLoginAuthService.saveOrUpdateAuth(barId, idCardMember.getMemberId(), siteId);
            } catch (Exception e) {
                logger.error("多账号异步绑定异常 [{}]", e.getMessage());
            }
        }
    }

    /**
     * 处理多账号关系增加及网吧授权增加或更新
     */
    class MultipleBindFromNetBarJob implements Runnable {

        private final Logger logger = LoggerFactory.getLogger(MultipleBindJob.class);

        private String idCardOutMemberName;
        private String unionId;
        private String barId;

        public MultipleBindFromNetBarJob(String idCardOutMemberName, String unionId, String barId) {
            this.idCardOutMemberName = idCardOutMemberName;
            this.unionId = unionId;
            this.barId = barId;
        }

        @Override
        public void run() {
            boolean switchConfig = RedisContext.getSwitchConfig(CacheKeyConstant.ConfigResourcesConstants.NET_BAR_FREE_LOGIN_CONFIG,
                    CacheKeyConstant.ConfigResourcesConstants.NET_BAR_TO_MULTIPLE_SWITCH, false);
            if (!switchConfig) {
                logger.debug("超级会员导多账号功能关闭，不导入帐号");
                return;
            }
            try {
                if (StringUtil.isBlank(barId)) {
                    logger.error("extData数据不不正确，不保存多账号关系");
                    return ;
                }
                MemberOutSite idCardOutMember = memberOutSiteDao.getMemberByOutMemberNameAndFrom(idCardOutMemberName, 1102);
                if (idCardOutMember == null) {
                    logger.error("网吧帐号不存在，不保存多账号关系");
                    return;
                }
                Member wxMember = memberOutSiteDao.getByOutMemberId(unionId);
                if (wxMember == null) {
                    logger.error("微信帐号不存在，不保存多账号关系");
                    return;
                }

                MemberMultipleAccountBind multipleAccountBindDb = memberMultipleAccountBindDao.getByMemberId(wxMember.getMemberId());
                if (multipleAccountBindDb != null && !multipleAccountBindDb.getIdCard().equals(idCardOutMember.getMemberId())) {
                    logger.error("通行证已绑定其他网吧帐号，本次不绑定");
                    return;
                }

                String siteId = RedisContext.getStrConfig(CacheKeyConstant.ConfigResourcesConstants.NET_BAR_FREE_LOGIN_CONFIG,
                        CacheKeyConstant.ConfigResourcesConstants.DEFAULT_FROM_SITE_ID_FOR_MULTIPLE, "barLoginSpecial");

                MemberMultipleAccountBind multipleAccountBind = memberMultipleAccountBindDao.getByIdCard(idCardOutMember.getMemberId());
                if (multipleAccountBind == null) {
                    multipleAccountBind = new MemberMultipleAccountBind();
                    multipleAccountBind.setMemberId(wxMember.getMemberId());
                    multipleAccountBind.setMemberName(wxMember.getMemberName());
                    multipleAccountBind.setFrom(siteId);
                    multipleAccountBind.setTimeAdd(new Date());
                    multipleAccountBind.setTimeEdit(new Date());
                    multipleAccountBind.setIdCard(idCardOutMember.getMemberId());
                    multipleAccountBind.setUnionId(unionId);
                    multipleAccountBind.setImportType(NET_BAR_BIND);
                    memberMultipleAccountBindDao.save(multipleAccountBind);
                }
                barFreeLoginAuthService.saveOrUpdateAuth(barId, idCardOutMember.getMemberId(), siteId);
            } catch (Exception e) {
                logger.error("多账号异步绑定[超级会员]异常 [{}]", e.getMessage());
            }
        }
    }
}
