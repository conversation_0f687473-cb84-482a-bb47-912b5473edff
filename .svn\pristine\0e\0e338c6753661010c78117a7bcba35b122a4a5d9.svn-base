package com.shunwang.basepassport.asynctask;

import com.shunwang.baseStone.context.DomainContext;
import com.shunwang.util.StringUtil;
import com.shunwang.util.encrypt.Md5Encrypt;
import com.shunwang.util.net.HttpUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * 日志作业父类
 *
 * <AUTHOR>
 * @since 2013-08-29
 */
public abstract class BaseLogJob implements Runnable {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    protected static final int SUCCESS = 0;

    protected static final String FIELD_SEP = ",";

    protected static final String TIME_FORMAT ="yyyy-MM-dd HH:mm:ss.SSS";

    //连接超时45秒, 读socket超时90秒(不能设的太长,否则当日志服务器不可用时
    //长时间的等待会造成大量的资源消耗
    protected static final int CONN_TIME_OUT = 1000 * 45;

    protected static final int READ_TIME_OUT = 1000 * 90;

    protected static final String LOG_SERVER_URL = DomainContext.getLogServer() + "/log/commonLogHandler.do";

    private static final String MD5_SALT = "*#^25*12ayhc!@#"; // MD5加密salt

    /**
     * 以POST请求发送日志到日志服务器
     * @param logType 日志类型
     * @param logContent 日志内容，类似CSV格式，逗号和反斜杠需要转义
     * @return 返回xml格式的响应
     * @throws Exception
     */
    public String doPost(String logType, String logContent) throws Exception {
        if (logger.isDebugEnabled()) {
            logger.debug("Send to the Log server[" + LOG_SERVER_URL + "]: [" + logType + "] { " + logContent + " }");
        }

        Map<String, String> params = new HashMap<String, String>();
        params.put("logType", logType);
        params.put("logText", logContent);
        params.put("sign", Md5Encrypt.encrypt(logType + logContent + MD5_SALT));

        String response = HttpUtil.doPost(LOG_SERVER_URL, params, "UTF-8", CONN_TIME_OUT, READ_TIME_OUT);

        if (logger.isDebugEnabled()) {
            logger.debug("Log server response: " + response);
        }

        return response;
    }

    /**
     * 转义特殊字符
     * @param value 包含特殊字符的字符串
     * @return 转义后的字符串
     */
    protected String escape(String value) {
        if(StringUtil.isBlank(value)) {
            return "";
        }
        value = value.replace("\\", "\\\\");
        value = value.replace(",", "\\~");
        value = value.replace("\n", "");
        return value;
    }

}
