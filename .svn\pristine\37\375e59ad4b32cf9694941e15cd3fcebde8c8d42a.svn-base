package com.shunwang.baseStone.cache.keygenerator;

import com.shunwang.baseStone.cache.CacheKeyConstant;
import org.jetbrains.annotations.NotNull;

import java.lang.reflect.Method;

/**
 * OutOauthDirKey 相关缓存
 *
 * <AUTHOR>
 * @date 2019/3/12
 **/
public class ServiceUserOutInterfaceKeyGenerator extends BaseKeyGenerator {

    @NotNull
    @Override
    public Object generate(@NotNull Object target, @NotNull Method method, @NotNull Object... params) {
        return generate(CacheKeyConstant.EhcacheKey.SERVER_USER_OUT_INTERFACE, params);
    }
}
