package com.shunwang.baseStone.cache;

import com.shunwang.baseStone.jms.mode.Mode;

/**
 * 为了各个项目之间缓存key不会冲突，
 * 因此每个缓存key都是以对应的项目名作为开头
 * 例: base项目的缓存key为 base_xxx
 */
public class CacheKeyConstant {

    public static final String CACHE_SPLIT = "_";

    public static final String SEPARATOR = "|";

    public static class LoginConfigConstants {
        /**
         * 商户登录可使用功能配置缓存
         */
        public static final String BUS_LOGIN_ELEMENT_CACHE_KEY = "base_login_config_";
    }

    public static class OutOauthDirConstants {

        /**
         * 商户外部接入缓存
         */
        public static final String BUS_OUT_OAUTH_DIR_CACHE_KEY = "base_out_oauth_dir_";
    }

    public static class SiteInterfaceConstants {
        /**
         * 商户分配接口配置
         */
        public static final String BUS_SITE_INTERFACE_CACHE_KEY = "base_site_interface_";
    }

    public static class CssConstants {
        /**
         * CSS管理
         */
        public static final String BUS_CSS_CONFIG_CACHE_KEY = "base_css_config_";
    }

    public static class UserOutInterfaceConstants {
        /**
         * 用户名前缀
         */
        public static final String BASE_USER_OUT_INTERFACE_KEY = "base_user_out_interface";
        public static final String BUS_ALL_PREFIX_NAME_KEY = "base_user_out_interface_all_prefix_name";
    }

    public static class SysConfigConstants {
        /**
         * 图片url
         */
        public static final String BUS_IMAGE_URL_CACHE_KEY = "base_image_url";

        /**
         * 图片路径
         */
        public static final String BUS_IMAGE_DIR_CACHE_KEY = "base_image_dir";

        /**
         * 加密图片路径
         */
        public static final String BUS_ENCRYPT_IMAGE_DIR_CACHE_KEY = "base_encrypt_image_dir";

        /**
         * 默认短信内容
         */
        public static final String BUS_MOBILE_DEFAULT_CACHE_KEY = "base_mobile_default_msg";

        /**
         * 有效手机号码段
         */
        public static final String BUS_VALID_PHONE_CACHE_KEY = "base_valid_phone_num";
        /**
         * 极验总开关
         */
        public static final String BUS_GT_SWITCH_CACHE_KEY = "base_gt_switch";
        /**
         * 极验异常自动关闭阀值
         */
        public static final String BUS_GT_THRESHOLD_CACHE_KEY = "base_gt_threshold";
    }

    public static class ResourceConstants {
        /**
         * config_resources的单条记录缓存前缀
         */
        public static final String RESOURCES_PREFIX = "back_resource_";
    }

    public static class GeetestLib {

        /**
         * 极验验证API服务状态Session Key
         */
        public final static String SSO_STATUS_KEY = "sso_gt_server_status_";

        /**
         * 极验验证API服务状态Session Key
         */
        public final static String PASSPORT_STATUS_KEY = "passport_gt_server_status_";

        /**
         * 极验异常报警并发锁
         */
        public final static String GT_ALARM_LOCK = "gt_alarm_lock";

    }

    public class BannerConstant {
        /**
         * 蝌蚪服务
         **/
        public static final String LOGIN_SERVICE = "pageLoginLetterHead";
        /**
         * 蝌蚪支付
         **/
        public static final String LOGIN_PAY = "pageLoginHeadGame";
        public static final String LOGIN_SERVICE_PREVIEW = "pageLoginLetterHead_pre";
        public static final String LOGIN_PAY_PREVIEW = "pageLoginHeadGame_pre";
        public static final String LOGIN_SERVICE_PREVIEW_TMP = "pageLoginLetterHead_tmp";
        public static final String LOGIN_PAY_PREVIEW_TMP = "pageLoginHeadGame_tmp";
        public static final String _LOGIN_SERVICE = "login_service";
        public static final String _LOGIN_PAY = "login_pay";
    }

    public class BaseEditBackgroundConsts {
        public static final String ACTIVITY_KEY = "base_activity_key_";
    }

    public class Passport {
        public static final String PASSPORT_USER_LAST_LOGON_LOG_CACHE_KEY = "passport_last_logon_log_cache_";
        public static final String PASSPORT_BIND_KEY = "passport_bind_";
        public static final String PASSPORT_SEND_SECOND_KEY = "passport_send_second_";
        public static final String CHANGE = "Change";
        public static final String BY = "By";
        public static final String TOKENID = "Tokenid";
        /**
         * 存放接入方免登跳转到注销页面所带的回调页面
         * key:MEMBER_CANCEL_PAGE_URL + memberId,value:pageUrl
         */
        public static final String MEMBER_CANCEL_PAGE_URL = "member_cancel_page_url_";

    }

    public class SSO {
        /**
         * 外部站点登录信息
         */
        public final static String OUT_OAUTH_KEY = "OUT_OAUTH_KEY";
        /**
         * time logon
         */
        public final static String LTIME_KEY = "sso_LTime_";

        public final static String TOKENID_PREFIX = "SSO_SMS_QUICK_LOGIN_";


        public final static String SIMPLE_IMPORT_LOCK = "C_";

        public final static String MOBILE_TOKENID_PREFIX = "SSO_MOBILE_LOGIN_";

        public final static String LOGIN_PERR = "login_PErr_";

        /**
         * 用户注册信息缓存
         */
        public static final String USER_MEMBER_INFO = "sso_member_cache_";

        public static final String WX_REFRESH_TOKEN_SCHEDULE = "wx_refresh_token_schedule";
        public static final String WX_REFRESH_TOKEN_TASK = "wx_refresh_token_task_";

        public static final String WX_OAUTH = "wx_oauth_";
        public static final String WX_OAUTH_TOKEN = "wx_oauth_token_";
        public static final String WX_REPLY = "wx_appid_reply_";

        public static final String SERVICE_NOTIFY_TASK_LOCK_KEY = "service_notify_task_lock_";

        /**
         * 用于用户登录写登录日志加锁，微信公众号存在多次回调，会重复写登录日志
         * key+memberId
         */
        public static final String LOGIN_LOG_LOCK_ = "login_log_lock_";

        /**
         * 用于微信异常报警缓存
         */
        public static final String WX_ALARM_CACHE = "wx_alarm_cache_";

        /**
         * 登录用户缓存，记录登录的链路
         */
        public static final String LOGIN_USER_SESSION = "login_user_session_";

        /**
         * 内部场景值缓存,比如计费上机免登授权二维码页面
         */
        public static final String INNER_SCAN = "inner_scan_";
    }

    public class PassportAppeal {

        public final static String ACTIVE_NO_EMAIL = "activeNoEamil_2011090321135428";
        public final static String ACTIVE_NO_MOBILE = "activeMobile_2011090321245698";
        /**
         * 用户申述key,防止重复提交
         */
        public static final String FIND_APPEAL_MSG = "passport_lock_find_appeal_msg_";

        /**
         * 找回密码
         */
        public static final String TOKENID_PREFIX = "PASSPORT_HTML5_FIND_PASSPORT_";

    }

    public class PassportActu {

        public final static String ACTU_TOKENID = "passport_actu_token_id_";
        /**
         * 实名认证找回密码
         */
        public static final String FIND_ACTU_MSG = "passport_lock_find_actu_msg_";
        public static final String CHANGE_EMAIL_TOKENID = "passport_change_email_tokenid_";
        public static final String CHANGE_PHONE_TOKENID = "passport_change_phone_num_tokenid_";

        /**
         * 百度OCR常量
         */
        public static final String BAIDU_OCR_LIMIT_PRE = "passport_baidu_ocr_limit_";
        public static final String BAIDU_OCR_INFO_PRE = "passport_baidu_ocr_info_";
        public static final String BAIDU_OCR_ACCESS_TOKEN = "passport_baidu_ocr_access_token";
        public static final String BAIDU_OCR_IDCARD_FRONT = "passport_baidu_ocr_idcard_front_";
        public static final String BAIDU_OCR_IDCARD_BACK = "passport_baidu_ocr_idcard_back_";


        public static final String OCR = "OCR_";
        public static final String OCR_CAFE = "OCR_cafeChange_";

    }

    public class InterfaceToken {

        public static final String ACCESS_TOKEN_PREFIX = "interface_accessToken_";

        public static final String REFRESH_TOKEN_PREFIX = "refreshToken";

        public static final String CACHE_KEY_LOGIN_TOKEN = "interface_loginToken_";
        /**
         * uuid加前缀
         */
        public static final String LOGIN_TICKET = "interface_login_ticket_";

        public static final String AUTHORIZED_TOKEN_PREFIX = "interface_authorized_token_";

        public static final String AUTHORIZED_CODE_PREFIX = "interface_authorized_code_";

        /**
         * 单账号,uuid加前缀
         */
        public static final String SINGLE_ACCOUNT_SIGN = "interface_single_account_sign_";

        /**
         * 密码二次校验,uuid加前缀
         */
        public static final String VALID_SIGN = "interface_valid_sign_";

        /**
         * 对应用户数据的key
         */
        public static final String MOBILE_USERINFO__KEY_ = "inteface_mobile_userinfo__key_";


        public static final String SHORT_URL = "interface_short_url_";
        /**
         * 一键登录场景值key
         */
        public static final String ONE_LOGIN_SCENE_KEY = "scene_one_login_";


    }

    public class InterfaceBussKey {
        /**
         * 当前密钥放置缓存的key
         */
        public static final String CACHE_KEY_BUSSKEY_CUR = "interface_bussKey_cur_";
        /**
         * 之前密钥放置缓存的key
         */
        public static final String CACHE_KEY_BUSSKEY_PRE = "interface_bussKey_pre_";

        public static final String CACHE_KEY_DYNAMIKEY = "interface_dynamicKey_";

        public static final String SENDER_CACHE_KEY = "interface_sender_";


    }

    public class InterfaceSmsKey {
        /**
         * 动态验证码缓存key
         * interface接口2-1,2-2
         * sso双活内部接口
         */
        public static final String SMS_DYNAMIC_PWD = "interface_sms_dyna_pwd_";
        /**
         * 普通验证码缓存key
         */
        public static final String SMS_SEND_PLAIN = "interface_sms_send_plain_";

        /**
         * interface 9,10接口短信验证码缓存key
         */
        public static final String SMS_CHECK_CODE = "interface_sms_code_";

        /**
         * 用于smsConfig 配置的缓存 key+siteId+businessType
         */
        public static final String SMS_CONFIG_CACHE = "sms_config_cache_";
        /**
         * 用于emailConfig 配置的缓存 key+siteId+businessType
         */
        public static final String EMAIL_CONFIG_CACHE = "email_config_cache_";
    }

    public class ConfigResourcesConstants {

        /**
         * IP黑名单配置
         */
        public static final String PERMIT_IP_CONFIG = "permitIpConfig";

        /**
         * 通用配置
         */
        public static final String COMMON_CONFIG = "commonConfig";

        /**跳弱密码修改站点配置**/
        public static final String JUMP_WEAK_PASSWORD_ERROR_SITES = "jumpWeakPasswordErrorSites";

        /**微信授权登录授权显示信息配置 - 临时 后续采用新方案**/
        public static final String WX_AUTH_LOGIN_APPID_SHOW_CONFIG = "wxAuthLoginAppIdShowConfig";
        /**
         * 手机登录用户协议自动选中开关及白名单
         */
        public static final String AGREEMENT_AUTO_CHECK_SWITCH = "agreementAutoCheckSwitch";
        public static final String AGREEMENT_AUTO_CHECK_SITE_ID_WHITELIST = "agreementAutoCheckSiteIdWhitelist";

        public static final String SMSCODE_LIMIT_CONFIG = "SmsCodeLimitConfig";
        public static final String SMSCODE_LIMIT_SITEID = "SmsCodeLimitSiteId";
        public static final String SMSCODE_LIMIT_VALID_PERIOD = "SmsCodeLimitValidPeriod";
        public static final String SMSCODE_LIMIT_QUANTITY = "SmsCodeLimitQuantity";
        public static final String SMSCODE_LIMIT_TERMINAL = "SmsCodeLimitTerminal";

        public static final String QRCODE_LOGIN_CHECK_TIME_INTERVAL = "qrCodeLoginCheckTimeInterval";


        /**
         * 手机充值SDK相关配置
         */
        public static final String MOBILE_RECHARGE_SDK_CONFIG = "mobileRechargeSdkConfig";

        public static final String MOBILE_RECHARGE_SDK_CONFIG_ACCESS_NAME = "accessTokenTimeOutInterval";

        public static final String MOBILE_RECHARGE_SDK_CONFIG_REFRESH_NAME = "refreshTokenTimeOutInterval";

        /**
         * OCR 配置
         */
        public static final String BAIDU_OCR_CONFIG_KEY = "OCRConfig";
        public static final String BAIDU_OCR_INTERFACE_PARAM_APIKEY = "apikey";
        public static final String BAIDU_OCR_INTERFACE_PARAM_APPID = "appid";
        public static final String BAIDU_OCR_INTERFACE_PARAM_SECRETKEY = "secretkey";

        public static final String SAFE_ITEMS_SCORE_CONFIG_KEY = "safeItemsScoreConfig";
        public static final String SAFE_ITEMS_SCORE_CONFIG_INIT_SCORE = "initScore";
        public static final String SAFE_ITEMS_SCORE_CONFIG_BIND_EMAIL = "emailBind";
        public static final String SAFE_ITEMS_SCORE_CONFIG_BIND_MOBILE = "mobileBind";
        public static final String SAFE_ITEMS_SCORE_CONFIG_LOGIN_PROTECTION = "loginProtection";
        public static final String SAFE_ITEMS_SCORE_CONFIG_NO_PASSWORD = "noPassword";
        public static final String SAFE_ITEMS_SCORE_CONFIG_BIND_QUESTION = "questionBind";
        public static final String SAFE_ITEMS_SCORE_CONFIG_REALNAME_AUTH = "realNameAuth";
        public static final String SAFE_ITEMS_SCORE_CONFIG_SAFENOTICE = "safeNotice";

        /**
         * 短信配置
         */
        public static final String TYPE_SMS_CONFIG = "smsConfig";
        /**
         * 发送短信内容的key
         */
        public static final String SMS_CONTENT = "smsContent";

        /**
         * 动态密码有效时间
         */
        public static final String VALID_TIME = "validTime";

        /**
         * 绑定手机号码的个数
         */
        public static final String MOBILE_BINDER_LIMIT = "mobileBinderLimit";

        /**
         * 跳转地址域名白名单
         */
        public static final String URL_DOMAINS_WHITE_LIST = "urlDomainsWhiteList";
        /**
         * xss传入参数白名单（白名单内的参数不过滤危险字符）
         */
        public static final String PARAMS_WHITE_LIST_FOR_XSS = "paramsWhiteListForXss";

        /**
         * xss值黑名单
         */
        public static final String VALUE_DANGER_LIST_FOR_XSS = "valueDangerListForXss";
        /**
         * 危险字符黑名单
         */
        public static final String DANGER_CHAR_LIST_FOR_XSS = "dangerCharListForXss";
        /**
         * 危险字符限制个数
         */
        public static final String DANGER_CHAR_LIMIT_FOR_XSS = "dangerCharLimitForXss";
        /**
         * 危险字符开关
         */
        public static final String DANGER_CHAR_IS_OPEN = "dangerCharIsOpen";

        /**
         * xss传入参数白名单（白名单内的参数不过滤危险字符）
         */
        public static final String XSS_IS_OPEN = "xssIsOpen";
        /**
         * 登录日志上报开关
         */
        public static final String LOGON_REPORT_SWITCH = "saveLoginLogSwitch";

        /**
         * 动态验证码配置
         */
        public static final String  TYPE_DYNAMIC_PWD_CONFIG = "dynamicPwdConfig";
        /**
         * 发送短信内容的key
         */
        public static final String DYNAMIC_PWD_SMS_CONTENT = "dynamicPwdSmsContent";

        /**
         * 注销帐号通知已接入注销功能的业务方
         */
        public static final String MEMBER_CANCEL_NOTICE_URLS = "memberCancelNoticeUrls";
        /**
         * 撤销注销帐号通知已接入注销功能的业务方
         */
        public static final String MEMBER_REVOKE_CANCEL_NOTICE_URLS = "memberRevokeCancelNoticeUrls";
        /**
         * 真实注销帐号通知已接入注销功能的业务方
         */
        public static final String MEMBER_REAL_CANCEL_NOTICE_URLS = "memberRealCancelNoticeUrls";
        /**
         * 修改手机通知已接入的业务方
         */
        public static final String MOBILE_CHANGE_NOTICE_URLS = "mobileChangeNoticeUrls";
        /**
         * 解绑手机帐号通知已接入的业务方
         */
        public static final String MOBILE_UNBIND_NOTICE_URLS = "mobileUnbindNoticeUrls";

        /**
         * 限制登录的密码错误次数
         */
        public static final String ERR_TIMES_OF_LIMIT_LOGIN = "errTimesOfLimitLogin";
        /**
         * 密码错误导致限制登录时长
         */
        public static final String LIMIT_SENCONDS_OF_LIMIT_LOGIN = "limitSecondsOfLimitLogin";

        /**
         * 防沉迷配置
         */
        public static final String ANTI_ADDITION_CONFIG = "antiAdditionConfig";

        /**
         * 防沉迷开关
         */
        public static final String ANTI_ADDITION_CONFIG_SWITCH = "antiAdditionSwitch";

        /**
         * 防沉迷报警开关
         */
        public static final String ANTI_ADDITION_CONFIG_ALERT_SWITCH = "antiAdditionAlertSwitch";

        /**
         * 防沉迷一分钟异常预警值
         */
        public static final String ANTI_ADDITION_CONFIG_ALERT_THRESHOLD = "antiAdditionAlertThreshold";

        /**
         * 防沉迷预警报警邮件
         */
        public static final String ANTI_ADDITION_CONFIG_ALERT_EMAILS = "antiAdditionAlertEmails";

        /**
         * 大数据接口防沉迷报警开关
         */
        public static final String ANTI_ADDITION_CONFIG_DC_ALERT_SWITCH = "antiAdditionDcAlertSwitch";

        /**
         * 大数据接口防沉迷一分钟异常预警值
         */
        public static final String ANTI_ADDITION_CONFIG_DC_ALERT_THRESHOLD = "antiAdditionDcAlertThreshold";


        /**
         * 创蓝30分钟调用预警值
         */
        public static final String ANTI_ADDITION_CONFIG_ALERT_INVOKE_THRESHOLD = "antiAdditionAlertInvokeThreshold";

        /**
         * 调休的假期
         */
        public static final String ANTI_ADDITION_CONFIG_HOLIDAY = "antiAdditionHoliday";

        /**
         * 周几
         */
        public static final String ANTI_ADDITION_CONFIG_DAY_OF_WEEK = "antiAdditionDayOfWeek";

        /**
         * 未成年人可玩游戏时间段
         */
        public static final String ANTI_ADDITION_CONFIG_PERIOD = "antiAdditionPeriod";
        /**
         * 防沉迷预警报警手机
         */
        public static final String ANTI_ADDITION_CONFIG_ALERT_MOBILES = "antiAdditionAlertMobiles";
        /**
         * 防沉迷预警报警短信时间间隔
         */
        public static final String ANTI_ADDITION_CONFIG_ALERT_SMS_INTERVAL = "antiAdditionAlertSmsInterval";

        /**
         * 防沉迷路由开关 国家接口超时或异常时是否走创蓝接口
         */
        public static final String ANTI_ADDITION_CONFIG_ROUTE_SWITCH = "antiAdditionRouteSwitch";
        /**
         * 防沉迷路由异常码 国家接口对应异常时是走创蓝接口
         */
        public static final String ANTI_ADDITION_CONFIG_ROUTE_ERR_CODES = "antiAdditionRouteErrCodes";
        /**
         * 防沉迷国家开关
         */
        public static final String ANTI_ADDITION_CONFIG_GAME_ALERT_SWITCH = "antiAdditionGameAlertSwitch";

        /**
         * 防沉迷国家报警开关
         */
        public static final String ANTI_ADDITION_CONFIG_GAME_ALERT_THRESHOLD = "antiAdditionGameAlertThreshold";
        /**
         * 国家防沉迷异常时间窗大小（分钟）
         */
        public static final String ANTI_ADDITION_CONFIG_GAME_TIME_WINDOW_SIZE = "antiAdditionConfigGameTimeWindowSize";
        /**
         * 防沉迷xx时间内姓名认证次数限制
         */
        public static final String ANIT_ADDITION_CONFIG_GAME_REAL_NAME_COUNT_LIMIT = "gameRealNameCountLimit";
        /**
         * 防沉迷姓名认证限制时间阀值
         */
        public static final String ANIT_ADDITION_CONFIG_GAME_REAL_NAME_TIME_LIMIT = "gameRealNameTimeLimit";
        /**
         * 国家防沉迷站点调用时间窗大小（分钟）
         */
        public static final String ANTI_ADDITION_CONFIG_GAME_SITE_TIME_WINDOW_SIZE = "antiGameSiteTimeWindowSize";
        /**
         * 防沉迷国家站点累计调用次数阀值
         */
        public static final String ANTI_ADDITION_CONFIG_GAME_SITE_ALERT_THRESHOLD = "antiGameAlertThreshold";
        /**
         * 防沉迷国家站点开关
         */
        public static final String ANTI_ADDITION_CONFIG_GAME_SITE_ALERT_SWITCH = "antiGameSiteAlertSwitch";
        /**
         * 防沉迷国家站点黑名单
         */
        public static final String ANTI_ADDITION_CONFIG_GAME_SITE_BLACK_LIST = "antiGameSiteBlackList";


        /**
         * 大数据统一采集配置
         */
        public static final String DATA_REPORT_CONFIG = "dataReportConfig";
        public static final String DATA_REPORT_SWITCH = "dataReportSwitch";

        /**
         * 风控配置
         */
        public static final String RISK_CONFIG = "riskConfig";

        public static final String RISK_SSO_LOGIN_IS_OPEN = "ssoLoginIsOpen";
        public static final String RISK_INTERFACE_LOGIN_IS_OPEN = "interfaceLoginIsOpen";
        public static final String RISK_INTERFACE_REG_IS_OPEN = "interfaceRegIsOpen";
        public static final String RISK_INTERFACE_MODIFY_IS_OPEN = "interfaceModifyIsOpen";
        public static final String RISK_INTERFACE_IMAGE_IS_OPEN = "interfaceImageIsOpen";
        public static final String RISK_INTERFACE_QUERY_USER_BY_MOBILE_IS_OPEN = "interfaceQueryUserByMobileIsOpen";
        /** 登录上报风控 **/
        public static final String RISK_SSO_LOGIN_REPORT_IS_OPEN = "ssoLoginReportIsOpen";
        /** 登录方式上报排除类型配置 **/
        public static final String RISK_SSO_LOGIN_REPORT_EXCLUDE_MAP_CONFIG = "ssoLoginReportExcludeMapConfig";

        /**
         * 计费上机业务配置
         */
        public static final String BAR_LOGIN_CONFIG = "barLoginConfig";
        /**
         * 请求黑卡超时配置
         */
        public static final String REQUEST_BLACK_ID_CARD_TIME_OUT = "requestBlackIdCardTimeOut";
        /**
         * 微信上机开关
         */
        public static final String BAR_LOGIN_IS_OPEN = "barLoginIsOpen";
        /**
         * 微信返回身份证绑定消息内容
         */
        public static final String BAR_LOGIN_WEIXIN_MSG_BIND = "barLoginWeiXinMsgBind";
        /**
         * 微信返回上机成功消息内容
         */
        public static final String BAR_LOGIN_WEIXIN_MSG_SUCCESS = "barLoginWeiXinMsgSuccess";
        /**
         * 微信上机默认公众号appid
         */
        public static final String BAR_LOGIN_DEFAULT_APPID = "barLoginDefaultAppid";
        /**
         * 微信上机默认tips
         */
        public static final String BAR_LOGIN_DEFAULT_TIPS = "barLoginDefaultTips";

        /**
         * 微信上机默认tips
         */
        public static final String BAR_LOGIN_SERVER_EXPIRE_SECONDS = "serverExpireSeconds";

        /**
         * 电竞酒店计费上机业务配置
         */
        public static final String HOTEL_LOGIN_CONFIG = "hotelLoginConfig";
        /**星云企微获客码地址**/
        public static final String XING_YUN_WXWORK_CONTACT_URL = "xingyunWxWorkContactUrl";
        /**星云企微开关**/
        public static final String XING_YUN_WXWORK_SWITCH = "xingyunWxWorkSwitch";
        /**不走企微网吧列表**/
        public static final String NO_ROUTE_WXWORK_NET_BAR_LIST = "noRouteWxworkNetBarList";
        /**
         * 微信上机开关
         */
        public static final String HOTEL_LOGIN_IS_OPEN = "loginIsOpen";

        /**
         * sso是否发送扫码上机绑定身份证微信消息开关
         */
        public static final String SSO_BIND_IDCARD_WX_MSG_IS_OPEN = "ssoBindIdCardWxMsgSwitch";
        /**
         * 电竞酒店微信返回上机成功消息内容
         */
        public static final String HOTEL_LOGIN_WEIXIN_MSG_SUCCESS = "loginWeiXinMsgSuccess";
        /**
         * 电竞酒店微信上机默认公众号appid
         */
        public static final String HOTEL_LOGIN_DEFAULT_APPID = "loginDefaultAppid";
        /**
         * 电竞酒店微信上机默认tips
         */
        public static final String HOTEL_LOGIN_DEFAULT_TIPS = "loginDefaultTips";
        /**
         * 电竞酒店微信上机认证地址
         */
        public static final String HOTEL_LOGIN_OAUTH_URL = "loginOauthUrl";
        /**
         * 电竞酒店微信上机二维码过期时间
         */
        public static final String HOTEL_LOGIN_SERVER_EXPIRE_SECONDS = "serverExpireSeconds";

        /**
         * 微信接口上限异常报警手机
         */
        public static final String WEIXIN_DAILY_LIMIT_ALARM_NUMS = "weixinDailyLimitAlarmNums";
        /**
         * 微信接口异常报警缓存时间
         */
        public static final String WEIXIN_DAILY_LIMIT_ALARM_CACHE_SECONDS = "weixinDailyLimitAlarmCacheSeconds";
        /**
         * 微信登录回复消息配置
         */
        public static final String WEIXIN_LOGIN_REPLY_MSG = "weixinLoginReplyMsg";
        /**
         * 微信登录，多账号回复消息配置
         */
        public static final String WEIXIN_LOGIN_REPLY_MSG_MULTIPLE = "weixinLoginReplyMsgMultiple";
        /**
         * 微信登录，单账号回复消息配置
         */
        public static final String WEIXIN_LOGIN_REPLY_MSG_SINGLE = "weixinLoginReplyMsgSingle";
        /**
         * 微信上机开关
         */
        public static final String SEND_POLL_WX_MSG_IS_OPEN = "sendPollWxMsgSwitch";
        /**
         * 微信业务扫码回复消息配置
         */
        public static final String WEIXIN_BUSINESS_SACAN_REPLAY_MSG = "weixinBusinessScanReplayMsg";

        /**
         * AI业务相关配置
         */
        public static final String AI_BUSINESS_CONFIG = "aiConfig";
        /**
         * 跳转Ai小程序绑定手机号发送微信小卡片消息的消息id
         */
        public static final String AI_WEIXIN_CARD_MSG = "aiWeixinCardMsg";

        /**
         * 计费免登配置
         */
        public static final String NET_BAR_FREE_LOGIN_CONFIG = "netBarFreeLogin";
        /**游客免登站点配置**/
        public static final String GUEST_FREE_LOGIN_SITE_ID = "guestFreeLoginSiteIds";
        /**默认微信appid**/
        public static final String DEFAULT_APPID = "defaultAppid";
        /**默认微信小程序appid**/
        public static final String DEFAULT_APPID_MINI = "defaultAppidMini";
        public static final String DEFAULT_APPID_MINI_PATH = "defaultAppidMiniPath";
        public static final String DEFAULT_FROM_SITE_ID_FOR_MULTIPLE = "defaultFromSiteIdForMultiple";
        public static final String NET_BAR_TO_MULTIPLE_SWITCH = "netBarToMultipleSwitch";
        /**
         * 身份证导入接口需要检测黑卡的siteId
         */
        public static final String CHECK_BLACK_CARD_SITE_ID = "checkBlackCardSiteId";
        /**
         * 卡片消息id配置
         */
        public static final String CARD_MSG_ID = "cardMsgIds";
        /** 登录成功微信模版消息id **/
        public static final String AUTO_LOGIN_TEMPLATE_MSG_ID = "autoLoginTemplateMsgId";
        /** 登录成功多账号微信模版消息id **/
        public static final String AUTO_LOGIN_MULTIPLE_TEMPLATE_MSG_ID = "autoLoginMultipleTemplateMsgId";
        /** 免登数据上报开关 **/
        public static final String FREE_LOGIN_REPORT_SWITCH = "freeLoginReportSwitch";
        /** 企微上报开关 **/
        public static final String WX_WORK_REPORT_SWITCH = "wxWorkReportSwitch";
        /** 手机绑定类型 默认小程序卡片消息 **/
        public static final String SINGLE_BIND_TYPE = "singleBindType";
        /** 网吧授权开关 默认开启 **/
        public static final String BAR_AUTH_SWITCH = "barAuthSwitch";
        /** 网吧免登协议开关 默认关闭 **/
        public static final String UN_AUTH_AGREEMENT_SWITCH = "unAuthAgreementSwitch";

        public static final String UN_AUTH_LIMIT_FREE_LOGIN_SWITCH = "unAuthLimitFreeLoginSwitch";
        /**
         * 计费多账号免登开关
         */
        public static final String UN_AUTH_FREE_LOGIN_FOR_MULTIPLE_ACCOUNT = "unAuthFreeLoginForMultipleAccount";

        /**
         * 跳转免登非授权检测开关
         */
        public static final String JUMP_FREE_LOGIN_UN_AUTH_CHECK_SWITCH = "jumpFreeLoginUnAuthLoginCheckSwitch";
        /** 多账号免登是否需要关注公众号开关 默认关闭 **/
        public static final String SUB_WX_FOR_MULTIPLE_FREE_LOGIN_SWITCH = "subWxForMultipleFreeLoginSwitch";

        /**原base配置**/
        public static final String TYPE_BASE_CONFIG = "baseConfig";
        /**实名变更通过短信通知消息模版**/
        public static final String BASE_CONFIG_ACTU_CHANGE_PASS_SMS_MSG = "actuChangeCheckPassMobileMsg";
        /**实名变更拒绝短信通知消息模版**/
        public static final String BASE_CONFIG_ACTU_CHANGE_REFUSE_SMS_MSG = "actuChangeCheckRefuseMobileMsg";
        /**实名拒绝短信通知消息**/
        public static final String BASE_CONFIG_ACTU_REFUSE_SMS_MSG = "actuCheckRefuseMobileMsg";
        /**实名通过短信通知消息**/
        public static final String BASE_CONFIG_ACTU_PASS_SMS_MSG = "actuCheckPassMobileMsg";
        /**银行卡预留手机号开关**/
        public static final String BASE_CONFIG_BANK_RESERVE_MOBILE_SWITCH = "bankReservePhoneSwitch";
        /**图片加密存储路径**/
        public static final String BASE_CONFIG_ENCRYPT_IMG_DIR = "encryptImgDir";
        /**极验开关**/
        public static final String BASE_CONFIG_GT_SWITCH = "gtSwitch";
        /**极验异常报警邮箱**/
        public static final String BASE_CONFIG_GT_ALARM_EMAILS = "gtEmails";
        /**极验异常自动关闭阀值**/
        public static final String BASE_CONFIG_GT_ERROR_AUTO_CLOSE_THRESHOLD = "gtThreshold";
        /**图片访问域名**/
        public static final String BASE_CONFIG_IMG_URL = "imgUrl";
        /**图片存储路径**/
        public static final String BASE_CONFIG_IMG_DIR = "imgDir";
        /**短信消息默认模版**/
        public static final String BASE_CONFIG_MOBILE_DEFAULT_MSG = "mobileDefualtMsg";
        /**注册短信消息默认模版**/
        public static final String BASE_CONFIG_MOBILE_REG_MSG = "mobileRegMsg";
        /**sso登录页广告开关*/
        public static final String BASE_CONFIG_PASSPORT_SSO_AD_BG_SWITCH = "passportSSOadBgIsOpen";
        /**sso广告地址**/
        public static final String BASE_CONFIG_PASSPORT_SSO_AD_URL = "passportSSOAdUrl";
        /**密保问题修改邮件通知模版**/
        public static final String BASE_CONFIG_PRO_CHANGE_EMAIL_KEY = "PRO_CHANGE_EMAIL_KEY";
        /**密保问题修改短信通知模版**/
        public static final String BASE_CONFIG_PRO_CHANGE_MOBILE_KEY = "PRO_CHANGE_MOBILE_KEY";
        /**密保问题设置邮件通知模版**/
        public static final String BASE_CONFIG_PRO_SET_EMAIL_KEY = "PRO_SET_EMAIL_KEY";
        /**密保问题设置短信通知模版**/
        public static final String BASE_CONFIG_PRO_SET_MOBILE_KEY = "PRO_SET_MOBILE_KEY";
        /**密码修改邮件通知模版**/
        public static final String BASE_CONFIG_PWD_CHANGE_EMAIL_KEY = "PWD_CHANGE_EMAIL_KEY";
        /**手机号段校验配置**/
        public static final String BASE_CONFIG_VALID_MOBILE_NUM = "validPhoneNum";
        /**密码修改短信通知模版**/
        public static final String BASE_CONFIG_PWD_CHANGE_MOBILE_KEY = "PWD_CHANGE_MOBILE_KEY";
    }


    public class Back {
        /**
         * 登录来源在缓存中的KEY
         */
        public final static String LOGIN_TYPE_MAP_CACHE_KEY = "LOGIN_TYPE_MAP";

        /**
         * business商户信息再缓存中的key
         */
        public final static String BUSINESS_MAP_CACHE_KEY = "BUSINESS_MAP_CACHE_KEY";

        /**
         * 地址信息在缓存中的key
         */
        public final static String AREA_MAP_CACHE_KEY = "AREA_MAP_CACHE_KEY";

        /**
         * UserRight类 在Cached中key的前缀
         */
        public static final String USER_RIGHT = "basePassport_user_right_";


    }


    public class Agreement {
        /**
         * 协议
         */
        public static final String AGREEMENT = "agreement";

        /**
         * 顺网通行证隐私政策
         */
        public static final String PRIVACY = "privacy";

        /**
         * 用户注册服务协议
         */
        public static final String USER_REGISTER = "register";
        /**
         * 顺网通行证注销协议
         */
        public static final String MEMBER_CANCEL = "member_cancel";

        /**
         * 顺网通行证实名认证服务协议
         */
        public static final String ACTUALITY = "actuality";

        /**
         * 顺网通行证防沉迷认证服务协议
         */
        public static final String ANTI_ADDITION = "anti_addition";

        /**
         * 顺网通行证实名认证变更服务协议
         */
        public static final String ACTUALITY_CHANGE = "actuality_change";

        public static final String FREE_LOGIN_AUTH = "free_login_auth";
    }

    public class SiteId {

        public static final String PASSPORT = "passport";

        public static final String PAY = "pay";
    }

    public class Platform {

        public static final String WEB = "web";

        public static final String h5 = "h5";

    }

    public class Css {
        public static final String LOGIN = "login";
        public static final String HTML5_CSS = "html5Css";
        public static final String OUT_OAUTH_CSS = "outOauthCss";
        public static final String PRIVACY_AGREEMENT = "privacyAgreement";
        public static final String USER_AGREEMENT = "userAgreement";
    }

    public enum EhcacheKey{
        CONFIG_API_APP("config_api_app_", "站点配置, 拼接appId", Mode.MODE_EXACT, 1),
        CONFIG_API_APP_LIST("config_api_app_list", "站点配置列表", Mode.MODE_EXACT, 0),
        CONFIG_APPID_REPLY("config_appid_reply_", "微信消息回复配置,拼接appid,mode,bindState", Mode.MODE_EXACT, 3),
        CONFIG_APP_LOGIN_ELEMENT("config_app_login_element_", "站点登录配置，拼接appid,elementName", Mode.MODE_EXACT, 2),
        CONFIG_BUSS_LINE("config_buss_line_", "业务线配置，拼接业务线id", Mode.MODE_EXACT, 1),
        CONFIG_BUSS_LINE_LOGIN_ELEMENT("config_buss_line_login_element_", "业务线登录配置，拼接businessLineId,elementName", Mode.MODE_EXACT, 2),
        CONFIG_CSS("config_css_", "样式配置, 拼接appid,bussCode", Mode.MODE_EXACT, 2),
        CONFIG_INTERFACE("config_interface_", "第三方接口配置, 拼接interfaceId", Mode.MODE_EXACT, 1),
        CONFIG_SITE_EMAIL("config_site_email_", "站点邮箱配置, 拼接appid,bussType", Mode.MODE_EXACT, 2),
        CONFIG_OAUTH("config_oauth_", "站点第三方登录配置, 拼接appid,type", Mode.MODE_EXACT, 2),
        CONFIG_OAUTH_DEFAULT("config_oauth_default_", "默认第三方登录配置, 拼接type", Mode.MODE_EXACT, 1),
        CONFIG_RESOURCES("config_resources_", "系统配置，拼接type, name", Mode.MODE_EXACT, 2),
        CONFIG_RICK_TEXT("config_rich_text_", "协议配置，拼接nameEn, terminal", Mode.MODE_EXACT, 2),
        CONFIG_RICK_TEXT_ID("config_rich_text_id_", "协议配置，拼接id", Mode.MODE_EXACT, 1),
        CONFIG_SERVICE("config_service_", "interface接口服务, 拼接serviceKey", Mode.MODE_EXACT, 1),
        CONFIG_SITE_INTERFACE("config_site_interface_", "站点接口配置，拼接站点及serviceKey", Mode.MODE_EXACT, 2),
        CONFIG_SITE_INTERFACE_OPEN("config_site_interface_open_", "站点接口配置，拼接interfaceId", Mode.MODE_EXACT, 1),
        CONFIG_SITE_SMS("config_site_sms_", "站点短信配置，拼接siteId,busnessType", Mode.MODE_EXACT, 2),
        CONFIG_USER_OUT_INTERFACE("config_user_out_interface_", "第三方接口配置，拼接interfaceId", Mode.MODE_EXACT, 1),
        CONFIG_USER_OUT_INTERFACE_PREFIX_LIST("config_user_out_interface_prefix_list", "第三方接口配置帐号前缀列表，新增或更新时需通知", Mode.MODE_EXACT, 0),

        SERVER_USER_OUT_INTERFACE_LIST("server_user_out_interface_list_", "站点第三方登录列表，拼接appid", Mode.MODE_EXACT, 1),
        SERVER_RESOURCES("server_resources_", "系统配置，拼接type, name", Mode.MODE_EXACT, 2),
        SERVER_LOGIN_ELEMENT("server_login_element_", "站点登录配置，拼接appId, elementName", Mode.MODE_EXACT, 2),
        SERVER_USER_OUT_INTERFACE("server_user_out_interface_", "站点第三方登录配置，拼接appid", Mode.MODE_EXACT, 1),
        SERVER_WX_OAUTH("server_wx_oauth_", "微信oauth配置，拼接siteId, type", Mode.MODE_EXACT, 2),
        SERVER_WX_OAUTH_TOKEN("server_wx_oauth_token_", "微信oauthToken配置，拼接appid, type", Mode.MODE_EXACT, 2),
        ;

        private String key;
        private String des;
        private int mode;
        private int paramLength;

        EhcacheKey(String key, String des, int mode, int paramLength) {
            this.des = des;
            this.key = key;
            this.mode = mode;
            this.paramLength = paramLength;
        }

        public String getKey() {
            return key;
        }

        public String getDes() {
            return des;
        }

        public int getMode() {
            return mode;
        }

        public int getParamLength() {
            return paramLength;
        }
    }
}
