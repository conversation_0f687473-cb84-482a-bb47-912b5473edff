var getDom = function(id){return document.getElementById(id);}
function QuickLoginPlugIn(){

	this.isIntalled = false,
	this.initQuickLoginPlugIn = function (){
		try{
			$("body").append('<div id="plugin" style="width: 0px;height: 0px;text-indent: -2222em;"></div>');
			if($.browser.msie){
				$("#plugin").html("<OBJECT name='swQuickLogin'  id='swQuickLogin' CLASSID='clsid:13C90E2C-F498-4D53-B8C3-F43D8FA7B899'></OBJECT>");
				var activeX = new ActiveXObject("AXSIGNIN.AXSignInCtrl.1");
				this.isIntalled = true;
			}else{
				this.initWebKit();
				if(this.isIntalled)
					$("#plugin").html("<embed id='swQuickLogin' type='application/x-kdsignin' hidden='true'>");
			}
		}catch (e) {
			this.isIntalled = false;
		}
	},
	this.initWebKit = function(){
		var signInPluginExisted = false;
		var len = navigator.plugins.length;
		for(var i = 0; i < len; i++) {
			if ( navigator.plugins[i].name  == "npsignin") {
				signInPluginExisted = true;
			}
		}
		this.isIntalled = signInPluginExisted;
	},
	this.put=function(appId,token,userName,extData){
		try{
			getDom("swQuickLogin").putWebToken(appId, token, userName, extData);
		}catch (e) {
			this.isIntalled = false;
			return false;
		}
	},
	this.get=function(appId){
		try{
			return getDom("swQuickLogin").getWebToken(appId);
		}catch (e) {
			this.isIntalled = false;
			return false;
		}
	},
	this.remove=function(appId){
		try{
			getDom("swQuickLogin").removeAllToken(appId);
			return true;
		}catch (e) {
			this.isIntalled = false;
			return false;
		}
	},
	this.getAll=function(){
		try{
			return getDom("swQuickLogin").getAllTokens();
		}catch (e) {
			this.isIntalled = false;
			return false;
		}
	}
}