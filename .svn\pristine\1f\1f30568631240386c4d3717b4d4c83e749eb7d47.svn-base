package com.shunwang.baseStone.sso.apapter;

import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.shunwang.baseStone.config.constants.ConfigOauthConstant;
import com.shunwang.baseStone.context.IPContext;
import com.shunwang.baseStone.siteinterface.context.SiteContext;
import com.shunwang.baseStone.sso.constant.UserOutsiteConstant;
import com.shunwang.baseStone.sso.pojo.QrCodeResponse;
import com.shunwang.baseStone.sso.weixin.oauth.service.WeixinOpenService;
import com.shunwang.baseStone.sso.weixin.oauth.service.WeixinRefreshService;
import com.shunwang.baseStone.sso.weixin.pojo.WeixinQrcode;
import com.shunwang.baseStone.sso.weixin.pojo.WeixinUser;
import com.shunwang.basepassport.asynctask.AsyncTaskExecutor;
import com.shunwang.basepassport.user.common.UserLoginSessionUtil;
import com.shunwang.basepassport.user.pojo.Member;
import com.shunwang.basepassport.user.pojo.MemberAccountBind;
import com.shunwang.basepassport.user.pojo.MemberOutSite;
import com.shunwang.basepassport.user.service.BarFreeLoginAuthService;
import com.shunwang.basepassport.user.service.DcReportService;
import com.shunwang.basepassport.weixin.constant.WeixinConstant;
import com.shunwang.basepassport.weixin.exception.WeixinOauthException;
import com.shunwang.basepassport.weixin.pojo.WeixinOauth;
import com.shunwang.basepassport.weixin.pojo.WeixinOauthToken;
import com.shunwang.basepassport.weixin.service.WeixinOauthService;
import com.shunwang.basepassport.weixin.service.WeixinOauthTokenService;
import com.shunwang.util.StringUtil;
import com.shunwang.util.math.RandomUtil;
import com.shunwang.util.net.IpUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.struts2.ServletActionContext;

import java.util.HashMap;
import java.util.Map;

/**
 * User:pf.ma
 * Date:2019/12/30
 * Time:14:10
 */
public abstract class WeixinAdapter extends UserOutsiteApapter {

    protected WeixinOauthService weixinOauthService ;
    protected WeixinOauthTokenService weixinOauthTokenService ;
    protected WeixinOpenService weixinOpenService ;
    protected BarFreeLoginAuthService barFreeLoginAuthService;

    protected String ssoSiteId = WeixinConstant.COMPONENT_SITE_ID;

	protected void initContextInfo() {
		//初始 ip context 和 siteContext
		IPContext.setIp(IpUtil.getIpAddress(ServletActionContext.getRequest()));
		SiteContext.setSiteId(site_id);
	}

	protected void updateHeadAndNick(WeixinUser weixinUser) {
		String wxHeadImg = weixinUser.getHeadimgurl();
		// 如果头像是空的或者头像是以http开头则修改为新头像或者微信的头像地址有变化
		Member paramMember = new Member();
        paramMember.setMemberId(member.getMemberId());
        // TODO感觉这个设置没必要，没检查，后续有时间可以看下2022-09-19
        paramMember.setMemberName(member.getMemberName());
        boolean needUpdate = false;
		if (StringUtil.isBlank(member.getHeadImg())
				|| (member.getHeadImg().startsWith("http") && !member.getHeadImg().equals(wxHeadImg))) {
			paramMember.setHeadImg(wxHeadImg);
			needUpdate = true;
		}
		String wxNickName = weixinUser.getNickname();
		if (!StringUtil.isBlank(wxNickName) && !wxNickName.equals(member.getNickName())) {
			paramMember.setNickName(wxNickName);
            needUpdate = true;
		}
		if (needUpdate) {
			interfaceService.updateMember(paramMember);
		}
	}

	protected MemberOutSite buildMemberOutSite(WeixinUser weixinUser, String loginType) {
	    if (StringUtil.isBlank(weixinUser.getUnionid())) {
	        LOG.error("当前appid[{}]未获取到unionId",  appId);
	        return null;
        }
		MemberOutSite memberOutSite = new MemberOutSite();
		memberOutSite.setOutMemberId(weixinUser.getUnionid());

		int i = 0;
		do {
			memberOutSite.setMemberName(generalMemberName(weixinUser.getUnionid()));
			if (getMemberDao().getByName(memberOutSite.getMemberName()) != null) {
				i++;
			} else {
				i = 4;
			}
		} while (i < 4);

		memberOutSite.setOutMemberName(memberOutSite.getMemberName());
		memberOutSite.setNickName("微信_" + weixinUser.getUnionid().substring(22));
		memberOutSite.setRegFrom(site_id);
		memberOutSite.setVersion(version);
		memberOutSite.setEnv(env);
		memberOutSite.setRemark(extData);
		memberOutSite.setMemberFrom(loginType);
		if (!StringUtil.isBlank(weixinUser.getHeadimgurl())) {
			memberOutSite.setHeadImg(weixinUser.getHeadimgurl());
		}
		return memberOutSite;
	}

    protected WeixinOauthToken getBySiteIdAndType(String site_id, int type) {
        WeixinOauth weixinOauth = weixinOauthService.getBySiteId(site_id, type) ;
        if(null == weixinOauth){
            throw new WeixinOauthException("接入方授权信息不存在，或未开启") ;
        }
        WeixinOauthToken weixinOauthToken = weixinOauthTokenService.getByAppIdAndType(weixinOauth.getAppId(), type) ;
        return weixinOauthToken ;
    }

    @Override
    protected MemberAccountBind buildMemberAccountBind() {
        MemberAccountBind memberAccountBind = super.buildMemberAccountBind();
        memberAccountBind.beginBuildLog("WEIXIN帐号绑定");
        memberAccountBind.setWeixin(member.getMemberId());
        return memberAccountBind;
    }

    /**
     * 刷新接入方accessToken
     * @param oauthToken
     */
    public void refreshAuthorizeAccessToken(WeixinOauthToken oauthToken){
        try {
            LOG.debug("刷新[{}]的token", oauthToken.getAppId());
            WeixinOauth weixinOauth = weixinOauthService.getBySiteId(ssoSiteId, WeixinConstant.TYPE.COMPONENT) ;
            WeixinOauthToken componentOauthToken = weixinOauthTokenService.getByAppIdAndType(weixinOauth.getAppId(), WeixinConstant.TYPE.COMPONENT) ;
            if(null == componentOauthToken){
                LOG.error("授权信息[siteId:{}]Token未获取", ssoSiteId) ;
                return ;
            }
            AsyncTaskExecutor.submit(new WeixinRefreshService(redisOperation, weixinOpenService, weixinOauthTokenService, componentOauthToken, oauthToken)) ;
        } catch (Exception e) {
            LOG.error("刷新[{}]的token异常", oauthToken.getAppId(), e);
        }
    }

    /**
     * 校验接入方微信appid是否配置及开启，过期自动刷新
     * @param weixinOauthToken
     */
    protected void checkOauthToken(WeixinOauthToken weixinOauthToken) {
        if(null == weixinOauthToken || !weixinOauthToken.isOpen()){
            throw new WeixinOauthException("接入方授权信息不存在，或未开启") ;
        }
        // 如果AccessToken临近过期或已经过期
        if(weixinOauthToken.isExpiry()){
            refreshAuthorizeAccessToken(weixinOauthToken) ;
        }
    }

    /**
     * 返回二维码结果集，目前用于微信公众号，微信小程序
     * @param weixinQrcode
     * @param cacheUserKey
     * @param qrCodeExpireSeconds
     * @return
     */
    protected Map<String, Object> respQrcode(WeixinQrcode weixinQrcode, String cacheUserKey, Integer qrCodeExpireSeconds) {
        return respQrcode(weixinQrcode, cacheUserKey, qrCodeExpireSeconds, null) ;
    }

    /**
     * 返回二维码结果集，目前用于微信公众号，微信小程序
     * @param weixinQrcode
     * @param cacheUserKey
     * @param qrCodeExpireSeconds
     * @return
     */
    protected Map<String, Object> respQrcode(WeixinQrcode weixinQrcode, String cacheUserKey, Integer qrCodeExpireSeconds, String tips) {
        if(StringUtils.isBlank(weixinQrcode.getQrcodeUrl())){
            LOG.error("获取场景值二维码为空") ;
            throw new WeixinOauthException("获取场景值二维码异常") ;
        }
        Map<String,Object> resultMap = new HashMap<>() ;
        resultMap.put("expireSeconds", qrCodeExpireSeconds);
        resultMap.put("qrcodeUrl", weixinQrcode.getQrcodeUrl()) ;
        resultMap.put("key", cacheUserKey) ;
        if (StringUtil.isNotBlank(tips)) {
            resultMap.put("tips", tips);
        }
        updateResult(cacheUserKey, new QrCodeResponse(QrCodeResponse.TypeStage.INIT));
        return resultMap ;
    }

    protected void wxLogin(QrCodeResponse result, String openId) {
        member.setOpenId(openId);
        login(member);
        result.setTicket(getTicket());
        result.setTockenId(getToken());
        result.setClientTicket(getClientTicket());
    }
    protected void bindNetBarAuth(String extMemberName, Integer netBarMemberId, String memberName) {
        UserLoginSessionUtil.LoginSession loginSession = UserLoginSessionUtil.getLoginSession(extMemberName);
        if (loginSession != null && StringUtil.isNotBlank(loginSession.getExtData())) {
            JsonObject jsonObject = JsonParser.parseString(loginSession.getExtData()).getAsJsonObject();
            if (jsonObject.has("barId")) {
                barFreeLoginAuthService.saveOrUpdateAuth(jsonObject.get("barId").getAsString(), netBarMemberId);
                DcReportService.freeLoginReport(memberName, extMemberName, UserOutsiteConstant.MINI_INTERFACE_ID.equals(getInterfaceId()) ?
                        DcReportService.OptTypeAndExtInfo.WX_MINI_BAR_AUTH : DcReportService.OptTypeAndExtInfo.WX_OPEN_BAR_AUTH);
            }
        }
    }

    @Override
    protected void cacheExtData(String key, Integer expireSeconds) {
        super.cacheExtData(key, expireSeconds);
        if (StringUtil.isNotBlank(appId)) {
            redisOperation.set(WeixinConstant.EXT_SCENE_KEY_APPID + key, appId, expireSeconds);
        }
    }

	public String generalMemberName(String unionId) {
		return "wx_" + unionId.substring(0, 13) + RandomUtil.getRandomStr(6);
	}

	@Override
	protected MemberAccountBind getByMemberId() {
		return memberAccountBindDao.getByWeixin(member.getMemberId());
	}

	@Override
	protected Integer getOutOauthType() {
		return ConfigOauthConstant.TYPE.WEIXIN.getInt();
	}

    public WeixinOauthService getWeixinOauthService() {
        return weixinOauthService;
    }

    public void setWeixinOauthService(WeixinOauthService weixinOauthService) {
        this.weixinOauthService = weixinOauthService;
    }

    public WeixinOauthTokenService getWeixinOauthTokenService() {
        return weixinOauthTokenService;
    }

    public void setWeixinOauthTokenService(WeixinOauthTokenService weixinOauthTokenService) {
        this.weixinOauthTokenService = weixinOauthTokenService;
    }

    public WeixinOpenService getWeixinOpenService() {
        return weixinOpenService;
    }

    public void setWeixinOpenService(WeixinOpenService weixinOpenService) {
        this.weixinOpenService = weixinOpenService;
    }

    public String getSsoSiteId() {
        return ssoSiteId;
    }

    public void setSsoSiteId(String ssoSiteId) {
        this.ssoSiteId = ssoSiteId;
    }

    public BarFreeLoginAuthService getBarFreeLoginAuthService() {
        return barFreeLoginAuthService;
    }

    public void setBarFreeLoginAuthService(BarFreeLoginAuthService barFreeLoginAuthService) {
        this.barFreeLoginAuthService = barFreeLoginAuthService;
    }
}
