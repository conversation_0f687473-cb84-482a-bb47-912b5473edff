$import('SFGridView');
$import('SFGrid');
$import('SFInput');
$import('ValidateRule');
$import('SFDirect');
$import('SFSelect');
$import('SFEditor');
var categoryId;
var RichTextView = $createClass('RichTextView', function () {
    this.pojoControls = [
        // this.pk,
        // new SFInput({
        //     field: 'name',
        //     name: '名称',
        //     rules: [
        //         new CheckEmpty(),
        //         new CheckMaxLength({length: 200})
        //     ]
        // }),
        // new SFSelect({
        //     field: 'accountType',
        //     name: "类型:",
        //     needdefault: true,
        //     defaultValue: '0',
        //     defaultText: "协议",
        //     items: [
        //         {value: '协议', text: '协议'},
        //     ],
        //     rules: [
        //         new CheckEmpty()
        //     ]
        // }),
        // new SFSelect({
        //     field: 'businesscategoryid', 'name': "部门:",
        //     needdefault: true,
        //     items: RichTextUtil.getBusinessCategoryItems(),
        //     onchange: function (event) {
        //         categoryId.setItems(RichTextUtil.getCategoryItems(this.getValue()));
        //     },
        //     rules: [
        //         new CheckEmpty()
        //     ]
        // }),
        // categoryId = new SFSelect({
        //     field: 'categoryid', 'name': "作业单元:",
        //     needdefault: true,
        //     items: RichTextUtil.getCategoryItems(),
        //     rules: [
        //         new CheckEmpty()
        //     ]
        // }),
        // new SFInput({
        //     field: 'remark',
        //     name: '备注',
        //     rules: [
        //         new CheckMaxLength({length: 100})
        //     ]
        // }),
    ];
    this.schControl = [
        new SFInput({field: 'schName', name: '名称'}),
        new SFSelect({
            field: 'schType',
            name: '类型',
            needdefault: false,
            items: [
                {value: '协议', text: '协议'},
            ]
        }),
    ];
    this.pojoHidden = ['id'];
    this.SFGridView();
}, 'SFGridView');

RichTextView.prototype.buildGrid = function () {
    var self = this;
    return new SFGrid({
        url: 'listRichText.do',
        col: [
            {id: 'id', text: 'key'},
            {id: 'name', text: '名称'},
            {id: 'terminal', text: '终端类型'},
            {id: 'type', text: '类型'},
            {id: 'stateShow', text: '状态'},
            {id: 'link', text: '链接'},
            {id: 'version', text: '版本号'},
            {id: 'remark', text: '备注'}],
        linebutton: [
            {
                text: '关闭',
                onclick: function (pojo) {
                    if (confirm("是否关闭当前记录"))
                        self.trigger("close", pojo);
                },
                showFun: function (data) {
                    return data.state == 1;
                }
            },
            {
                text: '打开',
                onclick: function (pojo) {
                    if (confirm("是否打开当前记录"))
                        self.trigger("open", pojo);
                },
                showFun: function (data) {
                    return data.state == 0;
                }
            },
            {
                text: '用户通知',
                onclick: function (pojo) {
                    if (confirm("是否通知用户"))
                        self.trigger("updateVersion", pojo);
                }
            },
            this.createUpdateBtn2(),
            this.createUploadBtn1()
        ]
    });
};

var updateWin;
var uploadWin1;
var editId = "";
//修改面板
RichTextView.prototype.createUpdateBtn2 = function () {
    var self = this;
    return {
        text: '修改', onclick: function (data) {
            editId = data.id;
            updateWin = new SFFormWindow({
                title: '修改',
                btns: [
                    {
                        text: '保存', onclick: function () {
                            self.update(updateWin.getValue());
                        }
                    },
                    {
                        text: '取消', onclick: function () {
                            updateWin.closeWin();
                        }
                    }
                ]
            });
            updateWin.add(
                new SFInput({field: 'name', name: '名称', readOnly: true})
            );
            updateWin.add(
                new SFInput({field: 'terminal', name: '终端类型', readOnly: true})
            );
            updateWin.add(
                new SFInput({field: 'type', name: '类型', readOnly: true})
            );
            updateWin.add(
                new SFSelect({
                    field: 'state', 'name': "状态:",
                    needdefault: true,
                    defaultValue: '1',
                    defaultText: "开启",
                    items: [
                        {value: '0', text: '关闭'},
                    ]
                }),
            );
            updateWin.add(
                new SFInput({field: 'link', name: '链接'})
            );
            updateWin.add(
                new SFInput({field: 'remark', name: '备注'})
            );
            updateWin.show();
            updateWin.setValue(data);

        }
    }
};
//上传面板
RichTextView.prototype.createUploadBtn1 = function () {
    var self = this;
    return {
        text: '内容编辑',
        onclick: function (data) {
            editId = data.id;
            uploadWin1 = new SFFormWindow({
                title: '内容编辑',
                btns: [
                    {
                        text: '保存',
                        onclick: function () {
                            self.ckeditor(uploadWin1.getValue());
                        }
                    },
                    {
                        text: '关闭',
                        onclick: function () {
                            uploadWin1.closeWin();
                        }
                    }
                ]
            });
            var editor = new SFEditor({
                field: 'content',
                uploadUrl: 'richTextContent.do?id=' + data.id,
                name: '内容编辑',
                value: data.content
            });
            uploadWin1.add(editor);
            // uploadWin1.add(
            //     new SFEditor({
            //         field: 'content',
            //         uploadUrl: 'richTextContent.do?id=' + data.id,
            //         name: '内容编辑'
            //     })
            // );
            uploadWin1.show();
            uploadWin1.setValue(data);
            // editor.setValue(data.content);
        }
    }
};
RichTextView.prototype.ckeditor = function (data) {
    $.ajax({
        url: "richTextContent.do",
        type: 'post',
        dataType: 'json',
        data: {id: editId, content: data.content},
        success: function (resp) {
            if (resp.success == true) {
                window.location.href = "../richText/richText.jsp";
            } else {
                alert(resp.msg);
            }
        },
        error: function () {
            alert("error");
        }
    });
};

RichTextView.prototype.update = function (data) {
    $.ajax({
        url: "updateRichText.do",
        type: 'post',
        dataType: 'json',
        data: {
            id: editId,
            name: data.name,
            terminal: data.terminal,
            type: data.type,
            state: data.state,
            link: data.link,
            remark: data.remark
        },
        success: function (resp) {
            if (resp.success == true) {
                window.location.href = "../richText/richText.jsp";
            } else {
                alert(resp.msg);
            }
        },
        error: function () {
            alert("error");
        }
    });
}