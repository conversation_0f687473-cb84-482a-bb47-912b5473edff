package com.shunwang.baseStone.context;

import com.shunwang.encrypt.core.encrypt.AesEncrypt;
import com.shunwang.plugin.ibatis.EncryptHandler;

public class DomainContext {
	private static String securityServer;
	private static String kedouServer;
	private static String baseServer;
	private static String logServer;
	private static String appServer;
	private static String actualityImgServer;

	private static String aesLogKey;


	private static EncryptHandler encryptHandler;

	public static EncryptHandler getEncryptHandler() {
		return encryptHandler;
	}

	public static String getAesLogKey() {
		return aesLogKey;
	}

	public void setAesLogKey(String aesLogKey) {
		DomainContext.aesLogKey = aesLogKey;
		encryptHandler = new EncryptHandler(new AesEncrypt(aesLogKey));
	}

	public static String getSecurityServer() {
		return securityServer;
	}
	public void setSecurityServer(String securityServer) {
		DomainContext.securityServer = securityServer;
	}
	public void setKedouServer(String kedouServer) {
		DomainContext.kedouServer = kedouServer;
	}
	public static String getKedouServer() {
		return kedouServer;
	}
	public void setBaseServer(String baseServer) {
		DomainContext.baseServer = baseServer;
	}
	public static String getBaseServer() {
		return baseServer;
	}

	public static String getAppServer() {
		return appServer;
	}

	public  void setAppServer(String appServer) {
		DomainContext.appServer = appServer;
	}

	public static String getLogServer() {
		return logServer;
	}

	public  void setLogServer(String logServer) {
		DomainContext.logServer = logServer;
	}

	public static String getActualityImgServer() {
		return actualityImgServer;
	}

	public void setActualityImgServer(String actualityImgServer) {
		DomainContext.actualityImgServer = actualityImgServer;
	}
}
