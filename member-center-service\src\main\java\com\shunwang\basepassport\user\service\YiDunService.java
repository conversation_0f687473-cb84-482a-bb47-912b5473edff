package com.shunwang.basepassport.user.service;

import com.shunwang.basepassport.manager.request.yidun.OneClickCheckRequest;
import com.shunwang.basepassport.manager.response.yidun.OneClickCheckResponse;
import com.shunwang.basepassport.manager.service.yidun.CheckMobileYiDunH5ServiceClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class YiDunService {
    private static final Logger logger = LoggerFactory.getLogger(YiDunService.class);

    public static OneClickCheckResponse checkMobileH5(String token, String accessToken, String siteId) {
        OneClickCheckRequest request = new OneClickCheckRequest();
        request.setToken(token);
        request.setAccessToken(accessToken);

        return CheckMobileYiDunH5ServiceClient.execute(request, siteId);
    }
}
