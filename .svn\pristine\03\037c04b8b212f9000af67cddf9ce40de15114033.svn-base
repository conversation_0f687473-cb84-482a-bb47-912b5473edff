<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">
	
	<!-- 域名上下文 单例实现	,提供service调用通用参数实现-->
	<bean id="domainContextBaseStone" class="com.shunwang.baseStone.context.DomainContext" >
		<property name="securityServer" value="${securityServer}"></property>
		<property name="kedouServer" value="${kedouServer}"></property>
		<property name="baseServer" value="${baseServer}"></property>
		<property name="logServer" value="${logServer}"></property>
		<property name="appServer" value="${appServer}"></property>
		<property name="actualityImgServer" value="${actualityImgServer}"></property>
	</bean>

	<!-- 域名上下文 单例实现 自定义配置参数	-->
	<bean id="domainContext" class="com.shunwang.passport.common.context.DomainContext" >
		<property name="appServer" value="${appServer}"></property>
		<property name="securityServer" value="${securityServer}"></property>
		<property name="SSO_Server" value="${ssoServer}"></property>
		<property name="kedouServer" value="${kedouServer}"></property>
		<property name="baseServer" value="${baseServer}"></property>
		<property name="ssoLoginURL" value="${ssoLoginURL}"></property>
		<property name="cookieDomain" value="${cookieDomain}"></property>
		<property name="tgURL" value="${tgURL}"></property>
		<property name="tgNoticeKey" value="${tgNoticeKey}"></property>
		<property name="payServer" value="${payServer}"></property>
		<property name="gameServer" value="${gameServer}"></property>
		<property name="helpServer" value="${helpServer}"></property>
		<property name="logServer" value="${logServer}"></property>
		<property name="gamebbsServer" value="${gamebbsServer}"></property>
		<property name="identityServer" value="${identityServer}"></property>
		<property name="assetsServer" value="${assets.server}"></property>
		<property name="assetsPath" value="${assets.path}"></property>
		<property name="assetsVersion" value="${assets.version}"></property>
		<property name="actualityImgServer" value="${actualityImgServer}"></property>
		<property name="payStaticServer" value="${payStaticServer}"></property>
	</bean>
    
</beans>