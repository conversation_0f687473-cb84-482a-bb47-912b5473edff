<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE struts PUBLIC "-//Apache Software Foundation//DTD Struts Configuration 2.0//EN"
	"http://struts.apache.org/dtds/struts-2.0.dtd">

<struts>
	<constant name="struts.action.extension" value="do"/>
	<constant name="struts.objectFactory" value="spring" />
	<constant name="struts.devMode" value="false" />
	<constant name="struts.configuration.xml.reload" value="false" />
	<constant name="struts.multipart.maxSize" value="2097152" />
	<constant name="struts.ui.theme" value="simple"></constant>

	<package name="isp" extends="struts-default">
		<interceptors>
			<interceptor-stack name="swDefaultStack">
				<interceptor-ref name="conversionError"/>
				<interceptor-ref name="defaultStack" />
			</interceptor-stack>
		</interceptors>
		<default-interceptor-ref name="swDefaultStack" />
		<global-results>
			<result name="error">ispError.jsp</result>
		</global-results>
		<global-exception-mappings>
			<exception-mapping result="error" exception="java.lang.Exception"></exception-mapping>
		</global-exception-mappings>
	</package>

	<include file="struts-default.xml" />
	<include file="struts-plugin.xml"></include>

	<include file="baseStone/struts/CheckCode_struts.xml" />
	
	<include file="sso/struts/*.xml" />
</struts>
