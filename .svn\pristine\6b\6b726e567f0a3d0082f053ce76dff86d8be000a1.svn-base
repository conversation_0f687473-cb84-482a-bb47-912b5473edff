package com.shunwang.basepassport.config.dao;


import com.shunwang.baseStone.core.dao.BaseStoneIbatisDao;
import com.shunwang.basepassport.config.pojo.ConfigInterface;

/**
 * 应用接口配置
 *
 */
public class ConfigInterfaceDao extends BaseStoneIbatisDao<ConfigInterface> {
	/**
	 * 查询接口配置
	 * @param interfaceKey
	 * @return
	 */
	public ConfigInterface findByInterfaceKey(int interfaceKey){
		return (ConfigInterface)getSqlMapClientTemplate().queryForObject(getStatementNameWrap("findByInterfaceKey"), interfaceKey);
	}

}
