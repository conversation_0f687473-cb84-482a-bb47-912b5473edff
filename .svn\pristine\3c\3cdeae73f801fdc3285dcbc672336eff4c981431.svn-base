<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <bean id="cacheMessageConsumer" class="com.shunwang.baseStone.jms.CacheMessageConsumer">
        <property name="cacheName" value="cache"/>
        <property name="cacheManager" ref="cacheManager"/>
    </bean>

    <!-- consumer -->
    <bean id="cacheProcessConsumer" class="org.springframework.jms.listener.DefaultMessageListenerContainer">
        <property name="connectionFactory" ref="activeMQConnectionFactory" />
        <property name="destination" ref="cacheDestination"/>
        <property name="messageListener" ref="cacheMessageConsumer"/>
        <!--客户端手动确认-->
        <property name="sessionAcknowledgeMode" value="2"/>
    </bean>

</beans>