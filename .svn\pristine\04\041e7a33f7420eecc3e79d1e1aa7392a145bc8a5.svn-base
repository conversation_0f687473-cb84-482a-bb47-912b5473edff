<%@ page contentType="text/html;charset=UTF-8"%>
<%@ include file="/common/taglibs.jsp"%>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name="Keywords" content="顺网通行证 密码 找回 邮箱绑定 重置" />
<meta name="Description" content="顺网通行证已将通知信发到用户邮箱中，用户需登陆邮箱按提示找回密码。" />
<title>顺网通行证-找回密码-邮箱找回</title>
<script type="text/javascript" src="${staticServer}/scripts/common/emailUrl.js" ></script>
 
</head>
<body>
<%@ include file="global_nav.jsp" %>
<div class="c_body forget_s01">
    <ul class="step_bar">
        <li><em>1</em> 选择找回方式</li>
        <li class="current"><em>2</em> 进行安全验证</li>
        <li><em>3</em> 设置新密码</li>
    </ul>
    <div class="forget_send">
        <i class="f_email"></i>
        <p class="tip">密码找回邮件  ${maskedEmailAddr} 发送成功！</p>
        <p>请您注意<strong><a href="#" onclick="goMail(document.getElementById('goEmail').value)">接收邮件</a></strong>！</p>
    </div>
    <form id="form1" method="post">
        <%--<input type="hidden" name="email" value="${email }"  id="goEmail"/>--%>
        <input type="hidden"name="memberName"  value="${memberName}" />
    </form>
    <div class="forget_desc">
        <p>1.由于垃圾邮件没有明确的判断标准，如果您的收件箱没有收到我们发送的邮件，您可以查阅一下垃圾邮件箱。</p>
        <p>2.没有收到邮件？ <a href="#" id="resendLink">重新发送</a> 或者 <a href="${appServer}/front/noLogin/pwdFind_appeal_front.htm?memberName=${memberName}&findWay=1">使用其它方式找回</a>。</p>
    </div>
</div>
<script type="text/javascript" charset="utf-8">
  $("#resendLink").click(function() {
    $("#form1")[0].action = "${appServer}/front/noLogin/sendEmail.htm";
    $("#form1").submit();
  });
</script>

</body>
</html>
