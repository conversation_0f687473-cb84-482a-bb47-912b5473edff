package com.shunwang.baseStone.jms;

import com.google.gson.reflect.TypeToken;
import com.shunwang.baseStone.jms.action.Action;
import com.shunwang.baseStone.jms.mode.Mode;
import com.shunwang.baseStone.jms.param.StringArrayParam;
import com.shunwang.baseStone.jms.param.StringParam;
import com.shunwang.util.json.GsonUtil;

import java.util.List;

/**
 * 构建消息工具类
 *
 * <AUTHOR>
 * @date 2019/3/12
 **/
public class CacheMessageBuilder {


    /**
     * 删除一堆缓存key
     *
     * @param keys
     * @return
     */
    public static CacheMessage newDeleteArrayMessage(int mode, String... keys) {
        if (keys == null || keys.length == 0) {
            return new CacheMessage(Action.NOOP, mode, new StringParam(""));
        }
        if (keys.length == 1) {
            return new CacheMessage(Action.DELETE, mode, new StringParam(keys[0]));
        } else {
            return new CacheMessage(Action.DELETE, mode, new StringArrayParam(keys));
        }
    }

    public static CacheMessage newDeleteArrayMessage(String... keys) {
      return newDeleteArrayMessage(Mode.MODE_EXACT, keys);
    }

    public static CacheMessage newPrefixDeleteArrayMessage(String... keys) {
        return newDeleteArrayMessage(Mode.MODE_PREFIX, keys);
    }

    public static CacheMessage newPrefixDeleteArrayMessage(List<String> keys) {
        String[] arrays = new String[keys.size()];
        keys.toArray(arrays);
        return newDeleteArrayMessage(Mode.MODE_PREFIX, arrays);
    }

    public static CacheMessage newDeleteArrayMessage(List<String> keys) {
        String[] arrays = new String[keys.size()];
        keys.toArray(arrays);
        return newDeleteArrayMessage(Mode.MODE_EXACT, arrays);
    }

    public static CacheMessage newCacheMessage(String json) {

//        JSONObject jsonObject = JSONObject.parseObject(json);
//        Integer action = jsonObject.getInteger("action");
//        Integer mode = jsonObject.getInteger("mode");
//
//        JSONObject param = jsonObject.getJSONObject("param");
//        Integer type = param.getInteger("type");
//        Param p = getParam(type, param);
        try {
            return GsonUtil.fromJson(json, new TypeToken<CacheMessage<StringParam>>() {
            }.getType());
        } catch (Exception e) {
            return GsonUtil.fromJson(json, new TypeToken<CacheMessage<StringArrayParam>>() {
            }.getType());
        }
    }

//    private static Param getParam(int type, JSONObject jsonObject) {
//        switch (type) {
//            case Param.TYPE_STRING:
//                return new StringParam(jsonObject.getString("value"));
//            case Param.TYPE_STRING_ARRAY:
//                JSONArray jsonArray = jsonObject.getJSONArray("value");
//                String[] keys = new String[jsonArray.size()];
//                jsonArray.toArray(keys);
//                return new StringArrayParam(keys);
//            default:
//                return null;
//        }
//    }

    public static void main(String[] args) {
        System.out.println(new CacheMessage(Action.QUERY, Mode.MODE_PREFIX, new StringParam("base_site_interface_sso_")));
    }
}
