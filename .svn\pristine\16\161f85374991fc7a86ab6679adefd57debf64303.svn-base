<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:aop="http://www.springframework.org/schema/aop"
	xsi:schemaLocation="http://www.springframework.org/schema/beans
	http://www.springframework.org/schema/beans/spring-beans.xsd
    http://www.springframework.org/schema/aop
	http://www.springframework.org/schema/aop/spring-aop.xsd">


    <bean id="configurer" class="org.springframework.beans.factory.config.PropertyPlaceholderConfigurer">
		<property name="locations">
			<list>
				<value>file:${INTERFACE_KEDOU_CONFIG_HOME}/baseStoneDb.properties</value>
				<value>file:${INTERFACE_KEDOU_CONFIG_HOME}/server.properties</value>
				<value>file:${INTERFACE_KEDOU_CONFIG_HOME}/cache.properties</value>
				<value>file:${INTERFACE_KEDOU_CONFIG_HOME}/email.properties</value>
				<value>file:${INTERFACE_KEDOU_CONFIG_HOME}/domain.properties</value>
			</list>
		</property>
	</bean>

	<import resource="classpath*:/basepassport.xml" />
	<import resource="classpath*:/baseStone.xml" />
	<import resource="classpath*:/interfaceCache/spring/cache.xml" />
	<import resource="classpath*:/basepassport/selfspring/*.xml" />
	<import resource="classpath*:/basepassport/jms/*.xml" />


	<aop:aspectj-autoproxy proxy-target-class="true"/>

	<bean id="encryptAspect" class="com.shunwang.basepassport.aspect.EncryptAspect">
		<property name="queryWithCipherColumn" value="${db.queryWithCipherColumn}"/>
		<property name="databaseKeyMapping">
			<map>
				<!--key为数据源的name-->
				<entry key="mysql-base-passport" value="${db.aes.password.basePassport}"/>
				<entry key="mysql-base-passport-log" value="${db.aes.password.basePassportLog}"/>
			</map>
		</property>
	</bean>
</beans>