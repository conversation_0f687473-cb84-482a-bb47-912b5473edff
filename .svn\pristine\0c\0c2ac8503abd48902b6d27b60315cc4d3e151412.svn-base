<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <!--send-->
    <bean id="mobileSendBaseAction" class="com.shunwang.basepassport.binder.web.send.MobileSendBaseAction"
          scope="prototype">
        <property name="bussinessDao" ref="bussinessDao"/>
    </bean>
    <bean id="sendForFindPwdAction" class="com.shunwang.basepassport.binder.web.send.SendForFindPwdAction"
          scope="prototype" parent="mobileSendBaseAction"/>
    <bean id="sendForMobileRegisterAction" class="com.shunwang.basepassport.binder.web.send.SendForMobileRegisterAction"
          scope="prototype"  parent="mobileSendBaseAction"/>
    <bean id="sendForSingleAccountBindAction" class="com.shunwang.basepassport.binder.web.send.SendForSingleAccountBindAction"
          scope="prototype" parent="mobileSendBaseAction"/>
    <bean id="sendForLoginAction" class="com.shunwang.basepassport.binder.web.send.SendForLoginAction"
          scope="prototype" parent="mobileSendBaseAction"/>
    <bean id="sendForDefaultAction" class="com.shunwang.basepassport.binder.web.send.SendForDefaultAction"
          scope="prototype" parent="mobileSendBaseAction"/>


    <!--bind-->
    <bean id="mobileBindBaseAction" class="com.shunwang.basepassport.binder.web.bind.MobileBindBaseAction"
          scope="prototype">
        <property name="memberDao" ref="memberDao"/>
        <property name="bussinessDao" ref="bussinessDao"/>
        <property name="weakPasswordDao" ref="weakPasswordDao"/>

    </bean>
    <bean id="confirmForFindPwdAction" class="com.shunwang.basepassport.binder.web.bind.ConfirmForFindPwdAction"
          parent="mobileBindBaseAction" scope="prototype"/>
    <bean id="confirmForLoginAction" class="com.shunwang.basepassport.binder.web.bind.ConfirmForLoginAction"
          parent="mobileBindBaseAction" scope="prototype"/>
    <bean id="confirmForNormalAction" class="com.shunwang.basepassport.binder.web.bind.ConfirmForNormalAction"
          parent="mobileBindBaseAction" scope="prototype"/>
    <bean id="confirmForNormalNoBindAction" class="com.shunwang.basepassport.binder.web.bind.ConfirmForNormalNoBindAction"
          parent="mobileBindBaseAction" scope="prototype"/>
    <bean id="confirmForRegAction" class="com.shunwang.basepassport.binder.web.bind.ConfirmForRegAction"
          parent="mobileBindBaseAction" scope="prototype"/>

    <bean id="confirmForRegNoActiveNoAction" class="com.shunwang.basepassport.binder.web.bind.ConfirmForRegNoActiveNoAction"
          parent="mobileBindBaseAction" scope="prototype"/>
    <bean id="confirmForSingleAccountBindAction" class="com.shunwang.basepassport.binder.web.bind.ConfirmForSingleAccountBindAction"
          parent="mobileBindBaseAction" scope="prototype">
        <property name="memberAccountBindDao" ref="memberAccountBindDao"/>
        <property name="memberOutSiteDao" ref="memberOutSiteDao" />
    </bean>
    <bean id="confirmForSingleAccountBindHotelAction" class="com.shunwang.basepassport.binder.web.bind.ConfirmForSingleAccountBindHotelAction"
          parent="mobileBindBaseAction" scope="prototype">
        <property name="memberAccountBindDao" ref="memberAccountBindDao"/>
        <property name="memberOutSiteDao" ref="memberOutSiteDao" />
    </bean>
    <bean id="confirmForLoginInnerAction" class="com.shunwang.basepassport.binder.web.bind.ConfirmForLoginInnerAction"
          parent="mobileBindBaseAction" scope="prototype"/>


    <bean id="singleAccountBindService" class="com.shunwang.basepassport.interc.SingleAccountBindService">
        <property name="memberAccountBindDao" ref="memberAccountBindDao"/>
        <property name="interfaceService" ref="interfaceService"/>
    </bean>

    <bean id="interfaceService" class="com.shunwang.basepassport.interc.InterInterfaceService">
        <property name="interfaceConfig" ref="interfaceConfig"/>
    </bean>

    <bean id="interfaceConfig" class="com.shunwang.basepassport.interc.InterInterfaceConfig">
        <property name="siteId" value="${interface.siteId}"/>
        <property name="sendbinderUrl" value="${interface.sendbinder.url}"/>
        <property name="sendbinderMd5Key" value="${interface.sendbinder.md5Key}"/>
        <property name="confirmForInnerLoginUrl" value="${interface.confirmForInnerLogin.url}"/>
        <property name="confirmForInnerLoginMd5Key" value="${interface.confirmForInnerLogin.md5Key}"/>
        <property name="mobileCheckCodeUpdateUrl" value="${interface.mobileCheckCodeUpdate.url}"/>
        <property name="mobileCheckCodeUpdateMd5Key" value="${interface.mobileCheckCodeUpdate.md5Key}"/>
        <property name="updateSingleAccountBindExtUrl" value="${interface.singleAccountBindExt.url}"/>
        <property name="updateSingleAccountBindExtMd5Key" value="${interface.singleAccountBindExt.md5Key}"/>
    </bean>

    <bean id="sendBinderUpdateAction" class="com.shunwang.basepassport.binder.web.SendBinderUpdateAction" scope="prototype"/>

</beans>
