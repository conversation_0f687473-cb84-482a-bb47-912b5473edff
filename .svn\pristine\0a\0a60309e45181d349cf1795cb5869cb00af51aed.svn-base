package com.shunwang.basepassport.user.web;

import com.shunwang.baseStone.core.action.BaseStoneAction;
import com.shunwang.baseStone.core.exception.BaseStoneException;
import com.shunwang.baseStone.core.response.BaseStoneResponse;
import com.shunwang.baseStone.encrypt.Encrypt;
import com.shunwang.baseStone.encrypt.pojo.EncryptItem;
import com.shunwang.basepassport.user.common.MemberConstants;
import com.shunwang.basepassport.user.dao.IdcardBindDao;
import com.shunwang.basepassport.user.pojo.IdcardBind;
import com.shunwang.util.lang.StringUtil;

import java.util.Date;

/**
 * Created by bao.jin on 2019/6/24.
 */
public class IdcardUnbindAction extends BaseStoneAction {

    private Integer memberId;
    private String idcard;
    private IdcardBindDao idcardBindDao;

    @Override
    public void process() throws Exception {

        IdcardBind param = new IdcardBind();
        if (null != memberId) {
            param.setMemberId(memberId);
            param.setState(MemberConstants.IdcardBind.IdcardBindState.UNBINDED);
            param.setTimeEdit(new Date());
            idcardBindDao.update(param);
        }

        if (null != idcard) {
            param.setIdcard(idcard);
            param.setState(MemberConstants.IdcardBind.IdcardBindState.UNBINDED);
            param.setTimeEdit(new Date());
            idcardBindDao.update(param);
        }

        BaseStoneResponse response = new BaseStoneResponse();
        setBaseResponse(response);
    }

    @Override
    public void checkParam() {
        if (null == memberId && StringUtil.isBlank(idcard)) {
            throw new BaseStoneException("4001", "身份证与memberId不能同时为空");
        }
    }

    @Override
    public String buildSignString() {
        Encrypt encrypt = new Encrypt();
        encrypt.addItem(new EncryptItem(super.getSiteId()));
        if (null != memberId) {
            encrypt.addItem(new EncryptItem(String.valueOf(memberId)));
        }
        if (!StringUtil.isBlank(idcard)) {
            encrypt.addItem(new EncryptItem(idcard));
        }
        encrypt.addItem(new EncryptItem(super.getTime()));
        return encrypt.buildSign();
    }

    @Override
    public String getSiteName() {
        return MemberConstants.IDCARD_UNBIND;
    }

    public Integer getMemberId() {
        return memberId;
    }

    public void setMemberId(Integer memberId) {
        this.memberId = memberId;
    }

    public String getIdcard() {
        return idcard;
    }

    public void setIdcard(String idcard) {
        this.idcard = idcard;
    }

    public IdcardBindDao getIdcardBindDao() {
        return idcardBindDao;
    }

    public void setIdcardBindDao(IdcardBindDao idcardBindDao) {
        this.idcardBindDao = idcardBindDao;
    }
}
