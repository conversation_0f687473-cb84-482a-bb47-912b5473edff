package com.shunwang.passport.swpaySDK.web.action;

import com.shunwang.baseStone.context.BaseStoneContext;
import com.shunwang.baseStone.context.RedisContext;
import com.shunwang.basepassport.binder.common.BinderConstants;
import com.shunwang.basepassport.binder.dao.BinderDao;
import com.shunwang.basepassport.binder.exception.*;
import com.shunwang.basepassport.binder.pojo.Binder;
import com.shunwang.basepassport.binder.pojo.MobileBinder;
import com.shunwang.basepassport.binder.pojo.PersonalSendNumber;
import com.shunwang.basepassport.commonExp.UserFormateErrorExp;
import com.shunwang.basepassport.context.UserContext;
import com.shunwang.basepassport.safeNotice.constants.SafeNoticeConstants;
import com.shunwang.basepassport.user.common.MemberUtil;
import com.shunwang.basepassport.user.common.UserCheckUtil;
import com.shunwang.passport.bind.exception.MobileLoginAsAccountExistExp;
import com.shunwang.passport.bind.exception.TokenInvalidExp;
import com.shunwang.passport.common.context.DomainContext;
import com.shunwang.passport.common.geetest.GeetestUtil;
import com.shunwang.passport.safeNotice.service.SafeNoticeSendSmsService;
import com.shunwang.passport.swpaySDK.exception.SWPaySDKBindException;
import com.shunwang.util.date.DateUtil;
import com.shunwang.util.encrypt.AesEncrypt;
import com.shunwang.util.lang.StringUtil;
import org.apache.commons.lang.StringUtils;
import org.apache.struts2.ServletActionContext;

import java.io.IOException;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @since 2015-05-06
 */
public class MobileBindAction extends BindBaseAction {

    private static final long serialVersionUID = -1352331193353202024L;

	//修改手机绑定
	private String target = "Mobile" ;
	private String tokenid ;
	private String source ;
    private MobileBinder mobileBinder;
    private boolean showGt;

	public String showBindMobile(){
		setMember(UserContext.getUser());
		showGt = GeetestUtil.isShowGt();
		//判断是否已经绑定了email
		if(getMember().getIsBindMobile()){
			try {
				ServletActionContext.getResponse().sendRedirect(DomainContext.getAppServer() + "/front/swpaysdk/mobileBindMode.htm");
			} catch (IOException e) {
				logger.error("showBindMobile重定向出错", e);
			}
			return null;
		}
		return SUCCESS ;
	}

	public String changeMobileByMobile(){
		target="Mobile" ;
		return verifyOldMobile() ;
	}

	public String changeEmailByMobile(){
		target="Email" ;
		return verifyOldMobile() ;
	}

	@Override
	protected boolean checkGt() {
		return GeetestUtil.validate(getRequest());
	}

	public String verifyOldMobile(){
		this.setMember(UserContext.getMember()) ;
		showGt = GeetestUtil.isShowGt();
		if(null == getOldNumber())
			return INPUT ;
		try {
			validateActiveNo();
		}catch (ParamNullExp ex){
			this.setMsg("请输入短信验证码") ;
			return INPUT ;
		}catch (TimeOutAndInvalidExp ex){
			this.setMsg("短信验证码已过期,请重新获取") ;
			return INPUT ;
		}catch (ActiveNoErrorExp ex){
			this.setMsg("短信验证码输入不正确,请重新输入") ;
			return INPUT ;
		}catch (BindNumErrorExp exp){
			this.setMsg("短信验证码已过期,请重新获取") ;
			return INPUT ;
		}
		//验证验证码成功
		createToken() ;
		return target ;
	}

	/**
	 * 用户注销短信校验
	 * @return
	 */
	public String checkForCancel(){
		Map<String, Object> json = createResultJSON(false);
		this.setMember(UserContext.getMember()) ;
		if(null == getOldNumber())
			return INPUT ;
		try {
			setNewNumber(getOldNumber());
			validateActiveNo();
			json.put(RESULT, true);
			json.put(MSG, "发送成功");
            String smsToken = AesEncrypt.Encrypt(getNewNumber() + getActiveNo(), MemberUtil.getKey());
			json.put("smsToken", smsToken.substring(8));//去掉前8位
		}catch (ParamNullExp ex){
			json.put(MSG, "请输入短信验证码") ;
		}catch (TimeOutAndInvalidExp ex){
			json.put(MSG, "短信验证码已过期,请重新获取") ;
		}catch (ActiveNoErrorExp ex){
			json.put(MSG, "短信验证码输入不正确,请重新输入") ;
		}catch (BindNumErrorExp exp){
			json.put(MSG, "短信验证码已过期,请重新获取") ;
		} catch (Exception exp) {
            json.put(MSG, "校验失败") ;
        }
		writeJSON(json);
		return null;
	}

	private void createToken() {
		source = "Mobile" ;
		String tokenid = UUID.randomUUID().toString();
		RedisContext.getRedisCache().set(getCacheKey(target,source) , tokenid, 10, TimeUnit.MINUTES);
		logger.debug("createTokenid[key:" +  getCacheKey(target,source) + ", value:"+tokenid+"]");
		setTokenid(tokenid);
	}

	@Override
	protected void validateToken() {
		String tokenid = getTokenid();
		String  tokenidInMem = RedisContext.getRedisCache().get(getCacheKey(target,source));
		logger.debug("validateTokenid[key:" + getCacheKey(target,source)+" value:" +tokenidInMem+"]");
		if(tokenid == null || !tokenid.equals(tokenidInMem))
			throw new TokenInvalidExp();
	}

	@Override
    protected void checkFormat(String number, boolean isNew) {
        if (StringUtil.isBlank(number))
            throw new ParamNullExp((isNew ? BinderConstants.NEW : "") + getType());
        if (!UserCheckUtil.checkMobile(number))
            throw new UserFormateErrorExp((isNew ? BinderConstants.NEW : "") + getType());
    }

	@Override
	protected void checkBindIsSame() {
		if(null != getNewNumber()&&getNewNumber().equals(getMember().getMobile()))
			throw new SWPaySDKBindException("原手机号和新手机号码相同") ;
	}

	@Override
	protected String setSuccessMsg() {
		return "手机绑定成功" ;
	}

	/**
	 * 检查号码是否已经是登录账号或绑定的超过5个
	 */
	@Override
	protected void checkNewNumberIsAvailable() {
		if(null != getNewNumber()){
			if(null != getMemberDao().getByMobile(getNewNumber())){
				//已经是登录账号
				throw new MobileLoginAsAccountExistExp() ;
			}
			int cnt  =getMemberDao().getCntByMobile(getNewNumber()) ;
			//超过限制
			if(cnt>=BinderConstants.getMobileBinderLimit()){
				throw new MobileBindExp() ;
			}
			return ;
		}
		throw new ParamNullExp("新手机号码") ;
	}



	@Override
    protected void checkSendNumber() {
        super.checkSendNumber();
        PersonalSendNumber personalSendNumber = this.buildPersonalSendNumber();
        personalSendNumber.setMemberId(UserContext.getUserId());
        personalSendNumber.setBeginDate(DateUtil.ymdhmsFormat(DateUtil.getCurrentDateBegin()));
        Integer cnt = getPersonalSendNumberDao().findCntByTime(personalSendNumber);
        if (null != cnt && cnt >= 5)
            throw new OneDaySendFiveTimesExp();
    }

	@Override
    protected MobileBinder getBinder() {
        if (mobileBinder != null) return mobileBinder;

        if (StringUtils.isBlank(this.getActiveNo())) { // 获取验证码
            if (this.getMember().getIsBindMobile()) { // 更换绑定
                mobileBinder = (MobileBinder) getBinderDao().getById(UserContext.getUserId());
            } else { // 新绑定
                mobileBinder = new MobileBinder();
            }
        } else { // 执行绑定
            mobileBinder = (MobileBinder) getBinderDao().getById(UserContext.getUserId());
            if (mobileBinder == null)
                throw new SWPaySDKBindException("请先获取验证码");
        }
        if (this.getMember().getIsBindMobile()) { // 更换绑定
			if(null != getNewNumber()){
				//第二步
				mobileBinder.setNumber(this.getNewNumber()) ;
			}else{
				//第一步
				mobileBinder.setNumber(getMember().getMobile());
			}
            mobileBinder.setBusinessType(StringUtil.isNotBlank(getBusinessType()) ? getBusinessType() : BinderConstants.CHANGENUMBER);
        } else { // 新绑定
            mobileBinder.setNumber(this.getNewNumber());
            mobileBinder.setBusinessType(StringUtil.isNotBlank(getBusinessType()) ? getBusinessType() : BinderConstants.BINDNUMBER);
        }
        if (BinderConstants.MEMBER_CANCEL_TYPE.equals(mobileBinder.getBusinessType())) {
			mobileBinder.setNumber(getOldNumber());
        	mobileBinder.setSendNumber(mobileBinder.getNumber());
		}
		mobileBinder.setMember(this.getMember());
        return mobileBinder;
    }

	@Override
    protected String getType() {
        return BinderConstants.MOBILE;
    }

    @Override
    protected void sendNoticeSms() {
        SafeNoticeSendSmsService safeNoticeSendSmsService =
                (SafeNoticeSendSmsService) BaseStoneContext.getInstance().getBean("safeNoticeSendSmsService");
        safeNoticeSendSmsService.sendNoticeSMS(UserContext.getUserId(),
                SafeNoticeConstants.SAFE_NOTICE_STATE_MOBILE_CHANGE.toString(), null);
    }

    @Override
    protected Integer getDoType() {
        return BinderConstants.DOTYPE_MOBILE;
    }

    @SuppressWarnings("unchecked")
    @Override
    protected BinderDao<Binder> getBinderDao() {
        return (BinderDao<Binder>) BaseStoneContext.getInstance().getBean("mobileBinderDao");
    }

	public String getTarget() {
		return target;
	}

	public void setTarget(String target) {
		this.target = target;
	}

	public String getTokenid() {
		return tokenid;
	}

	public void setTokenid(String tokenid) {
		this.tokenid = tokenid;
	}

	public String getSource() {
		return source;
	}

	public void setSource(String source) {
		this.source = source;
	}

	public boolean isShowGt() {
		return showGt;
	}

	public void setShowGt(boolean showGt) {
		this.showGt = showGt;
	}
}
