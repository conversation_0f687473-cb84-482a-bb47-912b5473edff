package com.shunwang.baseStone.context;

import com.shunwang.util.StringUtil;

import java.util.*;

public class RedisContext {

    private RedisContext() {
        // no instance can be created
    }


    public static RedisOperation getRedisCache() {
        return (RedisOperation) BaseStoneContext.getInstance().getBean("redisOperation");
    }

    public static RedisResourceOperation getResourceCache() {
        return (RedisResourceOperation) BaseStoneContext.getInstance().getBean("redisResourceOperation");
    }

    public static RedisAgreementOperation getAgreementCache() {
        return (RedisAgreementOperation) BaseStoneContext.getInstance().getBean("redisAgreementOperation");
    }

    /**
     * 获取resource配置中的开关支持[y,1,yes,on]
     * @param type
     * @param name
     * @return
     */
    public static boolean getSwitchConfig(String type, String name, boolean defaultSwitch) {
        String resourceValue = getResourceCache().getResourceValue(type, name);
        if (StringUtil.isBlank(resourceValue)) {
            return defaultSwitch;
        }
        return "y".equalsIgnoreCase(resourceValue) ||
                "1".equals(resourceValue) ||
                "yes".equalsIgnoreCase(resourceValue) ||
                "on".equalsIgnoreCase(resourceValue);
    }
    public static boolean getSwitchConfig(String type, String name) {
        return getSwitchConfig(type, name, false);
    }

    /**
     * 获取resource配置中的数值
     * @param type
     * @param name
     * @return
     */
    public static Integer getIntConfig(String type, String name) {
        String resourceValue = getResourceCache().getResourceValue(type, name);
        if (StringUtil.isBlank(resourceValue)) {
            return null;
        }
        return Integer.parseInt(resourceValue);
    }

    /**
     * 获取resource配置中的数值
     * @param type
     * @param name
     * @return
     */
    public static Integer getIntConfig(String type, String name, Integer defaultValue) {
        String resourceValue = getResourceCache().getResourceValue(type, name);
        if (StringUtil.isBlank(resourceValue)) {
            return defaultValue;
        }
        return Integer.parseInt(resourceValue);
    }

    /**
     * 获取resource配置中的数值
     * xxxA,xxxB,...
     * @param type
     * @param name
     *
     */
    public static List<String> getStringListConfig(String type, String name) {
        List<String> list = new ArrayList<>();
        String resourceValue = getResourceCache().getResourceValue(type, name);
        if (StringUtil.isBlank(resourceValue)) {
            return list;
        }

        String[] split = resourceValue.split(",");
        list.addAll(Arrays.asList(split));
        return list;
    }

    /**
     * 获取resource配置中的数值
     * xxxA:vvvA,xxxB:vvvB,...
     * @param type
     * @param name
     *
     */
    public static Map<String, String> getStringMapConfig(String type, String name) {
        Map<String, String> map = new HashMap<>();
        String resourceValue = getResourceCache().getResourceValue(type, name);
        if (StringUtil.isBlank(resourceValue)) {
            return map;
        }

        String[] split = resourceValue.split(",");
        for (String config : split) {
            String[] item = config.split(":");
            map.put(item[0], item[1]);
        }
        return map;
    }
    /**
     * 获取resource配置中的数值
     * xxxA:vvvA,xxxB:vvvB,...
     * @param type
     * @param name
     *
     */
    public static Map<String, Integer> getIntMapConfig(String type, String name) {
        Map<String, Integer> map = new HashMap<>();
        String resourceValue = getResourceCache().getResourceValue(type, name);
        if (StringUtil.isBlank(resourceValue)) {
            return map;
        }

        String[] split = resourceValue.split(",");
        for (String config : split) {
            String[] item = config.split(":");
            map.put(item[0], Integer.parseInt(item[1]));
        }
        return map;
    }

}
