var UploadUtil ={
	init:function(option){
		
		if(!option) option={};
		if(!option.flash_url)
			option.flash_url=appServer+"/scripts/common/swfupload.swf";
		option.post_params= {"tmd" : "tmd&tmd=tmd"};	
		option.file_size_limit= "2 MB";
		option.file_types = "*.jpg;*.gif;*.jpeg;*.png";
		option.file_types_description = "*.jpg;*.gif;*.jpeg;*.png";
		option.file_upload_limit = 1000;
		option.file_queue_limit = 0;
		option.file_post_name="upload";
		option.debug= false;
		if(!option.button_image_url)
			option.button_image_url= appServer+"/images/front/v2/bt_bg17.gif";
		if(!option.button_width)
			option.button_width= "95";
		if(!option.button_height)
			option.button_height= "26"
		if(!option.button_text)
			option.button_text= "上传图片";
		if(!option.button_text_style)
			option.button_text_style = ".theFont { font-size: 16; }";
		if(!option.button_text_left_padding)
			option.button_text_left_padding= 23;
		if(!option.file_queue_error_handler)
			option.button_text_top_padding= 3;
		if(!option.file_queue_error_handler)
			option.file_queue_error_handler = UploadUtil.file_queue_error_handler;
			//    file_dialog_start_handler:isLoginImg,
		if(!option.swfupload_preload_handler)
			option.swfupload_preload_handler = UploadUtil.swfupload_preload_handler;
		if(!option.file_dialog_complete_handler)
			option.file_dialog_complete_handler = UploadUtil.file_dialog_complete_handler;
		if(!option.upload_error_handler)
			option.upload_error_handler = UploadUtil.upload_error_handler;
		if(!option.upload_success_handler)
			option.upload_success_handler = UploadUtil.upload_success_handler;
		UploadUtil.swf = new SWFUpload(option);
		return UploadUtil.swf;
	},
	/**
	 * 错误处理
	 */
	file_queue_error_handler:function(file, errorCode, message){
		if(errorCode==SWFUpload.QUEUE_ERROR.FILE_EXCEEDS_SIZE_LIMIT){ 
			showInfo("上传图片大小不能超过2M");
		    return false;
		}else if(errorCode == SWFUpload.QUEUE_ERROR.ZERO_BYTE_FILE){
			 showInfo("上传图片为0字节");
			return false;
		}
	},
	/**
	 * 上传前预处理
	 */
	swfupload_preload_handler:function(){
		return true;
	},
	/**
	 * 上传完成
	 */
	file_dialog_complete_handler:function(){
		
		UploadUtil.swf.startUpload();
		return true;
	},
	/**
	 * 上传出错
	 */
	upload_error_handler:function(){
		return true;
	},
	/**
	 * 成功上传
	 */
	upload_success_handler:function(file, serverData,responseReceived){
		alert(serverData);
	}
}