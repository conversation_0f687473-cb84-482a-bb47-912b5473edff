package com.shunwang.baseStone.sso.constant;

import java.util.HashSet;
import java.util.Set;
/**
 * @Described：登录常量
 * <AUTHOR> create at 2013-8-5 下午07:14:16
 * @ClassNmae com.shunwang.baseStone.sso.constant.LoginConstant
 */
public class LoginConstant {

	public static final String TYPE_FROM_WX_WORK = "wxWork";

	public enum GuestType{
		ID_CARD(1, "计费帐号"),
		;
		private int type;
		private String name;

		GuestType(int type, String name){
			this.type = type;
			this.name = name;
		}

		public int getType() {
			return type;
		}

		public String getName() {
			return name;
		}
	}

	public enum GuestLogin{
		GUEST(1, "游客"),
		NORMAL(0, "正常"),
		;
		private int type;
		private String name;
		GuestLogin(int type, String name) {
			this.type = type;
			this.name = name;
		}

		public int getType() {
			return type;
		}

		public String getName() {
			return name;
		}
	}
	
	/**
	 * 登录页关联数据
	 */
	public final static Set<String> LOGIN_PAGE_SET = new HashSet<>();
	
	static{
		LOGIN_PAGE_SET.add("default");
		LOGIN_PAGE_SET.add("inline");
	}

}
