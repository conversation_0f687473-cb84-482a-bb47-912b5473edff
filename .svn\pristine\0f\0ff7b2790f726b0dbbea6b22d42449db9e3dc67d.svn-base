<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd" >
<sqlMap namespace="com.shunwang.basepassport.user.pojo.Member" >
<typeAlias alias="member" type="com.shunwang.basepassport.user.pojo.Member"/>

	<resultMap id="memberMap" class="com.shunwang.basepassport.user.pojo.Member" >
	    <result column="member_id" property="memberId" jdbcType="INTEGER"/>
		<result column="member_name" property="memberName" jdbcType="VARCHAR"/>
		<result column="member_pwd" property="memberPwd" jdbcType="VARCHAR"/>
		<result column="real_name" property="realName" jdbcType="VARCHAR"/>
		<result column="nick_name" property="nickName" jdbcType="VARCHAR"/>
		<result column="bind_state" property="bindState" jdbcType="INTEGER"/>
		<result column="head_img" property="headImg" jdbcType="VARCHAR"/>
		<result column="member_type" property="memberType" jdbcType="INTEGER"/>
		<result column="member_state" property="memberState" jdbcType="INTEGER"/>
		<result column="time_add" property="timeAdd" jdbcType="DATETIME"/>
		<result column="time_edit" property="timeEdit" jdbcType="DATETIME"/>
		<result column="time_logon" property="timeLogon" jdbcType="DATETIME"/>
		<result column="person_cert_state" property="personCertState" jdbcType="INTEGER"/>
		<result column="company_cert_state" property="companyCertState" jdbcType="INTEGER"/>
		<result column="weak_pwd_state" property="weakPwdState" jdbcType="VARCHAR"/>
		<result column="pwd_err_time" property="pwdErrTime" jdbcType="datetime"/>
		<result column="pwd_err_num" property="pwdErrNum" jdbcType="INTEGER"/>
		<result column="company_name" property="companyName" jdbcType="varchar"/>
		<result column="site_type" property="siteType" jdbcType="varchar"/>
		<result column="title_name" property="titleName" jdbcType="varchar"/>
        <result column="safe_items_score" property="safeItemsScore" jdbcType="INTEGER"/>
        <result column="member_special_type" property="memberSpecialType" jdbcType="INTEGER"/>
        <result column="cafe_cert_state" property="cafeCertState" jdbcType="INTEGER"/>

		<result column="email_coded" property="emailCoded" jdbcType="VARCHAR"/>
		<result column="mobile_coded" property="mobileCoded" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap id="memberListMap" class="com.shunwang.basepassport.user.pojo.Member" >
        <result column="member_id" property="memberId" jdbcType="INTEGER"/>
        <result column="member_name" property="memberName" jdbcType="VARCHAR"/>
		<result column="weak_pwd_state" property="weakPwdState" jdbcType="VARCHAR"/>
		<result column="email_coded" property="emailCoded" jdbcType="VARCHAR"/>
		<result column="mobile_coded" property="mobileCoded" jdbcType="VARCHAR"/>
    </resultMap>
    <resultMap id="memberInfoMap" class="com.shunwang.basepassport.user.pojo.Member" extends="memberMap">
    	<result  column="id_card_no" property="memberInfo.idCardNo" jdbcType="varchar"/>
    	<result  column="id_card_no_coded" property="memberInfo.idCardNoCoded" jdbcType="varchar"/>
    </resultMap>
    
	<select id="get" resultMap="memberMap">
	   SELECT 
	   		a.member_id,
      		a.member_name,
      		a.member_pwd,
      		a.real_name,
      		a.nick_name,
      		a.bind_state,
      		a.head_img,
     		a.member_type,
      		IFNULL(a.member_state,0) as member_state,
      		a.time_add,
      		a.time_edit,
      		a.time_logon,
      		a.time_check,
      		a.person_cert_state,
      		a.company_cert_state,
			a.weak_pwd_state,
      		a.pwd_err_time,
      		a.pwd_err_num,
      		a.company_name,
      		a.site_type,
      		a.title_name,
      		a.safe_items_score,
      		a.cafe_cert_state,
      		a.member_special_type,
      		a.email_coded,
      		a.mobile_coded
  		FROM personal_member a
  		WHERE  a.member_id = #memberId:INTEGER#
	</select>
	<select id="getByOutMemberId" resultMap="memberMap">
	   SELECT 
	   		a.member_id,
      		a.member_name,
      		a.member_pwd,
      		a.real_name,
      		a.nick_name,
      		a.bind_state,
      		a.head_img,
     		a.member_type,
      		IFNULL(a.member_state,0) as member_state,
      		a.time_add,
      		a.time_edit,
      		a.time_logon,
      		a.time_check,
      		a.person_cert_state,
      		a.company_cert_state,
			a.weak_pwd_state,
      		a.pwd_err_time,
      		a.pwd_err_num,
      		a.company_name,
      		a.site_type,
      		a.title_name,
      		a.safe_items_score,
      		a.cafe_cert_state,
      		a.member_special_type,
      		a.email_coded,
      		a.mobile_coded
  		FROM personal_member a,personal_member_outsite b
  		where a.member_id = b.member_id and b.out_member_id = #value#
	</select>
	<select id="findByName" resultMap="memberMap" parameterClass="java.lang.String" >
	    SELECT
	        a.member_id,
      		a.member_name,
      		a.member_pwd,
      		a.real_name,
      		a.nick_name,
      		a.bind_state,
      		a.head_img,
     		a.member_type,
      		IFNULL(a.member_state,0) as member_state,
      		a.time_add,
      		a.time_edit,
      		a.time_logon,
      		a.time_check,
      		a.person_cert_state,
      		a.company_cert_state,
			a.weak_pwd_state,
      		a.pwd_err_time,
      		a.pwd_err_num,
      		a.company_name,
      		a.site_type,
      		a.title_name,
      		a.safe_items_score,
      		a.cafe_cert_state,
      		a.member_special_type,
      		a.email_coded,
      		a.mobile_coded
	   FROM personal_member a 
	   WHERE  a.member_name = #memberName:VARCHAR#
	   limit 1
	</select>
	<select id="findByEmail" resultMap="memberMap" parameterClass="member">
	    SELECT
	        a.member_id,
      		a.member_name,
      		a.member_pwd,
      		a.real_name,
      		a.nick_name,
      		a.bind_state,
      		a.head_img,
     		a.member_type,
      		IFNULL(a.member_state,0) as member_state,
      		a.time_add,
      		a.time_edit,
      		a.time_logon,
      		a.time_check,
      		a.person_cert_state,
      		a.company_cert_state,
			a.weak_pwd_state,
      		a.pwd_err_time,
      		a.pwd_err_num,
      		a.company_name,
      		a.site_type,
      		a.title_name,
      		a.safe_items_score,
      		a.cafe_cert_state,
      		a.member_special_type,
      		a.email_coded,
      		a.mobile_coded
	   FROM personal_member a 
	   WHERE  <![CDATA[ a.bind_state & 256 = 256 ]]>
	   and  a.email_coded = #emailCoded:VARCHAR#
	</select>

    <select id="findMemberByEmail" resultMap="memberMap" parameterClass="member">
	    SELECT
	        a.member_id,
      		a.member_name,
      		a.member_pwd,
      		a.real_name,
      		a.nick_name,
      		a.bind_state,
      		a.head_img,
     		a.member_type,
      		IFNULL(a.member_state,0) as member_state,
      		a.time_add,
      		a.time_edit,
      		a.time_logon,
      		a.time_check,
      		a.person_cert_state,
      		a.company_cert_state,
			a.weak_pwd_state,
      		a.pwd_err_time,
      		a.pwd_err_num,
      		a.company_name,
      		a.site_type,
      		a.title_name,
      		a.safe_items_score,
      		a.cafe_cert_state,
      		a.member_special_type,
			a.email_coded,
			a.mobile_coded
	    FROM personal_member a
		WHERE a.email_coded = #emailCoded:VARCHAR#
	</select>

    <select id="findCntByEmail" resultClass="java.lang.Integer" parameterClass="member">
        SELECT COUNT(1) FROM personal_member
        WHERE <![CDATA[ bind_state & 2 = 2 ]]>
        and email_coded = #emailCoded:VARCHAR#
    </select>
	
	<select id="findByMobile" resultMap="memberMap" parameterClass="member">
	    SELECT
	        a.member_id,
      		a.member_name,
      		a.member_pwd,
      		a.real_name,
      		a.nick_name,
      		a.bind_state,
      		a.head_img,
     		a.member_type,
      		IFNULL(a.member_state,0) as member_state,
      		a.time_add,
      		a.time_edit,
      		a.time_logon,
      		a.time_check,
      		a.person_cert_state,
      		a.company_cert_state,
			a.weak_pwd_state,
      		a.pwd_err_time,
      		a.pwd_err_num,
      		a.company_name,
      		a.site_type,
      		a.title_name,
      		a.safe_items_score,
      		a.cafe_cert_state,
      		a.member_special_type,
			a.email_coded,
			a.mobile_coded
	   FROM personal_member a 
	   WHERE <![CDATA[ a.bind_state & 128 = 128 ]]>
	   and a.mobile_coded = #mobileCoded:VARCHAR#
	</select>

    <select id="findMemberByMobile" resultMap="memberMap" parameterClass="member">
	    SELECT
	        a.member_id,
      		a.member_name,
      		a.member_pwd,
      		a.real_name,
      		a.nick_name,
      		a.bind_state,
      		a.head_img,
     		a.member_type,
      		IFNULL(a.member_state,0) as member_state,
      		a.time_add,
      		a.time_edit,
      		a.time_logon,
      		a.time_check,
      		a.person_cert_state,
      		a.company_cert_state,
			a.weak_pwd_state,
      		a.pwd_err_time,
      		a.pwd_err_num,
      		a.company_name,
      		a.site_type,
      		a.title_name,
      		a.safe_items_score,
      		a.cafe_cert_state,
      		a.member_special_type,
			a.email_coded,
			a.mobile_coded
	   FROM personal_member a
	   where a.mobile_coded = #mobileCoded:VARCHAR#
	</select>

	<select id="findCouldBindListByMobile" resultMap="memberMap"  parameterClass="member">
		SELECT
		 a.member_id,
      		a.member_name,
      		a.member_pwd,
      		a.real_name,
      		a.nick_name,
      		a.bind_state,
      		a.head_img,
     		a.member_type,
      		IFNULL(a.member_state,0) as member_state,
      		a.time_add,
      		a.time_edit,
      		a.time_logon,
      		a.time_check,
      		a.person_cert_state,
      		a.company_cert_state,
			a.weak_pwd_state,
      		a.pwd_err_time,
      		a.pwd_err_num,
      		a.company_name,
      		a.site_type,
      		a.title_name,
      		a.safe_items_score,
      		a.cafe_cert_state,
      		a.member_special_type,
			a.email_coded,
			a.mobile_coded
		FROM personal_member a
		WHERE <![CDATA[ bind_state & 1 = 1 ]]>
		and mobile_coded = #mobileCoded:VARCHAR#
		and member_state = 0
	</select>

    <select id="findCntByMobile" resultClass="java.lang.Integer" parameterClass="member">
        SELECT COUNT(1) FROM personal_member
        WHERE <![CDATA[ bind_state & 1 = 1 ]]>
		and mobile_coded = #mobileCoded:VARCHAR#
    </select>
	<!-- TODO该语句有问题，有相关业务时需要调整sql-->
	<select id="getByIDCardNo" resultMap="memberInfoMap" parameterClass="com.shunwang.basepassport.actu.pojo.PersonalActuInfo">
	    SELECT 
	    	a.member_id,
      		a.member_name,
      		a.member_pwd,
      		a.real_name,
      		a.nick_name,
      		a.bind_state,
      		a.head_img,
     		a.member_type,
      		IFNULL(a.member_state,0) as member_state,
      		a.time_add,
      		a.time_edit,
      		a.time_logon,
      		a.time_check,
      		a.person_cert_state,
      		a.company_cert_state,
			a.weak_pwd_state,
      		a.pwd_err_time,
      		a.pwd_err_num,
      		a.company_name,
      		a.site_type,
      		a.title_name,
      		a.safe_items_score,
      		b.id_card_no,
      		b.id_card_no_coded,
      		a.cafe_cert_state,
      		a.member_special_type,
			a.email_coded,
			a.mobile_coded
	   	FROM personal_member a
		join personal_personal_actuality_info b on a.member_id = b.member_id and b.id_card_no_coded <![CDATA[ <> ]]> '' and (a.company_cert_state=2 or a.person_cert_state=2) and b.info_state='2' and b.personal_actuality_id = (
    		select personal_actuality_id
    		from personal_personal_actuality_info c
    		where c.member_id = b.member_id and c.id_card_no_coded = b.id_card_no_coded and c.info_state = b.info_state limit 1
    	)
		WHERE b.id_card_no_coded = #idCardNoCoded:VARCHAR#
  </select>

<update id="update" parameterClass="member" >
    UPDATE personal_member SET 
        time_edit = NOW()
        <isNotEmpty prepend="," property="memberName"> member_name = #memberName:VARCHAR# </isNotEmpty>
        <isNotEmpty prepend="," property="memberPwd"> member_pwd = #memberPwd:VARCHAR# </isNotEmpty>
        <isNotEmpty prepend="," property="bindState"> bind_state = #bindState:INTEGER# </isNotEmpty>
        <isNotNull prepend="," property="realName"> real_name = #realName:VARCHAR# </isNotNull>
        <isNotNull prepend="," property="nickName"> nick_name = #nickName:VARCHAR# </isNotNull>
        <isNotEmpty prepend="," property="headImg"> head_img = #headImg:VARCHAR# </isNotEmpty>
        <isNotEmpty prepend="," property="timeLogon"> time_logon = #timeLogon:TIMESTAMP# </isNotEmpty>
        <isNotEmpty prepend="," property="memberState"> member_state = #memberState:CHAR# </isNotEmpty>
        <isNotEmpty prepend="," property="weakPwdState"> weak_pwd_state = #weakPwdState:VARCHAR# </isNotEmpty>
        <isNotEmpty prepend="," property="companyCertState"> company_cert_state = #companyCertState:VARCHAR# </isNotEmpty>
        <isNotEmpty prepend="," property="personCertState"> person_cert_state = #personCertState:VARCHAR# </isNotEmpty>
        <isNotEmpty prepend="," property="pwdErrNum"> pwd_err_num = #pwdErrNum:INTEGER# </isNotEmpty>
        <isNotEmpty prepend="," property="companyName"> company_name = #companyName:VARCHAR# </isNotEmpty>
        <isNotEmpty prepend="," property="titleName"> title_name = #titleName:VARCHAR# </isNotEmpty>
        <isNotEmpty prepend="," property="siteType"> site_type = #siteType:VARCHAR# </isNotEmpty>
        <isNotEmpty prepend="," property="pwdErrTime"> pwd_err_time = #pwdErrTime:Date# </isNotEmpty>
        <isNotEmpty prepend="," property="safeItemsScore"> safe_items_score = #safeItemsScore:INTEGER# </isNotEmpty>
		<isNotEqual prepend="," property="cafeCertState" compareValue="0"> cafe_cert_state = #cafeCertState:INTEGER#</isNotEqual>
		<isNotEqual prepend="," property="memberSpecialType" compareValue="0"> member_special_type = #memberSpecialType:INTEGER#</isNotEqual>
		<isNotEmpty prepend="," property="emailCoded"> email_coded = #emailCoded:VARCHAR# </isNotEmpty>
		<isNotEmpty prepend="," property="mobileCoded"> mobile_coded = #mobileCoded:VARCHAR# </isNotEmpty>
    WHERE 
        member_id = #memberId:INTEGER#
</update>
<insert id="insert" parameterClass="member" >
    INSERT INTO 
		personal_member( 
      		member_name,
      		member_pwd,
      		real_name,
      		nick_name,
      		bind_state,
		    member_sex,
      		head_img,
     		member_type,
      		member_state,
      		time_add,
      		person_cert_state,
      		company_cert_state,
			weak_pwd_state,
      		company_name,
      		site_type,
      		title_name,
			email_coded,
			mobile_coded
			)VALUES (
            #memberName:VARCHAR#,
          	#memberPwd:VARCHAR#,
        	#realName:VARCHAR#,
            #nickName:VARCHAR#,
            #bindState:bindState#,
			2,
        	#headImg:VARCHAR#,
            #memberType:INTEGER#,
        	#memberState:INTEGER#,
        	NOW(),
        	#personCertState:INTEGER#,
            #companyCertState:INTEGER#,
			#weakPwdState:VARCHAR #,
            #companyName:VARCHAR#,
            #siteType:VARCHAR#,
            #titleName:VARCHAR#,
			#emailCoded:VARCHAR#,
			#mobileCoded:VARCHAR#
		)
    <selectKey resultClass="INTEGER" keyProperty="memberId" >
         SELECT LAST_INSERT_ID()
    </selectKey>
</insert>
<select id="getByIdAndTime" resultMap="memberMap" parameterClass="member">
    SELECT 
		a.member_id,
		a.member_name,
		a.member_pwd,
		a.real_name,
		a.nick_name,
		a.bind_state,
		a.head_img,
		a.member_type,
		IFNULL(a.member_state,0) as member_state,
		a.time_add,
		a.time_edit,
		a.time_logon,
		a.time_check,
		a.person_cert_state,
		a.company_cert_state,
		a.weak_pwd_state,
		a.pwd_err_time,
		a.pwd_err_num,
		a.company_name,
		a.site_type,
		a.title_name,
		a.email_coded,
		a.mobile_coded
   FROM personal_member a 
   WHERE a.pwd_err_time = #pwdErrTime:dateTime# AND a.member_id=#memberId:INTEGER#
</select>
<select id="findCnt" parameterClass="com.shunwang.framework.ibatis.query.ConditionQuery" resultClass="java.lang.Integer" >
	select count(*) from personal_member t
	<include refid="Example_Where_Clause" />
</select>

</sqlMap>
