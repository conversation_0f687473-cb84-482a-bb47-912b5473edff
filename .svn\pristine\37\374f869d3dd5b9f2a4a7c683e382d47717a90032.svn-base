/**
 * Created by min.da on 14-12-20.
 */
$(function() {
    $('.sidebar .menu li:first').addClass('current');
    if (typeof EMAIL_STEP != 'undefined') {
        if (EMAIL_STEP == 1) EMAIL_STEP1.init();
        if (EMAIL_STEP == 2) EMAIL_STEP2.init();
        if (EMAIL_STEP == 3) EMAIL_STEP3.init();
        if (EMAIL_STEP == 4) EMAIL_STEP4.init();
    }
});

var EMAIL_PAGE_TYPE = {
    bind: 3,
    change: 4
}

var EMAIL_STEP1 = function() {
    var that = {};
    var $group = $('.form_group');
    var $form = $group.find('form');
    var $email = $form.find('input[name="number"]:text');
    var $newEmail = $form.find('input[name="newNumber"]:text');
    var $checkCode = $form.find('input[name="checkCode"]:text');
    var $goAppeal = $form.find('a[name="goAppeal"]');
    var $type = $form.find('input[name="emailBinder.businessType"]:hidden');
    var type = $type.val();
    var message = {
        c1001: '邮箱地址不能为空',
        c1002: '邮箱地址格式不正确',
        c1003: '原',
        c1004: '新',
        c1008: '请输入验证码',
        c1009: '验证码错误',
        c1010: '验证码格式不正确',
        c1011: '您输入的邮箱已经被其他账户绑定',
        c1012: '同一邮箱最多可绑定5个通行证账号'
    }
    function checkEmail() {
        $email.val($email.val().toLowerCase());
        var ev = $.trim($email.val());
        var oldBefore = '';
        if (type == EMAIL_PAGE_TYPE.change) {
            oldBefore = message.c1003;
        }
        if (ev.length == 0) {
            showTips($email, oldBefore + message.c1001);
            return false;
        }
        if (!isEmail(ev)) {
            showTips($email, oldBefore + message.c1002);
            return false;
        }
        showTips($email, "", true);
        return true;
    }
    function checkNewEmail() {
        $newEmail.val($newEmail.val().toLowerCase());
        var nmv = $.trim($newEmail.val());
        if (nmv.length == 0) {
            showTips($newEmail, message.c1004 + message.c1001);
            return false;
        }
        if (!isEmail(nmv)) {
            showTips($newEmail, message.c1004 + message.c1002);
            return false;
        }
        showTips($newEmail, "", true);
        return true;
    }
    function checkEmailBinding() {
        var $selector = $email;
        if (type == EMAIL_PAGE_TYPE.change) {
            $selector = $newEmail;
        }
        var result = false;
        $.ajax({
            url: $CONFIG.appServer + '/front/securityCenter/queryEmailBinding.htm',
            type: 'post',
            data: {email: $selector.val()},
            dataType: 'json',
            async: false,
            success: function(json) {
                if (json)
                    result = json.result;
            }
        });

        if (!result) {
            showTips($selector, message.c1012);
            return false;
        }

        showTips($selector, '', true);
        return true;
    }
    function validateCheckCode() {
        var cv = $.trim($checkCode.val());
        if (cv.length == 0) {
            showTips($checkCode, message.c1008);
            return false;
        }
        if (!/[\w]{4}/.test(cv)) {
            showTips($checkCode, message.c1010);
            return false;
        }

        var flag = false;
        $.ajax({
            type: 'post',
            url: $CONFIG.appServer + '/front/noLogin/chkCode.htm',
            data: {"checkCode" : cv, r: Math.random()},
            async: false,
            success: function(json) {
                if (json.flag) {
                    flag = true;
                } else {
                    showTips($checkCode, message.c1009);
                }
            }
        });
        if (!flag) return false;

        showTips($checkCode, "", true);
        return true;
    }
    function showTips(_target, _message, _ok) {
        if (typeof _ok != 'undefined' && _ok) {
            _target.removeClass('form_item_error').nextAll('span').text('');
        } else {
//            _target.focus();
            _target.addClass('form_item_error').nextAll('span').text(_message);
        }
    }
    var submitted = false;
    function submit() {
        var validResult = true;
        if (type != EMAIL_PAGE_TYPE.change) {
            validResult = validResult &&  checkEmail() && checkEmailBinding();
        }
        validResult = validResult && validateCheckCode();
        if (validResult && !submitted) {
            submitted = true;
            $form.submit();
        }
    }
    that.init = function() {
        $('ul.step_bar li:first').addClass('current');
        $.EmailAutoComplete($email);
        $email.on('blur', function(){checkEmail()}).on('focus', function(){showTips($email, "", true)});
        if (type == EMAIL_PAGE_TYPE.change) {
            $.EmailAutoComplete($newEmail);
            $newEmail.on('blur', function(){checkNewEmail()}).on('focus', function(){showTips($newEmail, "", true)});
        }
//        $checkCode.on('blur', function(){setTimeout(validateCheckCode, 250)}).on('focus', function(){showTips($checkCode, "", true)});
        $checkCode.on('focus', function(){showTips($checkCode, "", true)});
        $form.find('input').on("keydown", function(event) {
            if(event.keyCode == 13) {
                submit();
            }
        });
        $checkCode.next().next().on('click', function() {
            $checkCode.next().attr('src', $CONFIG.appServer + '/checkCode.htm?r=' + Math.random());
            return false;
        });
        $form.find('.btn_default_lg').on('click', function() {
            submit();
            return false;
        });
        if (typeof $goAppeal != 'undefined') {
            $goAppeal.on('click', function() {
                $type.remove();
                $('<input type="hidden" name="appeal.userName" value="'+$form.find('input[name="memberName"]:hidden').val()+'">').appendTo($form);
                $form.attr('action', $CONFIG.appServer + '/front/noLogin/goAppealChangeEmail_front.htm');
                $form.submit();
                return false;
            });
        }
    };
    return that;
}();

var EMAIL_STEP2 = function() {
    var that = {};
    var $mail = $('.mail_check');
    var $sendAgain = $mail.find('.desc a');
    var STEP2_SECOND = 60;
    function sendEmail() {
        var type = $('input[name="businessType"]:hidden').val();
        var number = $('input[name="number"]:hidden').val();
        var tokenid = $('input[name="tokenid"]:hidden').val();
        var postData = {"emailBinder.businessType": type, number: number,newNumber:number, isCheckCode: 0, r: Math.random(),tokenid:tokenid};
        if (type == EMAIL_PAGE_TYPE.change) {
            postData.number = $('input[name="oldNumber"]:hidden').val();
            postData.newNumber = number;
        }
        $.ajax({
            url: $CONFIG.appServer + '/front/securityCenter/emailSendAgain.htm',
            data: postData,
            dataType: 'json',
            type: 'post',
            traditional: true,
            success: function(json) {
                if (json.errorMsg) {
                    if( json.errorMsg == "tokenid_invaild")
                        $mail.find('.form_error_bar').html('操超时，请<a href="/">返回首页</a>重新操作');
                    else
                        $mail.find('.form_error_bar').text(json.errorMsg);
                } else {
                    sendEmailState(60);
                }
            },
            error: function() {
                ajaxReqTimeout();
            }
        });
    }
    function sendEmailState(s) {
        STEP2_SECOND = s;
        var onInterval = setInterval(function() {
            var str = '';
            if (STEP2_SECOND == 0) {
                clearInterval(onInterval);
            } else {
                str = '（' + STEP2_SECOND + '秒）';
            }
            $sendAgain.text('重新申请发送' + str);
            STEP2_SECOND--;
        }, 1000);
    }
    that.init = function() {
        var type = $('input[name="businessType"]:hidden').val();
        if (type == EMAIL_PAGE_TYPE.change)
            $('.sidebar .menu li:first').addClass('current');
        else
            $('ul.step_bar li:eq(1)').addClass('current');
        sendEmailState($mail.find('input[name="sec"]:hidden').val());
        $mail.find('.tip a').on('click', function() {
            goMail($mail.find('input[name="number"]:hidden').val());
            return false;
        });
        $sendAgain.on('click', function() {
            if (STEP2_SECOND > 0) return;
            sendEmail();
            return false;
        });
    };
    return that;
}();

var EMAIL_STEP3 = function() {
    var that = {};
    var $sucModal = $('#succ-modal');
    var $failModal = $('#fail-modal');
    function setAsLoginAccount() {
        $.ajax({
            url: $CONFIG.appServer + '/front/securityCenter/bindEmailAsLoginAccount.htm',
            type: 'post',
            dataType: 'json',
            success: function(json) {
                setAsLoginAccountCallback(json);
            },
            error: function() {
            }
        });
    }
    function setAsLoginAccountCallback(_json) {
        if (_json.result=="true") {
            $sucModal.modal('show');
            timerCall($sucModal.find('.modal-body .win_return span'),5,function(){
                window.location.href = $CONFIG.appServer + "/front/member/securityCenter_index.htm";
            });
        } else {
            if (_json.data != null) {
                $failModal.modal('show');
                var html = '';
                if (typeof _json.data.number != 'undefined') {
                    html = '<strong>' + _json.data.number + '</strong> ';
                }
                if (typeof _json.data.msg != 'undefined') {
                    html += _json.data.msg;
                }
                $failModal.find('.win_tip :last').html(html);
            }
        }
    }
    that.init = function() {
        $('ul.step_bar li:last').addClass('current');
        $('.message .btn_group .btn_default_lg').on('click', function() {
            setAsLoginAccount();
            return false;
        });
    };
    return that;
}();

var EMAIL_STEP4 = function() {
    var that = {};
    var $group = $('.form_group');
    var $form = $group.find('form');
    var $newEmail = $form.find('input[name="newNumber"]:text');
    var $type = $form.find('input[name="emailBinder.businessType"]:hidden');
    var type = $type.val();
    var message = {
        c1001: '邮箱地址不能为空',
        c1002: '邮箱地址格式不正确',
        c1003: '原',
        c1004: '新',
        c1008: '请输入验证码',
        c1009: '验证码错误',
        c1010: '验证码格式不正确',
        c1011: '您输入的邮箱已经被其他账户绑定',
        c1012: '同一邮箱最多可绑定5个通行证账号'
    }
    function checkNewEmail() {
        $newEmail.val($newEmail.val().toLowerCase());
        var nmv = $.trim($newEmail.val());
        if (nmv.length == 0) {
            showTips($newEmail, message.c1004 + message.c1001);
            return false;
        }
        if (!isEmail(nmv)) {
            showTips($newEmail, message.c1004 + message.c1002);
            return false;
        }
        showTips($newEmail, "", true);
        return true;
    }
    function checkEmailBinding() {
        var $selector =  $newEmail;
        var result = false;
        $.ajax({
            url: $CONFIG.appServer + '/front/securityCenter/queryEmailBinding.htm',
            type: 'post',
            data: {email: $selector.val().toLowerCase()},
            dataType: 'json',
            async: false,
            success: function(json) {
                if (json)
                    result = json.result;
            }
        });

        if (!result) {
            showTips($selector, message.c1012);
            return false;
        }

        showTips($selector, '', true);
        return true;
    }

    function showTips(_target, _message, _ok) {
        if (typeof _ok != 'undefined' && _ok) {
            _target.removeClass('form_item_error').nextAll('span').text('');
        } else {
            _target.addClass('form_item_error').nextAll('span').text(_message);
        }
    }
    var submitted = false;
    function submit() {
        var validResult = checkNewEmail();
        validResult = validResult && checkEmailBinding() ;
        if (validResult && !submitted) {
            submitted = true;

            $form.submit();
        }
    }
    that.init = function() {

        $('ul.step_bar li:eq(1)').addClass('current');
        $newEmail.on('blur', function(){checkEmail()}).on('focus', function(){showTips($newEmail, "", true)});
        $form.find('input').on("keydown", function(event) {
            if(event.keyCode == 13) {
                submit();
            }
        });


        $form.find('.btn_default_lg').on('click', function() {
            submit();
            return false;
        });
    };
    return that;
}();