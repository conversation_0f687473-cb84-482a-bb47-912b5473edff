package com.shunwang.basepassport.config;

import com.shunwang.basepassport.BaseTest;
import com.shunwang.util.date.DateUtil;


public class TestAgreementQuery extends BaseTest {
    @Override
    public void init() {
        params.put("agreementId", "1");
        params.put("siteId", "sw_pay");
        params.put("time", DateUtil.getCurrentDateStamp());
        params.put("signVersion", "1.0");
        params.put("sign", getSign(params));
    }

    @Override
    protected String getUrl() {
        return "http://interface.kedou.com/front/interface/agreementQuery.htm";
    }

    @Override
    protected String getMd5Key() {
        return "123456";
    }
}
