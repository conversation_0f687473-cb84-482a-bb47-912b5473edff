package com.shunwang.basepassport.manager.service.chuanglan;

import com.shunwang.basepassport.config.dao.ConfigInterfaceDao;
import com.shunwang.basepassport.config.pojo.ConfigInterface;
import com.shunwang.basepassport.manager.HttpMethod;
import com.shunwang.basepassport.manager.request.chuanglan.ChuanglanRequest;
import com.shunwang.basepassport.manager.response.chuanglan.ChuanglanResponse;
import com.shunwang.util.net.HttpClientUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Constructor;
import java.nio.charset.StandardCharsets;
import java.util.Map;

public class ChuanglanServiceClient {
    private static final Logger LOGGER = LoggerFactory.getLogger(ChuanglanServiceClient.class);

    private static ConfigInterfaceDao configInterfaceDao;

    public void setConfigInterfaceDao(ConfigInterfaceDao configInterfaceDao) {
        ChuanglanServiceClient.configInterfaceDao = configInterfaceDao;
    }

    public static ChuanglanResponse execute(ChuanglanRequest request) {
        try {
            ConfigInterface setting = configInterfaceDao.findByInterfaceKey(request.getInterfaceKey());
            request.doInterfaceSetting(setting);
            String url = request.buildUrl();
            Map<String, String> requestParams = request.buildParams();
            Map<String, String> headers = request.getHeaders();
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("----------------------------------------");
                LOGGER.debug("url:{}", url);
                LOGGER.debug("method:{}", request.getHttpMethod());
                LOGGER.debug("requestParams:{}", requestParams);
                LOGGER.debug("headers:{}", headers);
                LOGGER.debug("----------------------------------------");
            }
            String response = null;
            if (request.getHttpMethod() == HttpMethod.GET) {
                response = HttpClientUtils.doGet(url, requestParams, headers, null, StandardCharsets.UTF_8);
            } else {
                response = HttpClientUtils.doPost(url, requestParams, headers, null, StandardCharsets.UTF_8);
            }

            Class<ChuanglanResponse> responseClass = request.getResponseClass();
            Constructor constructor = responseClass.getConstructor();
            ChuanglanResponse resp = (ChuanglanResponse) constructor.newInstance();
            resp.setRawJson(response);
            return resp.parse();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

}
