package com.shunwang.basepassport.bind.email;

import com.shunwang.basepassport.BaseTest;
import com.shunwang.util.date.DateUtil;

public class TestUnbindEmailAsLogin extends BaseTest {

    @Override
    public void init() {
        params.put("siteId", "lz_test");
        params.put("memberId", "********");
        params.put("email", "<EMAIL>");
        params.put("time", DateUtil.getCurrentDateStamp());
        params.put("signVersion", "1.0");
        params.put("sign", getSign(params));
    }

    @Override
    protected String getUrl() {
        return "http://interface.kedou.com/front/interface/unBindEmailAsLoginAccount.htm";
    }

    @Override
    protected String getMd5Key() {
        return "123456";
    }

    @Override
    protected boolean getJson() {
        return false;
    }
}
