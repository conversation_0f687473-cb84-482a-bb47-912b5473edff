<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ExternalStorageConfigurationManager" enabled="true" />
  <component name="MavenProjectsManager">
    <option name="originalFiles">
      <list>
        <option value="$PROJECT_DIR$/pom.xml" />
      </list>
    </option>
  </component>
  <component name="ProjectRootManager" version="2" languageLevel="JDK_1_8" project-jdk-name="1.8" project-jdk-type="JavaSDK" />
  <component name="SvnBranchConfigurationManager">
    <option name="myConfigurationMap">
      <map>
        <entry key="$PROJECT_DIR$">
          <value>
            <SvnBranchConfiguration>
              <option name="branchUrls">
                <list>
                  <option value="https://svn.shunwang.com/svn/passportLine/com.shunwang.member.center/backup_branches" />
                  <option value="https://svn.shunwang.com/svn/passportLine/com.shunwang.member.center/branches" />
                  <option value="https://svn.shunwang.com/svn/passportLine/com.shunwang.member.center/tags" />
                </list>
              </option>
              <option name="trunkUrl" value="https://svn.shunwang.com/svn/passportLine/com.shunwang.member.center/trunk" />
            </SvnBranchConfiguration>
          </value>
        </entry>
      </map>
    </option>
  </component>
</project>