package com.shunwang.basepassport.user.pojo;

import com.shunwang.baseStone.core.detail.Detail;
import com.shunwang.baseStone.core.detail.DetailItem;
import com.shunwang.baseStone.core.detail.HasDetail;
import com.shunwang.baseStone.core.pojo.BaseStoneObject;
import com.shunwang.basepassport.detail.pojo.PersonalEditLog;
import com.shunwang.encrypt.core.annotation.EncryptEnabled;
import com.shunwang.encrypt.core.annotation.EncryptField;

import java.io.Serializable;
import java.util.Date;

/**
 * User:pf.ma
 * Date:2018/06/13
 * Time:14:22
 */
@EncryptEnabled
public class MemberAccountBind extends BaseStoneObject implements HasDetail {

	private static final long serialVersionUID = 4944744176303584779L;
	private PersonalEditLog personalEditLog = new PersonalEditLog();

	private Integer id ;
	private Integer memberId ;
	private String memberName;
	@EncryptField(ref = "phoneCoded")
	private String phone ;
	private String phoneCoded;

	private Integer qq ;
	private Integer weixin ;
	private Integer weibo ;
	private Integer apple;
	private Integer alipay;
	private Integer idCard;
	private Date timeAdd ;
	private Date timeEdit ;

	private MemberOutSite qqMemberOutSite ;
	private MemberOutSite wxMemberOutSite ;
	private MemberOutSite wbMemberOutSite ;
    private MemberOutSite appleMemberOutSite;
    private MemberOutSite alipayMemberOutSite;
    private MemberOutSite idCardMemberOutSite;

	public String getPhoneCoded() {
		return phoneCoded;
	}

	public void setPhoneCoded(String phoneCoded) {
		this.phoneCoded = phoneCoded;
	}

	public Integer getMemberId() {
		return memberId;
	}

	public void setMemberId(Integer memberId) {
		this.memberId = memberId;
	}

	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

	public boolean getIsBindQq(){
		return null != qq ;
	}

	public Integer getQq() {
		return qq;
	}

	public void setQq(Integer qq) {
		this.personalEditLog.addItem(new DetailItem("qq",this.getQq(),qq));
		this.qq = qq;
	}

	public boolean getIsBindWeixin(){
		return null != weixin ;
	}

	public Integer getWeixin() {
		return weixin;
	}

	public void setWeixin(Integer weixin) {
		this.personalEditLog.addItem(new DetailItem("weixin",this.getWeixin(),weixin));
		this.weixin = weixin;
	}

	public boolean getIsBindWeibo(){
		return null != weibo ;
	}

	public Integer getWeibo() {
		return weibo;
	}

	public void setWeibo(Integer weibo) {
		this.personalEditLog.addItem(new DetailItem("weibo",this.getWeibo(),weibo));
		this.weibo = weibo;
	}
	public boolean getIsBindAlipay(){
		return null != alipay ;
	}
	public Integer getAlipay() {
		return alipay;
	}

	public void setAlipay(Integer alipay) {
		this.personalEditLog.addItem(new DetailItem("alipay",this.getAlipay(), alipay));
		this.alipay = alipay;
	}

	public Date getTimeAdd() {
		return timeAdd;
	}

	public void setTimeAdd(Date timeAdd) {
		this.timeAdd = timeAdd;
	}

	public Date getTimeEdit() {
		return timeEdit;
	}

	public void setTimeEdit(Date timeEdit) {
		this.timeEdit = timeEdit;
	}

	@Override
	public Serializable getPk() {
		return id;
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public PersonalEditLog getPersonalEditLog() {
		return personalEditLog;
	}

	public void setPersonalEditLog(PersonalEditLog personalEditLog) {
		this.personalEditLog = personalEditLog;
	}


	public void beginBuildLog(String editItem) {
		this.beginBuildLog();
		this.personalEditLog.setEditItem(editItem) ;
	}

	public MemberOutSite getQqMemberOutSite() {
		return qqMemberOutSite;
	}

	public void setQqMemberOutSite(MemberOutSite qqMemberOutSite) {
		this.qqMemberOutSite = qqMemberOutSite;
	}

	public MemberOutSite getWxMemberOutSite() {
		return wxMemberOutSite;
	}

	public void setWxMemberOutSite(MemberOutSite wxMemberOutSite) {
		this.wxMemberOutSite = wxMemberOutSite;
	}

	public MemberOutSite getWbMemberOutSite() {
		return wbMemberOutSite;
	}

	public void setWbMemberOutSite(MemberOutSite wbMemberOutSite) {
		this.wbMemberOutSite = wbMemberOutSite;
	}

	public MemberOutSite getAlipayMemberOutSite() {
		return alipayMemberOutSite;
	}

	public void setAlipayMemberOutSite(MemberOutSite alipayMemberOutSite) {
		this.alipayMemberOutSite = alipayMemberOutSite;
	}

	@Override
	public void beginBuildLog(){
		this.personalEditLog.beginBuildLog(true);
	}

	@Override
	public Detail getDetail() {
		return personalEditLog;
	}

    public String getMemberName() {
        return memberName;
    }

    public void setMemberName(String memberName) {
        this.memberName = memberName;
    }

    public boolean getIsBindApple(){
        return null != apple ;
    }

    public Integer getApple() {
        return apple;
    }

    public void setApple(Integer apple) {
        this.personalEditLog.addItem(new DetailItem("apple",this.getApple(),apple));
        this.apple = apple;
    }

    public MemberOutSite getAppleMemberOutSite() {
        return appleMemberOutSite;
    }

    public void setAppleMemberOutSite(MemberOutSite appleMemberOutSite) {
        this.appleMemberOutSite = appleMemberOutSite;
    }

	public Integer getIdCard() {
		return idCard;
	}

	public void setIdCard(Integer idCard) {
		this.personalEditLog.addItem(new DetailItem("idCard", this.getIdCard(), idCard));
		this.idCard = idCard;
	}

	public MemberOutSite getIdCardMemberOutSite() {
		return idCardMemberOutSite;
	}

	public void setIdCardMemberOutSite(MemberOutSite idCardMemberOutSite) {
		this.idCardMemberOutSite = idCardMemberOutSite;
	}
}
