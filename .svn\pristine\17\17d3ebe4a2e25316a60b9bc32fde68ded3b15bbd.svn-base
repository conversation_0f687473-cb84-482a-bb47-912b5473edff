<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd" >
<sqlMap namespace="com.shunwang.baseStone.notice.pojo.Notice">
<resultMap class="com.shunwang.baseStone.notice.pojo.Notice" id="BaseResultMap">
	<result property="noticeId" column="noticeId" jdbcType="int"/>
	<result property="siteType" column="siteType" jdbcType="varchar"/>
	<result property="pageType" column="pageType" jdbcType="varchar"/>
	<result property="moduleName" column="moduleName" jdbcType="varchar"/>
	<result property="moduleContentCur" column="moduleContentCur" jdbcType="varchar"/>
	<result property="moduleContentNew" column="moduleContentNew" jdbcType="varchar"/>
	<result property="moduleState" column="moduleState" jdbcType="char"/>
	<result property="isOpen" column="isOpen" jdbcType="bit"/>
	<result property="userAdd" column="userAdd" jdbcType="varchar"/>
	<result property="timeAdd" column="timeAdd" jdbcType="datetime"/>
	<result property="userEdit" column="userEdit" jdbcType="varchar"/>
	<result property="timeEdit" column="timeEdit" jdbcType="datetime"/>
	<result property="userCheck" column="userCheck" jdbcType="varchar"/>
	<result property="timeCheck" column="timeCheck" jdbcType="datetime"/>
	<result property="rejectRemark" column="rejectRemark" jdbcType="varchar"/>
	<result property="remark" column="remark" jdbcType="varchar"/>
	<result property="filePath" column="filePath" jdbcType="varchar"/>
</resultMap>
<select id="find" resultMap="BaseResultMap" parameterClass="com.shunwang.framework.ibatis.query.ConditionQuery" >
	SELECT
		t.noticeId,
		t.siteType,
		t.pageType,
		t.moduleName,
		t.moduleContentCur,
		t.moduleContentNew,
		t.moduleState,
		t.isOpen,
		t.userAdd,
		t.timeAdd,
		t.userEdit,
		t.timeEdit,
		t.userCheck,
		t.timeCheck,
		t.rejectRemark,
		t.remark,
		t.filePath	from config_notice t
	<isParameterPresent >
	<include refid="Example_Where_Clause" />
	</isParameterPresent>
	order by
	<isNotNull property="orderCol" >
		$orderCol$
	</isNotNull>
	<isNull property="orderCol" >
		noticeId desc 
	</isNull>
	<isNotEqual property="rp" compareValue="0" >
	    limit #firstResult#, #rp#
	</isNotEqual>
</select>
<insert id="insert" parameterClass="com.shunwang.baseStone.notice.pojo.Notice" >
	insert into config_notice (
		siteType,
		pageType,
		moduleName,
		moduleContentCur,
		moduleContentNew,
		moduleState,
		isOpen,
		userAdd,
		timeAdd,
		userEdit,
		timeEdit,
		userCheck,
		timeCheck,
		rejectRemark,
		remark	)values(
		#siteType:varchar#,
		#pageType:varchar#,
		#moduleName:varchar#,
		#moduleContentCur:varchar#,
		#moduleContentNew:varchar#,
		#moduleState:varchar#,
		#isOpen:bit#,
		#userAdd:varchar#,
		#timeAdd:datetime#,
		#userEdit:varchar#,
		#timeEdit:datetime#,
		#userCheck:varchar#,
		#timeCheck:datetime#,
		#rejectRemark:varchar#,
		#remark:varchar#	)
	<selectKey resultClass="java.lang.Integer" keyProperty="noticeId" >
	    SELECT LAST_INSERT_ID()
	</selectKey>
</insert>
<update id="update" parameterClass="com.shunwang.baseStone.notice.pojo.Notice" >
	update config_notice set		
			moduleState=#moduleState:varchar#
		<isNotEmpty prepend="," property="moduleContentCur"> moduleContentCur=#moduleContentCur:varchar# </isNotEmpty>	
		<isNotEmpty prepend=","  property="moduleContentNew"> moduleContentNew=#moduleContentNew:varchar# </isNotEmpty>			
		<isNotEmpty prepend=","  property="userEdit"> userEdit=#userEdit:varchar# </isNotEmpty>	
		<isNotEmpty prepend=","  property="timeEdit"> timeEdit=#timeEdit:datetime# </isNotEmpty>	
		<isNotEmpty prepend=","  property="userCheck"> userCheck=#userCheck:varchar# </isNotEmpty>	
		<isNotEmpty prepend=","  property="remark"> remark=#remark:varchar# </isNotEmpty>		
		<isNotEmpty prepend=","  property="timeCheck"> timeCheck=#timeCheck:datetime# </isNotEmpty>			
		<isNotEmpty prepend=","  property="rejectRemark"> rejectRemark=#rejectRemark:varchar# </isNotEmpty>		
		<isNotEmpty prepend=","  property="isOpen"> isOpen=#isOpen:bit# </isNotEmpty>			
		 where noticeId = #noticeId:int#
</update>
<delete id="delete" parameterClass="com.shunwang.baseStone.notice.pojo.Notice" >
	delete from config_notice where noticeId=#noticeId:int#
</delete>
<select id="get" resultMap="BaseResultMap" parameterClass="Integer">
	select
	noticeId,
	siteType,
	pageType,
	moduleName,
	moduleContentCur,
	moduleContentNew,
	moduleState,
	isOpen,
	userAdd,
	timeAdd,
	userEdit,
	timeEdit,
	userCheck,
	timeCheck,
	rejectRemark,
	remark,
	filePath	from config_notice
	where noticeId = #value#
</select>
<select id="findCnt" parameterClass="com.shunwang.framework.ibatis.query.ConditionQuery" resultClass="java.lang.Integer" >
	select count(*) from config_notice t
	<include refid="Example_Where_Clause" />
</select>

<select id="getByPageType" resultClass="com.shunwang.baseStone.notice.pojo.Notice" parameterClass="java.lang.String">
    select
    noticeId as noticeId,   
   moduleContentCur as moduleContentCur,
	moduleContentNew as moduleContentNew,
	moduleState as moduleState,	
	pageType as pageType,
	isOpen as isOpen
   from config_notice
    where pageType = #value#  and isOpen = 1
</select>
</sqlMap>