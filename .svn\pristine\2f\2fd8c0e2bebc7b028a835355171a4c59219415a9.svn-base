package com.shunwang.basepassport.user.dao;

import com.shunwang.baseStone.core.dao.BaseStoneIbatisDao;
import com.shunwang.basepassport.user.pojo.AgreementRecord;

public class AgreementRecordDao extends BaseStoneIbatisDao<AgreementRecord> {

    public AgreementRecord getAgreementRecord(Integer memberId, Integer agreementId) {
        AgreementRecord query = new AgreementRecord();
        query.setMemberId(memberId);
        query.setAgreementId(agreementId);
        return (AgreementRecord) getSqlMapClientTemplate()
                .queryForObject(getStatementNameWrap("getAgreementRecord"), query);
    }

}
