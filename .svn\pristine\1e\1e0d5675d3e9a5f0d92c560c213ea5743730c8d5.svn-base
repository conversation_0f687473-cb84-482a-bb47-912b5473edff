package com.shunwang.basepassport.user.web;


import com.shunwang.baseStone.sysconfig.context.SysConfigContext;
import com.shunwang.baseStone.context.BaseStoneContext;
import com.shunwang.basepassport.user.common.HeadUrlUtil;
import com.shunwang.basepassport.user.dao.MemberDao;
import com.shunwang.basepassport.user.pojo.Member;
import com.shunwang.framework.struts2.action.BaseAction;

public class HeadImageAction extends BaseAction {
	
	private static final long serialVersionUID = 1665585228L;
	
	private String memberId;
	
	public String execute() throws Exception {
		MemberDao memberDao = (MemberDao)BaseStoneContext.getInstance().getBean("memberDao");
		Member member = memberDao.getById(memberId);

        String url = "";
        if (member != null ) {
            url = HeadUrlUtil.getSmallHeadImageUrl(member);
        } else  {//默认头像
            HeadUrlUtil util = new HeadUrlUtil();
            util.setHeadImageUrl(SysConfigContext.getImgUrl() + HeadUrlUtil.key + HeadUrlUtil.M_UPLOAD);
            url = util.defaultSmallUrl(null);
        } 
        
        getResponse().sendRedirect(url);
            
		return null;
	}
	
	public String getSmallHeadImageUrl(Member member) {
		return HeadUrlUtil.getSmallHeadImageUrl(member);
	}
	

    public void setMemberId(String memberId) {
        this.memberId=memberId;
    }
    public String getMemberId() {
        return this.memberId;
    }

}
