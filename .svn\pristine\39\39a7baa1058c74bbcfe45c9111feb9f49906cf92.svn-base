package com.shunwang.basepassport.actu.web;

import com.shunwang.baseStone.encrypt.Encrypt;
import com.shunwang.baseStone.encrypt.pojo.EncryptItem;
import com.shunwang.basepassport.actu.dao.CafeActuChangeDao;
import com.shunwang.basepassport.actu.pojo.CafeActuChangeInfo;
import com.shunwang.basepassport.actu.response.CafeActuChangeResponse;
import com.shunwang.basepassport.binder.exception.MemberNameUnMatchedMemberId;
import com.shunwang.basepassport.user.exception.MsgNotFoundExp;
import com.shunwang.basepassport.user.exception.MsgNullExp;
import com.shunwang.basepassport.user.pojo.Member;
import com.shunwang.basepassport.user.web.MemberAction;
import com.shunwang.util.lang.StringUtil;

/**
 * User:pf.ma
 * Date:2017/06/26
 * Time:11:38
 */
public class cafeActuChangeAction extends MemberAction {

	private Integer memberId ;
	private String memberName ;

	private CafeActuChangeDao cafeActuChangeDao ;

	@Override
	public void doProcess() {
		CafeActuChangeInfo cafeActuChangeInfo = (CafeActuChangeInfo) cafeActuChangeDao.getByMemberName(getMember().getMemberName());
		setBaseResponse(new CafeActuChangeResponse(getMember(),cafeActuChangeInfo)) ;
	}

	@Override
	public boolean checkMemberIsExist() {
		if(null != memberId){
			Member m = getDao().getByMemberId(getMemberId()) ;
			if(m == null){
				throw new MsgNotFoundExp("用户id") ;
			}
			setMember(m) ;
			if(StringUtil.isNotBlank(memberName)&& !getMember().getMemberName().equals(getMemberName())){
				throw new MemberNameUnMatchedMemberId() ;
			}
			return true ;
		}else if(StringUtil.isNotBlank(memberName)){
			return super.checkMemberIsExist();
		}else{
			throw new MsgNullExp("通行证账号") ;
		}
	}

	@Override
	public String buildSignString() {
		Encrypt encrypt=new Encrypt();
		encrypt.addItem(new EncryptItem(this.getSiteId()));
		encrypt.addItem(new EncryptItem(null == this.getMemberId()?"":String.valueOf(this.getMemberId())));
		encrypt.addItem(new EncryptItem(this.getMemberName())) ;
		encrypt.addItem(new EncryptItem(this.getTime()));
		return encrypt.buildSign() ;
	}


	@Override
	public String getSiteName() {
		return "查询网吧实名认证变更状态";
	}

	public Integer getMemberId() {
		return memberId;
	}

	public void setMemberId(Integer memberId) {
		this.memberId = memberId;
	}

	@Override
	public String getMemberName() {
		return memberName;
	}

	public void setMemberName(String memberName) {
		this.memberName = memberName;
	}

	public CafeActuChangeDao getCafeActuChangeDao() {
		return cafeActuChangeDao;
	}

	public void setCafeActuChangeDao(CafeActuChangeDao cafeActuChangeDao) {
		this.cafeActuChangeDao = cafeActuChangeDao;
	}
}
