package com.shunwang.basepassport.site;

import com.shunwang.baseStone.siteinterface.context.SiteContext;

import com.shunwang.basepassport.config.common.SiteInterfaceUtil;
import junit.framework.TestCase;

public class SiteContextTest extends TestCase {
	public void testGetSite(){
		SiteContext.setSiteId("sw_pay");
		SiteContext.setSiteName("手机邮箱用户名唯一性验证");
		System.out.println(SiteInterfaceUtil.loadSiteInterface());
		
	}
}
