<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">
	
	<bean id="memberHeadImgDao" class="com.shunwang.basepassport.user.dao.MemberHeadImgDao">
		<property name="sqlMapClientTemplate" ref="sqlMapClientTemplate"/>
	</bean>

	<bean id="memberHeadImgService" class="com.shunwang.basepassport.headImg.service.MemberHeadImgService">
		<property name="memberHeadImgDao" ref="memberHeadImgDao"/>
	</bean>

</beans>