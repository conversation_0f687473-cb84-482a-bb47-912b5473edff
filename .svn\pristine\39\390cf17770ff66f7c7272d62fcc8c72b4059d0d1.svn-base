package com.shunwang.basepassport.user.common;

import com.shunwang.util.lang.StringUtil;

import java.util.regex.Pattern;

/**
 * 敏感信息处理工具类
 */
public class SensitiveInfoUtil {

    private final static Pattern idCardNoCompile = Pattern.compile("(\\w{6})\\w{1,8}(\\w{4})");
    private final static Pattern bankCardNoCompile = Pattern.compile("(\\w{3})\\w{1,20}(\\w{3})");
    private final static Pattern realNameCompile = Pattern.compile("([^x00-xff])[^x00-xff]{1,8}");
    private final static Pattern mobileCompile = Pattern.compile("(\\w{3})\\w{1,4}(\\w{4})");
    private final static Pattern licenceNoCompile = Pattern.compile("(\\w{4})\\w{1,15}(\\w{4})");
    private final static Pattern emailCompile = Pattern.compile("(\\w{1}?)\\w{0,2}@\\w{0,2}(\\w{1}?)");

    private final static Pattern memberNameCompile = Pattern.compile("(\\w{2}?)\\w{0,20}(\\w{2})");
    private final static Pattern ipCompile = Pattern.compile("(\\w{1,3}.\\w{1,3}.)\\w{1,3}(.\\w{1,3})");

    /**
     * str = "xxx.xxx.xxx.xxx", 返回 xxx.xxx.***.xxx 仅显示前两段和最后一段
     * @param str
     * @return 脱敏后的ip
     */
    public static String ipStarReplace(String str) {
        return StringUtil.isBlank(str) ? StringUtil.EMPTY_STRING : ipCompile.matcher(str).replaceFirst("$1***$2");
    }
    /**
     * str = "xxxxxxxxxxxxxxxxxx", 返回 xxxxxx****xxxx 仅显示前6后4
     * @param str
     * @return
     */
    public static String idCardNoStarReplace(String str) {
        return StringUtil.isBlank(str) ? StringUtil.EMPTY_STRING : idCardNoCompile.matcher(str).replaceFirst("$1********$2");
    }
    /**
     * str = "xxxxxxxxxxx", 返回 xxx*****xxx 仅显示前3后3
     * @param str
     * @return
     */
    public static String bankCardNoStarReplace(String str) {
        return StringUtil.isBlank(str) ? StringUtil.EMPTY_STRING : bankCardNoCompile.matcher(str).replaceFirst("$1********$2");
    }

    /**
     * str = "xx", 返回 x* 仅显示姓氏， 名用*代替
     * str = "xxx", 返回 x**
     * str = "xxxxx", 返回 x****
     * @param str
     * @return
     */
    public static String realNameStarReplace(String str) {
        return StringUtil.isBlank(str) ? StringUtil.EMPTY_STRING : realNameCompile.matcher(str).replaceFirst("$1**");
    }

    /**
     * str = "xxxxxxxxxxx", 返回 xxx****xxxx 仅显示前3后4
     * @param str
     * @return
     */
    public static String mobileStarReplace(String str) {
        return StringUtil.isBlank(str) ? StringUtil.EMPTY_STRING : mobileCompile.matcher(str).replaceFirst("$1*****$2");
    }

    /**
     * str = "xxxxxxxxxxxxxxxxx", 返回 xxxx******xxxx 仅显示前4后4
     * @param str
     * @return
     */
    public static String licenceNoStarReplace(String str) {
        return StringUtil.isBlank(str) ? StringUtil.EMPTY_STRING : licenceNoCompile.matcher(str).replaceFirst("$1********$2");
    }

    /**
     * str = "<EMAIL>",返回  **@**x.xx @前一个字符的全部*代替，@后两位*代替
     * str = "<EMAIL>,返回 x**@**x.xx @前两字符的，第一个字符显示第二个字符*代替，@后同上
     * str = "<EMAIL> 返回 xx..**@**xx.xx" @前大于两个字符的，靠近@的两字符*代替，@后同上
     * @param str
     * @return
     */
    public static String emailStarReplace(String str) {
        if (StringUtil.isBlank(str)) {
            return StringUtil.EMPTY_STRING;
        }
        if (!str.contains("@")) {
            return str;
        }
        return emailCompile.matcher(str).replaceFirst(str.indexOf("@") < 2 ? "**@**$2" : "$1**@**$2");
    }

    /**
     * str = "xxxxxxxxxxxxxxxxx", 返回 xx***xx 仅显示前2后2
     * @param str
     * @return
     */
    public static String memberNameStarReplace(String str) {
        return StringUtil.isBlank(str) ? StringUtil.EMPTY_STRING : memberNameCompile.matcher(str).replaceFirst("$1***$2");
    }

}
