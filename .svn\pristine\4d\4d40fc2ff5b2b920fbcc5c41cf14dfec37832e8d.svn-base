package com.shunwang.basepassport.util;

import com.shunwang.basepassport.weixin.constant.HttpClientConstant;
import org.apache.commons.lang.StringUtils;
import org.apache.struts2.ServletActionContext;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
/**
 * @author: lj.zeng
 * @create: 2025-03-27 09:57:51
 * @Description:
 */
public class UserAgentUtil {

    private static final String TERMINAL_IPHONE = "iPhone";
    private static final String TERMINAL_ANDROID = "Android";
    private static final String TERMINAL_OPEN_HARMONY = "OpenHarmony";
    private static final String TERMINAL_WINDOWS = "Windows";
    private static final String TERMINAL_APACHE = "Apache-HttpClient";
    private static final String TERMINAL_MAC = "Macintosh";


    public static String getUserAgent() {
        ServletRequestAttributes attributes = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes());
        if (attributes == null) {
            return StringUtils.EMPTY;
        }
        return attributes.getRequest().getHeader(HttpClientConstant.Header.USER_AGENT);
    }

    public static String getUserAgentLowerCase() {
        return ServletActionContext.getRequest().getHeader(HttpClientConstant.Header.USER_AGENT).toLowerCase();
    }

    public static String getTerminal() {
        String userAgent = getUserAgent();
        if (StringUtils.isBlank(userAgent)) {
            return StringUtils.EMPTY;
        }
        if (userAgent.contains(TERMINAL_IPHONE)) {
            return TERMINAL_IPHONE;
        }
        if (userAgent.contains(TERMINAL_ANDROID)) {
            return TERMINAL_ANDROID;
        }
        if (userAgent.contains(TERMINAL_OPEN_HARMONY)) {
            return TERMINAL_OPEN_HARMONY;
        }
        if (userAgent.contains(TERMINAL_WINDOWS)) {
            return TERMINAL_WINDOWS;
        }
        if (userAgent.contains(TERMINAL_APACHE)) {
            return TERMINAL_APACHE;
        }
        if (userAgent.contains(TERMINAL_MAC)) {
            return TERMINAL_MAC;
        }
        return StringUtils.EMPTY;
    }

    public static boolean isWechatWeb() {
        String userAgent = getUserAgentLowerCase();
        return StringUtils.isNotBlank(userAgent) && getUserAgent().contains("micromessenger");
    }

    public static boolean isWechatInnerWeb() {
        String userAgent = getUserAgentLowerCase();
        return StringUtils.isNotBlank(userAgent) && userAgent.contains("micromessenger") && !userAgent.contains("windowswechat");
    }

    public static boolean isAliapyWeb() {
        String userAgent = getUserAgentLowerCase();
        return StringUtils.isNotBlank(userAgent) && getUserAgent().contains("aliapp");
    }
    public static boolean isQQ() {
        String userAgent = getUserAgentLowerCase();
        return StringUtils.isNotBlank(userAgent) && getUserAgent().contains("qq/");
    }
    public static boolean isWeibo() {
        String userAgent = getUserAgentLowerCase();
        return StringUtils.isNotBlank(userAgent) && getUserAgent().contains("weibo");
    }

}
