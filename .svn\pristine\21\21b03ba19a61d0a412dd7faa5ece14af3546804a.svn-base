package com.shunwang.basepassport.actu.web;

import com.shunwang.baseStone.context.TransactionContext;
import com.shunwang.baseStone.encrypt.Encrypt;
import com.shunwang.baseStone.encrypt.pojo.EncryptItem;
import com.shunwang.basepassport.actu.common.ActuUtil;
import com.shunwang.basepassport.actu.common.IdentityCardUtil;
import com.shunwang.basepassport.actu.constant.ActuConstant;
import com.shunwang.basepassport.actu.dao.ActuDao;
import com.shunwang.basepassport.actu.exception.OutActuExp;
import com.shunwang.basepassport.actu.pojo.CafeActuInfo;
import com.shunwang.basepassport.commonExp.MsgIsExistExp;
import com.shunwang.basepassport.commonExp.ParamFormatErrorExp;
import com.shunwang.basepassport.commonExp.ParamLenErrorExp;
import com.shunwang.basepassport.commonExp.ParamNotFoundExp;
import com.shunwang.basepassport.user.common.UserCheckUtil;
import com.shunwang.basepassport.user.exception.MsgNotFoundExp;
import com.shunwang.basepassport.user.web.MemberAction;
import com.shunwang.util.lang.StringUtil;

import java.util.Date;

/**
 * User:pf.ma
 * Date:2017/05/09
 * Time:18:08
 */
public class OutActuAction extends MemberAction{

	private static final long serialVersionUID = -2821949552598208410L;

	private String userName ;
	/**
	 * 网吧名称
	 */
	private String compName ;
	/**
	 *  网吧业主名称
	 * @return
	 */
	private String linkUser ;
	/**
	 * 手机号码
	 */
	private String linkMobile ;
	/**
	 * 网吧地址
	 * @return
	 */
	private String cafeAddr ;
	/**
	 * 网吧营业执照
	 * @return
	 */
	private String cafeLicenceImg ;
	/**
	 * 身份证正面照
	 * @return
	 */
	private String idCardImg1 ;
	/**
	 * 身份证背面照
	 * @return
	 */
	private String idCardImg2 ;
	/**
	 * 联系人身份证号码
	 */
	private String idCardNo;
	/**
	 * 是否人证合一，1为是，0为否，人证合一时，可以不传手持证件照和门面照
	 */
	private String unification ;
	/**
	 * 手持身份证照片
	 * @return
	 */
	private String idCardImg3 ;
	/**
	 * 门面照
	 */
	private String facadeImg ;

	private String cafeLicenceNo;
	/**
	 * 法人姓名
	 */
	private String legalPersonName;
	/**
	 * 网吧运营环境照
	 */
	private String businessPlaceImg;
	/**
	 * 区县编码 可追溯省市
	 */
	private String areaId;


	@Override
	public void doProcess() {
		if(ActuConstant.INFO_STATE_PASS.equals(getMember().getCafeCertState()+"")){
			throw new OutActuExp("该用户已通过实名认证") ;
		}
		if (checkActuData()) {
			throw new MsgIsExistExp("实名认证信息") ;
		}
		try {
			TransactionContext.beginTransaction();
			doActu();
			TransactionContext.commitTran();
		} catch (Exception e) {
			TransactionContext.rollbackTran();
			log.error("实名认证", e);
			throw new OutActuExp() ;
		}
	}

	private void doActu(){
		CafeActuInfo cafeActuInfo = buildCafeActuInfo() ;
		CafeActuInfo.CafeActuInfoWrapper wrapper=cafeActuInfo.getCafeActuInfoWrapper();
		wrapper.setMemberId(getMember().getMemberId());
		wrapper.setMemberName(getMember().getMemberName());
		wrapper.setInfoState(ActuConstant.INFO_STATE_UN_CHECK);
		wrapper.setCreateTime(new Date());
		wrapper.setEditTime(new Date());
		getMember().setCafeCertState(Integer.parseInt(ActuConstant.INFO_STATE_UN_CHECK));
		cafeActuInfo.save(getMember());
	}

	private CafeActuInfo buildCafeActuInfo(){
		CafeActuInfo cafeActuInfo = new CafeActuInfo() ;
		CafeActuInfo.CafeActuInfoWrapper wrapper = cafeActuInfo.getCafeActuInfoWrapper() ;
		wrapper.setCompName(compName) ;
		wrapper.setLinkUser(linkUser) ;
		wrapper.setLinkMobile(linkMobile) ;
		wrapper.setCafeAddr(cafeAddr) ;
		wrapper.setCafeLicenceImg(cafeLicenceImg) ;
		wrapper.setIdCardImg1(idCardImg1) ;
		wrapper.setIdCardImg2(idCardImg2) ;
		wrapper.setLegalPersonName(legalPersonName);
		wrapper.setBusinessPlaceImg(businessPlaceImg);
		if(StringUtil.isNotBlank(getIdCardNo())) {
			wrapper.setIdCardNo(getIdCardNo().toUpperCase());
		}
		if(StringUtil.isNotBlank(unification)){
			wrapper.setUnification(Integer.parseInt(unification));
		}
		wrapper.setFacadeImg(facadeImg) ;
		wrapper.setIdCardImg3(idCardImg3) ;
		if(StringUtil.isNotBlank(cafeLicenceNo)){
			wrapper.setCafeLicenceNo(cafeLicenceNo);
		}
		if (StringUtil.isNotBlank(areaId)) {
			wrapper.setAreaId(Integer.parseInt(areaId));
		}
		return cafeActuInfo ;

	}

	private boolean checkActuData(){
		ActuDao actuDao = ActuUtil.getActuDao(ActuConstant.ACTU_FLAG_CAFE) ;
		Integer unCheckActuInfo = actuDao.findLatestedUnCheckCntByMemberId(getMember().getMemberId());
		if(unCheckActuInfo > 0){
			return true ;
		}
		return false ;
	}


	@Override
	public String buildSignString() {
		Encrypt encrypt=new Encrypt();
		encrypt.addItem(new EncryptItem(this.getSiteId()));
		encrypt.addItem(new EncryptItem(this.getCafeAddr())) ;
		encrypt.addItem(new EncryptItem(this.getCafeLicenceImg())) ;
		encrypt.addItem(new EncryptItem(this.getCompName())) ;
		if(StringUtil.isNotBlank(facadeImg)) {
			encrypt.addItem(new EncryptItem(this.getFacadeImg()));
		}
		encrypt.addItem(new EncryptItem(this.getLinkMobile())) ;
		encrypt.addItem(new EncryptItem(this.getLinkUser())) ;
		encrypt.addItem(new EncryptItem(this.getIdCardImg1())) ;
		encrypt.addItem(new EncryptItem(this.getIdCardImg2())) ;
		encrypt.addItem(new EncryptItem(this.getIdCardImg3())) ;
		encrypt.addItem(new EncryptItem(this.getTime()));
		if(StringUtil.isNotBlank(unification)) {
			encrypt.addItem(new EncryptItem(this.getUnification()));
		}
		encrypt.addItem(new EncryptItem(this.getUserName()));
		if (StringUtil.isNotBlank(getIdCardNo())) {
			encrypt.addItem(new EncryptItem(this.getIdCardNo()));
		}
		return encrypt.buildSign() ;
	}

	@Override
	public void checkParam() {
		if(!checkMemberIsExist()){
			throw new MsgNotFoundExp("通行证账号") ;
		}
		if(StringUtil.isBlank(compName)){
			throw new ParamNotFoundExp("网吧名称") ;
		}
		if(compName.length()>128){
			throw new ParamFormatErrorExp("网吧名称","不能超过128个字符") ;
		}
		if(StringUtil.isBlank(linkUser)){
			throw new ParamNotFoundExp("网吧业主名称") ;
		}
		if(UserCheckUtil.getStrRealLength(linkUser) > 40){
			throw new ParamFormatErrorExp("网吧业主名称","不能超过40个字符") ;
		}
		if(StringUtil.isBlank(linkMobile)){
			throw new ParamNotFoundExp("手机号码") ;
		}
		if(!UserCheckUtil.checkMobile(linkMobile)){
			throw new ParamFormatErrorExp("手机号码") ;
		}
		if(cafeAddr.length() > 128){
			throw new ParamFormatErrorExp("网吧地址","不能超过128个字符") ;
		}
		if(StringUtil.isBlank(cafeLicenceImg) || !cafeLicenceImg.startsWith("http")){
			throw new ParamNotFoundExp("营业执照图片地址") ;
		}
		if(cafeLicenceImg.length() > 100){
			throw new ParamLenErrorExp("营业执照图片地址") ;
		}
		if(StringUtil.isBlank(idCardImg1) || !idCardImg1.startsWith("http")){
			throw new ParamNotFoundExp("身份证正面照地址") ;
		}
		if(idCardImg1.length() > 100){
			throw new ParamLenErrorExp("身份证正面照") ;
		}
		if(StringUtil.isBlank(idCardImg2) || !idCardImg2.startsWith("http")){
			throw new ParamNotFoundExp("身份证反面照地址") ;
		}
		if(idCardImg2.length() > 100){
			throw new ParamLenErrorExp("身份证反面照") ;
		}
		if(StringUtil.isNotBlank(idCardNo) && !IdentityCardUtil.validate(idCardNo)){
			throw new ParamFormatErrorExp("无效身份证号码,请输入18位有效身份证号");
		}

		if(StringUtil.isNotBlank(businessPlaceImg)) {
			if (!businessPlaceImg.startsWith("http")) {
				throw new ParamNotFoundExp("网吧运营环境照地址");
			}
			if (businessPlaceImg.length() > 100){
				throw new ParamLenErrorExp("网吧运营环境照") ;
			}
		}
		//法人与申请人一致时优先unification接口设值 2019-04-26
		if (StringUtil.isNotBlank(legalPersonName)) {
			unification = legalPersonName.equals(linkUser) ? ActuConstant.UNIFICATION.toString() : ActuConstant.UNIFICATION.toString();
		}
		if (StringUtil.isNotBlank(areaId) && (!StringUtil.isNumer(areaId) || areaId.length() != 6)) {
			throw new ParamFormatErrorExp("区县编码");
		}
		Integer unificationNum = null;
		if (StringUtil.isNotBlank(unification)) {
			try {
				unificationNum = Integer.parseInt(unification);
			} catch (Exception e) {
				throw new ParamFormatErrorExp("人证合一");
			}
			if (ActuConstant.UNIFICATION_MAP.get(unificationNum) == null) {
				throw new ParamNotFoundExp("人证合一");
			}

			//不管是否人证合一,都必传网吧门面照地址
			if (StringUtil.isBlank(facadeImg) || !facadeImg.startsWith("http")) {
				throw new ParamNotFoundExp("网吧门面照地址");
			}
			if (facadeImg.length() > 100) {
				throw new ParamLenErrorExp("网吧门面照地址");
			}

			if (!ActuConstant.UNIFICATION.equals(unificationNum)) { //人证不合一
				if (StringUtil.isBlank(idCardImg3) || !idCardImg3.startsWith("http")) {
					throw new ParamNotFoundExp("手持证件照地址");
				}
				if (idCardImg3.length() > 100) {
					throw new ParamLenErrorExp("手持证件照地址");
				}

			}
		}else{
			if (StringUtil.isBlank(idCardImg3) || !idCardImg3.startsWith("http")) {
				throw new ParamNotFoundExp("手持证件照地址");
			}
			if (idCardImg3.length() > 100) {
				throw new ParamLenErrorExp("手持证件照地址");
			}
		}
	}

	@Override
	public String getSiteName() {
		return "网吧实名认证" ;
	}

	@Override
	public String getMemberName() {
		return getUserName();
	}

	public String getCompName() {
		return compName;
	}

	public void setCompName(String compName) {
		this.compName = compName;
	}

	public String getLinkUser() {
		return linkUser;
	}

	public void setLinkUser(String linkUser) {
		this.linkUser = linkUser;
	}

	public String getLinkMobile() {
		return linkMobile;
	}

	public void setLinkMobile(String linkMobile) {
		this.linkMobile = linkMobile;
	}

	public String getCafeAddr() {
		return cafeAddr;
	}

	public void setCafeAddr(String cafeAddr) {
		this.cafeAddr = cafeAddr;
	}

	public String getCafeLicenceImg() {
		return cafeLicenceImg;
	}

	public void setCafeLicenceImg(String cafeLicenceImg) {
		this.cafeLicenceImg = cafeLicenceImg;
	}

	public String getIdCardImg1() {
		return idCardImg1;
	}

	public void setIdCardImg1(String idCardImg1) {
		this.idCardImg1 = idCardImg1;
	}

	public String getIdCardImg2() {
		return idCardImg2;
	}

	public void setIdCardImg2(String idCardImg2) {
		this.idCardImg2 = idCardImg2;
	}

	public String getIdCardImg3() {
		return idCardImg3;
	}

	public void setIdCardImg3(String idCardImg3) {
		this.idCardImg3 = idCardImg3;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getIdCardNo() {
		return idCardNo;
	}

	public void setIdCardNo(String idCardNo) {
		this.idCardNo = idCardNo;
	}

	public String getUnification() {
		return unification;
	}

	public void setUnification(String unification) {
		this.unification = unification;
	}

	public String getFacadeImg() {
		return facadeImg;
	}

	public void setFacadeImg(String facadeImg) {
		this.facadeImg = facadeImg;
	}

	public String getCafeLicenceNo() {
		return cafeLicenceNo;
	}

	public void setCafeLicenceNo(String cafeLicenceNo) {
		this.cafeLicenceNo = cafeLicenceNo;
	}

	public String getLegalPersonName() {
		return legalPersonName;
	}

	public void setLegalPersonName(String legalPersonName) {
		this.legalPersonName = legalPersonName;
	}

	public String getBusinessPlaceImg() {
		return businessPlaceImg;
	}

	public void setBusinessPlaceImg(String businessPlaceImg) {
		this.businessPlaceImg = businessPlaceImg;
	}

    public String getAreaId() {
        return areaId;
    }

    public void setAreaId(String areaId) {
        this.areaId = areaId;
    }
}
