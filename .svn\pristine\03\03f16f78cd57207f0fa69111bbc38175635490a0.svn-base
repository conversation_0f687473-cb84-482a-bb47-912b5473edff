/*公用部分*/

.a035, .a035:visited {

	color: #035C88;

	text-decoration: none;

}

.a035:hover {

	color:#ff6600;

}

.field {

	padding-bottom:5px; color:#FFF;

}

.login-box { width:350px;}

.login-box .error {

	padding:0;

	margin:0;

}

.login-box .error li {

	list-style:none;

	line-height: 25px;

	height:25px;

	padding: 2px 10px 0px 90px;

	color:red;

}

.login-box .login-text {

	border: 1px solid #D0D4D5;

	height: 18px;

	line-height: 18px;

	margin-right: 3px;

	padding: 3px 3px 2px;

	vertical-align: middle;

	width: 168px;

	color:#666;

}

.login-box #username {color:#FFF ; background-color: #FFF; border: 1px solid #000;}

.login-box #username1 { color:#FFF; background-color: #FFF; border: 1px solid #000;}



.login-box .field label {

	display: inline-block;

	padding-right: 10px;

	text-align: right;

	width: 75px;

}

.login-box .check-code-img {

	border: 1px solid #CECECE;

	height: 24px;

	vertical-align: middle;

	width: 70px;

}

.login-box .safe {

	overflow: hidden; color:#FFF;

	padding-left:90px;

}

.login-box .submit {

	overflow: hidden;

	padding-top:0px;

}

.login-box .msg {

	overflow: hidden;

}

.login-box .msg p {

	float: none;

	white-space: normal;

	word-wrap: break-word;

}

.login-box .change-code {

	display: inline-block;

	line-height: 14px;

	margin: 0 0 0 4px;

	vertical-align: middle;

}

.login-box .checkcode {

	width: 60px;

}

.login-box .forget-pw {

	line-height:26px;

}

.login-box .submit {

	text-align:center;

}

.login-box .submit .enter, .login-box .submit .cancel {

	background: url(../images/button-bg1.gif) no-repeat top;

	border: 0 none;

	cursor: pointer;

	display: inline-block;

	font-size: 0;

	height: 30px;

	line-height: 100px;

	margin-right: 6px;

	overflow: hidden;

	vertical-align: middle;

	width:121px;

}

.login-box .submit .enter {

	background-position:left top;

}

.login-box .submit .enter:hover {

	background-position:left bottom;

}

.login-box .submit .cancel {

	display:none;

}

.line-one, .line-two, .text-three {

	display:none;

}

