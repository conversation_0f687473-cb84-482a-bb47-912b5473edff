<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="RemoteRepositoriesConfiguration">
    <remote-repository>
      <option name="id" value="internal-snapshots" />
      <option name="name" value="snapshot internal lib" />
      <option name="url" value="http://nexus.shunwang.com/nexus/content/groups/publicsnapshot/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="Extract-PDF-Excel" />
      <option name="name" value="Extract-PDF-Excel" />
      <option name="url" value="https://raw.githubusercontent.com/eadgyo/Extract-PDF-Excel/master/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="released internal lib" />
      <option name="url" value="http://nexus.shunwang.com/nexus/content/groups/public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Maven Central repository" />
      <option name="url" value="https://repo1.maven.org/maven2" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="jboss.community" />
      <option name="name" value="JBoss Community repository" />
      <option name="url" value="https://repository.jboss.org/nexus/content/repositories/public/" />
    </remote-repository>
  </component>
</project>