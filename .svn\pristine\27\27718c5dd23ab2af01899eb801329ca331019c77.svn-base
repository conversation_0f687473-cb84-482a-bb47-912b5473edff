package com.shunwang.basepassport.user.action;

import com.shunwang.basepassport.BaseTest;
import com.shunwang.util.date.DateUtil;
import com.shunwang.util.encrypt.AesEncrypt;
import com.shunwang.util.encrypt.Md5Encrypt;
import org.junit.Test;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;

/**
 * User:pf.ma
 * Date:2017/07/03
 * Time:17:19
 */
public class TestMobileSend extends BaseTest{
	@Override
	public void init() {
		params.put("siteId", "sw_pay");
//		params.put("userName", "");
		params.put("mobile","18254311987") ;
//		params.put("newMobile","")
		params.put("interfaceType", "13");
		params.put("singleBindToken","") ;
		params.put("time", DateUtil.getCurrentDateStamp());
		params.put("signVersion", "1.0");
		params.put("sign", getSign(params));
	}

	@Override
	protected String getUrl() {
		return "http://interface.kedou.com/front/interface/outMobileSendActiveNo.htm";
	}

	@Override
	protected String getMd5Key() {
		return "123456";
	}
}
