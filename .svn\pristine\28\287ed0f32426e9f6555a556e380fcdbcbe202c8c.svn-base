<%@ page contentType="text/html; charset=UTF-8"%>
<%@ include file="../common/taglibs.jsp" %>
<jsp:useBean id="copyrightNow" class="java.util.Date" />
<fmt:formatDate value="${copyrightNow}" type="both" dateStyle="long" pattern="yyyy" var="cuurentYeah"/>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>顺网SSO 登录页</title>
    <link href="${staticServer}/${cdnVersion}/style/public.css" rel="stylesheet" type="text/css" />
    <link href="${staticServer}/${cdnVersion}/style/public-gray.css" rel="stylesheet" type="text/css"/>
    <link href="${staticServer}/${cdnVersion}/style/default.css" rel="stylesheet" type="text/css" />
    <link href="${staticServer}/${cdnVersion}/style/loginDefault.css" rel="stylesheet" type="text/css" />
    <link href="${staticServer}/${cdnVersion}/style/login_default_next.css" rel="stylesheet" type="text/css" />
    <c:if test="${not empty loginCss && not empty loginCss.cssURL }">
        <link href="${loginCss.cssURL}" rel="stylesheet" type="text/css"/>
    </c:if>
</head>
<body>
    <div class="box">
        <div class="textss">使用您的顺网通行证账号访问<span>${site_name }</span>，一个账号通行更方便。</div>
        <div class="loginbox">
            <div class="ssologin">
                <div class="phone_box">
                    <div class="phone_text">
                    手机动态密码已开启，请输入验证码
                    </div>
                    <div id="showInfo" class="phone_wrong">${msg}</div>
                    <form id="dynamicForm" name="dynamicForm"  action="${appServer}/loginNext.do" method="post" onkeydown="if(event.keyCode == 13) return loginNext();">
                        <input type="hidden" name="userName" value="${member.memberName}" />
                        <input type="hidden" name="callbackUrl" value="${callbackUrl}" />
                        <input type="hidden" name="site_id" value="${site_id}"/>
                        <input type="hidden" name="cssSiteId" value="${cssSiteId}"/>
                        <input type="hidden" name="loginType" value="${member.loginType}"/>
                        <input type="hidden" name="loginPage"  value="default"/>
                        <input type="hidden" name="second" id="second" value="${second}"/>
                        <div class="iphone_input">
                        <input id="dynamicPwd" name="dynamicPwd" type="text" placeholder="请输入验证码" maxlength="6" value="" onpaste="value=value.replace(/[^\0-9\.]/g,'')" onkeyup="value=value.replace(/[^\0-9\.]/g,'')" onkeyup="unableSpaceKey('dynamicPwd');"/></div>
                        <input type="button" id="sendBtn" class="phone_sms" tabindex="2" value="" maxlength="10" onclick="sendAgain();"/>
                        <div class="phone_submit"><a href="#" onclick="javascript:loginNext();" onkeydown="if(event.keyCode == 13) loginNext();">确认</a></div>
                    </form>
                    <div class="phone_change"><a href="${identityServer}/appeal/step1" target="_blank">手机号码已变更？</a></div>
                </div>
            </div>
        </div>
    </div>
<script type="text/javascript" src="${staticServer}/${cdnVersion}/js/jquery-1.4.3.js"></script>
<script type="text/javascript" src="${staticServer}/${cdnVersion}/js/base.js"></script>
<script type="text/javascript">
    var appServer = "${appServer}";
    var sec = ${second};
    showSendAgain();

    function loginNext() {
        var dynamicPwd = $("#dynamicPwd").val();

        if(dynamicPwd == "") {
            showInfo("请输入验证码");
            return false;
        }

        var pattern =/^[0-9]{6}$/;
        if(!pattern.test(dynamicPwd)){
            showInfo("请输入6位数字组成的验证码");
            return false;
        }


        $("#dynamicForm").submit();
    }

    function showSendAgain(){
        var sendAgain=document.getElementById("sendBtn");
        if(null != sendAgain) {
            if(sec > 0){
                sendAgain.disabled=true;
                sendAgain.className="phone_sms";
                sendAgain.value = "重新发送"+sec.toString();
                sec = sec-1;
                $("#second").val(sec);
                setTimeout("showSendAgain()",1000);
            } else {
                sendAgain.className="phone_sms_resend";
                sendAgain.disabled=false;
                sendAgain.value = "重新发送";
            }
        }
    }

    function sendAgain() {
        clearInfo();
        var dataSub ={"userName":"${member.memberName}"};
        $.ajax( {
            url :appServer+"/getAgainDynamicPwd.do",
            data : dataSub,
            dataType : 'json',
            type : 'post',
            traditional:true,
            success : function(json) {
                if(!json.errorMsg){
                    var sendAgain=document.getElementById("sendBtn");
                    sendAgain.disabled=true;
                    sec = 60;
                    showSendAgain();
                } else {
                    showInfo(json.errorMsg);
                }
            },
            error : function() {

            }
         });
    }

    function showInfo(showCode){
        var showInfo = document.getElementById("showInfo");
        showInfo.innerHTML=showCode;
     }

    function clearInfo(){
        var showInfo = document.getElementById("showInfo");
        showInfo.innerHTML='';
    }

    function unableSpaceKey(id) {
        var obj = document.getElementById(id);
        var reg = /(\s)+/;
        if (reg.test(obj.value))
            obj.value = obj.value.replace(reg, "");
    }
</script>
</body>
</html>
