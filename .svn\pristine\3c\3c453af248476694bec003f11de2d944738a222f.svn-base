package com.shunwang.baseStone.useroutinterface.dao;

import java.util.List;

import com.shunwang.baseStone.core.dao.BaseStoneIbatisDao;
import com.shunwang.baseStone.loginelement.pojo.LoginElement;
import com.shunwang.baseStone.useroutinterface.pojo.OutOauthDir;
/**
 * @Described：
 * <AUTHOR> create at 2013-7-29 下午05:33:45
 * @ClassNmae com.shunwang.baseStone.useroutinterface.dao.OutOauthDirDao
 */
public class OutOauthDirDao extends BaseStoneIbatisDao<OutOauthDir> {
	
	@SuppressWarnings("unchecked")
	public List<OutOauthDir> findAllOutOauthDir(){
		return getSqlMapClientTemplate().queryForList(getStatementNameWrap("findAllOutOauthDir"));
	}
	
	@SuppressWarnings("unchecked")
	public List<OutOauthDir> findAll() {
		return getSqlMapClientTemplate().queryForList(getStatementNameWrap("findAll"));
	}
	
	@SuppressWarnings("unchecked")
	public List<OutOauthDir> findOutOauthDirByBus(LoginElement element) {
		return getSqlMapClientTemplate().queryForList(getStatementNameWrap("findOutOauthDirByBus"), element);
	}
	
	@SuppressWarnings("unchecked")
	public List<OutOauthDir> findOutOauthDirByCategoryId(LoginElement element) {
		return getSqlMapClientTemplate().queryForList(getStatementNameWrap("findOutOauthDirByCategoryId"), element);
	}

}
