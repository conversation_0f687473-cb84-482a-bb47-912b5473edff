<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<%@ page contentType="text/html;charset=UTF-8"%>
<%@ include file="/common/taglibs.jsp"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@page import="com.shunwang.basepassport.safeNotice.constants.SafeNoticeConstants"%>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <title>账号安全提醒 - 顺网通行证</title>
</head>
<body>

            <div class="shop_head">
                <strong>账号安全提醒</strong>
                当用户发生登录异常、修改密码、顺圆消费及其它等敏感操作时提供安全提醒服务。
            </div>
            <div class="safe_list">
                <ul>
                    <c:if test="${member.safeNoticePWDChangeIsOpen}">
                        <c:choose>
                            <c:when test="${memberSafeNotice.isOpenPWDChange}">

                            <li>

                        <span class="open">保护中</span>
                        <strong>密码修改提醒</strong>
                        <span class="desc">用户在进行修改密码操作时会发送短信提醒通知。</span>
						<span class="control">
							<a href="${appServer }/front/securityCenter/toCloseSafeNoticeItem.htm?itemState=<%=SafeNoticeConstants.SAFE_NOTICE_STATE_PWD_CHANGE %>" class="btn_default">关闭</a>
						</span>
                    </li>
                            </c:when>
                            <c:otherwise>
                                <li>
                                <span class="un_open">未开启</span>
                                <strong>密码修改提醒</strong>
                                <span class="desc">用户在进行修改密码操作时会发送短信提醒通知。</span>
						<span class="control">
							<a href="${appServer }/front/securityCenter/toOpenSafeNoticeItem.htm?itemState=<%=SafeNoticeConstants.SAFE_NOTICE_STATE_PWD_CHANGE %>" class="btn_default">立即开启</a>
						</span>
                                    </li>
                            </c:otherwise>
                        </c:choose>
                    </c:if>

                    <c:if test="${member.safeNoticeMobielChangeIsOpen}">
                        <c:choose>
                            <c:when test="${memberSafeNotice.isOpenMobielChange}">
                    <li>
                        <span class="open">保护中</span>
                        <strong>手机修改提醒</strong>
                        <span class="desc">用户在进行修改手机号码操作时会发送短信提醒通知。</span>
						<span class="control">
							<a href="${appServer }/front/securityCenter/toCloseSafeNoticeItem.htm?itemState=<%=SafeNoticeConstants.SAFE_NOTICE_STATE_MOBILE_CHANGE %>" class="btn_default">关闭</a>
						</span>
                    </li>
                            </c:when>
                            <c:otherwise>
                                <li>
                                    <span class="un_open">未开启</span>
                                    <strong>手机修改提醒</strong>
                                    <span class="desc">用户在进行修改手机号码操作时会发送短信提醒通知。</span>
						<span class="control">
							<a href="${appServer }/front/securityCenter/toOpenSafeNoticeItem.htm?itemState=<%=SafeNoticeConstants.SAFE_NOTICE_STATE_MOBILE_CHANGE %>" class="btn_default">立即开启</a>
						</span>
                                </li>
                            </c:otherwise>
                        </c:choose>
                    </c:if>

                    <c:if test="${member.safeNoticeEmailChangeIsOpen}">
                        <c:choose>
                            <c:when test="${memberSafeNotice.isOpenEmailChange}">
                    <li>
                        <span class="open">保护中</span>
                        <strong>邮箱修改提醒</strong>
                        <span class="desc">用户在进行修改邮箱地址操作时会发送短信提醒通知。</span>
						<span class="control">
							<a href="${appServer }/front/securityCenter/toCloseSafeNoticeItem.htm?itemState=<%=SafeNoticeConstants.SAFE_NOTICE_STATE_EMAIL_CHANGE %>" class="btn_default">关闭</a>
						</span>
                    </li>
                            </c:when>
                            <c:otherwise>
                        <li>
                            <span class="un_open">未开启</span>
                            <strong>邮箱修改提醒</strong>
                            <span class="desc">用户在进行修改邮箱地址操作时会发送短信提醒通知。</span>
						<span class="control">
							<a href="${appServer }/front/securityCenter/toOpenSafeNoticeItem.htm?itemState=<%=SafeNoticeConstants.SAFE_NOTICE_STATE_EMAIL_CHANGE %>" class="btn_default">立即开启</a>
						</span>
                        </li>
                            </c:otherwise>
                        </c:choose>
                    </c:if>

                    <c:if test="${member.safeNoticeQuestionChangeIsOpen}">
                        <c:choose>
                        <c:when test="${memberSafeNotice.isOpenQuestionChange}">
                    <li>
                        <span class="open">保护中</span>
                        <strong>密保修改提醒</strong>
                        <span class="desc">用户在进行修改密保问题操作时会发送短信提醒通知。</span>
						<span class="control">
							<a href="${appServer }/front/securityCenter/toCloseSafeNoticeItem.htm?itemState=<%=SafeNoticeConstants.SAFE_NOTICE_STATE_QUESTION_CHANGE %>" class="btn_default">关闭</a>
						</span>
                    </li>
                        </c:when>
                            <c:otherwise>
                        <li>
                            <span class="un_open">未开启</span>
                            <strong>密保修改提醒</strong>
                            <span class="desc">用户在进行修改密保问题操作时会发送短信提醒通知。</span>
						<span class="control">
							<a href="${appServer }/front/securityCenter/toOpenSafeNoticeItem.htm?itemState=<%=SafeNoticeConstants.SAFE_NOTICE_STATE_QUESTION_CHANGE %>" class="btn_default">立即开启</a>
						</span>
                        </li>
                            </c:otherwise>
                        </c:choose>
                    </c:if>
                </ul>
            </div>
            <script type="text/javascript">
               $('.sidebar .menu li :eq(0)').addClass('current');
            </script>
</body>
</html>