package com.shunwang.baseStone.sso.core.listenter;

import com.shunwang.baseStone.listener.ApplicationContextInitListener;
import com.shunwang.baseStone.sso.context.SsoDomainContext;

import javax.servlet.ServletContext;

public class ServerInfoListener extends ApplicationContextInitListener {

    @Override
    public void initApplication() {
        super.initApplication();
        setupContext(servletContext);
    }


    @SuppressWarnings("static-access")
    private void setupContext(ServletContext context) {
        SsoDomainContext ssoDomainContext = (SsoDomainContext) ctx.getBean("ssoDomainContext");
        context.setAttribute("ssoDomain", ssoDomainContext.getSsoDomain());
        context.setAttribute("ssoServer", ssoDomainContext.getSsoServer());
        context.setAttribute("interfaceServer", ssoDomainContext.getInterfaceServer());
        context.setAttribute("staticServer", ssoDomainContext.getStaticServer());
        context.setAttribute("cdnVersion", ssoDomainContext.getCdnVersion());
        context.setAttribute("identityServer", ssoDomainContext.getIdentityServer());
    }

}
