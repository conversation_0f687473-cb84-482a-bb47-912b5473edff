package com.shunwang.passport.weixin.action;

import com.shunwang.basepassport.user.pojo.Member;
import com.shunwang.passport.weixin.service.MyAccountService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.apache.struts2.ServletActionContext;
import com.shunwang.passport.weixin.constants.WeixinConstants;
import com.shunwang.baseStone.context.SessionContext;
import com.shunwang.passport.weixin.service.WeixinRemoteCall;
import com.shunwang.framework.struts2.action.BaseAction;

/**
 *
 * 处理微信Oauth2认证页的回调, 根据传回的code查询微信用户的openId, 
 * 然后放入Session. 同时如果有绑定的passport用户,也一起放入Session.
 * 并跳回微信用户请求的原始URL.
 *
 * <AUTHOR>
 * @since 2014-04
 *
*/ 
public class WeixinAuthAction extends BaseAction {

    private final static long serialVersionUID = 125231234L;
	private static Logger log = LoggerFactory.getLogger(WeixinAuthAction.class);

    /** 微信Oauth2认证成功后,跳转时带的code */
    private String code;
    /** 用户访问的原始url  */
    private String origUrl;

    private WeixinRemoteCall weixinRemoteCall;
    private MyAccountService myAccountService;

    /**
     * 根据code查询openId,放入session, 然后再跳转回原url.
     * 此action通常是OpenId拦截器自动转发过来.
     * @See OpenIdInterceptor.java
     */
    public String execute() {
        String openId = weixinRemoteCall.queryOpenId(code);
        SessionContext.put(WeixinConstants.KEY_OPENID, openId);
        //由于后续很多页面都需要检查微信openId是否绑定了用户
        //这里先把绑定用户查出来放到session,以便后续检查
        //在绑定或注册或解绑时,需同步更新WeixinConstants.KEY_WEIXIN_USER的值
        Member member =  myAccountService.queryBindMember(openId);
        if (member != null ) {
            SessionContext.put(WeixinConstants.KEY_WEIXIN_USER, member);
        }
        HttpServletResponse response = ServletActionContext.getResponse();
        try {
            response.sendRedirect(origUrl);
        } catch (IOException e) {
            log.warn("openid授权重定向出错:" + e.toString());
        }
        return null;
    }


    public void setCode(String code) {
        this.code=code;
    }
    public String getCode() {
        return this.code;
    }

    public void setWeixinRemoteCall(WeixinRemoteCall weixinRemoteCall) {
        this.weixinRemoteCall=weixinRemoteCall;
    }
    public WeixinRemoteCall getWeixinRemoteCall() {
        return this.weixinRemoteCall;
    }

    public void setOrigUrl(String origUrl) {
        this.origUrl=origUrl;
    }
    public String getOrigUrl() {
        return this.origUrl;
    }

    public void setMyAccountService(MyAccountService myAccountService) {
        this.myAccountService=myAccountService;
    }
    public MyAccountService getMyAccountService() {
        return this.myAccountService;
    }

}
