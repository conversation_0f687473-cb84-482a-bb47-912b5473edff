package com.shunwang.common;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;

/**
 *图片上传静态属性类
 * <AUTHOR>
 * @version Jan 14, 2010
 */

public class ImageUploadUtil {

	private static Logger log = LoggerFactory.getLogger(ImageUploadUtil.class) ;

	public static final String TYPE_NAME_HOME = "home";//首页

	public static final String TYPE_NAME_DETAIL = "detail";//详情页

	public static final String TYPE_NAME_NOTICE = "notify";//通知公告
	
	public static final String TYPE_NAME_ACTIVITY = "activity";//活动

	public static final String TYPE_NAME_BANK = "bank";//银行图片

	public static final String INAGE_ROUTE_HOME = "images/rightHome";//首页图片路径

	public static final String INAGE_ROUTE_DETAIL = "images/detail";//详情页图片路径

	public static final String INAGE_ROUTE_APP = "images/app";//应用图片路径
	
	public static final String INAGE_ROUTE_NOTICE = "images/notify";//通知通告图片路径
	
	public static final String INAGE_ROUTE_ACTIVITY = "images/activity";//活动图片路径
	
	public static final String INAGE_ROUTE_BANK = "images/bank";//银行图片路径

	public static String zipWidthHeightImageFile(File oldFile, File newFile, int width, int height, float quality) {

		if (oldFile == null) {
			return null;
		}
		try {
			/** 对服务器上的临时文件进行处理 */
			Image srcFile = ImageIO.read(oldFile);
			int w = srcFile.getWidth(null);
			int h = srcFile.getHeight(null);
			double bili;
			if(width>0){
				bili=width/(double)w;
				height = (int) (h*bili);
			}else{
				if(height>0){
					bili=height/(double)h;
					width = (int) (w*bili);
				}
			}

			String srcImgPath = newFile.getAbsoluteFile().toString();
//			System.out.println(srcImgPath);
			String subfix = "jpg";
			subfix = srcImgPath.substring(srcImgPath.lastIndexOf(".")+1,srcImgPath.length());

			BufferedImage buffImg = null;
			if(subfix.equals("png")){
				buffImg = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);
			}else{
				buffImg = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
			}

			Graphics2D graphics = buffImg.createGraphics();
			graphics.setBackground(new Color(255,255,255));
			graphics.setColor(new Color(255,255,255));
			graphics.fillRect(0, 0, width, height);
			graphics.drawImage(srcFile.getScaledInstance(width, height, Image.SCALE_SMOOTH), 0, 0, null);

			ImageIO.write(buffImg, subfix, new File(srcImgPath));

		} catch (FileNotFoundException e) {
			log.error("文件不存在",e) ;
		} catch (IOException e) {
			log.error("压缩图片错误",e);
		}
		return newFile.getAbsolutePath();
	}
	
}

