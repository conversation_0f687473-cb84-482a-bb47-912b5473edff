package com.shunwang.passport.changemobile.web;

import com.shunwang.baseStone.context.BaseStoneContext;
import com.shunwang.baseStone.context.RedisContext;
import com.shunwang.baseStone.key.SWKey;
import com.shunwang.baseStone.key.SWKeyContext;
import com.shunwang.baseStone.key.exception.KeyNotExistExp;
import com.shunwang.basepassport.actu.constant.ActuConstant;
import com.shunwang.basepassport.context.UserContext;
import com.shunwang.basepassport.find.common.AppealConstants;
import com.shunwang.basepassport.find.dao.AppealDao;
import com.shunwang.basepassport.find.pojo.Appeal;
import com.shunwang.basepassport.find.pojo.AppealExtendMsg;
import com.shunwang.basepassport.mobile.constants.MobileCheckCodeConstants;
import com.shunwang.basepassport.mobile.dao.MobileCheckCodeDao;
import com.shunwang.basepassport.mobile.pojo.MobileCheckCode;
import com.shunwang.basepassport.user.common.MemberConstants;
import com.shunwang.passport.common.context.DomainContext;
import com.shunwang.passport.common.geetest.GeetestUtil;
import com.shunwang.passport.find.web.AppealAction;
import com.shunwang.util.date.DateUtil;
import com.shunwang.util.lang.StringUtil;
import org.apache.commons.lang.StringUtils;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

public class AppealChangeMobileAction extends AppealAction {

	private static final long serialVersionUID = 1103444376786060933L;

	private Appeal appeal;


    private String dateType;
    private String beginDate;
    private String endDate;

	private List<AppealExtendMsg> extList;
	private String smsCheckCode;
	private String newMobile;
	private boolean showGt;

	/**
	 * ***********
	 * 创建日期: 2013-8-8
	 * 创建作者：JINBAO
	 * @param 
	 * @return String
	 * 功能：提交申诉理由
	 *************
	 */
	public String appealChangeMobileSuccess() {

		member = UserContext.getMember();

		//未登录之前,无member
		if(member == null){
			if (appeal == null || StringUtils.isBlank(appeal.getUserName())){
				return INPUT;
			}
			memberName = appeal.getUserName();
		}else{
			memberName = member.getMemberName();
		}


		String key = "submit_appeal_change_mobile_lock_" + memberName;

		try {

			if(RedisContext.getRedisCache().get(key) != null){
				log.warn("请匆重复操作");
				return SUCCESS; //直接跳转成功页面
			}
			RedisContext.getRedisCache().set(key,"lock", 1, TimeUnit.MINUTES);

			if(null == memberName)
				return INPUT;
			MobileCheckCode mobileCheckCode = getMobileCheckCode(memberName);
			if(null == mobileCheckCode) {
				setErrorMsg("请先获取验证码");
				return INPUT;
			}
			if(!newMobile.equals(mobileCheckCode.getMobile())) {
				setErrorMsg("更换的手机号码与获取短信的手机号码不一致");
				return INPUT;
			}
			if(!mobileCheckCode.getCheckCode().equals(smsCheckCode)) {
				setErrorMsg("手机短信验证码不正确，请重新输入");
				return INPUT;
			}
			if(DateUtil.compare(new Date(), mobileCheckCode.getSendTime(), DateUtil.ONE_MINUTE) > Integer.valueOf(MobileCheckCodeConstants.VALID_TIME_DEFAULT)) {
				setErrorMsg("手机短信验证码已过期，请重新发送");
				return INPUT;
			}

			//查找审核中
			AppealDao  appealDao=(AppealDao)BaseStoneContext.getInstance().getBean("appealDao");
			Appeal appealTemp = appealDao.findRecentAppealForMobile(memberName);
			//如果有申诉,而且正在审核中,则显示申诉审批结果
			if (appealTemp != null && AppealConstants.CHECKSTATE_WATITING.equals(appealTemp.getAppealState())) {
				log.warn("请匆重复操作");
				return SUCCESS; //直接跳转成功页面
			}

			AppealExtendMsg appealExt = new AppealExtendMsg(AppealConstants.APPEAL_TYPE_CHANGE_MOBILE, newMobile);		
			List<AppealExtendMsg> extList = new ArrayList<AppealExtendMsg>();
			extList.add(appealExt);
			appeal.setMobile(newMobile);
			appeal.setAppealType(AppealConstants.CHANGE_MOBILE_TYPE_APPEAL);	
			appeal.setExtList(extList);
		
			appeal.appealMsg();
		} catch(KeyNotExistExp e) {
			RedisContext.getRedisCache().del(key);
			setErrorMsg("执行顺序不正确");
			return INPUT;
		} catch (Exception e) {
			RedisContext.getRedisCache().del(key);

			log.error(e.getMessage(), e);
			super.initRegMsg();
			setErrorMsg("申诉单提交失败，请重试！");

			return INPUT;
		}
		return SUCCESS;
	}
	
	private String goAppealChangeMobile(){
		if(member == null)
			member = UserContext.getMember();
		//未登录之前,无member
		if(member == null){
			if(	(appeal!=null &&StringUtils.isBlank(appeal.getUserName())) && StringUtils.isBlank(memberName)){
				return INPUT;
			}
		}else{
			memberName = member.getMemberName();
		}

		SWKeyContext.put(new SWKey("changeMobileByAppeal"));
		this.getUserState(memberName );



		AppealDao appealDao=(AppealDao)BaseStoneContext.getInstance().getBean("appealDao");
		Appeal appealTemp = appealDao.findRecentAppealForMobile(memberName);

		//如果有申诉,而且正在审核中,则显示申诉审批结果
		return goAppealResult(appealTemp);

    }

	/**
	 * ***********
	 * 创建日期: 2013-8-8
	 * 创建作者：JINBAO
	 * @param 
	 * @return String
	 * 功能：跳转到申诉修改手机号码页面
	 *************
	 */
	public String appealInputInfo() throws IOException {
		if(UserContext.getMember()!=null){
			getResponse().sendRedirect(DomainContext.getIdentityServer() + "/appeal/step1?memberName=" + UserContext.getMember().getMemberName());
		} else {
			getResponse().sendRedirect(DomainContext.getIdentityServer() + "/appeal/step1");
		}
		return null;
//		findWay="3";
//		if(UserContext.getMember()==null  ){
//			if (StringUtil.isNotBlank(memberName) )
//				member = getMemberDao().getMember(memberName);
//			String valid = validateMemberNameAndCode();
//			if(valid!=null) return valid;
//		}else{
//			member = UserContext.getMember();
//			memberName = member.getMemberName();
//		}
//
//		//未绑定则先绑定(需要登录)
//		if(StringUtil.isNotBlank(memberName)){
//			getUserState(memberName);
//			Boolean isBind = (Boolean)this.getRequest().getAttribute("phoneIsBind");
//			if(isBind == null || !isBind){
//				return "bind";
//			}
//		}
//
//		String result = goAppealChangeMobile();
//		if(!SUCCESS.equals(result))
//			return result;
//
//        member = getMemberDao().getByName(memberName);
//		if(MemberConstants.MEMBER_SPECIAL_TYPE_CAFE.equals(member.getMemberSpecialType())){
//			//网吧业主,已实名
//			if(member.getCafeCertState().equals(Integer.parseInt(ActuConstant.INFO_STATE_PASS))){
//				this.getRequest().setAttribute("actuIsTrue", true); //用于标识是否需要实名认证 按钮是否出现
//				this.getRequest().setAttribute("memberType",MemberConstants.MEMBER_SPECIAL_TYPE_CAFE.equals(member.getMemberSpecialType()) ? "cafe":"person");//目前只有这两种
//				SWKeyContext.put(new SWKey("changeMobileByActu"));
//				return "realNameAppeal";
//
//			//网吧业主,未实名
//			}else{
//				SWKeyContext.get(new String[]{"changeMobileByAppeal"});
//				super.initRegMsg();
//				return "nonRealNameAppeal";
//			}
//		}
//
//        if (AppealConstants.USER_TYPE.equals(member.getMemberType()) && member.getPersonCertState().equals(Integer.parseInt(ActuConstant.INFO_STATE_PASS))) {
//            this.getRequest().setAttribute("actuIsTrue", true); //用于标识是否需要实名认证 按钮是否出现
//			this.getRequest().setAttribute("memberType",MemberConstants.MEMBER_SPECIAL_TYPE_CAFE.equals(member.getMemberSpecialType()) ? "cafe":"person");//目前只有这两种
//            SWKeyContext.put(new SWKey("changeMobileByActu"));
//            return "realNameAppeal";
//        } else {
//            SWKeyContext.get(new String[]{"changeMobileByAppeal"});
//            super.initRegMsg();
//            return "nonRealNameAppeal";
//        }
	}


	public String appealResetMobile() {
		if (appeal == null) return INPUT;
		appeal.setRealName(appeal.getRegRealName());
		appeal.setIdCardNo(appeal.getRegIdCard());
        if(dateType != null && ! dateType.equals("chooseDate")){
            Date schEndDate = DateUtil.getCurrentDateEnd();
            Date schBeginDate = null;
			
			if(dateType.equals("oneDay")){
				schBeginDate = DateUtil.getCurrentDateBegin();
			}
			if(dateType.equals("oneWeek")){
				schBeginDate = DateUtil.addDay(schEndDate, -7);
			}
			if(dateType.equals("oneMonth")){
				schBeginDate = DateUtil.zeroConvertTime(DateUtil.addMonth(schEndDate, -1));
			}
			if(dateType.equals("threeMonth")){
				schBeginDate = DateUtil.zeroConvertTime(DateUtil.addMonth(schEndDate, -3));
			}
			if(dateType.equals("halfYear")){
				schBeginDate = DateUtil.zeroConvertTime(DateUtil.addMonth(schEndDate, -6));
			}
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            beginDate = sdf.format(schBeginDate);
            endDate = sdf.format(schEndDate);
		}
        if(StringUtils.isBlank(appeal.getUserName()))
			appeal.setUserName(memberName);
        showGt = GeetestUtil.isShowGt();
		return SUCCESS;
    }
	
	public MobileCheckCode getMobileCheckCode(String memberName) {
		MobileCheckCodeDao  mobileCheckCodeDao = getMobileCheckCodeDao();
		MobileCheckCode paramMobileCheckCode = new MobileCheckCode();
		paramMobileCheckCode.setMemberName(memberName);
		paramMobileCheckCode.setType(MobileCheckCodeConstants.CHANGE_MOBILE);
		return mobileCheckCodeDao.findByNameAndType(paramMobileCheckCode);
	}
	
	private MobileCheckCodeDao getMobileCheckCodeDao() {
		return (MobileCheckCodeDao)BaseStoneContext.getInstance().getBean("mobileCheckCodeDao");
	}
	
	public Appeal getAppeal() {
		return appeal;
	}
	public void setAppeal(Appeal appeal) {
		this.appeal = appeal;
	}
	public List<AppealExtendMsg> getExtList() {
		return extList;
	}
	public void setExtList(List<AppealExtendMsg> extList) {
		this.extList = extList;
	}

	public String getSmsCheckCode() {
		return smsCheckCode;
	}

	public void setSmsCheckCode(String smsCheckCode) {
		this.smsCheckCode = smsCheckCode;
	}

	public String getNewMobile() {
		return newMobile;
	}

	public void setNewMobile(String newMobile) {
		this.newMobile = newMobile;
    }

    public void setDateType(String dateType) {
        this.dateType=dateType;
    }
    public String getDateType() {
        return this.dateType;
    }


    public void setBeginDate(String beginDate) {
        this.beginDate=beginDate;
    }
    public String getBeginDate() {
        return this.beginDate;
    }


    public void setEndDate(String endDate) {
        this.endDate=endDate;
    }
    public String getEndDate() {
        return this.endDate;
    }

	public boolean isShowGt() {
		return showGt;
	}

	public void setShowGt(boolean showGt) {
		this.showGt = showGt;
	}
}
