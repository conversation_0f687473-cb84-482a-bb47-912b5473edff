package com.shunwang.basepassport.user.service;

import com.shunwang.basepassport.manager.request.geetest.CheckMobileH5Request;
import com.shunwang.basepassport.manager.request.geetest.CheckMobileRequest;
import com.shunwang.basepassport.manager.response.geetest.CheckMobileH5Response;
import com.shunwang.basepassport.manager.response.geetest.CheckMobileResponse;
import com.shunwang.basepassport.manager.service.geetest.CheckMobileH5ServiceClient;
import com.shunwang.basepassport.manager.service.geetest.CheckMobileServiceClient;
import com.shunwang.toolbox.util.lang.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class GeeTestService {
    private static final Logger logger = LoggerFactory.getLogger(GeeTestService.class);
    /**
     *
     * @param token
     * @param processId
     * @param authcode
     * @return
     */
    public static CheckMobileResponse checkMobile(String token, String processId, String authcode, String sceneCode) {
        CheckMobileRequest request = new CheckMobileRequest();
        request.setAuthcode(authcode);
        request.setToken(token);
        request.setProcessId(processId);

        return CheckMobileServiceClient.execute(request, sceneCode);
    }

    public static CheckMobileH5Response checkMobileH5(String token, String processId, String authCode, String siteId, String phone) {
        CheckMobileH5Request request = new CheckMobileH5Request();
        request.setAuthCode(authCode);
        request.setToken(token);
        request.setProcessId(processId);
        if  (StringUtil.isNotBlank(phone)  ) {
            request.setPhone(phone);
        }

        return CheckMobileH5ServiceClient.execute(request, siteId);
    }
}
