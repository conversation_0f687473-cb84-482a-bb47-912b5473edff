package com.shunwang.basepassport.binder.web.send.bean;

/**
 * 保存 MobileBindBaseAction 中的参数
 * 传递给 processor
 *
 * <AUTHOR>
 * @date 2018/12/17
 **/
public class MobileBindBean extends BindBean {

    private String userName;
    private String mobile;
    private String mobileActiveNo;
    private String bindType;
    private String loginIp;
    private String environment;
    private String remark;
    private String version;
    private String accessSiteId;
    private String password;
    private String encryptType;
    private String encryptMode;
    private String singleAccountToken;
    private String reportData;
    private String terminal;
    /**
     * 标识用户密码是否是弱密码 1弱密码 2非弱密码
     */
    private String weakPwdState;


    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getMobileActiveNo() {
        return mobileActiveNo;
    }

    public void setMobileActiveNo(String mobileActiveNo) {
        this.mobileActiveNo = mobileActiveNo;
    }

    public String getBindType() {
        return bindType;
    }

    public void setBindType(String bindType) {
        this.bindType = bindType;
    }

    public String getLoginIp() {
        return loginIp;
    }

    public void setLoginIp(String loginIp) {
        this.loginIp = loginIp;
    }

    public String getEnvironment() {
        return environment;
    }

    public void setEnvironment(String environment) {
        this.environment = environment;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getAccessSiteId() {
        return accessSiteId;
    }

    public void setAccessSiteId(String accessSiteId) {
        this.accessSiteId = accessSiteId;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getEncryptType() {
        return encryptType;
    }

    public void setEncryptType(String encryptType) {
        this.encryptType = encryptType;
    }

    public String getEncryptMode() {
        return encryptMode;
    }

    public void setEncryptMode(String encryptMode) {
        this.encryptMode = encryptMode;
    }

    public String getSingleAccountToken() {
        return singleAccountToken;
    }

    public void setSingleAccountToken(String singleAccountToken) {
        this.singleAccountToken = singleAccountToken;
    }

    public String getReportData() {
        return reportData;
    }

    public void setReportData(String reportData) {
        this.reportData = reportData;
    }

    public String getTerminal() {
        return terminal;
    }

    public void setTerminal(String terminal) {
        this.terminal = terminal;
    }
}
