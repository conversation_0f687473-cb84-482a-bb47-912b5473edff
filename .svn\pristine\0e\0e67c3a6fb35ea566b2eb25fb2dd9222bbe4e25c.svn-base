<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
		  http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx.xsd">

    <!-- 数据源  -->
    <bean id="baseStone.dataSource" class="com.alibaba.druid.pool.DruidDataSource" init-method="init"
          destroy-method="close">
        <property name="name" value="mysql-base-config"/>
        <property name="driverClassName" value="${baseConfig.mysql.jdbc.driverClassName}"/>
        <property name="url" value="${baseConfig.mysql.jdbc.url}"/>
        <property name="username" value="${baseConfig.mysql.jdbc.username}"/>
        <property name="password" value="${baseConfig.mysql.jdbc.password}"/>
        <property name="connectionProperties" value="config.decrypt=true" />
        <property name="filters" value="stat,config" />
        <property name="maxActive" value="${baseConfig.mysql.jdbc.maxActive}"/>
        <property name="minIdle" value="${baseConfig.mysql.jdbc.minIdle}"/>
        <property name="initialSize" value="${baseConfig.mysql.jdbc.initialSize}"/>
        <property name="validationQuery" value="SELECT 1" />
        <property name="testOnBorrow" value="false" />
        <property name="testOnReturn" value="false" />
        <property name="testWhileIdle" value="true" />
        <property name="timeBetweenEvictionRunsMillis" value="60000" />
        <property name="minEvictableIdleTimeMillis" value="300000" />
        <property name="defaultAutoCommit" value="true"/>
        <property name="removeAbandoned" value="true"/>
        <property name="removeAbandonedTimeout" value="60"/>
        <property name="logAbandoned" value="true"/>
    </bean>
    
    <bean id="baseStone.sqlMapClient" class="org.springframework.orm.ibatis.SqlMapClientFactoryBean">
        <property name="configLocation" value="classpath:/baseStone/ibatis/baseStoneIbatis.xml"/>
        <property name="dataSource" ref="baseStone.dataSource"/>
    </bean>

    <bean id="baseStone.sqlMapClientTemplate" class="org.springframework.orm.ibatis.SqlMapClientTemplate">
        <property name="sqlMapClient">
            <ref bean="baseStone.sqlMapClient"/>
        </property>
    </bean>
    <bean id="baseStone.transactionManager" class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource" ref="baseStone.dataSource"/>
    </bean>

</beans>
