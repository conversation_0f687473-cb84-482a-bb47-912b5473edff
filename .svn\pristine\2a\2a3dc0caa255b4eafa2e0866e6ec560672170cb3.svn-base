package com.shunwang.basepassport.user.exception;

import com.shunwang.baseStone.core.exception.BaseStoneException;

/**
 * 令牌异常
 *
 * <AUTHOR>
 * @since 2013-11-22
 */
public class TokenException extends BaseStoneException {

    private static final long serialVersionUID = -2923424145752644397L;

    public static final String CREATE_ERR = "1040"; // 创建异常

    public static final String INVALID_ERR = "1041"; // 令牌无效

    public static final String VALIDATE_ERR = "1042"; // 令牌验证错误
    
    /**
     * 刷新TOKEN过期
     */
    public static final String REFRESH_TOKEN_TIME_OUT = "1043";
    
    /**
     * 刷新令牌无效
     */
    public static final String REFRESH_TOKEN_INVALID = "1044";
    
    /**
     * 未生成令牌
     */
    public static final String TOKEN_NOT_EXIST = "1045";
    
    /**访问令牌无效
     * 
     */
    public static final String ACCESS_TOKEN_INVALID = "1046";
    
    /**
     * 访问TOKEN过期
     */
    public static final String ACCESS_TOKEN_TIME_OUT = "1047";

    /**
     * 令牌删除失败
     */
    public static final String TOKEN_DELETE_FAILED = "1048";

    /**
     * 授权CODE不存在
     */
    public static final String AUTHORIZED_CODE_NOT_EXISTED = "1058" ;

    /**
     * 授权CODE无效
     */
    public static final String AUTHORIZED_CODE_INVALID = "1059" ;

	/**
	 * 授权Token不存在
	 */
	public static final String AUTHORIZED_TOKEN_NOT_EXISTED = "1063" ;

	/**
	 * 授权Token无效
	 */
	public static final String AUTHORIZED_TOKEN_INVALID = "1064" ;

	/**
	 * 被授权站点SiteId
	 */
	public static final String AUTHORIZED_SITE_ID_NOT_EXISTED = "1065" ;

    
    public TokenException(String msgId, String msg) {
        super(msgId, msg);
    }
}
