package com.shunwang.baseStone.sso.freeloginweb;

import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.apache.struts2.ServletActionContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.shunwang.baseStone.context.IPContext;
import com.shunwang.baseStone.siteinterface.context.SiteContext;
import com.shunwang.baseStone.sso.context.FreeLoginTicketContext;
import com.shunwang.baseStone.sso.context.UserLoginContext;
import com.shunwang.baseStone.sso.core.constants.SSOConstants;
import com.shunwang.baseStone.sso.exception.ValidateExp;
import com.shunwang.baseStone.sso.pojo.FreeLoginTicket;
import com.shunwang.basepassport.user.pojo.Member;
import com.shunwang.util.lang.StringUtil;
import com.shunwang.util.net.IpUtil;

public class CheckFreeTicketAction extends FreeLoginBaseAction {

	private static final long serialVersionUID = -4250644790743234354L;
	private static final Logger logger = LoggerFactory.getLogger(CheckFreeTicketAction.class);
	
	private String freeTicket;
	private Integer memberId;
	private String memberName;
	private Member member;
	private String version;
	private String env;
	private String extData;

	@Override
	protected Map<String, Object> process() throws Exception {
		
		FreeLoginTicket freeLoginTicket = FreeLoginTicketContext.getTicketById(freeTicket);
		if(freeLoginTicket == null) {
			logger.error("freeLoginTicket验证失败, loginTicket={}, memberId={}", freeTicket, memberId);
	        throw new ValidateExp("freeLoginTicket验证失败,loginTicket=" + freeTicket);
	    }
		
		if(!freeLoginTicket.getMemberId().equals(memberId)){
			logger.error("freeLoginTicket验证失败, loginTicket={}, memberId={}", freeTicket, freeLoginTicket.getMemberId());
	        throw new ValidateExp("freeLoginTicket验证失败,loginTicket=" + freeTicket);
		}
		setMemberName(freeLoginTicket.getMemberName());
		
		Map<String, Object> result = createResultJSON();
		IPContext.setIp(IpUtil.getIpAddress(ServletActionContext.getRequest()));
        SiteContext.setSiteId(siteId);
        String loginMsg = StringUtil.isBlank(extData)?siteId:siteId+" || "+extData;
        member = UserLoginContext.loginWithNoPwd(memberName,version,env,loginMsg,true);
		
		result.put("memberName", freeLoginTicket.getMemberName());
		result.put("memberId", freeLoginTicket.getMemberId());
	    return result;
	}

	@Override
	protected void checkParam() throws Exception {
		if (null == memberId)
            throw new ValidateExp("参数[memberId]不能为空");
    	if (StringUtils.isBlank(freeTicket))
            throw new ValidateExp("参数[freeTicket]不能为空");
	}

	@Override
	protected String getSiteName() {
		return SSOConstants.FreeLogin.checkFreeTicket;
	}

	public String getFreeTicket() {
		return freeTicket;
	}

	public void setFreeTicket(String freeTicket) {
		this.freeTicket = freeTicket;
	}

	public Integer getMemberId() {
		return memberId;
	}

	public void setMemberId(Integer memberId) {
		this.memberId = memberId;
	}

	public String getSiteId() {
		return siteId;
	}

	public void setSiteId(String siteId) {
		this.siteId = siteId;
	}

	public Member getMember() {
		return member;
	}

	public void setMember(Member member) {
		this.member = member;
	}

	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}

	public String getEnv() {
		return env;
	}

	public void setEnv(String env) {
		this.env = env;
	}

	public String getExtData() {
		return extData;
	}

	public void setExtData(String extData) {
		this.extData = extData;
	}

	public String getMemberName() {
		return memberName;
	}

	public void setMemberName(String memberName) {
		this.memberName = memberName;
	}

}
