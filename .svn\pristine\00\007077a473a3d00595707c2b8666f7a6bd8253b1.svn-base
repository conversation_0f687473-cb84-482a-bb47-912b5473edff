package com.shunwang.baseStone.sso.util;

import com.shunwang.basepassport.asynctask.LogonLogTaskExecutor;
import com.shunwang.basepassport.asynctask.UserStatisLogJob;
import com.shunwang.basepassport.user.pojo.UserStatisLog;
import com.shunwang.util.net.IpUtil;
import org.apache.struts2.ServletActionContext;

import java.util.Date;

/**
 * 用户统计日志操作
 *
 * <AUTHOR>
 * @since 2013-08-30
 */
@Deprecated
public class UserLogUtil {

    public static UserStatisLog createLoginLog(String siteId, String userName, String version, String env, String extData) {
        UserStatisLog log = createLog(siteId, userName, version, env, extData);
        log.setOperationType(UserStatisLog.Op.LOGIN.getType());

        return log;
    }

    public static UserStatisLog createPasswordLog(String siteId, String userName, String version, String env, String extData) {
        UserStatisLog log = createLog(siteId, userName, version, env, extData);
        log.setOperationType(UserStatisLog.Op.FORGET_PASSWORD.getType());

        return log;
    }

    public static void sendLog(UserStatisLog log, String message) {
        if (log != null) {
            if (message != null && message.contains("用户名不存在")) {
                log.setMemberName(null);
            }
            log.setErrorMessage(message);
            LogonLogTaskExecutor.submit(new UserStatisLogJob(log));
        }
    }

    private static UserStatisLog createLog(String siteId, String userName, String version, String env, String extData) {
        UserStatisLog log = new UserStatisLog();
        log.setClientIp(IpUtil.getIpAddress(ServletActionContext.getRequest()));
        log.setClientFrom(siteId);
        log.setMemberName(userName);
        log.setClientVersion(version);
        log.setClientEnvironment(env);
        log.setRemark(extData);
        log.setCreatedAt(new Date());

        return log;
    }
}
