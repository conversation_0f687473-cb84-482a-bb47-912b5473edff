<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd" >
<sqlMap namespace="com.shunwang.basepassport.weixin.pojo.WeixinOauthToken" >
	<typeAlias alias="weixinOauthToken" type="com.shunwang.basepassport.weixin.pojo.WeixinOauthToken"/>

	<resultMap id="baseMapper" class="weixinOauthToken">
		<result property="appId" column="app_id" jdbcType="VARCHAR" />
		<result property="accessToken" column="access_token" jdbcType="VARCHAR" />
		<result property="refreshToken" column="refresh_token" jdbcType="VARCHAR" />
		<result property="expireTime" column="expire_time" jdbcType="TIMESTAMP" />
		<result property="type" column="type" jdbcType="INTEGER" />
		<result property="state" column="state" jdbcType="INTEGER" />
		<result property="remark" column="remark" jdbcType="VARCHAR" />
		<result property="timeAdd" column="time_add" jdbcType="TIMESTAMP" />
		<result property="timeEdit" column="time_edit" jdbcType="TIMESTAMP" />
	</resultMap>

	<sql id="baseColumn">
		app_id,
		access_token,
		refresh_token,
		expire_time,
		`type`,
		state,
		remark,
		time_add,
		time_edit
	</sql>

	<insert id="insert" parameterClass="weixinOauthToken">
		INSERT INTO personal_weixin_oauth_token (
			app_id,
			access_token,
			refresh_token,
			expire_time,
			`type`,
			state,
			remark,
			time_add,
			time_edit
		)VALUES(
			#appId:INTEGER#,
			#accessToken:VARCHAR#,
			#refreshToken:VARCHAR#,
			#expireTime:TIMESTAMP#,
			#type:INTEGER#,
			#state:INTEGER#,
			#remark:VARCHAR#,
			#timeAdd:TIMESTAMP#,
			#timeEdit:TIMESTAMP#
		)
	</insert>

	<select id="find" parameterClass="weixinOauthToken" resultMap="baseMapper">
		SELECT
		<include refid="baseColumn" ></include>
		FROM personal_weixin_oauth_token
		<dynamic prepend="WHERE">
			<isNotEmpty prepend=" AND " property="appId">
				app_id = #appId:VARCHAR#
			</isNotEmpty>
			<isNotEmpty prepend=" AND " property="state">
				state = #state:VARCHAR#
			</isNotEmpty>
			<isNotEmpty prepend=" AND " property="type">
				type = #type:VARCHAR#
			</isNotEmpty>
		</dynamic>
	</select>


	<update id="updateToken" parameterClass="weixinOauthToken">
		UPDATE `personal_weixin_oauth_token`
		<dynamic prepend="SET">
			<isNotEmpty prepend="," property="accessToken">
				access_token = #accessToken:VARCHAR#
			</isNotEmpty>
			<isNotEmpty prepend="," property="refreshToken">
				refresh_token = #refreshToken:VARCHAR#
			</isNotEmpty>
			<isNotEmpty prepend="," property="expireTime">
				expire_time = #expireTime:TIMESTAMP#
			</isNotEmpty>
			<isNotEmpty prepend="," property="state">
				state = #state:INTEGER#
			</isNotEmpty>
			<isNotEmpty prepend="," property="timeAdd">
				time_add = #timeAdd:TIMESTAMP#
			</isNotEmpty>
			<isNotEmpty prepend="," property="timeEdit">
				time_edit = #timeEdit:TIMESTAMP#
			</isNotEmpty>

		</dynamic>
		<dynamic prepend="WHERE">
			<isNotEmpty prepend=" AND " property="appId">
				app_id = #appId:VARCHAR#
			</isNotEmpty>
			<isNotEmpty prepend=" AND " property="type">
				`type` = #type:INTEGER#
			</isNotEmpty>
		</dynamic>
	</update>

	<select id="getById" parameterClass="java.lang.String" resultMap="baseMapper">
		SELECT
		<include refid="baseColumn" ></include>
		FROM personal_weixin_oauth
		WHERE app_id = #appId:VARCHAR#
	</select>

	<select id="getByIdWithLock" parameterClass="java.lang.String" resultMap="baseMapper">
		SELECT
		<include refid="baseColumn"></include>
		FROM personal_weixin_oauth_token
		WHERE app_id = #appId:VARCHAR#
		FOR UPDATE
	</select>
</sqlMap>