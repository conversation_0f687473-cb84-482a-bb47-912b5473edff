package com.shunwang.basepassport.filesystem.dto;

import com.shunwang.util.encrypt.AesEncrypt;

import java.util.Arrays;

public class Extract {

    private String salt;

    private String directory;

    public Extract(String salt) {
        this.salt = salt;
    }

    public String decodeEncryptedPath(String encryptedPath) {
        try {
            return AesEncrypt.Decrypt(encryptedPath, salt);
        } catch (Exception ignored) {
        }

        return "";
    }


    public String encodeEncryptPath(String realPath) {
        try {
            return AesEncrypt.Encrypt(realPath, salt);
        } catch (Exception ignored) {
        }

        return "";
    }

    public static void main(String[] args) throws Exception {
        Extract extract = new Extract("a63e0bf1901e81a6");
        String path = extract.encodeEncryptPath("35m/736K/1570846357940434.jpg");
        System.out.println(path);
        System.out.println(path.length());

        String decodeEncryptedPath = extract.decodeEncryptedPath(path);
        System.out.println(decodeEncryptedPath);

        System.out.println(extract.decodeEncryptedPath("RG5keWJMazZlMU05WEZld255bXRuUk1sSEVFZmM5dU05UlRXZlpGV212TT0="));
        System.out.println(extract.encodeEncryptPath("front5156076073533.jpg"));

        String encrypt = AesEncrypt.Encrypt("front5156076073533.jpg", "a63e0bf1901e81a6");
        System.out.println(encrypt);

        String sss = "/images/actuality/company/additionImgs/kR9pekrFT9N/TgHU87gnecYHojRZpqdqW98NJOVWkDs=";

        String[] split = sss.split("/");
        System.out.println(Arrays.toString(split));
        split = sss.split("/", 5);
        System.out.println(Arrays.toString(split));

    }

}