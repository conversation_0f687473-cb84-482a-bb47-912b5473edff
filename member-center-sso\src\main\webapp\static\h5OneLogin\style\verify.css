.login-verify-phone { text-align: center; }
.login-verify-phone .verify-phone-content { padding: 30px 28px; }
.login-verify-phone .verify-phone-content .logo { margin: 0 auto; width: 74px; height: 74px; margin-bottom: 22px; background: url("https://callback.kedou.com/yxq/static/1.0.0/logo.png"); background-size: 100% 100%; background-repeat: no-repeat; }
.login-verify-phone .verify-phone-content .title { font-weight: bold; font-size: 16px; color: #333333; line-height: 20px; margin: 0 0 9px; }
.login-verify-phone .verify-phone-content .desc { font-weight: 500; font-size: 12px; color: #999999; line-height: 20px; }
.login-verify-phone .verify-phone-content .tips { text-align: left; }
.login-verify-phone .verify-phone-content .form-group { margin-top: 50px; height: 50px; display: flex; align-items: center; border-bottom: 1px solid #999; }
.login-verify-phone .verify-phone-content .form-group .icon { width: 24px; height: 24px; }
.login-verify-phone .verify-phone-content .form-group .form-control { padding-left: 20px; font-weight: 500; font-size: 14px; color: #333; line-height: 20px; background-color: transparent !important; }
.login-verify-phone .verify-phone-content .form-group .form-control::placeholder { color: #999999; }
.login-verify-phone .verify-phone-content .form-group .form-control:focus-visible { outline: none; }

.login-verify-code .verify-code-content { padding: 36px 12px; }
.login-verify-code .verify-code-content .title { margin: 0 0 24px; font-weight: bold; font-size: 20px; color: #333333; line-height: 20px; }
.login-verify-code .verify-code-content .desc, .login-verify-code .verify-code-content .tips { margin-bottom: 40px; font-weight: 500; font-size: 14px; color: #999999; line-height: 20px; }
.login-verify-code .verify-code-content .form-group { margin: 20px 0; display: flex; gap: 13px; align-items: center; justify-content: center; }
.login-verify-code .verify-code-content .form-group .code-input { width: 47px; height: 50px; font-weight: 500; font-size: 24px; color: #333333; line-height: 50px; text-align: center; border-radius: 8px; background: #FFFFFF; border: 1px solid #979797; }
.login-verify-code .verify-code-content .form-group .code-input:focus { outline: none; border-color: #4a90e2; box-shadow: 0 0 5px rgba(74, 144, 226, 0.5); }
.login-verify-code .verify-code-content .form-group span { width: 28px; height: 1px; margin: 0 4px; border: 1px solid #333333; }

.login-phone-step .verify-box { position: absolute; top: 0; left: 0; right: 0; background: #FFF; z-index: 9; height: 100%; background: url("https://callback.kedou.com/yxq/static/1.0.0/images/verify-phone-bg.png"); background-size: 100% 100%; background-repeat: no-repeat; }
.login-phone-step .verify-header { position: relative; height: 44px; padding: 0 15px; display: flex; justify-content: space-between; border-bottom: 1px solid rgba(0, 0, 0, 0.1); }
.login-phone-step .verify-header .back { position: absolute; left: 15px; top: 50%; border: none; background: transparent; transform: translateY(-50%); }
.login-phone-step .verify-header .back::after { content: ""; display: inline-block; border-width: 1px 0 0 1px; border-style: solid; width: 10px; height: 10px; border-color: #000; -webkit-transform: rotate(-45deg); transform: rotate(-45deg); }
.login-phone-step .verify-header .title { width: 100%; margin: 0; font-weight: 500; font-size: 17px; color: #333333; line-height: 44px; text-align: center; }
.login-phone-step .verify-bottom { padding: 0 26px; }
.login-phone-step .verify-bottom button { width: 100%; height: 50px; border-radius: 8px; background: #4768F2; border: 1px solid #4768F2; font-weight: bold; font-size: 16px; color: #FFFFFF; line-height: 50px; }
.login-phone-step .verify-error .form-group .code-input { border-color: #F30B0D; }
.login-phone-step .verify-error .form-group .code-input:focus { outline: none; border-color: #F30B0D; box-shadow: 0 0 5px rgba(243, 11, 13, 0.5); }
.login-phone-step .verify-error .tips { color: #F30B0D; }
.login-phone-step .verify-disabled { pointer-events: none; opacity: 0.5; }
.login-phone-step .protocol-tip { margin-top: 28px; }
.login-phone-step .protocol-tip input { margin-right: 6px; margin-bottom: 2px; }
.login-phone-step .protocol-tip input[type="checkbox"]:checked { background: #28C445; outline: none; }
.login-phone-step .protocol-tip p { display: flex; align-items: center; justify-content: center; font-weight: 500; font-size: 12px; color: #999999; line-height: 20px; }
.login-phone-step .protocol-tip a { padding: 0 4px; color: #3881FF; }
.login-phone-step .login-others { margin-top: 75px; border: none; }
.login-phone-step .login-others .hd { width: auto; margin-bottom: 25px; font-weight: 500; font-size: 12px; color: #999999; line-height: 20px; background: #FFF; }
.login-phone-step .login-others .hd p { display: inline-block; padding: 0 8px; background: #FFF; }
.login-phone-step .login-others .hd::after { position: absolute; top: 50%; left: 50%; content: ""; display: block; width: 168px; z-index: -1; height: .5px; background: #979797; transform: translate(-50%, -50%); }
.login-phone-step .login-others .bd { display: flex; flex-flow: wrap; padding: 0 40px; justify-content: space-between; }
.login-phone-step .login-others .drop { width: 50%; margin: 9px 0; display: flex; align-items: center; justify-content: center; text-decoration: none; }
.login-phone-step .login-others .drop img { width: 28px; height: 28px; border-radius: 50%; margin-right: 12px; }
.login-phone-step .login-others .drop span { font-weight: 500; font-size: 14px; color: #333333; line-height: 20px; }

.privacy-modal { background: rgba(0, 0, 0, 0.4); }
.privacy-modal .modal-content { position: absolute; top: 50%; left: 50%; width: 285px; margin: 0; padding: 24px 16px; border-radius: 8px; background: #FFFFFF; transform: translate(-50%, -50%); }
.privacy-modal .modal-content .modal-header { display: flex; align-items: center; justify-content: space-between; }
.privacy-modal .modal-content .modal-header .title { margin: 0; font-weight: bold; font-size: 16px; color: #333333; line-height: 20px; }
.privacy-modal .modal-content .modal-header .close { width: 12px; height: 12px; border: none; margin-bottom: 6px; background: url("https://callback.kedou.com/yxq/static/1.0.0/images/icon-close.png"); background-size: 100% 100%; background-repeat: no-repeat; }
.privacy-modal .modal-content .modal-body { overflow: hidden; background-color: #FFF; }
.privacy-modal .modal-content .modal-body .message { margin: 22px 0; font-weight: 500; font-size: 14px; color: #333333; line-height: 22px; }
.privacy-modal .modal-content .modal-body .btn-box { padding: 0; margin-bottom: 6px; }
.privacy-modal .modal-content .modal-body .btn-box button { width: 100%; height: 44px; padding: 0; font-weight: 500; font-size: 16px; color: #FFFFFF; line-height: 44px; border-radius: 8px; background: #4768F2; }

.toast-error { position: fixed; left: 0; right: 0; top: 50%; z-index: 99; text-align: center; transform: translateY(-50%); }
.toast-error .toast-content { display: inline-block; color: #FFF; font-size: 14px; padding: 12px 16px; border-radius: 8px; background: rgba(0, 0, 0, 0.8); }

/*# sourceMappingURL=verify.css.map */
