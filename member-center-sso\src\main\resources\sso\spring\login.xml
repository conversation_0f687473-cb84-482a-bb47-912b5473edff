<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	   xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

	<bean id="loginAction" class="com.shunwang.baseStone.sso.web.LoginAction" scope="prototype">
		<property name="effectiveTime" value="${effectiveTime}"></property>
		<property name="dynamicMd5Key" value="${dynamicMd5Key}"></property>
		<property name="dynamicAesKey" value="${dynamicAesKey}"></property>
		<property name="qrCodeExpireSeconds" value="${qrCode.expireSeconds}" />
		<property name="bussinessDao" ref="bussinessDao"/>
		<property name="whitelistUnsafe" value="${whitelist.unsafe.siteid}"></property>
		<property name="getTicketWithoutLoginUrl" value="${interface.getTicketWithoutLogin.url}"></property>
		<property name="getTicketWithoutLoginMd5Key" value="${interface.getTicketWithoutLogin.md5key}"></property>
		<property name="siteId" value="${siteId}"></property>
		<property name="memberAccountBindDao" ref="memberAccountBindDao" />
		<property name="interfaceService" ref="interfaceService" />
		<property name="loginElementService" ref="loginElementService"/>
		<property name="serviceNotifyDao" ref="serviceNotifyDao"/>
		<property name="weixinMsgService" ref="weixinMsgService"/>
		<property name="weixinOpenOauth" ref="weixinOpenOauth"/>
		<property name="multipleAccountBindService" ref="multipleAccountBindService"/>
	</bean>

	<bean id="qrcodeLoginAction" class="com.shunwang.baseStone.sso.web.QrcodeLoginAction" scope="prototype">
		<property name="bussinessDao" ref="bussinessDao"/>
		<property name="memberAccountBindDao" ref="memberAccountBindDao" />
		<property name="interfaceService" ref="interfaceService" />
		<property name="redisOperation" ref="redisOperation"/>
		<property name="weixinAuthAdapter" ref="weixinOauth"/>
		<property name="wxWorkUserBindService" ref="wxWorkUserBindService"/>
		<property name="expireSeconds" value="${qrCodeLogin.expireSeconds}"/>
	</bean>

	<bean id="logoutAction" class="com.shunwang.baseStone.sso.web.LogoutAction" scope="prototype" >
		<property name="loginElementService" ref="loginElementService"/>
	</bean>
	<bean id="quickLoginAction" class="com.shunwang.baseStone.sso.web.QuickLoginAction" scope="prototype" >
	</bean>

	<bean id="freeLoginAction" class="com.shunwang.baseStone.sso.web.FreeLoginAction" scope="prototype" >
		<property name="bussinessDao" ref="bussinessDao"/>
		<property name="interfaceService" ref="interfaceService"/>
		<property name="loginElementService" ref="loginElementService"/>
		<property name="memberAccountBindDao" ref="memberAccountBindDao"/>
		<property name="memberDao" ref="memberDao"/>
		<property name="memberOutSiteDao" ref="memberOutSiteDao"/>
		<property name="weixinOpenIdUnionIdService" ref="weixinOpenIdUnionIdService"/>
		<property name="barFreeLoginAuthService" ref="barFreeLoginAuthService"/>
		<property name="memberMultipleAccountBindDao" ref="memberMultipleAccountBindDao"/>
	</bean>

	<bean id="outOauthAction" class="com.shunwang.baseStone.sso.web.OutOauthAction" scope="prototype" >
	</bean>

	<bean id="userOutsiteApapter" class="com.shunwang.baseStone.sso.apapter.UserOutsiteApapter" abstract="true">
		<property name="memberDao" ref="memberDao" />
		<property name="memberAccountBindDao" ref="memberAccountBindDao" />
		<property name="interfaceService" ref="interfaceService"/>
		<property name="bussinessDao" ref="bussinessDao"/>
		<property name="loginElementService" ref="loginElementService"/>
		<property name="redisOperation" ref="redisOperation"/>
		<property name="weixinOpenIdUnionIdService" ref="weixinOpenIdUnionIdService"/>
	</bean>
	<bean id="weixinBaseOauth" class="com.shunwang.baseStone.sso.apapter.WeixinAdapter" parent="userOutsiteApapter" abstract="true">
		<property name="weixinOauthService" ref="weixinOauthService"/>
		<property name="weixinOauthTokenService" ref="weixinOauthTokenService"/>
		<property name="weixinOpenService" ref="weixinOpenService"/>
		<property name="barFreeLoginAuthService" ref="barFreeLoginAuthService"/>
	</bean>
	<bean id="weixinOauth" class="com.shunwang.baseStone.sso.apapter.WeixinAuthAdapter" parent="weixinBaseOauth" scope="prototype">
		<property name="weixinRemoteCall" ref="weixinRemoteCall" />
		<property name="wxIdCardBindDao" ref="wxIdCardBindDao"/>
		<property name="netBarService" ref="netBarService"/>
		<property name="wxWorkUserBindService" ref="wxWorkUserBindService"/>
		<property name="weixinWebCallbackUrl" value="${weixinWebCallbackUrl}"/>
	</bean>

	<bean id="miniOauth" class="com.shunwang.baseStone.sso.apapter.WeixinMiniAdapter" parent="weixinBaseOauth" scope="prototype">
		<property name="weixinRemoteCall" ref="weixinRemoteCall" />
		<property name="ssoSiteId" value="${ssoSiteId}" />
		<property name="qrCodeExpireSeconds" value="${qrCode.expireSeconds}" />
		<property name="memberMultipleAccountBindDao" ref="memberMultipleAccountBindDao"/>
		<property name="multipleAccountBindService" ref="multipleAccountBindService"/>
	</bean>

	<!-- 微信开放平台Service -->
	<bean id="weixinOpenService" class="com.shunwang.baseStone.sso.weixin.oauth.service.WeixinOpenService">
		<property name="configInterfaceDao" ref="configInterfaceDao" />
	</bean>

	<bean id="weixinOpenOauth" class="com.shunwang.baseStone.sso.apapter.WeixinOpenAuthAdapter" parent="weixinBaseOauth" scope="prototype">
		<property name="ssoSiteId" value="${ssoSiteId}" />
		<property name="netBarService" ref="netBarService"/>
		<property name="wxIdCardBindDao" ref="wxIdCardBindDao"/>
		<property name="areaAppidDao" ref="areaAppidDao"/>
		<property name="appidReplyDao" ref="appidReplyDao"/>
		<property name="weixinMsgService" ref="weixinMsgService"/>
	</bean>


	<bean id="weixinRemoteCall" class="com.shunwang.baseStone.sso.weixin.oauth.service.WeixinRemoteCall" >
		<property name="appId" value="${appid}"/>
	</bean>
	<bean id="chinaNetOauth" class="com.shunwang.baseStone.sso.apapter.ChinaNetApapter" parent="userOutsiteApapter" scope="prototype" >
	</bean>

	<bean id="hiNetOauth" class="com.shunwang.baseStone.sso.apapter.HiNetApapter" parent="userOutsiteApapter" scope="prototype" >
	</bean>

	<bean id="uc4008Oauth" class="com.shunwang.baseStone.sso.apapter.Uc4008Apapter" parent="userOutsiteApapter" scope="prototype" >
	</bean>

	<bean id="qqOauth" class="com.shunwang.baseStone.sso.apapter.QqAdapter" parent="userOutsiteApapter" scope="prototype" >
		<property name="getTicketWithoutLoginUrl" value="${interface.getTicketWithoutLogin.url}"></property>
		<property name="getTicketWithoutLoginMd5Key" value="${interface.getTicketWithoutLogin.md5key}"></property>
		<property name="siteId" value="${siteId}"></property>
	</bean>

	<bean id="googleOauth" class="com.shunwang.baseStone.sso.apapter.GoogleAdapter" parent="userOutsiteApapter" scope="prototype" >
		<property name="googleOauthService" ref="googleOauthService"/>
	</bean>

	<bean id="googleOauthService" class="com.shunwang.baseStone.sso.google.oauth.GoogleOauthService">
		<property name="configInterfaceDao" ref="configInterfaceDao"/>
	</bean>

	<!--APP扫码-->
	<bean id="appOauth" class="com.shunwang.baseStone.sso.apapter.AppAdapter" parent="userOutsiteApapter" scope="prototype" >
	</bean>

	<bean id="alipayOauth" class="com.shunwang.baseStone.sso.apapter.AlipayAuthAdapter" parent="userOutsiteApapter" scope="prototype" >
	</bean>

	<bean id="weiboOauth" class="com.shunwang.baseStone.sso.apapter.WeiboAuthAdapter" parent="userOutsiteApapter" scope="prototype" >
	</bean>

	<bean id="laokeiOauth" class="com.shunwang.baseStone.sso.apapter.LaokeiApapter" parent="userOutsiteApapter" scope="prototype" >
		<property name="getUserInfoUrl" value="${laokei.getUserInfo.url}"/>
	</bean>

	<bean id="sichuanNetOauth" class="com.shunwang.baseStone.sso.apapter.SichuanNetApapter" parent="userOutsiteApapter" scope="prototype" >
		<property name="getUserInfoUrl" value="${sichuanNet.getUserInfo.url}"/>
	</bean>

	<bean id="simpleImportUsersAdapter" class="com.shunwang.baseStone.sso.apapter.SimpleImportUsersAdapter" scope="prototype">
		<property name="interfaceService" ref="interfaceService" />
	</bean>

	<bean id="sicentOauth" class="com.shunwang.baseStone.sso.apapter.SicentAdapter" parent="simpleImportUsersAdapter" scope="prototype"/>

	<bean id="hotelOauth" class="com.shunwang.baseStone.sso.apapter.HotelAdapter" parent="simpleImportUsersAdapter" scope="prototype"/>

	<bean id="idCardAdapter" class="com.shunwang.baseStone.sso.apapter.IdCardAdapter" parent="simpleImportUsersAdapter" scope="prototype">
		<property name="barBlackCardService" ref="barBlackCardService"/>
	</bean>

	<bean id="connectedAccountsAdapter" class="com.shunwang.baseStone.sso.apapter.AbstractConnectedAccountsAdapter"
		  parent="simpleImportUsersAdapter" abstract="true">
		<property name="memberDao" ref="memberDao" />
		<property name="bussinessDao" ref="bussinessDao" />
		<property name="memberAccountBindDao" ref="memberAccountBindDao" ></property>
		<property name="getTicketWithoutLoginUrl" value="${interface.getTicketWithoutLogin.url}"></property>
		<property name="getTicketWithoutLoginMd5Key" value="${interface.getTicketWithoutLogin.md5key}"></property>
		<property name="getTicketSiteId" value="${siteId}"></property>
	</bean>

	<bean id="connectedWeixinUnionIDAdapter" class="com.shunwang.baseStone.sso.apapter.ConnectedWeixinUnionIDAdapter"
		  parent="connectedAccountsAdapter" scope="prototype">
		<property name="weixinOpenIdUnionIdService" ref="weixinOpenIdUnionIdService"/>
	</bean>

	<bean id="connectedQQUnionIdAdapter" class="com.shunwang.baseStone.sso.apapter.ConnectedQQUnionIdAdapter"
		  parent="connectedAccountsAdapter" scope="prototype">
	</bean>


	<bean id="connectedWeiboUIDAdapter" class="com.shunwang.baseStone.sso.apapter.ConnectedWeiboUIDAdapter"
		  parent="connectedAccountsAdapter" scope="prototype">
	</bean>

	<bean id="connectedGoogleUIDAdapter" class="com.shunwang.baseStone.sso.apapter.ConnectedGoogleUIDAdapter"
		  parent="connectedAccountsAdapter" scope="prototype">
	</bean>
	<bean id="connectedAlipayUIDAdapter" class="com.shunwang.baseStone.sso.apapter.ConnectedAlipayUIDAdapter"
		  parent="connectedAccountsAdapter" scope="prototype">
	</bean>
	<bean id="connectedAppleUnionIDAdapter" class="com.shunwang.baseStone.sso.apapter.ConnectedAppleUnionIDAdapter"
		  parent="connectedAccountsAdapter" scope="prototype">
	</bean>
	<bean id="connectedOneClickLoginAdapter" class="com.shunwang.baseStone.sso.apapter.ConnectedOneClickLoginAdapter"
		  parent="connectedAccountsAdapter" scope="prototype">
		<property name="personalOneLoginInfoDao" ref="personalOneLoginInfoDao"/>
	</bean>
	<bean id="connectedH5OneClickLoginAdapter" class="com.shunwang.baseStone.sso.apapter.ConnectedH5OneClickLoginAdapter"
		  parent="connectedAccountsAdapter" scope="prototype">
		<property name="personalOneLoginInfoDao" ref="personalOneLoginInfoDao"/>
	</bean>
	<bean id="connectedYiDunH5OneClickLoginAdapter" class="com.shunwang.baseStone.sso.apapter.ConnectedYiDunH5OneClickLoginAdapter"
		  parent="connectedAccountsAdapter" scope="prototype">
		<property name="personalOneLoginInfoDao" ref="personalOneLoginInfoDao"/>
	</bean>

	<bean class="com.shunwang.baseStone.sso.context.UserUpdateContext">
		<property name="sameNamseDealSiteIdStr" value="${updateSiteId}"/>
		<property name="sameNamseDealTableStr" value="${updateInterimTable}"/>
		<property name="updatePcKeyStr" value="${updatePcKey}"/>
		<property name="updateUrlStr" value="${updateUrl}"/>
	</bean>


	<bean class="com.shunwang.baseStone.sso.context.SsoDomainContext">
		<property name="ssoDomain" value="${ssoDomain}"/>
	</bean>

	<bean id="mobileLoginAction" class="com.shunwang.baseStone.sso.web.MobileLoginAction" scope="prototype">
		<property name="bindAsLoginAccountUrl" value="${interface.bindAsLoginAccount.url}"></property>
		<property name="bindAsLoginAccountMd5Key" value="${interface.bindAsLoginAccount.md5key}"></property>
		<property name="getTicketWithoutLoginUrl" value="${interface.getTicketWithoutLogin.url}"></property>
		<property name="getTicketWithoutLoginMd5Key" value="${interface.getTicketWithoutLogin.md5key}"></property>
		<property name="siteId" value="${siteId}"></property>
		<property name="interfaceService" ref="interfaceService"/>
		<property name="loginElementService" ref="loginElementService"/>
		<property name="configOneLoginDao" ref="configOneLoginDao"/>
	</bean>

	<bean id="obtainClientTokenAction" class="com.shunwang.baseStone.sso.freeloginweb.ObtainClientTokenAction" scope="prototype">
	</bean>

	<bean id="checkClientTokenAction" class="com.shunwang.baseStone.sso.freeloginweb.CheckClientTokenAction" scope="prototype">
	</bean>

	<bean id="obtainClientTicketForInterfaceAction" class="com.shunwang.baseStone.sso.freeloginweb.ObtainClientTicketForInterfaceAction" scope="prototype">
	</bean>

	<bean id="checkFreeTicketAction" class="com.shunwang.baseStone.sso.freeloginweb.CheckFreeTicketAction" scope="prototype">
	</bean>

	<bean id="checkFreeTicketForGuestAction" class="com.shunwang.baseStone.sso.freeloginweb.CheckFreeTicketForGuestAction" scope="prototype">
		<property name="memberAccountBindDao" ref="memberAccountBindDao"/>
		<property name="memberDao" ref="memberDao"/>
		<property name="memberOutSiteDao" ref="memberOutSiteDao"/>
		<property name="weixinOpenIdUnionIdService" ref="weixinOpenIdUnionIdService"/>
		<property name="barFreeLoginAuthService" ref="barFreeLoginAuthService"/>
	</bean>

	<bean id="regTicketCreateAction" class="com.shunwang.baseStone.sso.web.RegTicketCreateAction">
		<property name="bussinessDao" ref="bussinessDao"/>
		<property name="loginElementService" ref="loginElementService"/>
	</bean>

	<!--app登录-->
	<bean id="appPreLoginAction" class="com.shunwang.baseStone.sso.web.AppPreLoginAction">
		<property name="redisOperation" ref="redisOperation"/>
		<property name="bussinessDao" ref="bussinessDao"/>
		<property name="memberAccountBindDao" ref="memberAccountBindDao"/>
		<property name="memberDao" ref="memberDao"/>
	</bean>


	<bean id="appLoginAction" class="com.shunwang.baseStone.sso.web.AppLoginAction">
		<property name="memberDao" ref="memberDao"/>
		<property name="redisOperation" ref="redisOperation"/>
	</bean>

	<bean id="sceneCodeCreateAction" class="com.shunwang.baseStone.sso.web.SceneCodeCreateAction" scope= "prototype">
		<property name="sceneCodeExpireSeconds" value="${sceneCode.expireSeconds}"/>
	</bean>

	<bean id="wxIdCardBindAction" class="com.shunwang.baseStone.sso.web.WxIdCardBindAction" scope= "prototype">
		<property name="wxIdCardBindDao" ref="wxIdCardBindDao"/>
		<property name="configWxBindAdDao" ref="configWxBindAdDao"/>
		<property name="configResourcesDao" ref="configResourcesDao"/>
		<property name="wxTemplateMsgDao" ref="wxTemplateMsgDao"/>
		<property name="netBarService" ref="netBarService"/>
		<property name="redisOperation" ref="redisOperation"/>
		<property name="weixinOauthTokenService" ref="weixinOauthTokenService"/>
		<property name="memberOutSiteDao" ref="memberOutSiteDao"/>
		<property name="multipleAccountBindService" ref="multipleAccountBindService"/>
	</bean>

	<bean id="ticketCheckAction" class="com.shunwang.baseStone.sso.web.TicketCheckAction" scope="prototype">
		<property name="memberDao" ref="memberDao"/>
		<property name="memberAccountBindDao" ref="memberAccountBindDao" ></property>
		<property name="bussinessDao" ref="bussinessDao"/>
		<property name="memberMultipleAccountBindDao" ref="memberMultipleAccountBindDao"/>
	</bean>
</beans>
