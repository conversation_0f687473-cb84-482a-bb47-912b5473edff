package com.shunwang.baseStone.editBackground.web;

import com.shunwang.baseStone.cache.CacheKeyConstant;
import com.shunwang.baseStone.editBackground.pojo.EditBackground;
import org.apache.struts2.ServletActionContext;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletResponse;
import java.io.PrintWriter;
import java.util.Date;

public class EditBackgroundAction extends com.shunwang.framework.struts2.action.ConditionCrudAction<com.shunwang.baseStone.editBackground.pojo.EditBackground,com.shunwang.baseStone.editBackground.dao.EditBackgroundDao> {

	private static final long serialVersionUID = 3500548270802642222L;
	
	private static final Logger LOGGER = LoggerFactory.getLogger(EditBackgroundAction.class);
	
	private Integer id;
	private String  moduleName;
	private String  adName;
	private Date    modifyTime;
	private String  modifyUser;
	private Date	beginTime;
	private Date    endTime;
	private String  linkUrl;
	
	public void updateEditBackground() {
		boolean flag = false;
		String msg = "";
		try {
			EditBackground editBackground = getCrudbo().getById(id);
			editBackground.setBeginTime(beginTime);
			editBackground.setEndTime(endTime);
			editBackground.setLinkUrl(linkUrl);
			this.getCrudbo().update(editBackground);
			String key = CacheKeyConstant.BaseEditBackgroundConsts.ACTIVITY_KEY + id;
			this.getCrudbo().cachedEditBackground(key, editBackground);
			flag = true;
		} catch (Exception e) {
			LOGGER.error("数据更新出错", e);
			flag = false;
            msg = e.getMessage();
		}
		responseJSONString(msg, flag);
	}
	
	private void responseJSONString(String msg, boolean flag) {
    	HttpServletResponse response = ServletActionContext.getResponse();
		response.setHeader("Cache-Control", "no-cache");
        response.setContentType("application/json; charset=utf-8");
        PrintWriter pw = null;
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("msg", msg);
            jsonObject.put("success", flag);
            pw = response.getWriter();
            pw.write(jsonObject.toString());
            pw.flush();
        } catch (Exception e) {
			LOGGER.error("返回结果出错", e);
        } finally {
            if (pw != null) pw.close();
        }
    }
	
	
	
	public Integer getId() {
		return id;
	}
	
	public void setId(Integer id) {
		this.id = id;
	}
	
	public String getModuleName() {
		return moduleName;
	}
	
	public void setModuleName(String moduleName) {
		this.moduleName = moduleName;
	}
	
	public String getAdName() {
		return adName;
	}
	
	public void setAdName(String adName) {
		this.adName = adName;
	}
	
	public Date getModifyTime() {
		return modifyTime;
	}
	
	public void setModifyTime(Date modifyTime) {
		this.modifyTime = modifyTime;
	}
	
	public String getModifyUser() {
		return modifyUser;
	}
	
	public void setModifyUser(String modifyUser) {
		this.modifyUser = modifyUser;
	}
	
	public Date getBeginTime() {
		return beginTime;
	}
	
	public void setBeginTime(Date beginTime) {
		this.beginTime = beginTime;
	}
	
	public Date getEndTime() {
		return endTime;
	}
	
	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}
	
	public String getLinkUrl() {
		return linkUrl;
	}
	
	public void setLinkUrl(String linkUrl) {
		this.linkUrl = linkUrl;
	}
	
	
	
}
