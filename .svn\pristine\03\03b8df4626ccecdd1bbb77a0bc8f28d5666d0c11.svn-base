var $form=$(".profile-form");function checkUserName(){var $username=$("input[name='memberName']");var uv=$.trim($username.val());if(uv.length==0){showTips("请输入通行证账号/手机号/邮箱");return false}return true}function canSubmit(){return checkUserName()&&validateCheckCode()}function validateCheckCode(){var cv=$.trim($("#check-code-text").val());if(cv.length==0){showTips("请输入图片验证码");return false}if(!/[\w]{4}/.test(cv)){showTips("验证码格式不正确");return false}var flag=false;$.ajax({type:"post",url:$CONFIG.appServer+"/front/noLogin/chkCode.htm",data:{"checkCode":cv,r:Math.random()},async:false,success:function(json){if(json.flag){flag=true}else{showTips("验证码错误，请重新输入")}}});if(!flag){return false}return true}function showTips(msg){$(".tips-error p").html(msg);$(".tips-error").show()}function init(){var $submit=$("#next");var msg=$submit.attr("msg-data");msg&&showTips(msg);window.MobileBase.close=function(){try{window.SWMobileSDK.onBack()}catch(e){try{window.webkit.messageHandlers.onBack.postMessage({body:""})}catch(ex){console.error(ex)}}}}$("#next").click(function(e){if(!canSubmit()){e=e||window.event;if(e.preventDefault){e.preventDefault()}else{window.event.returnValue=false}return false}$form.attr("action",$("#actionUrl").val());$form.submit()});$("#change-code").click(function(e){$("#check-code-image").attr("src",$CONFIG.appServer+"/checkCode.htm?r="+Math.random());return false});window.MobileBase.goBack=function(){window.history.go(-1);window.MobileBase.goLogin()};init();