package com.shunwang.basepassport.user.web;

import com.shunwang.baseStone.context.BaseStoneContext;
import com.shunwang.baseStone.core.action.BaseStoneAction;
import com.shunwang.baseStone.encrypt.Encrypt;
import com.shunwang.baseStone.encrypt.pojo.EncryptItem;
import com.shunwang.basepassport.commonExp.ParamNotFoundExp;
import com.shunwang.basepassport.user.common.MemberConstants;
import com.shunwang.basepassport.user.common.UserCheckUtil;
import com.shunwang.basepassport.user.dao.MemberDao;
import com.shunwang.basepassport.user.exception.EmailNotAsLoginAccountExp;
import com.shunwang.basepassport.user.exception.MobileNotAsLoginAccountExp;
import com.shunwang.basepassport.user.exception.MsgNotFoundExp;
import com.shunwang.basepassport.user.pojo.Member;
import com.shunwang.basepassport.user.response.MemberQueryProvideToMobileRespone;
import com.shunwang.util.lang.StringUtil;

public class MemberQueryProvideToMobileAction extends BaseStoneAction {
	
	private static final long serialVersionUID = 3894104054335822706L;
	
	/**用户名**/
	public String userName;
	public String type;
	public MemberDao memberDao;
	
	@Override
	public String buildSignString() {
		Encrypt  encrypt=new Encrypt();
		encrypt.addItem(new EncryptItem(super.getSiteId()));
		encrypt.addItem(new EncryptItem(super.getTime()));
		encrypt.addItem(new EncryptItem(userName));
		return encrypt.buildSign();
	}

	@Override
	public String getSiteName() {
		return MemberConstants.MEMBER_QUERY_PROVIDE_TO_MOBILE;
	}
	
	@Override
	public void process() {
		Member member = findMember();
		if(null != member) {
			member.setMemberInfo(member.loadMemberInfo());
		}
		MemberQueryProvideToMobileRespone response = new MemberQueryProvideToMobileRespone(member);
		this.setBaseResponse(response);
	}
	
	private Member findMember() {
		if(null != type && type.equals(MemberConstants.QUERY_TYPE)) {
			return getMemberById();
		} else {
			return getMember();
		}
	}
	
	private Member getMemberById() {
		Member member = null;
		member = getMemberDao().getById(userName);
		if(null == member) 
			throw new MsgNotFoundExp(userName);
		return member;
	}
	
	/**
	 * ***********
	  * 创建日期: 20112011-8-1上午11:16:30
	  * 创建作者：jinbao
	  * @return 
	  * 功能：查询用户注册信息
	  *************
	 */
	private Member getMember() {
		Member member = null;
		if(UserCheckUtil.checkEmail(userName)) {
			member = getMemberDao().getByEmail(userName);
            if (null == member) {
                Integer bindCnt = getMemberDao().getCntByEmail(userName); //查询该邮箱有没有绑定过通行证
                if (null != bindCnt && bindCnt != 0) { //有绑定过,说明没有一个通行证将该邮箱设为登录账号。
                    throw new EmailNotAsLoginAccountExp();
                }
            }
		} else if(UserCheckUtil.checkMobile(userName)) {
			member = getMemberDao().getByMobile(userName);
            if (null == member) {
                Integer bindCnt = getMemberDao().getCntByMobile(userName); //查询该手机号码有没有绑定过通行证
                if (null != bindCnt && bindCnt != 0) { //有绑定过,说明没有一个通行证将该手机号码设为登录账号。
                    throw new MobileNotAsLoginAccountExp();
                }
            }
		} else {
			member = getMemberDao().getByName(userName);
		}
		if(null == member) 
			throw new MsgNotFoundExp(userName);
		return member;
	}
	
	public MemberDao getMemberDao() {
		if(null == memberDao)
			return (MemberDao)BaseStoneContext.getInstance().getBean("memberDao");
		return memberDao;
	}
	
	@Override
	public void checkParam(){
		if(StringUtil.isBlank(userName))
			throw new ParamNotFoundExp("用户名");
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = doUrlEncode(userName);
	}

	public void setMemberDao(MemberDao memberDao) {
		this.memberDao = memberDao;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}
}
