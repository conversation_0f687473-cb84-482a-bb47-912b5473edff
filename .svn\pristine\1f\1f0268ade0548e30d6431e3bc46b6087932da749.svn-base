package com.shunwang.basepassport.config.enums;

import lombok.Getter;
import lombok.Setter;

public interface OneLoginEnum {
    enum Channel {
        JI_JIAN(1, "极检"),
        YI_DUN(2, "易盾"),
        ;
        Channel(Integer value, String name) {
            this.value = value;
            this.name = name;
        }

        private @Getter
        @Setter
        Integer value;
        private @Getter @Setter String name;
    }


     enum TerminalTypeEnum {
         IOS(1,"android"),
         ANDROID(2,"ios"),
         H5(3,"h5"),
;
        TerminalTypeEnum(Integer value, String name) {
            this.value = value;
            this.name = name;
        }

        private @Getter @Setter Integer value;
        private @Getter @Setter String name;
    }



}
