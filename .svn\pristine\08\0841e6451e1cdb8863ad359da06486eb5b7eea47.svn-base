package com.shunwang.basepassport.manager.request.inter;


import com.shunwang.basepassport.manager.HttpMethod;
import com.shunwang.basepassport.manager.InterfaceConstant;
import com.shunwang.basepassport.manager.response.inter.FileUploadResponse;

import java.util.HashMap;
import java.util.Map;


public class FileUploadRequest extends BaseFileUploadRequest<FileUploadResponse> {
    private String directory;

    @Override
    public Map<String, String> buildParams() {
        Map<String, String> map = new HashMap<>();
        map.put("directory", directory);
        return map;
    }

    @Override
    public Class<FileUploadResponse> getResponseClass() {
        return FileUploadResponse.class;
    }

    @Override
    public Integer getInterfaceKey() {
        return InterfaceConstant.INTERFACE_KEY_FILE_UPLOAD;
    }

    @Override
    public HttpMethod getHttpMethod() {
        return HttpMethod.POST;
    }

    public String getDirectory() {
        return directory;
    }

    public void setDirectory(String directory) {
        this.directory = directory;
    }
}
