package com.shunwang.baseStone.account.pojo;

import java.util.Date;

public class PersonalAccount extends Account{
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	private Integer paId;   //

    private String paName;   //

    private Long amountLeft;   //
    private Double doubleAmountLeft;   //

    private String paState;   //

    private String paEncrypt;   //

    private Date timeAdd;   //

    private String remark;   //

	public Integer getPaId() {
		return paId;
	}

	public void setPaId(Integer paId) {
		this.paId = paId;
	}

	public String getPaName() {
		return paName;
	}

	public void setPaName(String paName) {
		this.paName = paName;
	}

	public Long getAmountLeft() {
		return amountLeft;
	}

	public void setAmountLeft(Long amountLeft) {
		this.amountLeft = amountLeft;
	}

	public Double getDoubleAmountLeft() {
		return doubleAmountLeft;
	}

	public void setDoubleAmountLeft(Double doubleAmountLeft) {
		this.doubleAmountLeft = doubleAmountLeft;
	}

	public String getPaState() {
		return paState;
	}

	public void setPaState(String paState) {
		this.paState = paState;
	}

	public String getPaEncrypt() {
		return paEncrypt;
	}

	public void setPaEncrypt(String paEncrypt) {
		this.paEncrypt = paEncrypt;
	}

	public Date getTimeAdd() {
		return timeAdd;
	}

	public void setTimeAdd(Date timeAdd) {
		this.timeAdd = timeAdd;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

}
