<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd" >

<sqlMap namespace="com.shunwang.basepassport.user.pojo.MemberToken">
<typeAlias alias="memberToken" type="com.shunwang.basepassport.user.pojo.MemberToken"/>

<resultMap class="com.shunwang.basepassport.user.pojo.MemberToken" id="BaseResultMap">
	<result column="member_id" property="memberId" jdbcType="int"/>
	<result column="member_name" property="memberName" jdbcType="varchar"/>
	<result column="site_id" property="siteId" jdbcType="varchar"/>
	<result column="access_token" property="accessToken" jdbcType="varchar"/>
	<result column="access_token_time_out" property="accessTokenTimeOut" jdbcType="DATETIME"/>
	<result column="refresh_token" property="refreshToken" jdbcType="varchar"/>
    <result column="refresh_token_time_out" property="refreshTokenTimeOut" jdbcType="DATETIME"/>
</resultMap>

<sql id="base_column">
    p.member_id,
    p.member_name,
    p.site_id,
    p.access_token,
    p.access_token_time_out,
    p.refresh_token,
    p.refresh_token_time_out
</sql>

<select id="get" resultMap="BaseResultMap" parameterClass="memberToken">
	SELECT
		<include refid="base_column"/>
	FROM personal_member_token p,personal_member z
	WHERE p.member_id=z.member_id and  z.member_name = #memberName# AND p.site_id = #siteId#
</select>

<select id="getMemberTokenByMemberNameAndSiteId" resultMap="BaseResultMap" parameterClass="memberToken">
    SELECT
        <include refid="base_column"/>
    FROM personal_member_token p,personal_member z
	WHERE p.member_id=z.member_id and z.member_name = #memberName# AND p.site_id = #siteId#
</select>


<insert id="insert" parameterClass="memberToken">
    INSERT INTO personal_member_token(
	        member_id,
	        member_name,
	        site_id
	        <isNotEmpty prepend="," property="accessToken">access_token</isNotEmpty>
	        <isNotEmpty prepend="," property="accessTokenTimeOut">access_token_time_out</isNotEmpty>
	        <isNotEmpty prepend="," property="refreshToken">refresh_token</isNotEmpty>
	        <isNotEmpty prepend="," property="refreshTokenTimeOut">refresh_token_time_out</isNotEmpty>
	      ) VALUES ( 
	        #memberId:INTEGER#,
	        #memberName:VARCHAR#,
	        #siteId:VARCHAR#
	        <isNotEmpty prepend="," property="accessToken">#accessToken:VARCHAR#</isNotEmpty>
	        <isNotEmpty prepend="," property="accessTokenTimeOut">#accessTokenTimeOut:VARCHAR#</isNotEmpty>
	        <isNotEmpty prepend="," property="refreshToken">#refreshToken:VARCHAR#</isNotEmpty>
	        <isNotEmpty prepend="," property="refreshTokenTimeOut">#refreshTokenTimeOut:VARCHAR#</isNotEmpty>
        )
</insert>

<update id="update" parameterClass="memberToken">
    UPDATE personal_member_token p,personal_member z SET
	    p.member_name = #memberName:VARCHAR#,
		p.site_id = #siteId#
	    <isNotEmpty prepend="," property="accessToken">p.access_token = #accessToken:VARCHAR#</isNotEmpty>
	    <isNotEmpty prepend="," property="accessTokenTimeOut">p.access_token_time_out = #accessTokenTimeOut:VARCHAR#</isNotEmpty>
	    <isNotEmpty prepend="," property="refreshToken">p.refresh_token = #refreshToken:VARCHAR#</isNotEmpty>
	    <isNotEmpty prepend="," property="refreshTokenTimeOut">p.refresh_token_time_out = #refreshTokenTimeOut:VARCHAR#</isNotEmpty>
    WHERE p.member_id = z.member_id and  z.member_name = #memberName# AND p.site_id = #siteId#
</update>

<delete id="deleteByMemberNameAndSiteId" parameterClass="memberToken">
    DELETE p FROM personal_member_token p,personal_member z  WHERE p.member_id=z.member_id and z.member_name = #memberName# AND p.site_id = #siteId#
</delete>
</sqlMap>