package com.shunwang.baseStone.sso.weixin.oauth.util;

import com.thoughtworks.xstream.XStream;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;
import org.dom4j.io.XMLWriter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.xml.sax.InputSource;
import org.xml.sax.SAXException;

import java.io.StringReader;
import java.io.StringWriter;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.StringTokenizer;

public class XmlUtils {
	private static final Logger logger = LoggerFactory.getLogger( com.shunwang.util.IO.XmlUtil.class);

	public XmlUtils() {
	}

	public static String toXmlStr(Map<String, Object> params, String toXmlStr, String toCharSet) throws Exception {
		String head = "<?xml version=\"1.0\" encoding=\"UTF-8\"?><xml>";
		String content = toXml(params, toXmlStr, toCharSet);
		String tail = "</xml>";

		return head + content + tail;
	}

	public static String toXml(Map<String, Object> params, String fromCharSet, String toCharSet) throws Exception {
		StringBuilder builder = new StringBuilder();
		Iterator iterator = params.keySet().iterator();

		while (iterator.hasNext()){
			String key = (String)iterator.next();
			Object value = params.get( key );

			builder.append( "<" + key + ">" );
			if (value instanceof HashMap){
				Map map = (HashMap)value;
				builder.append( toXml( map,"UTF-8","UTF-8" ) );
			} else {
				builder.append( value );
			}
			builder.append( "</" + key + ">" );
		}
		return new String(builder.toString().getBytes(fromCharSet), toCharSet);
	}

	public static Document parseXml(String resultXml) throws DocumentException {
		Document result = null;
		SAXReader reader = new SAXReader();
		String encoding = getEncoding(resultXml);
		InputSource source = new InputSource(new StringReader(resultXml));
		source.setEncoding(encoding);
		String FEATURE = "http://apache.org/xml/features/disallow-doctype-decl";

		try {
			reader.setFeature(FEATURE, true);
		} catch (SAXException var7) {
			logger.warn("A DOCTYPE was passed into the XML document");
		}

		result = reader.read(source);
		if (result.getXMLEncoding() == null) {
			result.setXMLEncoding(encoding);
		}

		return result;
	}

	private static String getEncoding(String text) {
		String result = null;
		String xml = text.trim();
		if (xml.startsWith("<?xml")) {
			int end = xml.indexOf("?>");
			String sub = xml.substring(0, end);
			StringTokenizer tokens = new StringTokenizer(sub, " =\"'");

			while(tokens.hasMoreTokens()) {
				String token = tokens.nextToken();
				if ("encoding".equals(token)) {
					if (tokens.hasMoreTokens()) {
						result = tokens.nextToken();
					}
					break;
				}
			}
		}

		return result;
	}

	public static <T> T parseXml(String xml, String replaceNode, Class<T> clazz) {
		XStream xStream = new XStream();
		XStream.setupDefaultSecurity(xStream);
		xStream.allowTypes(new Class[]{clazz});
		xStream.alias(replaceNode, clazz);
		xStream.ignoreUnknownElements();
		return clazz.cast(xStream.fromXML(xml));
	}

	public static void main(String[] args) throws Exception {
		Map<String,Object> map = new HashMap<>() ;
		map.put("ToUserName", "ihjuKegt6MkUvFhZhkRbQd9AY") ;
		map.put("FromUserName", "gh_1c1e13850c29") ;
		Long timestamp = System.currentTimeMillis() ;
		map.put("CreateTime", timestamp+"" ) ;
		map.put("MsgType", "text") ;
		map.put("Content", "参数51  集成环境- 初次使用扫码开机，请您点击") ;

		String replyXml = toXmlStr(map, "UTF-8", "UTF-8") ;

		System.out.println(replyXml);
	}
}
