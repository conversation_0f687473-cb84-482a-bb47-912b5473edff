package com.shunwang.basepassport.config.dao;

import com.shunwang.baseStone.core.dao.BaseStoneIbatisDao;
import com.shunwang.basepassport.config.pojo.AppLoginElement;
import org.springframework.cache.annotation.Cacheable;

import java.lang.reflect.Method;

public class AppLoginElementDao extends BaseStoneIbatisDao<AppLoginElement>{

	/**
	 * @see com.shunwang.baseStone.cache.keygenerator.ConfigAppLoginElementKeyGenerator
	 * @param appId 站点id
	 * @param elementName 登录配置项
	 */
	@Cacheable(value = "cache", keyGenerator = "configAppLoginElementKeyGenerator", unless = "#result==null")
	public AppLoginElement getByAppIdAndElementName(String appId, String elementName) {
		AppLoginElement temp = new AppLoginElement();
		temp.setAppId(appId);
		temp.setElementName(elementName);
		return (AppLoginElement) getSqlMapClientTemplate().queryForObject(getStatementNameWrap("getByAppIdAndElementName"), temp);
	}
}
