package com.shunwang.basepassport.user.pojo;

import com.shunwang.baseStone.core.detail.Detail;
import com.shunwang.baseStone.core.detail.DetailItem;
import com.shunwang.baseStone.core.detail.HasDetail;
import com.shunwang.baseStone.core.pojo.BaseStoneObject;
import com.shunwang.basepassport.detail.pojo.PersonalEditLog;

import java.io.Serializable;
import java.util.Date;

public class MemberMultipleAccountBind extends BaseStoneObject implements HasDetail {

	private static final long serialVersionUID = 4944744176303584779L;
	private PersonalEditLog personalEditLog = new PersonalEditLog();

	private Integer id ;
	private Integer memberId ;
	private String memberName;
	private String unionId ;
	private Integer idCard;
	private Date timeAdd ;
	private Date timeEdit ;

	private String fromSiteId;
	/**1多账号主动绑定 2单账号自动绑定**/
	private Integer importType;

	public Integer getMemberId() {
		return memberId;
	}

	public void setMemberId(Integer memberId) {
		this.memberId = memberId;
	}

	public String getUnionId() {
		return unionId;
	}

	public void setUnionId(String unionId) {
		this.personalEditLog.addItem(new DetailItem("unionId",this.getUnionId(), unionId));
		this.unionId = unionId;
	}

	public String getFromSiteId() {
		return fromSiteId;
	}

	public void setFrom(String fromSiteId) {
		this.personalEditLog.addItem(new DetailItem("fromSiteId",this.getFromSiteId(), fromSiteId));
		this.fromSiteId = fromSiteId;
	}

	public Date getTimeAdd() {
		return timeAdd;
	}

	public void setTimeAdd(Date timeAdd) {
		this.timeAdd = timeAdd;
	}

	public Date getTimeEdit() {
		return timeEdit;
	}

	public void setTimeEdit(Date timeEdit) {
		this.timeEdit = timeEdit;
	}

	@Override
	public Serializable getPk() {
		return id;
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public PersonalEditLog getPersonalEditLog() {
		return personalEditLog;
	}

	public void setPersonalEditLog(PersonalEditLog personalEditLog) {
		this.personalEditLog = personalEditLog;
	}


	public void beginBuildLog(String editItem) {
		this.beginBuildLog();
		this.personalEditLog.setEditItem(editItem) ;
	}

	@Override
	public void beginBuildLog(){
		this.personalEditLog.beginBuildLog(true);
	}

	@Override
	public Detail getDetail() {
		return personalEditLog;
	}

    public String getMemberName() {
        return memberName;
    }

    public void setMemberName(String memberName) {
        this.memberName = memberName;
    }

	public Integer getIdCard() {
		return idCard;
	}

	public void setIdCard(Integer idCard) {
		this.personalEditLog.addItem(new DetailItem("idCard", this.getIdCard(), idCard));
		this.idCard = idCard;
	}

	public void setFromSiteId(String fromSiteId) {
		this.fromSiteId = fromSiteId;
	}

	public Integer getImportType() {
		return importType;
	}

	public void setImportType(Integer importType) {
		this.importType = importType;
	}
}
