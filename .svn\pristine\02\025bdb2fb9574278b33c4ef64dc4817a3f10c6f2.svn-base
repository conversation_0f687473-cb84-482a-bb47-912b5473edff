package com.shunwang.basepassport.user.service;

import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.shunwang.baseStone.cache.CacheKeyConstant;
import com.shunwang.baseStone.context.IPContext;
import com.shunwang.baseStone.context.RedisContext;
import com.shunwang.basepassport.manager.request.report.FreeLoginReportRequest;
import com.shunwang.basepassport.manager.service.report.DcReportServiceClient;
import com.shunwang.basepassport.user.common.UserLoginSessionUtil;
import com.shunwang.toolbox.tracing.TraceUtil;
import com.shunwang.util.date.DateUtil;
import com.shunwang.util.json.GsonUtil;
import com.shunwang.util.lang.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.concurrent.*;

public class DcReportService {
    private static final Logger logger = LoggerFactory.getLogger(DcReportService.class);

    private static final Executor executor = new ThreadPoolExecutor(5, 20,
            60L, TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(1000),
            new ThreadPoolExecutor.DiscardOldestPolicy());

    /**
     * 上报数据，站点使用缓存数据的站点
     * @param memberName 用户名
     * @param optTypeAndExtInfo 上报类型
     */
    public static void freeLoginReport(String memberName, OptTypeAndExtInfo optTypeAndExtInfo) {
        freeLoginReport(memberName, memberName, optTypeAndExtInfo, null);
    }

    /**
     * 上报数据，站点使用实际调用方
     * @param memberName 用户名
     * @param optTypeAndExtInfo 上报类型
     * @param accessSiteId 调用方站点id
     */
    public static void freeLoginReport(String memberName, OptTypeAndExtInfo optTypeAndExtInfo, String accessSiteId) {
        freeLoginReport(memberName, memberName, optTypeAndExtInfo, accessSiteId);
    }

    /**
     * 上报数据，存在主账号切换的数据时使用
     * @param memberName 用户名
     * @param netBarMemberName 网吧用户名
     * @param optTypeAndExtInfo 上报类型
     * @param accessSiteId 调用方站点id
     */
    public static void freeLoginReport(String memberName, String netBarMemberName, OptTypeAndExtInfo optTypeAndExtInfo, String accessSiteId) {
        boolean reportSwitch = RedisContext.getSwitchConfig(CacheKeyConstant.ConfigResourcesConstants.NET_BAR_FREE_LOGIN_CONFIG,
                CacheKeyConstant.ConfigResourcesConstants.FREE_LOGIN_REPORT_SWITCH, false);
        if (!reportSwitch) {
            if (logger.isDebugEnabled()) {
                logger.debug("免登上报开关关闭，不上报");
            }
            return;
        }
        String ip = IPContext.getIp();
        executor.execute(() -> runFreeLoginReport(memberName, netBarMemberName, optTypeAndExtInfo, ip, accessSiteId));
    }

    public static void runFreeLoginReport(String memberName, String netBarMemberName, OptTypeAndExtInfo optTypeAndExtInfo, String ip, String accessSiteId) {
        try {
            TraceUtil.nestedSpanStart();
            if (StringUtil.isBlank(memberName)) {
                logger.warn("数据缺失，不上报");
                return;
            }
            UserLoginSessionUtil.LoginSession loginSession = UserLoginSessionUtil.getLoginSession(netBarMemberName);
            if (loginSession == null) {
                logger.warn("用户登录数据丢失，不上报");
                return;
            }
            String extData = loginSession.getExtData();
            if (StringUtil.isBlank(extData)) {
                logger.info("无extData数据，不上报");
                return;
            }
            String traceId = loginSession.getTraceId();
            loginSession = UserLoginSessionUtil.getLoginSession(memberName);
            JsonObject jsonObject = JsonParser.parseString(extData).getAsJsonObject();
            Map<String, String> reportMap = new LinkedHashMap<>();
            reportMap.put("optType", optTypeAndExtInfo.optType);
            reportMap.put("ssoAuth", loginSession.isSsoAuth() ? "0" : "1");
            reportMap.put("baid", GsonUtil.getStringFromJsonObject(jsonObject, "barId"));
            reportMap.put("report_time", DateUtil.getCurrentDateStamp());
            reportMap.put("memberName", memberName);
            String siteId = loginSession.getSiteId();
            reportMap.put("siteId", StringUtil.isBlank(accessSiteId) ? siteId : accessSiteId);
            reportMap.put("extInfo", optTypeAndExtInfo.extInfo);
            reportMap.put("token", traceId);
            reportMap.put("guid", GsonUtil.getStringFromJsonObject(jsonObject, "guid"));
            reportMap.put("mac", GsonUtil.getStringFromJsonObject(jsonObject, "mac"));
            reportMap.put("barName", GsonUtil.getStringFromJsonObject(jsonObject, "barName"));
            reportMap.put("ip", ip);

            FreeLoginReportRequest request = new FreeLoginReportRequest();
            request.setParam(reportMap);
            DcReportServiceClient.execute(request);
        } catch (Exception e) {
            logger.error("免登数据上报异常[{}]", e.getMessage());
        } finally {
            TraceUtil.nestedSpanEnd();
        }
    }

    public enum OptTypeAndExtInfo {
        ID_CARD_IMPORT_SUCCESS("1", "0", "身份证导入成功"),
        DLL_OBTAIN_TOKEN_SUCCESS("2", "0", "免登写入成功"),

        FREE_LOGIN_WX_BIND("3", "0", "免登公众号协议"),
        FREE_LOGIN_WX_SUB("3", "1", "免登公众号关注"),
        FREE_LOGIN_MINI_BIND("3", "2", "免登小程序绑手机"),
        FREE_LOGIN_MINI_AUTH("3", "3", "免登小程序网吧授权"),
        FREE_LOGIN_HEAD_AUTH("3", "4", "免登头像授权"),
        FREE_LOGIN_HEAD_AUTH_SUCCESS("3", "5", "免登头像授权登录成功"),
        FREE_LOGIN_HEAD_AUTH_AUTH_SUCCESS("3", "6", "免登头像授权"),
        FREE_LOGIN_HEAD_AUTH_SINGLE_TO_MULTIPLE("3", "7", "免登头像授权多账号"),
        FREE_LOGIN_HEAD_AUTH_FOR_MULTIPLE("3", "8", "免登头像授权多账号"),
        FREE_LOGIN_HEAD_AUTH_FOR_MULTIPLE_SUCCESS("3", "9", "免登头像授权多账号成功"),

        WX_OPEN_BAR_AUTH("4", "0", "公众号回调网吧授权"),
        WX_OPEN_BIND_WX_SUCCESS("4", "1", "公众号回调绑微信成功"),
        WX_OPEN_BIND_WX_FAIL("4", "2", "公众号回调绑微信失败"),
        WX_OPEN_BIND_ID_CARD_SUCCESS("4", "3", "公众号回调绑身份证成功"),
        WX_OPEN_BIND_ID_CARD_FAIL("4", "4", "公众号回调绑身份证失败"),
        WX_OPEN_LOGIN_SUCCESS("4", "5",  "公众号回调登录成功"),
        WX_OPEN_MULTIPLE_SUB_SUCCESS("4", "6", "公众号多账号关注成功"),
        WX_OPEN_MULTIPLE_SUB_FAIL("4", "7", "公众号多账号关注失败"),

        H5_BIND_BAR_AUTH("8", "0", "H5绑手机回调网吧授权"),
        H5_BIND_BIND_MOBILE_FAIL("8", "1", "H5绑手机回调已绑其他手机"),
        H5_BIND_BIND_ID_CARD_SUCCESS("8", "3", "H5绑手机回调绑身份证成功"),
        H5_BIND_BIND_ID_CARD_FAIL("8", "4", "H5绑手机回调绑身份证失败"),
        H5_BIND_LOGIN_SUCCESS("8", "5",  "H5绑手机回调登录成功"),

        WX_MINI_BAR_AUTH("5", "0", "小程序回调网吧授权"),
        WX_MINI_BIND_WX_SUCCESS("5", "1", "小程序回调绑微信成功"),
        WX_MINI_BIND_WX_FAIL("5", "2",  "小程序回调绑微信失败"),
        WX_MINI_BIND_ID_CARD_SUCCESS("5", "3",  "小程序回调绑身份证成功"),
        WX_MINI_BIND_ID_CARD_FAIL("5", "4",  "小程序回调绑身份证失败"),
        WX_MINI_LOGIN_SUCCESS("5", "5", "小程序回调登录成功"),
        WX_MINI_BIND_MOBILE_FAIL("5", "6",  "小程序回调绑手机失败"),

        WX_CARD_MSG_BIND_SEND_SUCCESS("6", "0", "卡片消息绑手机发送成功"),
        WX_CARD_MSG_BIND_SEND_FAIL("6", "1",  "卡片消息绑手机发送失败"),
        WX_CARD_MSG_SUB_SEND_SUCCESS("6", "2", "卡片消息关注发送成功"),
        WX_CARD_MSG_SUB_SEND_FAIL("6", "3",  "卡片消息关注发送失败"),
        WX_CARD_MSG_AUTH_SEND_SUCCESS("6", "4", "卡片消息授权发送成功"),
        WX_CARD_MSG_MULTIPLE_SEND_FAIL("6", "5",  "卡片消息多账号发送失败"),
        WX_CARD_MSG_MULTIPLE_SEND_SUCCESS("6", "6", "卡片消息多账号发送成功"),

        WX_MSG_TEMPLATE_SEND_SUCCESS("7", "0",  "模版消息发送成功"),
        WX_MSG_TEMPLATE_SEND_FAIL("7", "1", "模版消息发送失败"),
        WX_MSG_TEMPLATE_MULTIPLE_SEND_SUCCESS("7", "2",  "模版消息多账号发送成功"),
        WX_MSG_TEMPLATE_MULTIPLE_SEND_FAIL("7", "3", "模版消息多账号发送失败"),

        MULTIPLE_AUTH_NORMAL_SUCCESS("9", "1", "多账号账密授权成功"),
        MULTIPLE_UN_AUTH_NORMAL("9", "2", "多账号账密未授权"),
        MULTIPLE_AUTH_QQ_SUCCESS("9", "3", "多账号QQ授权成功"),
        MULTIPLE_UN_AUTH_QQ("9", "4", "多账号QQ未授权"),
        MULTIPLE_AUTH_WX_SUCCESS("9", "5", "多账号微信授权成功"),
        MULTIPLE_UN_AUTH_WX("9", "6", "多账号微信未授权"),
        MULTIPLE_AUTH_WX_OPEN_SUCCESS("9", "7", "多账号公众号授权成功"),
        MULTIPLE_UN_AUTH_WX_OPEN("9", "8", "多账号公众号未授权"),
        MULTIPLE_AUTH_WB_SUCCESS("9", "9", "多账号微博授权成功"),
        MULTIPLE_UN_AUTH_WB("9", "10", "多账号微博未授权"),
        MULTIPLE_AUTH_GOOGLE_SUCCESS("9", "11", "多账号谷歌授权成功"),
        MULTIPLE_UN_AUTH_GOOGLE("9", "12", "多账号谷歌未授权"),

        ;

        OptTypeAndExtInfo(String optType, String extInfo, String des) {
            this.extInfo = extInfo;
            this.optType = optType;
            this.des = des;
        }

        private String optType;
        private String extInfo;
        private String des;

        public String getOptType() {
            return optType;
        }

        public String getExtInfo() {
            return extInfo;
        }

        public String getDes() {
            return des;
        }

    }
}
