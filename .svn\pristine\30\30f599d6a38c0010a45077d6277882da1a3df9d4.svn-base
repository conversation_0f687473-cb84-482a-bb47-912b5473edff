<%@ page contentType="text/html;charset=UTF-8"%>
<%@ include file="/common/taglibs.jsp"%>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name="Keywords" content="顺网通行证、 修改邮箱 、实名 认证" />
<meta name="Description" content="填写新密码等待审核。" />
<title>顺网通行证-修改邮箱-实名认证修改</title>
<script type="text/javascript" src="${staticServer}/scripts/front/member/min/validation.min.js"></script>
<script type="text/javascript" src="${staticServer}/scripts/front/common/citys.js" ></script>
<script type="text/javascript" src="${staticServer}/scripts/front/common/calendar.js"></script>
<script type="text/javascript" src="${staticServer}/scripts/front/find/globalAppeal_front.js"></script>
<script type="text/javascript" src="${staticServer}/scripts/front/changeEmail/changeEmail.js"></script>

<style>
body,select
{
font-size:9pt;
font-family:Verdana;
}
a
{
color:red;
text-decoration:none;
}
a:hover{
text-decoration:underline;
}
</style>


</head>
<body>
<%@ include file="/front/changeEmail/global_nav.jsp" %>
<div class="c_body forget_s02">
    <ul class="step_bar">
        <li class="current"><em>1</em> 填写账号信息</li>
        <li><em>2</em> 设置新邮箱</li>
    </ul>
    <div class="form_group">
				<form class="passform " id="changEmailForm" action="${appServer}/front/noLogin/actu_changeEmailSuccess_front.htm"  method="post">
					<input type="hidden" name="memberName"  value="${memberName }"/>
					<input type="hidden" name="appeal.userName" id="userName" value="${appeal.userName}"/>
					<input type="hidden" name="appeal.realName" id="realName" value="${appeal.realName}"/> 
					<input type="hidden" name="appeal.idCardNo" id="idCardNo" value="${appeal.idCardNo}"/> 
					<input type="hidden" name="appeal.email" id="email" value="${appeal.email}"/> 
					<input type="hidden" name="appeal.mobile" id="mobile" value="${appeal.mobile}"/> 			     
					<input type="hidden" name="appeal.idCardUrl" id="idCardImgUrlActu" value="${appeal.idCardUrl}"/>
					<%@ include file="globalResetEmail_front.jsp" %>
				</form>
			</div>
		</div>
	<script type="text/javascript" >
	    var modifyEmail =
	    function() {
	       if(!check()) return false;
	        $("#changEmailForm").submit();
	    }
	</script>
</body>
</html>
