package com.shunwang.baseStone.cache.lock;

import com.shunwang.baseStone.context.RedisOperation;

import java.time.Duration;
import java.util.concurrent.TimeUnit;

/**
 * 自动过期redis锁
 */
public class TimedLock {
    RedisOperation redisOperation;

    protected String key;
    protected String value;
    protected long expire;
    protected TimeUnit timeUnit;

    public TimedLock(String key, Duration duration) {
        this(key, "0", duration.getSeconds(), TimeUnit.SECONDS);
    }

    public TimedLock(String key, String value, Duration duration) {
        this(key, value, duration.getSeconds(), TimeUnit.SECONDS);
    }

    public TimedLock(String key, String value, long expire, TimeUnit timeUnit) {
        this.key = key;
        this.value = value;
        this.expire = expire;
        this.timeUnit = timeUnit;
    }

    public boolean tryLock() {
        return redisOperation.setNx(key, value, expire, timeUnit);
    }

    public void unlock() {
        redisOperation.del(key);
    }

    public void setRedisOperation(RedisOperation redisOperation) {
        this.redisOperation = redisOperation;
    }
}
