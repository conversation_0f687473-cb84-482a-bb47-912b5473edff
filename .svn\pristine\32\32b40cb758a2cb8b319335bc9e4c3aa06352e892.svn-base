package com.shunwang.passport.upload.web;

import com.shunwang.baseStone.sysconfig.context.SysConfigContext;
import com.shunwang.basepassport.actu.constant.ActuConstant;
import com.shunwang.basepassport.context.UserContext;
import com.shunwang.basepassport.manager.request.inter.FileUploadRequest;
import com.shunwang.basepassport.manager.response.inter.FileUploadResponse;
import com.shunwang.basepassport.manager.service.InterfaceServiceClient;
import com.shunwang.basepassport.user.pojo.Member;
import com.shunwang.common.ImageUploadUtil;
import com.shunwang.passport.upload.constant.UploadContstant;
import com.shunwang.upload.exception.UploadException;
import com.shunwang.util.IO.FileUtil;
import com.shunwang.util.lang.StringUtil;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.List;
import java.util.Random;

/**
 * @Description:文件上传action
 * <AUTHOR>  create at 2011-9-13 下午01:53:49
 * @FileName com.shunwang.passport.upload.web.UploadAction.java
 */
public class UploadAction extends  com.shunwang.passport.upload.action.UploadAction {

	private File file;
	private String fileFileName;  //file+FileName为固定写法,否则取不到
	private String fileContentType;  //file+ContentType为固定写法
	private String type;
	
	private boolean isLogin = true;

	/**
	 * <AUTHOR> create at 2011-9-9 上午11:03:20 
	 */
	private static final long serialVersionUID = 8800707816097824917L;

	@Override
	protected String saveFile(File file) {
		try {
			checkParams(file);

			String fileName=getFileName();
            File targetFile = new File(SysConfigContext.getImgDir()+"/"+UploadContstant.UPLOAD_PATH_MAP.get(type)+fileName);
			FileUtil.copyData(file, targetFile);
			// 复制一份小文件
			String smallFileName = getSmallPath(targetFile.getPath()) ;
			File smallTargetFile = new File(smallFileName);
			ImageUploadUtil.zipWidthHeightImageFile(file,smallTargetFile,300,0,0.5f) ;
			return fileName;
		} catch (IOException e) {
			throw new UploadException("上传文件失败:"+e.getMessage());
		}
	}

	private String getSmallPath(String path){
		if(StringUtil.isBlank(path)){
			return path ;
		}
		Integer idx = path.lastIndexOf(".") ;
		if(idx > 0) {
			String subfix = path.substring(idx);
			return path.substring(0, idx)+"_small"+subfix ;
		}
		return path ;
	}

	public String ajaxUploadFile() {
		try{
			this.getResponse().setCharacterEncoding("utf-8");
			this.getResponse().setContentType("text/html; charset=UTF-8");
			this.getResponse().setHeader("Cache-Control", "no-cache");
			File file = getFile();
			if(file != null){
				String fileNameLong = this.getFileFileName();
				if(!this.isAllowed(this.getExtension(fileNameLong))){
					throw new UploadException("该扩展名不允许上传");
				}
				String url = saveFile(this.getFile());
				//windows环境下的目录分隔符,传到js中eval时会出错,需改为http url中使用的"/"
				url = url.replace("\\","/");
				try {
					getResponse().getWriter().write("{suc:true,fileName:'"+url+"'}");
					getResponse().getWriter().close();
				} catch (IOException e) {
					log.error("json回写异常",e);
				}
			}
		}catch(UploadException exp){
			handleException(exp);
		}
		return null;
	}

	public String ajaxUploadNetFile() {
		try {
			this.getResponse().setCharacterEncoding("utf-8");
			this.getResponse().setContentType("text/html; charset=UTF-8");
			this.getResponse().setHeader("Cache-Control", "no-cache");
			File file = getFile();
			if (file != null) {
				String fileNameLong = this.getFileFileName();
				if (!this.isAllowed(this.getExtension(fileNameLong))) {
					throw new UploadException("该扩展名不允许上传");
				}

				checkParams(file);
				String fileName = getFileName();
				//windows环境下的目录分隔符,传到js中eval时会出错,需改为http url中使用的"/"
				fileName = fileName.replace("\\", "/");
				try (FileInputStream fin = new FileInputStream(file)) {
					FileUploadRequest uploadRequest = new FileUploadRequest();
					uploadRequest.setDirectory(UploadContstant.UPLOAD_PATH_MAP.get(type) + fileName);
					uploadRequest.setInputStream(fin);

					FileUploadResponse response = InterfaceServiceClient.execute(uploadRequest);
					if (!response.isSuccess()) {
						log.error("上传文件异常[{}]", response.getMsg());
						getResponse().getWriter().write("{suc:false,error:'" + response.getMsg() + "'}");
						getResponse().getWriter().close();
					}
				} catch (IOException ex) {
					log.error("读取文件异常", ex);
				}

				try {
					getResponse().getWriter().write("{suc:true,fileName:'" + fileName + "'}");
					getResponse().getWriter().close();
				} catch (IOException e) {
					log.error("json回写异常", e);
				}
			}
		} catch (UploadException exp) {
			handleException(exp);
		}
		return null;
	}


	 @Override
	protected void succ(String url) {
		try {
			getResponse().setContentType("text/json;charset=UTF-8");
			getResponse().getWriter().write("{suc:true,fileName:'"+url+"'}");
			getResponse().getWriter().close();
		} catch (IOException e) {
			log.error("json回写异常",e);
		}
	}
	@Override
	protected List<String> getAllowedExtend(){
		List<String> ret = new java.util.ArrayList<String>();
		ret.add("gif");
		ret.add("jpg");
		ret.add("jpeg");
		ret.add("bmp");
		ret.add("png");
		return ret;
	}
	@Override
	protected  void handleException(UploadException exp){
		try {
		    log.error("",exp);
			getResponse().getWriter().write("{suc:false,errorMsg:'"+exp.getMessage()+"'}");
			getResponse().getWriter().close();
		} catch (IOException e) {
			log.error("异常回写异常",e);
		}
	}
	/**
	 * 检查数据参数
	 * <AUTHOR> create at 2011-9-9 下午02:06:48
	 */
	protected void checkParams(File file){
		//if(null==UserContext.getUser())
		//	throw new UploadException("nologin");
		if(null==file)
			throw new UploadException("请选择要上传的图片，大小不能超过5M");
		if(file.length() > ActuConstant.PIC_MAX_SIZE)
			throw new UploadException("图片大小不能超过5M，请重新选择");
		if(StringUtil.isBlank(type)||!UploadContstant.UPLOAD_PATH_MAP.containsKey(type))
			throw new UploadException("标识符错误");
	}

	public void setFile(File file) {
		this.file = file;
	}

	public File getFile() {
		return file;
	}
	private String getFileName(){
        Member member = UserContext.getUser();

        if (member == null ) {
            return ""+System.currentTimeMillis()+new Random().nextInt(1000)+ ".jpg";
        }
        Integer memberId = member.getMemberId();
        String dir = secondRank(memberId) + File.separator + thirdRank(memberId);
		return dir + File.separator + System.currentTimeMillis()+new Random().nextInt(1000)+ ".jpg";
	}


    private String secondRank(Integer memberId) {
	    return (memberId / 1000000) + "m";
	}
	
    private  String thirdRank(Integer memberId) {
	    int quotient = memberId / 1000000;
	    return ((memberId - quotient * 1000000) / 1000) + "K";
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getType() {
		return type;
	}

	public void setLogin(boolean isLogin) {
		this.isLogin = isLogin;
	}

	public boolean isLogin() {
		return isLogin;
	}

	public String getFileFileName() {
		return fileFileName;
	}

	public void setFileFileName(String fileFileName) {
		this.fileFileName = fileFileName;
	}

	public String getFileContentType() {
		return fileContentType;
	}

	public void setFileContentType(String fileContentType) {
		this.fileContentType = fileContentType;
	}
}
