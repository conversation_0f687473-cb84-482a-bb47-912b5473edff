package com.shunwang.basepassport.commonExp;

import com.shunwang.baseStone.core.exception.BaseStoneException;
import com.shunwang.basepassport.binder.common.ErrorCode;
import com.shunwang.util.lang.StringUtil;

public class FrequentlyExp extends BaseStoneException {
    private static final long serialVersionUID = 1L;

    public FrequentlyExp(){
        super(ErrorCode.C_1210.getCode(),ErrorCode.C_1210.getDescription());
    }

    public FrequentlyExp(String msg) {
        this.setMsgId(ErrorCode.C_1210.getCode());
        this.setMsg(StringUtil.isBlank(msg)?StringUtil.EMPTY_STRING:msg);
    }
}
