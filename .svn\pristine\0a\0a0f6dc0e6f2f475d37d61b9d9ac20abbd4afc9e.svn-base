package com.shunwang.basepassport.binder.web.bind.processor;

import com.shunwang.baseStone.context.BaseStoneContext;
import com.shunwang.baseStone.core.response.BaseStoneResponse;
import com.shunwang.basepassport.binder.common.BinderConstants;
import com.shunwang.basepassport.binder.pojo.SendBinder;
import com.shunwang.basepassport.binder.processor.Processor;
import com.shunwang.basepassport.binder.processor.ProcessorContext;
import com.shunwang.basepassport.binder.response.PhoneLoginRespone;
import com.shunwang.basepassport.binder.web.send.bean.MobileBindBean;
import com.shunwang.basepassport.interc.SingleAccountBindService;
import com.shunwang.basepassport.user.common.MemberConstants;
import com.shunwang.basepassport.user.common.TicketUtil;
import com.shunwang.basepassport.user.dao.MemberDao;
import com.shunwang.basepassport.user.pojo.Member;
import com.shunwang.basepassport.user.response.ValidateAgainResponse;

import java.util.List;

/**
 * 15: 手机验证码，选取账户设置手机号为其登录号
 *
 * <AUTHOR>
 * @date 2018/12/17
 **/
public class MobileChooseAccountToBindProcessor implements Processor {
    @Override
    public boolean matches(ProcessorContext context) {
        return BinderConstants.MOBLIE_CHOOSE_ACCOUNT_TOBIND.equals(context.getSendBinder().getBusinessType());
    }

    @Override
    public boolean doProcess(ProcessorContext context) {
        SendBinder sendBinder = context.getSendBinder();
        MobileBindBean bean = (MobileBindBean) context.getT();
        sendBinder.setNumber(bean.getNumber());
        sendBinder.validate(bean.getActiveNo());
        List<Member> memberList = getMemberDao().getCouldBindListByMobile(bean.getNumber());
        BaseStoneResponse response = setAsLoginAccount(memberList.get(0), bean);
        context.put("response", response);

        return false;
    }

    private BaseStoneResponse setAsLoginAccount(Member tempMember, MobileBindBean bean) {
        tempMember.setBindState(tempMember.getBindState() | MemberConstants.MEMBER_STATE_PHONE_AS_LOGIN_ACCOUNT);
        tempMember.update();
        tempMember.setLoginType(MemberConstants.LOGIN_TYPE_ACTIVE_NO);
        tempMember.setEnv(bean.getEnvironment());
        tempMember.setExtData(bean.getRemark());
        tempMember.loginWithNoPwd();
        String sign = getSingleAccountBindService().bindTable(tempMember, bean.getBindType(),
                bean.getSingleAccountToken(), bean.getBussiness());
        if (null != sign) {
            ValidateAgainResponse response = new ValidateAgainResponse();
            response.setMsgId("1019");
            response.setToken(sign);
            response.setMsg("请绑定手机号");
            return response;
        }
        PhoneLoginRespone response = new PhoneLoginRespone(tempMember);
        response.setTicket(TicketUtil.buildLoginTicket(tempMember));
        return response;
    }

    private SingleAccountBindService getSingleAccountBindService() {
        return (SingleAccountBindService) BaseStoneContext.getInstance().getBean("singleAccountBindService");
    }

    private MemberDao getMemberDao() {
        return (MemberDao) BaseStoneContext.getInstance().getBean("memberDao");
    }
}
