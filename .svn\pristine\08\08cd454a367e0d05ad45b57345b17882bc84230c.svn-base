package com.shunwang.basepassport.config.dao;

import com.shunwang.baseStone.core.dao.BaseStoneIbatisDao;
import com.shunwang.basepassport.config.pojo.SmsConfig;
import org.springframework.cache.annotation.Cacheable;

public class SmsConfigDao extends BaseStoneIbatisDao<SmsConfig> {

    @Cacheable(value = "cache", keyGenerator = "configSmsConfigKeyGenerator", unless = "#result == null")
    public SmsConfig findBySiteIdAndBusinessType(String siteId, String businessType) {
        SmsConfig smsConfig = new SmsConfig();
        smsConfig.setSiteId(siteId);
        smsConfig.setBusinessType(businessType);
        return (SmsConfig)getSqlMapClientTemplate().queryForObject(getStatementNameWrap("findOne"), smsConfig);
    }
}
