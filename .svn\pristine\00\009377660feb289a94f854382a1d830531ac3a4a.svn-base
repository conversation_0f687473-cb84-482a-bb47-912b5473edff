package com.shunwang.baseStone.cache.keygenerator;

import com.shunwang.baseStone.cache.CacheKeyConstant;
import org.jetbrains.annotations.NotNull;

import java.lang.reflect.Method;

/**
 * configService 相关缓存
 **/
public class ConfigInterfaceKeyGenerator extends BaseKeyGenerator {

    @NotNull
    @Override
    public Object generate(@NotNull Object target, @NotNull Method method, @NotNull Object... params) {
        return generate(CacheKeyConstant.EhcacheKey.CONFIG_SERVICE, params);
    }
}
