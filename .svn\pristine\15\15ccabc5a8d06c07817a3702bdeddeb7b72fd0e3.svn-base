jQuery.EmailAutoComplete=function(a){var c,d,b=a,e=[];e.push('<div class="AutoComplete" id="AutoComplete">'),e.push('	<ul class="AutoComplete_ul">'),e.push('		<li class="AutoComplete_title">请选择邮箱后缀</li>'),e.push('		<li hz="@163.com"></li>'),e.push('		<li hz="@qq.com"></li>'),e.push('		<li hz="@gmail.com"></li>'),e.push('		<li hz="@sohu.com"></li>'),e.push('		<li hz="@sina.com"></li>'),e.push('		<li hz="@126.com"></li>'),e.push('		<li hz="@hotmail.com"></li>'),e.push("	</ul>"),e.push("</div>"),$("body").append(e.join("")),c=$("#AutoComplete"),c.data("elt",b),d=c.find("li:not(.AutoComplete_title)"),d.mouseover(function(){$(this).siblings().filter(".hover").removeClass("hover"),$(this).addClass("hover")}).mouseout(function(){$(this).removeClass("hover")}).mousedown(function(){c.data("elt").val($(this).text()).change(),c.hide()}),b.keyup(function(a){if(/13|38|40|116/.test(a.keyCode)||""==this.value)return!1;var b=this.value;return-1==b.indexOf("@")?(c.hide(),!1):(d.each(function(){this.innerHTML=b.replace(/\@+.*/,"")+$(this).attr("hz"),this.innerHTML.indexOf(b)>=0?$(this).show():$(this).hide()}).filter(".hover").removeClass("hover"),c.show().css({left:$(this).offset().left,top:$(this).offset().top+$(this).outerHeight(!0)-1,position:"absolute",zIndex:"99999"}),0==d.filter(":visible").length?c.hide():d.filter(":visible").eq(0).addClass("hover"),void 0)}).keydown(function(a){38==a.keyCode?d.filter(".hover").prev().not(".AutoComplete_title").addClass("hover").next().removeClass("hover"):40==a.keyCode?d.filter(".hover").next().addClass("hover").prev().removeClass("hover"):13==a.keyCode&&(d.filter(".hover").mousedown(),a.preventDefault())}).focus(function(){c.data("elt",$(this))}).blur(function(){c.hide()})};