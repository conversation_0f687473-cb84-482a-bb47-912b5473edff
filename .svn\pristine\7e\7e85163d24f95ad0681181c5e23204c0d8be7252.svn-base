package com.shunwang.baseStone.sso.util;

import com.shunwang.baseStone.cache.CacheKeyConstant;
import com.shunwang.baseStone.context.RedisContext;
import com.shunwang.basepassport.binder.common.StaticImgTool;
import com.shunwang.basepassport.user.common.HeadUrlUtil;
import com.shunwang.basepassport.user.pojo.Member;
import com.shunwang.util.IO.FileUtil;
import com.shunwang.util.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLDecoder;
import java.util.UUID;

public class UploadFileUtil {

	private final static Logger logger = LoggerFactory.getLogger(UploadFileUtil.class) ;
	private static final String key = "/";
	private String headImagePath;
	private static final String DEFAULT = "default";
	private static final String TMP = "tmp";
	private String headImageUrl;
	
	public String getHeadImageUrl() {
		return headImageUrl;
	}

	public void setHeadImageUrl(String headImageUrl) {
		this.headImageUrl = headImageUrl;
	}

	public String getHeadImagePath() {
		return headImagePath;
	}

	public void setHeadImagePath(String headImagePath) {
		this.headImagePath = headImagePath;
	}

	public void deleteHeadImage(Integer memberId, String imgName) {
	    File file = new File(bigPath(memberId), imgName);
	    file.delete();
	}
	
    
    @SuppressWarnings("unused")
	private void uploadSmallImage(Member member, int width, int height) throws Exception {
	    String imgName = member.getHeadImg();
	    File file = new File(bigPath(member.getMemberId()), imgName);
	    try {
	        Image image = ImageIO.read(file);
	        BufferedImage tag = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
	      /**Image.SCALE_SMOOTH 的缩略算法   生成缩略图片的平滑度的
	 		优先级比速度高 生成的图片质量比较好 但速度慢**/  
	      tag.getGraphics().drawImage(
	        image.getScaledInstance(width, height, 
	        		Image.SCALE_SMOOTH), 0, 0, null);
	      
	      tag.getGraphics().dispose(); //释放资源
	      File tmpFile = new File(smallPath(member.getMemberId()));
	      if (!(tmpFile.exists())) {
	          tmpFile.mkdirs();
	      }
	
	      ImageIO.write(tag, "PNG", new File(tmpFile, imgName));
	    } catch (IOException e) {
	    	throw e;
	    }
  }

	public void deleteSmallHeadImage(Integer memberId, String imgName) {
	    File file = new File(smallPath(memberId), imgName);
	    file.delete();
	}
	  
	public void deleteTmpImage(Integer memberId, String imgName) {
	    File file = new File(tmpPath(memberId), imgName);
	    file.delete();
	}
	
	@SuppressWarnings("unused")
	private String bigUrl(Integer memberId) {
	    StringBuilder bu = new StringBuilder();
	    bu.append(headImageUrl)
	    .append(key)
	    .append(secondRank(memberId))
	    .append(key)
	    .append(thirdRank(memberId))
	    .append(key);

	    return url(bu.toString());
	}
	
	public String smallUrl(Integer memberId) {
	    StringBuilder bu = new StringBuilder();
	    bu.append(headImageUrl)
	    .append(key)
	    .append(secondRank(memberId))
	    .append(key)
	    .append(thirdRank(memberId))
	    .append(key);

	    return url(bu.toString());
	}
	
	/**
	 * ***********
	  * 创建日期: 2011-9-11
	  * 创建作者：jinbao
	  * @return
	  * 功能：临时图片URL(路径仅到上级目录),例：http://head.kedou.com/head/default/tmp/
	  *************
	 */
	public String tmpUrl(Integer memberId) {
	    StringBuilder bu = new StringBuilder();
	    bu.append(headImageUrl)
	    .append(key)
	    .append(DEFAULT)
	    .append(key)
	    .append(TMP)
	    .append(key);

	    return url(bu.toString());
	}
	
	private static String url(String url) {
	    int index = url.indexOf("http://");
	    if (index != -1)
	    	url = "http://" + url.substring(index + 7).replaceAll("/{2,}", "/");
	    else
	        url = url.replaceAll("/{2,}", "/");

	    return url;
	}

	@SuppressWarnings("unused")
	private String defaultBigUrl(String img) {
	    StringBuilder bu = new StringBuilder();
	    bu.append(headImageUrl)
	    .append(key)
	    .append("default")
	    .append(key)
	    .append("big")
	    .append(key)
	    .append(defaultImg(img));

	    return url(bu.toString());
	}
	
	public String defaultSmallUrl(String img) {
	    StringBuilder bu = new StringBuilder();
	    bu.append(headImageUrl)
	    .append(key)
	    .append("default").append(key)
	    .append(defaultImg(img));

	    return url(bu.toString());
	}
	
	public String defaultImg(String img) {
	    if(StringUtil.isBlank(img))
	    	return "default.jpg";

	    if(img.matches("(ke|dou|default).+"))
	    	return img;

	    return null;
	}
	
	/**
	 * ***********
	  * 创建日期: 2011-9-11
	  * 创建作者：jinbao
	  * @param memberId
	  * @return 
	  * 功能：临时图片URL（路径仅到上级目录），例：http://head.kedou.com/head/default/tmp/
	  *************
	 */
	public String tmpPath(Integer memberId) {
	    StringBuilder bu = new StringBuilder();
	    bu.append(headImagePath)
	    .append(key)
	    .append(DEFAULT)
	    .append(key)
	    .append(TMP)
	    .append(key);
	    return bu.toString();
	}
	
	/**
	 * ***********
	  * 创建日期: 2011-9-11
	  * 创建作者：jinbao
	  * @param memberId
	  * @return 
	  * 功能：大图片URL(路径仅到上级目录),例：http://head.kedou.com/head/1m/1k/
	  *************
	 */
	public String bigPath(Integer memberId) {
	    StringBuilder bu = new StringBuilder();
	    bu.append(headImagePath)
	    .append(key)
	    .append(secondRank(memberId))
	    .append(key)
	    .append(thirdRank(memberId))
	    .append(key);
	    return bu.toString();
	}
	
	public String smallPath(Integer memberId) {
	    StringBuilder bu = new StringBuilder();
	    bu.append(headImagePath)
	    .append(key)
	    .append(secondRank(memberId))
	    .append(key)
	    .append(thirdRank(memberId))
	    .append(key);
	    return bu.toString();
	}
	
	public static String getSmallHeadImageUrl(Member member) {
		UploadFileUtil util = new UploadFileUtil();
		util.setHeadImageUrl(StaticImgTool.getStaticUploadServer() + HeadUrlUtil.key + HeadUrlUtil.M_UPLOAD);
		String img = member.getHeadImg();
	    if (util.defaultImg(img) != null)
	    	return util.defaultSmallUrl(img);

		    return util.smallUrl(member.getMemberId()) + member.getHeadImg() + "?rand=" + Math.random();
	}
	
	public String secondRank(Integer memberId) {
	    return (memberId / 1000000) + "m";
	}
	
	private String thirdRank(Integer memberId) {
	    int quotient = memberId / 1000000;
	    return ((memberId - quotient * 1000000) / 1000) + "K";
	}
	
	/**
	 * ***********
	  * 创建日期: 20112011-8-3下午04:36:36
	  * 创建作者：jinbao
	  * @param param
	  * @return 
	  * 功能：对中文字段进行编码
	  *************
	 */
	public String doUrlEncode(String param){
		try {
			if(null != param)
				return URLDecoder.decode(new String(param.getBytes("ISO-8859-1")), "utf-8");
		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
		}
		return param;
	}

	/**
	 * 保存网络上的头像资源
	 * @param destUrl
	 * @return
	 */
	public static String saveNetHeadImgToPath(Integer memberId,String destUrl){
		//头像存储地址
		UploadFileUtil util = new UploadFileUtil();
		String configDir = RedisContext.getResourceCache().getResourceValue(CacheKeyConstant.ConfigResourcesConstants.TYPE_BASE_CONFIG, CacheKeyConstant.ConfigResourcesConstants.BASE_CONFIG_IMG_DIR);
		util.setHeadImagePath(configDir + HeadUrlUtil.key +HeadUrlUtil.M_UPLOAD);
		String currentDirPath = util.bigPath(memberId) ;
		String currentDirSmallPath = util.smallPath(memberId) ;//小图
		File dir = new File(currentDirPath);
		if(!dir.exists())
			dir.mkdirs();
		String backfileName = UUID.randomUUID().toString()+getExtension(destUrl);
		File pathToSave = new File(currentDirPath, backfileName);

		FileOutputStream fos = null;
		BufferedInputStream bis = null;
		HttpURLConnection httpUrl = null;
		URL url = null;
		int BUFFER_SIZE = 1024;
		byte[] buf = new byte[BUFFER_SIZE];
		int size = 0;
		try {
			url = new URL(destUrl);
			httpUrl = (HttpURLConnection) url.openConnection();
			httpUrl.connect();
			bis = new BufferedInputStream(httpUrl.getInputStream());
			fos = new FileOutputStream(pathToSave);
			while ((size = bis.read(buf)) != -1) {
				fos.write(buf, 0, size);
			}
			fos.flush();
			//拷贝一份进入小图
			File smallPath = new File(currentDirSmallPath,backfileName) ;
			FileUtil.copyData(pathToSave,smallPath) ;
		} catch (Exception e) {
			logger.error("保存网络头像失败",e) ;
		} finally {
			try {
				if(null != fos)
					fos.close();
				if(null != bis)
					bis.close();
				if(null != httpUrl)
					httpUrl.disconnect();
			} catch (IOException e) {
				logger.error("保存网络头像,关闭资源失败") ;
			}
		}
		//返回路径
		return backfileName;
	}

	/**
	 * 获取扩展名的方法
	 */
	protected static String getExtension(String fileName) {
		if(fileName.lastIndexOf(".")<=fileName.lastIndexOf("/")||fileName.lastIndexOf(".")==-1){//防止出现http://wx.qlogo.cn/mmopen/PiajxSqBRaEID1nUCBz22nsYUPZsqiaR4cHgXRa8JHmoS0Z4Ln5gdhDfia4ztwqPm5AjicsS8Jl7SYMknmjFTicocZQ/0图片地址
			return ".jpg" ;//默认.jpg
		}
		return fileName.substring(fileName.lastIndexOf("."));
	}
}
