<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ include file="/common/taglibs.jsp" %>
<!DOCTYPE HTML>
<html>

<head>
	<meta charset="utf-8">
	<title>实名认证 - 顺网通行证</title>
	<script type="text/javascript" src="${staticServer}/scripts/common/ajaxupload.js"></script>
	<style>
		.step_bar li {
			width: 120px;
		}
	</style>
</head>

<body>
	<div class="shop_head">
		<strong>企业实名认证</strong>
		如需在顺网结算中心申请提现，请先通过实名认证。
	</div>
	<ul class="step_bar">
		<li>
			<em>1</em> 认证手机号码</li>
		<li>
			<em>2</em> 填写基础信息</li>
		<li class="current">
			<em>3</em> 上传认证图片</li>
		<li>
			<em>4</em> 确认信息</li>
		<li>
			<em>5</em> 申请完成</li>
	</ul>
	<form id="comfirmPersonalActuForm" action="${appServer}/front/actuality/confirmCompanyActu.htm" method="post">
	<div class="form_group">
		<input type="hidden" value="${tokenId}" value="${tokenId}" name="tokenId" id="tokenId" />
		<input type="hidden" value="${companyActuInfo.companyActuInfoWrapper.memberFrom }"
			   name="companyActuInfo.companyActuInfoWrapper.memberFrom"/>
		<input type="hidden" value="${companyActuInfo.companyActuInfoWrapper.compName }"
			   name="companyActuInfo.companyActuInfoWrapper.compName"/>
		<input type="hidden" value="${companyActuInfo.companyActuInfoWrapper.bussinessEntity }"
			   name="companyActuInfo.companyActuInfoWrapper.bussinessEntity"/>
		<input type="hidden" value="${companyActuInfo.companyActuInfoWrapper.bussinessEntityMobileNo }"
			   name="companyActuInfo.companyActuInfoWrapper.bussinessEntityMobileNo"/>
		<input type="hidden" value="${companyActuInfo.companyActuInfoWrapper.compOrganizeCode }"
			   name="companyActuInfo.companyActuInfoWrapper.compOrganizeCode"/>
		<input type="hidden" value="${companyActuInfo.companyActuInfoWrapper.compLicenceNo }"
			   name="companyActuInfo.companyActuInfoWrapper.compLicenceNo"/>
		<input type="hidden" value="${companyActuInfo.companyActuInfoWrapper.licenceProvinceName }"
			   name="companyActuInfo.companyActuInfoWrapper.licenceProvinceName"/>
		<input type="hidden" value="${companyActuInfo.companyActuInfoWrapper.licenceCityName }"
			   name="companyActuInfo.companyActuInfoWrapper.licenceCityName"/>
		<input type="hidden" value="${companyActuInfo.companyActuInfoWrapper.compFax }"
			   name="companyActuInfo.companyActuInfoWrapper.compFax"/>
		<input type="hidden" value="${companyActuInfo.companyActuInfoWrapper.billType }"
			   name="companyActuInfo.companyActuInfoWrapper.billType"/>
		<input type="hidden" value="${companyActuInfo.companyActuInfoWrapper.revenueCertificateNo }"
			   name="companyActuInfo.companyActuInfoWrapper.revenueCertificateNo"/>
		<input type="hidden" value="${companyActuInfo.companyActuInfoWrapper.revenueCertificateImg }"
			   name="companyActuInfo.companyActuInfoWrapper.revenueCertificateImg"/>
		<input type="hidden" value="${companyActuInfo.companyActuInfoWrapper.compLicenceEndtimeShow }"
			   name="companyActuInfo.companyActuInfoWrapper.compLicenceEndtime"/>
		<input type="hidden" value="${companyActuInfo.companyActuInfoWrapper.bankName }"
			   name="companyActuInfo.companyActuInfoWrapper.bankName"/>
		<input type="hidden" value="${companyActuInfo.companyActuInfoWrapper.bankNo }"
			   name="companyActuInfo.companyActuInfoWrapper.bankNo"/>
		<input name="refuseContext" value="${refuseContext }" type="hidden"/>
		<input type="hidden" value="2" name="flag"/>
		<input type="hidden" name="invoiceCheck" value="${invoiceCheck}" />
		<table cellpadding="0" cellspacing="0">
			<tr>
				<td colspan="3">
					<div class="notice">
						<h3 class="b">图片上传须知：</h3>
						<p>• 证件必须是清晰彩色原件电子稿，可以是扫描件或者数码拍摄照片，不能用复印件。</p>
						<p>• 仅支持JPG、JPEG、BMP的图片格式，大小不超过5M。</p>
					</div>
				</td>
			</tr>
			<tr>
				<th class="w_md">
					<span class="not_null">*</span>营业执照：</th>
				<td style="position: relative; z-index: 0;">
					<input type="hidden" style="width:220px;" class="form_input"
						   name="companyActuInfo.companyActuInfoWrapper.compLicenceImg"
						   value="${companyActuInfo.companyActuInfoWrapper.compLicenceImg }" id="compLicenceImg"/>
					<span class="upload-photos upload_a">
						<ul>
							<li>
								<div class="photo" <c:if test="${not empty companyActuInfo.companyActuInfoWrapper.compLicenceImg }">  style="display: block;"  </c:if>  id="photo_0">
            						<img <c:if test="${empty companyActuInfo.companyActuInfoWrapper.compLicenceImg }"> src="" </c:if> <c:if test="${not empty companyActuInfo.companyActuInfoWrapper.compLicenceImg }"> src="${actualityImgServer}/images/actuality/company/compLicenceImg/${companyActuInfo.companyActuInfoWrapper.compLicenceImg }" </c:if> alt="" id="img_0">
									 <span class="del" id="photo_0Button">
                                        <c:if test="${not empty companyActuInfo.companyActuInfoWrapper.compLicenceImg}">点击修改</c:if>
                                        <c:if test="${empty companyActuInfo.companyActuInfoWrapper.compLicenceImg}">上传成功</c:if>
                                    </span>
								</div>
								<input type="file" class="file" name="" value="" id="up_0">
								<a href="#" class="add-photo">+</a>
							</li>
						</ul>
					</span>
					<span class="form_tip">示例：
							<span class="fancybox-img">
								<img src="${staticServer}/images/company/compLicenceImg/fancy0.jpg">
							</span>
						</span>
					<div class="form_lh">
						<p class="from_tip">正副本均可，扫描件请加盖公司章</p>
						<p class="form_tip" id="compLicenceImgAlert"></p>
					</div>
				</td>
			</tr>
			<tr>
				<td colspan="3">
					<div class="heading">
						<span>授权人 认证图片</span>
					</div>
				</td>
			</tr>
			<tr>
				<th class="w_md">
					<span class="not_null">*</span>身份证正面照：</th>
				<td style="position: relative; z-index: 0;">
					<input type="hidden" name="companyActuInfo.companyActuInfoWrapper.idCardImg1" id="idCardImg1" value="${companyActuInfo.companyActuInfoWrapper.idCardImg1}">
					<span class="upload-photos upload_a">
							<ul>
								<li>
									<div class="photo" <c:if test="${not empty companyActuInfo.companyActuInfoWrapper.idCardImg1}"> style="display: block;"</c:if> id="photo_1">
										<img <c:if test="${empty companyActuInfo.companyActuInfoWrapper.idCardImg1}"> src="" </c:if> <c:if test="${not empty companyActuInfo.companyActuInfoWrapper.idCardImg1}"> src="${actualityImgServer}/images/actuality/company/idcardFront/${companyActuInfo.companyActuInfoWrapper.idCardImg1}" </c:if> alt="" id="img_1">
										<span class="del">
                                            <c:if test="${not empty companyActuInfo.companyActuInfoWrapper.idCardImg1}">点击修改</c:if>
                                        </span>
									</div>
									<input type="file" class="file" name="" value="" id="up_1">
									<a href="#" class="add-photo">+</a>
								</li>
							</ul>
						</span>
					<span class="form_tip">示例：
							<span class="fancybox-img">
								<img src="${staticServer}/images/cafe/fancy1.jpg">
							</span>
						</span>
					<div class="form_lh">
						<p class="form_tip">身份证照保证四角完整，内容清晰可见，无反光</p>
						<p class="form_error" id="idCardImg1Error"></p>
					</div>
				</td>
			</tr>
			<tr>
				<th>
					<span class="not_null">*</span>身份证背面照：
				</th>
				<td style="position: relative; z-index: 0;">
					<input type="hidden" name="companyActuInfo.companyActuInfoWrapper.idCardImg2" id="idCardImg2" value="${companyActuInfo.companyActuInfoWrapper.idCardImg2}">
					<span class="upload-photos upload_a">
							<ul>
								<li>
									<div class="photo" <c:if test="${not empty companyActuInfo.companyActuInfoWrapper.idCardImg2}"> style="display: block;"</c:if> id="photo_2">
										<img <c:if test="${empty companyActuInfo.companyActuInfoWrapper.idCardImg2}"> src="" </c:if> <c:if test="${not empty companyActuInfo.companyActuInfoWrapper.idCardImg2}"> src="${actualityImgServer}/images/actuality/company/idcardBack/${companyActuInfo.companyActuInfoWrapper.idCardImg2}" </c:if> alt="" id="img_2">
										<span class="del">
                                            <c:if test="${not empty companyActuInfo.companyActuInfoWrapper.idCardImg2}">点击修改</c:if>
                                        </span>
									</div>
									<input type="file" class="file" name="" value="" id="up_2">
									<a href="#" class="add-photo">+</a>
								</li>
							</ul>
						</span>
					<span class="form_tip">示例：
							<span class="fancybox-img">
								<img src="${staticServer}/images/cafe/fancy2.jpg">
							</span>
						</span>
					<div class="form_lh">
						<p class="form_tip">身份证照保证四角完整，内容清晰可见，无反光</p>
						<p class="form_error" id="idCardImg2Error"></p>
					</div>
				</td>
			</tr>
			<tr>
				<th>
					<span class="not_null">*</span>手持证件照：
				</th>
				<td style="position: relative; z-index: 0;">
					<input type="hidden" name="companyActuInfo.companyActuInfoWrapper.idCardImg3" id="idCardImg3" value="${companyActuInfo.companyActuInfoWrapper.idCardImg3}">
					<span class="upload-photos upload_a">
							<ul>
								<li>
									<div class="photo" <c:if test="${not empty companyActuInfo.companyActuInfoWrapper.idCardImg3}"> style="display: block;"</c:if> id="photo_3">
										<img <c:if test="${empty companyActuInfo.companyActuInfoWrapper.idCardImg3}"> src="" </c:if> <c:if test="${not empty companyActuInfo.companyActuInfoWrapper.idCardImg2}"> src="${actualityImgServer}/images/actuality/company/idcardHead/${companyActuInfo.companyActuInfoWrapper.idCardImg3}" </c:if> alt="" id="img_3">
										<span class="del">
                                            <c:if test="${not empty companyActuInfo.companyActuInfoWrapper.idCardImg3}">点击修改</c:if>
                                        </span>
									</div>
									<input type="file" class="file" name="" value="" id="up_3">
									<a href="#" class="add-photo">+</a>
								</li>
							</ul>
						</span>
					<span class="form_tip">示例：
							<span class="fancybox-img">
								<img src="${staticServer}/images/cafe/fancy3.png">
							</span>
						</span>
					<div class="form_lh">
						<p class="form_tip">手持身份证正面，身份证保证信息清晰</p>
						<p class="form_error" id="idCardImg3Error"></p>
					</div>
				</td>
			</tr>
			<tr>
				<th></th>
				<td>
					<p>
						<input name="checkbox" type="checkbox" value="" id="checkBox" checked="checked"> 我已阅读并同意
						<a class="a035" href="javascript:void(0)" id="xieyiBtn">《顺网通行证实名认证服务协议》</a>
						<span style="color: red;" id="checkBoxError"></span>
					</p>
				</td>
			</tr>
			<tr>
				<th></th>
				<td>
					<span style="color: red;" id="errorMsg">${errorMsg}</span>
				</td>
			</tr>
			<tr>
				<th></th>
				<td>
					<a href="#" id="j-submit" class="btn_default_lg">提交审核</a>
				</td>
			</tr>
		</table>
	</div>
	</form>
	<!-- 协议 start -->
	<div role="dialog" aria-hidden="true" class="modal hide fade" id="xieyiWin" >
		<div class="win_xieyi">
			<div class="title">顺网通行证实名认证服务协议</div>
			<span  data-dismiss="modal" aria-hidden="true" class="xieyi_close" title="关闭"></span>
			<div class="detail">
				<p>为了保障您的权益，您确认已于开始“顺网通行证实名认证”前，具体阅读了本服务协议所有内容。当您开始申请使用“顺网通行证实名认证”时，即表示您已阅读、了解并同意接受本服务协议之所有内容。</p>
				<p>为了提高交易的安全性和用户身份的可信度，从2010年11月1日开始，杭州顺网科技股份有限公司（以下简称“顺网科技”）推出“顺网通行证实名认证”服务，您进行顺网通行证实名认证前必须先注册成为顺网的会员。顺网科技有权采取各种其认为必要手段对用户的身份进行识别。但是，作为普通的网络服务提供商，顺网科技所能采取的方法有限，而且在网络上进行用户身份识别也存在一定的困难，因此，顺网科技用户身份的准确性和绝对真实性不做任何保证。</p>
				<p>顺网通行证实名认证服务的具体程序包括： <br />
					1、身份信息识别 <br />
					2、银行账户识别 </p>
				<p>通过这两种识别手段，顺网科技有权记录并保存您的通过认证的信息，亦有权根据本协议的约定向您或第三方提供您是否通过“顺网通行证实名认证”的结论以及您的身份信息。</p>
				<p><strong>一、关于“顺网通行证实名认证”服务的理解与认同</strong><br />
					1、“顺网通行证实名认证”服务是由顺网科技提供的一项身份识别服务。 <br />
					2、顺网科技有权单方随时修改或变更本服务协议之内容，并随时通过顺网网站公布最新之约定事项，不另作个別通知。若您于任何修改或变更本服务条款后继续使用本服务，则视为您已阅读、了解并同意接受修改或变更，若您不同意，则您应立即停用本服务。</p>
				<p><strong>二、身份信息识别须知</strong><br />
					1、中华人民共和国大陆地区个人会员可提供以下证件用于顺网通行证实名认证：身份证、护照等明确标有身份证号的证件之一。（选择在线上传证件，必须是原件扫描件，或者数码拍摄件，第二代身份证（IC版）需要同时提交正反两面）、户籍证明开具时间至今在三个月内有效，有效期3个月内的证件不予受理。)<br />
					2、台湾地区及海外的个人会员请提供：回乡证/护照 大陆担保人有效证件 担保人亲笔签名的担保函（有效期3个月内的证件不予受理)，需要同时提交有公章页。目前暂时不能为香港、澳门地区的个人会员提供顺网通行证实名认证服务？？？。<br />
					3、根据中国法律规定不具有完全民事行为能力的个人，不提供顺网通行证实名认证服务。<br />
					4、非个人的其他会员申请商家会员认证服务的，应向顺网科技提供如下认证所需资料：中华人民共和国工商行政机构颁发的营业执照或中华人民共和国组织机构代码证等顺网科技认可的有效证件、提交申请人的身份证件（IC版的身份证需要同时提交正反两面，提交申请人如非法定代表人，必须提供该单位的委托授权书），有效期3个月内的证件不予受理；<br />
					5、通过顺网身份信息核实的会员不能自行修改其认证信息中的商号、姓名以及身份证件号码等身份信息。<br />
					大陆个人认证的有效期与其提供的身份证件有效期（非长期）一致，户籍证明从审核当日开始起计算，有效期一年。若提供的证件长期有效，则认证有效期为20年。海外会员的有效期按证件有效期和担保日期最少的有效期计算。商户认证的有效期一般为一年，若营业执照有效期小于一年则以营业执照有效期为准。</p>
				<p><strong>三、银行账户识别须知</strong><br />
					1、所有的个人会员进行顺网通行证实名认证都必须提供本人的中国大陆境内银行开设的人民币账户信息，包括：账号，开户名，开户银行。<br />
					2、所有的非个人的其他会员必须提供该单位在中国大陆境内银行开设的人民币账号，开户名，开户银行。<br />
					3、请严格按照顺网科技提供的方式申请银行账户核实，您填写的银行账户的开户名必须与身份信息中的真实姓名或营业执照、组织机构代码证中的会员名称完全一致，所有经您确认的键入的资料将引用为认证资料。<br />
					4、假如您不满十八周岁（已满十六周岁未满十八周岁、并以自己的劳动为主要生活来源的除外），因您尚不具备完全民事行为能力，请您暂时不要申请银行账户核实。假如您以提供不实认证材料的方法，使顺网科技误认为您是完全民事行为能力者而受理您的银行账户核实申请，则因此所发生的一切后果将由您及您的法定监护人承担，顺网科技不承担任何责任。</p>
				<p><strong>四、授权声明</strong><br />
					1、身份认证信息共享：<br />
					为了使您享有便捷的服务，您经由其它网站向顺网提交顺网通行证实名认证申请即表示您同意顺网科技为您核对您向顺网提交的全部身份信息和银行账户信息，并同意顺网科技将您是否通过顺网通行证实名认证的结果及您的身份信息提供给该网站。<br />
					2、认证资料的治理：<br />
					您提交给顺网的认证资料，顺网科技有权保留。顺网科技保证不会公开、编辑或透露您顺网通行证实名认证的核实资料及保存在顺网科技的非公开内容用于商业目的，但根据前款规定以及本款所列的以下情况除外：<br />
					1) 会员用户授权顺网科技透露其相关信息；<br />
					2) 根据有关法律规定，顺网科技有法律上的责任向国家司法及行政机关提供；<br />
					3) 第三方和顺网科技一起为网站和会员提供服务时，第三方向您提供服务所必须的相关信息。<br />
					4) 基于解决您与第三人的实际纠纷需要，顺网科技可以向第三人提供您的身份信息以帮助第三人维护其合法权益。</p>
				<p><strong>五、第三方网站的链接</strong><br />
					1、银行账户核实过程中，顺网结算平台(pay.kedou.com)可能包括指向第三方网站（如网上银行网站）的链接（以下简称“链接网站”）。”链接网站”非由顺网科技所控制，对于任何”链接网站”的内容，包含但不限于“链接网站”内含的任何链接，或”链接网站”的任何改变或更新，顺网科技均不予负责。自“链接网站”接收的网络传播或其它形式之传送，顺网科技不予负责。</p>
				<p><strong>六、不得为非法或禁止的使用</strong><br />
					1、接受本协议全部的说明、条款、条件和须知是您申请“银行账户核实”的先决条件。作为其中的一个重要内容，您必须向顺网科技保证，您不得为任何非法或为本规定、条件及须知所禁止之目的进行银行账户核实申请。您不得以任何可能损害、使瘫痪、使过度负荷或损害其他网站或其他网站的服务或顺网科技或干扰他人对于顺网通行证实名认证申请的使用等方式使用顺网通行证实名认证服务。您不得经由非顺网科技预定提供的任何方式取得或试图取得任何资料或信息。</p>
				<p><strong>七、有关免责</strong><br />
					下列情况时，顺网科技无需承担任何责任：<br />
					1、由于您将用户密码告知他人或未保管好自己的密码或与他人共享注册账户或任何其他非顺网科技的过错，由此导致的任何个人资料泄露。<br />
					2、任何由于黑客攻击、计算机病毒侵入或发作、电信部门技术调整导致之影响、因政府管制而造成的暂时性关闭、由于第三方原因(包括不可抗力，例如国际出口的主干线路及国际出口电信提供商一方出现故障、火灾、水灾、雷击、地震、洪水、台风、龙卷风、火山爆发、瘟疫和传染病流行、罢工、战争或暴力行为或类似事件等)及其他非因顺网科技过错而造成的认证信息泄露、丢失、被盗用或被篡改等。<br />
					3、由于与顺网科技链接的其它网站（如网上银行等）所造成的银行账户信息泄露及由此而导致的任何法律争议和后果。<br />
					4、任何用户，包括未成年人用户向顺网科技提供错误、不完整、不实信息等造成不能通过顺网通行证实名认证或遭受任何其他损失，概与顺网科技无关。</p>
				<p><strong>八、 顺网通行证实名认证服务协议的适用</strong><br />
					顺网通行证实名认证服务协议的名词解释，意思理解，可互相引用参照，如有不同理解，应以本协议为准，并且您同意并接受基于善意，本公司对相关名词、条款做出进一步的解释，并以最新的解释为准。<br />
					本服务协议适用中华人民共和国法律，如与本协议有关的某一特定事项缺乏明确法律规定，则应参照通用的国际商业惯例和（或）行业惯例。<br />
					假如本服务协议条款中的部分条款被有管辖权的法院认定为违法，那么这些条款并不影响其他条款的有效性并将应用其他有效条款按最接近双方意图的可能而推定。</p>
				<p><strong>九、管辖</strong><br />
					本服务条款之解释与适用，以及与本服务条款有关的争议，均应依照中华人民共和国法律予以处理，并以浙江省杭州市西湖区人民法院为第一审管辖法院。</p>
			</div>
		</div>
	</div>
	<!-- footer end -->
	<div class="fancybox-wrap">
		<div class="mask"></div>
		<div class="fancybox">
			<a aria-hidden="true" title="关闭" class="close">&times;</a>
			<div class="fancybox-bd"></div>
		</div>
	</div>
	<!-- 提示 -->
	<div id="msg-modal" tabindex="-1" role="dialog" aria-hidden="true" class="modal hide fade alert">
		<div class="modal-dialog">
			<i class="tip_loading"></i>
			证件识别校验中...
		</div>
	</div>
	<script src="${staticServer}/scripts/front/base.js"></script>
	<script src="${staticServer}/scripts/common/bootstrap-modal.js"></script>
	<script type="text/javascript">
		$('.sidebar .menu li:first').addClass('current');
	</script>
	<script type="text/javascript">
		//微信浮动层
		floatLayer.init();

		new AjaxUpload('#up_0', {
			action: $CONFIG.appServer + '/front/upload/ajaxUploadNetFile.htm?type=compLicence',
			name: 'file',
			responseType: 'json',
			onSubmit: function (file, ext) {
				if($('#photo_0Button').html()=="点击修改" || $('#photo_0Button').html()=="上传失败"){
					$('#photo_0Button').html("上传中");
				}else {
					$("#photo_0").show().css("height","0px") ;
					$('#photo_0Button').html("上传中").css("color", "black").css("background", "none");
				}
				if (ext && /^(jpg|jpeg|bmp|png)$/.test(ext.toLowerCase())) {
					this.setData({
						'file': file
					});
				} else {
					alert("请上传格式为 jpg|jpeg|bmp|png 的文件！");
					return false;
				}
			},
			onComplete: function (file, response) {
				if(typeof(response.suc)=="undefined") {
					$('#photo_0Button').html("上传失败");
					alert("上传文件大小不能超过5M!");
					return false;
				}
				if (response.suc == false) {
					showInfo("compLicenceImgAlert", response.error);
					return;
				} else {
					var url = '${actualityImgServer}/images/actuality/company/compLicenceImg/' + response.fileName;
					$("#img_0").attr('src',url);
					$("#photo_0").show();
					$('#photo_0Button').html("点击修改").css("color","white").css("background","rgba(0,0,0,0.4)");
					$("#compLicenceImg").val(response.fileName);
				}
			}
		});

		// 上传身份证正面照
		new AjaxUpload('#up_1', {
			action: $CONFIG.appServer + '/front/upload/ajaxUploadNetFile.htm?type=compFront',
			name: 'file',
			responseType: 'json',
			onSubmit: function (file, ext) {
				if($('#photo_1').find(".del").html()=="点击修改" || $('#photo_1').find(".del").html()=="上传失败"){
					$('#photo_1').find(".del").html("上传中");
				}else {
					$("#photo_1").show().css("height","0px") ;
					$('#photo_1').find(".del").html("上传中").css("color", "black").css("background", "none");
				}
				if (ext && /^(jpg|jpeg|bmp|png)$/.test(ext.toLowerCase())) {
					this.setData({
						'file': file
					});
				} else {
					alert("请上传格式为 jpg|jpeg|bmp|png 的文件！");
					return false;
				}
			},
			onComplete: function (file, response) {
				if(typeof(response.suc)=="undefined") {
					$('#photo_1').find(".del").html("上传失败");
					alert("上传文件大小不能超过5M!");
					return false;
				}
				if (response.suc == false) {
					alert(response.error)
					return;
				} else {
					var url = '${actualityImgServer}/images/actuality/company/idcardFront/' + response.fileName;
					$("#photo_1").show();
					$('#photo_1').find(".del").html("点击修改").css("color","white").css("background","rgba(0,0,0,0.4)");
					$("#img_1").attr('src',url);
					$("#idCardImg1").val(response.fileName);
					$("#idCardImg1Error").html("");
				}
			}
		});

		// 上传身份证背面
		new AjaxUpload('#up_2', {
			action: $CONFIG.appServer + '/front/upload/ajaxUploadNetFile.htm?type=compBack',
			name: 'file',
			responseType: 'json',
			onSubmit: function (file, ext) {
				if($('#photo_2').find(".del").html()=="点击修改" || $('#photo_2').find(".del").html()=="上传失败"){
					$('#photo_2').find(".del").html("上传中");
				}else {
					$("#photo_2").show().css("height","0px") ;
					$('#photo_2').find(".del").html("上传中").css("color", "black").css("background", "none");
				}
				if (ext && /^(jpg|jpeg|bmp|png)$/.test(ext.toLowerCase())) {
					this.setData({
						'file': file
					});
				} else {
					alert("请上传格式为 jpg|jpeg|bmp|png 的文件！");
					return false;
				}
			},
			onComplete: function (file, response) {
				if(typeof(response.suc)=="undefined") {
					$('#photo_2').find(".del").html("上传失败");
					alert("上传文件大小不能超过5M!");
					return false;
				}
				if (response.suc == false) {
					alert(response.error)
					return;
				} else {
					var url = '${actualityImgServer}/images/actuality/company/idcardBack/' + response.fileName;
					$("#photo_2").show();
					$('#photo_2').find(".del").html("点击修改").css("color","white").css("background","rgba(0,0,0,0.4)");
					$("#img_2").attr('src',url);
					$("#idCardImg2").val(response.fileName);
					$("#idCardImg2Error").html("");
				}
			}
		});

		// 上传手持身份证
		new AjaxUpload('#up_3', {
			action: $CONFIG.appServer + '/front/upload/ajaxUploadNetFile.htm?type=compHead',
			name: 'file',
			responseType: 'json',
			onSubmit: function (file, ext) {
				if($('#photo_3').find(".del").html()=="点击修改" || $('#photo_2').find(".del").html()=="上传失败"){
					$('#photo_3').find(".del").html("上传中");
				}else {
					$("#photo_3").show().css("height","0px") ;
					$('#photo_3').find(".del").html("上传中").css("color", "black").css("background", "none");
				}
				if (ext && /^(jpg|jpeg|bmp|png)$/.test(ext.toLowerCase())) {
					this.setData({
						'file': file
					});
				} else {
					alert("请上传格式为 jpg|jpeg|bmp|png 的文件！");
					return false;
				}
			},
			onComplete: function (file, response) {
				if(typeof(response.suc)=="undefined") {
					$('#photo_3').find(".del").html("上传失败");
					alert("上传文件大小不能超过5M!");
					return false;
				}
				if (response.suc == false) {
					alert(response.error)
					return;
				} else {
					var url = '${actualityImgServer}/images/actuality/company/idcardHead/' + response.fileName;
					$("#photo_3").show();
					$('#photo_3').find(".del").html("点击修改").css("color","white").css("background","rgba(0,0,0,0.4)");
					$("#img_3").attr('src',url);
					$("#idCardImg3").val(response.fileName);
					$("#idCardImg3Error").html("");
				}
			}
		});

		$("#xieyiBtn").bind("click",function(){
			$('#xieyiWin').modal('show');
			return false;
		});

		// 证件识别校验中...
		$('#j-submit').on('click', function (e) {
			$("#errorMsg").html("") ;
			if(!$("#checkBox").is(":checked")){
				$("#checkBoxError").html("请阅读并同意实名认证服务协议") ;
				return false ;
			}else{
				$("#checkBoxError").html("") ;
			}
			// 检查图是否上传
			var compLicenceImg = $.trim($("#compLicenceImg").val()) ;
			if(compLicenceImg == ''){
				showInfo("compLicenceImgAlert","请上传营业执照") ;
				return false ;
			}else{
				$("#compLicenceImgAlert").html("") ;
			}
			var idcardImg1 = $.trim($("#idCardImg1").val()) ;
			if(idcardImg1 == ''){
				showInfo("idCardImg1Error","请上传身份证正面照") ;
				return false ;
			}else{
				$("#idCardImg1Error").html("");
			}
			var idcardImg2 = $.trim($("#idCardImg2").val()) ;
			if(idcardImg2 == ''){
				showInfo("idCardImg2Error","请上传身份证背面照") ;
				return false ;
			}else{
				$("#idCardImg2Error").html("");
			}
			var idcardImg3 = $.trim($("#idCardImg3").val()) ;
			if(idcardImg3 == ''){
				showInfo("idCardImg3Error","请上传手持身份证照片") ;
				return false ;
			}else{
				$("#idCardImg3Error").html("");
			}
			var tokenId = $("#tokenId").val() ;
			//开始识别身份证
			e.preventDefault();
			$("#msg-modal").modal("show");
			$.ajax({
				url:$CONFIG.appServer + '/front/actuality/actuIdCardOcr.htm',
				type:"post",
				dataType:"json",
				data:{"idCardImg1":idcardImg1,"idCardImg2":idcardImg2,"tokenId":tokenId,"flag":2},
				success:function(json){
					$('#msg-modal').modal('hide');
					if(json.errorMsg){
						$("#errorMsg").html(json.errorMsg) ;
					}else{
						$("#comfirmPersonalActuForm").submit() ;
					}
				},
				complete:function(resp){
					$('#msg-modal').modal('hide');
					if(resp.responseText){
						var url = eval('(' + resp.responseText + ')').url ;
						if(url){
							window.location.href= url ;
						}
					}
				}
			})
		});

		// 显示示例
		$('.fancybox-img').on('click', function () {
			var $this = $(this),
					_src = $this.find('img').attr('src'),
					$fancyboxWrap = $('.fancybox-wrap');

			$fancyboxWrap.find('.fancybox-bd').html('<img src=' + _src + '>').end().show();
		});
		$('.fancybox-wrap').on('click', '.close', function () {
			var $this = $(this),
					$parent = $('.fancybox-wrap');
			$parent.hide();
		})

		function showInfo(showInfo, msg) {
			if (msg == "") {
				$("#" + showInfo).html("");
				return;
			}

			$("#" + showInfo).css("color", "red");
			$("#" + showInfo).removeClass("form_tip");
			$("#" + showInfo).addClass("col_red");
			$("#" + showInfo).html("<img src=${staticServer}/images/front/error.gif>" + msg);
		}
	</script>
</body>

</html>