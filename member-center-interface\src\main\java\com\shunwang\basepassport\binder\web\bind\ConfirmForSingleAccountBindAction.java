package com.shunwang.basepassport.binder.web.bind;

import com.shunwang.baseStone.cache.CacheKeyConstant;
import com.shunwang.baseStone.config.constants.ConfigOauthConstant;
import com.shunwang.baseStone.context.RedisContext;
import com.shunwang.baseStone.core.exception.BaseStoneException;
import com.shunwang.basepassport.binder.common.BinderConstants;
import com.shunwang.basepassport.binder.common.ErrorCode;
import com.shunwang.basepassport.binder.exception.WrongAccountTypeExp;
import com.shunwang.basepassport.binder.exception.WrongBindTypeExp;
import com.shunwang.basepassport.binder.pojo.SendBinder;
import com.shunwang.basepassport.commonExp.ParamNotFoundExp;
import com.shunwang.basepassport.user.common.UserCheckUtil;
import com.shunwang.basepassport.user.dao.MemberAccountBindDao;
import com.shunwang.basepassport.user.dao.MemberOutSiteDao;
import com.shunwang.basepassport.user.exception.MemberNotFoundByOutUserExp;
import com.shunwang.basepassport.user.pojo.Member;
import com.shunwang.basepassport.user.pojo.MemberAccountBind;
import com.shunwang.basepassport.user.pojo.MemberOutSite;
import com.shunwang.basepassport.user.pojo.SingleAccountToken;
import com.shunwang.util.lang.StringUtil;

/**
 * 5：单账号绑定手机
 *
 * <AUTHOR>
 * @date 2018/12/15
 **/
public class ConfirmForSingleAccountBindAction extends MobileBindBaseAction {

    private MemberAccountBindDao memberAccountBindDao;

    private MemberOutSiteDao memberOutSiteDao;

    @Override
    public void checkParam() {
        setBindType(BinderConstants.ConfirmConstants.ConfirmTypes.TYPE_SINGLE_ACCOUNT_BIND);
        super.checkParam();
        if (StringUtil.isBlank(getSingleAccountToken())) {
            throw new ParamNotFoundExp("singleAccountToken");
        }
        if (StringUtil.isBlank(getMobile())) {
            throw new ParamNotFoundExp("mobile");
        }
    }

    @Override
    protected SendBinder buildSendBinder() {
        SendBinder sendBinder = null;
        if (!bussiness.isSingleAccount()) {
            throw new WrongAccountTypeExp();
        } else {
            //单账号
            sendBinder = processSingleAccount();
        }

        return sendBinder;
    }


    /**
     * 单账号处理逻辑
     * 先查关联表
     * <p>
     * <p>
     * 普通账号:mobile相同或表里为空,绑定为登录账号
     * 不相同报错
     * 三方账号:mobile被设置为登录账号,关联表
     * 未被设置为登录账号
     */
    protected SendBinder processSingleAccount() {
        String key = CacheKeyConstant.InterfaceToken.SINGLE_ACCOUNT_SIGN + getSingleAccountToken();
        SingleAccountToken signToken = RedisContext.getRedisCache().get(key, SingleAccountToken.class);

        if (signToken == null) {
            throw new BaseStoneException(ErrorCode.C_1081.getCode(), ErrorCode.C_1081.getDescription());
        }

        SendBinder sendBinder = null;
        MemberAccountBind memberAccountBind = memberAccountBindDao.getByMobile(getMobile());
        if (memberAccountBind != null) {
            Integer memberId = memberAccountBind.getMemberId();
            //对应信息不相同抛异常
            if (ConfigOauthConstant.TYPE.NORMAL.getInt().equals(signToken.getType())) {
                if (!memberId.equals(signToken.getUnionId())) { //账号关联表的手机号码必定被其他账号设为登录账号了
                    throw new BaseStoneException(ErrorCode.C_1025.getCode(), ErrorCode.C_1025.getDescription());
                }
            } else if (ConfigOauthConstant.TYPE.QQ.getInt().equals(signToken.getType())) {
                if (memberAccountBind.getIsBindQq()) {
                    if (!memberAccountBind.getQq().equals(signToken.getUnionId())) {
                        throw new BaseStoneException(ErrorCode.C_1077.getCode(), ErrorCode.C_1077.getDescription() + "QQ");
                    }
                }
            } else if (ConfigOauthConstant.TYPE.WEIXIN.getInt().equals(signToken.getType())) {
                if (memberAccountBind.getIsBindWeixin()) {
                    if (!memberAccountBind.getWeixin().equals(signToken.getUnionId())) {
                        throw new BaseStoneException(ErrorCode.C_1077.getCode(), ErrorCode.C_1077.getDescription() + "微信");
                    }
                }
            } else if (ConfigOauthConstant.TYPE.WEIBO.getInt().equals(signToken.getType())) {
                if (memberAccountBind.getIsBindWeibo()) {
                    if (!memberAccountBind.getWeibo().equals(signToken.getUnionId())) {
                        throw new BaseStoneException(ErrorCode.C_1077.getCode(), ErrorCode.C_1077.getDescription() + "微博");
                    }
                }
            } else if (ConfigOauthConstant.TYPE.APPLE.getInt().equals(signToken.getType())) {
                if (memberAccountBind.getIsBindApple()) {
                    if (!memberAccountBind.getApple().equals(signToken.getUnionId())) {
                        throw new BaseStoneException(ErrorCode.C_1077.getCode(), ErrorCode.C_1077.getDescription() + "APPLE");
                    }
                }
            } else if (ConfigOauthConstant.TYPE.ALIPAY.getInt().equals(signToken.getType())) {
                if (memberAccountBind.getIsBindAlipay()) {
                    if (!memberAccountBind.getAlipay().equals(signToken.getUnionId())) {
                        throw new BaseStoneException(ErrorCode.C_1077.getCode(), ErrorCode.C_1077.getDescription() + "ALIPAY");
                    }
                }
            } else {
                //无额外信息或额外信息错误,不应该执行到这里
                throw new WrongBindTypeExp();
            }
            // 发短信必定也是这个账号发的
            Member member = memberDao.getByMemberId(memberId);

            sendBinder = getSendBinderFromCache(member);
            if (sendBinder == null) {
                sendBinder = (SendBinder) getBinderDao().getById(memberId);
            }
            sendBinder.setMember(member);
            sendBinder.setBusinessType(BinderConstants.MOBLIE_BIND_TABLE);
            return sendBinder;
        }

        //关联表中不存在记录
        if (ConfigOauthConstant.TYPE.NORMAL.getInt().equals(signToken.getType())) {
            Member member = memberDao.getByMemberId(signToken.getUnionId());
            if (member == null) { //不出意外肯定有
                throw new MemberNotFoundByOutUserExp(getMobile());
            }
            if (member.getIsBindMobile() && !member.getMobile().equals(getMobile())) { //绑定手机且不是这个手机
                throw new BaseStoneException(ErrorCode.C_1082.getCode(), ErrorCode.C_1082.getDescription());
            } else if (member.getMobileAsLoginAccount() && !member.getMobile().equals(getMobile())) {
                //该账号已经绑定了其他手机号码作为登录账号，一般逻辑走不到这里
                throw new BaseStoneException(ErrorCode.C_1083.getCode(), ErrorCode.C_1083.getDescription());
            }
            // 这个手机号码能拿到账号
            Member phoneMember = memberDao.getByMobile(getNumber());
            if (null != phoneMember && !phoneMember.getMemberId().equals(signToken.getUnionId())) {
                throw new BaseStoneException(ErrorCode.C_1025.getCode(), ErrorCode.C_1025.getDescription());
            }
            sendBinder = (SendBinder) getBinderDao().getById(member.getMemberId());
            sendBinder.setMember(member);
            //如果账号未绑定手机号或已经绑定或已经设为登录账号且手机号码一致
            if (member.getMobileAsLoginAccount()) {
                sendBinder.setBusinessType(BinderConstants.MOBLIE_BIND_TABLE);
            } else {
                sendBinder.setBusinessType(BinderConstants.MOBLIE_BIND_AS_LOGIN);
            }

        } else if (ConfigOauthConstant.TYPE.QQ.getInt().equals(signToken.getType())
                || ConfigOauthConstant.TYPE.WEIXIN.getInt().equals(signToken.getType())
                || ConfigOauthConstant.TYPE.WEIBO.getInt().equals(signToken.getType())
                || ConfigOauthConstant.TYPE.APPLE.getInt().equals(signToken.getType())
                || ConfigOauthConstant.TYPE.ALIPAY.getInt().equals(signToken.getType())) {
            MemberAccountBind oldAccountBind = null;
            if (ConfigOauthConstant.TYPE.QQ.getInt().equals(signToken.getType())) {
                oldAccountBind = memberAccountBindDao.getByQq(signToken.getUnionId());
            } else if (ConfigOauthConstant.TYPE.WEIBO.getInt().equals(signToken.getType())) {
                oldAccountBind = memberAccountBindDao.getByWeibo(signToken.getUnionId());
            } else if (ConfigOauthConstant.TYPE.WEIXIN.getInt().equals(signToken.getType())) {
                oldAccountBind = memberAccountBindDao.getByWeixin(signToken.getUnionId());
            } else if (ConfigOauthConstant.TYPE.APPLE.getInt().equals(signToken.getType())) {
                oldAccountBind = memberAccountBindDao.getByApple(signToken.getUnionId());
            } else if (ConfigOauthConstant.TYPE.ALIPAY.getInt().equals(signToken.getType())) {
                oldAccountBind = memberAccountBindDao.getByAlipay(signToken.getUnionId());
            }
            if (null != oldAccountBind) {
                if (!oldAccountBind.getPhone().equals(getNumber())) {
                    throw new BaseStoneException(ErrorCode.C_1091.getCode(), ErrorCode.C_1091.getDescription());
                }
            }
            //三方账号的情况
            Member member = memberDao.getByMobile(getNumber());
            if (member != null) {
                //手机号已经设置成登录号
                UserCheckUtil.checkMemberState(member.getMemberState(), null);
                setMember(member);
                sendBinder = (SendBinder) getBinderDao().getById(member.getMemberId());
                sendBinder.setMember(member);
                sendBinder.setMemberName((member.getMemberName()));
                //关联表逻辑
                sendBinder.setBusinessType(BinderConstants.MOBLIE_BIND_TABLE);
            } else {
                //未设置成登录号
                int cnt = memberDao.getCntByMobile(getNumber());
                if (cnt == 1) {
                    sendBinder = (SendBinder) getBinderDao().getRegitsterBinderByNumber(getNumber());
                    sendBinder.setBusinessType(BinderConstants.MOBLIE_CHOOSE_ACCOUNT_TOBIND);
                } else {
                    sendBinder = adjustBindNum();
                }
            }
        } else {
            //无额外信息或额外信息错误,不应该执行到这里
            throw new WrongBindTypeExp();
        }
        return sendBinder;
    }

    public MemberAccountBindDao getMemberAccountBindDao() {
        return memberAccountBindDao;
    }

    public void setMemberAccountBindDao(MemberAccountBindDao memberAccountBindDao) {
        this.memberAccountBindDao = memberAccountBindDao;
    }

    public MemberOutSiteDao getMemberOutSiteDao() {
        return memberOutSiteDao;
    }

    public void setMemberOutSiteDao(MemberOutSiteDao memberOutSiteDao) {
        this.memberOutSiteDao = memberOutSiteDao;
    }
}
