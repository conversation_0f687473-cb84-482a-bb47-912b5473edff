package com.shunwang.basepassport.config.pojo;

import com.shunwang.baseStone.context.BaseStoneContext;
import com.shunwang.baseStone.core.pojo.BaseStoneObject;
import com.shunwang.baseStone.useroutinterface.constant.UseroutConstant;
import com.shunwang.baseStone.useroutinterface.dao.UseroutInterfaceDao;
import com.shunwang.basepassport.config.constants.ConfigEnum;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

/**
 * 外部用户接入接口
 */
public class UserOutInterface extends BaseStoneObject {

    private static final long serialVersionUID = 1L;
    private Integer interfaceId;
    private String outServiceId;
    private String serviceUrl;
    private String callbackUrl;
    private String serviceKey;
    private String serviceState;  //0、正常 1、关闭
    private String permitIp;
    private Date timeAdd;
    private String userAdd;
    private Date timeEdit;
    private String userEdit;
    private String remark;
    private String prefixName;
    private String serviceProvider;
    private String serviceImg;
    private String serviceBigImg;
    private Integer dirId;
    private Integer orderBy;
    private Integer autoLogin;

    public Integer getInterfaceId() {
        return interfaceId;
    }

    public void setInterfaceId(Integer interfaceId) {
        this.interfaceId = interfaceId;
    }

    public String getOutServiceId() {
        return outServiceId;
    }

    public void setOutServiceId(String outServiceId) {
        this.outServiceId = outServiceId;
    }

    public String getServiceUrl() {
        return serviceUrl;
    }

    public void setServiceUrl(String serviceUrl) {
        this.serviceUrl = serviceUrl;
    }

    public String getCallbackUrl() {
        return callbackUrl;
    }

    public void setCallbackUrl(String callbackUrl) {
        this.callbackUrl = callbackUrl;
    }

    public String getServiceKey() {
        return serviceKey;
    }

    public void setServiceKey(String serviceKey) {
        this.serviceKey = serviceKey;
    }

    public String getServiceState() {
        return serviceState;
    }

    public void setServiceState(String serviceState) {
        this.serviceState = serviceState;
    }

    public String getPermitIp() {
        return permitIp;
    }

    public void setPermitIp(String permitIp) {
        this.permitIp = permitIp;
    }

    public Date getTimeAdd() {
        return timeAdd;
    }

    public void setTimeAdd(Date timeAdd) {
        this.timeAdd = timeAdd;
    }

    public String getUserAdd() {
        return userAdd;
    }

    public void setUserAdd(String userAdd) {
        this.userAdd = userAdd;
    }

    public Date getTimeEdit() {
        return timeEdit;
    }

    public void setTimeEdit(Date timeEdit) {
        this.timeEdit = timeEdit;
    }

    public String getUserEdit() {
        return userEdit;
    }

    public void setUserEdit(String userEdit) {
        this.userEdit = userEdit;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getPrefixName() {
        return prefixName;
    }

    public void setPrefixName(String prefixName) {
        this.prefixName = prefixName;
    }

    public String getServiceProvider() {
        return serviceProvider;
    }

    public void setServiceProvider(String serviceProvider) {
        this.serviceProvider = serviceProvider;
    }

    public String getServiceImg() {
        return serviceImg;
    }

    public void setServiceImg(String serviceImg) {
        this.serviceImg = serviceImg;
    }

    public String getServiceBigImg() {
        return serviceBigImg;
    }

    public void setServiceBigImg(String serviceBigImg) {
        this.serviceBigImg = serviceBigImg;
    }

    public Integer getDirId() {
        return dirId;
    }

    public void setDirId(Integer dirId) {
        this.dirId = dirId;
    }

    public Integer getOrderBy() {
        return orderBy;
    }

    public void setOrderBy(Integer orderBy) {
        this.orderBy = orderBy;
    }

    public Integer getAutoLogin() {
        return autoLogin;
    }

    public void setAutoLogin(Integer autoLogin) {
        this.autoLogin = autoLogin;
    }

    public UseroutInterfaceDao findDao() {
        return (UseroutInterfaceDao) BaseStoneContext.getInstance().getBean("useroutInterfaceDao");
    }

    public boolean isOpen() {
        return !Objects.equals(this.serviceState, UseroutConstant.S_Close);
    }

    @Override
    public Serializable getPk() {
        return interfaceId;
    }

    /**
     * 直接写isAutoLogin会影响sqlMap取autoLogin（true）值，影响db的autoLogin更新
     */
    public boolean isAutoLoginOpen() {
        return autoLogin != null && ConfigEnum.ConfigSwitch.ZERO_OPEN.getValue() == autoLogin;
    }

}
