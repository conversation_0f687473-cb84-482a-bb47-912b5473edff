package com.shunwang.baseStone.sso.apapter;

import com.shunwang.baseStone.bussiness.dao.BussinessDao;
import com.shunwang.baseStone.bussiness.pojo.Bussiness;
import com.shunwang.baseStone.context.BaseStoneContext;
import com.shunwang.baseStone.core.exception.BaseStoneException;
import com.shunwang.baseStone.core.util.SignTool;
import com.shunwang.baseStone.sso.constant.UserOutsiteConstant;
import com.shunwang.baseStone.sso.intfc.InterfaceService;
import com.shunwang.baseStone.useroutinterface.context.UseroutInterfaceContext;
import com.shunwang.basepassport.binder.common.ErrorCode;
import com.shunwang.basepassport.commonExp.ParamNotFoundExp;
import com.shunwang.basepassport.config.pojo.SiteInterface;
import com.shunwang.basepassport.manager.bean.ReportEntity;
import com.shunwang.basepassport.manager.response.yidun.OneClickCheckResponse;
import com.shunwang.basepassport.user.common.MemberConstants;
import com.shunwang.basepassport.user.common.UserLoginSessionUtil;
import com.shunwang.basepassport.user.constant.OneLoginConstant;
import com.shunwang.basepassport.user.dao.PersonalOneLoginInfoDao;
import com.shunwang.basepassport.user.pojo.Member;
import com.shunwang.basepassport.user.pojo.MemberAccountBind;
import com.shunwang.basepassport.user.pojo.PersonalOneLoginInfo;
import com.shunwang.basepassport.user.service.BaseXmlResponse;
import com.shunwang.basepassport.user.service.YiDunService;
import com.shunwang.util.StringUtil;
import com.shunwang.util.encrypt.Md5Encrypt;
import com.shunwang.util.net.IpUtil;
import org.apache.commons.lang.StringUtils;
import org.apache.struts2.ServletActionContext;
import org.jdom.Document;
import org.jdom.Element;
import org.jdom.JDOMException;
import org.jdom.input.SAXBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.StringReader;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import static com.shunwang.baseStone.core.util.SignTool.PARAM_SPLIT;

/**
 */
public class ConnectedYiDunH5OneClickLoginAdapter extends AbstractConnectedAccountsAdapter {

    private final static Logger log = LoggerFactory.getLogger(ConnectedYiDunH5OneClickLoginAdapter.class);

    private PersonalOneLoginInfoDao personalOneLoginInfoDao;
    private String token;
    private String mobile;
    private String accessToken;
    private String time;

    public String bind() {
        Map<String, Object> json = createResultJSON(false);
        try {
            validParam(); // 校验参数
            useroutInterface = UseroutInterfaceContext.getUseroutInterfaceById(getInterfaceId());
            OneClickCheckResponse checkResponse = YiDunService.checkMobileH5(token, accessToken, getSiteId());
            if (checkResponse.isSuccess()) {
                CompletableFuture.runAsync(()-> saveH5ChargeInfo(checkResponse));
                mobile = checkResponse.getPhone();
            } else {
                log.error("易盾H5一键登录失败：{}", checkResponse.getMsg());
                return writeErrorJson(ErrorCode.C_1095, checkResponse.getMsg());
            }

            member = getMemberDao().getByMobile(mobile);

            if (member == null) {
                // 用户数据不存在，则为其创建
                BaseXmlResponse response = interfaceService.confirmForRegNoActiveNo(buildParamsForOutMobileConfirmUrl());
                member = parseResultForOutMobileConfirmUrl(response.getRawXml());
                if (member == null) {
                    return writeErrorJson(ErrorCode.C_1066);
                }
                doReport(member, ReportEntity.InterfaceType.reg);
            } else if (MemberConstants.USER_CANCEL_STATE == member.getMemberState()) {
                return writeErrorJson(ErrorCode.C_1068, member.getMemberName());
            }

            Bussiness bussiness = getBussinessDao().getById(getSiteId());
            if (bussiness.isSingleAccount()) {
                processSingleAccount(member);
            }
            member.setLoginType(MemberConstants.LOGIN_TYPE_ONE_LOGIN);
            member.setPwdType(MemberConstants.PWD_TYPE_ONE_LOGIN_H5);
            member.setAccessSiteId(getSiteId());
            member.loginWithNoPwd();
            doReport(member, ReportEntity.InterfaceType.login);

            json.put(RESULT, true);
            json.put("memberId", member.getMemberId());
            json.put("memberName", member.getMemberName());
            if (bussiness.isSingleAccount()) { //单账号
                json.put("phone", member.getMobile());
            }
            if (useroutInterface.isAutoLoginOpen()) {
                login(member);
                json.put("ticketId", this.getTicket());
                json.put("tockenId", this.getTockenId());
            }
            UserLoginSessionUtil.saveSession(member.getMemberName(), UserLoginSessionUtil.LoginType.SSO_IMPORT_H5.getType(), null, getSiteId());
            if (bussiness.isSingleAccount()) {
                UserLoginSessionUtil.saveMasterSession(member.getMemberName(), member.getMemberName(), UserLoginSessionUtil.LoginType.SSO_IMPORT_H5.getType(), null, getSiteId());
            }

            return writeJson(json);
        } catch (BaseStoneException e) {
            return writeErrorJson(e);
        } catch (Exception e) {
            logger.error("易盾H5一键登录出现异常：{}", e.getMessage());
            return writeErrorJson(ErrorCode.C_1000);
        }
    }

    private Map<String, String> buildParamsForOutMobileConfirmUrl(){
        Map<String, String> paramMap = new LinkedHashMap<>();
        paramMap.put("userName", "");
        paramMap.put("mobile", mobile);
        paramMap.put("loginIp", IpUtil.getIpAddress(ServletActionContext.getRequest()));
        paramMap.put("accessSiteId", getSiteId());
        return paramMap;
    }

    private Member parseResultForOutMobileConfirmUrl(String xmlText) {
        SAXBuilder builder = new SAXBuilder();
        StringReader strReader = new StringReader(xmlText);
        Member member = new Member();
        try {
            Document document =  builder.build(strReader);
            Element rootNode = document.getRootElement();
            Element node = (Element)rootNode.getChildren("Result").get(0);
            String msgId =  node.getAttributeValue("msgId");
            if("0".equals(msgId)) {
                node = (Element) node.getChildren("items").get(0);
                List<Element> abc = node.getChildren("item");
                member.setMemberName(abc.get(0).getChildText("userName"));
                member.setMemberId(Integer.valueOf(abc.get(0).getChildText("userId")));
                return member;
            }
        } catch (IOException e) {
            log.error("解析消息出错", e);
        } catch (JDOMException e) {
            log.error("解析消息出错", e);
        }
        return null;
    }

    /**
     * 处理单帐号登录绑定逻辑
     */
    protected void processSingleAccount(Member member) {
        MemberAccountBind memberAccountBind = memberAccountBindDao.getByMemberId(member.getMemberId());
        if (null == memberAccountBind) {
            memberAccountBind = new MemberAccountBind();
            memberAccountBind.setMemberId(member.getMemberId());
            if (null != memberAccountBindDao.getByMobile(member.getMobile())) {
                log.info("账号[{}]绑定的手机已经存在关联表直接绑定", member.getMemberName());
            } else {
                memberAccountBind.setPhone(member.getMobile());
            }
            getInterfaceService().singleAccountBindExt(memberAccountBind);
        }
    }


    protected void validParam() {
        if (StringUtils.isBlank(getSiteId())) {
            throw new ParamNotFoundExp("siteId");
        }
        if (StringUtils.isBlank(time)) {
            throw new ParamNotFoundExp("time");
        }
        setTimestamp(time);//保证接口传参与interface一致，一遍后期改造

        if (!checkTimestamp()) {
            throw new BaseStoneException(ErrorCode.C_1003);
        }
        BussinessDao bussinessDao = (BussinessDao) BaseStoneContext.getInstance().getBean("bussinessDao");
        if (bussinessDao.getById(getSiteId()) == null) {
            throw new BaseStoneException(ErrorCode.C_1004);
        }
        if (StringUtil.isBlank(token)) {
            throw new ParamNotFoundExp("token");
        }
        if (StringUtil.isBlank(accessToken)) {
            throw new ParamNotFoundExp("accessToken");
        }
    }
    protected void validSign(SiteInterface siteInterface) {
        String source = SignTool.buildSignStringSorted("sign");
        String mySign = Md5Encrypt.encrypt(source + PARAM_SPLIT + siteInterface.getMd5Key()).toUpperCase();
        if (!mySign.equalsIgnoreCase(getSign())) {
            if (log.isInfoEnabled()) {
                log.info("易盾H5一键登录我方原串：" + source);
                log.info("易盾H5一键登录我方签名：" + mySign + " 易盾H5一键登录对方签名：" + getSign());
            }
            throw new BaseStoneException(ErrorCode.C_1002);
        }
    }


    /**
     * 保存成功结果，因失败时不返回手机号，无意义不保存数据，相关错误需看请求返回日志
     * @param response 渠道返回数据
     */
    private void saveH5ChargeInfo(OneClickCheckResponse response) {
        PersonalOneLoginInfo loginInfo = new PersonalOneLoginInfo();
        loginInfo.setChargeStatus(OneLoginConstant.RECHARGE_STATE_PAY);
        loginInfo.setMobile(response.getPhone());
        loginInfo.setSiteId(getSiteId());
        loginInfo.setChannel(OneLoginConstant.CHANNEL_YIDUN);
        loginInfo.setStatus(response.getCode());
        loginInfo.setRemark(response.getMsg());
        loginInfo.setTimeAdd(new Date());
        getPersonalOneLoginInfoDao().save(loginInfo);
    }

    @Override
    protected void initBindId(MemberAccountBind memberAccountBind, Integer memberId) {
        //获取的是手机号，不建外部帐号
    }

    @Override
    protected MemberAccountBind getByUnionId() {
        //获取的是手机号，不建外部帐号
        return null;
    }

    @Override
    protected int getAdapterType() {
        return 0;
    }

    @Override
    protected String generalMemberName() {
        return null;
    }

    @Override
    protected String getOutMemberName() {
        return null;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public InterfaceService getInterfaceService() {
        return interfaceService;
    }

    public void setInterfaceService(InterfaceService interfaceService) {
        this.interfaceService = interfaceService;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    @Override
    public String getInterfaceId() {
        return UserOutsiteConstant.ONE_CLICK_LOGIN_INTERFACE_ID;
    }

    public PersonalOneLoginInfoDao getPersonalOneLoginInfoDao() {
        return personalOneLoginInfoDao;
    }

    public void setPersonalOneLoginInfoDao(PersonalOneLoginInfoDao personalOneLoginInfoDao) {
        this.personalOneLoginInfoDao = personalOneLoginInfoDao;
    }

    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    public String getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }
}
