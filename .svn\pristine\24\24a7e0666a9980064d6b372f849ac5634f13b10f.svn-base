package com.shunwang.basepassport.manager.response.oa;

import com.google.gson.annotations.Expose;
import com.shunwang.xmlbean.annotation.XmlInit;

public class GetUserData {

    @Expose
    private String realName;
    @Expose
    private String mobile;
    @Expose
    private boolean isDel;
    @Expose
    private Integer status;

    public String getRealName() {
        return realName;
    }
    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getMobile() {
        return mobile;
    }
    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public boolean isDel() {
        return isDel;
    }
    public void setDel(boolean isDel) {
        this.isDel = isDel;
    }

    public Integer getStatus() {
        return status;
    }
    public void setStatus(Integer status) {
        this.status = status;
    }

}
