package com.shunwang.baseStone.sso.apapter;

import com.google.gson.Gson;
import com.shunwang.baseStone.context.IPContext;
import com.shunwang.baseStone.core.exception.BaseStoneException;
import com.shunwang.baseStone.siteinterface.context.SiteContext;
import com.shunwang.baseStone.sso.constant.UserOutsiteConstant;
import com.shunwang.baseStone.sso.constant.UserTockenConstant;
import com.shunwang.baseStone.sso.context.TicketContext;
import com.shunwang.baseStone.sso.context.UserTockenContext;
import com.shunwang.baseStone.sso.exception.ValidateExp;
import com.shunwang.baseStone.sso.intfc.InterfaceService;
import com.shunwang.baseStone.sso.util.InetAddressUtils;
import com.shunwang.baseStone.useroutinterface.constant.UseroutConstant;
import com.shunwang.basepassport.binder.common.ErrorCode;
import com.shunwang.basepassport.config.common.ApiAppUtil;
import com.shunwang.basepassport.config.common.UserOutInterfaceUtil;
import com.shunwang.basepassport.config.pojo.ApiApp;
import com.shunwang.basepassport.config.pojo.UserOutInterface;
import com.shunwang.basepassport.manager.bean.ReportEntity;
import com.shunwang.basepassport.manager.service.bo.ReportProcessor;
import com.shunwang.basepassport.user.common.MemberConstants;
import com.shunwang.basepassport.user.common.UserCheckUtil;
import com.shunwang.basepassport.user.common.UserLoginSessionUtil;
import com.shunwang.basepassport.user.pojo.Member;
import com.shunwang.basepassport.user.pojo.MemberOutSite;
import com.shunwang.framework.struts2.action.BaseAction;
import com.shunwang.util.encrypt.Md5Encrypt;
import com.shunwang.util.lang.StringUtil;
import com.shunwang.util.net.CookieUtil;
import com.shunwang.util.net.IpUtil;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.text.ParseException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static com.shunwang.baseStone.core.util.SignTool.buildSignStringSorted;

/**
 * 简单账号导入Adapter(只根据第三方账号的唯一标示做绑定，为第三方主动导账号数据过来)
 *
 * Copyright© 2005-2015 shunwang. All Rights Reserved.
 * author: <EMAIL>
 * date: 15-2-3
 * time: 下午2:46
 * since:
 */
public class SimpleImportUsersAdapter extends BaseAction implements ReportProcessor {

    private final static Logger log = LoggerFactory.getLogger(SimpleImportUsersAdapter.class);

    private static final long serialVersionUID = -4201805005889624396L;
    protected final static String RESULT = "result";
    protected final static String CODE = "code";
    protected final static String MSG = "msg";
    protected final static long PERMIT_TIME = 30000L; //允许的毫秒数(milliseconds)

    private String interfaceId;
    private String userId;
    private String siteId;
    private String timestamp;
    private String clientIp;
    private String sign;

    private String ticket;
    private String tockenId;

    protected Member member;
    protected UserOutInterface userOutInterface;
    protected InterfaceService interfaceService;

    ///增加传入参数：version、env、extdata，非必填。用于用户登录成功后将这些参数信息
// 写入登录日志对应的字段（logon_version、logon _environment、remark）中；
// 若为新用户注册时，需将version、env、extdata参数写入用户信息表对应的字段中（reg_version、reg_environment、remark）。
    protected String version;
    protected String env;
    protected String extData;
    protected String reportData;

    protected String signVersion;

    /**
     * 这个接口目前做了两件事：
     * 1、用户导入，如果外部用户不存在则生成对应账号；然后会更新登录记录(包括personal.member表和登录日志表)
     * 2、生成ticketId和token，然后返回给对方，对方拿到这个凭证可以实现免登录
     *
     * 本来想的是这个接口只做用户导入，导入成功后通知对方，对方再调用免登录接口去其他平台操作；因写这个接口时对方要求，就按原来类似的接口那样把凭证生成返回给对方了，
     * 但这违背了接口的初衷，后面再改吧。
     *
     */
    public String bind() {
        Map<String, Object> json = createResultJSON(false);
        try {
            validParam();
            userOutInterface = UserOutInterfaceUtil.loadUserOutInterface(getInterfaceId());
            if (userOutInterface == null) {
                return writeErrorJson("非法参数[interfaceId]");
            }
            if (StringUtils.equals(userOutInterface.getServiceState(), UseroutConstant.S_Close)) {
                return writeErrorJson("抱歉，接口已停止服务。");
            }
            validSign();
            MemberOutSite memberOutSite = buildMemberOutSite();
            IPContext.setIp(StringUtils.isNotBlank(clientIp) ? clientIp : IpUtil.getIpAddress(getRequest()));
            SiteContext.setSiteId(getSiteId());
            member = memberOutSite.login();
            if (null == member) {
                outReg(memberOutSite);
            }
            json.put(RESULT, true);
            json.put("memberId", member.getMemberId());
            json.put("memberName", member.getMemberName());

            if (userOutInterface.isAutoLoginOpen()) {
                login(member);
                json.put("ticketId", this.getTicket());
                json.put("tockenId", this.getTockenId());
                UserLoginSessionUtil.saveSession(member.getMemberName(),
                        UserLoginSessionUtil.LoginType.SSO_IMPORT.getType() + getInterfaceId(), null, getSiteId());
            }
        } catch (ValidateExp e) {
            return writeErrorJson(e.getMsg());
        } catch (BaseStoneException e) {
            json.put("msgId", e.getMsgId());
            json.put("msg", e.getMsg());
            json.put(RESULT, false);
            return writeJson(json);
        } catch (Exception e) {
            if (log.isErrorEnabled()) {
                log.error(getServiceProvider() + "账号导入出现异常：" + e.getMessage(), e);
            }
            return writeErrorJson("系统异常");
        }
        return writeJson(json);
    }

    protected void validParam() throws Exception {
        if (StringUtils.isBlank(getInterfaceId())) {
            throw new ValidateExp("缺少必要的参数[interfaceId]");
        }
        if (StringUtils.isBlank(userId)) {
            throw new ValidateExp("缺少必要的参数[userId]");
        }
        if (StringUtils.isBlank(siteId)) {
            throw new ValidateExp("缺少必要的参数[siteId]");
        }
        if (StringUtils.isBlank(timestamp)) {
            throw new ValidateExp("缺少必要的参数[timestamp]");
        }
        if (StringUtils.isBlank(sign)) {
            throw new ValidateExp("缺少必要的参数[sign]");
        }
        try {
            Integer.parseInt(getInterfaceId());
        } catch (NumberFormatException e) {
            throw new ValidateExp("参数类型错误[interfaceId]");
        }
        if (UserCheckUtil.checkEmail(userId)) {
            throw new ValidateExp("userId不能是邮箱");
        }
        if (StringUtils.isNotBlank(clientIp) && !InetAddressUtils.isIPv4Addr(clientIp)) {
            throw new ValidateExp("clientIp不是正确的IP地址");
        }
        if (!checkTimestamp()) {
            throw new ValidateExp("时间超时，请联系管理员校对系统时间");
        }

        ApiApp apiApp = ApiAppUtil.loadApiApp(getSiteId());
        if (apiApp == null) {
            throw new ValidateExp("非法参数[siteId]");
        }
    }
    
    protected void validSign() {
        String source = buildSource();
        String sourceSign = source + UserOutsiteConstant.SPLIT + StringUtils.trimToEmpty(userOutInterface.getServiceKey());
        String mySign = buildSign(sourceSign);
        if (!StringUtils.equalsIgnoreCase(sign, mySign)) {
            if (log.isInfoEnabled()) {
                log.info("我方原串：" + source + "|md5");
            }
            if (log.isInfoEnabled()) {
                log.info("我方签名：" + mySign + " 对方签名：" + sign);
            }
            throw new ValidateExp("签名错误");
        }
    }

    protected String buildSource() {
        StringBuffer sb = new StringBuffer();
        if (StringUtil.isNotBlank(signVersion)) {
            return buildSignStringSorted("sign");
        }
        sb.append(StringUtils.trimToEmpty(getUserId())).append(UserOutsiteConstant.SPLIT)
                .append(StringUtils.trimToEmpty(getSiteId())).append(UserOutsiteConstant.SPLIT)
                .append(StringUtils.trimToEmpty(getTimestamp()));
        return sb.toString();
    }

    protected String buildSign(String source) {
        String sign = null;
        try {
            if (StringUtil.isNotBlank(signVersion)) {
                return Md5Encrypt.encrypt(source).toUpperCase();
            }
            sign = Md5Encrypt.encrypt(URLEncoder.encode(source, "UTF-8").toUpperCase(), "UTF-8").toUpperCase();
        } catch (UnsupportedEncodingException e) {
            if (log.isErrorEnabled()) {
                log.error("构建签名时出现异常：" + e.getMessage(), e);
            }
        }
        return sign;
    }

    protected MemberOutSite buildMemberOutSite() {
        MemberOutSite memberOutSite = new MemberOutSite();
        memberOutSite.setOutMemberId(genMemberName());   //需要唯一，防止和其他厂商账号唯一标示符冲突
        memberOutSite.setOutMemberName(getUserId());
        memberOutSite.setRegFrom(siteId);
        //记录登录日志时用到
        memberOutSite.setMemberFrom(getInterfaceId());
        memberOutSite.setMemberName(memberOutSite.getOutMemberId());
        memberOutSite.setRemark(getExtData());
        memberOutSite.setVersion(getVersion());
        memberOutSite.setEnv(getEnv());
        return memberOutSite;
    }

    protected void login(Member member){
        this.setTicket(TicketContext.createTicket(member, getSiteId()).toString());
        this.setTockenId(UserTockenContext.createTocken(member, siteId).toString());
        CookieUtil.setCookieUpgrade(this.getResponse(), UserTockenConstant.S_UserTockenKey, getTockenId());
    }

    protected void outReg(MemberOutSite memberOutSite) throws Exception {
        try {
            memberOutSite.setMemberFrom(getInterfaceId());
            member = findOrSave(memberOutSite);
            member.setLoginType(getInterfaceId());
            member.loginWithNoPwd();
        } catch (Exception e) {
            if (log.isErrorEnabled()) {
                log.error("调用outReg时出现异常：" + e.getMessage(), e);
            }
            throw e;
        }
    }

    protected Member findOrSave(MemberOutSite memberOutSite){
        Member member = memberOutSite.getDao().getByOutMemberId(memberOutSite.getOutMemberId());
        if(null!=member){
            member.setLoginType(MemberConstants.LOGIN_TYPE_ACCOUNT);
            member.setVrsion(StringUtil.isNotBlank(memberOutSite.getVersion())? memberOutSite.getVersion() : "");
            member.setEnv(memberOutSite.getEnv());
            member.setExtData(memberOutSite.getRemark());
            member.loginWithNoPwd();
            doReport(member, ReportEntity.InterfaceType.login);
            return member;
        }
        Member regMember = interfaceService.outSiteMemberRegister(memberOutSite);
        doReport(regMember, ReportEntity.InterfaceType.reg);
        return regMember;
    }


    protected void doReport(Member member, ReportEntity.InterfaceType interfaceType) {
        Integer memberId = member == null ? null : member.getMemberId();
        report(getSiteId(), memberId, interfaceType, reportData);
    }

    protected String getServiceProvider() {
        return null != userOutInterface ? userOutInterface.getServiceProvider() : "";
    }

    protected String genMemberName() {
        return userOutInterface.getPrefixName() + userId;
    }

    /**
     * <AUTHOR>
     * @param result
     * @return
     */
    protected Map<String, Object> createResultJSON(boolean result) {
        Map<String, Object> json = new HashMap<String, Object>();
        json.put(RESULT, result);
        return json;
    }

    /**
     * 把MAP对象转换成JSON字符串
     * <AUTHOR>
     * @param json
     * @return
     */
    protected String toJSON(Map<String, Object> json){
        try {
            Gson gson = new Gson();
            return gson.toJson(json);
        } catch (Exception e) {
            log.error("", e);
        }
        return null;
    }

    protected String writeErrorJson(String msg) {
        Map<String, Object> json = createResultJSON(false);
        json.put(MSG, msg);
        return writeJson(json);
    }

    protected String writeErrorJson(ErrorCode errorCode) {
        Map<String, Object> json = createResultJSON(false);
        json.put(MSG, errorCode.getDescription());
        json.put(CODE, errorCode.getCode());
        return writeJson(json);
    }

    protected String writeErrorJson(ErrorCode errorCode,String memberName) {
        Map<String, Object> json = createResultJSON(false);
        json.put(MSG, errorCode.getDescription());
        json.put(CODE, errorCode.getCode());
        json.put("memberName", memberName);
        return writeJson(json);
    }

    protected String writeErrorJson(BaseStoneException e) {
        Map<String, Object> json = createResultJSON(false);
        json.put(MSG, e.getMessage());
        json.put(CODE, e.getMsgId());
        return writeJson(json);
    }

    protected String writeJson(Map<String, Object> json) {
        getResponse().setContentType("text/json;charset=UTF-8");
        try {
            getResponse().getWriter().write(toJSON(json));
        } catch (IOException e) {
            if (log.isErrorEnabled()) {
                log.error(e.getMessage());
            }
        }
        return null;
    }

    /**
     * 时间参数验证 (目前后台接口允许时间无法设置，暂定为30秒钟)
     *
     * <AUTHOR>
     * @return
     */
    protected boolean checkTimestamp() {
        Date d1;
        try {
             d1 = DateUtils.parseDate(timestamp, new String[] {"yyyyMMddHHmmss"});
        } catch (ParseException e) {
            if (log.isErrorEnabled()) {
                log.error("时间格式不正确,timestamp:" + timestamp);
            }
            throw new ValidateExp("时间格式不正确");
        }
        long time = (d1.getTime() - System.currentTimeMillis()) / 1000L;
        //time > 0L
        if (Math.abs(time) > PERMIT_TIME) {
            return false;
        }
        return true;
    }

    public void setInterfaceId(String interfaceId) {
        this.interfaceId = interfaceId;
    }

    public String getInterfaceId() {
        return interfaceId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getSiteId() {
        return siteId;
    }

    public void setSiteId(String siteId) {
        this.siteId = siteId;
    }

    public String getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }

    public String getClientIp() {
        return clientIp;
    }

    public void setClientIp(String clientIp) {
        this.clientIp = clientIp;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    public String getTicket() {
        return ticket;
    }

    public void setTicket(String ticket) {
        this.ticket = ticket;
    }

    public String getTockenId() {
        return tockenId;
    }

    public void setTockenId(String tockenId) {
        this.tockenId = tockenId;
    }

    public String getExtData() {
        return extData;
    }

    public void setExtData(String extData) {
        this.extData = extData;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getEnv() {
        return env;
    }

    public void setEnv(String env) {
        this.env = env;
    }

    public String getReportData() {
        return reportData;
    }

    public void setReportData(String reportData) {
        this.reportData = reportData;
    }

    public InterfaceService getInterfaceService() {
        return interfaceService;
    }

    public void setInterfaceService(InterfaceService interfaceService) {
        this.interfaceService = interfaceService;
    }

    public String getSignVersion() {
        return signVersion;
    }

    public void setSignVersion(String signVersion) {
        this.signVersion = signVersion;
    }
}
