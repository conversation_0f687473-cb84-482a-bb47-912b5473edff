package com.shunwang.basepassport.user.web;

import com.google.gson.Gson;
import com.shunwang.baseStone.cache.CacheKeyConstant;
import com.shunwang.baseStone.context.RedisContext;
import com.shunwang.baseStone.encrypt.Encrypt;
import com.shunwang.baseStone.encrypt.pojo.EncryptItem;
import com.shunwang.basepassport.commonExp.ParamNotFoundExp;
import com.shunwang.basepassport.user.common.AuthorizedCodeUtil;
import com.shunwang.basepassport.user.common.AuthorizedTokenUtil;
import com.shunwang.basepassport.user.exception.TokenException;
import com.shunwang.basepassport.user.pojo.AuthorizedCode;
import com.shunwang.basepassport.user.pojo.AuthorizedToken;
import com.shunwang.basepassport.user.response.AuthorizedTokenResponse;
import com.shunwang.util.lang.StringUtil;

import java.util.concurrent.TimeUnit;

/**
 * User:pf.ma
 * Date:2017/06/05
 * Time:16:47
 * 这个接口用于使用AuthorizedCodeAction获得的授权code来换取这个页面所需要的授权Token
 */
public class AuthorizedTokenAction extends AbstractLoginAction {

	//授权token有效时间为5min
	private final static long TIME_OUT = 5 ;


	private String authorizedCode ;

	@Override
	public void process() throws Exception {
		loadMemberName();
		AuthorizedCode code = findAuthorizedCode() ;
		if(null == code){
			throw new TokenException(TokenException.AUTHORIZED_CODE_NOT_EXISTED,"授权code不存在") ;
		}
		if(!code.getAuthorizedCode().equals(authorizedCode)){
			throw new TokenException(TokenException.AUTHORIZED_CODE_INVALID,"授权code无效") ;
		}
		if(!code.getAuthorizedSiteId().equals(getSiteId())){
			throw new TokenException(TokenException.AUTHORIZED_CODE_INVALID,"授权code无效") ;
		}
		// 生成授权Token
		AuthorizedToken authorizedToken = null ;
		if(RedisContext.getRedisCache().del(getCacheKey())){
			authorizedToken = saveAuthorizedToken() ;
		}
		this.setBaseResponse(new AuthorizedTokenResponse(authorizedToken,getMemberName())) ;
	}

	private AuthorizedToken saveAuthorizedToken(){
		AuthorizedToken authorizedToken = buildAuthorizedToken() ;
		String cacheKey = AuthorizedTokenUtil.getAuthorizedTokenKey(getMemberName(), getSiteId());
		logger.info("用户[" + getMemberName() + "]增加authorizedToken["+ cacheKey +" => " + authorizedToken + "]缓存");
		if (!RedisContext.getRedisCache().set(cacheKey, authorizedToken, Integer.valueOf(TIME_OUT+""), TimeUnit.MINUTES)) {
			logger.error("用户[" + getMemberName() + "]增加authorizedToken["+ cacheKey +" => " + authorizedToken + "]缓存异常");
		}
		return authorizedToken ;
	}

	private AuthorizedToken buildAuthorizedToken(){
		String token = AuthorizedTokenUtil.createAuthorizedToken() ;
		AuthorizedToken authorizedToken = new AuthorizedToken() ;
		authorizedToken.setMemberId(getMemberId()) ;
		authorizedToken.setSiteId(getSiteId()) ;
		authorizedToken.setAuthorizedToken(token) ;
		return authorizedToken ;
	}

	private AuthorizedCode findAuthorizedCode() {
		return RedisContext.getRedisCache().get(getCacheKey(),AuthorizedCode.class);
	}

	private String getCacheKey(){
		return AuthorizedCodeUtil.getAuthorizedCodeKey(getMemberName(), getSiteId()) ;
	}

	@Override
	public void checkParam() {
		if (StringUtil.isBlank(getMemberId())) {
			throw new ParamNotFoundExp("memberId");
		}
		if (StringUtil.isBlank(getAuthorizedCode())) {
			throw new ParamNotFoundExp("authorizedCode");
		}
	}

	@Override
	public String getSiteName() {
		return "获取授权Token";
	}

	@Override
	public String buildSignString() {
		Encrypt encrypt = new Encrypt();
		encrypt.addItem(new EncryptItem(super.getSiteId()));
		encrypt.addItem(new EncryptItem(super.getTime()));
		encrypt.addItem(new EncryptItem(getMemberId()));
		encrypt.addItem(new EncryptItem(authorizedCode));
		return encrypt.buildSign();
	}

	public String getAuthorizedCode() {
		return authorizedCode;
	}

	public void setAuthorizedCode(String authorizedCode) {
		this.authorizedCode = authorizedCode;
	}
}
