<%@ page contentType="text/html;charset=UTF-8"%>
<%@ include file="/common/taglibs.jsp"%>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name="Keywords" content="顺网网通行证 ，修改手机号码 ，密保问题" />
<meta name="Description" content="输入密保问题的答案。" />
<title>顺网通行证-修改手机号码-密保问题修改</title>

<script type="text/javascript" src="${staticServer}/scripts/front/changeMobile/changeMobile.js"></script>
</head>
<body>
<%@ include file="/front/changeMobile/global_nav.jsp" %>
<div class="c_body forget_s03">
    <ul class="step_bar">
        <li class="current"><em>1</em> 输入密保问题</li>
        <li><em>2</em> 重置手机号码</li>
        <li><em>3</em> 用密保问题修改完成</li>
    </ul>
    <div class="form_group">
				<form id="changeMobile" action="/front/noLogin/goQuestionChangeMobileNext_front.htm" method="post">
					<input type="hidden" name="appeal.userName" value="${appeal.userName}" />
                    <input type="hidden" name="memberName" value="${appeal.userName}" />
					<input  type="hidden"  id="positionOrg1" name="appeal.questionIsBind"  value="${appeal.safeCardIsBind }" />
					<input  type="hidden"  id="positionOrg2" name="appeal.safeCardIsBind"  value="${appeal.safeCardIsBind }" />
                    <input type="hidden" name="password" id="password"/>
                    <table cellpadding="0" cellspacing="0">
                        <thead>
                        <tr>
                            <th class="w_lg"></th>
                            <td><span class="form_tip_lg">请验证您现在正在使用的密保问题</span></td>
                        </tr>
                        <tr>
                            <th class="w_lg"></th>
                            <td><span id="errorMessagesShowSpan" stlye="color:red;">
                                <script type="text/javascript">
                                    <c:if test="${!empty errorMsg}">
                                    document.getElementById("errorMessagesShowSpan").innerHTML="<img src='<c:url value='/images/front/error.gif'/>' />您输入的答案不正确，请重输 !";
                                    </c:if>
                                    <c:if test="${!empty msg}">
                                    document.getElementById("errorMessagesShowSpan").innerHTML="<img src='<c:url value='/images/front/error.gif'/>' />您输入的答案不正确，请重输 !";
                                    </c:if>
                                </script>
                            </span></td>
                        </tr>
                        </thead>
                        <tbody>
                        <jsp:include page="../find/memberBindQuestionSet_common.jsp"></jsp:include>
                        <tr>
                            <th></th>
                            <td>
                                <a href="###" class="btn_default_lg" onclick="return checkAnswer();">确定</a>
                            </td>
                        </tr>
                        </tbody>
                    </table>
				</form>
			</div></div>
</body>
</html>
