package com.shunwang.passport.member.web;

import com.shunwang.basepassport.context.UserContext;
import com.shunwang.basepassport.detail.common.DetailContants;
import com.shunwang.basepassport.detail.dao.PersonalEditLogDao;
import com.shunwang.basepassport.detail.pojo.PersonalEditLog;
import com.shunwang.basepassport.user.pojo.Member;
import com.shunwang.framework.struts2.action.BaseAction;
import com.shunwang.util.StringUtil;
import com.shunwang.util.date.DateUtil;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 * User: lu.ye
 * Date: 14-12-15
 * Time: 上午9:51
 * To change this template use File | Settings | File Templates.
 */
public class EditLogAction extends BaseAction {
    private PersonalEditLogDao editLogDao;

    private String beginDate;
    private String endDate;

    private Member member;

    private List<PersonalEditLog> editLogs;

    public String editLogQuery(){
        Map<String,String> paramMap=new HashMap<String,String>();
        if(StringUtil.isBlank(beginDate)) {
            //如果开始时间没传定义前七天
            Date weekday= DateUtil.zeroConvertTime(DateUtil.addDay(new Date(), -7));
            beginDate =   DateUtil.dateFormat(DateUtil.getInstance(DateUtil.ymdhms_DATE_FORMAT),weekday );
        }
        if(StringUtil.isBlank(endDate)){
            //如果结束时间为空，默认为当前系统时间
            endDate=DateUtil.dateFormat(DateUtil.getInstance(DateUtil.ymdhms_DATE_FORMAT),new Date());
        }
        paramMap.put("begintime", beginDate);
        paramMap.put("endtime",endDate);
        paramMap.put("memberName", UserContext.getUserName());
        paramMap.put("type", DetailContants.FRONT);
        editLogs=editLogDao.queryForList(paramMap);
        member = UserContext.getUser();
        return SUCCESS;
    }

    public PersonalEditLogDao getEditLogDao() {
        return editLogDao;
    }

    public void setEditLogDao(PersonalEditLogDao editLogDao) {
        this.editLogDao = editLogDao;
    }



    public List<PersonalEditLog> getEditLogs() {
        return editLogs;
    }

    public void setEditLogs(List<PersonalEditLog> editLogs) {
        this.editLogs = editLogs;
    }

    public String getBeginDate() {
        return beginDate;
    }

    public void setBeginDate(String beginDate) {
        this.beginDate = beginDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public Member getMember() {
        return member;
    }

    public void setMember(Member member) {
        this.member = member;
    }
}
