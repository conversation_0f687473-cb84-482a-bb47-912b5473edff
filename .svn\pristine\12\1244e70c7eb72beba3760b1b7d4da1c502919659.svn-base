<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ include file="/common/taglibs.jsp" %>
<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<title>实名认证 - 顺网通行证</title>
	<link rel="stylesheet" href="${staticServer}/scripts/common/calendar/calendar.css" type="text/css" />
	<script type="text/javascript" src="${staticServer}/scripts/common/StringUtil.js"></script>
	<script type="text/javascript" src="${staticServer}/scripts/common/lockDiv.js"></script>
	<script type="text/javascript" src="${staticServer}/scripts/common/jquery.jSelectDate.js"></script>
	<script type="text/javascript" src="${staticServer}/scripts/front/common/commonCheck.js"></script>
	<script type="text/javascript" src="${staticServer}/scripts/front/actu/newCompanyActuInfo_compInfo_front.js"></script>
	<script type="text/javascript" src="${staticServer}/scripts/common/ajaxupload.js"></script>
	<style>
		.step_bar li {
			width: 120px;
		}
	</style>
</head>

<body>
	<div class="shop_head">
		<strong>企业实名认证</strong>
		如需在顺网结算中心申请提现，请先通过实名认证。
	</div>
	<ul class="step_bar">
		<li>
			<em>1</em> 认证手机号码</li>
		<li class="current">
			<em>2</em> 填写基础信息</li>
		<li>
			<em>3</em> 上传认证图片</li>
		<li>
			<em>4</em> 确认信息</li>
		<li>
			<em>5</em> 申请完成</li>
	</ul>
	<form id="confirmCompanyActuInfoForm" action="${appServer}/front/actuality/companyUploadActuInfo_front.htm"
		  method="post">
		<input type="hidden" name="tokenId" value="${tokenId}" />
		<input type="hidden" value="${companyActuInfo.companyActuInfoWrapper.idCardImg3 }"
			   name="companyActuInfo.companyActuInfoWrapper.idCardImg3"/>
		<input type="hidden" value="${companyActuInfo.companyActuInfoWrapper.compLicenceImg }"
			   name="companyActuInfo.companyActuInfoWrapper.compLicenceImg"/>
		<input type="hidden" name="companyActuInfo.companyActuInfoWrapper.memberFrom" value="${companyActuInfo.companyActuInfoWrapper.memberFrom}">
	<div class="form_group">
		<table cellpadding="0" cellspacing="0">
			<tbody>
			<tr>
				<th class="w_md_eq">
					<span class="not_null">*</span>公司名称：</th>
				<td>
					<input name="companyActuInfo.companyActuInfoWrapper.compName" type="text" style="width:220px;"
						   class="form_input" id="compName" tabindex="2" value="${ companyActuInfo.companyActuInfoWrapper.compName}"
						   maxlength="128"/>
					<em class="form_tip" id="compNameAlert">最多可输入128个字符</em>
					<input name="flag" value="2" type="hidden"/>
					<input name="refuseContext" value="${refuseContext }" type="hidden"/>
				</td>
			</tr>
			<tr>
				<th>
					<span class="not_null">*</span>法人姓名：</th>
				<td>
					<input name="companyActuInfo.companyActuInfoWrapper.bussinessEntity" type="text" style="width:220px;"
						   class="form_input" id="bussinessEntity" tabindex="2"
						   value="${ companyActuInfo.companyActuInfoWrapper.bussinessEntity}" maxlength="128"/>
					<em class="form_tip" id="bussinessEntityAlert">最多可输入128个字符</em></p>
				</td>
			</tr>
			<tr>
				<th>
					<span class="not_null">*</span>法人手机号码：</th>
				<td>
					<input name="companyActuInfo.companyActuInfoWrapper.bussinessEntityMobileNo" type="text" style="width:220px;"
						   class="form_input" id="bussinessEntityMobileNo" tabindex="2"
						   value="${companyActuInfo.companyActuInfoWrapper.bussinessEntityMobileNo }" maxlength="11"/>
					<em class="form_tip" id="bussinessEntityMobileNoAlert">请输入11位数字</em></p>
				</td>
			</tr>
			<tr>
				<th>组织机构代码：</th>
				<td>
					<input name="companyActuInfo.companyActuInfoWrapper.compOrganizeCode" type="text" style="width:220px;"
						   class="form_input" id="compOrganizeCode" tabindex="2"
						   value="${companyActuInfo.companyActuInfoWrapper.compOrganizeCode }" maxlength="10"/>
					<em class="form_tip" id="compOrganizeCodeAlert">输入10位数字、拉丁字母或者“-”</em>
				</td>
			</tr>
			<tr>
				<th>
					<span class="not_null">*</span>营业执照号：</th>
				<td>
					<input name="companyActuInfo.companyActuInfoWrapper.compLicenceNo" type="text" style="width:220px;"
						   class="form_input" id="compLicenceNo" tabindex="3"
						   value="${companyActuInfo.companyActuInfoWrapper.compLicenceNo }" maxlength="50"/>
					<em class="form_tip" id="compLicenceNoAlert">请输入50位以内数字或字母。</em>
				</td>
			</tr>
			<tr>
				<th>
					<span class="not_null">*</span>营业执照有效期：</th>
				<td>
					<input name="companyActuInfo.companyActuInfoWrapper.compLicenceEndtime" class="form_input" type="text"
						   id="beginDate"
						   value="${ companyActuInfo.companyActuInfoWrapper.compLicenceEndtimeShow}"
						   size="14" contenteditable="false"/>
					<label><input class="form_checkbox" type="checkbox" name="checkbox" id="longTime"
								  onclick="licenceEndtimeLocked();"/>长期</label>
					<input type="hidden" name="companyActuInfo.companyActuInfoWrapper.compLicenceEndtime" id="selectTime"
						   value="${companyActuInfo.companyActuInfoWrapper.compLicenceEndtimeShow}"/>
					<em class="form_tip" id="compLicenceEndtimeAlert"></em>
				</td>
			</tr>
			<tr>
				<th>
					<span class="not_null">*</span>营业执照所在地：</th>
				<td>
					<select name="" class="form_select"  id="selectProv">
						<option value="0" >--请选择--</option>
						<c:forEach var="item" items="${provList}">
							<option value="${item.areaId}">${item.name}</option>
						</c:forEach>
					</select>
					<select class="form_select" id="selectCity">
						<option value="0">--请选择--</option>
					</select>
					<input type="hidden" name="companyActuInfo.companyActuInfoWrapper.licenceProvinceName"
						   value="${companyActuInfo.companyActuInfoWrapper.licenceProvinceName }" id="licenceProvinceName">
					<input type="hidden" name="companyActuInfo.companyActuInfoWrapper.licenceCityName"
						   value="${companyActuInfo.companyActuInfoWrapper.licenceCityName }" id="licenceCityName">
					<span class="form_tip" id="compLicenceAddrAlert">如果找不到所在城市，可以选择所在地区或上级市</span>
				</td>
			</tr>
			<tr>
				<th>传真：</th>
				<td>
					<input name="companyActuInfo.companyActuInfoWrapper.compFax" type="text" style="width:220px;" class="form_input"
						   id="compFax" tabindex="3" value="${companyActuInfo.companyActuInfoWrapper.compFax }" maxlength="20"/>
					<span class="form_tip" id="compFaxAlert">最多输入20位数字(比如：0571-81234567)</span>
				</td>
			</tr>
			<tr>
				<th>
					<span class="not_null">*</span>开票类型：</th>
				<td>
					<select class="form_select" style="width:150px;" name="companyActuInfo.companyActuInfoWrapper.billType" id="billType"
							onchange="showRevenueCertificate();">
						<option value="0" name="companyActuInfo.companyActuInfoWrapper.billType"
								<c:if test="${companyActuInfo.companyActuInfoWrapper.billType==0 }">selected</c:if>>请选择开票类型
						</option>
						<option value="1" name="companyActuInfo.companyActuInfoWrapper.billType"
								<c:if test="${companyActuInfo.companyActuInfoWrapper.billType==1 }">selected</c:if>>-增值税普通发票-
						</option>
						<option value="2" name="companyActuInfo.companyActuInfoWrapper.billType"
								<c:if test="${companyActuInfo.companyActuInfoWrapper.billType==2 }">selected</c:if>>-增值税专业发票-
						</option>
					</select>
					<input type="hidden" name="companyActuInfo.companyActuInfoWrapper.compBillType" id="compBillType"
						   value="${companyActuInfo.companyActuInfoWrapper.compBillType }"/>
					<em class="form_tip" id="billTypeAlert"></em>
					<div id="invoiceCheckbox" style="display:none;margin-top:5px;">
						<label>
							<input type="hidden" name="invoiceCheck" value="${invoiceCheck}" />
							<input type="checkbox" <c:if test="${invoiceCheck == '1'}">checked</c:if> class="form_checkbox"  onchange="changeInvoiceCheck();"/>已使用统一社会信用代码（三证合一）
						</label>
					</div>
				</td>
			</tr>
			</tbody>
			<tbody id="revenueCertificateShow" style="display:none;">
			<tr id="revenueCertificateNoTr" <c:if test="${invoiceCheck == '1'}">style="display:none;"</c:if>>
				<th><span class="not_null">*</span>企业税务登记证税号（统一社会信用代码）：</th>
				<td>
					<input name="companyActuInfo.companyActuInfoWrapper.revenueCertificateNo" type="text" style="width:220px;"
						   class="form_input" id="revenueCertificateNo" tabindex="3"
						   value="${companyActuInfo.companyActuInfoWrapper.revenueCertificateNo }" maxlength="50"/>
					<em class="form_tip" id="revenueCertificateNoAlert">请输入50位以内的数字或字母（注意，请填写国税税号）。</em>
				</td>
			</tr>
			<tr>
				<th><span class="not_null">*</span>企业开户行：</th>
				<td>
					<input name="companyActuInfo.companyActuInfoWrapper.bankName" type="text" style="width:220px;"
						   class="form_input" id="bankName" tabindex="3" value="${companyActuInfo.companyActuInfoWrapper.bankName }"
						   maxlength="200"/>
					<em class="form_tip" id="bankNameAlert">不支持信用卡和存折进行认证。</em>
				</td>
			</tr>
			<tr>
				<th><span class="not_null">*</span>银行卡号：</th>
				<td>
					<input name="companyActuInfo.companyActuInfoWrapper.bankNo" type="text" style="width:220px;" class="form_input"
						   id="bankNo" tabindex="3" value="${companyActuInfo.companyActuInfoWrapper.bankNo }" maxlength="25"/>
					<em class="form_tip" id="bankNoAlert">此银行卡号为日后开发票使用，请仔细填写。</em>
				</td>
			</tr>
			<tr id="revenueCeritificateImgTr" <c:if test="${invoiceCheck == '1'}">style="display:none;"</c:if>>
				<th><span class="not_null">*</span>企业税务登记证扫描：</th>
				<td>
					<input type="hidden" style="width:220px;" class="form_input"
						   name="companyActuInfo.companyActuInfoWrapper.revenueCertificateImg"
						   value="${companyActuInfo.companyActuInfoWrapper.revenueCertificateImg }" id="revenueCertificateImg"/>
					<div class="upload_idcard">
        <span class="upload-photos upload_b"><ul><li><div class="photo"  <c:if test="${not empty companyActuInfo.companyActuInfoWrapper.revenueCertificateImg}"> style="display: block;"  </c:if>id="photo_1">
            <img <c:if test="${empty companyActuInfo.companyActuInfoWrapper.revenueCertificateImg}"> src="" </c:if> <c:if test="${not empty companyActuInfo.companyActuInfoWrapper.revenueCertificateImg }"> src="${actualityImgServer}/images/actuality/company/revenueCertificateImg/${companyActuInfo.companyActuInfoWrapper.revenueCertificateImg }" </c:if> alt="" id="img_1"><span class="del">上传成功</span></div><input type="file" class="file" name="" value="" id="up_1"><a href="#" class="add-photo">+</a></li></ul></span>
					</div>
					<p class="form_tip" id="revenueCertificateImgAlert">证件需清晰有效的彩色扫描件或数码照。仅支持：jpg、jpeg、png、gif的图片格式，<span class="attention">图片大小不超过5M</span></p>
				</td>
			</tr>
			</tbody>
			<tbody>
			<tr>
				<th></th>
				<td>
					<div id="errorMsg" style="color: red">${errorMsg}</div>
					<a href="###" onclick="return goConfirmPage()" class="btn_default_lg">下一步</a></td>
			</tr>
			</tbody>
		</table>

	</div>
	</form>
	<!-- footer end -->
	<script type="text/javascript">
		$('.sidebar .menu li:first').addClass('current');
	</script>
	<script>
		$(document).ready(function () {
			floatLayer.init();
			//微信浮动层

			new AjaxUpload('#up_1', {
				action: $CONFIG.appServer + '/front/upload/ajaxUploadNetFile.htm?type=revenueCertificate',
				name: 'file',
				responseType: 'json',
				onSubmit: function (file, ext, size) {
					$('#uploadButtonName').html("+上传中");
					if (ext && /^(jpg|jpeg|bmp|png)$/.test(ext.toLowerCase())) {
						this.setData({
							'file': file
						});
					} else {
						alert("请上传格式为 jpg|jpeg|bmp|png 的文件！");
						return false;
					}
				},
				onComplete: function (file, response) {
					if(typeof(response.suc)=="undefined") {
						$('#uploadButtonName').html("+上传失败");
						alert("上传文件大小不能超过5M!");
						return false;
					}
					if (response.suc == false) {
						showInfo("revenueCertificateImgAlert", response.error);
					} else {
						var url = '${actualityImgServer}/images/actuality/company/revenueCertificateImg/' + response.fileName;
						$("#img_1").attr('src',url);
						$("#photo_1").show();
						showInfo("revenueCertificateImgAlert", "");
						$("#revenueCertificateImg").val(response.fileName);
						$("#revenueCertificateImgAlert").html("证件需清晰有效的彩色扫描件或数码照。仅支持：jpg、jpeg、png、gif的图片格式，<span class=\"attention\">图片大小不超过5M</span>");
					}
				}
			});

			if ($("#revenueCertificateImg").val() != '')
				buttonTextFirst = "";
			else buttonTextFirst = "";
		});

		function showInfo(showInfo, msg) {
			if (msg == "") {
				$("#" + showInfo).html("");
				return;
			}

			$("#" + showInfo).css("color", "red");
			$("#" + showInfo).removeClass("form_tip");
			$("#" + showInfo).addClass("col_red");
			$("#" + showInfo).html("<img src=${staticServer}/images/front/error.gif>" + msg);
		}

	</script>
</body>

</html>