package com.shunwang.basepassport.user.web;

import com.shunwang.baseStone.context.BaseStoneContext;
import com.shunwang.baseStone.core.action.BaseStoneAction;
import com.shunwang.baseStone.encrypt.Encrypt;
import com.shunwang.baseStone.encrypt.pojo.EncryptItem;
import com.shunwang.basepassport.user.common.TicketUtil;
import com.shunwang.basepassport.user.common.UserLoginSessionUtil;
import com.shunwang.basepassport.user.dao.MemberDao;
import com.shunwang.basepassport.user.exception.MsgNotFoundExp;
import com.shunwang.basepassport.user.pojo.Member;
import com.shunwang.basepassport.user.response.ThirdLoginResponse;

/**
 * Created by bd.fang on 2016/6/6.
 */
public class GetTicketActon extends BaseStoneAction {
    private Integer memberId;

    public String getTicket() {
        Member member = this.getDao().getByMemberId(memberId);
        if (null == member) {
            throw new MsgNotFoundExp("用户");
        }
        UserLoginSessionUtil.ssoAuthCheck(member.getMemberName(), getSiteId());

        //获取返回签名串
        ThirdLoginResponse response =new ThirdLoginResponse(member);
        response.setTicket(TicketUtil.buildLoginTicket(member));
        this.setBaseResponse(response);
        return null;
    }

    @Override
    public void process() throws Exception {
        this.getTicket();
    }

    @Override
    public String getSiteName() {
        return "getTicketWithoutLogin";//获取app免登陆ticket
    }

    @Override
    public String buildSignString() {
        Encrypt encrypt = new Encrypt();
        encrypt.addItem(new EncryptItem(String.valueOf(getMemberId())));
        encrypt.addItem(new EncryptItem(getSiteId()));
        encrypt.addItem(new EncryptItem(getTime()));
        return encrypt.buildSign();
    }

    public MemberDao getDao() {
        return (MemberDao) BaseStoneContext.getInstance().getBean("memberDao");
    }

    public Integer getMemberId() {
        return memberId;
    }

    public void setMemberId(Integer memberId) {
        this.memberId = memberId;
    }
}
