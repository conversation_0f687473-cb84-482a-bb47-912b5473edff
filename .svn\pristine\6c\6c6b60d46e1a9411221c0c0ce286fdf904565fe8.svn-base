package com.shunwang.basepassport.config.pojo;

import com.shunwang.baseStone.core.pojo.BaseStoneObject;
import com.shunwang.baseStone.siteinterface.constant.SiteInterfaceConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.Serializable;

public class SiteInterface extends BaseStoneObject {
	
	private static final Logger log = LoggerFactory.getLogger(SiteInterface.class);

	private static final long serialVersionUID = -4011776770969972211L;
	private Integer interfaceId;
	private String serviceKey;
	private String businessKey;
	private String md5Key;
	private String permitIp;
	private Integer permitTime;
	private Integer state;
	private String remark;
	private String publicKey;
	private String privateKey;

	public Integer getInterfaceId() {
		return interfaceId;
	}

	public void setInterfaceId(Integer interfaceId) {
		this.interfaceId = interfaceId;
	}

	public String getServiceKey() {
		return serviceKey;
	}

	public void setServiceKey(String serviceKey) {
		this.serviceKey = serviceKey;
	}

	public String getBusinessKey() {
		return businessKey;
	}

	public void setBusinessKey(String businessKey) {
		this.businessKey = businessKey;
	}

	public String getMd5Key() {
		return md5Key;
	}

	public void setMd5Key(String md5Key) {
		this.md5Key = md5Key;
	}

	public String getPermitIp() {
		return permitIp;
	}

	public void setPermitIp(String permitIp) {
		this.permitIp = permitIp;
	}

	public Integer getPermitTime() {
		return permitTime;
	}

	public void setPermitTime(Integer permitTime) {
		this.permitTime = permitTime;
	}

	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getPublicKey() {
		return publicKey;
	}

	public void setPublicKey(String publicKey) {
		this.publicKey = publicKey;
	}

	public String getPrivateKey() {
		return privateKey;
	}

	public void setPrivateKey(String privateKey) {
		this.privateKey = privateKey;
	}

	@Override
	public Serializable getPk() {
		return interfaceId;
	}

	/**
	 * 判断是否打开
	 */
	public boolean isOpen(){
		return this.state !=SiteInterfaceConstant.S_Close;
	}
}
