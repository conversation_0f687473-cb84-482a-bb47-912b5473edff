$import('SFGridView');
$import('SFGrid');
$import('SFInput');
$import('SFSelect');
$import('ValidateRule');
$import('SFDirect');

var BusLoginElementView = $createClass('BusLoginElementView',function(){
	var self = this;
	this.pk =  new SFInput({field:'categoryid'
		,name:'作业单元ID'
		});
	
	this.pojoControls = [
		new SFInput({field:'categoryId'
			,readOnly:true
			,name:'作业单元ID'
		}),
		new SFInput({field:'bussinessKey'
			,readOnly:true
			,name:'商户ID'
		}),
		new SFInput({field:'type'
			,readOnly:true
			,name:'类型'
			}),
		new SFInput({field:'name'
			,readOnly:true
			,name:'名称'
			}),
		new SFSelect({
				field:'value1',
				needdefault:true,
				name:'验证码方式',
				value:'value1',
				items:[
					{value:'1',text:'极验验证码'},
					{value:'2',text:'图片验证码'}
				]
			}),
		new SFInput({field:'value'
			,name:'用户密码错误几次出现验证码'
			,rules:[
			    new CheckPlusInt(),
				new CheckEmpty(),
				new CheckMaxLength({length:3})
			]})
	];
	this.schControl =  [
		new SFInput({field:'categoryName',name:'作业单元', value:categoryName}),
		new SFInput({field:'busName',name:'商户', value:busName})
	];
	this.pojoHidden =['regLink'];
	this.SFGridView();
},'SFGridView');
var data = {};
BusLoginElementView.prototype.buildGrid = function(){
	var self = this;
	var grid = new SFGrid({
		url:'findLoginElementByBusKey.do?bussinessKey='+key,
		col:[
			{id:'nameCn',text:'界面元素'},
			{id:'stateshow',text:'状态'},
			{id:'shareLoginElementShow',text:'共享配置状态'},
			{id:'userEdit',text:'修改人'},
			{id:'timeEdit',text:'修改时间'}
		],
		linebutton:[
		    {
				text:'编辑',
				onclick:function(pojo){
		    	    if(pojo.name=="checkcode") {
		    	    	self.processUpdate(pojo);
		    	    } else if(pojo.name=="partlyAccess") {
		    	    	top.location="../partlyAccess/partlyAccess.jsp?bussinessKey=" + key + "&bussinessName=" + pojo.bussinessName + "&categoryName=" + pojo.categoryName;
		    	    } else if (pojo.name=="immediatelyReg"){
		    	    	data.type = pojo.type;
		    	    	data.name = "immediatelyReg";
		    	    	data.regLink = pojo.regLink
		    	    	data.categoryId = pojo.categoryId;
		    	    	self.createRegWin(pojo);
		    	    } else if (pojo.name == 'appQRCodeConfig') {
						data.type = pojo.type;
						data.name = "appQRCodeConfig";
						data.value = pojo.value;
						data.categoryId = pojo.categoryId;
						self.createAppLoginConfigWin(pojo);
					} else if (pojo.name == 'quickLogin') {
		    	    	data.type = pojo.type;
		    	    	data.name = pojo.name;
		    	    	data.value = pojo.value;
		    	    	data.categoryId = pojo.categoryId;
		    	    	self.createQuickLoginWin(pojo);
					}
		    		
				}
			},
			{
				text:'关闭',
				onclick:function(pojo){
					if(confirm("是否关闭当前记录"))
						self.trigger("closeCategory",pojo);
				},
				showFun:function(data){
					return data.state==1;
				}
			},
			{
				text:'打开',
				onclick:function(pojo){
					if(confirm("是否打开当前记录"))
						self.trigger("openCategory",pojo);
				},
				showFun:function(data){
					return data.state==0;
				}
			},
			{
				text:'共享作业单元配置',
				onclick:function(pojo){
					if(confirm("是否共享作业单元配置"))
						self.trigger("openShare",pojo);
				},
				showFun:function(data){
					return data.shareLoginElement==0;
				}
			},
			{
				text:'不共享作业单元配置',
				onclick:function(pojo){
					if(confirm("是否不共享作业单元配置"))
						self.trigger("closeShare",pojo);
				},
				showFun:function(data){
					return data.shareLoginElement==1;
				}
			}
		]
	});
	return grid;
}

var regWin;

BusLoginElementView.prototype.createRegWin = function(pojo) {
			var self = this;
            updateWin = new SFFormWindow({                  
            title: '注册地址链接',
            btns: [
                {text: '保存', onclick: function(){
                	var regLink = updateWin.getValue().regLink;
                	data.regLink = regLink;
                	pojo.regLink = regLink;
                	updateWin.setValue(pojo);
                	self.update()
                	}
                },
                {text: '取消', onclick:function(){updateWin.closeWin();}}
            ]
            });
            
            updateWin.add(
                 new SFInput({field:'regLink',name:'注册地址链接'})
            );
            updateWin.show();
            updateWin.setValue(pojo);
            return updateWin;
}
BusLoginElementView.prototype.createAppLoginConfigWin = function(pojo) {
	var self = this;
	updateAppWin = new SFFormWindow({
		title: 'APP配置',
		btns: [
			{text: '保存', onclick: function(){
					var value = updateAppWin.getValue().value;
					data.value = value;
					pojo.value = value;
					updateAppWin.setValue(pojo);
					self.updateAppQRCodeConfig()
				}
			},
			{text: '取消', onclick:function(){updateAppWin.closeWin();}}
		]
	});

	updateAppWin.add(
		new SFInput({field:'value',name:'APP名称'})
	);
	updateAppWin.show();
	updateAppWin.setValue(pojo);
	return updateAppWin;
}

BusLoginElementView.prototype.createQuickLoginWin = function (pojo) {
	var self = this;
	updateQuickLoginWin = new SFFormWindow({
		title: '快速登录配置——授权页显示，非ssoAuth帐号免登',
		btns: [
			{
				text: '保存', onclick: function () {
					var value = updateQuickLoginWin.getValue().value;
					data.value = value;
					pojo.value = value;
					updateQuickLoginWin.setValue(pojo);
					self.updateQuickLoginConfig();
				}
			},
			{
				text: '取消', onclick: function (){
					updateQuickLoginWin.closeWin();
				}
			}
		]
	});
	updateQuickLoginWin.add(
		new SFSelect({
			field:'value',
			needdefault: true,
			defaultValue: '1',
			defaultText:'不显示——授权免登',
			name:'免登授权配置',
			items:[
				// {value:'1',text:'不显示'},
				{value:'2',text:'显示——授权免登'},
				{value:'4',text:'不显示——非授权免登'},
				{value:'6',text:'显示——非授权免登'}
			]
		}),
	);
	updateQuickLoginWin.show();
	updateQuickLoginWin.setValue(pojo);
	return updateQuickLoginWin;
}

BusLoginElementView.prototype.update = function(){	
	var regLink = data.regLink;
	var type = data.type;
	var name = data.name;
	var categoryId = data.categoryId;
	if (regLink == "" || regLink == null) {
		alert("请填写注册地址链接！");
		return;
	}
	if (regLink && regLink.length > 2048) {
		alert("注册地址链接过长！");
		return;
	}
    var postData = {regLink: regLink, type: type, name: name, bussinessKey: key, categoryId: categoryId};
    $.ajax({
	      url: "updateRegLink.do",
	      type: 'post',
	      dataType: 'json',
	      data: postData,
	      success: function(data) {
	    	 updateWin.closeWin()
	      	 if (data.success == true) {
	      		alert("更新成功！");
	      	 } else {
	      		 alert(data.msg);
	      	 }
	      },
	      error: function(){
	      }
	});
}

BusLoginElementView.prototype.updateAppQRCodeConfig = function(){
	var value = data.value;
	var type = data.type;
	var name = data.name;
	var categoryId = data.categoryId;
	if (value == "" || value == null) {
		alert("请填写APP名称！");
		return;
	}
	if (value && value.length > 20) {
		alert("APP名称过长！");
		return;
	}
	var postData = {value: value, type: type, name: name, bussinessKey: key, categoryId: categoryId};
	$.ajax({
		url: "updateAppQRCodeConfig.do",
		type: 'post',
		dataType: 'json',
		data: postData,
		success: function(data) {
			updateAppWin.closeWin()
			if (data.success == true) {
				alert("更新成功！");
			} else {
				alert(data.msg);
			}
		},
		error: function(){
		}
	});
}

BusLoginElementView.prototype.updateQuickLoginConfig = function(){
	var value = data.value;
	var type = data.type;
	var name = data.name;
	var categoryId = data.categoryId;

	var postData = {value: value, type: type, name: name, bussinessKey: key, categoryId: categoryId};
	$.ajax({
		url: "updateQuickLoginConfig.do",
		type: 'post',
		dataType: 'json',
		data: postData,
		success: function(data) {
			updateQuickLoginWin.closeWin()
			if (data.success == true) {
				alert("更新成功！");
			} else {
				alert(data.msg);
			}
		},
		error: function(){
		}
	});
}

BusLoginElementView.prototype.buildPojoWin = function(){
	var self = this;
	var controls = this.getPojoControls()
	if(controls){
		var ret = new SFFormWindow({
			title:'验证码模块',
			controls:controls,
			closeBtn:true,
			hidden:this.pojoHidden,
			btns:[
				{text:'保存',
					onclick:function(){
						self.save()
					}
				}
			]
		});
		return ret;
	}
}
