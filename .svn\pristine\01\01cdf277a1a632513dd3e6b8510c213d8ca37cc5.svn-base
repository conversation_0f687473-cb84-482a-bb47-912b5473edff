package com.shunwang.basepassport.binder.web.bind;

import com.shunwang.basepassport.binder.common.BinderConstants;
import com.shunwang.basepassport.binder.exception.NoBindExp;
import com.shunwang.basepassport.binder.pojo.SendBinder;
import com.shunwang.basepassport.commonExp.ParamNotFoundExp;
import com.shunwang.util.lang.StringUtil;

/**
 * bindType 2：手机校验码校验，不会绑定手机
 *
 * <AUTHOR>
 * @date 2018/12/15
 **/
public class ConfirmForNormalNoBindAction extends MobileBindBaseAction {

    @Override
    public void checkParam() {
        setBindType(BinderConstants.ConfirmConstants.ConfirmTypes.TYPE_NORMAL);
        super.checkParam();
        if (getMemberId() == null && StringUtil.isBlank(this.getMemberName())) {
            throw new ParamNotFoundExp("memberName");
        }
    }

    @Override
    protected SendBinder buildSendBinder() {
        SendBinder sendBinder = getSendBinderFromCache(getMemberInfo());
        if (sendBinder == null) {
            sendBinder = (SendBinder) getBinderDao().getById(getMemberInfo().getMemberId());
        }
        //SendBinder sendBinder = (SendBinder) getBinderDao().getById(getMemberInfo().getMemberId());
        if (null == sendBinder) {
            throw new NoBindExp(this.getType());
        }
        sendBinder.setMember(getMemberInfo());
//        sendBinder.setMemberName(getMemberName());
        sendBinder.setBusinessType(BinderConstants.MOBILE_NORMAL_CODE);
        sendBinder.beginBuildLog();
        return sendBinder;
    }
}
