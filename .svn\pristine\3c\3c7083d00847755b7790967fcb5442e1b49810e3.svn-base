package com.shunwang.basepassport.config.pojo;

import com.shunwang.baseStone.core.pojo.BaseStoneObject;

import java.io.Serializable;
import java.util.Date;

/**
 * User:lj.zeng
 * Date:2021/08/10
 * Time:9:53
 */
public class SmsConfig extends BaseStoneObject {

	private Integer id;
	private String siteId;
	private String businessType;//业务类型
	private String content;//短信内容 支持%memberName%,%activeNo%,$time$
	private Integer limitOfHour;//一小时限制发送次数
	private Integer limitOfDay;//一天限制发送次数
	private Integer validTime;//验证码有效期
	private Date timeAdd;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getSiteId() {
		return siteId;
	}

	public void setSiteId(String siteId) {
		this.siteId = siteId;
	}

	public String getBusinessType() {
		return businessType;
	}

	public void setBusinessType(String businessType) {
		this.businessType = businessType;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	public Integer getLimitOfHour() {
		return limitOfHour;
	}

	public void setLimitOfHour(Integer limitOfHour) {
		this.limitOfHour = limitOfHour;
	}

	public Integer getLimitOfDay() {
		return limitOfDay;
	}

	public void setLimitOfDay(Integer limitOfDay) {
		this.limitOfDay = limitOfDay;
	}

	public Integer getValidTime() {
		return validTime;
	}

	public void setValidTime(Integer validTime) {
		this.validTime = validTime;
	}

	public Date getTimeAdd() {
		return timeAdd;
	}

	public void setTimeAdd(Date timeAdd) {
		this.timeAdd = timeAdd;
	}

	@Override
	public Serializable getPk() {
		return id;
	}
}
