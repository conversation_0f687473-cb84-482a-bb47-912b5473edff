body{ }
    img{ border:none}
    .box{ }

    .loginbox{ float:left; width:430px; height:315px; margin:30px 0 0 30px;display:inline;}
    .textss{float:left; text-align:left; font-size:14px; font-family:"宋体"; line-height:21px; background:url(../images/icoss.jpg); background-repeat:no-repeat; padding-left:30px; width:500px; margin:120px 0 0 30px;}
    .textss span{ font-weight:bold; color:#1582bd}
    .rightboxss{ float:right; width:310px; height:315px; margin:45px 30px 0 0 ; display:inline}
    .nores{ float:left; font-size:12px; line-height:34px; color:#767676; padding-right:10px;}
    .nores02{ float:left; width:310px; font-size:12px; line-height:24px; color:#767676; padding-top:25px;}
    .foot{ clear:both; width:100%; height:65px;padding-top:200px;}
    .foottext{margin:0 auto; text-align:center; font-size:12px; font-family:"宋体"; line-height:30px; color:#333;}
    .foottext a{color:#333; text-decoration:none}
    .logobox{ float:left;width:310px; height:182px; margin:10px 0 0 0;}
    .n01{ float:left; margin:6px 6px 0 0;}

    .button_ress a{ background:url(../images/button_res.jpg); width:119px; height:34px; float:left; display:block}
    .button_ress a:hover{ background:url(../images/button_res02.jpg); width:119px; height:34px; display:block}
    /*2013.8.1 xc.sun*/
    .ssologin { height:273px;}
    .login-other { height:21px; margin-left:10px; margin-top:0;}
    .login-other .login-input { float:left; line-height:21px; margin-right:10px;}
    .login-other .other-list { float:left; padding-top:0;}

    .login-box2 .info { margin-bottom:10px;}
    .login-box2 .error {  margin-bottom:15px;}
    .login-box2 .other { padding-top:20px;}
    .public-name{ line-height: 36px; font-size: 16px;font-family: simsun; color:#616060;  font-weight: 100;padding-right:20px;}
    .link-a{line-height: 36px; color: #1582bd; text-decoration: none}