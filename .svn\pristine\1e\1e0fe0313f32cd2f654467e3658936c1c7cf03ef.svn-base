package com.shunwang.basepassport.manager.request.geetest;

import com.google.gson.Gson;
import com.shunwang.basepassport.config.pojo.ConfigInterface;
import com.shunwang.basepassport.manager.HttpMethod;
import com.shunwang.basepassport.manager.InterfaceConstant;
import com.shunwang.basepassport.manager.request.BaseRequest;
import com.shunwang.basepassport.manager.response.geetest.CheckMobileResponse;
import com.shunwang.basepassport.manager.util.HmacSHAUtil;
import com.shunwang.util.json.GsonUtil;

import java.util.HashMap;
import java.util.Map;

public class CheckMobileRequest extends BaseRequest<CheckMobileResponse> {
    private String appId;
    private String appKey;
    private String token;
    private String processId;
    private String authcode;

    @Override
    public Map<String, String> buildParams() {
        //调用下边的方法
        return null;
    }

    public String buildContent() {
        long timeStamp = System.currentTimeMillis();
        Map<String, Object> params = new HashMap<>();
        params.put("process_id", processId);
        params.put("token", token);
        params.put("is_phone_encode", false);//默认不加密
        params.put("timestamp", timeStamp);
        params.put("authcode", authcode);
        String plainText = appId + "&&" + timeStamp;
        String sign = HmacSHAUtil.sha256(plainText, getAppKey());
        params.put("sign", sign);
        return GsonUtil.toJson(params);
    }

    @Override
    public Class<CheckMobileResponse> getResponseClass() {
        return CheckMobileResponse.class;
    }

    @Override
    public Integer getInterfaceKey() {
        return InterfaceConstant.INTERFACE_KEY_GEETEST_CHECK_MOBILE;
    }

    @Override
    public void doInterfaceSetting(ConfigInterface setting) {
        setUrl(setting.getInterfaceUrl1());
        setAppId(setting.getInterfacePartnerId());
        setAppKey(setting.getInterfaceMd5Key());
    }

    @Override
    public Map<String, String> getHeaders() {
        addHeader("Content-Type","application/json");
        return headers;
    }

    @Override
    public HttpMethod getHttpMethod() {
        return HttpMethod.POST;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getProcessId() {
        return processId;
    }

    public void setProcessId(String processId) {
        this.processId = processId;
    }

    public String getAuthcode() {
        return authcode;
    }

    public void setAuthcode(String authcode) {
        this.authcode = authcode;
    }

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }
}
