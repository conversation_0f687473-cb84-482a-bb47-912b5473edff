package com.shunwang.baseStone.sso.web;

import com.shunwang.baseStone.core.exception.BaseStoneException;
import com.shunwang.baseStone.encrypt.pojo.EncryptItem;
import java.io.UnsupportedEncodingException;

import com.shunwang.basepassport.binder.common.ErrorCode;
import com.shunwang.util.encrypt.Md5Encrypt;
import java.net.URLEncoder;
import com.shunwang.baseStone.siteinterface.context.SiteContext;
import com.shunwang.baseStone.siteinterface.pojo.SiteInterface;
import com.shunwang.baseStone.core.exception.MsgNullExp;
import com.shunwang.baseStone.sso.context.TicketContext;
import com.shunwang.baseStone.sso.pojo.ClientInfo;
import com.shunwang.util.encrypt.Base64Encrypt;
import com.shunwang.baseStone.sso.util.ClientInfoBuilder;
import com.shunwang.util.json.GsonUtil;
import com.shunwang.util.net.IpUtil;
import com.shunwang.baseStone.context.BaseStoneContext;
import com.shunwang.basepassport.user.dao.MemberDao;
import com.shunwang.basepassport.user.pojo.Member;
import org.apache.struts2.ServletActionContext;
import com.shunwang.baseStone.sso.constant.UserTockenConstant;
import com.shunwang.baseStone.context.IPContext;
import com.shunwang.util.lang.StringUtil;
import com.shunwang.baseStone.sso.context.UserTockenContext;
import com.shunwang.framework.struts2.action.BaseAction;
import com.shunwang.baseStone.sso.pojo.UserTocken;
import com.shunwang.baseStone.encrypt.Encrypt;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


/*
 *  ******************************
 * * Copyright (c)  by iCafeMavin  Information Technology Inc.
 * * All right reserved.
 * * Create Date: 2013年6月6日 
 * * Create Author: sn.wang
 * * ModuleName:  com.shunwang.baseStone.sso
 * * ProjectName: SSO
 * * Function:  无密码登录, 用于客户端(f1/云海)在已经
 *              持有memberName和 userToken的情况下进行免登录操作
 *  *****************************************
 */

public class LoginWithTokenAction extends BaseAction {

	private final static Logger log = LoggerFactory.getLogger(LoginWithTokenAction.class);

	private static final long serialVersionUID = 8745171501633036477L;

    private String userName;
    private Member member;
    private String siteId;
    private String tokenId;

    private String sign;
    private String time;

    //跨站点够登录时,需要创建token的siteId
    private String newSiteId;


	public  void checkSign(){
		if(StringUtil.isBlank(this.getSign()))
			throw new MsgNullExp("签名");
		if(!this.getSign().equals(this.createSignString()))
			throw new BaseStoneException(ErrorCode.C_1002);
	}

    public String createSignString(){
        SiteContext.setSiteName("SSO");
		SiteContext.setSiteId(this.getSiteId());
		SiteInterface site = SiteContext.getSiteInterface();
		String ret="";
		try {
			String sign = this.buildSignString()+"|"+site.getMd5Key();
			log.debug("request请求加密前字符串:" + sign);
			ret = URLEncoder.encode(sign,"utf-8").toUpperCase();
		} catch (UnsupportedEncodingException e) {
			log.debug("对签名转码错误", e);
		}
		ret = Md5Encrypt.encrypt(ret).toUpperCase();
		return ret;
	}

	public String buildSignString() {
		Encrypt  encrypt=new Encrypt();
		encrypt.addItem(new EncryptItem(getSiteId()));
		encrypt.addItem(new EncryptItem(getTime()));		
		return encrypt.buildSign();
	}


    
    public String execute() throws Exception {


        ClientInfo clientInfo = new ClientInfo();

        try {
            checkSign();
        } catch (MsgNullExp e) {
            return processF1Error(e.getMessage());
        } catch (BaseStoneException e) {
            return processF1Error(e.getMessage());
        }

        UserTocken userAPPTocken = UserTockenContext.getUserAPPTocken(tokenId,StringUtil.trimNull(siteId));

        if(userAPPTocken == null ) {
            return processF1Error("非法请求[usertocken null],请重新登录后重试！");
        }

        if (!userAPPTocken.getUserName().equals(userName)) {
            return processF1Error("非法请求[userName非法],请重新登录后重试！");
        }


        MemberDao memberDao =(MemberDao) BaseStoneContext.getInstance().getBean("memberDao");
        member = memberDao.getMember(userName);

        IPContext.setIp(IpUtil.getIpAddress(ServletActionContext.getRequest()));
        member.loginWithNoPwd();

        //为新的站点创建token
        String newToken=tokenId;
        if (newSiteId!= null && tokenCreateAble(siteId, newSiteId)) {
            newToken =  UserTockenContext.createTocken(member, siteId).toString();
        } else {
            newSiteId =siteId;
        }

        //刷新老的siteId的登录信息
        UserTockenContext.refreshTocken(userAPPTocken, StringUtil.trimNull(siteId));

        //设置新的登录信息
        String currentTicket = TicketContext.createTicket(member,siteId).toString();
        clientInfo = ClientInfoBuilder.setSSoLoginSuccInfo(member, "FALSE",currentTicket, newToken,UserTockenConstant.MD5KEY_CLIENT_F1);
        clientInfo.setSiteId(newSiteId);
        return processF1Success(clientInfo);
        
    }

    private boolean tokenCreateAble(String existedSiteId, String newSiteId) {
        //TODO: 跟据base配置的站点级别, 以确定已经存在的siteId的级别是否高于newSiteId
        //比如如果 云海的siteId 已经登录, F1发免登录请求,创建f1对应的siteId的token,是允许的
        //反之则不行
        return true;
    }

    private String processF1Success(ClientInfo clientInfo) throws Exception {
        this.getResponse().addHeader("F1-Accounts-SignIn", Base64Encrypt.encrypt(GsonUtil.toJson(clientInfo),"utf-8"));
        return "f1Success";
    }

    private String processF1Error(String message) throws Exception {
        ClientInfo clientInfo = new ClientInfo();
        clientInfo.setErrorMsg(message);
        this.getResponse().addHeader("F1-Accounts-SignIn", Base64Encrypt.encrypt(GsonUtil.toJson(clientInfo),"utf-8"));
        this.getResponse().getWriter().println(message);
        return "f1Success";
    }


    public void setUserName(String userName) {
        this.userName=userName;
    }
    public String getUserName() {
        return this.userName;
    }

    public void setMember(Member member) {
        this.member=member;
    }
    public Member getMember() {
        return this.member;
    }

    public void setSiteId(String siteId) {
        this.siteId=siteId;
    }
    public String getSiteId() {
        return this.siteId;
    }

    public void setTokenId(String tokenId) {
        this.tokenId=tokenId;
    }
    public String getTokenId() {
        return this.tokenId;
    }


    public void setSign(String sign) {
        this.sign=sign;
    }
    public String getSign() {
        return this.sign;
    }

    public void setTime(String time) {
        this.time=time;
    }
    public String getTime() {
        return this.time;
    }

    public void setNewSiteId(String newSiteId) {
        this.newSiteId=newSiteId;
    }
    public String getNewSiteId() {
        return this.newSiteId;
    }

}
