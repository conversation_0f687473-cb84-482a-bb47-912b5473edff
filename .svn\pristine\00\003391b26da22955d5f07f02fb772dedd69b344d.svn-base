var $form = $('.form-box'),
	$sentcode = $("#sent-code"),
	$email = $form.find("input[name='newNumber']"),
	$activeNo = $form.find("input[name='activeNo']"),
	$sub = $("#verity"),
	$tips = $(".tips-error"),
	$tokenid = $("input[name='tokenid']"),
	$source = $form.find("input[name='source']");

var CONSTANTS={
	SEC:60
}

var MSG={
	C1001:"重新获取",
	C1002:"秒后重新获取",
	C1003:'请输入邮箱中的验证码',
	C1004:'请输入需绑定的电子邮箱',
	C1005:'电子邮箱格式错误，请重新输入'
}


function time(wait) {
	if (wait <= 0) {
		$sentcode.removeClass("disabled").addClass("btn-primary")
		$sentcode.text("发送验证码");
		wait = 60;
	} else {
		$sentcode.removeClass("btn-primary").addClass("disabled")
		$sentcode.text(wait+MSG.C1002);;
		wait--;
		setTimeout(function() {
				time(wait)
			},
			1000)
	}
}


function showTips(msg) {
	$tips.find("p").html(msg) ;
	$tips.show() ;
}

$form.on('submit',function(){
	if($activeNo.val().length==0){
		showTips(MSG.C1003) ;
		return false ;
	}
	return true ;
});

function chkEmail(strEmail) {
	if (!/^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/.test(strEmail)) {
		return false;
	}
	else {
		return true;
	}
}

function getKey(str){
	var email = $email.val() ;
	return "email"+str+email ;
}

$email.on('keyup', function () {
	$email.val($email.val().toLowerCase());
});


$sentcode.on('click',function(){
	var newNumber = $email.val().toLowerCase();
	//检查手机
	if(newNumber.length==0){
		showTips(MSG.C1004) ;
		return false ;
	}
	chkEmail(newNumber) ;
	$.ajax({
		url: $CONFIG.appServer + '/front/swpaysdk/sendEmailActiveNo.htm',
		data: {"newNumber":newNumber,"tokenid":$tokenid.val(),"source":$source.val()},
		dataType: 'json',
		type: 'post',
		success: function(json) {
			if (!json.result && json.msg) {
				showTips(json.msg);
				if(json.flush){
					setTimeout(function() {
							window.location.href='/front/swpaysdk/emailBindMode.htm';
						},
						4000)
				}
			}
			if(json.result){
				try {
					window.sessionStorage.setItem(getKey("_email_"),new Date().getTime()) ;
					//保存验证码的发送状态
					window.sessionStorage.setItem(getKey("_sendStatus_"),"true") ;
				} catch (e) {
					console.error(e);
				}

				$activeNo.removeAttr("disabled") ;
				time(CONSTANTS.SEC) ;
				$tips.hide() ;
			}
		},
		error: function(e) {
			showTips(e);
		}
	});
}) ;

function init(){
	var msg =  $('.tips-error p').text();
	if(msg) {
		$('.tips-error').show();
	}
	var sendStatus;
	//检查发送状态
	try {
		sendStatus = window.sessionStorage.getItem(getKey("_sendStatus_")) ;
	} catch (e) {
		console.error(e);
	}

	if(sendStatus=="true"){
		$activeNo.removeAttr("disabled") ;
	}else{
		$activeNo.attr("disabled","true") ;
	}
	var sentTime;
	try {
		sentTime = window.sessionStorage.getItem(getKey("_email_")) ;
	} catch (e) {
		console.error(e);
	}

	var interval = Math.round((new Date().getTime()-sentTime)/1000) ;
	if(interval<60){
		wait = (60-interval) ;
		time(wait) ;
	}

	if($tips.find("p").html()=="") {
		$tips.hide();
	}
}

init() ;