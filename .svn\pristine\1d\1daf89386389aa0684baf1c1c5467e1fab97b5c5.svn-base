<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">
	
	
	<bean id="swCrypto" class="com.web2x.struts2.cryp.Web2xCrypImpl">
        <property name="algorithm" value="AES" />
        <property name="transformation" value="AES/ECB/PKCS5Padding" />
        <property name="key">
            <value><![CDATA[0rV68fe_e8.tu5A8]]></value>
        </property>
    </bean>
    
	<!-- 后台用户 -->
	<bean class="com.web2x.struts2.cookie.Web2xConfigure">
		<property name="name" value="KEY_BACK_USER" />
		<property name="clientName" value="_bs_bcu_" />
		<property name="path" value="" />
		<property name="lifeTime" value="-1" />
		<property name="domain" value="${app.domain}" />
		<property name="randomChar" value="0" />
		<property name="crypto" ref="swCrypto" />
		<property name="charset" value="UTF-8" />
	</bean>


	<!-- 统一登录接入 -->
	<bean class="com.web2x.struts2.cookie.Web2xConfigure">
		<property name="name" value="oauth2_redirect_uri" />
		<property name="clientName" value="_pb_o2ru_base__" />
		<property name="path" value="" />
		<property name="lifeTime" value="-1" />
		<property name="domain" value="${app.domain}" />
		<property name="randomChar" value="0" />
		<property name="crypto" ref="swCrypto" />
		<property name="charset" value="UTF-8" />
	</bean>

	<bean class="com.web2x.struts2.cookie.Web2xConfigure">
		<property name="name" value="oauth2_user_email" />
		<property name="clientName" value="_pb_o2ue_base__" />
		<property name="path" value="" />
		<property name="lifeTime" value="-1" />
		<property name="domain" value="${app.domain}" />
		<property name="randomChar" value="0" />
		<property name="crypto" ref="swCrypto" />
		<property name="charset" value="UTF-8" />
	</bean>
	
</beans>