package com.shunwang.basepassport.actu.dao;

import com.shunwang.baseStone.core.dao.BaseStoneIbatisDao;
import com.shunwang.basepassport.actu.pojo.PersonalActuReport;

import java.util.List;

/**
 * @Description:网吧业主实名认证
 * <AUTHOR>  create at 2015-5-25 上午10:47:25
 *
 */
public class PersonalActuReportDao extends BaseStoneIbatisDao<PersonalActuReport> {

    public List<PersonalActuReport> findByMemberAndType(PersonalActuReport pojo) {
        return this.getSqlMapClientTemplate().queryForList(getStatementNameWrap("find"), pojo);
    }

    public List<PersonalActuReport> findUnreported(Integer days) {
        return getSqlMapClientTemplate().queryForList(getStatementNameWrap("findUnreported"), days);
    }

    public void updateStateToReporting(PersonalActuReport pojo) {
        getSqlMapClientTemplate().update(getStatementNameWrap("updateStateToReporting"), pojo);
    }

    public void updateStateToReported(PersonalActuReport pojo) {
        getSqlMapClientTemplate().update(getStatementNameWrap("updateStateToReported"), pojo);
    }
}
