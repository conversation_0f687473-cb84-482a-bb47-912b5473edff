<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd" >

<sqlMap namespace="com.shunwang.basepassport.user.pojo.SafeGuardCode">

	<typeAlias alias="safeGuardCode"
		type="com.shunwang.basepassport.user.pojo.SafeGuardCode" />
		
	<select id="findAssignedCodeByUser" parameterClass="java.lang.String" resultClass="safeGuardCode">
        select * from personal_member_safe_guard_code where receiver=#memberName# limit 1
	</select>
	
    <!-- 分配兑换码给用户 -->
	<update id="assignCode" parameterClass="java.lang.String">
        update personal_member_safe_guard_code set receiver=#memberName#, receive_time=NOW() 
        where id = (select min(id) from 
            personal_member_safe_guard_code where receiver is null )
	</update>


</sqlMap>
