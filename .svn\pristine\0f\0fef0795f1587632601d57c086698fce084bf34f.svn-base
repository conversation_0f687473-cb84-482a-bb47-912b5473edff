package com.shunwang.baseStone.sso.context;

import junit.framework.TestCase;

import com.shunwang.baseStone.context.BaseStoneContext;
import com.shunwang.baseStone.sso.exception.TicketExp;
import com.shunwang.baseStone.sso.pojo.Ticket;
import com.shunwang.basepassport.user.pojo.Member;

public class TicketContextTest extends TestCase {
	@SuppressWarnings("static-access")
	public void testValidate(){
		BaseStoneContext.getInstance().setFileName("/ssoContext.xml");
		BaseStoneContext.getInstance().getBean("identityContext");
		Member member = new Member();
		member.setMemberId(123);
		Ticket ticket = TicketContext.createTicket(member, "identity");
		TicketContext.validateTicket(ticket.getTicketId());
		try{
			TicketContext.validateTicket(ticket.getTicketId());
			this.fail();
		}catch(TicketExp exp){
			System.out.println("抛出异常就对了");
		}
	}
}
