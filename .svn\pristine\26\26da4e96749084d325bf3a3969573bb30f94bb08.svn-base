<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE struts PUBLIC
        "-//Apache Software Foundation//DTD Struts Configuration 2.0//EN"
        "http://struts.apache.org/dtds/struts-2.0.dtd">
<struts>

    <package name="login" extends="struts-default">
        <action name="gtRegister" class="geetestAction" method="gtRegister"/>
        <action name="gtValidate" class="geetestAction" method="gtValidate"/>
        <action name="gtSwitch" class="geetestAction" method="gtSwitch"/>

        <action name="loginPreCheck" class="loginAction" method="preCheckForLogin"/>

        <action name="reportDllData" class="loginAction" method="reportDllData"/>

        <action name="login" class="loginAction">
            <result name="input">/login/login.jsp</result>
            <result name="inputForHtml5">/login/loginForHtml5.jsp</result>
            <result name="inputCancel">/login/loginCancel.jsp</result>
            <result name="inputCancelForHtml5">/login/loginCancelForHtml5.jsp</result>
            <result name="dynamicPwd">/login/loginDynamicPwd.jsp</result>
            <result name="unsafe_success">/login/succWithFreeLogin.jsp</result>
            <result name="singleAccountBind">/login/singleAccountBind.jsp</result>
            <result name="singleAccountBindForH5">/login/singleAccountBindForH5.jsp</result>
            <result name="geetestValided">/login/geetest_valid.jsp</result>
            <result name="smsValided">/login/sms_valid.jsp</result>
            <result name="emailValided">/login/email_valid.jsp</result>
            <result>/login/succWithFreeLogin.jsp</result>
<!--            <result>/login/outOauthSucc.jsp</result>-->
            <param name="yxgw">yxgw.css</param>
            <param name="ww">ww.css</param>
            <exception-mapping result="input" exception="com.shunwang.baseStone.checkCode.exp.CheckCodeExp">
            </exception-mapping>
        </action>

        <action name="qrcodeLogin" class="qrcodeLoginAction">
            <result name="input">/login/loginQrcode.jsp</result>
        </action>
        <action name="goAuth" class="qrcodeLoginAction" method="goAuth">
            <result name="tip">/login/loginQrcodeTip.jsp</result>
            <result name="success">/login/outOauthQr.jsp</result>
        </action>

        <action name="goAuthForNetBar" class="qrcodeLoginAction" method="goAuthForNetBar">
            <result name="tip">/login/loginQrcodeTip.jsp</result>
            <result name="success">/login/outOauthQrForNetBar.jsp</result>
        </action>
        <action name="bootPc" class="qrcodeLoginAction" method="bootPc">
            <result name="success">/login/outOauthQrForNetBar.jsp</result>
            <result name="tip">/login/loginQrcodeTip.jsp</result>
        </action>

        <action name="loginCallback" method="loginCallback" class="loginAction">
            <result name="success">/login/outOauthSucc.jsp</result>
            <result name="singleAccountBind">/login/singleAccountBind.jsp</result>
            <result name="inputCancel">/login/loginCancel.jsp</result>
            <result name="inputCancelForHtml5">/login/loginCancelForHtml5.jsp</result>
        </action>

        <action name="autoLoginCallback" method="autoLoginCallback" class="loginAction">
            <result name="success">/login/succAutoFreeLogin.jsp</result>
            <result name="input">/login/login.jsp</result>
        </action>

        <action name="loginNext" method="dynamicPwdLogin" class="loginAction">
            <result name="input">/login/loginDynamicPwd.jsp</result>
            <result name="success">/login/succWithFreeLogin.jsp</result>
            <result name="unsafe_success">/login/succWithFreeLogin.jsp</result>
            <result name="singleAccountBind">/login/singleAccountBind.jsp</result>
            <result name="singleAccountBindForH5">/login/singleAccountBindForH5.jsp</result>
            <result name="default">/login/loginDefaultNext.jsp</result>
            <param name="yxgw">yxgw.css</param>
            <param name="ww">ww.css</param>
        </action>

        <action name="sendSmsActiveNo" method="sendSmsActiveNo" class="loginAction"/>

        <action name="outMobileSmsConfirm" method="outMobileSmsConfirm" class="loginAction">
            <result name="input">/login/login.jsp</result>
            <result name="success">/login/succWithFreeLogin.jsp</result>
            <result name="inputCancel">/login/loginCancel.jsp</result>
            <result name="inputCancelForHtml5">/login/loginCancelForHtml5.jsp</result>
        </action>

        <action name="sendSingleBindActiveNo" method="sendSingleBindActiveNo" class="loginAction">
        </action>

        <action name="comfirmSingleBindActionNo" method="comfirmSingleBindActionNo" class="loginAction">
        </action>

        <action name="getAgainDynamicPwd" method="getAgainDynamicPwd" class="loginAction">
        </action>

        <action name="freeLogin" class="freeLoginAction">
            <result name="input">/login/login.jsp</result>
            <result name="inline">/login/login.jsp</result>
            <result name="default">/login/loginDefault.jsp</result>
            <result>/login/succAutoFreeLogin.jsp</result>
            <result name="singleAccountBind">/login/singleAccountBind.jsp</result>
            <param name="yxgw">yxgw.css</param>
            <param name="ww">ww.css</param>
            <exception-mapping result="input" exception="com.shunwang.baseStone.checkCode.exp.CheckCodeExp">
            </exception-mapping>
        </action>

        <action name="logout" class="logoutAction">
            <result>/login/logout.jsp</result>
        </action>

        <action name="goOutOauth" method="goOutSiteOauth" class="loginAction">
            <interceptor-ref name="defaultStack"/>
            <interceptor-ref name="coop">
                <param name="mode">unsafe-none</param>
            </interceptor-ref>
            <result name="input">/login/login.jsp</result>
            <result name="success">/login/outOauth.jsp</result>
            <param name="yxgw">yxgw.css</param>
            <param name="ww">ww.css</param>
            <exception-mapping result="input" exception="com.shunwang.baseStone.checkCode.exp.CheckCodeExp">
            </exception-mapping>
        </action>

        <action name="getOutSiteOauth" method="getOutSiteOauth" class="loginAction">
        </action>
        <action name="getOutSiteOauthByServer" method="getOutSiteOauthByServer" class="loginAction">
        </action>

        <action name="checkWxOpenLogin" method="checkWxOpenLogin" class="loginAction">
        </action>
        <action name="checkWxOpenLoginByServer" method="checkWxOpenLogin" class="loginAction">
        </action>

        <action name="laokeiOauth" method="oauthCallback" class="laokeiOauth">
            <result name="input">/login/login.jsp</result>
            <result name="success">/login/outOauthSucc.jsp</result>
            <result name="loginSucc">/login/outOauthSucc.jsp</result>
        </action>

        <action name="laokeiBind" method="laokeiBind" class="laokeiOauth">
            <result name="input">/login/login.jsp</result>
            <result name="success">/login/outOauthSucc.jsp</result>
            <result name="loginSucc">/login/outOauthSucc.jsp</result>

        </action>

        <action name="qqOauth" method="oauthCallback" class="qqOauth">
            <interceptor-ref name="defaultStack"/>
            <interceptor-ref name="coop">
                <param name="mode">unsafe-none</param>
            </interceptor-ref>
            <result name="input">/login/login.jsp</result>
            <result name="success">/login/outOauthSucc.jsp</result>
            <result name="successQr">/login/outOauthQrSucc.jsp</result>
            <result name="singleAccountBind">/login/singleAccountBind.jsp</result>
            <result name="singleAccountBindForH5">/login/singleAccountBindForH5.jsp</result>
            <result name="inputCancel">/login/loginCancel.jsp</result>
            <result name="inputCancelForHtml5">/login/loginCancelForHtml5.jsp</result>
        </action>

        <action name="chinaNetOauth" method="oauthCallback" class="chinaNetOauth">
            <result name="input">/login/login.jsp</result>
            <result name="success">/login/outOauthSucc.jsp</result>
            <result name="loginSucc">/login/outOauthSucc.jsp</result>
        </action>
        <action name="hiNetOauth" method="oauthCallback" class="hiNetOauth">
            <result name="input">/login/login.jsp</result>
            <result name="success">/login/outOauthSucc.jsp</result>
            <result name="loginSucc">/login/outOauthSucc.jsp</result>
        </action>
        <action name="4008ucOauth" method="oauthCallback" class="uc4008Oauth">
            <result name="input">/login/login.jsp</result>
            <result name="success">/login/outOauthSucc.jsp</result>
            <result name="loginSucc">/login/outOauthSucc.jsp</result>
        </action>
        <action name="sichuanNetOauth" method="oauthCallback" class="sichuanNetOauth">
            <result name="input">/login/login.jsp</result>
            <result name="success">/login/outOauthSucc.jsp</result>
            <result name="loginSucc">/login/outOauthSucc.jsp</result>
        </action>
        <action name="outReg" method="outReg" class="chinaNetOauth">
            <result name="input">/login/outOauthReg.jsp</result>
            <result name="success">/login/outOauthSucc.jsp</result>
        </action>

        <action name="sicentBind" method="bind" class="sicentOauth">
        </action>

        <action name="hotelBind" method="bind" class="hotelOauth">
        </action>

        <action name="createSimpleUserByIDCard" method="bind" class="idCardAdapter">
        </action>

        <action name="simpleImportUsers" method="bind" class="simpleImportUsersAdapter">
        </action>

        <action name="createByWXUnionID" method="bind" class="connectedWeixinUnionIDAdapter">
        </action>

        <action name="createByWeiboUID" method="bind" class="connectedWeiboUIDAdapter">
        </action>

        <action name="createByGoogleUID" method="bind" class="connectedGoogleUIDAdapter">
        </action>

        <action name="createByQQOpenID" method="bind" class="connectedQQOpenIDAdapter">
        </action>
        <action name="createByAlipayUnionID" method="bind" class="connectedAlipayUIDAdapter">
        </action>
        <action name="createByQQUnionID" method="bind" class="connectedQQUnionIdAdapter">
        </action>
        <action name="createByAppleUnionID" method="bind" class="connectedAppleUnionIDAdapter">
        </action>
        <action name="createByOneClickLogin" method="bind" class="connectedOneClickLoginAdapter">
        </action>
        <action name="createByH5OneClickLogin" method="bind" class="connectedH5OneClickLoginAdapter">
        </action>
        <action name="createByYiDunH5OneClickLogin" method="bind" class="connectedYiDunH5OneClickLoginAdapter">
        </action>

        <action name="checkUserName" method="checkUserName" class="chinaNetOauth">
        </action>

        <action name="ticketCheck" class="ticketCheckAction">
        </action>
        <action name="reg" class="com.shunwang.baseStone.sso.web.RegAction">

        </action>
        <action name="identityGet" class="com.shunwang.baseStone.sso.web.IndetityAction">
        </action>


        <action name="appPreLogin" class="appPreLoginAction"/>
        <action name="appLogin" class="appLoginAction"/>

        <action name="ticketCreate" class="com.shunwang.baseStone.sso.web.TicketCreateAction">
            <result name="success">/login/error.jsp</result>
        </action>
        <action name="regTicketCreate" class="regTicketCreateAction">
            <result>/login/succWithFreeLogin.jsp</result>
        </action>

        <action name="createSceneCode" class="sceneCodeCreateAction"  method="execute" />

        <action name="interLoginAuthorize" class="com.shunwang.baseStone.sso.web.InterfaceLoginAuthorizeAction">
        </action>

        <action name="mobileLoginPage" method="mobileLoginPage" class="mobileLoginAction">
            <result name="mobileLoginHtml5New">/login/loginForHtml5New.jsp</result>
        </action>

        <action name="sendActiveNo" method="sendActiveNo" class="mobileLoginAction"/>

        <action name="outMobileConfirm" method="outMobileConfirm" class="mobileLoginAction">
            <result name="inputForHtml5">/login/loginForHtml5.jsp</result>
            <result name="success">/login/succ.jsp</result>
            <result name="inputCancelForHtml5">/login/loginCancelForHtml5.jsp</result>
        </action>

        <action name="outMobileConfirmNew" method="outMobileConfirmNew" class="mobileLoginAction"/>

        <action name="bindAsLoginAccount" method="bindAsLoginAccount" class="mobileLoginAction">
            <result name="inputForHtml5">/login/loginForHtml5.jsp</result>
            <result name="success">/login/succ.jsp</result>
        </action>


        <action name="undoCancel" method="undoCancel" class="loginAction">
        </action>
        <action name="geetestValid" method="geetestValid" class="loginAction">

        </action>
    </package>

    <package name="wxBindIdCard" namespace="/wxBindIdCard" extends="struts-default">
        <action name="bindWxAndIdCard" method="execute" class="wxIdCardBindAction">
        </action>
        <action name="goBind" method="bindPage" class="wxIdCardBindAction">
            <result name="input">/wxBindIdCard/index.jsp</result>
            <result name="success">/wxBindIdCard/result.jsp</result>
        </action>
        <action name="goEdit" method="editPage" class="wxIdCardBindAction">
            <result name="input">/wxBindIdCard/edit.jsp</result>
        </action>
        <action name="edit" method="edit" class="wxIdCardBindAction">
        </action>
        <action name="goResult" method="resultPage" class="wxIdCardBindAction">
            <result name="input">/wxBindIdCard/result.jsp</result>
        </action>
        <action name="getAllAds" method="getAllAds" class="wxIdCardBindAction">
        </action>
        <action name="report" method="report" class="wxIdCardBindAction">
        </action>
    </package>

    <package name="outOauth" namespace="/front/outSite" extends="struts-default">
        <action name="nologinAuth" class="outOauthAction">
            <result name="success">/login/succ.jsp</result>
            <result name="input">/login/login.jsp</result>
        </action>
    </package>

    <package name="weixinOauth" namespace="/oauth" extends="struts-default">
        <action name="nologinAuth" method="oauthCallback" class="weixinOauth">
            <result name="input">/login/login.jsp</result>
            <result name="success">/login/outOauthSucc.jsp</result>
            <result name="successQr">/login/outOauthQrSucc.jsp</result>
            <result name="singleAccountBind">/login/singleAccountBind.jsp</result>
            <result name="inputCancel">/login/loginCancel.jsp</result>
            <result name="inputCancelForHtml5">/login/loginCancelForHtml5.jsp</result>
            <result name="tip">/login/loginQrcodeTip.jsp</result>
        </action>
    </package>

    <package name="single" namespace="/single" extends="struts-default">
        <action name="goSingle" method="goSingle" class="loginAction">
            <result name="singleAccountBind">/login/singleAccountBind.jsp</result>
            <result name="singleAccountBindForH5">/login/singleAccountBindForH5.jsp</result>
        </action>
        <action name="goSingleNoJump" method="goSingleNoJump" class="loginAction">
            <result name="input">/login/singleAccountBindNoJumpForH5.jsp</result>
        </action>
    </package>

    <package name="weixinMini" namespace="/mini" extends="struts-default">
        <action name="nologinAuth" method="oauthCallback" class="miniOauth"/>
        <!--用于小程序测试，业务方小程序需自己获取-->
        <action name="pullUnionId" method="pullUnionId" class="miniOauth"/>
        <!--计费免登查询协议数据-->
        <action name="loadMiniConfig" method="loadMiniConfig" class="miniOauth"/>
    </package>

    <package name="weixinOpen" namespace="/weixin" extends="struts-default">
        <action name="ticket"  method="verifyTicket" class="weixinOpenOauth">
            <result name="success" type="stream">
                <param name="contentType">text/plain</param>
                <param name="inputName">stream</param>
            </result>
        </action>
        <!-- 去预授权页面 -->
        <action name="goPreAuth" method="goPreAuth" class="weixinOpenOauth">
            <result name="success">/login/wxPreOauth.jsp</result>
            <result name="error">/login/preAuthFail.jsp</result>
        </action>
        <action name="preAuthCallback" method="preAuthCallback" class="weixinOpenOauth">
            <result name="error">/login/preAuthFail.jsp</result>
            <result name="success">/login/preAuthSuccess.jsp</result>
        </action>
    </package>

    <package name="wxCallOauth" namespace="/wxCallback" extends="struts-default">
        <action name="*" method="oauthCallback" class="weixinOpenOauth">
            <param name="appId" >{1}</param>
        </action>
    </package>

    <package name="weiboOauth" namespace="/weibo" extends="struts-default">
        <action name="nologinAuth" method="oauthCallback" class="weiboOauth">
            <result name="input">/login/login.jsp</result>
            <result name="success">/login/outOauthSucc.jsp</result>
            <result name="successQr">/login/outOauthQrSucc.jsp</result>
            <result name="singleAccountBind">/login/singleAccountBind.jsp</result>
            <result name="singleAccountBindForH5">/login/singleAccountBindForH5.jsp</result>
            <result name="inputCancel">/login/loginCancel.jsp</result>
            <result name="inputCancelForHtml5">/login/loginCancelForHtml5.jsp</result>
        </action>
    </package>

    <package name="alipayOauth" namespace="/alipay" extends="struts-default">
        <action name="nologinAuth" method="oauthCallback" class="alipayOauth">
            <result name="input">/login/login.jsp</result>
            <result name="success">/login/outOauthSucc.jsp</result>
            <result name="successQr">/login/outOauthQrSucc.jsp</result>
            <result name="singleAccountBind">/login/singleAccountBind.jsp</result>
            <result name="singleAccountBindForH5">/login/singleAccountBindForH5.jsp</result>
            <result name="inputCancel">/login/loginCancel.jsp</result>
            <result name="inputCancelForHtml5">/login/loginCancelForHtml5.jsp</result>
        </action>
    </package>

    <package name="googleOauth" namespace="/google" extends="struts-default">
        <action name="nologinAuth" method="oauthCallback" class="googleOauth">
            <result name="input">/login/login.jsp</result>
            <result name="success">/login/outOauthSucc.jsp</result>
            <result name="singleAccountBind">/login/singleAccountBind.jsp</result>
            <result name="singleAccountBindForH5">/login/singleAccountBindForH5.jsp</result>
            <result name="inputCancel">/login/loginCancel.jsp</result>
            <result name="inputCancelForHtml5">/login/loginCancelForHtml5.jsp</result>
        </action>
    </package>

    <package name="freeLogin" namespace="/freelogin" extends="struts-default">
        <action name="obtainClientToken" method="execute" class="obtainClientTokenAction">
        </action>

        <action name="checkClientToken" method="execute" class="checkClientTokenAction">
        </action>

        <!-- interface客户端免登用 -->
        <action name="obtainClientTicket" method="execute" class="obtainClientTicketForInterfaceAction">
        </action>

        <action name="checkFreeTicket" method="execute" class="checkFreeTicketAction">
        </action>
        <!-- interface客户端免登用 -->

        <action name="checkFreeTicketForGuest" method="execute" class="checkFreeTicketForGuestAction"/>

    </package>

</struts>
