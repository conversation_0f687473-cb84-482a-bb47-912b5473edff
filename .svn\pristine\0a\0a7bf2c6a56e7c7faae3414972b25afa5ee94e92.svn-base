package com.shunwang.basepassport.weixin.exception;

import com.shunwang.baseStone.core.exception.BaseStoneException;

import static com.shunwang.basepassport.binder.common.ErrorCode.C_1001;
import static com.shunwang.basepassport.binder.common.ErrorCode.C_1092;


public class ParamErrorException extends BaseStoneException {

	public ParamErrorException(){
		super(C_1001.getCode(), C_1001.getDescription()) ;
	}

	public ParamErrorException(String message){
		super(C_1001.getCode(),message) ;
	}
}
