package com.shunwang.baseStone.sso.task;

import org.springframework.scheduling.annotation.Scheduled;

/**
 * @author: lj.zeng
 * @create: 2023-03-07 09:40:54
 * @Description: 用户注销通知任务
 */
public class MobileChangeNotifyTask extends ServiceNotifyTask{

    private static final String NOTIFY_GROUP = "mobileChange";

    @Override
    protected String notifyGroup() {
        return NOTIFY_GROUP;
    }

    @Override
    @Scheduled(cron = "${task.service.notify.mobileChange.cronExpression}")
    public void timer() {
        super.timer();
    }
}
