<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd" >
	<sqlMap namespace="com.shunwang.basepassport.config.pojo.SiteInterface">
	<typeAlias alias="siteInterface" type="com.shunwang.basepassport.config.pojo.SiteInterface"/>
	<resultMap class="com.shunwang.basepassport.config.pojo.SiteInterface" id="BaseResultMap">
		<result property="interfaceId" column="interface_id" jdbcType="int"/>
		<result property="serviceKey" column="service_key" jdbcType="varchar"/>
		<result property="businessKey" column="business_key" jdbcType="varchar"/>
		<result property="md5Key" column="md5_key" jdbcType="varchar"/>
		<result property="permitIp" column="permit_ip" jdbcType="varchar"/>
		<result property="permitTime" column="permit_time" jdbcType="int"/>
		<result property="state" column="state" jdbcType="int"/>
		<result property="privateKey" column="private_key" jdbcType="varchar"/>
		<result property="publicKey" column="public_key" jdbcType="varchar"/>
	</resultMap>

	<sql id="baseColumn">
		a.interface_id,
		a.service_key,
		a.business_key,
		a.md5_key,
		a.permit_ip,
		a.permit_time,
		a.state,
		a.private_key,
		a.public_key
	</sql>

	<select id="get" resultMap="BaseResultMap" parameterClass="java.lang.String">
		select
		<include refid="baseColumn"/>
		from config_site_interface a
		where a.interface_id = #value#
	</select>

	<select id="getByAppIdAndServiceKey" resultMap="BaseResultMap" parameterClass="siteInterface">
		select
		<include refid="baseColumn"/>
		from config_site_interface a
		where a.business_key = #businessKey# and a.service_key = #serviceKey#
		limit 1
	</select>

	<select id="getOpenOneByAppId" resultMap="BaseResultMap" parameterClass="siteInterface">
		select
		<include refid="baseColumn"/>
		from config_site_interface a
		where a.business_key = #businessKey# and `state` = #state#
		limit 1
	</select>

</sqlMap>