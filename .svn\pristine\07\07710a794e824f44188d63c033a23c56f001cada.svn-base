package com.shunwang.passport.find.web;

import com.alibaba.druid.util.StringUtils;
import com.shunwang.baseStone.context.RedisContext;
import com.shunwang.basepassport.actu.constant.ActuConstant;
import com.shunwang.baseStone.context.BaseStoneContext;
import com.shunwang.basepassport.find.dao.AppealDao;
import com.shunwang.basepassport.user.common.MemberConstants;
import com.shunwang.basepassport.user.common.UserCheckUtil;
import com.shunwang.basepassport.user.dao.MemberDao;
import com.shunwang.basepassport.user.pojo.Member;
import java.util.Date;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

import com.shunwang.util.date.DateUtil;
import com.shunwang.baseStone.key.SWKey;
import com.shunwang.baseStone.key.SWKeyContext;
import com.shunwang.basepassport.context.UserContext;
import com.shunwang.basepassport.find.common.AppealConstants;
import com.shunwang.basepassport.find.pojo.Appeal;
import com.shunwang.basepassport.find.pojo.AppealExtendMsg;
import com.shunwang.util.lang.StringUtil;


/**
 * 
 * ****************************
 * 版权所有：顺网科技 保留所有权利
 * 创建日期: 2011-8-22  下午02:08:57
 * 创建作者：陈积慧
 * 文件名称：AppealFindQueAction.java
 * 版本： 1.0
 * 功能：申诉找回密保问题 Action
 * 最后修改时间：
 * 修改记录：
 ***************************************
 */
public class AppealFindQueAction extends AppealAction{

	/**
	 * 
	 */
	private static final long serialVersionUID = -8937634035366480365L;
	
	
	private Appeal appeal ;
	

    private String dateType;
    private String beginDate;
    private String endDate;

    public MemberDao getMemberDao(){		
		return (MemberDao)BaseStoneContext.getInstance().getBean("memberDao");
	}

	/**
	 * ***********
	  * 创建日期: 2011-8-30  上午10:49:14
	  * 创建作者：chenjh
	  * @return
	  * 功能：初始化注册方式 
	  *************
	 */
	public String goAppealChangeQue(){
		if(member == null)
			member = UserContext.getMember();
		//未登录之前,无member
		if(member == null){
			if(	(appeal!=null && org.apache.commons.lang.StringUtils.isBlank(appeal.getUserName())) && org.apache.commons.lang.StringUtils.isBlank(memberName)){
				return INPUT;
			}
		}else{
			memberName = member.getMemberName();
		}

		AppealDao appealDao=(AppealDao)BaseStoneContext.getInstance().getBean("appealDao");
		Appeal appeal = appealDao.findRecentAppealForQuestion(memberName);

		return super.goAppealResult(appeal);
	}


	public String appealInputInfo(){

		if(UserContext.getMember()==null  ){
			if (StringUtil.isNotBlank(memberName) )
				member = getMemberDao().getMember(memberName);
			String valid = validateMemberNameAndCode();
			if(valid!=null) return valid;
		}else{
			member=UserContext.getMember();
			memberName = member.getMemberName();
		}

		//未绑定则先绑定(需要登录)
		if(StringUtil.isNotBlank(memberName)){
			getUserState(memberName);
			Boolean isBind = (Boolean)this.getRequest().getAttribute("questionIsBind");
			if(isBind == null || !isBind){
				return "bind";
			}
		}


		if("5".equals(findWay)){
			this.getUserState(memberName);
			return "quickFind";
		}

		String result = goAppealChangeQue();
		if(!SUCCESS.equals(result)) return result;

		member = getMemberDao().getByName(memberName);
        if (AppealConstants.USER_TYPE.equals(member.getMemberType()) && member.getPersonCertState().equals(Integer.parseInt(ActuConstant.INFO_STATE_PASS)) ||
				MemberConstants.MEMBER_SPECIAL_TYPE_CAFE.equals(member.getMemberSpecialType()) && member.getCafeCertState().equals(Integer.parseInt(ActuConstant.INFO_STATE_PASS))) {
            this.getRequest().setAttribute("actuIsTrue", true); //用于标识是否需要实名认证 按钮是否出现
			this.getRequest().setAttribute("memberType",MemberConstants.MEMBER_SPECIAL_TYPE_CAFE.equals(member.getMemberSpecialType()) ? "cafe":"person");//目前只有这两种
            return "realNameAppeal";
        } else {
            SWKeyContext.put(new SWKey("appealQque"));
            super.initRegMsg();
            return "nonRealNameAppeal";
        }
	}
	/**
	 * ***********
	  * 创建日期: 2011-8-22  下午01:47:27
	  * 创建作者：chenjh
	  * @return
	  * 功能：提交申诉信息
	 * @throws Exception 
	  *************
	 */
	@Override
	public String commitAppealMsg(){
		if(appeal == null && org.apache.commons.lang.StringUtils.isBlank(memberName)){
			setErrorMsg("账号不存在");
			return INPUT;
		}
		if(StringUtil.isNotBlank(memberName))
			appeal.setUserName(memberName);
		else
			memberName=appeal.getUserName();
		String key = "commit_appeal_que_lock_" + memberName;
		try {
			if(!RedisContext.getRedisCache().setNx(key," ", 5)){
				log.warn("请匆重复操作");
				return SUCCESS; //直接跳转成功页面
			}

			//查找审核中
			AppealDao  appealDao=(AppealDao)BaseStoneContext.getInstance().getBean("appealDao");
			Appeal appealTemp = appealDao.findRecentAppealForQuestion(memberName);
			//如果有申诉,而且正在审核中,则显示申诉审批结果
			if (appealTemp != null && AppealConstants.CHECKSTATE_WATITING.equals(appealTemp.getAppealState())) {
				log.warn("请匆重复操作");
				return SUCCESS; //直接跳转成功页面
			}
			//封装扩展信息
			AppealExtendMsg appealExtOne=new AppealExtendMsg(this.getQuestion1()+"", this.getAnswer1());
			AppealExtendMsg appealExtTwo=new AppealExtendMsg(this.getQuestion2()+"", this.getAnswer2());
			AppealExtendMsg appealExtThree=new AppealExtendMsg(this.getQuestion3()+"", this.getAnswer3());
			List<AppealExtendMsg>extList=new ArrayList<AppealExtendMsg>();
			extList.add(appealExtOne);
			extList.add(appealExtTwo);
			extList.add(appealExtThree);
			appeal.setAppealType(AppealConstants.FIND_TYPE_THREE);
			appeal.setExtList(extList);

			SWKeyContext.get("appealQque");
			this.checkData();//再次检查问题
			if(StringUtil.isBlank(appeal.getUserName()))
				appeal.setUserName(memberName);
			this.checkCode();
			appeal.appealMsg();
		} catch (Exception e) {
			super.initRegMsg();
			setErrorMsg(e.getMessage());
			log.error("commitAppealMsg出错",e);
			return INPUT;
		}
		return SUCCESS;
	}


	public String gotoResetProblem(){
		if (appeal == null) return INPUT;
		appeal.setRealName(appeal.getRegRealName());
		appeal.setIdCardNo(appeal.getRegIdCard());
        if(dateType != null && ! dateType.equals("chooseDate")){
            Date schEndDate = DateUtil.getCurrentDateEnd();
            Date schBeginDate = null;
			
			if(dateType.equals("oneDay")){
				schBeginDate = DateUtil.getCurrentDateBegin();
			}
			if(dateType.equals("oneWeek")){
				schBeginDate = DateUtil.addDay(schEndDate, -7);
			}
			if(dateType.equals("oneMonth")){
				schBeginDate = DateUtil.zeroConvertTime(DateUtil.addMonth(schEndDate, -1));
			}
			if(dateType.equals("threeMonth")){
				schBeginDate = DateUtil.zeroConvertTime(DateUtil.addMonth(schEndDate, -3));
			}
			if(dateType.equals("halfYear")){
				schBeginDate = DateUtil.zeroConvertTime(DateUtil.addMonth(schEndDate, -6));
			}
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            beginDate = sdf.format(schBeginDate);
            endDate = sdf.format(schEndDate);

		}
		return SUCCESS;
    }
	


	public Appeal getAppeal() {
		return appeal;
	}


	public void setAppeal(Appeal appeal) {
		this.appeal = appeal;
	}



    public void setDateType(String dateType) {
        this.dateType=dateType;
    }
    public String getDateType() {
        return this.dateType;
    }


    public void setBeginDate(String beginDate) {
        this.beginDate=beginDate;
    }
    public String getBeginDate() {
        return this.beginDate;
    }


    public void setEndDate(String endDate) {
        this.endDate=endDate;
    }
    public String getEndDate() {
        return this.endDate;
    }
}
