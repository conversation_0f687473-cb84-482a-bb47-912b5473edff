package com.shunwang.basepassport.binder.pojo;

import com.shunwang.baseStone.context.BaseStoneContext;
import com.shunwang.baseStone.context.TransactionContext;
import com.shunwang.baseStone.core.detail.DetailItem;
import com.shunwang.basepassport.binder.common.BinderConstants;
import com.shunwang.basepassport.binder.dao.BinderDao;
import com.shunwang.basepassport.commonExp.SystemExp;
import com.shunwang.basepassport.detail.common.DetailContants;
import com.shunwang.basepassport.detail.dao.PersonalEditLogDao;
import com.shunwang.basepassport.detail.pojo.PersonalEditLog;
import com.shunwang.basepassport.user.common.MemberConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;

public class ShunLingBinder extends Binder {

	private static final long serialVersionUID = 9202666899232876964L;
	private final static Logger log = LoggerFactory.getLogger(ShunLingBinder.class);
	private Date timeAdd;
	
	@Override
	public void bind() {
		this.setTimeAdd(new Date());
		try {
			if(!getMember().getIsBindShunLing()) {
				TransactionContext.beginTransaction();
				getEditLogDao().save(buildEditLog());
				getMember().setIsBindShunLing(Boolean.TRUE);
				getMember().update();
				TransactionContext.commitTran();
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			TransactionContext.rollbackTran();
			throw new SystemExp();
		}
	}
	
	public PersonalEditLog buildEditLog() {
		PersonalEditLog editLog = new PersonalEditLog();
		DetailItem item = new DetailItem("绑定状态", String.valueOf(getMember().getBindState()),String.valueOf(getMember().getBindState() + MemberConstants.MEMBER_STATE_SHUN_LING));
//		DetailItem itemCur = new DetailItem("cur", "", String.valueOf(getMember().getBindState() + MemberConstants.MEMBER_STATE_SHUN_LING));
		editLog.setMember(getMember());
		editLog.setType(DetailContants.FRONT);
		editLog.setUserAdd(getMemberName());
		editLog.setValuePre(String.valueOf(getMember().getBindState()));
		editLog.setValueCur(String.valueOf(getMember().getBindState() + MemberConstants.MEMBER_STATE_SHUN_LING));
		editLog.setEditItem("开通顺令");
		editLog.setDate(new Date());
		editLog.beginBuildLog(Boolean.TRUE);
		editLog.addItem(item);
//		editLog.addItem(itemCur);
		
		return editLog;
	}

	@Override
	public BinderDao<Binder> getBinderDao() {
		return null;
	}

	public PersonalEditLogDao getEditLogDao() {
		return (PersonalEditLogDao) BaseStoneContext.getInstance().getBean("personalEditLogDao");
	}
	
	@Override
	public String getType() {
		return BinderConstants.SHUN_LING;
	}

	@Override
	public boolean isBinded() {
		return this.getMember().getIsBindShunLing();
	}

	@Override
	public void send() {
		
	}

	@Override
	public void unBind() {
		
	}

	@Override
	public void validate(Object object) {
		
	}

	public Date getTimeAdd() {
		return timeAdd;
	}

	public void setTimeAdd(Date timeAdd) {
		this.timeAdd = timeAdd;
	}
	
}
