package com.shunwang.baseStone.sso.freeloginweb;

import java.util.Map;

import com.shunwang.basepassport.binder.exception.ParamNullExp;
import com.shunwang.basepassport.user.common.UserCheckUtil;
import com.shunwang.basepassport.user.common.UserLoginSessionUtil;
import com.shunwang.basepassport.user.exception.NotMatchExp;
import com.shunwang.util.lang.StringUtil;
import org.apache.commons.lang.StringUtils;

import com.shunwang.baseStone.context.BaseStoneContext;
import com.shunwang.baseStone.sso.context.ClientTicketContext;
import com.shunwang.baseStone.sso.core.constants.SSOConstants;
import com.shunwang.baseStone.sso.exception.ValidateExp;
import com.shunwang.basepassport.user.dao.MemberDao;
import com.shunwang.basepassport.user.pojo.Member;

/**
 * 客户端免登录通过接口登录时获取clientTicket接口
 * 
 * <AUTHOR>
 *
 */
public class ObtainClientTicketForInterfaceAction extends FreeLoginBaseAction {

	private static final long serialVersionUID = -2684727823718989257L;
	private String memberName;
	private Integer memberId ;
	private String clientTicket;

	protected Map<String, Object> process() throws Exception {
		
		Map<String, Object> result = createResultJSON();
		if (memberId == null && StringUtils.isBlank(memberName)) {
			throw new ParamNullExp("memberId或memberName");
		}
		Member member = StringUtil.isNotBlank(memberName) ?
				getMemberDao().getMember(memberName) : getMemberDao().getByMemberId(memberId);
		if (null == member) {
			throw new ValidateExp("member不存在");
		}
		if (memberId != null && !member.getMemberId().equals(memberId)){
			throw new NotMatchExp("memberId与memberName不匹配") ;
		}
		UserCheckUtil.checkMemberState(member.getMemberState(), member.getMemberName());
		UserLoginSessionUtil.ssoAuthCheck(member.getMemberName(), getSiteId());

		setClientTicket(ClientTicketContext.createClientTicket(member,siteId).toString());
		
		result.put("clientTicket", clientTicket);
		result.put("memberName", member.getMemberName());
		result.put("memberId", member.getMemberId());
		
		return result;
	}

	@Override
	protected void checkParam() throws Exception {
		if(StringUtils.isBlank(memberName) && null == memberId)
			throw new ValidateExp("参数[memberName]和[memberId]不能同时为空");
	}

	@Override
	protected String getSiteName() {
		return SSOConstants.FreeLogin.obtainClientTicketForInterface;
	}

	public String getMemberName() {
		return memberName;
	}

	public void setMemberName(String memberName) {
		this.memberName = memberName;
	}
	
	protected MemberDao getMemberDao() {
		return (MemberDao) BaseStoneContext.getInstance().getBean("memberDao");
	}

	public String getClientTicket() {
		return clientTicket;
	}

	public void setClientTicket(String clientTicket) {
		this.clientTicket = clientTicket;
	}

	public Integer getMemberId() {
		return memberId;
	}

	public void setMemberId(Integer memberId) {
		this.memberId = memberId;
	}
}
