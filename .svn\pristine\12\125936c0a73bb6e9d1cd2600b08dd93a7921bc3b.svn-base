/**
 * Created by min.da on 15-4-27.
 */
!function(window) {
    var that = {};

    that.goBack = function() {
        try {
            window.SWMobileSDK.onClose();
        } catch (e) {
            try{
                window.webkit.messageHandlers.onClose.postMessage({body:''});
            }catch(ex){
                window.history.back();
            }
        }
    };

    that.goHome = function() {
        window.location.href = $CONFIG.appServer + '/front/swpaysdk/myAccount.htm';
    };

    that.close = function() {
        try {
            window.SWMobileSDK.onClose();
        } catch (e) {
			try{
				window.webkit.messageHandlers.onClose.postMessage({body:''});
			}catch(ex){
				window.history.back();
			}
        }
    };

    that.goLogin = function() {
        try {
            window.SWMobileSDK.onBack();
        } catch (e) {
            try{
                window.webkit.messageHandlers.onBack.postMessage({body:''});
            }catch (ex){
                console.error(ex);
            }
        }
    };

    $('header.header-bar .back').on('click', function() {
        that.goBack();
    });

    $('#btnClose_damin_20150514').on('click', function() {
        that.close();
    });

    $(".btn").on("touchstart",function(){
        $(this).addClass(".active")
    }).on("touchend",function(){
        $(this).removeClass(".active")
    })

    window.MobileBase = that;
}(window);