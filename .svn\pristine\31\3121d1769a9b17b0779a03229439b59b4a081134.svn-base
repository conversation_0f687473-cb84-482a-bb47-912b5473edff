package com.shunwang.basepassport.user.response;

import com.google.gson.annotations.Expose;
import com.shunwang.baseStone.core.response.BaseStoneResponse;
import com.shunwang.baseStone.encrypt.Encrypt;
import com.shunwang.baseStone.encrypt.pojo.EncryptItem;
import com.shunwang.basepassport.user.pojo.MemberToken;
import com.shunwang.xmlbean.annotation.XmlInit;

import java.util.Date;

public class RefreshTokenResponse extends BaseStoneResponse {
	
	private RefreshTokenResult refreshTokenResult;

	public RefreshTokenResponse(MemberToken memberToken) {
		this.refreshTokenResult = new RefreshTokenResult();
		this.refreshTokenResult.memberName = "";
		this.refreshTokenResult.memberId = memberToken.getMemberId();
		this.refreshTokenResult.accessToken = memberToken.getAccessToken();
		this.refreshTokenResult.accessTokenTimeOutInterval = memberToken.getAccessTokenTimeOutInterval();
		this.refreshTokenResult.refreshToken = memberToken.getRefreshToken();
	}
	
	public String buildSign(){
		Encrypt  encrypt=new Encrypt();
		encrypt.addItem(new EncryptItem(this.refreshTokenResult.memberId+""));
		encrypt.addItem(new EncryptItem(this.refreshTokenResult.accessToken));
		encrypt.addItem(new EncryptItem(this.refreshTokenResult.accessTokenTimeOutInterval));
		encrypt.addItem(new EncryptItem(this.refreshTokenResult.refreshToken));
		return encrypt.buildSign();
	}
	
	public class RefreshTokenResult {
		@Expose
		private Integer memberId;
		@Expose
		private String memberName;
		@Expose
		private String accessToken;
		@Expose
		private Date accessTokenTimeOut;
		@Expose
		private String accessTokenTimeOutInterval;
		@Expose
		private String refreshToken;
		
		@XmlInit
		public Integer getMemberId() {
			return memberId;
		}
		public void setMemberId(Integer memberId) {
			this.memberId = memberId;
		}
		
		@XmlInit
		public String getMemberName() {
			return memberName;
		}
		public void setMemberName(String memberName) {
			this.memberName = memberName;
		}
		
		@XmlInit
		public String getAccessToken() {
			return accessToken;
		}
		public void setAccessToken(String accessToken) {
			this.accessToken = accessToken;
		}
		
		@XmlInit
		public Date getAccessTokenTimeOut() {
			return accessTokenTimeOut;
		}
		public void setAccessTokenTimeOut(Date accessTokenTimeOut) {
			this.accessTokenTimeOut = accessTokenTimeOut;
		}
		
		@XmlInit
		public String getAccessTokenTimeOutInterval() {
			return accessTokenTimeOutInterval;
		}
		public void setAccessTokenTimeOutInterval(String accessTokenTimeOutInterval) {
			this.accessTokenTimeOutInterval = accessTokenTimeOutInterval;
		}
		
		@XmlInit
		public String getRefreshToken() {
			return refreshToken;
		}
		public void setRefreshToken(String refreshToken) {
			this.refreshToken = refreshToken;
		}
		
	}

	@XmlInit(path="items/item")
	public RefreshTokenResult getRefreshTokenResult() {
		return refreshTokenResult;
	}

	public void setRefreshTokenResult(RefreshTokenResult refreshTokenResult) {
		this.refreshTokenResult = refreshTokenResult;
	}
	
}
