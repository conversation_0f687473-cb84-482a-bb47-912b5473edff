package com.shunwang.baseStone.sso.intfc;

import com.shunwang.baseStone.config.constants.ConfigOauthConstant;
import com.shunwang.baseStone.context.IPContext;
import com.shunwang.baseStone.core.util.SignTool;
import com.shunwang.baseStone.sso.intfc.pojo.SmsCode;
import com.shunwang.basepassport.key.common.UserRegisterKeyUtil;
import com.shunwang.basepassport.user.pojo.Member;
import com.shunwang.basepassport.user.pojo.MemberAccountBind;
import com.shunwang.basepassport.user.pojo.MemberOutSite;
import com.shunwang.basepassport.user.service.BaseXmlResponse;
import com.shunwang.basepassport.user.service.SaxParserHelper;
import com.shunwang.framework.exception.WinterException;
import com.shunwang.toolbox.util.lang.StringUtil;
import com.shunwang.util.date.DateUtil;
import com.shunwang.util.encrypt.Md5Encrypt;
import com.shunwang.util.json.GsonUtil;
import com.shunwang.util.net.HttpClientUtils;
import com.shunwang.util.net.HttpUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.charset.Charset;
import java.util.HashMap;
import java.util.Map;

/**
 * 调用interface 服务
 *
 * <AUTHOR>
 * @since 2018/11/06
 */
public class InterfaceService {
    private static final Logger log = LoggerFactory.getLogger(InterfaceService.class);

    private static final int TIMEOUT = 10000;

    private InterfaceConfig interfaceConfig;

    private static Map<String, String> mappingForMember = new HashMap<>();

    static {
        mappingForMember.put("userId", "memberId");
        mappingForMember.put("userName", "memberName");
    }

    /**
     * interfaceType: 13
     *
     * @param params
     * @return
     */
    public BaseXmlResponse sendForMobileRegister(Map<String, String> params) {
        buildCommonParam(params, interfaceConfig.getSendForMobileRegisterMd5Key());
        String responseString;
        try {
            responseString = HttpClientUtils.doPost(interfaceConfig.getSendForMobileRegisterUrl(), params,
                    Charset.forName("utf-8"), TIMEOUT, TIMEOUT, TIMEOUT);
            if (log.isDebugEnabled()) {
                log.debug("interface请求参数:" + interfaceConfig.getSendForMobileRegisterUrl() + "\t" + GsonUtil.toJson(params));
            }
            log.info("手机短信快捷登录发送短信验证码返回信息为:" + responseString);
            return parseResult(responseString);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw e;
        }
    }

    /**
     * interfaceType: 17
     *
     * @param params
     * @return
     */
    public BaseXmlResponse sendForSingleAccountBind(Map<String, String> params) {
        buildCommonParam(params, interfaceConfig.getSendForSingleAccountBindMd5Key());
        String responseString;
        try {
            responseString = HttpClientUtils.doPost(interfaceConfig.getSendForSingleAccountBindUrl(), params,
                    Charset.forName("utf-8"), TIMEOUT, TIMEOUT, TIMEOUT);
            if (log.isDebugEnabled()) {
                log.debug("interface请求参数:" + interfaceConfig.getSendForSingleAccountBindUrl() + "\t" + GsonUtil.toJson(params));
            }
            log.info("单账号绑定发送短信验证码返回信息为:" + responseString);
            return parseResult(responseString);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw e;
        }
    }

    /**
     * bindType = 1
     *
     * @param params
     * @return
     */
    public BaseXmlResponse confirmForLogin(Map<String, String> params) {
        buildCommonParam(params, interfaceConfig.getConfirmForLoginMd5Key());
        String responseString;
        try {
            responseString = HttpClientUtils.doPost(interfaceConfig.getConfirmForLoginUrl(), params,
                    Charset.forName("utf-8"), TIMEOUT, TIMEOUT, TIMEOUT);
            if (log.isDebugEnabled()) {
                log.debug("interface请求参数:" + interfaceConfig.getConfirmForLoginUrl() + "\t" + GsonUtil.toJson(params));
            }
            return parseResult(responseString);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return newErrorResponse();
        }
    }

    /**
     * bindType = 1
     *
     * @param params
     * @return
     */
    public BaseXmlResponse confirmForRegNoActiveNo(Map<String, String> params) {
        buildCommonParam(params, interfaceConfig.getConfirmForRegNoActiveNo5Key());
        String responseString;
        try {
            responseString = HttpClientUtils.doPost(interfaceConfig.getConfirmForRegNoActiveNoUrl(), params,
                    Charset.forName("utf-8"), TIMEOUT, TIMEOUT, TIMEOUT);
            if (log.isDebugEnabled()) {
                log.debug("interface请求参数:" + interfaceConfig.getConfirmForRegNoActiveNoUrl() + "\t" + GsonUtil.toJson(params));
            }
            return parseResult(responseString);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return newErrorResponse();
        }
    }

    /**
     * bindType: 5
     *
     * @param params
     * @return
     */
    public BaseXmlResponse confirmForSingleAccountBind(Map<String, String> params) {
        buildCommonParam(params, interfaceConfig.getConfirmForSingleAccountBindMd5Key());
        String responseString;
        try {
            responseString = HttpClientUtils.doPost(interfaceConfig.getConfirmForSingleAccountBindUrl(), params,
                    Charset.forName("utf-8"), TIMEOUT, TIMEOUT, TIMEOUT);
            if (log.isDebugEnabled()) {
                log.debug("interface请求参数:" + interfaceConfig.getConfirmForSingleAccountBindUrl() + "\t" + GsonUtil.toJson(params));
            }
            return parseResult(responseString);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 调用通过外部账户注册用户接口
     *
     * @param memberOutSite
     * @return member对象
     */
    public Member outSiteMemberRegister(MemberOutSite memberOutSite) {
        Map<String, String> params = buildParam(memberOutSite);
        try {
            try {
                HttpClientUtils.doPost(interfaceConfig.getOutSiteMemberRegisterUrl(), params,
                        Charset.forName("utf-8"), TIMEOUT, TIMEOUT, TIMEOUT);
                if (log.isDebugEnabled()) {
                    log.debug("interface请求参数:" + interfaceConfig.getOutSiteMemberRegisterUrl() + "\t" + GsonUtil.toJson(params));
                }
            } catch (Exception e) {
                log.error("调用interface接口注册失败", e);
            }
            //调用接口注册结束后直接从缓存中获取用户信息
            return UserRegisterKeyUtil.getByMemberName(memberOutSite.getMemberName());
            //BaseXmlResponse<Member> response = SaxParserHelper.parse(responseString, Member.class,
            //mappingForMember);
            //if (response.isSuccess()) {
            //    return response.getValue();
            //}
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }


    /**
     * 保存外部站点账户信息接口
     *
     * @param memberOutSite
     * @return
     */
    public boolean updateMemberOutSite(MemberOutSite memberOutSite) {
        Map<String, String> params = buildParamForUpdate(memberOutSite);
        String responseString;
        try {
            responseString = HttpUtil.doPost(interfaceConfig.getUpdateOutSiteMemberUrl(), params, "UTF-8");
            if (log.isDebugEnabled()) {
                log.debug("interface请求参数:" + interfaceConfig.getUpdateOutSiteMemberUrl() + "\t" + GsonUtil.toJson(params));
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return false;
        }
        BaseXmlResponse response = parseResult(responseString);
        return response.isSuccess();
    }

    /**
     * @param memberAccountBind
     * @return
     */
    public boolean singleAccountBindExt(MemberAccountBind memberAccountBind) {
        Map<String, String> params = buildParamForSingleAccountBindExt(memberAccountBind);
        String responseString;
        try {
            responseString = HttpUtil.doPost(interfaceConfig.getUpdateSingleAccountBindExtUrl(), params, "UTF-8");
            if (log.isDebugEnabled()) {
                log.debug("interface请求参数:" + interfaceConfig.getUpdateSingleAccountBindExtUrl() + "\t" + GsonUtil.toJson(params));
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return false;
        }
        BaseXmlResponse response = parseResult(responseString);
        return response.isSuccess();
    }


    /**
     * 动态密码短信发送内部接口
     *
     * @param member
     * @return
     */
    public BaseXmlResponse<SmsCode> sendDynamicPassword(Member member) {
        Map<String, String> params = buildParamForSendDynamicPassword(member);
        String responseString;
        try {
            responseString = HttpUtil.doPost(interfaceConfig.getSendDynamicPasswordInnerUrl(), params, "UTF-8");
            if (log.isDebugEnabled()) {
                log.debug("interface请求参数:" + interfaceConfig.getSendDynamicPasswordInnerUrl() + "\t" + GsonUtil.toJson(params));
            }
            return SaxParserHelper.parse(responseString, SmsCode.class);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }


    /**
     * 动态密码短信验证内部接口
     *
     * @param member
     * @param verifyCode
     * @return
     */
    public BaseXmlResponse<SmsCode> dynamicSecurityValidate(Member member, String verifyCode) {
        Map<String, String> params = buildParamForValidateDynamicPassword(member, verifyCode);
        String responseString;
        try {
            responseString = HttpUtil.doPost(interfaceConfig.getDynamicSecurityValidateInnerUrl(), params, "UTF-8");
            if (log.isDebugEnabled()) {
                log.debug("interface请求参数:" + interfaceConfig.getDynamicSecurityValidateInnerUrl() + "\t" + GsonUtil.toJson(params));
            }
            return SaxParserHelper.parse(responseString, SmsCode.class);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public boolean updateMember(Member member) {
        Map<String, String> params = new HashMap<>();
        params.put("memberId", member.getMemberId().toString());
        params.put("nickName", member.getNickName());
        params.put("headImg", member.getHeadImg());

        buildCommonParam(params, interfaceConfig.getUpdateMemberExtMd5Key());
        String responseString;
        try {
            responseString = HttpUtil.doPost(interfaceConfig.getUpdateMemberExtUrl(), params, "UTF-8");
            if (log.isDebugEnabled()) {
                log.debug("interface请求参数:" + interfaceConfig.getUpdateMemberExtUrl() + "\t" + GsonUtil.toJson(params));
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new WinterException("调用interface接口更新用户信息失败");
        }
        BaseXmlResponse response = parseResult(responseString);
        return response.isSuccess();
    }

    public boolean updateMemberSpecialType(Member member) {
        Map<String, String> params = new HashMap<>();
        params.put("memberId", member.getMemberId().toString());
        params.put("memberSpecialType", member.getMemberSpecialType().toString());

        buildCommonParam(params, interfaceConfig.getUpdateMemberExtMd5Key());
        String responseString;
        try {
            responseString = HttpUtil.doPost(interfaceConfig.getUpdateMemberExtUrl(), params, "UTF-8");
            if (log.isDebugEnabled()) {
                log.debug("interface请求参数:" + interfaceConfig.getUpdateMemberExtUrl() + "\t" + GsonUtil.toJson(params));
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new WinterException("调用interface接口更新用户信息失败");
        }
        BaseXmlResponse response = parseResult(responseString);
        return response.isSuccess();
    }


    public boolean verifyToken(Integer memberId, String accessToken) {
        Map<String, String> params = new HashMap<>();
        params.put("memberId", String.valueOf(memberId));
        params.put("accessToken", accessToken);

        buildCommonParam(params, interfaceConfig.getUpdateMemberExtMd5Key());
        String responseString;
        try {
            responseString = HttpUtil.doPost(interfaceConfig.getUpdateMemberExtUrl(), params, "UTF-8");
            if (log.isDebugEnabled()) {
                log.debug("interface请求参数:" + interfaceConfig.getUpdateMemberExtUrl() + "\t" + GsonUtil.toJson(params));
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new WinterException("调用interface接口更新用户信息失败");
        }
        BaseXmlResponse response = parseResult(responseString);
        return response.isSuccess();
    }


    // ------------------------------------build params start-------------------------------------------------

    private BaseXmlResponse newErrorResponse() {
        BaseXmlResponse response = new BaseXmlResponse();
        response.setMsgId("-1");
        return response;
    }


    /**
     * outSiteMemberRegister
     *
     * @param memberOutSite
     * @return
     */
    private Map<String, String> buildParam(MemberOutSite memberOutSite) {
        Map<String, String> map = new HashMap<>(32);
        map.put("outMemberId", memberOutSite.getOutMemberId());
        map.put("nickName", memberOutSite.getNickName());
        map.put("memberName", memberOutSite.getMemberName());
        map.put("outMemberName", memberOutSite.getOutMemberName());
        map.put("headImage", memberOutSite.getHeadImg());
        map.put("regFrom", memberOutSite.getRegFrom());
        map.put("version", memberOutSite.getVersion());
        map.put("env", memberOutSite.getEnv());
        map.put("remark", memberOutSite.getRemark());
        map.put("memberFrom", memberOutSite.getMemberFrom());
        //不加这个参数interface 注册的时候获取的就是sso的ip地址
        map.put("clientIp", IPContext.getIp());
        map.put("level", memberOutSite.getLevel());

        buildCommonParam(map, interfaceConfig.getOutSiteMemberRegisterMd5Key());

        return map;
    }

    /**
     * outSiteMemberRegister
     * 实际只用到了 oldOutMemberId,newOutMemberId, outMemberId(条件)
     *
     * @param memberOutSite
     * @return
     */
    private Map<String, String> buildParamForUpdate(MemberOutSite memberOutSite) {
        Map<String, String> map = new HashMap<>(16);
        map.put("outMemberId", memberOutSite.getOutMemberId());
        map.put("oldOutMemberId", memberOutSite.getOldOutMemberId());
        map.put("newOutMemberId", memberOutSite.getNewOutMemberId());
        map.put("memberFrom", memberOutSite.getMemberFrom());
        //map.put("outMemberName", memberOutSite.getOutMemberName());
        //map.put("nickName", memberOutSite.getNickName());
        //map.put("memberName", memberOutSite.getMemberName());
        //map.put("memberId", memberOutSite.getMemberId().toString());
        //map.put("regFrom", memberOutSite.getRegFrom());
        //map.put("level", memberOutSite.getLevel());

        buildCommonParam(map, interfaceConfig.getUpdateOutSiteMemberMd5Key());

        return map;
    }

    private Map<String, String> buildParamForSendDynamicPassword(Member member) {
        Map<String, String> map = new HashMap<>();
        map.put("userName", member.getMemberName());

        buildCommonParam(map, interfaceConfig.getSendDynamicPasswordInnerMd5Key());
        return map;
    }


    private Map<String, String> buildParamForValidateDynamicPassword(Member member, String verifyCode) {
        Map<String, String> map = new HashMap<>();
        map.put("userName", member.getMemberName());
        map.put("verifyCode", verifyCode);

        buildCommonParam(map, interfaceConfig.getDynamicSecurityValidateInnerMd5Key());
        return map;
    }

    private Map<String, String> buildParamForSingleAccountBindExt(MemberAccountBind memberAccountBind) {
        Map<String, String> map = new HashMap<>();
        map.put("memberId", memberAccountBind.getMemberId().toString());
        map.put("phone", memberAccountBind.getPhone());

        if (memberAccountBind.getQq() != null) {
            map.put("type", ConfigOauthConstant.TYPE.QQ.getInt().toString());
            map.put("unionId", memberAccountBind.getQq().toString());
        } else if (memberAccountBind.getWeixin() != null) {
            map.put("type", ConfigOauthConstant.TYPE.WEIXIN.getInt().toString());
            map.put("unionId", memberAccountBind.getWeixin().toString());
        } else if (memberAccountBind.getWeibo() != null) {
            map.put("type", ConfigOauthConstant.TYPE.WEIBO.getInt().toString());
            map.put("unionId", memberAccountBind.getWeibo().toString());
        } else if (memberAccountBind.getApple() != null) {
            map.put("type", ConfigOauthConstant.TYPE.APPLE.getInt().toString());
            map.put("unionId", memberAccountBind.getApple().toString());
        } else if (memberAccountBind.getAlipay() != null) {
            map.put("type", ConfigOauthConstant.TYPE.ALIPAY.getInt().toString());
            map.put("unionId", memberAccountBind.getAlipay().toString());
        }

        buildCommonParam(map, interfaceConfig.getUpdateSingleAccountBindExtMd5Key());
        return map;
    }

    /**
     * 接口公共参数
     *
     * @param map
     * @param md5Key
     */
    private void buildCommonParam(Map<String, String> map, String md5Key) {
        map.put("siteId", interfaceConfig.getSiteId());
        map.put("time", DateUtil.getCurrentDateStamp());
        map.put("signVersion", "1.0");

        String signSource = SignTool.buildSignStringSorted(map, "sign", md5Key);
        String sign = Md5Encrypt.encrypt(signSource).toUpperCase();
        map.put("sign", sign);
    }


    // ------------------------------------build params end------------------------------------------------
    // ------------------------------------parse result start------------------------------------------------

    private BaseXmlResponse parseResult(String responseString) {
        if (StringUtil.isBlank(responseString)) {
            throw new WinterException("请求interface网络异常，返回为空");
        }
        try {
            return SaxParserHelper.parse(responseString);
        } catch (Exception e) {
            log.error("解析返回结果出错:" + responseString, e);
            throw new WinterException("解析返回结果出错:" + responseString);
        }
    }

    // ------------------------------------parse result end------------------------------------------------

    public InterfaceConfig getInterfaceConfig() {
        return interfaceConfig;
    }

    public void setInterfaceConfig(InterfaceConfig interfaceConfig) {
        this.interfaceConfig = interfaceConfig;
    }
}
