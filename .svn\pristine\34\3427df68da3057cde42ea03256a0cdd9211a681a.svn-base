package com.shunwang.passport.find.web;

import com.shunwang.baseStone.cache.CacheKeyConstant;
import com.shunwang.baseStone.context.BaseStoneContext;
import com.shunwang.baseStone.context.RedisContext;
import com.shunwang.baseStone.key.SWKey;
import com.shunwang.baseStone.key.SWKeyContext;
import com.shunwang.basepassport.context.UserContext;
import com.shunwang.basepassport.find.common.AppealConstants;
import com.shunwang.basepassport.find.dao.AppealDao;
import com.shunwang.basepassport.find.pojo.Appeal;
import com.shunwang.basepassport.find.pojo.AppealExtendMsg;
import com.shunwang.basepassport.user.common.PwdUtil;
import com.shunwang.passport.common.context.DomainContext;
import com.shunwang.util.lang.StringUtil;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 *
 * ****************************
 * 版权所有：顺网科技 保留所有权利
 * 创建日期: 2011-8-24  下午05:52:33
 * 创建作者：陈积慧
 * 文件名称：AppealActuPwdAction.java
 * 版本： 1.0
 * 功能：通过实名认证  找回密码  找回密保问题   密保卡
 * 最后修改时间：
 * 修改记录：
 ***************************************
 */
public class FindActuAction extends AppealAction{

	/**
	 *
	 */
	private static final long serialVersionUID = 1465974679797482455L;
	private Appeal appeal;
	private String password;
	private File attachment;
	private String appealResult ;
	public static final long maxSize = 2048576; //允许的图片最大值
	private String idCardType; //front or back
	private String FRONT_iD_CARD="/images/findByActuality/person/idcardFront";
	private List<AppealExtendMsg>extList=null;

	/**
	 * 账户申诉中心
	 * @return
	 */
	public String appealCenter() throws IOException {
		if(UserContext.getMember()!=null){
			getResponse().sendRedirect(DomainContext.getIdentityServer() + "/appeal/step1?memberName=" + UserContext.getMember().getMemberName());
		} else {
			getResponse().sendRedirect(DomainContext.getIdentityServer() + "/appeal/step1");
		}
		return null;
	}

	/**
	 * ***********
	 * 创建日期: 2011-8-24  下午05:54:26
	 * 创建作者：chenjh
	 * @return
	 * 功能：携带用户认证绑定状态跳转
	 *************
	 */
	public String goActuFind(){
		if(null!=UserContext.getUserName()){
			this.getUserState(UserContext.getUserName());
		}
		else
			this.getUserState(appeal.getUserName());

		return SUCCESS;
	}


	/**
	 * ***********
	 * 创建日期: 2011-8-25  上午10:22:16
	 * 创建作者：chenjh
	 * @return
	 * 功能： 下一步
	 *************
	 */
	public String goNextStep(){

		return SUCCESS;
	}



	/**
	 * ***********
	 * 创建日期: 2011-8-25  上午09:54:26
	 * 创建作者：chenjh
	 * @return
	 * 功能：封装用户信息 提交 密码      密保卡 申诉信息
	 *************
	 */
	@Override
	public String commitAppealMsg() {
		memberName = UserContext.getUserName() ;
		if(StringUtil.isEmpty(memberName)){
			memberName = getAppeal().getUserName() ;
		}
		String key = CacheKeyConstant.PassportActu.FIND_ACTU_MSG + memberName ;
		if (!RedisContext.getRedisCache().set(key , " ",5, TimeUnit.SECONDS)) {
			appealResult = "申述单已经提交，不能重复提交" ;
			return "appealResult" ;
		}
		//检查是否已经提交
		AppealDao appealDao=(AppealDao) BaseStoneContext.getInstance().getBean("appealDao");
		Appeal appealTemp = appealDao.findRecentAppeal(memberName);

		//如果有申诉,而且正在审核中,则显示申诉审批结果
		if (appealTemp != null && appealTemp.getAppealState().equals("1")) {
			appealResult = "您的通行证账号" + memberName + "的申诉已经受理，我们将在1-3个工作日内进行审核，请留意您的邮箱或手机提醒！";
			RedisContext.getRedisCache().del(key) ;
			return "appealResult";
		}

		String strMd5NewPwd = PwdUtil.convertMd5(password);
		AppealExtendMsg  appealExt=new AppealExtendMsg(AppealConstants.APPEAL_TYPE_PWD,strMd5NewPwd);
		appeal.setAppealType(AppealConstants.FIND_TYPE_FOUR);
		extList=new ArrayList<>();
		extList.add(appealExt);
		appeal.setExtList(extList);
		try {
			appeal.appealMsg();
			SWKeyContext.put(new SWKey("showAppealQueryResult"));
		} catch (Exception e) {
			setErrorMsg("实名认证找回失败，请重试！");
			log.error(memberName + "实名认证找回失败",e);
			return INPUT;
		}finally {
			RedisContext.getRedisCache().del(key) ;
		}

		return SUCCESS;
	}


	/**
	 * ***********
	 * 创建日期: 2011-8-30  下午02:52:24
	 * 创建作者：chenjh
	 * @return
	 * 功能：实名认证找回封装 密保问题信息
	 *************
	 */
	public String commintMsgActuFindQue(){
		if(appeal == null && org.apache.commons.lang.StringUtils.isBlank(memberName)){
			setErrorMsg("账号不存在");
			return INPUT;
		}
		if(StringUtil.isNotBlank(memberName))
			appeal.setUserName(memberName);
		else
			memberName=appeal.getUserName();
		String key = "commit_msg_actu_find_question_lock_"+memberName;
		try {

			if(! RedisContext.getRedisCache().setNx(key," ", 5)){
				log.warn("请匆重复操作");
				return SUCCESS; //直接跳转成功页面
			}

			//查找是否申诉中
			AppealDao appealDao=(AppealDao)BaseStoneContext.getInstance().getBean("appealDao");
			Appeal appealTemp = appealDao.findRecentAppealForQuestion(memberName);

			//如果有申诉,而且正在审核中,则显示申诉审批结果
			if (appealTemp != null && AppealConstants.CHECKSTATE_WATITING.equals(appealTemp.getAppealState())) {
				log.warn("请匆重复操作");
				return SUCCESS; //直接跳转成功页面
			}

			//封装扩展信息
			AppealExtendMsg appealExtOne=new AppealExtendMsg(this.getQuestion1()+"", this.getAnswer1());
			AppealExtendMsg appealExtTwo=new AppealExtendMsg(this.getQuestion2()+"", this.getAnswer2());
			AppealExtendMsg appealExtThree=new AppealExtendMsg(this.getQuestion3()+"", this.getAnswer3());
			extList=new ArrayList<AppealExtendMsg>();
			extList.add(appealExtOne);
			extList.add(appealExtTwo);
			extList.add(appealExtThree);
			appeal.setAppealType(AppealConstants.FIND_TYPE_FIFE);
			appeal.setExtList(extList);

			SWKeyContext.get("findByActu");
			this.checkData();//再次检查问题
			if(UserContext.getUser()!=null)
				appeal.setUserName(UserContext.getUserName());
			appeal.appealMsg();
		} catch (Exception e) {
			super.initRegMsg();
			setErrorMsg(e.getMessage());
			log.error("commintMsgActuFindQue 出错",e);
			return INPUT;
		}finally {
			RedisContext.getRedisCache().del(key);
		}
		return SUCCESS;
	}



	public String findQueNext(){
		SWKeyContext.put(new SWKey("findByActu"));
		String flag=getRequest().getParameter("flag");
		if(null!=flag&&"card".equals(flag)){
			return "findCard";
		}else if(null!=flag&&"question".equals(flag)){
			return "findQuestion";
		}
		return INPUT;
	}



	/**
	 * <AUTHOR>
	 * @作用 身份证上传
	 * @date 2011-04-19
	 * @return
	 */
//    public String uploadImage() throws Exception {
//
//    	if(attachment == null){
//    		setErrorMsg("请选择要上传的图片，大小不能超过2M");
//    		return ERROR;
//    	}
//    	if(attachment.length() > maxSize){
//    		setErrorMsg("图片大小不能超过2M，请重新选择");
//    		return ERROR;
//    	}
//
//    	if(idCardType == null ||idCardType.equals("")){
//    		this.getResponse().getWriter().write(ERROR);
//	    	return null;
//    	}
//		String imageName = idCardType+ DateUtil.getCurrentDateStamp() + ".jpg";
//		UploadFile uploadFile=new UploadFile();
//		String path="";
//		if(idCardType.equals("front")){
//			path=FileUtil.getRootPath()+FRONT_iD_CARD;
//		}
//		uploadFile.upload(attachment, path, imageName);
//		this.getResponse().getWriter().write(SUCCESS+"|"+imageName+"|"+FRONT_iD_CARD);
//		return null;
//    } 


	public Appeal getAppeal() {
		return appeal;
	}

	public void setAppeal(Appeal appeal) {
		this.appeal = appeal;
	}

	public File getAttachment() {
		return attachment;
	}

	public void setAttachment(File attachment) {
		this.attachment = attachment;
	}

	public String getIdCardType() {
		return idCardType;
	}

	public void setIdCardType(String idCardType) {
		this.idCardType = idCardType;
	}

	public String getFRONT_iD_CARD() {
		return FRONT_iD_CARD;
	}

	public String getPassword() {
		return password;
	}


	public void setPassword(String password) {
		this.password = password;
	}


	public void setFRONT_iD_CARD(String fRONTIDCARD) {
		FRONT_iD_CARD = fRONTIDCARD;
	}
	public static long getMaxsize() {
		return maxSize;
	}


	public List<AppealExtendMsg> getExtList() {
		return extList;
	}


	public void setExtList(List<AppealExtendMsg> extList) {
		this.extList = extList;
	}

	public String getAppealResult() {
		return appealResult;
	}

	public void setAppealResult(String appealResult) {
		this.appealResult = appealResult;
	}
}
