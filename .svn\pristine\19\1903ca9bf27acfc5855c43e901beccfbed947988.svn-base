package com.shunwang.passport.actu.exception;

import com.shunwang.baseStone.core.exception.BaseStoneException;
/**
 * 不能还原异常
 * @Description:不能撤销 不能后退 不能还原异常
 * <AUTHOR>  create at 2011-9-3 下午09:03:02
 * @FileName com.shunwang.passport.actu.exception.CanNotUnDoExp.java
 */
public class CanNotUnDoExp extends BaseStoneException {

	/**
	 * <AUTHOR> create at 2011-9-3 下午08:14:22 
	 */
	private static final long serialVersionUID = -1557070496280539491L;
	
	public CanNotUnDoExp(String warnMsg) {
		super("88",warnMsg);
	}

}
