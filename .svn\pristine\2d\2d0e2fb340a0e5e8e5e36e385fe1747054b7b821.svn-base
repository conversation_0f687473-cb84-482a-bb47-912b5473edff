<%@ page contentType="text/html;charset=UTF-8"%>
<%@ include file="/common/taglibs.jsp"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta name="Keywords" content="顺网通行证、 密码 、找回 、申诉" />
<meta name="Description" content="申诉找回密码需要先填写基本资料。" />

<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>顺网通行证-找回密码-申诉找回</title>

<script type="text/javascript" src="${staticServer}/scripts/common/tabindex.js"></script>
<script type="text/javascript" src="${staticServer}/scripts/front/member/min/validation.min.js"></script>
<script type="text/javascript" src="${staticServer}/scripts/front/find/globalAppeal_front_a.js"></script>

<script type="text/javascript">
  function goQuestionFind(){
      var form1 = document.getElementById("form1");
      form1.action="/front/noLogin/findPwd_question_goQuestion.htm";
     // form.submit();
      document.forms[0].submit();
  }
   function gokamiFind(){
      var form1 = document.getElementById("form1");
      form1.action="/front/noLogin/pwdFind_safeCard_createPosition.htm";
     // form1.submit();
      document.forms[0].submit();
  }
function tosubmit3(){ 
	    var form1 = document.getElementById("form1");
	    form1.action="/front/noLogin/findPwd_byActu.htm"	    
		document.forms[0].submit();
	}
</script>


</head>
<body>
<%@ include file="global_nav.jsp" %>
<div class="c_body forget_s02">
    <ul class="step_bar">
        <li><em>1</em> 填写账号信息</li>
        <li><em>2</em> 设置新密码</li>
    </ul>
    <div class="form_group">
        <form id="form1" action="/front/noLogin/pwdFind_appeal_front.htm" method="post">
            <input type="hidden" id="positionOrg1" name="appeal.questionIsBind" value="${appeal.questionIsBind }"/>
            <input type="hidden" id="positionOrg2" name="appeal.safeCardIsBind" value="${appeal.safeCardIsBind }"/>
            <input type="hidden" name="appeal.userName" id="userName" value="${memberName}"/>
            <input type="hidden" name="memberName" value="${memberName}"/>
            <input type="hidden" name="memberType" id="memberType" value="${memberType }"/>
            <%@ include file="/front/find/globalAppeal_front_a.jsp" %>
        </form>
    </div>
</div>
</body>
</html>
