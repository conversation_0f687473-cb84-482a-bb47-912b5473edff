package com.shunwang.basepassport.user.exception;

import com.shunwang.baseStone.core.exception.BaseStoneException;
import com.shunwang.basepassport.binder.common.BinderConstants;

/**
 * Copyright© 2005-2014 shunwang. All Rights Reserved.
 * author: <EMAIL>
 * date: 14-10-21
 * time: 下午5:21
 * since:
 */
public class MobileBindLimitExp extends BaseStoneException {

    private static final long serialVersionUID = -3349465967072822008L;

    public MobileBindLimitExp() {
        super("1052", "该手机号码已绑定过" + BinderConstants.getMobileBinderLimit() + "个通行证");
    }

    public MobileBindLimitExp(String errMsg){
        super("1052",errMsg) ;
    }
}
