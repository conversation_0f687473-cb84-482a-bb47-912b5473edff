package com.shunwang.basepassport.manager.request.risk;

import com.shunwang.basepassport.config.pojo.ConfigInterface;
import com.shunwang.basepassport.manager.HttpMethod;
import com.shunwang.basepassport.manager.InterfaceConstant;
import com.shunwang.basepassport.manager.bean.RiskParam;
import com.shunwang.basepassport.manager.request.BaseRequest;
import com.shunwang.basepassport.manager.response.risk.RiskResponse;
import com.shunwang.basepassport.manager.util.SignTool;
import com.shunwang.util.date.DateUtil;
import com.shunwang.util.encrypt.Md5Encrypt;
import com.shunwang.util.json.GsonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * 大数据统一采集服务接口
 */
public class RiskRequest extends BaseRequest<RiskResponse> {
    private static final Logger LOGGER = LoggerFactory.getLogger(RiskRequest.class);


    private String businessKey;
    private String serviceKey;
    private String secret;

    private RiskParam riskParam;

    @Override
    public Map<String, String> buildParams(){
        Map<String, String> params = new HashMap<>();
        params.put("businessKey", getBusinessKey());
        params.put("service", getServiceKey());
        params.put("time", DateUtil.getCurrentDateStamp());
        params.put("data", GsonUtil.toJson(riskParam));

        String signSource = SignTool.buildSignStringSortedMap(params, "sign", secret);
        String sign = Md5Encrypt.encrypt(signSource ,  SignTool.ENCODE);
        params.put("sign", sign);
        return params;
    }

    @Override
    public Class<RiskResponse> getResponseClass() {
        return RiskResponse.class;
    }

    @Override
    public Integer getInterfaceKey() {
        return InterfaceConstant.INTERFACE_KEY_RISK_REPORT;
    }

    @Override
    public void doInterfaceSetting(ConfigInterface setting) {
        setUrl(setting.getInterfaceUrl1());
        setBusinessKey(setting.getInterfacePartnerId());
        setServiceKey(setting.getInterfaceEmail());
        setSecret(setting.getInterfaceMd5Key());
    }

    @Override
    public HttpMethod getHttpMethod() {
        return HttpMethod.POST;
    }

    public String getBusinessKey() {
        return businessKey;
    }

    public void setBusinessKey(String businessKey) {
        this.businessKey = businessKey;
    }

    public String getServiceKey() {
        return serviceKey;
    }

    public void setServiceKey(String serviceKey) {
        this.serviceKey = serviceKey;
    }

    public String getSecret() {
        return secret;
    }

    public void setSecret(String secret) {
        this.secret = secret;
    }

    public RiskParam getRiskParam() {
        return riskParam;
    }

    public void setRiskParam(RiskParam riskParam) {
        this.riskParam = riskParam;
    }
}
