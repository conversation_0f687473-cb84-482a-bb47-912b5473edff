package com.shunwang.baseStone.core.exception;

import com.shunwang.basepassport.binder.common.ErrorCode;

public class BaseStoneException extends RuntimeException {

	private static final long serialVersionUID = -7126182747920218295L;
	private String msgId;
	private String msg;
	public String getMsgId() {
		return msgId;
	}
	public void setMsgId(String msgId) {
		this.msgId = msgId;
	}
	public String getMsg() {
		return msg;
	}
	public BaseStoneException(String msgId, String msg) {
		super();
		this.msgId = msgId;
		this.msg = msg;
	}

	public BaseStoneException(ErrorCode errorCode) {
		super();
		this.msgId = errorCode.getCode();
		this.msg = errorCode.getDescription();
	}
	public void setMsg(String msg) {
		this.msg = msg;
	}
	
	
	public BaseStoneException(){
		
	}
	@Override
	public String getMessage() {
		return this.getMsg();
	}
	
}
