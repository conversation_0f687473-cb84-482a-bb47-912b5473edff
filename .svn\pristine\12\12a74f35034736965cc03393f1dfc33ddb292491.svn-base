package com.shunwang.basepassport.user.web;

import com.shunwang.baseStone.context.BaseStoneContext;
import com.shunwang.baseStone.core.response.BaseStoneResponse;
import com.shunwang.baseStone.core.util.SignTool;
import com.shunwang.basepassport.config.pojo.SiteInterface;
import com.shunwang.basepassport.config.common.SiteInterfaceUtil;
import com.shunwang.basepassport.user.common.MemberConstants;
import com.shunwang.basepassport.user.common.UserCheckUtil;
import com.shunwang.basepassport.user.dao.MemberDao;
import com.shunwang.basepassport.user.exception.MsgIllFormateExp;
import com.shunwang.basepassport.user.exception.MsgNullExp;
import com.shunwang.basepassport.user.pojo.Member;
import com.shunwang.basepassport.user.response.CustomServiceResponse;
import com.shunwang.util.encrypt.Md5Encrypt;
import com.shunwang.util.lang.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.struts2.ServletActionContext;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.List;

/**
 * 在线客服接口
 * <p>本接口只返回json格式响应</p>
 *
 * <AUTHOR>
 * @since 2016-01-07
 */
public class CustomServiceAction extends MemberAction {

    private String account;
    private String phone;

    @Override
    public void doProcess() {

    }

    /**
     * 在线客服接口,仅account,phone，不增加memberId参数
     */
    @Override
    public void process() {
        CustomServiceResponse response = new CustomServiceResponse();
        response.setNeedSign(false);

        if (StringUtil.isNotBlank(account)) {
            //account 已经赋值到 getMemberName()
            Member member = super.getMemberInfo();
            if (member != null) {
                member.setMemberInfo(member.loadMemberInfo());
                response.addCustomer(member);
            }else {
                log.warn("账号[" + account + "]没有匹配的通行证用户");
            }
        } else {
            List<Member> members = getMemberDao().getListByMobile(phone);
            if (members != null && !members.isEmpty()) {
                for (Member member : members) {
                    member.setMemberInfo(member.loadMemberInfo());
                    response.addCustomer(member);
                }
            } else {
                log.warn("手机号[" + phone +"]没有匹配的通行证用户");
            }
        }
        this.setBaseResponse(response);
    }

    @Override
    public BaseStoneResponse createResponse(){
        return new CustomServiceResponse();
    }

    @Override
    public void checkParam() {
        if (StringUtil.isBlank(account) && StringUtils.isBlank(phone)) {
            throw new MsgNullExp("用户名和手机号");
        }
        if (StringUtils.isNotBlank(phone) && !UserCheckUtil.checkMobile(phone)) {
            throw new MsgIllFormateExp("无效手机号格式");
        }
    }

    @Override
    public String getSiteName() {
        return MemberConstants.CUSTOM_SERVICE;
    }


    @Override
    public String buildSignString() {
        SiteInterface site = SiteInterfaceUtil.loadSiteInterface();
        return getSiteId() + "|" + getTime() + "|" + site.getMd5Key()
                + "|" + StringUtil.trimNull(account) + "|" + StringUtil.trimNull(phone);
    }

    public String createSignString() {

        if(SignTool.isUpdateVersion(getSignVersion())){
            return super.createSignString();
        }

        String ret = "";
        try {
            String sign = this.buildSignString();
            log.warn("request请求加密前字符串:" + sign);
            ret = URLEncoder.encode(sign, "utf-8").toUpperCase();
        } catch (UnsupportedEncodingException e) {
            log.error("对签名转码错误", e);
        }
        ret = Md5Encrypt.encrypt(ret).toUpperCase();
        return ret;
    }

    private MemberDao getMemberDao() {
        return (MemberDao) BaseStoneContext.getInstance().getBean("memberDao");
    }


    public String getAccount() {
        return account;
    }


    @Override
    public String getMemberName() {
        return account;
    }



    public void setAccount(String account) {
        String method = ServletActionContext.getRequest().getMethod();
        if ("GET".equalsIgnoreCase(method)) {
            this.account = doUrlEncode(account);
        } else {
            this.account = account;
        }
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }
}
