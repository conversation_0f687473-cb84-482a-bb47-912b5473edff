//发送验证码后,倒计时60秒
var sec = 60;
var activeCodeSend = false;

function doSubmit(){
    $(".btn-submit").addClass('disabled');
    if(! canSubmit()){
        $(".btn-submit").removeClass('disabled');
        return false;
    }
    $("#bindForm").submit();
}

function showErrorDiv() {
    var msg = arguments[0];
    var cssClass = "error";
    if (arguments.length == 2 ) {
        cssClass=arguments[1]
    }
    if ($("#errorDiv").length) {
        $("#errorDiv").remove();
    } 
    $("#bindForm").after(' <div id="errorDiv" class="' + cssClass + '">' + msg + '</div>');
}

function canSubmit() {
    if (!checkMobileNumber()) return false;
    if (!checkActiveNo()) return false;
    return true;
}


function checkActiveNo() {
    var activeNo=$("#activeNo").val();
    if(activeNo.length==0){
        showErrorDiv("请输入验证码");
        return false;
    }
    var pattern =/^[0-9]{6}$/;
    if(!pattern.test(activeNo)){
        showErrorDiv("请输入6位数字组成的验证码");
        return false;
    }
    if (!activeCodeSend) {
        showErrorDiv("请先获取验证码");
        return false;
    }
    return true;
}

function checkMobileNumber(){
  var mobile=$("#newMobileNo").val();
  if(mobile.length==0){
    showErrorDiv("请输入您需要绑定的手机号码！");
    return false;
  }
  if(! isMobileNo(mobile)){
    showErrorDiv("手机格式不正确，请重输！");
    return false;
  }
  return true;
}

function showSendAgain(){

    if(sec>0){
        $('#btn-send-code').text(sec.toString()+"秒后重试");
        sec = sec-1;
        setTimeout("showSendAgain()",1000);	
    }else{
        $('#btn-send-code').text( "重新获取验证码 ");
        $("#btn-send-code").removeClass('disabled');
        if ($("#errorDiv").length) {
            $("#errorDiv").remove();
        } 
    }
}

function sendActiveNo() {
    if (!checkMobileNumber()) return ;
    var dataSub ={"newMobileNo": $("#newMobileNo").val()};
    $.ajax( {
        url :"sendActiveNo.htm",
        data : dataSub,
        type : 'post',
        success : function(response) {
          if (response == "ok") {
            showErrorDiv("验证码已发送", "info");
            activeCodeSend = true;
            sec = 60;
            $("#btn-send-code").addClass('disabled');
            showSendAgain();
          } else {
            showErrorDiv(response, "error");
          }
        },
        error : function() {
        }
    });
}

$(document).ready(function() {
    $('.btn-submit').click(function (e) {
        e.preventDefault();
        if ($(this).hasClass('disabled')) {
            return false; // Do something else in here if required
        } else {
            doSubmit();
        }
    });

    $('#btn-send-code').click(function (e) {
        e.preventDefault();
        if ($(this).hasClass('disabled')) {
            return false; // Do something else in here if required
        } else {
            sendActiveNo();
        }
    });
});



