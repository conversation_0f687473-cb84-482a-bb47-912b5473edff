package com.shunwang.baseStone.sso.common.util;

public class CharacterPick {

    public static final boolean isChinese(char c) {
        Character.UnicodeBlock ub = Character.UnicodeBlock.of(c);
        if (ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS
                || ub == Character.UnicodeBlock.CJK_COMPATIBILITY_IDEOGRAPHS
                || ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_A) {
            return true;
        }
        return false;
    }
    
    public static final boolean isChineseCharacter(char chineseStr) {
        if ((chineseStr >= 0x4e00) && (chineseStr <= 0x9fbb)) {
            return true;
        }
        return false;
    }
    
    public static final boolean isEnglish(char enStr) {
        if ((enStr >= 0x61) && (enStr <= 0x7a) || (enStr >= 0x41) && (enStr <= 0x5a)) {
            return true;
        }
        return false;
    }
    
    public static final boolean isDigit(char digitStr) {
        if ((digitStr >= 0x30) && (digitStr <= 0x39)) {
            return true;
        }
        return false;
    }
    
}
