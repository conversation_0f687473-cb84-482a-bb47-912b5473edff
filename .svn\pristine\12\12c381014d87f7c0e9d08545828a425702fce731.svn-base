package com.shunwang.basepassport.binder.web.send;

import com.shunwang.basepassport.binder.common.BinderConstants;
import com.shunwang.basepassport.binder.pojo.SendBinder;
import com.shunwang.basepassport.commonExp.ParamNotFoundExp;
import com.shunwang.util.lang.StringUtil;
import org.apache.struts2.ServletActionContext;

import java.util.HashMap;
import java.util.Map;

/**
 * 匹配不需要特殊构建sendBinder处理的情况
 *
 * <AUTHOR>
 * @date 2018/12/17
 **/
public class SendForDefaultAction extends MobileSendBaseAction {
    final static HashMap<String, String> mapping = new HashMap<>();

    static {
        //偷懒,应该不同url不同action
        mapping.put("/front/interface/sendForFindPwdQuestion.htm",BinderConstants.SendConstants.SendInterfaceType.TYPE_FIND_PWD_QUESTION);
//        mapping.put("/front/interface/sendForFindPwd.htm",BinderConstants.SendConstants.SendInterfaceType.TYPE_FIND_PWD);
        mapping.put("/front/interface/sendForBindNumber.htm",
                BinderConstants.SendConstants.SendInterfaceType.TYPE_BIND_NUMBER);
        mapping.put("/front/interface/sendForChangeNumber.htm",
                BinderConstants.SendConstants.SendInterfaceType.TYPE_CHANGE_NUMBER);
        mapping.put("/front/interface/sendForFindPwdCard.htm", BinderConstants.SendConstants.SendInterfaceType.TYPE_FIND_PWDCARD);
        mapping.put("/front/interface/sendForFindPayPwd.htm", BinderConstants.SendConstants.SendInterfaceType.TYPE_FIND_PAYPWD);
//        mapping.put("/front/interface/sendForMobileRegister.htm",
//                BinderConstants.SendConstants.SendInterfaceType.TYPE_MOBILE_REGISTER);
//        mapping.put("/front/interface/sendForMobileLogin.htm",
//                BinderConstants.SendConstants.SendInterfaceType.TYPE_MOBILE_LOGIN);
//        mapping.put("/front/interface/sendForMobileSingleAccountBind.htm", BinderConstants.SendConstants.SendInterfaceType.TYPE_MOBILE_SINGLE_ACCOUNT_BIND);
        mapping.put("/front/interface/sendForMobileNormalCode.htm",
                BinderConstants.SendConstants.SendInterfaceType.TYPE_MOBILE_NORMAL_CODE);
    }

    @Override
    public void checkParam() {
        String requestURI = ServletActionContext.getRequest().getRequestURI();
        String interfaceType = mapping.get(requestURI);
        //短信发送url多出/的问题导致url无法匹配从而获取不到interfaceType eg://front/interface/sendForMobileNormalCode.htm
        if (interfaceType == null) {
            for (Map.Entry<String, String> entry : mapping.entrySet()) {
                if (requestURI.contains(entry.getKey())) {
                    interfaceType = entry.getValue();
                    break;
                }
            }
        }
        //assert interfaceType != null;
        setInterfaceType(interfaceType);
        super.checkParam();

        if (this.getInterfaceType().equals(BinderConstants.BINDNUMBER)) {
            if (StringUtil.isBlank(this.getMobile())) {
                throw new ParamNotFoundExp();
            }
        }
        if (this.getInterfaceType().equals(BinderConstants.CHANGENUMBER)) {
            if ((StringUtil.isBlank(this.getMobile()) || StringUtil.isBlank(this.getNewMobile()))) {
                throw new ParamNotFoundExp();
            }
        }
        //应该可以去掉 待验证
//        if (this.getInterfaceType().equals(BinderConstants.MOBILE_NORMAL_CODE)) {
//            if (StringUtil.isBlank(this.getUserName())) {
//                throw new ParamNotFoundExp();
//            }
//        }
    }

    @Override
    protected SendBinder buildSendBinder() {
        SendBinder sendBinder = (SendBinder) getBinderDao().getById(getMemberInfo().getMemberId());
        if (null == sendBinder) {
            sendBinder = createSendBinder();
        }
        sendBinder.setMember(getMemberInfo());
        sendBinder.setMemberName(getMemberInfo().getMemberName());
        return sendBinder;
    }
}
