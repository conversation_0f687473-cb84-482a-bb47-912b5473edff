package com.shunwang.basepassport.manager.service.yidun;

import com.shunwang.baseStone.core.exception.BaseStoneException;
import com.shunwang.basepassport.binder.common.ErrorCode;
import com.shunwang.basepassport.config.dao.ConfigInterfaceDao;
import com.shunwang.basepassport.config.dao.ConfigOneLoginDao;
import com.shunwang.basepassport.config.enums.OneLoginEnum;
import com.shunwang.basepassport.config.pojo.ConfigInterface;
import com.shunwang.basepassport.config.pojo.ConfigOneLogin;
import com.shunwang.basepassport.manager.request.yidun.OneClickCheckRequest;
import com.shunwang.basepassport.manager.response.yidun.OneClickCheckResponse;
import com.shunwang.util.net.HttpClientUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Constructor;
import java.nio.charset.StandardCharsets;
import java.util.Map;

public class CheckMobileYiDunH5ServiceClient {
    private static final Logger LOGGER = LoggerFactory.getLogger(CheckMobileYiDunH5ServiceClient.class);

    private static ConfigInterfaceDao configInterfaceDao;
    private static ConfigOneLoginDao configOneLoginDao;
    public void setConfigOneLoginDao(ConfigOneLoginDao configOneLoginDao) {
        CheckMobileYiDunH5ServiceClient.configOneLoginDao = configOneLoginDao;
    }
    public void setConfigInterfaceDao(ConfigInterfaceDao configInterfaceDao) {
        CheckMobileYiDunH5ServiceClient.configInterfaceDao = configInterfaceDao;
    }

    public static OneClickCheckResponse execute(OneClickCheckRequest request, String siteId) {
        try {
            ConfigInterface setting = configInterfaceDao.findByInterfaceKey(request.getInterfaceKey());
            request.doInterfaceSetting(setting);
//            resetRequest(request,  siteId);
            String url = request.buildUrl();
            Map<String, String> requestParams = request.buildParams();
            Map<String, String> headers = request.getHeaders();
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("----------------------------------------");
                LOGGER.debug("url:{}", url);
                LOGGER.debug("method:{}", request.getHttpMethod());
                LOGGER.debug("requestParams:{}", requestParams);
                LOGGER.debug("headers:{}", headers);
                LOGGER.debug("----------------------------------------");
            }
            String response = new String(HttpClientUtils.doPost(url, requestParams, headers, null, StandardCharsets.UTF_8));

            Class<OneClickCheckResponse> responseClass = request.getResponseClass();
            Constructor constructor = responseClass.getConstructor();
            OneClickCheckResponse resp = (OneClickCheckResponse) constructor.newInstance();
            resp.setRawJson(response);
            return resp.parse();
        } catch (BaseStoneException e) {
            throw e;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private static void resetRequest(OneClickCheckRequest request, String siteId) {
        ConfigOneLogin configOneLogin = configOneLoginDao.findBySiteIdAndChannel(siteId, OneLoginEnum.Channel.YI_DUN.getValue(), OneLoginEnum.TerminalTypeEnum.H5.getValue());
        if (configOneLogin == null) {
            LOGGER.warn("易盾H5一键登录渠道[{}]配置不存在，请联系管理员后台配置", siteId);
            throw new BaseStoneException(ErrorCode.C_1096);
        }
        request.setAppId(configOneLogin.getAppid());
        request.setAppKey(configOneLogin.getMd5Key());
    }

}
