<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">
	
	  <bean id="checkUniqueAction" class="com.shunwang.basepassport.user.web.CheckUniqueAction" scope= "prototype">	  	 
	  </bean>
	  
	  <bean id="updatePwdAction" class="com.shunwang.basepassport.user.web.UpdatePwdAction" scope= "prototype">	  	 
	  </bean>
	  
	  <bean id="memberLoginAction" class="com.shunwang.basepassport.user.web.MemberLoginAction" scope= "prototype">
          <property name="bussinessDao" ref="bussinessDao"/>
          <property name="memberAccountBindDao" ref="memberAccountBindDao"/>
          <property name="interfaceService" ref="interfaceService"/>
          <property name="loginElementService" ref="loginElementService"/>
	  </bean>

      <bean id="moibleThirdOutLoginAction" class="com.shunwang.basepassport.user.web.MoibleThirdOutLoginAction" scope= "prototype">

      </bean>

      <bean id="getTicketActon" class="com.shunwang.basepassport.user.web.GetTicketActon" scope= "prototype"/>

	  <bean id="realnamePassportQueryAction" class="com.shunwang.basepassport.actu.web.RealnamePassportQueryAction" scope= "prototype">
	  	<property name="memberDao" ref="memberDao"></property>
	  </bean>
	  <bean id="memberQueryAction" class="com.shunwang.basepassport.user.web.MemberQueryAction" scope= "prototype"></bean>

	  <bean id="registerAction" class="com.shunwang.basepassport.user.web.RegisterAction" scope= "prototype">
          <property name="bussinessDao" ref="bussinessDao"/>
          <property name="memberAccountBindDao" ref="memberAccountBindDao"/>
          <property name="idCardVerifyBo" ref="idCardVerifyBo"/>
      </bean>
	  <bean id="kedouBarUserRegisterAction" class="com.shunwang.basepassport.user.web.KedouBarUserRegisterAction" scope= "prototype"></bean>

	  <bean id="tmpRegisterAction" class="com.shunwang.basepassport.user.web.TmpRegisterAction" scope= "prototype"></bean>
	  	  	 
	  <bean id="updateMemberAction" class="com.shunwang.basepassport.user.web.UpdateMemberAction" scope= "prototype">
          <property name="idCardVerifyBo" ref="idCardVerifyBo"/>
		  <property name="bussinessDao" ref="bussinessDao" />
      </bean>

	  <bean id="memberQueryFullAction" class="com.shunwang.basepassport.user.web.MemberQueryFullAction" scope="prototype"></bean>
	  <bean id="memberQueryProvideToMobileAction" class="com.shunwang.basepassport.user.web.MemberQueryProvideToMobileAction" scope="prototype"></bean>
	  <bean id="memberQueryByOutUserAction" class="com.shunwang.basepassport.user.web.MemberQueryByOutUserAction" scope="prototype"></bean>


	  <bean id="headImageAction" class="com.shunwang.basepassport.user.web.HeadImageAction" scope= "prototype"></bean>

<!--    <bean id="memberUnsafeDao" class="com.shunwang.basepassport.user.dao.MemberUnsafeDao">-->
<!--        <property name="sqlMapClientTemplate">-->
<!--            <ref bean="sqlMapClientTemplate"/>-->
<!--        </property>-->
<!--    </bean>-->

    <bean id="headImageModifyAction"
          class="com.shunwang.basepassport.user.web.HeadImageModifyAction" scope="prototype">
        <property name="headImgMaxSize" value="${headImgMaxSize}"/>
    </bean>

    <bean id="timeSyncAction" class="com.shunwang.basepassport.user.web.TimeSyncAction"
          scope="prototype"></bean>

    <bean id="sendDynamicPasswordAction"
          class="com.shunwang.basepassport.user.web.SendDynamicPasswordAction" scope="prototype">
    </bean>

    <bean id="dynamicSecurityValidateAction"
          class="com.shunwang.basepassport.user.web.DynamicSecurityValidateAction"
          scope="prototype">
        <property name="effectiveTime" value="${effectiveTime}"/>
        <property name="dynamicMd5Key" value="${dynamicMd5Key}"/>
        <property name="dynamicAesKey" value="${dynamicAesKey}"/>
    </bean>
    <bean id="getOutOauthDirAction" class="com.shunwang.basepassport.user.web.GetOutOauthDirAction" scope="prototype">
        <property name="loginElementService" ref="loginElementService"/>
    </bean>
    <bean id="verifyLoginStatusAction"
          class="com.shunwang.basepassport.user.web.VerifyLoginStatusAction" scope="prototype">
    </bean>

    <bean id="refreshTokenAction" class="com.shunwang.basepassport.user.web.RefreshTokenAction"
          scope="prototype">
    </bean>

    <bean id="verifyTokenAction" class="com.shunwang.basepassport.user.web.VerifyTokenAction"
          scope="prototype">
    </bean>

    <bean id="SDKVerifyTokenAction" class="com.shunwang.basepassport.user.web.SDKVerifyTokenAction"  parent="verifyTokenAction"
          scope="prototype"/>


    <bean id="memberLogoutAction" class="com.shunwang.basepassport.user.web.MemberLogoutAction"
          scope="prototype">
    </bean>

    <bean id="blankAction" class="com.shunwang.basepassport.user.web.BlankAction" scope="prototype">
    </bean>

    <bean id="customServiceAction" class="com.shunwang.basepassport.user.web.CustomServiceAction" scope="prototype"></bean>

    <bean id="userBankCardQueryAction" class="com.shunwang.basepassport.user.web.UserBankCardQueryAction" scope= "prototype">
        <property name="userBankCardDao" ref="userBankCardDao" />
    </bean>
    <bean id="bindUserBankCardAction" class="com.shunwang.basepassport.user.web.BindUserBankCardAction" scope= "prototype">
        <property name="userBankCardDao" ref="userBankCardDao" />
        <property name="memberDao" ref="memberDao" />
    </bean>
    <bean id="modifyUserBankCardAction" class="com.shunwang.basepassport.user.web.ModifyUserBankCardAction" scope= "prototype">
        <property name="userBankCardDao" ref="userBankCardDao" />
    </bean>

    <bean id="hlbAddressConversionAction" class="com.shunwang.basepassport.user.web.HlbAddressConversionAction" scope= "prototype">
        <property name="areaDao" ref="areaDao" />
    </bean>

    <bean id="memberCancelNoticeAction" class="com.shunwang.basepassport.user.web.MemberCancelNoticeAction" scope="prototype">
        <property name="serviceNotifyDao" ref="serviceNotifyDao"/>
    </bean>
    <bean id="mobileChangeNoticeAction" class="com.shunwang.basepassport.user.web.MobileChangeNoticeAction" scope="prototype">
        <property name="serviceNotifyDao" ref="serviceNotifyDao"/>
    </bean>
</beans>
