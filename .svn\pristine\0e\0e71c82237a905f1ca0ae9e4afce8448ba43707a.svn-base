<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMapConfig 	PUBLIC "-//ibatis.apache.org//DTD SQL Map Config 2.0//EN"  "http://ibatis.apache.org/dtd/sql-map-config-2.dtd">
<sqlMapConfig>
	<settings cacheModelsEnabled="true"  enhancementEnabled="false" lazyLoadingEnabled="false"   maxRequests="3000" maxSessions="3000" maxTransactions="3000" useStatementNamespaces="true"/>
	<sqlMap resource="baseStone/ibatis/WhereClause.xml"/>
<!--	<sqlMap resource="baseStone/ibatis/sql_SiteInterface.xml"/>-->
<!--	<sqlMap resource="baseStone/ibatis/sql_Bussiness.xml"/>-->
<!--	<sqlMap resource="baseStone/ibatis/sql_PageHome.xml"/>-->
<!--	<sqlMap resource="baseStone/ibatis/sql_EditBackground.xml"/>-->
<!--	<sqlMap resource="baseStone/ibatis/sql_notice.xml"/>-->
<!--	<sqlMap resource="baseStone/ibatis/sql_Reason.xml"/>-->
<!--	<sqlMap resource="baseStone/ibatis/sql_zone.xml"/>-->
<!--	<sqlMap resource="baseStone/ibatis/sql_MemberAdvise.xml"/>-->
<!--	<sqlMap resource="baseStone/ibatis/sql_Sysconfig.xml"/>-->
<!--	<sqlMap resource="baseStone/ibatis/sql_Regist.xml"/>-->
<!--	<sqlMap resource="baseStone/ibatis/sql_Service.xml"/>-->
<!--	<sqlMap resource="baseStone/ibatis/sql_backuser.xml"/>-->
<!--	<sqlMap resource="baseStone/ibatis/sql_EditLog.xml"/>-->
<!--	<sqlMap resource="baseStone/ibatis/sql_UseroutInterface.xml"/>-->
<!--	<sqlMap resource="baseStone/ibatis/sql_css.xml"/>-->
<!--	<sqlMap resource="baseStone/ibatis/sql_Category.xml"/>-->
<!--	<sqlMap resource="baseStone/ibatis/sql_businessCategory.xml"/>-->
<!--	<sqlMap resource="baseStone/ibatis/sql_OutOauthDir.xml"/>-->
<!--	<sqlMap resource="baseStone/ibatis/sql_baseLoginElement.xml"/>-->
<!--	<sqlMap resource="baseStone/ibatis/sql_loginElement.xml"/>-->
<!--	<sqlMap resource="baseStone/ibatis/sql_RichText.xml"/>-->
<!--	<sqlMap resource="baseStone/ibatis/sql_configOauth.xml"/>-->

	<!-- 
	<sqlMap resource="baseStone/ibatis/sql_Dictionary.xml"/>
	<sqlMap resource="baseStone/ibatis/sql_SensitiveWord.xml"/>
	 -->
</sqlMapConfig>
