package com.shunwang.basepassport.site;

import junit.framework.TestCase;

import com.shunwang.baseStone.context.BaseStoneContext;
import com.shunwang.baseStone.siteinterface.dao.SiteInterfaceDao;
import com.shunwang.baseStone.siteinterface.pojo.SiteInterface;

/**
 * ****************************
 * 版权所有：顺网科技 保留所有权利
 * 创建日期: Jul 26, 2011 11:10:15 AM
 * 创建作者：陈积慧
 * 文件名称：ISiteInterfaceDaoTest.java
 * 版本： 1.0
 * 功能：
 * 最后修改时间：
 * 修改记录：
 ***************************************
 */
public class SiteInterfaceDaoTest extends TestCase{

	
	public void testGetBySiteIdAndSiteName(){
		BaseStoneContext.getInstance().setFileName("/basepassportContext.xml");
		SiteInterfaceDao dao=(SiteInterfaceDao)BaseStoneContext.getInstance().getBean("siteInterfaceDao");
		SiteInterface site=dao.getBySiteIdAndName("sw_pay", "手机邮箱用户名唯一性验证");
		System.out.println(site.getMd5Key());
	}

}
