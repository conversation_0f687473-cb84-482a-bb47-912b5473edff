package com.shunwang.baseStone.cache;

/**
 * 为了各个项目之间缓存key不会冲突，
 * 因此每个缓存key都是以对应的项目名作为开头
 * 例: base项目的缓存key为 base_xxx
 */
public class CacheKeyConstant {

    public static final String CACHE_SPLIT = "_";

    public static final String SEPARATOR = "|";

    public static class LoginConfigConstants {
        /**
         * 商户登录可使用功能配置缓存
         */
        public static final String BUS_LOGIN_ELEMENT_CACHE_KEY = "base_login_config_";
    }

    public static class OutOauthDirConstants {

        /**
         * 商户外部接入缓存
         */
        public static final String BUS_OUT_OAUTH_DIR_CACHE_KEY = "base_out_oauth_dir_";
    }

    public static class SiteInterfaceConstants {
        /**
         * 商户分配接口配置
         */
        public static final String BUS_SITE_INTERFACE_CACHE_KEY = "base_site_interface_";
    }

    public static class CssConstants {
        /**
         * CSS管理
         */
        public static final String BUS_CSS_CONFIG_CACHE_KEY = "base_css_config_";
    }

    public static class UserOutInterfaceConstants {
        /**
         * 用户名前缀
         */
        public static final String BASE_USER_OUT_INTERFACE_KEY = "base_user_out_interface";
        public static final String BUS_ALL_PREFIX_NAME_KEY = "base_user_out_interface_all_prefix_name";
    }

    public static class SysConfigConstants {
        /**
         * 图片url
         */
        public static final String BUS_IMAGE_URL_CACHE_KEY = "base_image_url";

        /**
         * 图片路径
         */
        public static final String BUS_IMAGE_DIR_CACHE_KEY = "base_image_dir";

        /**
         * 加密图片路径
         */
        public static final String BUS_ENCRYPT_IMAGE_DIR_CACHE_KEY = "base_encrypt_image_dir";

        /**
         * 默认短信内容
         */
        public static final String BUS_MOBILE_DEFAULT_CACHE_KEY = "base_mobile_default_msg";

        /**
         * 有效手机号码段
         */
        public static final String BUS_VALID_PHONE_CACHE_KEY = "base_valid_phone_num";
        /**
         * 极验总开关
         */
        public static final String BUS_GT_SWITCH_CACHE_KEY = "base_gt_switch";
        /**
         * 极验异常自动关闭阀值
         */
        public static final String BUS_GT_THRESHOLD_CACHE_KEY = "base_gt_threshold";
    }

    public static class ResourceConstants {
        /**
         * config_resources的单条记录缓存前缀
         */
        public static final String RESOURCES_PREFIX = "back_resource_";
    }

    public static class GeetestLib {

        /**
         * 极验验证API服务状态Session Key
         */
        public final static String SSO_STATUS_KEY = "sso_gt_server_status_";

        /**
         * 极验验证API服务状态Session Key
         */
        public final static String PASSPORT_STATUS_KEY = "passport_gt_server_status_";

        /**
         * 极验异常报警并发锁
         */
        public final static String GT_ALARM_LOCK = "gt_alarm_lock";

    }

    public class BannerConstant {
        /**
         * 蝌蚪服务
         **/
        public static final String LOGIN_SERVICE = "pageLoginLetterHead";
        /**
         * 蝌蚪支付
         **/
        public static final String LOGIN_PAY = "pageLoginHeadGame";
        public static final String LOGIN_SERVICE_PREVIEW = "pageLoginLetterHead_pre";
        public static final String LOGIN_PAY_PREVIEW = "pageLoginHeadGame_pre";
        public static final String LOGIN_SERVICE_PREVIEW_TMP = "pageLoginLetterHead_tmp";
        public static final String LOGIN_PAY_PREVIEW_TMP = "pageLoginHeadGame_tmp";
        public static final String _LOGIN_SERVICE = "login_service";
        public static final String _LOGIN_PAY = "login_pay";
    }

    public class BaseEditBackgroundConsts {
        public static final String ACTIVITY_KEY = "base_activity_key_";
    }

    public class Passport {
        public static final String PASSPORT_USER_LAST_LOGON_LOG_CACHE_KEY = "passport_last_logon_log_cache_";
        public static final String PASSPORT_BIND_KEY = "passport_bind_";
        public static final String PASSPORT_SEND_SECOND_KEY = "passport_send_second_";
        public static final String CHANGE = "Change";
        public static final String BY = "By";
        public static final String TOKENID = "Tokenid";
        /**
         * 存放接入方免登跳转到注销页面所带的回调页面
         * key:MEMBER_CANCEL_PAGE_URL + memberId,value:pageUrl
         */
        public static final String MEMBER_CANCEL_PAGE_URL = "member_cancel_page_url_";

    }

    public class SSO {
        /**
         * 外部站点登录信息
         */
        public final static String OUT_OAUTH_KEY = "OUT_OAUTH_KEY";
        /**
         * time logon
         */
        public final static String LTIME_KEY = "sso_LTime_";

        public final static String TOKENID_PREFIX = "SSO_SMS_QUICK_LOGIN_";


        public final static String SIMPLE_IMPORT_LOCK = "C_";

        public final static String MOBILE_TOKENID_PREFIX = "SSO_MOBILE_LOGIN_";

        public final static String LOGIN_PERR = "login_PErr_";

        /**
         * 用户注册信息缓存
         */
        public static final String USER_MEMBER_INFO = "sso_member_cache_";

        public static final String WX_REFRESH_TOKEN_SCHEDULE = "wx_refresh_token_schedule";
        public static final String WX_REFRESH_TOKEN_TASK = "wx_refresh_token_task_";

        public static final String WX_OAUTH = "wx_oauth_";
        public static final String WX_OAUTH_TOKEN = "wx_oauth_token_";
        public static final String WX_REPLY = "wx_appid_reply_";

        public static final String SERVICE_NOTIFY_TASK_LOCK_KEY = "service_notify_task_lock_";

        /**
         * 用于用户登录写登录日志加锁，微信公众号存在多次回调，会重复写登录日志
         * key+memberId
         */
        public static final String LOGIN_LOG_LOCK_ = "login_log_lock_";

        /**
         * 用于微信异常报警缓存
         */
        public static final String WX_ALARM_CACHE = "wx_alarm_cache_";

        /**
         * 登录用户缓存，记录登录的链路
         */
        public static final String LOGIN_USER_SESSION = "login_user_session_";

        /**
         * 内部场景值缓存,比如计费上机免登授权二维码页面
         */
        public static final String INNER_SCAN = "inner_scan_";
    }

    public class PassportAppeal {

        public final static String ACTIVE_NO_EMAIL = "activeNoEamil_2011090321135428";
        public final static String ACTIVE_NO_MOBILE = "activeMobile_2011090321245698";
        /**
         * 用户申述key,防止重复提交
         */
        public static final String FIND_APPEAL_MSG = "passport_lock_find_appeal_msg_";

        /**
         * 找回密码
         */
        public static final String TOKENID_PREFIX = "PASSPORT_HTML5_FIND_PASSPORT_";

    }

    public class PassportActu {

        public final static String ACTU_TOKENID = "passport_actu_token_id_";
        /**
         * 实名认证找回密码
         */
        public static final String FIND_ACTU_MSG = "passport_lock_find_actu_msg_";
        public static final String CHANGE_EMAIL_TOKENID = "passport_change_email_tokenid_";
        public static final String CHANGE_PHONE_TOKENID = "passport_change_phone_num_tokenid_";

        /**
         * 百度OCR常量
         */
        public static final String BAIDU_OCR_LIMIT_PRE = "passport_baidu_ocr_limit_";
        public static final String BAIDU_OCR_INFO_PRE = "passport_baidu_ocr_info_";
        public static final String BAIDU_OCR_ACCESS_TOKEN = "passport_baidu_ocr_access_token";
        public static final String BAIDU_OCR_IDCARD_FRONT = "passport_baidu_ocr_idcard_front_";
        public static final String BAIDU_OCR_IDCARD_BACK = "passport_baidu_ocr_idcard_back_";


        public static final String OCR = "OCR_";
        public static final String OCR_CAFE = "OCR_cafeChange_";

    }

    public class InterfaceToken {

        public static final String ACCESS_TOKEN_PREFIX = "interface_accessToken_";

        public static final String REFRESH_TOKEN_PREFIX = "refreshToken";

        public static final String CACHE_KEY_LOGIN_TOKEN = "interface_loginToken_";
        /**
         * uuid加前缀
         */
        public static final String LOGIN_TICKET = "interface_login_ticket_";

        public static final String AUTHORIZED_TOKEN_PREFIX = "interface_authorized_token_";

        public static final String AUTHORIZED_CODE_PREFIX = "interface_authorized_code_";

        /**
         * 单账号,uuid加前缀
         */
        public static final String SINGLE_ACCOUNT_SIGN = "interface_single_account_sign_";

        /**
         * 密码二次校验,uuid加前缀
         */
        public static final String VALID_SIGN = "interface_valid_sign_";

        /**
         * 对应用户数据的key
         */
        public static final String MOBILE_USERINFO__KEY_ = "inteface_mobile_userinfo__key_";


        public static final String SHORT_URL = "interface_short_url_";
        /**
         * 一键登录场景值key
         */
        public static final String ONE_LOGIN_SCENE_KEY = "scene_one_login_";


    }

    public class InterfaceBussKey {
        /**
         * 当前密钥放置缓存的key
         */
        public static final String CACHE_KEY_BUSSKEY_CUR = "interface_bussKey_cur_";
        /**
         * 之前密钥放置缓存的key
         */
        public static final String CACHE_KEY_BUSSKEY_PRE = "interface_bussKey_pre_";

        public static final String CACHE_KEY_DYNAMIKEY = "interface_dynamicKey_";

        public static final String SENDER_CACHE_KEY = "interface_sender_";


    }

    public class InterfaceSmsKey {
        /**
         * 动态验证码缓存key
         * interface接口2-1,2-2
         * sso双活内部接口
         */
        public static final String SMS_DYNAMIC_PWD = "interface_sms_dyna_pwd_";
        /**
         * 普通验证码缓存key
         */
        public static final String SMS_SEND_PLAIN = "interface_sms_send_plain_";

        /**
         * interface 9,10接口短信验证码缓存key
         */
        public static final String SMS_CHECK_CODE = "interface_sms_code_";

        /**
         * 用于smsConfig 配置的缓存 key+siteId+businessType
         */
        public static final String SMS_CONFIG_CACHE = "sms_config_cache_";
        /**
         * 用于emailConfig 配置的缓存 key+siteId+businessType
         */
        public static final String EMAIL_CONFIG_CACHE = "email_config_cache_";
    }

    public class ConfigResourcesConstants {

        /**
         * IP黑名单配置
         */
        public static final String PERMIT_IP_CONFIG = "permitIpConfig";

        /**
         * 通用配置
         */
        public static final String COMMON_CONFIG = "commonConfig";
        /**
         * 手机登录用户协议自动选中开关及白名单
         */
        public static final String AGREEMENT_AUTO_CHECK_SWITCH = "agreementAutoCheckSwitch";
        public static final String AGREEMENT_AUTO_CHECK_SITE_ID_WHITELIST = "agreementAutoCheckSiteIdWhitelist";

        public static final String SMSCODE_LIMIT_CONFIG = "SmsCodeLimitConfig";
        public static final String SMSCODE_LIMIT_SITEID = "SmsCodeLimitSiteId";
        public static final String SMSCODE_LIMIT_VALID_PERIOD = "SmsCodeLimitValidPeriod";
        public static final String SMSCODE_LIMIT_QUANTITY = "SmsCodeLimitQuantity";
        public static final String SMSCODE_LIMIT_TERMINAL = "SmsCodeLimitTerminal";



        /**
         * 手机充值SDK相关配置
         */
        public static final String MOBILE_RECHARGE_SDK_CONFIG = "mobileRechargeSdkConfig";

        public static final String MOBILE_RECHARGE_SDK_CONFIG_ACCESS_NAME = "accessTokenTimeOutInterval";

        public static final String MOBILE_RECHARGE_SDK_CONFIG_REFRESH_NAME = "refreshTokenTimeOutInterval";

        /**
         * OCR 配置
         */
        public static final String BAIDU_OCR_CONFIG_KEY = "OCRConfig";
        public static final String BAIDU_OCR_INTERFACE_PARAM_APIKEY = "apikey";
        public static final String BAIDU_OCR_INTERFACE_PARAM_APPID = "appid";
        public static final String BAIDU_OCR_INTERFACE_PARAM_SECRETKEY = "secretkey";

        public static final String SAFE_ITEMS_SCORE_CONFIG_KEY = "safeItemsScoreConfig";
        public static final String SAFE_ITEMS_SCORE_CONFIG_INIT_SCORE = "initScore";
        public static final String SAFE_ITEMS_SCORE_CONFIG_BIND_EMAIL = "emailBind";
        public static final String SAFE_ITEMS_SCORE_CONFIG_BIND_MOBILE = "mobileBind";
        public static final String SAFE_ITEMS_SCORE_CONFIG_LOGIN_PROTECTION = "loginProtection";
        public static final String SAFE_ITEMS_SCORE_CONFIG_NO_PASSWORD = "noPassword";
        public static final String SAFE_ITEMS_SCORE_CONFIG_BIND_QUESTION = "questionBind";
        public static final String SAFE_ITEMS_SCORE_CONFIG_REALNAME_AUTH = "realNameAuth";
        public static final String SAFE_ITEMS_SCORE_CONFIG_SAFENOTICE = "safeNotice";

        /**
         * 绑定手机号码的个数
         */
        public static final String MOBILE_BINDER_LIMIT = "mobileBinderLimit";

        /**
         * 跳转地址域名白名单
         */
        public static final String URL_DOMAINS_WHITE_LIST = "urlDomainsWhiteList";
        /**
         * xss传入参数白名单（白名单内的参数不过滤危险字符）
         */
        public static final String PARAMS_WHITE_LIST_FOR_XSS = "paramsWhiteListForXss";

        /**
         * xss值黑名单
         */
        public static final String VALUE_DANGER_LIST_FOR_XSS = "valueDangerListForXss";
        /**
         * 危险字符黑名单
         */
        public static final String DANGER_CHAR_LIST_FOR_XSS = "dangerCharListForXss";
        /**
         * 危险字符限制个数
         */
        public static final String DANGER_CHAR_LIMIT_FOR_XSS = "dangerCharLimitForXss";
        /**
         * 危险字符开关
         */
        public static final String DANGER_CHAR_IS_OPEN = "dangerCharIsOpen";

        /**
         * xss传入参数白名单（白名单内的参数不过滤危险字符）
         */
        public static final String XSS_IS_OPEN = "xssIsOpen";

        /**
         * 注销帐号通知已接入注销功能的业务方
         */
        public static final String MEMBER_CANCEL_NOTICE_URLS = "memberCancelNoticeUrls";
        /**
         * 撤销注销帐号通知已接入注销功能的业务方
         */
        public static final String MEMBER_REVOKE_CANCEL_NOTICE_URLS = "memberRevokeCancelNoticeUrls";
        /**
         * 真实注销帐号通知已接入注销功能的业务方
         */
        public static final String MEMBER_REAL_CANCEL_NOTICE_URLS = "memberRealCancelNoticeUrls";
        /**
         * 修改手机通知已接入的业务方
         */
        public static final String MOBILE_CHANGE_NOTICE_URLS = "mobileChangeNoticeUrls";
        /**
         * 解绑手机帐号通知已接入的业务方
         */
        public static final String MOBILE_UNBIND_NOTICE_URLS = "mobileUnbindNoticeUrls";

        /**
         * 限制登录的密码错误次数
         */
        public static final String ERR_TIMES_OF_LIMIT_LOGIN = "errTimesOfLimitLogin";
        /**
         * 密码错误导致限制登录时长
         */
        public static final String LIMIT_SENCONDS_OF_LIMIT_LOGIN = "limitSecondsOfLimitLogin";

        /**
         * 防沉迷配置
         */
        public static final String ANTI_ADDITION_CONFIG = "antiAdditionConfig";

        /**
         * 防沉迷开关
         */
        public static final String ANTI_ADDITION_CONFIG_SWITCH = "antiAdditionSwitch";

        /**
         * 防沉迷报警开关
         */
        public static final String ANTI_ADDITION_CONFIG_ALERT_SWITCH = "antiAdditionAlertSwitch";

        /**
         * 防沉迷一分钟异常预警值
         */
        public static final String ANTI_ADDITION_CONFIG_ALERT_THRESHOLD = "antiAdditionAlertThreshold";

        /**
         * 防沉迷预警报警邮件
         */
        public static final String ANTI_ADDITION_CONFIG_ALERT_EMAILS = "antiAdditionAlertEmails";

        /**
         * 大数据接口防沉迷报警开关
         */
        public static final String ANTI_ADDITION_CONFIG_DC_ALERT_SWITCH = "antiAdditionDcAlertSwitch";

        /**
         * 大数据接口防沉迷一分钟异常预警值
         */
        public static final String ANTI_ADDITION_CONFIG_DC_ALERT_THRESHOLD = "antiAdditionDcAlertThreshold";


        /**
         * 创蓝30分钟调用预警值
         */
        public static final String ANTI_ADDITION_CONFIG_ALERT_INVOKE_THRESHOLD = "antiAdditionAlertInvokeThreshold";

        /**
         * 调休的假期
         */
        public static final String ANTI_ADDITION_CONFIG_HOLIDAY = "antiAdditionHoliday";

        /**
         * 周几
         */
        public static final String ANTI_ADDITION_CONFIG_DAY_OF_WEEK = "antiAdditionDayOfWeek";

        /**
         * 未成年人可玩游戏时间段
         */
        public static final String ANTI_ADDITION_CONFIG_PERIOD = "antiAdditionPeriod";
        /**
         * 防沉迷预警报警手机
         */
        public static final String ANTI_ADDITION_CONFIG_ALERT_MOBILES = "antiAdditionAlertMobiles";
        /**
         * 防沉迷预警报警短信时间间隔
         */
        public static final String ANTI_ADDITION_CONFIG_ALERT_SMS_INTERVAL = "antiAdditionAlertSmsInterval";

        /**
         * 防沉迷路由开关 国家接口超时或异常时是否走创蓝接口
         */
        public static final String ANTI_ADDITION_CONFIG_ROUTE_SWITCH = "antiAdditionRouteSwitch";
        /**
         * 防沉迷路由异常码 国家接口对应异常时是走创蓝接口
         */
        public static final String ANTI_ADDITION_CONFIG_ROUTE_ERR_CODES = "antiAdditionRouteErrCodes";
        /**
         * 防沉迷国家开关
         */
        public static final String ANTI_ADDITION_CONFIG_GAME_ALERT_SWITCH = "antiAdditionGameAlertSwitch";

        /**
         * 防沉迷国家报警开关
         */
        public static final String ANTI_ADDITION_CONFIG_GAME_ALERT_THRESHOLD = "antiAdditionGameAlertThreshold";
        /**
         * 国家防沉迷异常时间窗大小（分钟）
         */
        public static final String ANTI_ADDITION_CONFIG_GAME_TIME_WINDOW_SIZE = "antiAdditionConfigGameTimeWindowSize";
        /**
         * 防沉迷xx时间内姓名认证次数限制
         */
        public static final String ANIT_ADDITION_CONFIG_GAME_REAL_NAME_COUNT_LIMIT = "gameRealNameCountLimit";
        /**
         * 防沉迷姓名认证限制时间阀值
         */
        public static final String ANIT_ADDITION_CONFIG_GAME_REAL_NAME_TIME_LIMIT = "gameRealNameTimeLimit";
        /**
         * 国家防沉迷站点调用时间窗大小（分钟）
         */
        public static final String ANTI_ADDITION_CONFIG_GAME_SITE_TIME_WINDOW_SIZE = "antiGameSiteTimeWindowSize";
        /**
         * 防沉迷国家站点累计调用次数阀值
         */
        public static final String ANTI_ADDITION_CONFIG_GAME_SITE_ALERT_THRESHOLD = "antiGameAlertThreshold";
        /**
         * 防沉迷国家站点开关
         */
        public static final String ANTI_ADDITION_CONFIG_GAME_SITE_ALERT_SWITCH = "antiGameSiteAlertSwitch";
        /**
         * 防沉迷国家站点黑名单
         */
        public static final String ANTI_ADDITION_CONFIG_GAME_SITE_BLACK_LIST = "antiGameSiteBlackList";


        /**
         * 大数据统一采集配置
         */
        public static final String DATA_REPORT_CONFIG = "dataReportConfig";
        public static final String DATA_REPORT_SWITCH = "dataReportSwitch";

        /**
         * 风控配置
         */
        public static final String RISK_CONFIG = "riskConfig";

        public static final String RISK_SSO_LOGIN_IS_OPEN = "ssoLoginIsOpen";
        public static final String RISK_INTERFACE_LOGIN_IS_OPEN = "interfaceLoginIsOpen";
        public static final String RISK_INTERFACE_REG_IS_OPEN = "interfaceRegIsOpen";
        public static final String RISK_INTERFACE_MODIFY_IS_OPEN = "interfaceModifyIsOpen";
        public static final String RISK_INTERFACE_IMAGE_IS_OPEN = "interfaceImageIsOpen";
        public static final String RISK_INTERFACE_QUERY_USER_BY_MOBILE_IS_OPEN = "interfaceQueryUserByMobileIsOpen";

        /**
         * 计费上机业务配置
         */
        public static final String BAR_LOGIN_CONFIG = "barLoginConfig";
        /**
         * 微信上机开关
         */
        public static final String BAR_LOGIN_IS_OPEN = "barLoginIsOpen";
        /**
         * 微信返回身份证绑定消息内容
         */
        public static final String BAR_LOGIN_WEIXIN_MSG_BIND = "barLoginWeiXinMsgBind";
        /**
         * 微信返回上机成功消息内容
         */
        public static final String BAR_LOGIN_WEIXIN_MSG_SUCCESS = "barLoginWeiXinMsgSuccess";
        /**
         * 微信上机默认公众号appid
         */
        public static final String BAR_LOGIN_DEFAULT_APPID = "barLoginDefaultAppid";
        /**
         * 微信上机默认tips
         */
        public static final String BAR_LOGIN_DEFAULT_TIPS = "barLoginDefaultTips";

        /**
         * 微信上机默认tips
         */
        public static final String BAR_LOGIN_SERVER_EXPIRE_SECONDS = "serverExpireSeconds";

        /**
         * 电竞酒店计费上机业务配置
         */
        public static final String HOTEL_LOGIN_CONFIG = "hotelLoginConfig";

        /**
         * 微信上机开关
         */
        public static final String HOTEL_LOGIN_IS_OPEN = "loginIsOpen";

        /**
         * sso是否发送扫码上机绑定身份证微信消息开关
         */
        public static final String SSO_BIND_IDCARD_WX_MSG_IS_OPEN = "ssoBindIdCardWxMsgSwitch";
        /**
         * 电竞酒店微信返回上机成功消息内容
         */
        public static final String HOTEL_LOGIN_WEIXIN_MSG_SUCCESS = "loginWeiXinMsgSuccess";
        /**
         * 电竞酒店微信上机默认公众号appid
         */
        public static final String HOTEL_LOGIN_DEFAULT_APPID = "loginDefaultAppid";
        /**
         * 电竞酒店微信上机默认tips
         */
        public static final String HOTEL_LOGIN_DEFAULT_TIPS = "loginDefaultTips";
        /**
         * 电竞酒店微信上机认证地址
         */
        public static final String HOTEL_LOGIN_OAUTH_URL = "loginOauthUrl";
        /**
         * 电竞酒店微信上机二维码过期时间
         */
        public static final String HOTEL_LOGIN_SERVER_EXPIRE_SECONDS = "serverExpireSeconds";

        /**
         * 微信接口上限异常报警手机
         */
        public static final String WEIXIN_DAILY_LIMIT_ALARM_NUMS = "weixinDailyLimitAlarmNums";
        /**
         * 微信接口异常报警缓存时间
         */
        public static final String WEIXIN_DAILY_LIMIT_ALARM_CACHE_SECONDS = "weixinDailyLimitAlarmCacheSeconds";
        /**
         * 微信登录回复消息配置
         */
        public static final String WEIXIN_LOGIN_REPLY_MSG = "weixinLoginReplyMsg";
        /**
         * 微信登录，多账号回复消息配置
         */
        public static final String WEIXIN_LOGIN_REPLY_MSG_MULTIPLE = "weixinLoginReplyMsgMultiple";
        /**
         * 微信登录，单账号回复消息配置
         */
        public static final String WEIXIN_LOGIN_REPLY_MSG_SINGLE = "weixinLoginReplyMsgSingle";
        /**
         * 微信上机开关
         */
        public static final String SEND_POLL_WX_MSG_IS_OPEN = "sendPollWxMsgSwitch";
        /**
         * 微信业务扫码回复消息配置
         */
        public static final String WEIXIN_BUSINESS_SACAN_REPLAY_MSG = "weixinBusinessScanReplayMsg";

        /**
         * AI业务相关配置
         */
        public static final String AI_BUSINESS_CONFIG = "aiConfig";
        /**
         * 跳转Ai小程序绑定手机号发送微信小卡片消息的消息id
         */
        public static final String AI_WEIXIN_CARD_MSG = "aiWeixinCardMsg";

        /**
         * 计费免登配置
         */
        public static final String NET_BAR_FREE_LOGIN_CONFIG = "netBarFreeLogin";
        /**默认微信appid**/
        public static final String DEFAULT_APPID = "defaultAppid";
        /**默认微信小程序appid**/
        public static final String DEFAULT_APPID_MINI = "defaultAppidMini";
        public static final String DEFAULT_APPID_MINI_PATH = "defaultAppidMiniPath";
        /**
         * 身份证导入接口需要检测黑卡的siteId
         */
        public static final String CHECK_BLACK_CARD_SITE_ID = "checkBlackCardSiteId";
        /**
         * 卡片消息id配置
         */
        public static final String CARD_MSG_ID = "cardMsgIds";
        /** 登录成功微信模版消息id **/
        public static final String AUTO_LOGIN_TEMPLATE_MSG_ID = "autoLoginTemplateMsgId";
        /** 免登数据上报开关 **/
        public static final String FREE_LOGIN_REPORT_SWITCH = "freeLoginReportSwitch";
        /** 手机绑定类型 默认小程序卡片消息 **/
        public static final String SINGLE_BIND_TYPE = "singleBindType";
        /** 网吧授权开关 默认开启 **/
        public static final String BAR_AUTH_SWITCH = "barAuthSwitch";
        /** 网吧免登协议开关 默认关闭 **/
        public static final String UN_AUTH_AGREEMENT_SWITCH = "unAuthAgreementSwitch";

        public static final String UN_AUTH_LIMIT_FREE_LOGIN_SWITCH = "unAuthLimitFreeLoginSwitch";
        /**
         * 计费多账号免登开关
         */
        public static final String UN_AUTH_FREE_LOGIN_FOR_MULTIPLE_ACCOUNT = "unAuthFreeLoginForMultipleAccount";

        /**
         * 跳转免登非授权检测开关
         */
        public static final String JUMP_FREE_LOGIN_UN_AUTH_CHECK_SWITCH = "jumpFreeLoginUnAuthLoginCheckSwitch";
    }


    public class Back {
        /**
         * 登录来源在缓存中的KEY
         */
        public final static String LOGIN_TYPE_MAP_CACHE_KEY = "LOGIN_TYPE_MAP";

        /**
         * business商户信息再缓存中的key
         */
        public final static String BUSINESS_MAP_CACHE_KEY = "BUSINESS_MAP_CACHE_KEY";

        /**
         * 地址信息在缓存中的key
         */
        public final static String AREA_MAP_CACHE_KEY = "AREA_MAP_CACHE_KEY";

        /**
         * UserRight类 在Cached中key的前缀
         */
        public static final String USER_RIGHT = "basePassport_user_right_";


    }


    public class Agreement {
        /**
         * 协议
         */
        public static final String AGREEMENT = "agreement";

        /**
         * 顺网通行证隐私政策
         */
        public static final String PRIVACY = "privacy";

        /**
         * 用户注册服务协议
         */
        public static final String USER_REGISTER = "register";
        /**
         * 顺网通行证注销协议
         */
        public static final String MEMBER_CANCEL = "member_cancel";

        /**
         * 顺网通行证实名认证服务协议
         */
        public static final String ACTUALITY = "actuality";

        /**
         * 顺网通行证防沉迷认证服务协议
         */
        public static final String ANTI_ADDITION = "anti_addition";

        /**
         * 顺网通行证实名认证变更服务协议
         */
        public static final String ACTUALITY_CHANGE = "actuality_change";

        public static final String FREE_LOGIN_AUTH = "free_login_auth";
    }

    public class SiteId {

        public static final String PASSPORT = "passport";

        public static final String PAY = "pay";
    }

    public class Platform {

        public static final String WEB = "web";

        public static final String h5 = "h5";

    }

    public class Css {
        public static final String LOGIN = "login";
        public static final String HTML5_CSS = "html5Css";
        public static final String OUT_OAUTH_CSS = "outOauthCss";
        public static final String PRIVACY_AGREEMENT = "privacyAgreement";
        public static final String USER_AGREEMENT = "userAgreement";
    }
}
