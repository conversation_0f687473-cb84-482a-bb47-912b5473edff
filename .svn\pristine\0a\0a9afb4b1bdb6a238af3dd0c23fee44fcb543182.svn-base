<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd" >
	<sqlMap namespace="com.shunwang.basepassport.config.pojo.Css">
	<typeAlias alias="css" type="com.shunwang.basepassport.config.pojo.Css"/>
	<resultMap class="com.shunwang.basepassport.config.pojo.Css" id="BaseResultMap">
		<result property="siteId" column="site_id" jdbcType="varchar"/>
		<result property="bussCode" column="buss_code" jdbcType="varchar"/>
		<result property="cssUrl" column="css_url" jdbcType="varchar"/>
		<result property="state" column="state" jdbcType="int"/>
		<result property="remark" column="remark" jdbcType="varchar"/>
		<result property="timeAdd" column="time_add" jdbcType="datetime"/>
		<result property="userAdd" column="user_add" jdbcType="varchar"/>
		<result property="timeEdit" column="time_edit" jdbcType="datetime"/>
		<result property="userEdit" column="user_edit" jdbcType="varchar"/>

	</resultMap>

	<sql id="baseColumn">
		a.site_id,
		a.buss_code,
		a.css_url,
		a.state,
		a.remark,
		a.time_add,
		a.user_add,
		a.time_edit,
		a.user_edit

	</sql>

	<select id="getBySiteIdAndBussCode" resultMap="BaseResultMap" parameterClass="css">
		select
		<include refid="baseColumn"/>
		from config_css a
		where a.site_id = #siteId# and a.buss_code = #bussCode#
	</select>
</sqlMap>