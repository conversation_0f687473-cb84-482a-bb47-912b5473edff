SWImportUtil.localCtx = "../js/local/";
SWImportUtil.modelMap['AreaView'] = 'local:area/AreaView.js';
SWImportUtil.modelMap['AreaModel'] = 'local:area/AreaModel.js';

SWImportUtil.modelMap['BussinessView'] = 'local:bussiness/BussinessView.js';
SWImportUtil.modelMap['BussinessModel'] = 'local:bussiness/BussinessModel.js';

SWImportUtil.modelMap['ServiceView'] = 'local:service/ServiceView.js';
SWImportUtil.modelMap['ServiceModel'] = 'local:service/ServiceModel.js';
SWImportUtil.modelMap['ServiceSelInp'] = 'local:service/ServiceSelInp.js';

SWImportUtil.modelMap['CityView'] = 'local:city/CityView.js';
SWImportUtil.modelMap['CityModel'] = 'local:city/CityModel.js';

SWImportUtil.modelMap['IpView'] = 'local:ip/IpView.js';
SWImportUtil.modelMap['IpModel'] = 'local:ip/IpModel.js';

SWImportUtil.modelMap['EditBackgroundView'] = 'local:editBackground/EditBackgroundView.js';
SWImportUtil.modelMap['EditBackgroundModel'] = 'local:editBackground/EditBackgroundModel.js';

SWImportUtil.modelMap['NoticeView'] = 'local:notice/NoticeView.js';
SWImportUtil.modelMap['NoticeModel'] = 'local:notice/NoticeModel.js';

SWImportUtil.modelMap['PageHomeView'] = 'local:pageHome/PageHomeView.js';
SWImportUtil.modelMap['PageHomeModel'] = 'local:pageHome/PageHomeModel.js';

SWImportUtil.modelMap['ProvinceView'] = 'local:province/ProvinceView.js';
SWImportUtil.modelMap['ProvinceModel'] = 'local:province/ProvinceModel.js';

SWImportUtil.modelMap['ReasonView'] = 'local:reason/ReasonView.js';
SWImportUtil.modelMap['ReasonModel'] = 'local:reason/ReasonModel.js';

SWImportUtil.modelMap['SensitiveWordView'] = 'local:sensitiveWord/SensitiveWordView.js';
SWImportUtil.modelMap['SensitiveWordModel'] = 'local:sensitiveWord/SensitiveWordModel.js';

SWImportUtil.modelMap['SiteFromView'] = 'local:siteFrom/SiteFromView.js';
SWImportUtil.modelMap['SiteFromModel'] = 'local:siteFrom/SiteFromModel.js';

SWImportUtil.modelMap['SiteInterfaceView'] = 'local:siteInterface/SiteInterfaceView.js';
SWImportUtil.modelMap['SiteInterfaceModel'] = 'local:siteInterface/SiteInterfaceModel.js';

SWImportUtil.modelMap['MemberAdviseView'] = 'local:memberAdvise/MemberAdviseView.js';
SWImportUtil.modelMap['MemberAdviseModel'] = 'local:memberAdvise/MemberAdviseModel.js';

SWImportUtil.modelMap['DictionaryView'] = 'local:dictionary/DictionaryView.js';
SWImportUtil.modelMap['DictionaryModel'] = 'local:dictionary/DictionaryModel.js';

SWImportUtil.modelMap['EditLogView'] = 'local:editLog/EditLogView.js';
SWImportUtil.modelMap['EditLogModel'] = 'local:editLog/EditLogModel.js';

SWImportUtil.modelMap['SysconfigView'] = 'local:sysconfig/SysconfigView.js';
SWImportUtil.modelMap['SysconfigModel'] = 'local:sysconfig/SysconfigModel.js';

SWImportUtil.modelMap['UseroutInterfaceView'] = 'local:useroutInterface/UseroutInterfaceView.js';
SWImportUtil.modelMap['UseroutInterfaceModel'] = 'local:useroutInterface/UseroutInterfaceModel.js';

SWImportUtil.modelMap['ConfigOauthView'] = 'local:configoauth/ConfigOauthView.js';
SWImportUtil.modelMap['ConfigOauthModel'] = 'local:configoauth/ConfigOauthModel.js';

SWImportUtil.modelMap['CssView'] = 'local:css/CssView.js';
SWImportUtil.modelMap['CssModel'] = 'local:css/CssModel.js';

SWImportUtil.modelMap['CategoryView'] = 'local:category/CategoryView.js';
SWImportUtil.modelMap['CategoryModel'] = 'local:category/CategoryModel.js';

SWImportUtil.modelMap['LoginElementView'] = 'local:loginElement/LoginElementView.js';
SWImportUtil.modelMap['LoginElementModel'] = 'local:loginElement/LoginElementModel.js';

SWImportUtil.modelMap['PartlyAccessView'] = 'local:partlyAccess/PartlyAccessView.js';
SWImportUtil.modelMap['PartlyAccessModel'] = 'local:partlyAccess/PartlyAccessModel.js';

SWImportUtil.modelMap['BusLoginElementView'] = 'local:busLoginElement/BusLoginElementView.js';
SWImportUtil.modelMap['BusLoginElementModel'] = 'local:busLoginElement/BusLoginElementModel.js';

SWImportUtil.modelMap['BusinessCategoryView'] = 'local:businessCategory/BusinessCategoryView.js';
SWImportUtil.modelMap['BusinessCategoryModel'] = 'local:businessCategory/BusinessCategoryModel.js';

SWImportUtil.modelMap['RichTextView'] = 'local:richText/RichTextView.js';
SWImportUtil.modelMap['RichTextModel'] = 'local:richText/RichTextModel.js';
