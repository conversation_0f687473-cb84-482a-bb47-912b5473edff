package com.shunwang.baseStone.editBackground.dao;


import com.shunwang.baseStone.cache.CacheKeyConstant;
import com.shunwang.baseStone.context.RedisContext;
import com.shunwang.baseStone.editBackground.pojo.EditBackground;
import com.shunwang.framework.ibatis.dao.ConditionQueryDao;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.orm.ibatis.SqlMapClientFactoryBean;

public class EditBackgroundDao extends ConditionQueryDao<EditBackground> {
	
	private static final Logger LOGGER = LoggerFactory.getLogger(EditBackgroundDao.class);


	private SqlMapClientFactoryBean sqlMapClient;

	public void setSqlMapClient(SqlMapClientFactoryBean sqlMapClient) {
		this.sqlMapClient = sqlMapClient;
	}

	public EditBackground findByActivityId(Integer id) {
		String key = CacheKeyConstant.BaseEditBackgroundConsts.ACTIVITY_KEY + id;
		EditBackground back = RedisContext.getRedisCache().get(key,EditBackground.class);
		LOGGER.info("************base平台从缓存获取积分活动配置为:*************" + back);
		if (back == null) {
			back = getActivityInfoById(id);
			RedisContext.getRedisCache().set(key, back);
		}
		return back;
	}
	
	public void cachedEditBackground(String key, EditBackground value) {
		RedisContext.getRedisCache().set(key, value);
	}
	
	public EditBackground getActivityInfoById(Integer id) {
		return (EditBackground) getSqlMapClientTemplate().queryForObject(getStatementNameWrap("getActivityInfoById"), id);
	}
	
	
	
	
	
}
