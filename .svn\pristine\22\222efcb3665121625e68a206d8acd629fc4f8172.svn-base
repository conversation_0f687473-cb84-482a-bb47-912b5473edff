package com.shunwang.basepassport.user.action;

import com.shunwang.util.date.DateUtil;
import com.shunwang.util.encrypt.Md5Encrypt;
import org.junit.Test;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;

/**
 * User:pf.ma
 * Date:2017/07/03
 * Time:17:19
 */
public class TestMemberInfoModify {
	@Test
	public void test() throws UnsupportedEncodingException {
		String siteId = "Passport";
		String md5Key = "123456" ;

		String userType = "2" ;

		String userName = "feifei0013" ;
		String realName = "你好·额" ;
		String identity = "342623199308089987" ;

		String email = "" ;
		String mobile = "" ;
		String protectQuestionId1 = "" ;
		String protectQuestionId2 = "" ;
		String protectQuestionId3 = "" ;
		String protectAnswer1 = "";
		String protectAnswer2 = "" ;
		String protectAnswer3 = "" ;
		String emailIsBind = "" ;
		String mobileIsBind = "" ;
		String companyName = "" ;
		String linkMan = "你好·额" ;
		

		String time = DateUtil.getCurrentDateStamp();
;

		String plainText = siteId+"|"+userName+"|"+userType+"|"+email+"|"+mobile+"|"+realName+"|"+identity+"|"+protectQuestionId1+"|"+protectQuestionId2
				+"|"+protectQuestionId3+"|"+protectAnswer1+"|"+protectAnswer2+"|"+protectAnswer3+"|"+emailIsBind+"|"+mobileIsBind +"|"+ companyName +"|"+ linkMan
				+"|"+time +"|"+ md5Key ;

		String sign = Md5Encrypt.encrypt(URLEncoder.encode(plainText, "UTF-8").toUpperCase(), "UTF-8").toUpperCase();

		String respond = "";
		HttpURLConnection conn = null;

		try {
			URL url = new URL("http://interface.kedou.com/front/interface/outUpdateUserMsg.htm");
			conn = (HttpURLConnection) url.openConnection();
			conn.setDoOutput(true);
			conn.setUseCaches(false);
			conn.setRequestMethod("POST");
//			conn.setRequestProperty("Accept", "application/json");
			conn.getOutputStream().write(("siteId=" + siteId +"&userName="+userName+"&userType="+userType
					+"&linkMan="+URLEncoder.encode(URLEncoder.encode(linkMan,"utf-8"))
					+"&realName="+URLEncoder.encode(URLEncoder.encode(realName,"utf-8"),"utf-8")+"&identity=" +identity +"&time="+time + "&sign=" + sign).getBytes());
			conn.connect();

			BufferedReader reader = new BufferedReader(new InputStreamReader(conn.getInputStream(), "utf-8"));
			String lines = "";

			while ((lines = reader.readLine()) != null) {
				respond += lines;
			}

			reader.close();
			conn.disconnect();
		} catch (Exception e) {
			//e.printStackTrace();
			respond = e.getMessage();
		} finally {
			if (conn != null) {
				conn.disconnect();
			}
			conn = null;
		}

		System.out.println(respond);
	}
}
