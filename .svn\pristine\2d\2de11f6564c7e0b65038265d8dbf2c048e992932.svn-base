package com.shunwang.baseStone.useroutinterface.dao;

import com.shunwang.baseStone.cache.CacheKeyConstant;
import com.shunwang.baseStone.context.RedisContext;
import com.shunwang.baseStone.core.dao.CacheMapDao;
import com.shunwang.baseStone.core.dao.RefreshMem;
import com.shunwang.baseStone.useroutinterface.pojo.UseroutInterface;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Deprecated
public class UseroutInterfaceDao extends CacheMapDao<UseroutInterface> implements RefreshMem{

	private final static Logger log = LoggerFactory.getLogger(UseroutInterfaceDao.class);
	
	@Override
	protected String getKey(UseroutInterface pojo) {
		return CacheKeyConstant.UserOutInterfaceConstants.BASE_USER_OUT_INTERFACE_KEY+CacheKeyConstant.CACHE_SPLIT+pojo.getInerfaceId();
	}
	public UseroutInterface getById(String intefaceId) {
		return super.getById(intefaceId);
	}

	@Override
	protected UseroutInterface loadCachePojo(UseroutInterface pojo) {
		UseroutInterfaceDbDao dao =(UseroutInterfaceDbDao)this.getDbDao();
		UseroutInterface ret = dao.getByTypeAndKey(pojo.getOutServiceId(),pojo.getServiceKey());
		return ret;
	}
	
	@Override
	public void refresh() {
		Map<String, UseroutInterface> ret=this.loadMap();
		saveMap(ret);
		for(String key : ret.keySet()){
			refreshCachePojo(ret.get(key));
		}
	}
	
	public void refreshCachePojo(UseroutInterface pojo) {
		saveCachePojo(getKey(pojo),pojo);
	}
	
	/**
	 * 查询前缀名list
	 * 
	 * @return
	 * <AUTHOR> 创建于 2012-2-3 上午10:04:24
	 * @throws
	 */
	public List<String> getPrefixNameList(){
		List<String> list =  RedisContext.getRedisCache().get(CacheKeyConstant.UserOutInterfaceConstants.BUS_ALL_PREFIX_NAME_KEY,List.class);
		if (list == null || list.size() == 0) {
			return refreshPrefixNameList();
		}
		return list;
	}
	
	private List<String> refreshPrefixNameList(){
		List<String> prefixNameList = ((UseroutInterfaceDbDao)getDbDao()).getPrefixNameList();
		if (prefixNameList == null) {
			return new ArrayList<>();
		}
		RedisContext.getRedisCache().set(CacheKeyConstant.UserOutInterfaceConstants.BUS_ALL_PREFIX_NAME_KEY, prefixNameList);
		return prefixNameList;
	}
	
	@Override
	protected String getClassName() {
		return "com.shunwang.baseStone.useroutinterface.pojo.UseroutInterface";
	}
}
