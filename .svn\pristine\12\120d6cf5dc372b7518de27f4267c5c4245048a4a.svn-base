package com.shunwang.baseStone.sso.apapter;

import com.shunwang.baseStone.cache.CacheKeyConstant;
import com.shunwang.baseStone.context.RedisContext;
import com.shunwang.baseStone.core.exception.BaseStoneException;
import com.shunwang.baseStone.siteinterface.context.SiteContext;
import com.shunwang.baseStone.sso.constant.UserOutsiteConstant;
import com.shunwang.baseStone.sso.context.ClientTicketContext;
import com.shunwang.baseStone.sso.exception.ValidateExp;
import com.shunwang.baseStone.useroutinterface.constant.UseroutConstant;
import com.shunwang.basepassport.actu.common.IdentityCardUtil;
import com.shunwang.basepassport.binder.common.ErrorCode;
import com.shunwang.basepassport.config.common.UserOutInterfaceUtil;
import com.shunwang.basepassport.config.pojo.SiteInterface;
import com.shunwang.basepassport.manager.bean.ReportEntity;
import com.shunwang.basepassport.user.common.MemberConstants;
import com.shunwang.basepassport.user.common.UserLoginSessionUtil;
import com.shunwang.basepassport.user.pojo.Member;
import com.shunwang.basepassport.user.pojo.MemberOutSite;
import com.shunwang.basepassport.user.service.BarBlackCardService;
import com.shunwang.basepassport.user.service.DcReportService;
import com.shunwang.util.encrypt.Md5Encrypt;
import com.shunwang.util.lang.StringUtil;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigInteger;
import java.util.List;
import java.util.Map;

/**
 * 根据身份证号码导入外部用户并生成对应的通行证账号
 *
 * <AUTHOR>
 * @since 2015-03-26
 */
public class IdCardAdapter extends SimpleImportUsersAdapter {

    private static final Logger log = LoggerFactory.getLogger(IdCardAdapter.class);

    private String extInfo;

    private BarBlackCardService barBlackCardService;

    @Override
    public String bind() {
        Map<String, Object> json = createResultJSON(false);
        try {
            validParam(); // 校验参数
            userOutInterface = UserOutInterfaceUtil.loadUserOutInterface(getInterfaceId()); // 取得用户导入接口配置
            if (StringUtils.equals(userOutInterface.getServiceState(), UseroutConstant.S_Open)) { // 如果接口状态是开启的
                SiteContext.setSiteName(UserOutsiteConstant.SITE_INTERFACE_IMPORT_USER_IDCARD);
                SiteContext.setSiteId(getSiteId());
                SiteInterface siteInterface = SiteContext.getSiteInterface(); // 取得商户该服务的配置
                if (null != siteInterface && siteInterface.getState().equals(Integer.valueOf(UseroutConstant.S_Open))) { // 如果该商户分配了该服务并且状态是开启
                    userOutInterface.setServiceKey(siteInterface.getMd5Key()); // 设置md5key，后面校验签名要用到
                    validSign(); // 校验签名
                    MemberOutSite memberOutSite = buildMemberOutSite(); // 构建外部用户
                    //请求由客户端发起，计费拿不到出口ip，传来的都是局域网ip，所以不使用clientIp
//                    IPContext.setIp(StringUtils.isNotBlank(getClientIp()) ? getClientIp() : IpUtil.getIpAddress(getRequest()));
                    outReg(memberOutSite); // 注册(先根据outMemberId查询member表的memberName，如果用户存在则直接返回；不存在则注册并返回memberId，再更新外部用户表的memberId字段。)
                    json.put(RESULT, true);
                    json.put("memberId", member.getMemberId());
                    json.put("memberName", member.getMemberName());
                    if (userOutInterface.isAutoLoginOpen()) {
                        login(member);
                        json.put("ticketId", this.getTicket());
                        json.put("tockenId", this.getTockenId());
                        List<String> siteList = RedisContext.getStringListConfig(CacheKeyConstant.ConfigResourcesConstants.NET_BAR_FREE_LOGIN_CONFIG,
                                CacheKeyConstant.ConfigResourcesConstants.CHECK_BLACK_CARD_SITE_ID);
                        if (siteList.contains(getSiteId())) {
                            json.put("clientTicket", ClientTicketContext.createClientTicket(member, getSiteId()).getClientTicketId());
                            UserLoginSessionUtil.saveSession(member.getMemberName(), UserLoginSessionUtil.LoginType.SSO_IMPORT_NET_BAR.getType(), null, getSiteId(), extInfo);
                            DcReportService.freeLoginReport(member.getMemberName(), DcReportService.OptTypeAndExtInfo.ID_CARD_IMPORT_SUCCESS);
                        }
                    }
                } else {
                    json.put(MSG, "服务未分配或已关闭。");
                }
            } else {
                return writeErrorJson(ErrorCode.C_1014);
            }
        } catch (ValidateExp e) {
            json.put(MSG, e.getMsg());
        } catch (BaseStoneException e) {
            return writeErrorJson(e);
        } catch (Exception e) {
            if (log.isErrorEnabled())
                log.error(getServiceProvider() + "账号导入出现异常：" + e.getMessage(), e);
            json.put(MSG, "系统异常");
        }
        return writeJson(json);
    }

    @Override
    protected void validParam() throws Exception {
        super.validParam();
        if (!isUserIdEncrypt() && !IdentityCardUtil.validate(getUserId())) {
            throw new ValidateExp("不是正确的身份证号码");
        }
        List<String> siteList = RedisContext.getStringListConfig(CacheKeyConstant.ConfigResourcesConstants.NET_BAR_FREE_LOGIN_CONFIG,
                CacheKeyConstant.ConfigResourcesConstants.CHECK_BLACK_CARD_SITE_ID);
        if (siteList.contains(getSiteId())) {
            boolean blackCardCheck = barBlackCardService.checkBlackCard(encryptUserId());
            if (!blackCardCheck) {
                throw new ValidateExp("黑卡用户");
            }
        }
    }

    /**
     * 避免无必要的调用member.loginWithNoPwd()，但memberOutSite.findOrSave()里用户存在的情况会调一次，
     * 但这部分代码在interface那个项目里，需要新拉分支，暂时这样做，用户存在的情况下可以少调用一次
     *
     * @param memberOutSite
     * @throws Exception
     */
    @Override
    protected void outReg(MemberOutSite memberOutSite) throws Exception {
        try {
            memberOutSite.setMemberFrom(getInterfaceId());
            member = findOrSave(memberOutSite);
            if (member == null) {
                throw new BaseStoneException(ErrorCode.C_1211);
            }
            member.setLoginType(getInterfaceId());
        } catch (Exception e) {
            if (log.isErrorEnabled())
                log.error("调用outReg时出现异常：" + e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 导入之前为明文导入，现在需要转为密文，新数据入库采用密文，为了兼容明文，未加密的会使用明文查我方数据都改成密文后，全部切换成密文
     * @param memberOutSite
     * @return
     */
    protected Member findOrSave(MemberOutSite memberOutSite){
        Member member = null;
        MemberOutSite memberOut = memberOutSite.getDao().getMemberByOutMemberNameAndFrom(encryptUserId(), Integer.parseInt(getInterfaceId()));
        if (memberOut != null) {
            member = memberOutSite.getDao().getByOutMemberId(memberOut.getOutMemberId());
        } else if(!isUserIdEncrypt()){
            member = memberOutSite.getDao().getByOutMemberId(getOldOutMemberId());
        }
        if(null != member){
            member.setLoginType(MemberConstants.LOGIN_TYPE_ACCOUNT);
            member.setVrsion(StringUtil.isNotBlank(memberOutSite.getVersion())? memberOutSite.getVersion() : "");
            member.setEnv(memberOutSite.getEnv());
            member.setExtData(memberOutSite.getRemark());
            member.loginWithNoPwd();
            doReport(member, ReportEntity.InterfaceType.login);
            return member;
        }
        memberOutSite.setOutMemberName(encryptUserId());
        memberOutSite.setLevel("1");
        Member regMember = interfaceService.outSiteMemberRegister(memberOutSite);
        doReport(regMember, ReportEntity.InterfaceType.reg);
        return regMember;
    }

    @Override
    public String getInterfaceId() {
        return UserOutsiteConstant.IDCARD_INTERFACE_ID;
    }

    /**
     * 将身份证号码由十进制转成三十六进制(末位是X的要先去掉)
     * @return base后台配置的前缀 + 三十六进制后的身份证号码
     */
    @Override
    protected String genMemberName() {
        String encryptUserId = isUserIdEncrypt() ? getUserId() : encryptUserId();
        BigInteger bi = new BigInteger(encryptUserId.replace("JF",""), 16);
        return userOutInterface.getPrefixName() + bi.toString(36);
    }

    private String getOldOutMemberId() {
        String idCard = getUserId().toLowerCase();
        if (idCard.contains("x")) {
            idCard = StringUtils.removeEnd(idCard, "x");
        }
        BigInteger bi = new BigInteger(idCard, 10);

        return userOutInterface.getPrefixName() +
                bi.toString(36);
    }

    private boolean isUserIdEncrypt() {
        return getUserId().startsWith("JF");
    }

    private String encryptUserId() {
        if (isUserIdEncrypt()) {
            return getUserId();
        }
        String key = "J!@F^3Sa21fe";
        return "JF" + Md5Encrypt.encrypt(getUserId().toLowerCase() + key).toUpperCase();
    }

    public BarBlackCardService getBarBlackCardService() {
        return barBlackCardService;
    }

    public void setBarBlackCardService(BarBlackCardService barBlackCardService) {
        this.barBlackCardService = barBlackCardService;
    }

    public String getExtInfo() {
        return extInfo;
    }

    public void setExtInfo(String extInfo) {
        this.extInfo = extInfo;
    }
}
