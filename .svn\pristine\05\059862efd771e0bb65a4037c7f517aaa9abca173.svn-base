function Avatar(a,b){this.$el=$(a),this.$uploadForm=this.$el.find("#upload-form"),this.$act=this.$el.find(".act"),this.$native=this.$el.find(".native img"),this.$middle=this.$el.find(".middle .preview img"),this.$small=this.$el.find(".small .preview img"),this.$msgModal=$("#msg-modal"),this.dotSrc=this.$native.attr("src"),this.ias=null,this.maxWidth=300,this.maxHeight=300,this.initialize(),b?(this.avatar=b,this.image=this.avatar.image,this.coordinate=this.avatar.coordinate,this.initAvatar()):(this.coordinate={x1:0,y1:0,x2:120,y2:120},this.$act.hide())}$(function(){$(".sidebar .menu li :eq(1)").addClass("current");var a=new Avatar("#avatar",!1);window.UploadHandler=function(b){a.handler(b)}}),Avatar.prototype={initialize:function(){$("#upload").change($.proxy(this.submit,this)),this.$act.find(".reupload").click($.proxy(this.reupload,this)),this.$el.find(".btn_default_lg").click($.proxy(this.save,this))},showTips:function(a){this.$msgModal.find(".modal-dialog").text(a),this.$msgModal.modal("show")},reupload:function(){return $("#upload").val(""),this.reset(),this.$uploadForm.show(),this.$act.hide(),!1},submit:function(){this.$uploadForm.submit()},handler:function(a){var b=this[a.status];b&&(this.result={status:a.status,msg:a.msg},this.image={src:a.url,filename:a.filename,width:a.width,height:a.height},b.call(this))},success:function(){this.$act.show(),this.adjustImage(),this.$uploadForm.hide(),this.initImgAreaSelect()},failure:function(){"undefined"!=typeof this.result.msg?this.showTips(this.result.msg):this.showTips("上传失败")},initAvatar:function(){var a=this;this.adjustImage(),this.$uploadForm.hide(),a.initImgAreaSelect()},reset:function(){var a=this;this.$native.imgAreaSelect({remove:!0}),$.each(["native","middle","small"],function(b,c){a["$"+c].attr("src",a.dotSrc).removeAttr("style")})},adjustImage:function(a){var a=a||this.image,b=a.width,c=a.height,d=120,e=120;this.$native.attr("src",a.src),this.$middle.attr("src",a.src),this.$small.attr("src",a.src),this.$native.removeAttr("width"),this.$native.removeAttr("height"),a.width>=this.maxWidth||a.height>=this.maxHeight?a.width>a.height?(b=this.maxWidth,c=this.maxWidth/a.width*a.height,d=e=120*(a.height/this.maxHeight),this.$native.css({"margin-top":-(this.maxWidth/a.width*a.height/2)})):(b=this.maxHeight/a.height*a.width,c=this.maxHeight,d=e=120*(a.width/this.maxWidth),this.$native.css({"margin-left":-(this.maxHeight/a.height*a.width/2)})):(a.width<120&&a.height<120&&this.$middle.css({width:120,height:120}),a.width<56&&a.height<56&&this.$small.css({width:56,height:56}),this.$native.css({"margin-left":-(b/2),"margin-top":-(c/2)})),this.$native.attr("width",b),this.$native.attr("height",c),this.coordinate.x2=d,this.coordinate.y2=e},initImgAreaSelect:function(){this.ias=this.$native.imgAreaSelect({x1:this.coordinate.x1,y1:this.coordinate.y1,x2:this.coordinate.x2,y2:this.coordinate.y2,imageWidth:this.image.width,imageHeight:this.image.height,minHeight:256,minWidth:256,aspectRatio:"4:4",handles:!0,persistent:!0,fadeSpeed:200,onSelectChange:$.proxy(this.preview,this),onInit:$.proxy(this.preview,this)})},preview:function(a,b){b.width&&b.height&&(this.$middle.css({width:Math.round(120/b.width*this.image.width),height:Math.round(120/b.height*this.image.height),marginLeft:-Math.round(120/b.width*b.x1),marginTop:-Math.round(120/b.height*b.y1)}),this.$small.css({width:Math.round(56/b.width*this.image.width),height:Math.round(56/b.height*this.image.height),marginLeft:-Math.round(56/b.width*b.x1),marginTop:-Math.round(56/b.height*b.y1)}),this.areaSelectData={x1:b.x1,y1:b.y1,x2:b.x2,y2:b.y2,w:b.width,h:b.height})},save:function(){var b,c,d,e,a=this;return $("#upload").val()?(b=-1,c=-1,d=-1,e=-1,this.areaSelectData&&(b=this.areaSelectData.x1,c=this.areaSelectData.y1,d=this.areaSelectData.x2,e=this.areaSelectData.y2),$.ajax({url:"/front/member/headImgGen.htm",type:"post",data:{x1:b,y1:c,x2:d,y2:e,filename:this.image.filename},dataType:"json",success:function(b){b.result&&(a.showTips("保存成功"),setTimeout(function(){window.location.href=$CONFIG.appServer+"/front/member/securityCenter_index.htm"},2500))},error:function(){}})):this.showTips("请上传头像"),!1}};