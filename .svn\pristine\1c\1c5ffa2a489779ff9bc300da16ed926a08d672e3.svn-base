<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd" >
<sqlMap namespace="com.shunwang.baseStone.css.pojo.Css">
<resultMap class="com.shunwang.baseStone.css.pojo.Css" id="BaseResultMap">
	<result property="siteId" column="siteId" jdbcType="varchar"/>
	<result property="bussCode" column="bussCode" jdbcType="varchar"/>
	<result property="cssURL" column="cssURL" jdbcType="varchar"/>
	<result property="remark" column="remark" jdbcType="varchar"/>
    <result property="state" column="state" jdbcType="INTEGER"/>
	<result property="timeadd" column="timeadd" jdbcType="datetime"/>
	<result property="timeedit" column="timeedit" jdbcType="datetime"/>
	<result property="useradd" column="useradd" jdbcType="varchar"/>
	<result property="useredit" column="useredit" jdbcType="varchar"/>
</resultMap>
<select id="find" resultMap="BaseResultMap" parameterClass="com.shunwang.framework.ibatis.query.ConditionQuery" >
	SELECT
		t.siteId,
		t.bussCode,
		t.cssURL,
		t.remark,
		t.state,
		t.timeadd,
		t.useradd,
		t.timeedit,
		t.useredit	from config_css t
	<isParameterPresent >
	<include refid="Example_Where_Clause" />
	</isParameterPresent>
	order by
	<isNotNull property="orderCol" >
		$orderCol$
	</isNotNull>
	<isNull property="orderCol" >
		t.timeedit desc,t.timeadd desc 
	</isNull>
	<isNotEqual property="rp" compareValue="0" >
	    limit #firstResult#, #rp#
	</isNotEqual>
</select>
<insert id="insert" parameterClass="com.shunwang.baseStone.css.pojo.Css" >
	insert into config_css (
		siteId,
		bussCode,
		cssURL,
		remark,
		state,
		timeadd,
		useradd,
		timeedit,
		useredit	)values(
		#siteId:varchar#,
		#bussCode:varchar#,
		#cssURL:varchar#,
		#remark:varchar#,
		#state:INTEGER #,
		#timeadd:datetime#,
		#useradd:varchar#,
		#timeedit:datetime#,
		#useredit:varchar#)
</insert>
<update id="update" parameterClass="com.shunwang.baseStone.css.pojo.Css" >
	update config_css set
		cssURL=#cssURL:varchar#,
		remark=#remark:varchar#,
		state = #state:INTEGER #,
		timeedit=#timeedit:datetime#,
		useredit=#useredit:varchar# 
		where bussCode = #bussCode:varchar# and siteId=#siteId:varchar# 
</update>
<delete id="delete" parameterClass="com.shunwang.baseStone.css.pojo.Css" >
	delete from config_css where bussCode=#bussCode:varchar# and siteId=#siteId:varchar#
</delete>
<select id="get" resultMap="BaseResultMap" parameterClass="String">
	select
	siteId,
	bussCode,
	cssURL,
	remark,
	state,
	timeadd,
	useradd,
	timeedit,
	useredit	from config_css
	where bussCode = #value#
</select>
<select id="findCnt" parameterClass="com.shunwang.framework.ibatis.query.ConditionQuery" resultClass="java.lang.Integer" >
	select count(*) from config_css t
	<include refid="Example_Where_Clause" />
</select>
<select id="getCssBySiteIdAndBussCode" resultMap="BaseResultMap" parameterClass="com.shunwang.baseStone.css.pojo.Css">
	select
	siteId,
	bussCode,
	cssURL,
	remark,
	state,
	timeadd,
	useradd,
	timeedit,
	useredit	from config_css
	where siteId=#siteId:varchar# and  bussCode=#bussCode:varchar#
</select>
<select id="findAll" resultMap="BaseResultMap">
	select
	siteId,
	bussCode,
	cssURL,
	remark,
	state,
	timeadd,
	useradd,
	timeedit,
	useredit	from config_css
</select>

</sqlMap>