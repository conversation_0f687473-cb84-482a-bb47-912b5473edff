﻿body,h1,h2,h3,h4,h5,h6,hr,p,blockquote,dl,dt,dd,ul,ol,li,pre,form,fieldset,legend,button,input,textarea,th,td,div{margin:0;padding:0}body,button,textarea{font:12px/1.5 tahoma,'宋体',arial,\5b8b\4f53,sans-serif}
h1,h2,h3,h4,h5,h6{font-size:100%}address,cite,dfn,em,var{font-style:normal}
code,kbd,pre,samp{font-family:courier new,courier,monospace}
small{font-size:12px}ul,ol{list-style:none}a:hover{text-decoration:underline}
a:focus{outline:0}sup{vertical-align:text-top}sub{vertical-align:text-bottom}legend{color:#000}
fieldset,img{border:0}button,input,select,textarea{font-size:100%}table{border-collapse:collapse;border-spacing:0}
/* font size */
.f12{font-size:12px}.f13{font-size:13px}.f14{font-size:14px}.f16{font-size:16px}.f20{font-size:20px}
/* font bold */
.fb{font-weight:bold}.fn{font-weight:normal}
.t2{text-indent:2em}
/* line-height */
.lh150{line-height:150%}.lh180{line-height:180%}.lh200{line-height:200%}
.unl{text-decoration:underline}
.no_unl{text-decoration:none}
/* position */
.tc{text-align:center}.tr{text-align:right}.tl{text-align:left}
.bc{margin-left:auto;margin-right:auto}
.fr{float:right;display:inline}.fl{float:left;display:inline}
.cb{clear:both}.cr{clear:right}.cl{clear:left}
.vm{vertical-align:middle}.pr{position:relative}.pa{position:absolute}.abs—right{position:absolute;right:0}.zoom{zoom:1}
.hidden{visibility:hidden}.none{display:none}
/* clearfix */
.clearfix:after{content:".";display:block;height:0;clear:both;visibility:hidden}.clearfix{display:inline-block;}
*html .clearfix{height:1%}.clearfix{display:block; clear:both}
.clear{ clear:both;}

.login-loading { height:100%;  background:url(../images/load.gif) no-repeat center; min-height:200px; _height:200px; }
.login-box2 { padding-top:29px;}
.login-box2 .info { margin-bottom:5px; line-height:18px; text-align:center;}
.login-box2 .error { line-height:18px; text-align:center; color:#f00; margin-bottom:10px;}
.login-box2 .other { padding-top:13px; text-align:center;}
.login-box2 .other a { color:#347ab8;}

.user-box { width:215px; height:85px; position:relative; margin:auto;}
.user-box .prev,
.user-box .next { width:14px; height:85px; position:absolute; top:0; background:url(../images/gallery-btn.gif) no-repeat left center; cursor:pointer;}

.user-box .prev:hover,
.user-box .next:hover { background:url(../images/gallery-btn-hover.gif) no-repeat left center;}

.user-box .prev { left:0;}
.user-box .next { right:0; background-position:right center;}

.user-box .prev:hover { left:0;}
.user-box .next:hover { right:0; background-position:right center;}

.user-box .prev-dis,
.user-box .next-dis,
.user-box .prev-dis:hover,
.user-box .next-dis:hover { background:url(../images/gallery-btn-disabled.gif) no-repeat left center; cursor:default;}

.user-box .prev-dis { left:0;}
.user-box .next-dis { right:0; background-position:right center;}

.user-box .prev-dis:hover { left:0;}
.user-box .next-dis:hover { right:0; background-position:right center;}

.user-cont { margin:0 34px; height:85px; overflow:hidden; position:relative;}
.user-list { height:85px; overflow:visible; position:absolute; left:0; top:0; white-space:nowrap; font-size:0;}
.user-list li { width:58px; height:85px; display:inline-block; *display:inline; *zoom:1; margin-right:31px;}
.user-list li img { width:56px; height:56px; display:block; border:1px solid #afc9d6;}
.user-list li a { color:#3379b5; text-decoration:none;}
.user-list li span { height:20px; margin-top:7px; display:block; line-height:20px; cursor:pointer; width:100%; overflow:hidden; white-space:nowrap;text-overflow:ellipsis; font-size:12px; text-align:center;}
#safeDiv{display:none}

#dialog-checkcode{position: absolute;top: 0;bottom: 0;left: 0;right: 0;}

/* 微信扫码登陆 */
.weChat-login{height: 384px;margin: 0 auto; padding-top: 34px;text-align: center;overflow: hidden;}
.weChat-login div.title{font-size: 22px; font-weight:400; color: #4F525A;font-family:FZZhunYuan-M02S;}
/*  二维码盒子 */
.weChat-login div.code-box{width: 100%;height: 188px; margin: 24px 0px 10px 0px; text-align: center;}
/*  二维码登录 */
.weChat-login div.code-box .code_login{ width: 100%;height: 188px;}
.weChat-login div.code-box .code_login img{margin: 0 auto;}
/* 加载中样式 */
.weChat-login div.code-box .code_load{ width: 100%;height: 188px; background: url(../images/code_load.png) center center no-repeat; position: relative;}
.weChat-login div.code-box .code_load .load-icon{ position: absolute; background: url(../images/loadicon.png) center center no-repeat;width: 100%;height: 49px; z-index: 100;top: 30%;}
.weChat-login div.code-box .code_load .load-text{  width: 100%;  position: absolute;  top: 58%; color: #4F525A; font-size: 12px;}
/* 失效样式 */
.weChat-login div.code-box .code_fail{ width: 100%;  height: 188px; background: url(../images/code_fail.png) center center no-repeat; position: relative;}
.weChat-login div.code-box .code_fail .code_fail_box{ width: 100%; position: absolute;  top: 30%;  font-size: 12px; color: #4F525A;}
.weChat-login div.code-box .code_fail .code_fail_box .code_refresh_btn button{ width:140px; height:40px; font-size: 14px; color: #FFFFFF; font-weight:400; background:rgba(0,177,238,1); border: none; outline: none; cursor: pointer;  margin-top: 9px;}
/* 二维码底部文字 */
.weChat-login div.text{  font-size: 12px; color: #999999; font-family:SimSun;}
.weChat-login div.change-login-type { display: none;}
.weChat-login div.back-passport{ position: relative; left: 116px; margin-top: 22px;}
.weChat-login div.back-passport a{ text-decoration: none; font-size: 12px; color: #09B4EF; font-family:SimSun;}
.weChat-login div.back-passport a span{ position: relative;  bottom: 3px;}

/* 微信扫码登陆 */
.weChat-login2{ height: 384px;  margin: 0 auto; padding-top: 34px; text-align: center; overflow: hidden;}
/* 微信撤销提示 */
.weChat-repeal-tip-content { height: 409px; margin: 0 auto;  text-align: center; overflow: hidden;}
.weChat-repeal-tip-content .weChat-repeal-box{ width: 188px; height: 188px; margin: 0 auto; margin-top: 109px; color: #4F525A;}
.weChat-repeal-tip-content .weChat-repeal-box .title{  font-size:22px; font-family:FZY3JW;  margin-top: 30px; margin-bottom: 33px;}
.weChat-repeal-tip-content .weChat-repeal-box button{ width:120px; height:40px;  border: none; outline: none;  background:rgba(0,177,238,1);  color: #FFFFFF; margin-top: 10px;}
.weChat-repeal-tip-content .weChat-repeal-box button{  width:120px; height:40px; border: none; outline: none;}
.weChat-repeal-tip-content .weChat-repeal-box button.btn-repeal{ background:rgba(0,177,238,1); color: #FFFFFF; margin-right: 20px;}
.weChat-repeal-tip-content .weChat-repeal-box button.btn-cancel{ border: 1px solid #9A9EA7; display: none}
.weChat-repeal-tip-content .back-passport{ text-align: right; margin-right: 35px; margin-top: 30px;}
/* 小程序二维码登录start ps:在上面-微信扫码登陆样式基础上更改 */
.mini-weChat-login div.code-box .code_login{ margin-left: 0px;}
/*设置小程序二维码显示大小，自定义大小请覆盖此样式*/
.mini-weChat-login div.code-box .code_login img{ width: 188px; height: 188px;}
.mini-weChat-login div.code-box .code_load { background: url('../images/application_code_loading.png') center center no-repeat;}
.mini-weChat-login div.code-box .code_fail{  background: url('../images/application_code_failure.png') center center no-repeat;}
.mini-weChat-login div.code-box .code_fail .code_fail_box .code_refresh_btn button{  background:rgba(252,125,0,1);  border-radius:2px;}
/* 小程序二维码登录end */
.login-box .submit .single_cancel {  display: none;}
.app-weChat-login{ height: 384px; margin: 0 auto; padding-top: 34px; text-align: center;  overflow: hidden; position: relative;}
.app-weChat-login .title{  color:rgba(79,82,90,1);  font-size:22px;}
.app-weChat-login div.title{  font-size: 22px; font-weight:400; color: #4F525A; font-family:FZZhunYuan-M02S;}
/*  二维码盒子 */
.app-weChat-login div.code-box{ width: 100%; height: 188px;  margin: 24px 0px 10px 0px;  text-align: center;}
/*  二维码登录 */
.app-weChat-login div.code-box .code_login{ width: 100%; height: 188px;}
.app-weChat-login div.code-box .code_login .code-load-desc{  color: #999999;}
/* 加载中样式 */
.app-weChat-login div.code-box .code_load{ width: 100%;  height: 188px; background: url(../images/code_load.png) center center no-repeat;  position: relative;}
.app-weChat-login div.code-box .code_load .load-icon{  position: absolute; background: url(../images/loadicon.png) center center no-repeat; width: 100%;  height: 49px;  z-index: 100;  top: 30%;}
.app-weChat-login div.code-box .code_load .load-text{ width: 100%; position: absolute; top: 58%;  color: #4F525A; font-size: 12px;}
.app-weChat-login div.code-box .code_load .code-load-desc{ position: absolute; width: 100%; height: 49px;  z-index: 100; bottom: -55px; color: #999999;}
.app-weChat-login .symbol-1{ position: relative;  top: 5px; left: 3px;}
.app-weChat-login .symbol-2{ position: relative;  top: 5px;}
/* 失效样式 */
.app-weChat-login div.code-box .code_fail{  width: 100%; height: 188px;  background: url(../images/code_fail.png) center center no-repeat;  position: relative;}
.app-weChat-login div.code-box .code_fail .code_fail_box{  width: 100%; position: absolute;  top: 30%; font-size: 12px; color: #4F525A;}
.app-weChat-login div.code-box .code_fail .code-fail-desc{  width: 100%;  position: absolute;  bottom: -32px; font-size: 12px; color: #999999;}
.app-weChat-login div.code-box .code_fail .code_fail_box .code_refresh_btn button{ width:140px;  height:40px; font-size: 14px; color: #FFFFFF;  font-weight:400;  background:rgba(0,177,238,1);  border: none; outline: none; cursor: pointer; margin-top: 9px;}
/* 成功样式 */
.app-weChat-login div.code-box .code_success{  width: 100%;  height: 188px; background: url(../images/code_fail.png) center center no-repeat; position: relative;}
.app-weChat-login div.code-box .code_success .code_success_box{ width: 100%; position: absolute; top: 20%; font-size: 12px;  color: #4F525A;}
.app-weChat-login div.code-box .code_success .code_success_box .code_success_text{  color: #4F525A;  font-size:14px;  font-family:Microsoft YaHei; font-weight:bold; margin: 5px 0;}
.app-weChat-login div.code-box .code_success .code-success-desc{   width: 100%;  position: absolute; bottom: -32px; font-size: 12px; color: #999999;}
/* 安全验证提示信息-passport */
.security-verification-passport {  text-align: center; padding-top: 58px;}
.security-verification-passport .waring-icon { display: block;}
.security-verification-passport .tip {  font-size: 22px; margin-top: 10px;}
.security-verification-passport .desc { font-size: 16px;}
.security-verification-passport button {  width: 140px; height: 40px; font-size: 16px;}
/* app登录 账号页图标 */
.passport-login .app_qr_code img{  position: absolute; left: 56%; top: 0;}
/* app登录 手机登录页图标 */
.sms-login .app_qr_code img{ position: absolute; left: 56%; top: 0;}
/* app扫码登录页图标 */
.app-weChat-login .phone-code img{ position: absolute; right: 44%; top: -4%;}
/* app扫码登录页二维码 */
.app-weChat-login div.code-box .code_login .app_code_login_qr{ position: relative; left: 63px;}
/* 极验样式 */
.geetest_wind.geetest_panel .geetest_panel_box.geetest_panelshowclick { width: 188px !important; height: 240px !important;  margin-left: -94px;  margin-top: -120px;}
/* 单独页面撤销样式 */
.alone-repeal-tip-content { width: 100%; height: 416px; margin: 0 auto;text-align: center;background:rgba(241,241,241,1);}
.alone-repeal-tip-content .repeal-tip-box{  margin: 0 auto;}
.alone-repeal-tip-content .repeal-tip-back-box{ margin: 0 auto;}
.alone-repeal-tip-content .repeal-tip-title{ color: #4F525A; font-size: 22px; font-family:FZY3JW; padding-top: 70px;}
.alone-repeal-tip-content .repeal-tip-desc{ margin:40px 0px 20px 0px; color: #4F525A; font-size:14px; font-family:Microsoft YaHei; font-weight:400; color:#303030; height: 36px; line-height:22px;}
.alone-repeal-tip-content .repeal-btn-group button{ width:120px; height:40px; border: none; outline: none;}
.alone-repeal-tip-content .repeal-btn-group button.btn-repeal{ background:rgba(0,177,238,1); color: #FFFFFF; margin-right: 20px;}
.alone-repeal-tip-content .repeal-btn-group button.btn-cancel{ border: 1px solid #9A9EA7;}
.alone-repeal-tip-content .repeal-btn-group button.btn-repeal-back{ margin-top: 50px; background:rgba(0,177,238,1); color: #FFFFFF;}
.alone-repeal-tip-content .repeal-tip-img{ text-align: center; padding-top: 96px; margin-bottom: 19px;}
.alone-repeal-tip-content .repeal-tip-back{ font-size: 16px; font-weight:400;}
/* sso内撤销样式 */
.repeal-tip-content { width: 366px; height: 416px; border: 1px solid #e1e1e1; margin: 0 auto; padding-left: 29px; padding-right: 35px;}
.repeal-tip-content .repeal-tip-title{  color: #4F525A; font-size: 22px; font-family:FZY3JW; margin-top: 130px;}
.repeal-tip-content .repeal-tip-desc{ width: 302px; margin:40px 0px 20px 0px;  color: #4F525A; font-size:14px; font-family:Microsoft YaHei; font-weight:400; color:rgba(79,82,90,1); height: 36px; line-height:22px;}
.repeal-tip-content .repeal-btn-group button{  width:120px; height:40px; border: none; outline: none;}
.repeal-tip-content .repeal-btn-group button.btn-repeal{ background:rgba(0,177,238,1); color: #FFFFFF; margin-right: 20px;}
.repeal-tip-content .repeal-btn-group button.btn-repeal-back{ margin-left: 125px; margin-top: 50px;}
.repeal-tip-content .repeal-tip-img{ text-align: center; margin-top: 100px;}
.repeal-tip-content .repeal-tip-back{ margin-top: 17px; text-align: center; font-size:16px; font-family:Microsoft YaHei; font-weight:400; color:#4F525A;}

/* sso登录样式 */
.bind-content, .anthorization-content{ width: 100%; text-align: center;}
/* 授权样式----start */
.anthorization-content .header-title{ padding:10px; font-size: 16px; font-weight: 500;}
.anthorization-content .header-tip{  font-size: 12px; padding-bottom: 20px;  display: flex;  text-align: center; justify-content: center;  flex-wrap: wrap;}
.anthorization-content .userinfo{  padding:10px; position: relative;  width: 102px;  margin:0 auto;}
.anthorization-content .userinfo .avatar{ width: 102px; position:relative;}
.anthorization-content .userinfo .qrcode-box{ display: none;}
.anthorization-content .userinfo .qrcode-box .qrcode-img,
.anthorization-content .userinfo .avatar .avatar-img{  width: 100px;  height: 100px; overflow: hidden;  border:1px solid rgb(88, 89, 90);}
.anthorization-content .userinfo .qrcode-box .qrcode-img img,
.anthorization-content .userinfo .avatar .avatar-img img{  width: 100%; height: 100%; cursor: pointer;}
.anthorization-content .userinfo .avatar .avatar-number-id{  position: absolute; bottom: 0px;  left: 0px;  width: 100%; height: 25px;  line-height: 25px; font-size: 12px; color:#fff; background-color: rgba(80,80,80,0.6);}
.anthorization-content .userinfo .nickname{ font-size: 16px;  padding-top: 10px; color:#888;}
.anthorization-content .other-ways-to-login{ font-size: 12px;  cursor: pointer;  color:#096dd1;}
.anthorization-content .other-ways-to-login::after{ content:"";  width: 12px; height: 12px; background:url(../img/goto.png);}
/* 手机号绑定样式-----start */
.bind-content .form-box .form-item-agreement .anthorization-footer,
.anthorization-content .anthorization-footer{  height: 30px; line-height: 30px; display: flex; align-items: center; justify-content: center; flex-wrap: wrap;}
.bind-content .form-box .form-item-agreement .anthorization-footer>div,
.anthorization-content .anthorization-footer>div{ display: block;}
.bind-content .form-box .form-item-agreement .anthorization-footer .check-box,
.anthorization-content .anthorization-footer .check-box{ margin-top: -1px; margin-right: 5px;}
.bind-content .form-box .form-item-agreement .anthorization-footer .agreement-text,
.anthorization-content .anthorization-footer .agreement-text{ font-size: 12px;}
.bind-content .form-box .form-item-agreement .anthorization-footer .agreement,
.anthorization-content .anthorization-footer .agreement{ font-size: 14px; cursor: pointer; color: #48bff4;}

.bind-content .form-box{ padding:30px 40px; max-width: 335px; margin:0 auto;}
.bind-content .form-box .form-item{   width: 100%;  padding-top:20px ; position: relative;}
.bind-content .form-box .form-item input{ width: calc(100% - 25px);  height: 40px; padding:0 10px;}
.bind-content .form-box .form-item input.have-icon{ width: calc(100% - 50px); padding-left: 35px; padding-right: 10px;}
.bind-content .form-box .form-item .verification-code{height: 42px;}
.bind-content .form-box .form-item .verification-code .verification-input{ width: 55%; float: left;}
.bind-content .form-box .form-item .verification-code .verification-btn{ width: 40%;  float: right;}
.bind-content .bind-button{  cursor: pointer;  height: 42px; line-height: 42px;  width: 100%;  text-align: center;  color:#fff; font-size: 16px;}
.bind-content .verification-code .verification-btn .send-btn{ background-color: #01a1e9;}
.bind-content .login-btn{  background-color: #fe3a21;}
.bind-content .cancel-btn{ color:#333; background-color: #efefef;}
.bind-content .icon-phone{ display: block;  position: absolute; left: 3px; top:31px;  width: 22px; height: 22px; background:url(../images/phone.png) no-repeat center; background-size: contain;}
.anthorization-content .error-tip{ text-align: center;  color:#fe3a21;  margin-bottom: 2px;}
.bind-content .error-tip{ text-align: left; color:#fe3a21;}

.sso-authorize-login-wx{position:relative;margin:0 auto;font-size:12px;text-align:center}
.sso-authorize-login-wx .code-box .code_default{display:flex;justify-content:space-around;height:calc(100% - 10px);width:calc(100% - 10px);margin:auto;padding-top:5px}
.sso-authorize-login-wx .code-box .code_default img{height:100%;width:100%}
.sso-authorize-login-wx .code-box .code_default button{height:100%;width:100%;padding:0 25px;height:40px;color:#fff;background:#4928d8}
.sso-authorize-login-wx .code-box .code_fail{width:100%;background:url(../images/code_fail.png) no-repeat;background-size:contain;position:static;position:unset;height:100%}
.sso-authorize-login-wx .code-box .code_fail .code_fail_box{width:100%;position:absolute;font-size:12px;color:#4f525a}
.sso-authorize-login-wx .code-box .code_fail .code_fail_box .code_fail_text{padding-top:30px}
.sso-authorize-login-wx .code-box .code_fail .code_fail_box .code_refresh_btn button{width:140px;height:40px;font-size:14px;color:#ffffff;font-weight:400;background:rgb(0,177,238);border:none;outline:none;cursor:pointer;margin-top:9px}
.sso-authorize-login-wx .page-content{max-width:310px;min-width:288px;min-height:261px;background:#ffffff;padding-top:31px;padding-bottom:33px;border-radius:16px;margin:0 auto}
.sso-authorize-login-wx .page-content .header .header-title{font-size:14px;font-family:SourceHanSansCN-Bold,SourceHanSansCN;font-weight:bold;color:#333333;line-height:22px}
.sso-authorize-login-wx .page-content .header .desc{padding-top:10px}
.sso-authorize-login-wx .page-content .header .desc span{color:#4928d8}
.sso-authorize-login-wx .page-content .main{padding-top:21px;padding-bottom:21px}
.sso-authorize-login-wx .page-content .main .qrcode-wrapper{display:block;position:relative;height:140px;width:140px;margin:auto;background:url("../images/qrcode_border.png") no-repeat;background-size:contain}
.sso-authorize-login-wx .page-content .main .qrcode-wrapper .qrcode,.sso-authorize-login-wx .page-content .main .qrcode-wrapper .mask{height:calc(100% - 10px);width:calc(100% - 10px);margin:5px;border-radius:10px}
.sso-authorize-login-wx .page-content .main .qrcode-wrapper .qrcode{background:#e1e1e1}
.sso-authorize-login-wx .page-content .main .qrcode-wrapper .mask{position:absolute;top:0;left:0;background:#111111;opacity:0.8}
.sso-authorize-login-wx .page-content .main .qrcodeDesc{padding-top:7px;font-weight:400;color:#333333;line-height:20px}
.sso-authorize-login-wx .page-content .main .quick_login_button{display:none}
.sso-authorize-login-wx .page-content .main .text-area{display:none}
.sso-authorize-login-wx .page-content .bottom button{display:none}
.sso-authorize-login-wx .page-content .bottom .privacy-control{padding-top:9px;padding-left:14px}
.sso-authorize-login-wx .page-content .bottom .other-ways-to-login .loglink,.sso-authorize-login-wx .page-content .bottom .other-ways-to-login .tip{display:block;text-align:end;margin-left:22px}
.sso-authorize-login-wx .page-content .bottom .other-ways-to-login .loglink{margin-top:24px;margin-right:32px;position:relative}
.sso-authorize-login-wx .page-content .bottom .other-ways-to-login .loglink::after{display:inline-block;content:"";background-image:url("../images/linktip-after.png");background-size:cover;position:absolute;top:2px;right:-18px;height:14px;width:14px}
.sso-authorize-login-wx .page-content .bottom .other-ways-to-login .tip{font-weight:400;color:#888888;line-height:20px;padding-top:4px;padding-right:14px}
.sso-authorize-login-wx .page-content .bottom .linkList a{margin:0 -6px;color:#4928d8}
.sso-authorize-login-wx .page-content a{text-decoration:none}
.sso-authorize-login-wx .page-content button{width:228px;height:40px;color:#fff;background:#4928d8;border:unset;border-radius:20px;margin-top:17px}
.sso-authorize-login-wx .page-content.type3-4 .qrcode-wrapper{width:98px;height:98px;background:url("../images/qrcode_border1.png") no-repeat contain}
.sso-authorize-login-wx .page-content.type3-4 .quick_login_button{display:unset}
.sso-authorize-login-wx .page-content.type3-1 .privacy-control{display:none}
.sso-authorize-login-wx .page-content.type3-1 .tip{display:none}
.sso-authorize-login-wx .page-content.privacy-page .header .desc{display:none}
.sso-authorize-login-wx .page-content.privacy-page .main .qrcode-wrapper{display:none}
.sso-authorize-login-wx .page-content.privacy-page .main .text-area{display:block;box-sizing:border-box;margin:auto;width:282px;height:142px;background:rgba(73,40,216,0.06);border-radius:8px;padding:12px;text-align:left}
.sso-authorize-login-wx .page-content.privacy-page .main .text-area a{color:gray}
.sso-authorize-login-wx .page-content.privacy-page .bottom .privacy-control{display:none}
.sso-authorize-login-wx .page-content.privacy-page .bottom button{display:unset}
.sso-authorize-login-wx .page-content.privacy-page .tip{display:none}


