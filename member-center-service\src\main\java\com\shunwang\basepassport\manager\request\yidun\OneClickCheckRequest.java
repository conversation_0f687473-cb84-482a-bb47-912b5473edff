package com.shunwang.basepassport.manager.request.yidun;

import cn.hutool.core.lang.UUID;
import com.shunwang.basepassport.config.pojo.ConfigInterface;
import com.shunwang.basepassport.manager.HttpMethod;
import com.shunwang.basepassport.manager.InterfaceConstant;
import com.shunwang.basepassport.manager.request.BaseRequest;
import com.shunwang.basepassport.manager.response.yidun.OneClickCheckResponse;
import org.apache.commons.codec.digest.DigestUtils;

import java.io.UnsupportedEncodingException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

public class OneClickCheckRequest extends BaseRequest<OneClickCheckResponse> {

    private String appId;
    private String appKey;
    private String businessId;
    private String token;
    private String accessToken;

    @Override
    public Map<String, String> buildParams() {
        String timeStamp = String.valueOf(System.currentTimeMillis());
        Map<String, String> params = new HashMap<>();
        // 1.设置公共参数
        params.put("secretId", getAppId());
        params.put("businessId", getBusinessId());
        params.put("version", "v1");
        params.put("timestamp", timeStamp);
        params.put("nonce", UUID.randomUUID().toString().replace("-", ""));
        // 2.设置私有属性
        params.put("accessToken", accessToken);
        params.put("token", token);
        // 3.生成签名信息
        String signature = genSignature(getAppKey(), params);
        params.put("signature", signature);
        return params;
    }

    /**
     * 生成签名信息
     *
     * @param secretKey 产品私钥
     * @param params    接口请求参数名和参数值map，不包括signature参数名
     * @return
     * @throws Exception
     */
    private String genSignature(String secretKey, Map<String, String> params) {
        // 1. 参数名按照ASCII码表升序排序
        String[] keys = params.keySet().toArray(new String[0]);
        Arrays.sort(keys);

        // 2. 按照排序拼接参数名与参数值
        StringBuffer paramBuffer = new StringBuffer();
        for (String key : keys) {
            paramBuffer.append(key).append(params.get(key) == null ? "" : params.get(key));
        }
        // 3. 将secretKey拼接到最后
        paramBuffer.append(secretKey);

        // 4. MD5是128位长度的摘要算法，用16进制表示，一个十六进制的字符能表示4个位，所以签名后的字符串长度固定为32个十六进制字符。
        try {
            return DigestUtils.md5Hex(paramBuffer.toString().getBytes("UTF-8"));
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
    }


    @Override
    public Class<OneClickCheckResponse> getResponseClass() {
        return OneClickCheckResponse.class;
    }

    @Override
    public Integer getInterfaceKey() {
        return InterfaceConstant.INTERFACE_KEY_YIDUN_H5_CHECK_MOBILE;
    }

    @Override
    public void doInterfaceSetting(ConfigInterface setting) {
        setUrl(setting.getInterfaceUrl1());
        setAppId(setting.getInterfacePartnerId());
        setAppKey(setting.getInterfaceMd5Key());
        setBusinessId(setting.getInterfaceEmail());
    }

    @Override
    public Map<String, String> getHeaders() {
        addHeader("Content-Type","application/x-www-form-urlencoded");
        return headers;
    }

    @Override
    public HttpMethod getHttpMethod() {
        return HttpMethod.POST;
    }


    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public String getBusinessId() {
        return businessId;
    }

    public void setBusinessId(String businessId) {
        this.businessId = businessId;
    }

    public String getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }
}
