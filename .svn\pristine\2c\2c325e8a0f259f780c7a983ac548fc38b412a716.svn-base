package com.shunwang.baseStone.sso.weixin.pojo;

import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.shunwang.util.json.GsonUtil;
import org.apache.commons.lang3.time.DateUtils;

import java.util.Date;

/**
 * User:pf.ma
 * Date:2019/12/31
 * Time:13:27
 */
public class WeixinQrcode {

	private Integer errcode;
	private String errmsg;
	private String qrcodeUrl ;
	private String ticket ;
	private Date expireTime ;
    public WeixinQrcode(){}
	public WeixinQrcode(String result){
        JsonObject jsonObject = JsonParser.parseString(result).getAsJsonObject();
        errcode = GsonUtil.getIntegerFromJsonObject(jsonObject, "errcode");
        errmsg = GsonUtil.getStringFromJsonObject(jsonObject, "errmsg");
        if (null == errcode) {
			qrcodeUrl = jsonObject.get("url").getAsString();
			ticket = jsonObject.get("ticket").getAsString();
			Integer expire_seconds = jsonObject.get("expire_seconds").getAsInt();
			expireTime = DateUtils.addSeconds(new Date(), expire_seconds == null ? 0 : expire_seconds);
		}
	}

	public String getQrcodeUrl() {
		return qrcodeUrl;
	}

	public void setQrcodeUrl(String qrcodeUrl) {
		this.qrcodeUrl = qrcodeUrl;
	}

	public String getTicket() {
		return ticket;
	}

	public void setTicket(String ticket) {
		this.ticket = ticket;
	}

	public Date getExpireTime() {
		return expireTime;
	}

	public void setExpireTime(Date expireTime) {
		this.expireTime = expireTime;
	}

	public Integer getErrcode() {
		return errcode;
	}

	public void setErrcode(Integer errcode) {
		this.errcode = errcode;
	}

	public String getErrmsg() {
		return errmsg;
	}

	public void setErrmsg(String errmsg) {
		this.errmsg = errmsg;
	}
}
