package com.shunwang.basepassport.manager.response.swpay;

import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.shunwang.basepassport.manager.IResponse;
import com.shunwang.util.lang.StringUtil;

public class BaseSwpayJsonResponse implements IResponse {
    private Integer code;
    private String rawJson;
    private String msg;
    public static final int SUCCESS = 0;
    private JsonObject jsonObject;

    public boolean isSuccess() {
        return code != null && SUCCESS == code;
    }

    public BaseSwpayJsonResponse parse() {
        if (StringUtil.isBlank(rawJson)) {
            return null;
        }
        jsonObject = JsonParser.parseString(rawJson).getAsJsonObject();
        JsonObject result = jsonObject.get("result").getAsJsonObject();
        code = result.get("code").getAsInt();
        msg = checkJsonItem(result, "msg") ? result.get("msg").getAsString() : "";
        return this;
    }

    protected boolean checkJsonItem(JsonObject jsonObject,String itemName) {
        return jsonObject.get(itemName) != null;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getRawJson() {
        return rawJson;
    }

    public void setRawJson(String rawJson) {
        this.rawJson = rawJson;
    }

    public JsonObject getJsonObject() {
        return jsonObject;
    }

    public void setJsonObject(JsonObject jsonObject) {
        this.jsonObject = jsonObject;
    }
}
