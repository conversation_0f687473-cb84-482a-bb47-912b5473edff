package com.shunwang.baseStone.core.detail;

import java.io.Serializable;

/**
 * ****************************
 * 版权所有：顺网科技 保留所有权利
 * 创建日期: Jul 13, 2011 4:57:20 PM
 * 创建作者：xiangjie
 * 文件名称：
 * 版本： 1.0
 * 功能：更改项
 * 最后修改时间：
 * 修改记录：
****************************************
 */
public class DetailItem implements Serializable{
	/**
	 * 
	 */
	private static final long serialVersionUID = -4140467825223816434L;
	private String key;
	private Object curValue;
	private Object preValue;

	public DetailItem(String key,Object preValue,Object curValue){
		this.key = key;
		this.curValue = curValue;
		this.preValue = preValue;
	}
	
	public String getKey() {
		return key;
	}

	public void setKey(String key) {
		this.key = key;
	}

	public Object getCurValue() {
		return curValue;
	}

	public void setCurValue(Object curValue) {
		this.curValue = curValue;
	}

	public Object getPreValue() {
		return preValue;
	}

	public void setPreValue(Object preValue) {
		this.preValue = preValue;
	}

}
