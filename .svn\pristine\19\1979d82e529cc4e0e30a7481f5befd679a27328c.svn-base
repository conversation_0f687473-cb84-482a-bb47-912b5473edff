package com.shunwang.passport.weixin.pojo;


/**
 * 表示微信用户发送的消息
 * 可以有各种类型, 如文本,语音,视频等
 *
 * <AUTHOR>
 * @since 2014-04
 *
 */
public class WeixinBasicMsg {

    /** 文本消息 */
    public static final String MSG_TYPE_TEXT = "text"; 
    /** 图片消息 */
    public static final String MSG_TYPE_IMAGE = "image"; 
    /** 语音消息 */
    public static final String MSG_TYPE_VOICE = "voice"; 
    /** 视频消息 */
    public static final String MSG_TYPE_VIDEO = "video"; 
    /** 地理位置消息 */
    public static final String MSG_TYPE_LOCATION = "location"; 
    /** 链接消息 */
    public static final String MSG_TYPE_LINK = "link"; 
    /** 客户端事件,包括菜单点击,关注,取消关注等 */
    public static final String MSG_TYPE_EVENT = "event"; 

    /** 关注 */
    public static final String EVENT_TYPE_SUB = "subscribe"; 
    /** 取消关注 */
    public static final String EVENT_TYPE_UNSUB = "unsubscribe"; 
    /** click型菜单点击 */
    public static final String EVENT_TYPE_CLICK = "CLICK"; 
    /** view型菜单点击 */
    public static final String EVENT_TYPE_VIEW = "VIEW"; 
    /** 上报客户端位置 */
    public static final String EVENT_TYPE_LOCATION = "LOCATION"; 

    
    protected String toUserName;
    protected String fromUserName;
    protected String createTime;
    protected String msgType;

    public WeixinBasicMsg() {
    }

    public WeixinBasicMsg(String toUserName,String fromUserName) {
        this.toUserName = toUserName;
        this.fromUserName = fromUserName;
    }

    public WeixinBasicMsg(String toUserName,String fromUserName, String createTime, String msgType) {
        this(toUserName,fromUserName);
        this.createTime = createTime;
        this.msgType = msgType;
    }

    public void setToUserName(String toUserName) {
        this.toUserName=toUserName;
    }
    public String getToUserName() {
        return this.toUserName;
    }


    public void setFromUserName(String fromUserName) {
        this.fromUserName=fromUserName;
    }
    public String getFromUserName() {
        return this.fromUserName;
    }


    public void setCreateTime(String createTime) {
        this.createTime=createTime;
    }
    public String getCreateTime() {
        return this.createTime;
    }


    public void setMsgType(String msgType) {
        this.msgType=msgType;
    }
    public String getMsgType() {
        return this.msgType;
    }

}
