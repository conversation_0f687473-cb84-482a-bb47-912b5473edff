package com.shunwang.basepassport.user.web;

import com.shunwang.baseStone.config.constants.ConfigOauthConstant;
import com.shunwang.baseStone.context.BaseStoneContext;
import com.shunwang.baseStone.context.IPContext;
import com.shunwang.baseStone.context.LoginElementService;
import com.shunwang.baseStone.context.RedisContext;
import com.shunwang.baseStone.core.action.BaseStoneAction;
import com.shunwang.baseStone.encrypt.Encrypt;
import com.shunwang.baseStone.encrypt.pojo.EncryptItem;
import com.shunwang.baseStone.loginelement.constants.LoginElementConstant;
import com.shunwang.baseStone.siteinterface.context.SiteContext;
import com.shunwang.basepassport.commonExp.ParamFormatErrorExp;
import com.shunwang.basepassport.commonExp.ParamNotFoundExp;
import com.shunwang.basepassport.commonExp.UserFormateErrorExp;
import com.shunwang.basepassport.config.common.ApiAppUtil;
import com.shunwang.basepassport.config.pojo.SiteInterface;
import com.shunwang.basepassport.interc.InterInterfaceService;
import com.shunwang.basepassport.key.common.DynamicKeyUtil;
import com.shunwang.basepassport.key.common.UserRegisterKeyUtil;
import com.shunwang.basepassport.manager.service.bo.ReportProcessor;
import com.shunwang.basepassport.user.common.*;
import com.shunwang.basepassport.user.dao.MemberAccountBindDao;
import com.shunwang.basepassport.user.dao.MemberDao;
import com.shunwang.basepassport.user.dao.MemberTokenDao;
import com.shunwang.basepassport.user.exception.EmailNotAsLoginAccountExp;
import com.shunwang.basepassport.user.exception.MobileNotAsLoginAccountExp;
import com.shunwang.basepassport.user.exception.UsernameOrPwdErrorExp;
import com.shunwang.basepassport.user.pojo.*;
import com.shunwang.basepassport.user.response.MemberLoginResponse;
import com.shunwang.basepassport.user.response.ValidateAgainResponse;
import com.shunwang.util.lang.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * ****************************
 *
 * @版权所有：顺网科技 保留所有权利
 * @创建日期: 2011-7-28  下午03:12:01
 * @创建作者：陈积慧
 * @文件名称：MemberOutLoginAction.java
 * @版本： 1.0
 * @功能： 登录接口
 * @最后修改时间：
 * @修改记录： **************************************
 */
public class MemberLoginAction extends BaseStoneAction implements ReportProcessor {

    private final static Logger log = LoggerFactory.getLogger(MemberLoginAction.class);
    private static final long serialVersionUID = 1L;
    private String encryptType;//加密类型（比如DES、AES）为空默认AES
    private String encryptMode;//加密模式
    private String userName;//账号(可以是:账号/手机/邮箱) 通过url编码
    private String loginEnvironment;//登陆环境(如网吧ID等)
    private String loginIp;//登录IP
    private String siteVersion;//客户端的版本号
    private String password;
    private String dynamic;//1:用动态key加密
    private String key;//用于签名的key
    private String securityType; //1表示需要开启安全加强模式，会增加用户账号是否泄露判断逻辑，参数为空或者为非"1"字符是兼容模式
    private Boolean needDynamicPwd = Boolean.TRUE;
    private Boolean needShunLing = Boolean.TRUE;
    private String remark; // 登录需要采集的信息，JSON格式(如手机SDK登录需要采集手机设备、OS、网络等信息过来)
    /**
     * 上报大数据的数据 json格式
     */
    private String reportData;
    /**
     * 标识用户密码是否是弱密码 1弱密码 2非弱密码
     */
    private String weakPwdState;

    private MemberAccountBindDao memberAccountBindDao;
    private InterInterfaceService interfaceService;
    private LoginElementService loginElementService;

    public String memberOutLogin() {
        Member member = UserRegisterKeyUtil.getMember(userName);
        //Member member = this.getDao().getMember(userName);
        if (null == member) {
            if (UserCheckUtil.checkEmail(userName)) { //如果是邮箱
                throw new EmailNotAsLoginAccountExp();
            }
            if (UserCheckUtil.checkMobile(userName)) { //如果是手机号码
                throw new MobileNotAsLoginAccountExp();
            }
            throw new UsernameOrPwdErrorExp();
        }
        try {
            initLoginElement(getSiteId());
        } catch (Exception e) {
            log.error("初始化登录配置异常", e);
        }
        UserCheckUtil.checkMemberState(member.getMemberState(), member.getMemberName());
        checkSecurity(member);
        IPContext.setIp(loginIp);
        member.initLoginMsg(siteVersion, loginEnvironment, remark);
        member.login(password);
        member.updateWeakPwdState(weakPwdState);
        reportLogin(getSiteId(), member, reportData);

        ValidateAgainResponse response =null ;
        //单账号
        if (ApiAppUtil.isSingleAccount(getSiteId())){
            Integer memberId = member.getMemberId();
            MemberAccountBind memberAccountBind = memberAccountBindDao.getByMemberId(memberId);
            if (memberAccountBind == null) {
                if(member.getMobileAsLoginAccount()){
                    memberAccountBind = MemberUtil.buildSingleAccount(member);
                    interfaceService.singleAccountBindExt(memberAccountBind);
                }else {
                	String singleAccountToken  = MemberUtil.buildSingleSign(memberId, ConfigOauthConstant.TYPE.NORMAL.getInt());
                    response = new ValidateAgainResponse();
                    response.setMsgId("1019");
                    response.setToken(singleAccountToken) ;
                    response.setMsg("请绑定手机号");
                }
            }
        }

        if(null == response){
            response = validateAgainIfNecessary(member);
        }
        if (response != null) {
            setBaseResponse(response);
            return null;
        }
        UserLoginSessionUtil.saveSession(member.getMemberName(), UserLoginSessionUtil.LoginType.INTERFACE_PWD.getType(), null, getSiteId());
        //获取返回签名串
        this.getReturnResponse(member);

        return null;
    }

    /**
     * 根据条件返回是否需要二次验证的响应
     *
     * @param member 登录用户
     * @return 二次验证响应，为null表示不需要验证
     */
    private ValidateAgainResponse validateAgainIfNecessary(Member member) {
        if (needDynamicPwd && member.getIsBindDynamicPwd()) {
            ValidateAgainResponse response = new ValidateAgainResponse();
            response.setMsgId("1019");
            response.setBindState(member.getBindState());
            response.setToken(TokenUtil.createLoginToken(getSiteId(), member.getMemberName()));
            response.setMsg("用户需要二次验证");
            return response;
        }
        return null;
    }

    public void initLoginElement(String siteId) {
        if (null == siteId) {
            return;
        }
        //初始化动态密码 取接口的开关
        setNeedDynamicPwd(loginElementService.getConfigIsOpen(siteId, LoginElementConstant.DYNAMIC_PWD_INTERFACE_SWITCH));
    }

    /**
     * @创建日期: 2011-7-29  下午04:02:44
     * @创建作者：chenjh
     * @功能：获取返回签名串 ************
     */
    private void getReturnResponse(Member member) {
        //构建返回签名串
        MemberInfo memberInfo = member.loadMemberInfo();
        MemberLoginResponse memberResponse = new MemberLoginResponse();
        memberResponse.setKey(key);
        memberResponse.getMemberAndInfo().setUserName(member.getMemberName());
        memberResponse.getMemberAndInfo().setRealName(member.getRealName());
        memberResponse.getMemberAndInfo().setUserType(member.getMemberType());
        memberResponse.getMemberAndInfo().setBindState(member.getBindState());
        memberResponse.getMemberAndInfo().setCompanyName(member.getCompanyName());
        memberResponse.getMemberAndInfo().setEmail(member.getEmail());
        memberResponse.getMemberAndInfo().setIdentity(memberInfo.getIdCardNo());
        memberResponse.getMemberAndInfo().setMobile(member.getMobile());
        memberResponse.getMemberAndInfo().setLinkMan(memberInfo.getLinkMan());
        memberResponse.getMemberAndInfo().setTitleName(member.getTitleName());

        //返回的pojo值
        memberResponse.getMemberAndInfo().setUserId(member.getMemberId());
        memberResponse.getMemberAndInfo().setPersonCertState(member.getPersonCertState());
        memberResponse.getMemberAndInfo().setCompanyCertState(member.getCompanyCertState());
        memberResponse.getMemberAndInfo().setQq(memberInfo.getQq());
        memberResponse.getMemberAndInfo().setFixedMobile(memberInfo.getFixedMobile());
        memberResponse.getMemberAndInfo().setLinkAddress(memberInfo.getLinkAddress());
        memberResponse.getMemberAndInfo().setPostalcode(memberInfo.getPostCode());
        memberResponse.getMemberAndInfo().setMemberState(member.getMemberState());

        String ticket = LoginStatusHandlerUtil.getTicket();
        String loginStatusKey = LoginStatusHandlerUtil.getCacheKey(member.getMemberName(), ticket);
        log.info("用户[" + member.getMemberName() + "]登录Ticket["+ loginStatusKey +" => " + ticket + "]写入缓存");
        if (!RedisContext.getRedisCache().set(loginStatusKey, ticket, 10,TimeUnit.MINUTES)) {
            log.error("用户[" + member.getMemberName() + "]登录Ticket["
                      + loginStatusKey + " => " + ticket + "]写入缓存失败");
        }
        memberResponse.getMemberAndInfo().setTicket(ticket);
        memberResponse.getMemberAndInfo().setNickName(member.getNickName());

        this.setBaseResponse(memberResponse);
    }

    /**
     * @return 功能：创建签名串 ************
     * @创建日期: Jul 27, 2011 15:06:39 PM
     * @创建作者：chenjh
     */
    @Override
    public String buildSignString() {
        password = MemberUtil.decryt(password, encryptType, encryptMode);
        Encrypt encrypt = new Encrypt();
        encrypt.addItem(new EncryptItem(getSiteId()));
        encrypt.addItem(new EncryptItem(userName));
        encrypt.addItem(new EncryptItem(loginEnvironment));
        encrypt.addItem(new EncryptItem(loginIp));
        encrypt.addItem(new EncryptItem(siteVersion));
        encrypt.addItem(new EncryptItem(password));
        encrypt.addItem(new EncryptItem(getTime()));

        return encrypt.buildSign();
    }

    @Override
    public String getSiteName() {
        return MemberConstants.LOGIN;
    }

    /**
     * 根据参数securityType判断是否开启安全加强模式
     */
    public void checkSecurity(Member member) {
        MemberUnsafe temp = new MemberUnsafe();
        temp.setMemberId(member.getMemberId());
        temp = member.getMemberUnsafeDao().getMemberUnsafe(temp);
        if (!StringUtil.isBlank(getSecurityType()) && MemberConstants.MEMBERUNSAFE_SECURITYTYPE_PRO.equals(getSecurityType()) && temp != null) {
            if (temp.getMemberType().equals(MemberConstants.MEMBERUNSAFE_MEMBERTYPE_UNSAFE)) {
                throw new UserFormateErrorExp("", "该用户疑似账号泄露，登录失败");
            }
        }
    }

    /**
     * @创建日期: Jul 27, 2011 15:06:39 PM
     * @创建作者：chenjh
     * @功能：检查参数 ************
     */
    @Override
    public void checkParam() {
        if (StringUtil.isBlank(getTime())) {
            throw new ParamNotFoundExp("time");
        }
        if (StringUtil.isBlank(getSign())) {
            throw new ParamNotFoundExp("sign");
        }
        if (StringUtil.isBlank(userName)) {
            throw new ParamNotFoundExp("userName");
        }
        if (StringUtil.isBlank(password)) {
            throw new ParamNotFoundExp("password");
        }
        if (StringUtil.isBlank(loginIp)) {
            throw new ParamNotFoundExp("loginIp");
        }
        if (StringUtil.isNotBlank(weakPwdState) &&
                !MemberConstants.WEAK_PWD_STATE_1.equals(weakPwdState) &&
                !MemberConstants.WEAK_PWD_STATE_2.equals(weakPwdState)) {
            throw new ParamFormatErrorExp("weakPwdState");
        }
    }

    @Override
    public void process() {
        this.memberOutLogin();
    }

    @Override
    public String createSignString() {
        SiteInterface site = SiteContext.getSiteInterface();
        key = StringUtil.isNotBlank(getDynamic()) && getDynamic().equals("1") ? DynamicKeyUtil
                .getDynamicKey(getSiteId(), userName, getSiteName()) : site.getMd5Key();
        return super.createSignString();
    }

    public MemberDao getDao() {
        return (MemberDao) BaseStoneContext.getInstance().getBean("memberDao");
    }

    public MemberTokenDao getMemberTokenDao() {
        return (MemberTokenDao) BaseStoneContext.getInstance().getBean("memberTokenDao");
    }

    public String getEncryptType() {
        return encryptType;
    }

    public void setEncryptType(String encryptType) {
        this.encryptType = encryptType;
    }

    public String getEncryptMode() {
        return encryptMode;
    }

    public void setEncryptMode(String encryptMode) {
        this.encryptMode = encryptMode;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = super.doUrlEncode(userName);
    }

    public String getLoginEnvironment() {
        return loginEnvironment;
    }

    public void setLoginEnvironment(String loginEnvironment) {
        this.loginEnvironment = loginEnvironment;
    }

    public String getLoginIp() {
        return loginIp;
    }

    public void setLoginIp(String loginIp) {
        this.loginIp = loginIp;
    }

    public String getSiteVersion() {
        return siteVersion;
    }

    public void setSiteVersion(String siteVersion) {
        this.siteVersion = siteVersion;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getDynamic() {
        return dynamic;
    }

    public void setDynamic(String dynamic) {
        this.dynamic = dynamic;
    }

    public Boolean getNeedDynamicPwd() {
        return needDynamicPwd;
    }

    public void setNeedDynamicPwd(Boolean needDynamicPwd) {
        this.needDynamicPwd = needDynamicPwd;
    }

    public Boolean getNeedShunLing() {
        return needShunLing;
    }

    public void setNeedShunLing(Boolean needShunLing) {
        this.needShunLing = needShunLing;
    }

    public String getSecurityType() {
        return securityType;
    }

    public void setSecurityType(String securityType) {
        this.securityType = securityType;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public MemberAccountBindDao getMemberAccountBindDao() {
        return memberAccountBindDao;
    }

    public void setMemberAccountBindDao(MemberAccountBindDao memberAccountBindDao) {
        this.memberAccountBindDao = memberAccountBindDao;
    }

    public InterInterfaceService getInterfaceService() {
        return interfaceService;
    }

    public void setInterfaceService(InterInterfaceService interfaceService) {
        this.interfaceService = interfaceService;
    }

    public LoginElementService getLoginElementService() {
        return loginElementService;
    }

    public void setLoginElementService(LoginElementService loginElementService) {
        this.loginElementService = loginElementService;
    }

    public String getReportData() {
        return reportData;
    }

    public void setReportData(String reportData) {
        this.reportData = reportData;
    }

    public String getWeakPwdState() {
        return weakPwdState;
    }

    public void setWeakPwdState(String weakPwdState) {
        this.weakPwdState = weakPwdState;
    }
}
