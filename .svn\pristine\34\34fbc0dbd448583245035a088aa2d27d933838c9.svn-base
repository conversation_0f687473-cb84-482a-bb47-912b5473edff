package com.shunwang.basepassport.user.web;

import com.shunwang.baseStone.context.BaseStoneContext;
import com.shunwang.baseStone.core.action.BaseStoneAction;
import com.shunwang.baseStone.core.exception.BaseStoneException;
import com.shunwang.baseStone.encrypt.Encrypt;
import com.shunwang.baseStone.encrypt.pojo.EncryptItem;
import com.shunwang.basepassport.binder.common.ErrorCode;
import com.shunwang.basepassport.user.common.UserCheckUtil;
import com.shunwang.basepassport.user.dao.MemberDao;
import com.shunwang.basepassport.user.exception.*;
import com.shunwang.basepassport.user.pojo.Member;
import com.shunwang.util.lang.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public abstract class MemberAction extends BaseStoneAction {

    private static final Logger logger = LoggerFactory.getLogger(MemberAction.class);
    private static final long serialVersionUID = -3006978825104091012L;
    protected Member member;
    private Integer memberId;

    protected static boolean notMatch(String userName, Integer memberId, Member member) {
        if (member == null) {
            logger.warn("用户不存在");
            throw new MsgNotFoundExp("用户");
        }

        if (StringUtil.isNotBlank(userName) && memberId != null && (!member.getMemberName().equals(userName) || !member.getMemberId().equals(memberId))) {
            logger.warn("用户名[{}]和ID[{}]不匹配", userName, memberId);
            throw new NotMatchExp("用户名和ID不匹配");
        }
        return true;
    }

    protected boolean bothBlank(String userName, Integer memberId) {
        if (StringUtils.isBlank(userName) && memberId == null) {
            logger.warn("userName ,memberId不能同时为空");
            throw new BaseStoneException(ErrorCode.C_1001.getCode(), "userName ,memberId不能同时为空");
        }
        return true;
    }

    protected void addEncryptItem(Encrypt encrypt, String userName, Integer memberId) {
        if (StringUtils.isNotBlank(userName))
            encrypt.addItem(new EncryptItem(userName, true));
        if (memberId != null)
            encrypt.addItem(new EncryptItem(memberId.toString(), true));
    }

    public boolean checkMemberIsExist() {
        return null != loadMember();
    }

    public Member loadMember() {
        long beginT = System.currentTimeMillis();
        bothBlank(getMemberName(), getMemberId());

        if (StringUtil.isNotBlank(getMemberName())) {
            if (UserCheckUtil.checkEmail(getMemberName())) {
                member = getDao().getByEmail(getMemberName());
                if (null == member) {
                    Integer bindCnt = getDao().getCntByEmail(getMemberName()); //查询该邮箱有没有绑定过通行证
                    if (null != bindCnt && bindCnt != 0) { //有绑定过,说明没有一个通行证将该邮箱设为登录账号。
                        throw new EmailNotAsLoginAccountExp();
                    }
                }
                if (log.isInfoEnabled()) {
                    log.info("checkEmail execute time:" + (System.currentTimeMillis() - beginT));
                }
            } else if (UserCheckUtil.checkMobile(getMemberName())) {
                member = getDao().getByMobile(getMemberName());
                if (null == member) {
                    Integer bindCnt = getDao().getCntByMobile(getMemberName()); //查询该手机号码有没有绑定过通行证
                    if (null != bindCnt && bindCnt != 0) { //有绑定过,说明没有一个通行证将该手机号码设为登录账号。
                        throw new MobileNotAsLoginAccountExp();
                    }
                }
                if (log.isInfoEnabled()) {
                    log.info("checkMobile execute time:" + (System.currentTimeMillis() - beginT));
                }
            } else {
                member = getDao().getByName(getMemberName());
            }
        } else if (getMemberId() != null) {
            member = getDao().getByMemberId(getMemberId());
        }

        notMatch(getMemberName(), getMemberId(), member);

        if (log.isInfoEnabled()) {
            log.info("getMember execute time:" + (System.currentTimeMillis() - beginT));
        }
        return member;
    }

    public Member getMemberInfo() {

        if (null == member) {

            bothBlank(getMemberName(), getMemberId());

            if (StringUtil.isNotBlank(getMemberName())) {
                member = getDao().getByName(getMemberName());
            } else if (getMemberId() != null) {
                member = getDao().getByMemberId(getMemberId());
            }

            notMatch(getMemberName(), getMemberId(), member);
        }
        return member;
    }

    /**
     * @return
     * @Description:获取用户名
     * <AUTHOR> create at 2011-7-25 下午03:07:37
     */
    public abstract String getMemberName();

    /**
     * @return
     * @Description:实际要执行的处理
     * <AUTHOR> create at 2011-7-25 下午03:08:35
     */
    public abstract void doProcess() throws EncryptExeception;

    @Override
    public void process() throws Exception {
        if (!checkMemberIsExist())
            throw new MsgNotFoundExp("用户");
        doProcess();
    }

    public Member getMember() {
        return member;
    }

    public void setMember(Member member) {
        this.member = member;
    }

    /**
     * @return
     * @Description:获取用户dao
     * <AUTHOR> create at 2011-7-25 下午02:39:34
     */
    public MemberDao getDao() {
        return (MemberDao) BaseStoneContext.getInstance().getBean("memberDao");
    }

    public Integer getMemberId() {
        return memberId;
    }

    public void setMemberId(Integer memberId) {
        this.memberId = memberId;
    }
}
