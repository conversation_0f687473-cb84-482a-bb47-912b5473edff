<%@ page contentType="text/html; charset=UTF-8" %>
<%@ include file="../common/taglibs.jsp" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="Access-Control-Allow-Origin" content="*"/> 
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <title>顺网Qrcode登录页</title>
    <link href="${staticServer}/${cdnVersion}/style/public.css" rel="stylesheet" type="text/css"/>
    <link href="${staticServer}/${cdnVersion}/style/public-gray.css" rel="stylesheet" type="text/css"/>
    <c:choose>
		<c:when test="${not empty loginCss && not empty loginCss.cssUrl }">
		    <link href="${loginCss.cssUrl}" rel="stylesheet" type="text/css"/>
		</c:when>
		<c:otherwise>
		    <link href="${staticServer}/${cdnVersion}/style/default.css" rel="stylesheet" type="text/css"/>
		</c:otherwise>
	</c:choose>

    <script type="text/javascript" src="${staticServer}/${cdnVersion}/js/jquery-1.4.3.js"></script>
    <script type="text/javascript" src="${staticServer}/${cdnVersion}/js/jquery.slider.js"></script>
    <script type="text/javascript" src="${staticServer}/${cdnVersion}/js/qrcode.min.js"></script>
    <s:if test="needReportData">
        <script type="text/javascript" src="${staticServer}/${cdnVersion}/login/js/sw-pv-report-js.min.js"></script>
    </s:if>
    <script type="text/javascript">
        var SSO_SERVER = "${ssoServer}";
        var interfaceServer = "${interfaceServer}";
        var identityServer = "${identityServer}";
    </script>
</head>
<body>
    <!-- APP二维码登录 -->
    <div class="app-weChat-login" style="padding-top: 0">
        <!-- 二维码 -->
        <div class="code-box" style="margin: 0">
            <!-- 二维码登录 -->
            <div class="code_login" style="display:block;">
                <div id="app_code_login"></div>
            </div>
            <!-- 二维码登录加载中 -->
            <div class="code_load" style="display:none;">
                <div class="load-icon"></div>
                <div class="load-text">加载中</div>
            </div>
            <!-- 二维码失效 -->
            <div class="code_fail" style="display:none;">
                <div class="code_fail_box">
                    <div class="code_fail_text">二维码已失效</div>
                    <div class="code_refresh_btn">
                        <button>请点击刷新</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script type="text/javascript">
        if ("https:" == document.location.protocol) {
            interfaceServer = interfaceServer.replace("http:", "https:");
            SSO_SERVER = SSO_SERVER.replace("http:", "https:");
        }
    
        var errorMsg = "${msg}";
        var siteId = "${site_id}";
        var callbackUrl = "${callbackUrl}";
        var tgt = "${tgt}";
        var userKey = '${userKey}';
        var isCheckLoginState = true;
        var expireSeconds = '${expireSeconds}';

        if (!!errorMsg) {
            console.log("错误："+ errorMsg);
            isCheckLoginState = false;
        } else {
            $("#app_code_login").html("");
            new QRCode(document.getElementById("app_code_login"), {
                width:180,
                height:180,
                correctLevel: QRCode.CorrectLevel.H,
                text: SSO_SERVER + "/goAuth.do?uk=" + userKey
            });
            $(".app-weChat-login").show();
            $('.app-weChat-login .code_login').show();
            checkLoginState(expireSeconds);
        }
        function parseJsonParam(param, key) {
            var paramStr = "";
            if (param instanceof String || param instanceof Number || param instanceof Boolean) {
                paramStr += "&" + key + "=" + encodeURIComponent(param);
            } else {
                $.each(param, function(i) {
                    var k = key == null ? i : key + (param instanceof Array ? "[" + i + "]" : "." + i);
                    paramStr += '&' + parseJsonParam(this, k);
                });
            }
            return paramStr.substr(1);
        };

        //扫码状态检测 倒计时
        function checkLoginState(countDownSec){
            if (!isCheckLoginState) {
                return;//异常后无需检测
            }
            if(countDownSec > 0){
                countDownSec = countDownSec-2;
                $.ajax({
                    url:  "/checkWxOpenLogin.do",
                    type: 'post',
                    dataType: 'json',
                    data: {"userKey" : userKey, 'site_id': siteId},
                    success: function(data){
                        console.log(data);
                        if (data == null || data == '') {
                            //未扫码
                            return;
                        }
                        if (data.stage == 'error') {
                            isCheckLoginState = false;
                            return;
                        } else {
                            var urlParam = parseJsonParam(data);
                            window.location.href = "/loginCallback.do?" + urlParam + "&callbackUrl=" + encodeURIComponent(callbackUrl) + "&site_id=" + siteId+ "&tgt=" + tgt;
                        }
                    },
                    error: function(e){
                        console.log(e);
                    }
                });
                setTimeout("checkLoginState("+countDownSec+")",2000);
            }
        }
    </script>
</body>
</html>
