package com.shunwang.basepassport.user.web;

import com.shunwang.baseStone.context.BaseStoneContext;
import com.shunwang.baseStone.core.action.BaseStoneAction;
import com.shunwang.baseStone.core.exception.BaseStoneException;
import com.shunwang.baseStone.encrypt.Encrypt;
import com.shunwang.baseStone.encrypt.pojo.EncryptItem;
import com.shunwang.baseStone.siteinterface.context.SiteContext;
import com.shunwang.basepassport.actu.constant.ActuConstant;
import com.shunwang.basepassport.actu.pojo.PersonalActuVerifyRecord;
import com.shunwang.basepassport.actu.service.IdCardVerifyBo;
import com.shunwang.basepassport.binder.pojo.EmailBinder;
import com.shunwang.basepassport.binder.pojo.MobileBinder;
import com.shunwang.basepassport.commonExp.MsgIsExistExp;
import com.shunwang.basepassport.commonExp.ParamFormatErrorExp;
import com.shunwang.basepassport.commonExp.ParamNotFoundExp;
import com.shunwang.basepassport.config.common.ApiAppUtil;
import com.shunwang.basepassport.config.common.BusinessLineUtil;
import com.shunwang.basepassport.config.pojo.ApiApp;
import com.shunwang.basepassport.config.pojo.BusinessLine;
import com.shunwang.basepassport.config.pojo.SiteInterface;
import com.shunwang.basepassport.detail.common.DetailContants;
import com.shunwang.basepassport.enums.WeakPasswordEnum;
import com.shunwang.basepassport.key.common.DynamicKeyUtil;
import com.shunwang.basepassport.key.common.UserRegisterKeyUtil;
import com.shunwang.basepassport.manager.bean.ReportEntity;
import com.shunwang.basepassport.manager.service.bo.ReportProcessor;
import com.shunwang.basepassport.user.common.MemberConstants;
import com.shunwang.basepassport.user.common.MemberUtil;
import com.shunwang.basepassport.user.common.PwdUtil;
import com.shunwang.basepassport.user.dao.MemberAccountBindDao;
import com.shunwang.basepassport.user.dao.MemberDao;
import com.shunwang.basepassport.user.exception.NotMatchExp;
import com.shunwang.basepassport.user.exception.ThirdPartExp;
import com.shunwang.basepassport.user.pojo.*;
import com.shunwang.basepassport.user.response.RegisterResponse;
import com.shunwang.basepassport.util.WeakPasswordUtil;
import com.shunwang.util.encrypt.AesEncrypt;
import com.shunwang.util.encrypt.Md5Encrypt;
import com.shunwang.util.lang.StringUtil;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.SQLException;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR> create at 2012-7-25 下午04:17:08
 * @Described：注册接口
 * @FileNmae com.shunwang.basepassport.user.web.RegisterAction.java
 */
public class RegisterAction extends BaseStoneAction implements ReportProcessor {

    private final static Logger log = LoggerFactory.getLogger(RegisterAction.class);
    private static final long serialVersionUID = -6539642118287080727L;
    PersonalActuVerifyRecord record;
    /**
     * 用户从哪台机子登录的，GUID+mac地址，使用extData参数:记录在remark字段
     */
    private String remark;
    private MemberAccountBindDao memberAccountBindDao;
    private IdCardVerifyBo idCardVerifyBo;
    private boolean isSingleAccount = false;
    /**
     * 注册来源客户端版本号
     **/
    private String siteVersion;
    /**
     * 用户帐号
     **/
    private String userName;
    /**
     * 密码
     **/
    private String password;
    /**
     * 登录名
     **/
    private String memberName;
    /**
     * 联系电话
     **/
    private String linkMobile;
    /**
     * 注册IP
     **/
    private String regIP;
    /**
     * 用户类型,1代表个人 2代表企业
     **/
    private String userType;
    /**
     * 邮箱
     **/
    private String email;
    /**
     * 手机
     **/
    private String mobile;
    /**
     * 密保问题1
     **/
    private String protectQuestionId1;
    /**
     * 密保问题2
     **/
    private String protectQuestionId2;
    /**
     * 密保问题3
     **/
    private String protectQuestionId3;
    /**
     * 密保问题答案1
     **/
    private String protectAnswer1;
    /**
     * 密保问题答案2
     **/
    private String protectAnswer2;
    /**
     * 密保问题答案3
     **/
    private String protectAnswer3;
    /**
     * 真实姓名
     **/
    private String realName;
    /**
     * 身份证号(校验身份证长度15/18位
     **/
    private String identity;
    /**
     * 公司名
     **/
    private String companyName;
    /**
     * 联系人
     **/
    private String linkMan;
    /**
     * 注册环境(如网吧ID等)
     **/
    private String regEnvironment;
    /**
     * 注册来源
     **/
    private String regFrom;
    /**
     * 客户端IP
     **/
    private String clientIp;
    /**
     * 加密类型（比如DES、AES）为空默认AES
     **/
    private String encryptType;
    /**
     * 加密模式
     **/
    private String encryptMode;
    /**
     * QQ
     **/
    private String qq;
    /**
     * 固定电话
     **/
    private String fixedMobile;
    /**
     * 邮政编码
     **/
    private String postalcode;
    /**
     * 联系地址
     **/
    private String linkAddress;
    /**
     * 是否绑定邮箱
     **/
    private String isBindEmail;
    /**
     * 是否绑定手机
     **/
    private String isBindMoblie;
    private String dynamic;//1:用动态key加密
    private String key;//用于签名的key
    /**
     * 上报大数据的数据json格式
     */
    private String reportData;
    /**
     * 身份证真实姓名认证
     */
    private String isAuthentication;
    /**
     * ⽹吧id（⽹吧场景调⽤，必填），
     * 格式：⽹维id+下划线+计费id，其中⽹维
     * _id和计费id⾄少⼀个有值
     * 调用大数据身份证验证接口用到
     */
    private String barId;

    /**
     * 标识用户密码是否是弱密码 1弱密码 2非弱密码
     */
    private String weakPwdState;

    private boolean isAuth;

    private String decryptedIdCardNo;
    private String decryptedRealName;

    public static void main(String[] args) {
        try {
            String md5 = Md5Encrypt.encrypt("123456");
            System.out.println(md5);
            System.out.println(AesEncrypt.Encrypt(md5, "6rV82fu_e3.tw5A8"));
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    @Override
    public void process() throws Exception {
        try {
            boolean needVerifyIdCard = false;
            if (isAuth) {
                needVerifyIdCard = idCardVerifyBo.needVerifyIdCard(null, decryptedIdCardNo);
                if (needVerifyIdCard) {
                    queryVerifyRecord();
                }
            }

            Member member = registerMember();

            if (isSingleAccount) {
                //增加单帐号绑定关系
                createMemberAccountBind(member);
            }
            member.setVrsion(StringUtil.isNotBlank(siteVersion) ? siteVersion : "");
            member.setEnv(regEnvironment);
            member.setExtData(remark);
//			member.setLoginType(regFrom);
            UserRegisterKeyUtil.cacheMember(member);
            if (needVerifyIdCard) {
                saveIdCardVerify(member);
            }
            BaseStoneContext context = BaseStoneContext.getInstance();
            MemberDao memberDao = (MemberDao) context.getBean("memberDao");
            memberDao.update(member);

            RegisterResponse response = new RegisterResponse(member, key);
            response.setIsWeakPwd(WeakPasswordEnum.IS_WEAK_PASSWORD.getText().equals(member.getWeakPwdState()) ? 1 : 0);
            this.setBaseResponse(response);
        } catch (Exception e) {
            if (e instanceof SQLException) {
                log.error("RegisterAction ERROR:", e);
                throw new MsgIsExistExp(userName);
            } else {
                throw e;
            }
        }
    }

    /**
     * 查询数据库或调用接口
     */
    private void queryVerifyRecord() {
        PersonalActuVerifyRecord recordInDb = idCardVerifyBo.getRecord(decryptedIdCardNo);
        if (recordInDb == null) {
            PersonalActuVerifyRecord recordRemote = idCardVerifyBo.getRecordRemote(decryptedIdCardNo, decryptedRealName);
            if (recordRemote != null) {
                recordRemote.setSiteId(getSiteId());
                updateRecordCategory(recordRemote);
                idCardVerifyBo.saveOrUpdate(recordRemote);
                if (!recordRemote.isMatch()) {
                    throw new NotMatchExp("身份证和真实姓名不匹配");
                }
            } else {
                throw new ThirdPartExp("调创蓝实名接口异常");
            }
            record = recordRemote;
        } else if (!recordInDb.getRealName().equals(decryptedRealName)) {
            throw new NotMatchExp("身份证和真实姓名不匹配");
        } else {
            record = recordInDb;
            record.setChargeStatus(ActuConstant.CHARGE_STATUS_NO_FEE);
            record.setId(null);
            record.setCreateTime(new Date());
            record.setOriginFrom(ActuConstant.ORIGIN_FROM_DB);
        }
    }

    private void saveIdCardVerify(Member member) {
        record.setSiteId(getSiteId());
        updateRecordCategory(record);
        record.setMemberId(member.getMemberId());
        record.setMemberName(member.getMemberName());
        record.setCreateTime(new Date());
        idCardVerifyBo.saveOrUpdate(record);
    }

    private void updateRecordCategory(PersonalActuVerifyRecord record) {
        String siteId = record.getSiteId();
        if (StringUtils.isNotBlank(siteId)) {
            BusinessLine businessLine = BusinessLineUtil.loadBusinessLineByAppId(siteId);
            if (null != businessLine) {
                record.setCategoryId(businessLine.getId());
                record.setCategoryName(businessLine.getName());
            }
        }
    }

    private MemberAccountBind createMemberAccountBind(Member member) {
        MemberAccountBind memberAccountBind = MemberUtil.buildSingleAccount(member);
        memberAccountBind.getPersonalEditLog().setMember(member);
        memberAccountBind.getPersonalEditLog().setUserAdd(member.getMemberName());
        memberAccountBind.getPersonalEditLog().setType(DetailContants.FRONT);
        memberAccountBindDao.save(memberAccountBind);
        log.info("创建单账号绑定记录 memberId:{}", memberAccountBind.getMemberId());
        return memberAccountBind;
    }

    public Member registerMember() {
        Member member = buildMember();

        member = member.regist();
        report(getSiteId(), member.getMemberId(), ReportEntity.InterfaceType.reg, reportData);

        return member;
    }

    @Override
    public void processTransaction() throws Exception {
        process();
    }

    /**
     * ***********
     * 创建日期: ********-7-26下午05:31:31
     * 创建作者：jinbao
     *
     * @return 功能：构造要创建的注册用户对象
     * ************
     */
    public Member buildMember() {
        //前端传递来的密文解密之后的结果
        String strPwd = "";
        Member member = new Member();
        MemberInfo memberInfo = new MemberInfo();
        MemberProtectedQuestion memberProtectedQuestions = new MemberProtectedQuestion();
        member.setMemberName(userName);
        //解密
        strPwd = MemberUtil.decryt(password, encryptType, encryptMode);

        //查询前端传递来的md5加密后的密码是否为弱密码
        if (WeakPasswordUtil.weakPasswordCheck(strPwd)) {
            member.setWeakPwdState(WeakPasswordEnum.IS_WEAK_PASSWORD.getText());
        }

        member.setMemberPwd(PwdUtil.convertMd5(strPwd));
        if (userType.equals(String.valueOf(MemberConstants.USER_TYPE_COMPANY))) {
            member.setCompanyName(companyName);
            memberInfo.setLinkMan(linkMan);
        } else {
            member.setRealName(decryptedRealName);
        }
        member.setMemberType(Integer.valueOf(userType));
        memberInfo.setIdCardNo(decryptedIdCardNo);
        member.setBindState(MemberConstants.MEMBER_STATE_NOTHING);
        member.setMemberState(MemberConstants.USER_NATURAL_STATE);
        //member.setWeakPwdState(weakPwdState);

        memberInfo.setRegFrom(StringUtil.isEmpty(regFrom) ? getSiteId() : regFrom);
        memberInfo.setRegEnvironment(regEnvironment);
        memberInfo.setRegVersion(siteVersion);
        memberInfo.setRemark(remark);
        memberInfo.setRegIp(regIP);
        memberInfo.setQq(qq);
        memberInfo.setLinkAddress(linkAddress);
        memberInfo.setPostCode(postalcode);
        memberInfo.setFixedMobile(fixedMobile);
        member.setCompanyCertState(Integer.parseInt(ActuConstant.INFO_STATE_UN_APPLY));
        member.setPersonCertState(Integer.parseInt(ActuConstant.INFO_STATE_UN_APPLY));

        if (!StringUtil.isBlank(protectQuestionId1) || !StringUtil.isBlank(protectQuestionId2) || !StringUtil.isBlank(protectQuestionId3))
            member.setBindState(member.getBindState() | MemberConstants.MEMBER_STATE_PROBLEM);

        if (StringUtil.isNotBlank(protectQuestionId1)) {
            ProtectedQuestion[] protectedQuestions = new ProtectedQuestion[3];
            ProtectedQuestion protectedQuestion1 = new ProtectedQuestion();
            protectedQuestion1.setQuestionKey(Integer.valueOf(protectQuestionId1));
            protectedQuestion1.setAnswer(MemberUtil.decryt(protectAnswer1, encryptType, encryptMode));
            ProtectedQuestion protectedQuestion2 = new ProtectedQuestion();
            protectedQuestion2.setQuestionKey(Integer.valueOf(protectQuestionId2));
            protectedQuestion2.setAnswer(MemberUtil.decryt(protectAnswer2, encryptType, encryptMode));
            ProtectedQuestion protectedQuestion3 = new ProtectedQuestion();
            protectedQuestion3.setQuestionKey(Integer.valueOf(protectQuestionId3));
            protectedQuestion3.setAnswer(MemberUtil.decryt(protectAnswer3, encryptType, encryptMode));
            protectedQuestions[0] = protectedQuestion1;
            protectedQuestions[1] = protectedQuestion2;
            protectedQuestions[2] = protectedQuestion3;

            memberProtectedQuestions.setProtectedQuestions(protectedQuestions);
            member.setMemberProtectedQuestions(memberProtectedQuestions);
        }

        if ("true".equals(isBindEmail) && !StringUtil.isBlank(email)) {
            member.setEmail(email);
            member.setIsBindEmail(true);
            member.setEmailBinder(buildEmailBinder(member));
            member.setBindState(member.getBindState() | MemberConstants.MEMBER_STATE_MAIL);
        }
        if ("true".equals(isBindMoblie) && !StringUtil.isBlank(mobile)) {
            member.setMobile(mobile);
            member.setIsBindMobile(true);
            member.setMobileBinder(buildMobileBinder(member));
            member.setBindState(member.getBindState() | MemberConstants.MEMBER_STATE_PHONE);
        }
        if (isSingleAccount) {
            member.setMoblieAsLoginAccount(true);
        }

        //设置是从接口注册的
        member.setRegFromInterface(true);
        member.setSiteType(MemberConstants.SITE_TYPE_IN);
        member.setMemberInfo(memberInfo);
        return member;
    }

    @Override
    public String buildSignString() {
        Encrypt encrypt = new Encrypt();
        encrypt.addItem(new EncryptItem(super.getSiteId()));
        encrypt.addItem(new EncryptItem(siteVersion));
        encrypt.addItem(new EncryptItem(regEnvironment));
        encrypt.addItem(new EncryptItem(regIP));
        encrypt.addItem(new EncryptItem(userName));
        encrypt.addItem(new EncryptItem(userType));
        encrypt.addItem(new EncryptItem(MemberUtil.decryt(password, encryptType, encryptMode)));
        encrypt.addItem(new EncryptItem(email));
        encrypt.addItem(new EncryptItem(mobile));
        encrypt.addItem(new EncryptItem(protectQuestionId1));
        encrypt.addItem(new EncryptItem(protectQuestionId2));
        encrypt.addItem(new EncryptItem(protectQuestionId3));
        encrypt.addItem(new EncryptItem(StringUtil.isBlank(protectAnswer1) ? protectAnswer1 : MemberUtil.decryt(protectAnswer1, encryptType, encryptMode)));
        encrypt.addItem(new EncryptItem(StringUtil.isBlank(protectAnswer2) ? protectAnswer2 : MemberUtil.decryt(protectAnswer2, encryptType, encryptMode)));
        encrypt.addItem(new EncryptItem(StringUtil.isBlank(protectAnswer3) ? protectAnswer3 : MemberUtil.decryt(protectAnswer3, encryptType, encryptMode)));
        encrypt.addItem(new EncryptItem(StringUtil.isBlank(realName) ? realName : decryptedRealName));
        encrypt.addItem(new EncryptItem(StringUtil.isBlank(identity) ? identity : decryptedIdCardNo));
        encrypt.addItem(new EncryptItem(companyName));
        encrypt.addItem(new EncryptItem(linkMan));
        encrypt.addItem(new EncryptItem(super.getTime()));
        return encrypt.buildSign();
    }

    @Override
    public String createSignString() {
        SiteInterface site = SiteContext.getSiteInterface();
        key = StringUtil.isNotBlank(getDynamic()) && getDynamic().equals("1") ? DynamicKeyUtil.getDynamicKey(getSiteId(), userName, getSiteName()) : site.getMd5Key();
        return super.createSignString();
    }

    @Override
    public String getSiteName() {
        return MemberConstants.REGISTER;
    }

    /**
     * ***********
     * 创建日期: ********-7-26上午11:27:10
     * 创建作者：jinbao
     * 功能：校验接口参数,关键参数为空时,抛异常
     * ************
     */
    public void checkParam() {
        if (StringUtil.isBlank(getSiteId()))
            throw new ParamNotFoundExp("siteId");
        if (StringUtil.isBlank(getTime()))
            throw new ParamNotFoundExp("time");
        if (StringUtil.isBlank(getSign()))
            throw new ParamNotFoundExp("sign");
        if (StringUtil.isBlank(userName))
            throw new ParamNotFoundExp("用户名");
        if (StringUtil.isBlank(password))
            throw new ParamNotFoundExp("密码");
        if (StringUtil.isBlank(regIP))
            throw new ParamNotFoundExp("注册IP");
        // 查询账号体系
        ApiApp apiApp = ApiAppUtil.loadApiApp(getSiteId());
        isSingleAccount = apiApp.isSingleAccount();
        if (isSingleAccount) {
            if (!"true".equals(isBindMoblie)) {
                throw new BaseStoneException("1056", "单帐号平台注册isBindMoblie参数必须传true");
            }
            if (StringUtil.isBlank(mobile)) {
                throw new ParamNotFoundExp("手机号");
            }
            Member member = new Member().getDao().getByMobile(mobile);
            if (member != null) {
                throw new MsgIsExistExp("手机号设为登录帐号");
            }
            MemberAccountBind memberAccountBind = memberAccountBindDao.getByMobile(mobile);
            if (memberAccountBind != null) {
                throw new MsgIsExistExp("该手机号的单帐号");
            }
        }
        if (isAuth) {
            if (StringUtil.isEmpty(identity)) {
                throw new ParamNotFoundExp("身份证");
            }
            if (StringUtil.isEmpty(realName)) {
                throw new ParamNotFoundExp("真实姓名");
            }
        }
        if (!StringUtil.isEmpty(identity)) {
            decryptedIdCardNo = MemberUtil.decryt(identity, encryptType, encryptMode);
        }
        if (!StringUtil.isEmpty(realName)) {
            decryptedRealName = MemberUtil.decryt(realName, encryptType, encryptMode);
        }
        if (Objects.nonNull(weakPwdState) && !MemberConstants.WEAK_PWD_STATE_1.equals(weakPwdState) &&
                !MemberConstants.WEAK_PWD_STATE_2.equals(weakPwdState)) {
            throw new ParamFormatErrorExp("弱密码状态");
        }
    }

    /**
     * 构建emailbinder
     *
     * @return
     * @throws
     * <AUTHOR> 创建于 2011-8-23 下午05:14:19
     */
    private EmailBinder buildEmailBinder(Member member) {
        EmailBinder emailBinder = new EmailBinder();
        emailBinder.setNumber(email);
        emailBinder.setFirstActiveTime(new Date());
        return emailBinder;
    }

    /**
     * 构建emailbinder
     *
     * @return
     * @throws
     * <AUTHOR> 创建于 2011-8-23 下午05:14:19
     */
    private MobileBinder buildMobileBinder(Member member) {
        MobileBinder mobileBinder = new MobileBinder();
        mobileBinder.setNumber(mobile);
        mobileBinder.setFirstActiveTime(new Date());
        return mobileBinder;
    }

    public String getMemberName() {
        return memberName;
    }

    public void setMemberName(String memberName) {
        this.memberName = memberName;
        //this.memberName = doUrlEncode(memberName);
    }

    public String getLinkMobile() {
        return linkMobile;
    }

    public void setLinkMobile(String linkMobile) {
        this.linkMobile = linkMobile;
    }

    public String getSiteVersion() {
        return siteVersion;
    }

    public void setSiteVersion(String siteVersion) {
        this.siteVersion = siteVersion;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
        //this.userName = doUrlEncode(userName);
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getRegIP() {
        return regIP;
    }

    public void setRegIP(String regIP) {
        this.regIP = regIP;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getProtectQuestionId1() {
        return protectQuestionId1;
    }

    public void setProtectQuestionId1(String protectQuestionId1) {
        this.protectQuestionId1 = protectQuestionId1;
    }

    public String getProtectQuestionId2() {
        return protectQuestionId2;
    }

    public void setProtectQuestionId2(String protectQuestionId2) {
        this.protectQuestionId2 = protectQuestionId2;
    }

    public String getProtectQuestionId3() {
        return protectQuestionId3;
    }

    public void setProtectQuestionId3(String protectQuestionId3) {
        this.protectQuestionId3 = protectQuestionId3;
    }

    public String getProtectAnswer1() {
        return protectAnswer1;
    }

    public void setProtectAnswer1(String protectAnswer1) {
        this.protectAnswer1 = protectAnswer1;
    }

    public String getProtectAnswer2() {
        return protectAnswer2;
    }

    public void setProtectAnswer2(String protectAnswer2) {
        this.protectAnswer2 = protectAnswer2;
    }

    public String getProtectAnswer3() {
        return protectAnswer3;
    }

    public void setProtectAnswer3(String protectAnswer3) {
        this.protectAnswer3 = protectAnswer3;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getIdentity() {
        return identity;
    }

    public void setIdentity(String identity) {
        this.identity = identity;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = doUrlEncode(companyName);
    }

    public String getLinkMan() {
        return linkMan;
    }

    public void setLinkMan(String linkMan) {
        this.linkMan = doUrlEncode(linkMan);
    }

    public String getRegEnvironment() {
        return regEnvironment;
    }

    public void setRegEnvironment(String regEnvironment) {
        this.regEnvironment = regEnvironment;
    }

    public String getRegFrom() {
        return regFrom;
    }

    public void setRegFrom(String regFrom) {
        this.regFrom = regFrom;
    }

    public String getClientIp() {
        return clientIp;
    }

    public void setClientIp(String clientIp) {
        this.clientIp = clientIp;
    }

    public String getEncryptType() {
        return encryptType;
    }

    public void setEncryptType(String encryptType) {
        this.encryptType = encryptType;
    }

    public String getEncryptMode() {
        return encryptMode;
    }

    public void setEncryptMode(String encryptMode) {
        this.encryptMode = encryptMode;
    }

    public String getQq() {
        return qq;
    }

    public void setQq(String qq) {
        this.qq = qq;
    }

    public String getFixedMobile() {
        return fixedMobile;
    }

    public void setFixedMobile(String fixedMobile) {
        this.fixedMobile = fixedMobile;
    }

    public String getPostalcode() {
        return postalcode;
    }

    public void setPostalcode(String postalcode) {
        this.postalcode = postalcode;
    }

    public String getLinkAddress() {
        return linkAddress;
    }

    public void setLinkAddress(String linkAddress) {
        this.linkAddress = doUrlEncode(linkAddress);
    }

    public String getIsBindEmail() {
        return isBindEmail;
    }

    public void setIsBindEmail(String isBindEmail) {
        this.isBindEmail = isBindEmail;
    }

    public String getIsBindMoblie() {
        return isBindMoblie;
    }

    public void setIsBindMoblie(String isBindMoblie) {
        this.isBindMoblie = isBindMoblie;
    }

    public String getDynamic() {
        return dynamic;
    }

    public void setDynamic(String dynamic) {
        this.dynamic = dynamic;
    }

    public String getRemark() {
        return this.remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public MemberAccountBindDao getMemberAccountBindDao() {
        return memberAccountBindDao;
    }

    public void setMemberAccountBindDao(MemberAccountBindDao memberAccountBindDao) {
        this.memberAccountBindDao = memberAccountBindDao;
    }

    public String getIsAuthentication() {
        return isAuthentication;
    }

    public void setIsAuthentication(String isAuthentication) {
        try {
            this.isAuthentication = isAuthentication;
            isAuth = Boolean.parseBoolean(isAuthentication);
        } catch (Exception e) {
            log.error("解析isAuthentication异常", e);
        }
    }

    public String getBarId() {
        return barId;
    }

    public void setBarId(String barId) {
        this.barId = barId;
    }

    public IdCardVerifyBo getIdCardVerifyBo() {
        return idCardVerifyBo;
    }

    public void setIdCardVerifyBo(IdCardVerifyBo idCardVerifyBo) {
        this.idCardVerifyBo = idCardVerifyBo;
    }

    public String getReportData() {
        return reportData;
    }

    public void setReportData(String reportData) {
        this.reportData = reportData;
    }

    public String getWeakPwdState() {
        return weakPwdState;
    }

    public void setWeakPwdState(String weakPwdState) {
        this.weakPwdState = weakPwdState;
    }

}
