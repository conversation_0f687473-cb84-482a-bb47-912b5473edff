package com.shunwang.basepassport.mobile;

import com.shunwang.basepassport.BaseTest;
import com.shunwang.util.date.DateUtil;


public class TestEmailSend extends BaseTest {
    @Override
    public void init() {
        String type = "2";
        params.put("type", type);
        params.put("siteId", "identity");
        if ("1".equalsIgnoreCase(type)) {
            params.put("memberId", "91485505");
        }
        params.put("email", "<EMAIL>");
        params.put("time", DateUtil.getCurrentDateStamp());
        params.put("signVersion", "1.0");
        params.put("sign", getSign(params));
    }

    @Override
    protected String getUrl() {
        return "http://interface.kedou.com/front/interface/emailSend.htm";
    }

    @Override
    protected String getMd5Key() {
        return "123456";
    }
}
