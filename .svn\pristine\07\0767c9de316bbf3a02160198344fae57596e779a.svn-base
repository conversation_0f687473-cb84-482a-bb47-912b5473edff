package com.shunwang.baseStone.notice.pojo;

import java.io.Serializable;
import java.util.Date;

import com.shunwang.baseStone.context.BackUserContext;
import com.shunwang.baseStone.core.detail.Detail;
import com.shunwang.baseStone.core.detail.DetailItem;
import com.shunwang.baseStone.core.detail.HasDetail;
import com.shunwang.baseStone.core.pojo.BaseStoneObject;
import com.shunwang.baseStone.editlog.pojo.EditLog;
import com.shunwang.baseStone.notice.common.NoticeConstants;
import com.shunwang.baseStone.pagehome.common.PageHomeConstants;

public class Notice extends BaseStoneObject implements HasDetail{
	public EditLog editLog = new EditLog();
	private static final long serialVersionUID = -3211176099190625605L;
	private Integer noticeId;
	private String siteType;
	private String pageType;
	private String moduleName;
	private String moduleContentCur;
	private String moduleContentNew;
	private String moduleState;
	private Boolean isOpen;
	private String userAdd;
	private Date timeAdd = new Date();
	private String userEdit;
	private Date timeEdit = new Date();
	private String userCheck;
	private Date timeCheck = new Date();
	private String rejectRemark;
	private String remark;
	
	public Notice(){
		
	}
	
	@SuppressWarnings("unused")
	private String remarkShow;
	public String getRemarkShow() {
		if(null!=remark&&remark.length()>NoticeConstants.NUM_EIGHT){
			return remark.substring(0, 8)+"...";
		}
		return remark;
	}

	public void setRemarkShow(String remarkShow) {
		this.remarkShow = remarkShow;
	}
	@SuppressWarnings("unused")
	private String siteTypeShow;
	private String filePath;
	
	public String getFilePath() {
		return filePath;
	}

	public void setFilePath(String filePath) {
		this.filePath = filePath;
	}

	public String getSiteTypeShow() {
		if(siteType.equals(PageHomeConstants.PASSPORT))
			return siteTypeShow="通行证";
		if(siteType.equals(PageHomeConstants.WWW))
			return siteTypeShow="顺网游戏";
		if(siteType.equals(PageHomeConstants.PAY))
			return siteTypeShow="结算平台";	
		return null;
	}

	public void setSiteTypeShow(String siteTypeShow) {
		this.siteTypeShow = siteTypeShow;
	}
	
	public String getBtnName() {
		return this.isOpen == false?"显示":"关闭";
	}
	
	public void setNoticeId(Integer value){
		this.noticeId=value;
	}
	public Integer getNoticeId(){
		return this.noticeId;
	}
	public void setSiteType(String value){
		this.siteType=value;
	}
	public String getSiteType(){
		return this.siteType;
	}
	public void setPageType(String value){
		this.pageType=value;
	}
	public String getPageType(){
		return this.pageType;
	}
	public void setModuleName(String value){
		this.moduleName=value;
	}
	public String getModuleStateName() {
		String showName = "";
		if(this.moduleState.equals("0")) {
			showName = "删除";
		} else if(this.moduleState.equals("1")) {
			showName = "待审核";
		} else if(this.moduleState.equals("2")) {
			showName = "批准";
		} else {
			showName = "拒绝";
		}
		return showName;
	}
	public void setModuleStateName(String value){
		this.moduleState=value;
	}
	public String getModuleName(){
		return this.moduleName;
	}
	public void setModuleContentCur(String value){
		editLog.addItem(new DetailItem("已审核的内容",moduleContentCur, value));
		this.moduleContentCur=value;
	}
	public String getModuleContentCur(){
		return this.moduleContentCur;
	}
	public void setModuleContentNew(String value){						
		editLog.addItem(new DetailItem("未审核的内容	",moduleContentNew, value));
		this.moduleContentNew=value;
	}
	public String getModuleContentNew(){
		return this.moduleContentNew;
	}
	public void setModuleState(String value){
		this.moduleState=value;
	}
	public String getModuleState(){
		return this.moduleState ;
	}
	public void setIsOpen(Boolean value){
		this.isOpen=value;
	}
	public Boolean getIsOpen(){
		return this.isOpen;
	}
	public String getIsOpenName() {
		return this.isOpen == false?"关闭":"显示";
	}
	public void setIsOpenName(Boolean value){
		this.isOpen=value;
	}
	public void setUserAdd(String value){
		this.userAdd=value;
	}
	public String getUserAdd(){
		return this.userAdd;
	}
	public void setTimeAdd(java.util.Date value){
		this.timeAdd=value;
	}
	public java.util.Date getTimeAdd(){
		return this.timeAdd;
	}
	public void setUserEdit(String value){
		this.userEdit=value;
	}
	public String getUserEdit(){
		return this.userEdit;
	}
	public void setTimeEdit(java.util.Date value){
		this.timeEdit=value;
	}
	public java.util.Date getTimeEdit(){
		return this.timeEdit;
	}
	public void setUserCheck(String value){
		this.userCheck=value;
	}
	public String getUserCheck(){
		return this.userCheck;
	}
	public void setTimeCheck(java.util.Date value){
		this.timeCheck=value;
	}
	public java.util.Date getTimeCheck(){
		return this.timeCheck;
	}
	public void setRejectRemark(String value){
		this.rejectRemark=value;
	}
	public String getRejectRemark(){
		return this.rejectRemark;
	}
	public void setRemark(String value){
		editLog.addItem(new DetailItem("备注",remark, value));
		this.remark=value;
	}
	public String getRemark(){
		return this.remark;
	}

	@Override
	public Detail getDetail() {
		// TODO Auto-generated method stub
		return editLog;
	}

	@Override
	public void beginBuildLog() {
		// TODO Auto-generated method stub
		String sysclass = this.getClass().toString();
		String conkey = sysclass.substring(sysclass.lastIndexOf(".") + 1,
				sysclass.length());
		editLog.beginBuildLog(true);
		editLog.setEditItem("公告管理");
		editLog.setConfKey(conkey);
		editLog.setUserAdd(BackUserContext.getUserName());
	}

	@Override
	public Serializable getPk() {
		// TODO Auto-generated method stub
		return pageType;
	}
}
