package com.shunwang.basepassport.user.web;

import com.shunwang.baseStone.cache.CacheKeyConstant;
import com.shunwang.baseStone.context.BaseStoneContext;
import com.shunwang.baseStone.context.RedisContext;
import com.shunwang.baseStone.core.response.BaseStoneResponse;
import com.shunwang.basepassport.commonExp.ParamNotFoundExp;
import com.shunwang.basepassport.interc.InterInterfaceService;
import com.shunwang.basepassport.key.common.SmsKeyUtil;
import com.shunwang.basepassport.mobile.constants.MobileCheckCodeConstants;
import com.shunwang.basepassport.mobile.pojo.MobileCheckCode;
import com.shunwang.basepassport.user.common.MemberConstants;
import com.shunwang.basepassport.user.exception.UserLoginException;
import com.shunwang.basepassport.user.pojo.Member;
import com.shunwang.util.date.DateUtil;
import com.shunwang.util.lang.StringUtil;
import com.shunwang.util.math.RandomUtil;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 动态密码短信发送Action 内部接口
 *
 * <AUTHOR>
 * @since 2018-11-07
 */
public class SendDynamicPasswordInnerAction extends DynamicSecurityAction {

    @Override
    public void checkParam() {
        if (StringUtil.isBlank(getSiteId())) {
            throw new ParamNotFoundExp("siteId");
        }
        if (StringUtil.isBlank(getUserName())) {
            throw new ParamNotFoundExp("userName");
        }
        if (StringUtil.isBlank(getTime())) {
            throw new ParamNotFoundExp("time");
        }
        if (StringUtil.isBlank(getSign())) {
            throw new ParamNotFoundExp("sign");
        }
        member = super.loadMember();
    }

    @Override
    public String getMemberName() {
        return userName;
    }

    @Override
    public void doProcess() {

    }

    @Override
    public void process() throws Exception {
      if (!member.getIsBindMobile()) {
            throw new UserLoginException(UserLoginException.NO_DYNAMIC_PWD, "用户未绑定手机");
        }

        if (!member.getIsBindDynamicPwd()) {
            throw new UserLoginException(UserLoginException.NO_DYNAMIC_PWD, "用户未开通动态密码");
        }

        String key = SmsKeyUtil.buildDynamicKey(member.getMemberId(), MobileCheckCodeConstants.DYNAMIC_PWD_LOGIN);
        MobileCheckCode mobileCheckCode = SmsKeyUtil.getDynamicPwd(key, MobileCheckCode.class);

        if (mobileCheckCode != null) {
            if (DateUtil.compare(new Date(), mobileCheckCode.getSendTime(), DateUtil.ONE_MINUTE) < MobileCheckCodeConstants.SEND_AGAIN_INTERVAL_DEFAULT) {
                throw new UserLoginException(UserLoginException.DYNAMIC_PWD_ONLY_ONE, "动态密码一分钟只能发送一次");
            }
        }

        String validTime = getValidTime();
        sendPassword(member);

        Map<String, Object> map = new HashMap<>();
        //单位分钟
        map.put("validTime", validTime);
        map.put("send", true);
        map.put("sendTime", System.currentTimeMillis());
        List list = new ArrayList();
        list.add(map);

        BaseStoneResponse response = new BaseStoneResponse();
        response.setMsgId("0");
        response.setMsg("操作成功");
        response.setItems(list);
        setBaseResponse(response);
    }

    @Override
    public String getSiteName() {
        return MemberConstants.DYNAMIC_PASSWORD_SMS_INNER;
    }

    public String getSmsContent(String pwd) {
        String content = RedisContext.getStrConfig(CacheKeyConstant.ConfigResourcesConstants.TYPE_DYNAMIC_PWD_CONFIG,
                CacheKeyConstant.ConfigResourcesConstants.DYNAMIC_PWD_SMS_CONTENT, "");
        if (!StringUtil.isEmpty(content)) {
            content = content.replace("$password$", pwd);
        }
        return content;
    }

    private void sendPassword(Member member) {
        String pwd = RandomUtil.getRandomStr(6, true);
        String content = getSmsContent(pwd);
        String validTime = getValidTime();
        content = content.replace("$time$", validTime);
        MobileCheckCode paramMobileCheckCode = null;
        try {
            //1、存缓存
            MobileCheckCode mobileCheckCode = new MobileCheckCode();
            paramMobileCheckCode = mobileCheckCode.buildMobileCheckCode(member, pwd,
                    MobileCheckCodeConstants.DYNAMIC_PWD_LOGIN, member.getMobile());

            String key = SmsKeyUtil.buildDynamicKey(member.getMemberId(), MobileCheckCodeConstants.DYNAMIC_PWD_LOGIN);
            SmsKeyUtil.putDynamicPwd(key, paramMobileCheckCode, Long.valueOf(validTime), TimeUnit.MINUTES);

            //2、发短信
            mobileCheckCode.send(member.getMobile(), content, "手机动态密码登录");
            //3、入库
            //mobileCheckCode.savemobileCheckCode(paramMobileCheckCode);
        } catch (Exception e) {
            LOG.error("短信发送异常：", e);
            throw new UserLoginException(UserLoginException.DYNAMIC_PWD_SMS_ERROR, "发送失败");
        }
        try {
            if (paramMobileCheckCode != null) {
                //3、入库
                paramMobileCheckCode.setFromSiteId(getSiteId());
                getInterfaceService().updateMobileCheckCode(paramMobileCheckCode);
            }
        } catch (Exception e) {
            LOG.error("调用接口保存mobileCheckCode失败", e);
        }
    }

    private InterInterfaceService getInterfaceService() {
        return ((InterInterfaceService) BaseStoneContext.getInstance().getBean("interfaceService"));
    }
}
