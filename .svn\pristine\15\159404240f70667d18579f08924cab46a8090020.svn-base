<%@ page contentType="text/html;charset=UTF-8"%>
<%@ include file="/common/taglibs.jsp"%>
<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8"/>
<meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0" />
<meta name="HandheldFriendly"content="true"/>
<meta http-equiv="x-rim-auto-match"content="none"/>
<meta name="format-detection"content="telephone=no">
<title>绑定通行证</title>
<link href="style/public.css" rel="stylesheet" type="text/css" />
<script src="js/zepto.min.js" type="text/javascript" charset="utf-8"></script>
<script src="js/bind.js" type="text/javascript" charset="utf-8"></script>
</head>

<body>
<div class="content">
  <div class="loginBox">
  <p class="zt1 textR">还没有顺网通行证？<a href="showReg.htm">立即注册</a></p>
    <form class="loginCon" action="doBind.htm" id="bindForm" method="post">
      <div class="text-1">
        <input name="passportUserName" type="text" placeholder="通行证账号" value="${passportUserName}">
      </div>
      <div class="text-2">
        <input name="passportUserPwd" type="password" placeholder="密码">
      </div>
      <c:if test="${showCheckCode}">
        <div class="identifying text-3">
          <input name="checkCode" type="text" placeholder="验证码" maxlength="4">
            <img src="/checkCode.htm"  align="absmiddle" id="captcha"  />
        </div>
      </c:if>
    </form>
    <c:if test="${!empty errorMsg}">
      <div id="errorDiv" class="error">${errorMsg}</div>
    </c:if>
    <a class="btn-submit" href="##">绑 定</a>
  </div>
</div>
</body>
</html>
