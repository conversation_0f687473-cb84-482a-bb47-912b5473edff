package com.shunwang.baseStone.cache.alert;

import java.util.Date;

public interface Window {

    String LAST_TIME_TRIGGER_KEY = "_last_triggered_time";

    /**
     * 加一
     *
     * @return
     */
    Window addAndGet();

    /**
     * 获取总数
     *
     * @return
     */
    long totalCount();

    /**
     * 达到limit之后回调
     *
     * @param limit
     * @param callback
     */
    void triggerOn(int limit, TriggerCallback callback);

    /**
     * 触发报警操作
     */
    default void doTrigger() {}


    /**
     * 获取当前对应的key
     *
     * @return
     */
    String getKey();

    default String getLastTriggerTimeKey() {
        return getKey() + LAST_TIME_TRIGGER_KEY;
    }

    void updateTriggerTime();

    Date getLastTriggerTime();
}