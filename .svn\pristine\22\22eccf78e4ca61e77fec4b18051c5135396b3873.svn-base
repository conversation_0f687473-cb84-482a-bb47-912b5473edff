<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd" >
<sqlMap namespace="com.shunwang.basepassport.config.pojo.BarBlackCard" >

    <resultMap id="BaseResultMap" class="com.shunwang.basepassport.config.pojo.BarBlackCard">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="idCardNo" column="id_card_no" jdbcType="VARCHAR"/>
        <result property="timeAdd" column="time_add" jdbcType="TIMESTAMP"/>
        <result property="timeEdit" column="time_edit" jdbcType="TIMESTAMP"/>
        <result property="state" column="state" jdbcType="INTEGER"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="base_column_list">
        id,
        id_card_no,
        time_add,
        time_edit,
        state,
        remark
    </sql>

    <sql id="base_where">
        <dynamic prepend="where ">
            <isNotEmpty prepend="and" property="idCardNo">
                id_card_no = #idCardNo#
            </isNotEmpty>
            <isNotEmpty prepend="and" property="state">
                state = #state#
            </isNotEmpty>
        </dynamic>
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterClass="java.lang.Integer">
        select
        <include refid="base_column_list"/>
        from config_bar_black_card
        where id = #id:INTEGER#
    </select>

    <select id="findByIdCardNo" resultMap="BaseResultMap" parameterClass="java.lang.String">
        select
        <include refid="base_column_list"/>
        from config_bar_black_card
        where id_card_no = #idCardNo#
    </select>

    <insert id="insert" parameterClass="com.shunwang.basepassport.config.pojo.BarBlackCard" >
        insert into config_bar_black_card (
            id_card_no,
            state,
            time_add,
            time_edit,
            remark
        ) VALUES (
             #idCardNo:VARCHAR#,
             #state:INTEGER#,
             #timeAdd:DATETIME#,
             #timeEdit:DATETIME#,
             #remark:VARCHAR#
        )
    </insert>

</sqlMap>