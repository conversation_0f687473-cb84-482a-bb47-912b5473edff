package com.shunwang.baseStone.sender;

import com.shunwang.baseStone.sender.exp.SendExp;
import com.shunwang.baseStone.sender.pojo.SendMsg;

/**
 * ****************************
 * 版权所有：顺网科技 保留所有权利
 * 创建日期: Jul 21, 2011 7:20:10 PM
 * 创建作者：xiangjie
 * 文件名称：
 * 版本： 1.0
 * 功能：发送接口
 * 最后修改时间：
 * 修改记录：
****************************************
 */
public interface ISender {
	/**
	 * 
	 * 发送
	 * @return
	 * <AUTHOR> 创建于 Jul 21, 2011 7:20:24 PM
	 * @throws
	 */
	public void doSend(SendMsg sendMsg) throws SendExp;

}
