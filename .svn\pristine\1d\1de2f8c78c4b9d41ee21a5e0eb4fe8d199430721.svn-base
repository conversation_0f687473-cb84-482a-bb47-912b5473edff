package com.shunwang.passport.swpaySDK.web.action;

import com.google.gson.Gson;
import com.shunwang.framework.struts2.action.BaseAction;
import com.shunwang.passport.swpaySDK.constant.Constants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2015-04-29
 */
public class SWPaySDKBaseAction extends BaseAction {

    private static final long serialVersionUID = 8511366532931504639L;

    protected Logger logger = LoggerFactory.getLogger(getClass());

    protected static Gson gson = Constants.gson;

    protected static final String RESULT = "result";
    protected static final String MSG = "msg";
    protected static final String IS_REG = "isReg";

    /**
     * <AUTHOR>
     * @param result
     * @return
     */
    protected Map<String, Object> createResultJSON(boolean result) {
        Map<String, Object> json = new HashMap<String, Object>();
        json.put(RESULT, result);
        return json;
    }

    /**
     * 把MAP对象转换成JSON字符串
     *
     * <AUTHOR>
     * @param json
     * @return
     */
    protected String toJSON(Map<String, Object> json) {
        return toJSON(json);
    }

    /**
     * 把对象转换成JSON字符串
     *
     * <AUTHOR>
     * @param json
     * @return
     */
    protected String toJSON(Object json) {
        return gson.toJson(json);
    }

    /**
     * 输出
     *
     * <AUTHOR>
     * @param obj The <code>Object</code> to be printed
     * @param contentType MIME类型
     * @param flush 是否立即提交
     */
    protected void write(Object obj, String contentType, boolean flush) {
        HttpServletResponse response = this.getResponse();
        response.setContentType(contentType);
        try {
            response.getWriter().print(obj);
            if (flush) {
                response.getWriter().flush();
            }
        } catch (IOException e) {
            logger.error("", e);
        }
    }

    /**
     * 输出json
     *
     * <AUTHOR>
     * @param obj
     */
    protected void writeJSON(Object obj) {
        write(toJSON(obj), "text/json;charset=UTF-8", false);
    }
}
