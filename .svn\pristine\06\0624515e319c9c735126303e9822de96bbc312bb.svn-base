<%@ page contentType="text/html;charset=UTF-8"%>
<%@ include file="/common/taglibs.jsp"%>
<script type="text/javascript" src="${staticServer}/scripts/front/member/sensitiveWords.js"></script>
<script type="text/javascript" src="${staticServer}/scripts/common/StringUtil.js"></script>
<script type="text/javascript" src="${staticServer}/scripts/common/md5.min.js"></script>
<tr>
    <th>问题一：</th>
    <td>
        <select name="question1" class="form_select" style="width:232px;" id="question1" onchange="doChange();">
             <option value="0">--请选择密保问题--</option>
        </select>
    </td>
</tr>
<tr>
    <th>答&nbsp;&nbsp;案：</th>
    <td>
        <input name="answer1i" type="text" class="form_input" style="width:220px;" id="answer1i" value="${answerShow1 }" tabindex="1" maxlength="64" />
        <input name="answer1" id="answer1" type="hidden">
        <span class="form_tip" id="errorSpan1">64个字符以内</span>
    </td>
</tr>
<tr>
    <th>问题二：</th>
    <td>
        <select name="question2" class="form_select" style="width:232px;" id="question2" onchange="doChange();">
            <option value="0">--请选择密保问题--</option>
        </select>
    </td>
</tr>
<tr>
    <th>答&nbsp;&nbsp;案：</th>
    <td>
        <input name="answer2i" type="text" class="form_input" style="width:220px;" id="answer2i" value="${answerShow2 }" tabindex="1"   maxlength="64" />
        <input name="answer2" id="answer2" type="hidden">
        <span class="form_tip" id="errorSpan2">64个字符以内</span>
    </td>
</tr>
<tr>
    <th>问题三：</th>
    <td>
        <select name="question3" class="form_select" style="width:232px;" id="question3" onchange="doChange();">
            <option value="0">--请选择密保问题--</option>
        </select>

    </td>
</tr>
<tr>
    <th>答&nbsp;&nbsp;案：</th>
    <td>
        <input name="answer3i" type="text" class="form_input" style="width:220px;" id="answer3i" value="${answerShow3 }" tabindex="1"  maxlength="64" />
        <input name="answer3" id="answer3" type="hidden">
        <span class="form_tip" id="errorSpan3">64个字符以内</span>
    </td>
</tr>

<script  type="text/javascript">
var questions = new Array();
<c:forEach var="memberQuestion" items="${memberQuestions}">
	questions.push({questionId:'${memberQuestion.questionId}', questionContext:'${memberQuestion.questionContext}'});
</c:forEach>

var question1 = document.getElementById("question1");
var question2 = document.getElementById("question2");
var question3 = document.getElementById("question3");

//初始化问题
function doInit(){
	
	if(questions.length==0 || questions==null){
		return ;
	}
	for(var i=0;i<questions.length;i++){
	
		var question = questions[i];
		
		question1.options.add(new Option(question.questionContext,question.questionId));
		question2.options.add(new Option(question.questionContext,question.questionId));		
		question3.options.add(new Option(question.questionContext,question.questionId));
	}
	
}

function doInitQuestionError(){
	setOption(question1,"${question1}");
	setOption(question2,"${question2}");
	setOption(question3,"${question3}");
}

//下拉框改变事件
function doChange(){
	 
	var q1Id = question1.value;
	
	var q2Id = question2.value;
	var q3Id = question3.value;
	var oneIndex = 0;
	var twoIndex = 0;
	var threeIndex = 0;
	 
	question2.options.length=0;
	question1.options.length=0;
	question3.options.length=0;
	
	question1.options.add(new Option("--请选择密保问题--",0));
	question2.options.add(new Option("--请选择密保问题--",0));
	question3.options.add(new Option("--请选择密保问题--",0));
	
	oneIndex = doResetSelectBox(q1Id,q2Id,q3Id,question1);
	twoIndex = doResetSelectBox(q2Id,q1Id,q3Id,question2);
	threeIndex = doResetSelectBox(q3Id,q1Id,q2Id,question3);
	question1.options.selectedIndex = oneIndex;
	question2.options.selectedIndex = twoIndex;
	question3.options.selectedIndex = threeIndex;
	
}

//加载当前下拉框并返回选择的索引值
function doResetSelectBox(currentId,otherId1,otherId2,selectBox){

	var j=1;
	var index = 0;
	 
	for(var i=0;i<questions.length;i++){
		var question = questions[i];
		var questId = question.questionId;
		
		if(otherId1!="0"&&otherId1==questId){
			 
			continue;
		}
		if(otherId2!="0"&&otherId2==questId){
			continue;
		}
		selectBox.options.add(new Option(question.questionContext,question.questionId));
		
		if(questId==currentId)
			index = j;
		j++;
	}
	return index;
}
 

doInit();
function check(){
	if(checkQuestion()&&checkAnswer()){
		return true;
	}
	return false;
}
function checkQuestion(){
    document.getElementById("errorMessagesShowSpan").innerHTML="";
	if(question1.value==null||question1.value=="0"){
		document.getElementById("errorMessagesShowSpan").innerHTML="请选择一个问题作为问题一！";
		question1.focus();
		return false;
	}
	if(question2.value==null||question2.value=="0"){
		document.getElementById("errorMessagesShowSpan").innerHTML="请选择一个问题作为问题二！";
		question2.focus();
		return false;
	}
	if(question3.value==null||question3.value=="0"){
		document.getElementById("errorMessagesShowSpan").innerHTML="请选择一个问题作为问题三！";
		question3.focus();
		return false;
	}
	return true;
}
function checkAnswer(){
    document.getElementById("errorSpan1").innerHTML="64个字符以内";
    document.getElementById("errorSpan2").innerHTML="64个字符以内";
    document.getElementById("errorSpan3").innerHTML="64个字符以内";
    document.getElementById("errorSpan1").className="form_tip";
    document.getElementById("errorSpan2").className="form_tip";
    document.getElementById("errorSpan3").className="form_tip";
	var answer1i=document.getElementById("answer1i");
	var answer2i=document.getElementById("answer2i");
	var answer3i=document.getElementById("answer3i");
	if(answer1i.value==null||answer1i.value.Trim()=="")
	{
		document.getElementById("errorSpan1").innerHTML="答案不能为空！";
        document.getElementById("errorSpan1").className="form_error";
        answer1i.focus();
		answer1i.value="";
		return false;
	}
	if(answer2i.value==null||answer2i.value.Trim()=="")
	{
		document.getElementById("errorSpan2").innerHTML="答案不能为空！";
        document.getElementById("errorSpan2").className="form_error";
        answer2i.focus();
		answer2i.value="";
		return false;
	}
	if(answer3i.value==null||answer3i.value.Trim()=="")
	{
		document.getElementById("errorSpan3").innerHTML="答案不能为空";
        document.getElementById("errorSpan3").className="form_error";
        answer3i.focus();
		answer3i.value=="";
		return false;
	}
	if(answer1i.value.Trim().gblen()<=0 || answer1i.value.Trim().gblen()>64){
		document.getElementById("errorSpan1").innerHTML="64个字符以内";
        document.getElementById("errorSpan1").className="form_error";
		answer1i.focus();
		return false;
	}
	if(answer2i.value.Trim().gblen()<=0 || answer2i.value.Trim().gblen()>64){
		document.getElementById("errorSpan2").innerHTML="64个字符以内！";
        document.getElementById("errorSpan2").className="form_error";
        answer2i.focus();
		return false;
	}
	if(answer3i.value.Trim().gblen()<=0 || answer3i.value.Trim().gblen()>64){
		document.getElementById("errorSpan3").innerHTML="64个字符以内！";
        document.getElementById("errorSpan3").className="form_error";
        answer3i.focus();
		return false;
	}
	var reg=/[<&{\/\'\"]/; 
	if(reg.test(answer1i.value))
	{
		document.getElementById("errorSpan1").innerHTML="不能包含特殊字符/<&{'\"，请重输！";
        document.getElementById("errorSpan1").className="form_error";
        answer1i.focus();
		return false;
	}
	if(reg.test(answer2i.value))
	{
		document.getElementById("errorSpan2").innerHTML="不能包含特殊字符/<&{'\"，请重输！";
        document.getElementById("errorSpan2").className="form_error";
        answer2i.focus();
		return false;
	}
	if(reg.test(answer3i.value))
	{
		document.getElementById("errorSpan3").innerHTML="不能包含特殊字符/<&{'\"，请重输！";
        document.getElementById("errorSpan3").className="form_error";
        answer3i.focus();
		return false;
	}
	document.getElementById("answer1").value=SBC2DBC(hex_md5(answer1i.value.Trim()));
	document.getElementById("answer2").value=SBC2DBC(hex_md5(answer2i.value.Trim()));
	document.getElementById("answer3").value=SBC2DBC(hex_md5(answer3i.value.Trim()));
	return true;
}


String.prototype.gblen = function() {   
	var len = 0;   
	for (var i=0; i<this.length; i++) {   
		if (this.charCodeAt(i)>127 || this.charCodeAt(i)==94) {   
			len += 2;   
		} else {   
			len ++;   
		}   
	}   
	return len;   
}
String.prototype.Trim= function()      
{      
	  return this.replace(/(^[\s|　]*)|([\s|　]*$)/g,"");    
} 
</script>