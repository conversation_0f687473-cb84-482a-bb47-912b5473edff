package com.shunwang.baseStone.sso.web;


import com.shunwang.baseStone.context.RedisContext;
import com.shunwang.baseStone.context.RedisOperation;
import com.shunwang.baseStone.context.TransactionContext;
import com.shunwang.baseStone.sso.context.SsoDomainContext;
import com.shunwang.basepassport.weixin.constant.BarLoginReportEnum;
import com.shunwang.baseStone.sso.netbar.NetBarLoginJob;
import com.shunwang.baseStone.sso.netbar.NetBarService;
import com.shunwang.basepassport.weixin.pojo.BarLoginReportInfo;
import com.shunwang.baseStone.sso.pojo.QrCodeResponse;
import com.shunwang.baseStone.sso.util.IdcardValidatorUtil;
import com.shunwang.baseStone.sso.weixin.task.BindTemplateMsgJob;
import com.shunwang.basepassport.asynctask.AsyncTaskExecutor;
import com.shunwang.basepassport.config.constants.WxTemplateMsgConstants;
import com.shunwang.basepassport.config.dao.ConfigResourcesDao;
import com.shunwang.basepassport.config.dao.ConfigWxBindAdDao;
import com.shunwang.basepassport.config.dao.WxTemplateMsgDao;
import com.shunwang.basepassport.config.pojo.ConfigResources;
import com.shunwang.basepassport.config.pojo.ConfigWxBindAd;
import com.shunwang.basepassport.config.pojo.WxTemplateMsg;
import com.shunwang.basepassport.user.dao.MemberOutSiteDao;
import com.shunwang.basepassport.user.dao.WxIdCardBindDao;
import com.shunwang.basepassport.user.pojo.Member;
import com.shunwang.basepassport.user.pojo.WxIdCardBind;
import com.shunwang.basepassport.weixin.constant.WeixinConstant;
import com.shunwang.basepassport.weixin.pojo.WeixinOauthToken;
import com.shunwang.basepassport.weixin.service.WeixinOauthTokenService;
import com.shunwang.framework.struts2.action.BaseAction;
import com.shunwang.util.encrypt.Md5Encrypt;
import com.shunwang.util.json.GsonUtil;
import com.shunwang.util.lang.StringUtil;
import org.apache.struts2.ServletActionContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 微信扫码登录上机，如果发现没有绑定过身份证，则需要进行绑定
 */
public class WxIdCardBindAction extends BaseAction{

	private final static Logger reportLogger = LoggerFactory.getLogger("com.shunwang.baseStone.sso.netBarReportLog");
	protected final Logger log = LoggerFactory.getLogger(this.getClass());


	private static final long serialVersionUID = 6699129077428474968L;
	/** 状态：正常 **/
	private static final Integer STATE_NORMAL = 1;
	/** 状态：停用 **/
	private static final Integer STATE_STOP = 2;

	private final String jfKey = "J!@F^3Sa21fe";
	private String configWxBindAd_list_redis_key = "query_wxBindAd_type_1";
	private String configWxBindAd_config_redis_key = "back_resource_wxscancodeconfig_adblackbarid";
	private String configWxTemplateMsg_redis_key = "configWxTemplateMsg_data";
	private String send_templateMsgUrl_redis_key = "send_templateMsg_url_redis_key";

	private String idCardNo;
	private String eventKey;
	private String idCardNoResult;
	private String barid;
	private String unionId;
	private Integer memberId;

	private WxIdCardBindDao    wxIdCardBindDao;
	private ConfigWxBindAdDao  configWxBindAdDao;
	private ConfigResourcesDao configResourcesDao;
	private WxTemplateMsgDao   wxTemplateMsgDao;
	private NetBarService      netBarService;
	private WeixinOauthTokenService weixinOauthTokenService;
	private MemberOutSiteDao memberOutSiteDao;

	protected RedisOperation redisOperation;


	public String bindPage(){
		QrCodeResponse qrCodeResponse = GsonUtil.fromJson( RedisContext.getRedisCache().get(eventKey), QrCodeResponse.class);
		if (qrCodeResponse == null){
			return INPUT;
		}
		String extData = qrCodeResponse.getExtData();
		BarLoginReportInfo reportInfo = new BarLoginReportInfo(BarLoginReportEnum.OptTypeEnum.OPT_GO_BIND, extData);

		String unionId = qrCodeResponse.getUnionId();
		WxIdCardBind wxIdCardBind = wxIdCardBindDao.getByUnionId( unionId );
		if (wxIdCardBind != null){
			reportInfo.setOptType(BarLoginReportEnum.OptTypeEnum.OPT_GO_RESULT.getValue());
			reportInfo.setUnionId(unionId);
			reportLogger.info(GsonUtil.toJson(reportInfo));
			return SUCCESS;
		}
		reportLogger.info(GsonUtil.toJson(reportInfo));
		return INPUT;
	}

	public String editPage() throws IOException {
		Member member = memberOutSiteDao.getByOutMemberId(unionId);
		if (!member.getMemberId().equals(memberId)) {
			getResponse().getWriter().write("参数非法！");
			return null;
		}
		WxIdCardBind wxIdCardBind = wxIdCardBindDao.getByUnionId(unionId);
		idCardNo = wxIdCardBind.getIdCardNo();
		return INPUT;
	}

	public String edit() throws IOException {
		boolean success = RedisContext.getRedisCache().setNx("wxIdCardBind_" + unionId, "1", 30, TimeUnit.SECONDS);
		HttpServletResponse response = ServletActionContext.getResponse();
		response.setContentType("text/json;charset=UTF-8");
		if (success) {
			try {
				if (memberId == null || StringUtil.isBlank(unionId) || StringUtil.isBlank(idCardNo)) {
					response.getWriter().write("{\"msg\": \"参数非法！\"}");
					return null;
				}
				Member member = memberOutSiteDao.getByOutMemberId(unionId);
				if (!member.getMemberId().equals(memberId)) {
					response.getWriter().write("{\"msg\": \"参数非法！\"}");
					return null;
				}
				if (!IdcardValidatorUtil.isValidatedAllIdcard(idCardNo)) {
					//身份证验证失败
					response.getWriter().write("{\"msg\": \"身份证格式错误！\"}");
					return null;
				}

				WxIdCardBind old = wxIdCardBindDao.getByUnionId(unionId);
				if (old.getIdCardNo().equals(idCardNo)) {
					response.getWriter().write("{\"msg\": \"新身份证与旧身份证相同\"}");
					return null;
				}

				List<WxIdCardBind> historyRecords = wxIdCardBindDao.getAllByUnionId(unionId);
				if (historyRecords.size() >= 2) {
					response.getWriter().write("{\"msg\": \"身份证只能修改一次!\"}");
					return null;
				}

				try {
					TransactionContext.beginTransaction();
					//保存新身份证
					WxIdCardBind newBind = new WxIdCardBind();
					newBind.setIdCardNo(idCardNo);
					newBind.setIdCardNoSend(genUserIdEncrypt());
					newBind.setUnionId(unionId);
					newBind.setCreateTime(new Date());
					newBind.setTailNo(idCardNo.substring(idCardNo.length() - 6));
					newBind.setState(STATE_NORMAL);
					wxIdCardBindDao.save(newBind);

					//修改旧身份证状态
					old.setState(STATE_STOP);
					wxIdCardBindDao.updateState(old);
					TransactionContext.commitTran();
				} catch (Exception e) {
					TransactionContext.rollbackTran();
					log.error("微信修改绑定身份证失败", e);
					response.getWriter().write("{\"msg\": \"系统异常!\"}");
					return null;
				}
				response.getWriter().write("{\"code\": \"0\"}");
				return null;
			} finally {
				RedisContext.getRedisCache().del("wxIdCardBind_" + unionId);
			}
		}
		response.getWriter().write("{\"msg\": \"请勿频繁操作!\"}");
		return null;

	}

	public String resultPage(){
		QrCodeResponse qrCodeResponse = GsonUtil.fromJson( RedisContext.getRedisCache().get(eventKey), QrCodeResponse.class);
		if (qrCodeResponse != null){
			String extData = qrCodeResponse.getExtData();
			BarLoginReportInfo reportInfo = new BarLoginReportInfo(BarLoginReportEnum.OptTypeEnum.OPT_GO_RESULT, extData);
			reportLogger.info(GsonUtil.toJson(reportInfo));
		}
		return INPUT;
	}

	@Override
	public String execute() throws Exception {
		HttpServletResponse response = ServletActionContext.getResponse();
		QrCodeResponse qrCodeResponse = GsonUtil.fromJson( RedisContext.getRedisCache().get(eventKey), QrCodeResponse.class);

		if (qrCodeResponse == null){
			response.getWriter().write("{\"code\": -1}");
			return null;
		}

		String unionId = qrCodeResponse.getUnionId();
		if (StringUtil.isBlank( unionId ) || StringUtil.isBlank( idCardNo ) || StringUtil.isBlank( eventKey )){
			response.getWriter().write("{\"code\": -2}");
			return null;
		}
		if (!IdcardValidatorUtil.isValidatedAllIdcard(idCardNo)){
			//身份证验证失败
			response.getWriter().write("{\"code\": -2}");
			return null;
		}
		WxIdCardBind wxIdCardBind = wxIdCardBindDao.getByUnionId( unionId );
		if (wxIdCardBind != null){
			response.getWriter().write("{\"code\": -3}");
			return null;
		}
		wxIdCardBind = new WxIdCardBind();
		wxIdCardBind.setIdCardNo( idCardNo );
		wxIdCardBind.setIdCardNoSend( genUserIdEncrypt() );
		wxIdCardBind.setUnionId( unionId );
		wxIdCardBind.setCreateTime( new Date() );
		wxIdCardBind.setTailNo( idCardNo.substring(idCardNo.length() - 6) );
		wxIdCardBind.setState( STATE_NORMAL );
		wxIdCardBindDao.save( wxIdCardBind );
		//上机数据上报
		String extData = qrCodeResponse.getExtData();
		BarLoginReportInfo reportInfo = new BarLoginReportInfo(BarLoginReportEnum.OptTypeEnum.OPT_DO_BIND, extData);
		reportInfo.setExtInfo(BarLoginReportEnum.ExtInfo.INFO_0.getValue());
		reportLogger.info(GsonUtil.toJson(reportInfo));

		AsyncTaskExecutor.submitForBar(new NetBarLoginJob(eventKey, wxIdCardBind.getIdCardNoSend(), netBarService));
		//绑定成功发送微信模版消息
		sendWxTemplateMsgTask(qrCodeResponse);
		response.getWriter().write("{\"code\": 0}");
		return null;
	}
	public String report() throws Exception {
		HttpServletRequest request = ServletActionContext.getRequest();
		String type = request.getParameter("type");
		String eventKey = request.getParameter("eventKey");
		String extInfo = request.getParameter("extInfo");
		HttpServletResponse response = ServletActionContext.getResponse();
		QrCodeResponse qrCodeResponse = GsonUtil.fromJson( RedisContext.getRedisCache().get(eventKey), QrCodeResponse.class);

		if (qrCodeResponse == null || StringUtil.isBlank(type)){
			response.getWriter().write("{\"code\": -1}");
			return null;
		}

		//上机数据上报
		String extData = qrCodeResponse.getExtData();
		BarLoginReportInfo reportInfo;
		if (BarLoginReportEnum.OptTypeEnum.OPT_TYPE_INDEX_READY.getValue() == Integer.parseInt(type)) {
			reportInfo = new BarLoginReportInfo(BarLoginReportEnum.OptTypeEnum.OPT_TYPE_INDEX_READY, extData);
		} else if (BarLoginReportEnum.OptTypeEnum.OPT_TYPE_RESULT_READY.getValue() == Integer.parseInt(type)) {
			reportInfo = new BarLoginReportInfo(BarLoginReportEnum.OptTypeEnum.OPT_TYPE_RESULT_READY, extData);
		} else if (BarLoginReportEnum.OptTypeEnum.OPT_TYPE_BIND_BTN.getValue() == Integer.parseInt(type)) {
			reportInfo = new BarLoginReportInfo(BarLoginReportEnum.OptTypeEnum.OPT_TYPE_BIND_BTN, extData);
		} else if (BarLoginReportEnum.OptTypeEnum.OPT_TYPE_AD_CLICK.getValue() == Integer.parseInt(type)) {
			reportInfo = new BarLoginReportInfo(BarLoginReportEnum.OptTypeEnum.OPT_TYPE_AD_CLICK, extData);
		} else {
			response.getWriter().write("{\"code\": 1}");
			return null;
		}
		reportInfo.setUnionId(qrCodeResponse.getUnionId());
		reportInfo.setExtInfo(extInfo);
		reportLogger.info(GsonUtil.toJson(reportInfo));

		response.getWriter().write("{\"code\": 0}");
		return null;
	}
	/**
	 * 绑定成功页面广告列表
	 * @return
	 * @throws IOException
	 */
	public String getAllAds() throws IOException {
		HttpServletResponse response = ServletActionContext.getResponse();

		//如果barId在配置名单中，则不加载广告
		ConfigResources configResources = getFromRedis();
		//state = 0，配置不可用，barId在value则在黑名单
		if (configResources != null && configResources.getState() == 1 && StringUtil.isNotBlank( configResources.getValue() )
				&& StringUtil.isNotBlank( barid ) && configResources.getValue().indexOf( barid ) != -1){
			String [] barIdArray = configResources.getValue().split( "," );
			boolean isShow = true;
			for(int i = 0; i < barIdArray.length; i++){
				if (barid.equals( barIdArray[i] )){
					isShow = false;
				}
			}
			if (!isShow){
				response.getWriter().write(GsonUtil.toJson( new ArrayList<ConfigWxBindAd>() ));
				return null;
			}
		}

		String adsJson = RedisContext.getRedisCache().get( configWxBindAd_list_redis_key );
		if (StringUtil.isBlank( adsJson )){
			List<ConfigWxBindAd> adList = configWxBindAdDao.findAll();
			for (ConfigWxBindAd ad : adList){
				if (StringUtil.isNotBlank( ad.getImgUrl() )){
					ad.setImgUrlShow( SsoDomainContext.getBackStaticServer() + ad.getImgUrlShow() );
				}
			}
			adsJson = GsonUtil.toJson( adList );
			RedisContext.getRedisCache().set( configWxBindAd_list_redis_key, adsJson, 1, TimeUnit.DAYS );
		}

		response.getWriter().write(adsJson);
		return null;
	}

	private String genUserIdEncrypt() {
		return "JF" + Md5Encrypt.encrypt(getIdCardNo().toUpperCase() + jfKey).toUpperCase();
	}

	private ConfigResources getFromRedis(){
		String configResourcesJson = RedisContext.getRedisCache().get( configWxBindAd_config_redis_key );
		if (configResourcesJson != null){
			return GsonUtil.jsonToBean( configResourcesJson, ConfigResources.class );
		}
		ConfigResources configResources = configResourcesDao.findResourcesByTypeAndName( "wxScanCodeConfig", "adBlackBarId" );
		if (configResources != null){
			RedisContext.getRedisCache().set( configWxBindAd_config_redis_key, GsonUtil.toJson( configResources ), 1, TimeUnit.DAYS );
			return configResources;
		}

		return null;
	}

	private void sendWxTemplateMsgTask(QrCodeResponse qrCodeResponse){
		String wxTemplateMsgJson = RedisContext.getRedisCache().get( configWxTemplateMsg_redis_key );
		WxTemplateMsg wxTemplateMsg;
		if (StringUtil.isNotBlank( wxTemplateMsgJson )){
			wxTemplateMsg = GsonUtil.jsonToBean( wxTemplateMsgJson, WxTemplateMsg.class );
		}else{
			wxTemplateMsg = wxTemplateMsgDao.getByType( WxTemplateMsgConstants.WxTemplateMsgType.BIND_IDCARD_TYPE );
			if (wxTemplateMsg == null){
				return;
			}
			RedisContext.getRedisCache().set( configWxTemplateMsg_redis_key, GsonUtil.toJson( wxTemplateMsg ), 1, TimeUnit.DAYS );
		}

		String sendUrlJson = RedisContext.getRedisCache().get( send_templateMsgUrl_redis_key );
		ConfigResources configResources;
		if (StringUtil.isNotBlank( sendUrlJson )){
			configResources = GsonUtil.jsonToBean( sendUrlJson, ConfigResources.class );
		}else{
			configResources = configResourcesDao.findResourcesByTypeAndName( "wxTemplateMsgUrlConfig", "wxTemplateMsgUrl" );
			RedisContext.getRedisCache().set( send_templateMsgUrl_redis_key, GsonUtil.toJson( configResources ), 1, TimeUnit.DAYS );
		}
		WeixinOauthToken weixinOauthToken = weixinOauthTokenService.getByAppIdAndType(wxTemplateMsg.getAppid(), WeixinConstant.TYPE.AUTHORIZER) ;

		if (weixinOauthToken == null){
			return;
		}
		AsyncTaskExecutor.submitForBar(new BindTemplateMsgJob(weixinOauthToken.getAccessToken(), qrCodeResponse.getOpenId(), idCardNo, configResources.getValue(), wxTemplateMsg));

		String extData = qrCodeResponse.getExtData();
		BarLoginReportInfo reportInfo = new BarLoginReportInfo(BarLoginReportEnum.OptTypeEnum.OPT_TYPE_SEND_TEMPLATE_MSG, extData);
		reportInfo.setExtInfo(BarLoginReportEnum.ExtInfo.INFO_2.getValue());
		reportLogger.info(GsonUtil.toJson(reportInfo));
	}

	public String getIdCardNo() {
		return idCardNo;
	}

	public void setIdCardNo(String idCardNo) {
		this.idCardNo = idCardNo;
	}

	public String getEventKey() {
		return eventKey;
	}

	public void setEventKey(String eventKey) {
		this.eventKey = eventKey;
	}

	public String getIdCardNoResult() {
		String result = idCardNo;
		return result.replace(result.substring(3,14),"***");
	}

	public void setIdCardNoResult(String idCardNoResult) {
		this.idCardNoResult = idCardNoResult;
	}

	public WxIdCardBindDao getWxIdCardBindDao() {
		return wxIdCardBindDao;
	}

	public void setWxIdCardBindDao(WxIdCardBindDao wxIdCardBindDao) {
		this.wxIdCardBindDao = wxIdCardBindDao;
	}

	public NetBarService getNetBarService() {
		return netBarService;
	}

	public void setNetBarService(NetBarService netBarService) {
		this.netBarService = netBarService;
	}

	public RedisOperation getRedisOperation() {
		return redisOperation;
	}

	public void setRedisOperation(RedisOperation redisOperation) {
		this.redisOperation = redisOperation;
	}

	public ConfigWxBindAdDao getConfigWxBindAdDao() {
		return configWxBindAdDao;
	}

	public void setConfigWxBindAdDao(ConfigWxBindAdDao configWxBindAdDao) {
		this.configWxBindAdDao = configWxBindAdDao;
	}

	public ConfigResourcesDao getConfigResourcesDao() {
		return configResourcesDao;
	}

	public void setConfigResourcesDao(ConfigResourcesDao configResourcesDao) {
		this.configResourcesDao = configResourcesDao;
	}

	public String getBarid() {
		return barid;
	}

	public void setBarid(String barid) {
		this.barid = barid;
	}

	public WxTemplateMsgDao getWxTemplateMsgDao() {
		return wxTemplateMsgDao;
	}

	public void setWxTemplateMsgDao(WxTemplateMsgDao wxTemplateMsgDao) {
		this.wxTemplateMsgDao = wxTemplateMsgDao;
	}

	public WeixinOauthTokenService getWeixinOauthTokenService() {
		return weixinOauthTokenService;
	}

	public void setWeixinOauthTokenService(WeixinOauthTokenService weixinOauthTokenService) {
		this.weixinOauthTokenService = weixinOauthTokenService;
	}

	public String getUnionId() {
		return unionId;
	}

	public void setUnionId(String unionId) {
		this.unionId = unionId;
	}

	public Integer getMemberId() {
		return memberId;
	}

	public void setMemberId(Integer memberId) {
		this.memberId = memberId;
	}

	public MemberOutSiteDao getMemberOutSiteDao() {
		return memberOutSiteDao;
	}

	public void setMemberOutSiteDao(MemberOutSiteDao memberOutSiteDao) {
		this.memberOutSiteDao = memberOutSiteDao;
	}
}
