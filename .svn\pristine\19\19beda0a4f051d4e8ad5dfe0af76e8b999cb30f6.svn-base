package com.shunwang.passport.common.interceptor;

import com.shunwang.baseStone.sso.exception.SSOException;
import com.shunwang.baseStone.sso.interceptor.LoginInterceptor;
import com.shunwang.baseStone.sso.pojo.SSOUser;
import com.shunwang.basepassport.context.UserContext;
import com.shunwang.basepassport.user.pojo.Member;
import com.shunwang.passport.common.constant.CommonConststant;
import com.shunwang.passport.common.context.DomainContext;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.Map;
import java.util.Set;

public class SWPaySDKUserLoginCheckInterceptor extends LoginInterceptor {

    private static final long serialVersionUID = -93488348038282924L;

    private static final Logger log = LoggerFactory.getLogger(SWPaySDKUserLoginCheckInterceptor.class);

    /**
     * 校验失败
     */
    @Override
    protected void handleException(HttpServletRequest servletRequest,
                                   HttpServletResponse servletResponse, SSOException validateException) throws IOException {
        writeLog(servletRequest, validateException);
        callSdk(servletResponse);
    }

    /**
     * 未登录或登陆超时
     */
    @Override
    protected void handleNoTicket(HttpServletRequest servletRequest, HttpServletResponse servletResponse) {
        try {
            callSdk(servletRequest,servletResponse);
        } catch (IOException e) {
            log.error("SWPaySDKUserLoginCheckInterceptor handleNoTicket ERROR", e);
        }
    }

    @Override
    protected boolean isLogin(HttpServletRequest request) {
        try {
            return null != UserContext.getUserId();
        } catch (Exception e) {
            log.error("SWPaySDKUserLoginCheckInterceptor isLogin ERROR", e);
            return dealNoLogin();
        }
    }

    /**
     * 未登录处理
     *
     * @return
     */
    private boolean dealNoLogin() {
        UserContext.removeUser();
        return false;
    }

    @Override
    protected void refushUser(HttpServletResponse response) {
        UserContext.setUser(UserContext.getUser());
    }

    @Override
    protected void saveUser(HttpServletRequest servletRequest, HttpServletResponse response, SSOUser user) {
        Member member = new Member();
        member.setMemberId((Integer) user.getUserId());
        member.setTitleName(user.getTitleName());
        member.setMemberName(user.getUserName());
        UserContext.setUser(member);
    }

    @Override
    public String getDomain() {
        return DomainContext.getAppServer();
    }

    protected void callSdk(HttpServletResponse servletResponse) throws IOException {
        String url = DomainContext.getAppServer() + CommonConststant.SWPAYSDK_UNLOGIN_PAGE;
        servletResponse.sendRedirect(url);
    }

    protected void callSdk(HttpServletRequest request,HttpServletResponse servletResponse) throws IOException {
        String url = DomainContext.getAppServer() + CommonConststant.SWPAYSDK_UNLOGIN_PAGE;
        if(null != request) {
            String msg = request.getParameter("msg");
            if (StringUtils.isNoneBlank(msg)) {
                url = url.indexOf("?") >= 0 ? (url + "&msg=" + msg) : (url + "?msg=" + msg);
            }
            servletResponse.sendRedirect(url);
        }
    }

    @SuppressWarnings("unchecked")
    protected String getParamStr(HttpServletRequest servletRequest) {
        Map<String, String[]> paramMap = servletRequest.getParameterMap();
        StringBuilder builder = new StringBuilder();
        Set<String> set = paramMap.keySet();
        for (String key : set) {
            String[] vals = paramMap.get(key);
            for (String val : vals) {
                if (val != null)
                    try {
                        val = java.net.URLEncoder.encode(val, "UTF-8");
                    } catch (UnsupportedEncodingException e) {
                        log.error("SWPaySDKUserLoginCheckInterceptor getParamStr ERROR", e);
                    }
                builder.append(key).append("=").append(val).append("&");
            }
        }
        return builder.toString();
    }

    /**
     * 记录日志
     *
     * @param servletRequest
     * @param e
     */
    protected void writeLog(HttpServletRequest servletRequest, Exception e) {
        String paramsStr = getParamStr(servletRequest);
        StringBuilder logInfo = new StringBuilder("{登录检查结果：false,用户IP：");
        logInfo.append(servletRequest.getRemoteAddr()).append(",访问地址：")
                .append(servletRequest.getRequestURI()).append(",参数列表：{")
                .append(paramsStr).append("}}");
        log.error(logInfo.toString(), e);
    }
}
