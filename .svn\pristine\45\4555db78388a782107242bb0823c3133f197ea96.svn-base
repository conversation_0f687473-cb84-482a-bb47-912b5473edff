//20250610 网易本机登录js

$(document).ready(function () {
  const $NEOneContainer = $("#NEOneContainer");
  const $loginPhoneStepBox = $("#loginPhoneStep");

  const defaultConfig = {
    businessId: oneLoginBusinessId,
    mode: "float",
    phoneInputStyle: "square",
    elements: [
      "back",
      "logo",
      "slogan",
      "phone",
      "loginButton",
      "switchButton",
      "policy",
    ],
    logo: staticUrl + "/h5OneLogin/images/logo.png",
    logoStyles: {
      top: 58,
      width: 74,
      height: 74,
    },
    sloganAppName: "顺网会员中心",
    sloganStyles: {
      top: 22,
    },
    phoneStyles: {
      top: 64,
      size: 28,
      squareWidth: 33,
      squareHeight: 47,
      squareBackground: "#F1F1F1",
    },
    switchBtnText: "< 其他方式登录",
    switchBtnStyles: {
      top: 24,
      color: "#333",
    },
    loginBtnText: "本机号码一键登录",
    loginBtnStyles: {
      top: 60,
      size: 16,
      height: 50,
      background: "#4768F2",
    },
    policy: [
      {
        content: "用户协议",
        url: "//i.kedou.com/agreement/register",
      },
      {
        content: "隐私授权",
        url: "//i.kedou.com/agreement/privacy",
      },
    ],
    policyStyles: {
      top: 24,
      size: 12,
      linkColor: "#3881FF",
    },
    iframeStyles: {
      position: "absolute",
      top: "0",
      left: "0",
      riggt: "0",
      height: "500px",
      background: 'url("' + staticUrl + '/h5OneLogin/images/verify-phone-bg.png")', 
      backgroundSize: "100% 100%",
      backgroundRepeat: "no-repeat",
    },
  };

  // const configUrl = "https://callback.kedou.com/yxq/static/1.0.0/js/data.json";
  const configUrl = $("#login-form").find("input[name='oneLoginStyleJsUrl']").val();
  let neOneLogin;

  configUrl ? loadJSON(configUrl) : initNEOneLogin();

  // 切换到其他登录方式
  function switchOtherLogin() {
    // 业务方可以在这里处理 OneLogin 失败后的降级流程
    $loginPhoneStepBox.show();
    $NEOneContainer.hide();
    neOneLogin.dispose();
  }

  // 获取外部JSON数据
  async function loadJSON(url) {
    try {
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      initNEOneLogin(data);
      return data;
    } catch (error) {
      console.error("加载JSON失败:", error);
    }
  }

  /**
   * 初始化 NEOneLogin
   * @param {Object} data - 配置数据
   */
  function initNEOneLogin(data = {}) {
    console.log(data);
    const _data = typeof data === 'string' ? JSON.parse(data): data;
    neOneLogin = new window.NEOneLogin({ ...defaultConfig, ..._data });
    neOneLogin.getToken();

    neOneLogin.on("success", (data) => {
      console.log("success", data);
      // 成功拿到 token 和 accessToken 后像服务端发起校验
      $.ajax({
        url: '/createByYiDunH5OneClickLogin.do',
        data: {
          token: data.token,
          accessToken: data.accessToken,
          siteId: $("#login-form").find("input[name='site_id']").val(),
          time: getCurrentTimeFormatted(),
        },
        type: "post",
        cache: false,
        dataType: "json",
        success: function (data) {
          var callbackUrl = $("#login-form").find("input[name='callbackUrl']").val();
          console.log(data);
          if (!data.result) {
            showToastError("一键登录失败，请换验证码登录");
            neOneLogin.enableLoginButton();
            switchOtherLogin();
            return;
          }
          var ticketId = data.ticketId;
          var tockenId = data.tockenId;
          // 判断URL是否已有参数
          var separator = (callbackUrl.indexOf('?') === -1) ? '?' : '&';
          // 拼接参数
          var newUrl = callbackUrl + separator + 'ticketId=' + ticketId + '&tockenId=' + tockenId;

          window.location.href = newUrl;
        },
        error: function (jqXHR, textStatus, errorThrow) {
          showToastError("一键登录失败，请换验证码登录");
          neOneLogin.dispose();
          switchOtherLogin();
        },
      });
    });
    neOneLogin.on("error", (err) => {
      // err.data 中可以拿到错误信息
      console.log("error", err.data);
      // 如果需要用户无感知切换到其他登录方式，可以把错误提示去掉
      // showToastError(err.data?.msg || "异常");
      // alert(JSON.stringify(err))
      if (err.data?.code === 20002 || err.data?.code === 30001 || err.data?.code === 30003 || err.data?.code === 40002) {
        showToastError("一键登录失败，请换验证码登录");
      }

      neOneLogin.dispose();
      // neOneLogin = null;
      // 失败后可以走业务方自己的降级流程
      switchOtherLogin();
    });

    neOneLogin.on("switch", () => {
      // 当授权页点击切换按钮时触发
      // 与 close 事件不同的是，close 事件触发时意味着授权页已经被关闭
      // 但 switch 事件不会关闭授权页，如果需要关闭，需要开发调用 disposeAuthFrame 方法
      switchOtherLogin();
    });
  }

  function getCurrentTimeFormatted() {
    var now = new Date();

    var year = now.getFullYear();           // 四位年份
    var month = now.getMonth() + 1;         // 月份（从0开始，需+1）
    var day = now.getDate();                // 日期
    var hours = now.getHours();             // 小时
    var minutes = now.getMinutes();         // 分钟
    var seconds = now.getSeconds();         // 秒

    // 补零函数
    function padZero(n) {
      return n < 10 ? '0' + n : n;
    }

    // 拼接成 YYYYMMDDHHMMSS 格式
    return '' +
        year +
        padZero(month) +
        padZero(day) +
        padZero(hours) +
        padZero(minutes) +
        padZero(seconds);
  }

});
