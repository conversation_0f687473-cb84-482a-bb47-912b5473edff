package com.shunwang.basepassport.user.web;

import com.shunwang.baseStone.config.constants.MemberEnum;
import com.shunwang.basepassport.asynctask.AsyncTaskExecutor;
import com.shunwang.basepassport.asynctask.MemberCancelNoticeJob;
import com.shunwang.basepassport.user.common.MemberConstants;
import com.shunwang.basepassport.user.dao.ServiceNotifyDao;
import com.shunwang.basepassport.user.pojo.Member;

public class MemberCancelNoticeAction extends MemberAction {

	private static final long serialVersionUID = -8504242884886373808L;

	private ServiceNotifyDao serviceNotifyDao;
	private String memberName;
    /**
     * type为1或不传时：用户注销操作通知
     * type为2时：用户撤销注销通知
     * type为3时：系统已注销账户通知
     */
	private Integer type;

	@Override
	public String getSiteName() {
		return MemberConstants.MEMBER_CANCEL_NOTICE;
	}

    @Override
    public String getMemberName() {
        return memberName;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    @Override
    public void doProcess() {
        log.info("不被调用");
    }


	@Override
	public void process() {
        Member member = super.getMemberInfo();//校验membername,memberId,且 member!=null
        MemberCancelNoticeJob job = new MemberCancelNoticeJob( serviceNotifyDao,
                member, type == null ?
                MemberEnum.CancelNotifyType.TYPE_COMMIT_CANCEL.getType() : type);
        AsyncTaskExecutor.submit(job);
	}

    public void setMemberName(String memberName) {
        this.memberName = memberName;
    }

    public ServiceNotifyDao getServiceNotifyDao() {
        return serviceNotifyDao;
    }

    public void setServiceNotifyDao(ServiceNotifyDao serviceNotifyDao) {
        this.serviceNotifyDao = serviceNotifyDao;
    }
}
