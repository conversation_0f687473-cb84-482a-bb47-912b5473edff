<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="/common/taglibs.jsp" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>顺网通行证-实名认证</title>
</head>


<body>
<jsp:include page="/front/actu/actu_refuse_context.jsp"></jsp:include>
    <div class="shop_head">
        <strong>实名认证</strong>
        如需在顺网结算中心申请提现，请先通过实名认证。
    </div>
    <ul class="step_bar">
        <li><em>1</em> 填写个人信息</li>
        <li class="current"><em>2</em> 确认信息</li>
        <li><em>3</em> 申请完成</li>
    </ul>
    <form action="${appServer }/front/actuality/personalActuInfo_commit_front.htm" method="post"
          onsubmit="return false;">
        <input type="hidden" value="${personalActuInfo.realName }" name="personalActuInfo.realName"/>
        <input type="hidden" value="${personalActuInfo.linkPhone }" name="personalActuInfo.linkPhone"/>
        <input type="hidden" value="${personalActuInfo.linkAddr }" name="personalActuInfo.linkAddr"/>
        <input type="hidden" value="${personalActuInfo.idCardNo }" name="personalActuInfo.idCardNo"/>
        <input type="hidden" value="${personalActuInfo.idCardEndtimeShow }" name="personalActuInfo.idCardEndtime"
               id="idCardEndtime"/>
        <input type="hidden" value="${personalActuInfo.idCardType }" name="personalActuInfo.idCardType"/>
        <input type="hidden" value="${personalActuInfo.idCardImg1 }" name="personalActuInfo.idCardImg1"/>
        <input type="hidden" value="${personalActuInfo.idCardImg2 }" name="personalActuInfo.idCardImg2"/>
        <input type="hidden" value="${personalActuInfo.bankUser }" name="personalActuInfo.bankUser"/>
        <input type="hidden" value="${actuInfo.bankName }" name="personalActuInfo.bankName"/>
        <input type="hidden" value="${actuInfo.provinceName }" name="personalActuInfo.provinceName"/>
        <input type="hidden" value="${actuInfo.cityName }" name="personalActuInfo.cityName"/>
        <input type="hidden" value="${actuInfo.bankBranchName }" name="personalActuInfo.bankBranchName"/>
        <input type="hidden" value="${actuInfo.bankNo }" name="personalActuInfo.bankNo"/>
        <input type="hidden" value="${changeSign }" name="changeSign"/>
        <input name="refuseContext" value="${refuseContext }" type="hidden"/>
        <input type="hidden" name="personalActuInfo.idCardNoIsFull" value="${personalActuInfo.idCardNoIsFull}"/>
        <input type="hidden" name="personalActuInfo.areaId" value="${personalActuInfo.areaId}"/>
        <input type="hidden" name="flag" value="1"/>

        <div class="info_table">


            <table cellpadding="0" cellspacing="0">
                <tr>
                    <th>姓名：</th>
                    <td>${personalActuInfo.realName }&nbsp;</td>
                </tr>
                <tr>
                    <th>身份证号码：</th>
                    <td>${personalActuInfo.idCardNoShow }&nbsp;</td>
                </tr>
                <tr>
                    <th>身份证类型：</th>
                    <td>${personalActuInfo.idCardTypeShow }&nbsp;</td>
                </tr>
                <tr>
                    <th>身份证正面照：</th>
                    <td>${personalActuInfo.existIdCardImg1?'已上传':'未上传' } <a class="a035"
                                                                            onmouseover="showSubmit(event,'${personalActuInfo.idCardImg1Show }');"
                                                                            onmouseout="hide();">查看</a>
                        <div class="form_preview">
                            <%@ include file="/front/common/imageCommon.jsp" %>
                        </div>
                    </td>
                </tr>
                <c:if test="${personalActuInfo.idCardType ==2}">
                    <tr>
                        <th>身份证反面照：</th>
                        <td>${personalActuInfo.existIdCardImg2?'已上传':'未上传'  } <a class="a035"
                                                                                 onmouseover="showSubmit(event,'${personalActuInfo.idCardImg2Show }');"
                                                                                 onmouseout="hide();">查看</a></td>
                    </tr>
                </c:if>
                <tr>
                    <th>身份证到期时间：</th>
                    <td><c:choose>
                        <c:when test="${personalActuInfo.idCardEndtimeShow == '1970-01-01'}">长期有效</c:when>
                        <c:otherwise>${personalActuInfo.idCardEndtimeShow }&nbsp;</c:otherwise>
                    </c:choose></td>
                </tr>
                <tr>
                    <th>手机号码：</th>
                    <td>${personalActuInfo.linkPhone }&nbsp;</td>
                </tr>
                <tr>
                    <th>联系地址：</th>
                    <td>${personalActuInfo.provArea.name}-${personalActuInfo.cityArea.name}-${personalActuInfo.distArea.name}-${personalActuInfo.linkAddr }&nbsp;</td>
                </tr>
            </table>
            <div class="btn_group">
                <a href="###" onclick="return doSubmit()"  class="btn_default_lg">确认提交</a>
                <a  id="button_20111011201012" class="btn_cancel_lg" href="javascript:resetSubmit('msg');">返回修改</a>
            </div>
        </div>
    </form>

<script type="text/javascript">
    $('.sidebar .menu li:first').addClass('current');
</script>
<script>
    function resetSubmit(type) {
        var obj = document.forms[0];
        var url = '${$CONFIG.appServer }/front/actuality/goToPersonalEdit.htm';
        obj.action = url;
        obj.submit();
    }
    function doSubmit() {
        document.forms[0].submit();
        document.getElementById("button_20111011201012").disabled = true;
    }
</script>
</body>
</html>
