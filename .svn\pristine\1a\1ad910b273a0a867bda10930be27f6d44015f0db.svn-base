package com.shunwang.basepassport.manager.service.bo.impl;

import com.shunwang.basepassport.manager.request.netbar.ActuInfoChangeNoticeRequest;
import com.shunwang.basepassport.manager.request.netbar.ActuInfoNoticeRequest;
import com.shunwang.basepassport.manager.service.bo.NetbarInterfaceBo;
import com.shunwang.basepassport.manager.service.netbar.NetbarServiceClient;

public class NetbarInterfaceImpl implements NetbarInterfaceBo {
    @Override
    public void doCafeActuNotice(String memberName, String msg) {
        ActuInfoNoticeRequest request = new ActuInfoNoticeRequest();
        request.setUserName(memberName);
        request.setMsg(msg);
        NetbarServiceClient.execute(request);
    }

    @Override
    public void doCafeActuChangeNotice(String memberName, String msg) {
        ActuInfoChangeNoticeRequest request = new ActuInfoChangeNoticeRequest();
        request.setUserName(memberName);
        request.setMsg(msg);
        NetbarServiceClient.execute(request);
    }
}
