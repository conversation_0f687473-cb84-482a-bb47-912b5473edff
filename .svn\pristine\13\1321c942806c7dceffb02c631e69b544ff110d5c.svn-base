package com.shunwang.basepassport.manager.service.oa;

import com.shunwang.basepassport.config.dao.ConfigInterfaceDao;
import com.shunwang.basepassport.config.pojo.ConfigInterface;
import com.shunwang.basepassport.manager.bean.RiskParam;
import com.shunwang.basepassport.manager.request.oa.GetUserRequest;
import com.shunwang.basepassport.manager.request.risk.RiskRequest;
import com.shunwang.basepassport.manager.response.oa.OaResponse;
import com.shunwang.basepassport.manager.response.risk.RiskResponse;
import com.shunwang.util.net.HttpClientUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Constructor;
import java.util.Map;

public class OaServiceClient {
    private static final Logger LOGGER = LoggerFactory.getLogger(OaServiceClient.class);

    private static ConfigInterfaceDao configInterfaceDao;

    public void setConfigInterfaceDao(ConfigInterfaceDao configInterfaceDao) {
        OaServiceClient.configInterfaceDao = configInterfaceDao;
    }

    public static OaResponse execute(GetUserRequest request) {
        try {
            ConfigInterface setting = configInterfaceDao.findByInterfaceKey(request.getInterfaceKey());
            request.doInterfaceSetting(setting);
            String url = request.buildUrl();
            Map<String, String> requestParams = request.buildParams();
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("----------------------------------------");
                LOGGER.debug("url:{}", url);
                LOGGER.debug("method:{}", request.getHttpMethod());
                LOGGER.debug("body:{}", request.toString());
                LOGGER.debug("----------------------------------------");
            }

            String response  = HttpClientUtils.doGet(url, requestParams);

            Class<OaResponse> responseClass = request.getResponseClass();
            Constructor constructor = responseClass.getConstructor();
            OaResponse resp = (OaResponse) constructor.newInstance();
            resp.setJson(response);
            return resp.parse();
        } catch (Exception e) {
            LOGGER.error("请求OA平台异常[{}]", e.getMessage());
        }
        //异常是返回null,不影响主流程
        return null;
    }

    public static void main(String[] args) {
        GetUserRequest request = new GetUserRequest();
        request.setEmail("lj.zeng");

        OaResponse response = OaServiceClient.execute(request);

        if (response.isSuccess()) {
            System.out.println("success");
        }
    }

}
