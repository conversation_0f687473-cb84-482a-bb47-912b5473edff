package com.shunwang.basepassport.weixin.service;

import com.shunwang.baseStone.cache.CacheKeyConstant;
import com.shunwang.baseStone.cache.lock.TimedLock;
import com.shunwang.baseStone.cache.service.CacheService;
import com.shunwang.baseStone.context.RedisContext;
import com.shunwang.basepassport.asynctask.SendTemplateMsgAsyncTaskExecutor;
import com.shunwang.basepassport.config.dao.AppidReplyDao;
import com.shunwang.basepassport.config.dao.ConfigResourcesDao;
import com.shunwang.basepassport.config.dao.WxTemplateMsgDao;
import com.shunwang.basepassport.config.enums.AppidReplyEnum;
import com.shunwang.basepassport.config.pojo.AppidReply;
import com.shunwang.basepassport.config.pojo.WxTemplateMsg;
import com.shunwang.basepassport.user.dao.MemberAccountBindDao;
import com.shunwang.basepassport.user.dao.MemberOutSiteDao;
import com.shunwang.basepassport.user.pojo.MemberAccountBind;
import com.shunwang.basepassport.user.pojo.MemberOutSite;
import com.shunwang.basepassport.weixin.constant.BarLoginEnum;
import com.shunwang.basepassport.weixin.constant.WeixinConstant;
import com.shunwang.basepassport.weixin.pojo.BarLoginReportInfo;
import com.shunwang.basepassport.weixin.pojo.WeixinOauthToken;
import com.shunwang.basepassport.weixin.pojo.WeixinOpenIdUnionId;
import com.shunwang.basepassport.weixin.pojo.WeixinTemplateMsg;
import com.shunwang.basepassport.weixin.task.SendTemplateMsgJob;
import com.shunwang.util.StringUtil;
import com.shunwang.util.date.DateUtil;
import com.shunwang.util.json.GsonUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Base64Util;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;

import java.time.Duration;
import java.util.Date;
import java.util.Map;
import java.util.Objects;
import java.util.Random;
import java.util.concurrent.Future;

public class WeixinMsgService {
    protected final Logger log = LoggerFactory.getLogger(WeixinMsgService.class);
    private static final Logger reportLogger = LoggerFactory.getLogger("com.shunwang.baseStone.sso.netBarReportLog");

    private WeixinOauthTokenService weixinOauthTokenService;
    private AppidReplyDao          appidReplyDao;
    private MemberAccountBindDao memberAccountBindDao;
    private WeixinOpenIdUnionIdService weixinOpenIdUnionIdService;
    private MemberOutSiteDao memberOutSiteDao;
    private WxTemplateMsgDao wxTemplateMsgDao;

    private final Random random = new Random();
    public final String send_templateMsgUrl_redis_key = "send_templateMsg_url_redis_key";

    public boolean sendAutoLoginTemplateMsg(Integer memberId, String memberName) {
        try {
            TimedLock timedLock = CacheService.newTimedLock("auto_login_send_wx_msg_lock_" + memberId, Duration.ofSeconds(10));
            if (!timedLock.tryLock()) {
                log.warn("10s内已发送，本次不发送微信模版消息");
                return false;
            }
            MemberAccountBind accountBind = memberAccountBindDao.getByMemberId(memberId);
            if (accountBind == null || accountBind.getWeixin() == null) {
                log.error("[{}]未绑定微信帐号，不发送模版消息", memberId);
                return false;
            }
            MemberOutSite memberOutSite = memberOutSiteDao.getMemberByMemberId(accountBind.getWeixin());
            if (memberOutSite == null) {
                log.error("[{}]微信数据异常，不发送模版消息", memberId);
                return false;
            }
            String appId = RedisContext.getResourceCache().getResourceValue(CacheKeyConstant.ConfigResourcesConstants.NET_BAR_FREE_LOGIN_CONFIG,
                    CacheKeyConstant.ConfigResourcesConstants.DEFAULT_APPID);
            if (StringUtil.isBlank(appId)) {
                log.error("[{}]默认公众号未配置，不发送模版消息", memberId);
                return false;
            }
            WeixinOpenIdUnionId appIdAndUnionId = weixinOpenIdUnionIdService.getByAppIdAndUnionId(appId, memberOutSite.getOutMemberId());
            if (appIdAndUnionId == null) {
                log.error("[{}]微信关注数据异常，不发送模版消息", memberId);
                return false;
            }
            Integer templateMsgId = RedisContext.getIntConfig(CacheKeyConstant.ConfigResourcesConstants.NET_BAR_FREE_LOGIN_CONFIG,
                    CacheKeyConstant.ConfigResourcesConstants.AUTO_LOGIN_TEMPLATE_MSG_ID);
            WxTemplateMsg wxTemplateMsg = wxTemplateMsgDao.getById(templateMsgId);
            if (wxTemplateMsg == null) {
                log.error("[{}]微信模版消息为配置，不发送模版消息", memberId);
                return false;
            }

            WeixinTemplateMsg weixinTemplateMsg = new WeixinTemplateMsg();
            BeanUtils.copyProperties(wxTemplateMsg, weixinTemplateMsg);
            weixinTemplateMsg.setAppid(appId);
            weixinTemplateMsg.setOpenId(appIdAndUnionId.getOpenId());
//            weixinTemplateMsg.setKeyword1("");
            weixinTemplateMsg.setKeyword2(memberName);
            sendTemplateMsg(weixinTemplateMsg);
            return true;
        } catch (Exception e) {
            //仅记录异常，不影响登录跳转
            log.error("发送模版消息异常", e);
        }
        return false;
    }

    public Future<String> sendTemplateMsg(WeixinTemplateMsg msg) {
        if (msg == null){
            log.warn("消息为空");
            return null;
        }
        String resourceValue = RedisContext.getResourceCache().getResourceValue("wxTemplateMsgUrlConfig", "wxTemplateMsgUrl");

        WeixinOauthToken weixinOauthToken = weixinOauthTokenService.getByAppIdAndType(msg.getAppid(), WeixinConstant.TYPE.AUTHORIZER);
        if (weixinOauthToken == null) {
            log.error("appId{}对应的token不存在", msg.getAppid());
            return null;
        }
        Future<String> result = SendTemplateMsgAsyncTaskExecutor.submit(new SendTemplateMsgJob(weixinOauthToken.getAccessToken(),
                resourceValue, msg));
        return result;
    }

    /**
     * 构建微信上机成功通知
     */
    public AppidReply buildWxBarLoginNotice(BarLoginReportInfo reportInfo, String appId, String memberName, String tailNo) {
        AppidReply appidReply = getNetBarReply(appId, BarLoginEnum.ModeEnum.NET_BAR.getValue(), BarLoginEnum.BindStateEnum.NO_BIND_MSG.getValue(), reportInfo);
        String value = appidReply.getReplyMsg();
        if (StringUtil.isNotBlank(value)) {
            value = value.replace("$memberName$", memberName).replace("$idCardNo$", "****" + tailNo).replace("\\\\n", "\n");
        } else {
            value = "微信扫码成功，请到电脑端上机！（10秒还未成功请联系网管或证件号开机）";
        }
        appidReply.setReplyMsg( value );
        return appidReply;
    }

    /**
     * 构建微信绑身份证通知
     */
    public AppidReply buildWxBarBindNotice(BarLoginReportInfo reportInfo, String appId, String eventKey, String barid) {
        AppidReply appidReply = getNetBarReply(appId, BarLoginEnum.ModeEnum.NET_BAR.getValue(), BarLoginEnum.BindStateEnum.BIND_MSG.getValue(), reportInfo);
        String value = appidReply.getReplyMsg();
        String bindUrl = "https://sso.kedou.com/wxBindIdCard/goBind.do?eventKey=" + eventKey + "&barid=" + barid;
        if (StringUtil.isNotBlank(value)) {
            value = value.replace("$bindUrl$", bindUrl).replace("\\\\n", "\n");
        } else {
            value = "主人，您未绑定身份证，戳 <a href='" +bindUrl+ "'>这里去绑定吧~</a>";
        }
        appidReply.setReplyMsg( value );
        return appidReply;
    }

    /**
     * 构建电竞酒店上机消息
     * @param reportInfo 数据上报对象
     * @param scene 计费认证场景值
     */
    public AppidReply buildWxHotelLoginNotice(BarLoginReportInfo reportInfo, String scene, String appId, String clientTicket) {
        AppidReply appidReply = getNetBarReply(appId, BarLoginEnum.ModeEnum.E_SPORT_HOTEL.getValue(), BarLoginEnum.BindStateEnum.BIND_MSG.getValue(), reportInfo);
        String value = appidReply.getReplyMsg();
        String url = getHotelOauthUrl();
        //认证地址包含计费场景值scene（处理链接失效等）及免登票据token（用于写入客户端免登插件）
        url = url.replace("$scene$", scene).replace("$token$", clientTicket);
        if (StringUtil.isNotBlank(value)) {
            value = value.replace("$url$", url).replace("\\\\n", "\n");
        } else {
            value = "依据国家政策要求，您需完成相关认证才能上机，<a href='"+url+"'>马上认证~</a>";
        }
        appidReply.setReplyMsg( value );
        return appidReply;
    }

    /**
     * 查找微信回复消息
     * @param appid 回调的微信公众号
     * @param bindState 需要回复的绑定状态
     * @param reportInfo  上报数据对象，需要记录返回的消息id
     */
    public AppidReply getNetBarReply(String appid, Integer mode, Integer bindState, BarLoginReportInfo reportInfo) {
        Integer[] list = appidReplyDao.calculate(appid, mode, bindState);
        reportInfo.setExtInfo("0");
        if (list == null || list.length == 0) {
            reportLogger.info(GsonUtil.toJson(reportInfo));
            return new AppidReply(getNetBarDefaultReply(mode, bindState), AppidReplyEnum.ContentType.WORD.getValue());
        }
        Integer id = list[random.nextInt(list.length)];
        AppidReply reply = appidReplyDao.getById(id);
        if (reply == null) {
            reportLogger.info(GsonUtil.toJson(reportInfo));
            return new AppidReply(getNetBarDefaultReply(mode, bindState), AppidReplyEnum.ContentType.WORD.getValue());
        }
        reportInfo.setExtInfo(reply.getId() + "");
        reportLogger.info(GsonUtil.toJson(reportInfo));
        return reply;
    }

    /**
     * 获取默认回复消息
     * @param mode 当前上机类型 1网吧  2电竞酒店
     * @param bindState 是否绑定身份证 用于网吧  电竞酒店暂时不需要
     */
    public String getNetBarDefaultReply(Integer mode, Integer bindState) {
        if (Objects.equals(BarLoginEnum.ModeEnum.NET_BAR.getValue(), mode)) {
            return  RedisContext.getResourceCache().getResourceValue(
                    CacheKeyConstant.ConfigResourcesConstants.BAR_LOGIN_CONFIG, BarLoginEnum.BindStateEnum.BIND_MSG.getValue() == bindState
                            ? CacheKeyConstant.ConfigResourcesConstants.BAR_LOGIN_WEIXIN_MSG_BIND :
                            CacheKeyConstant.ConfigResourcesConstants.BAR_LOGIN_WEIXIN_MSG_SUCCESS);
        }
        return  RedisContext.getResourceCache().getResourceValue(
                CacheKeyConstant.ConfigResourcesConstants.HOTEL_LOGIN_CONFIG,
                CacheKeyConstant.ConfigResourcesConstants.HOTEL_LOGIN_WEIXIN_MSG_SUCCESS);
    }

    /**
     * 构建微信上机免登通知
     */
    public String buildWxBarFreeLoginNotice(String appId, String memberName, Integer bindState, String scene) {
        String value = getWxReplayMsg(appId, BarLoginEnum.ModeEnum.NET_BAR_FREE_LOGIN.getValue(), bindState);
        if (StringUtil.isNotBlank(value)){
            return value.replace("$account$", memberName)
                    .replace( "$time$", DateUtil.ymdhmsFormat(new Date()))
                    .replace("$scene$", scene);
        }
        return "扫码成功,马上就好。";
    }

    /**
     * 读取电竞酒店上机认证地址
     */
    private String getHotelOauthUrl() {
        String value = RedisContext.getResourceCache().getResourceValue(CacheKeyConstant.ConfigResourcesConstants.HOTEL_LOGIN_CONFIG,
                CacheKeyConstant.ConfigResourcesConstants.HOTEL_LOGIN_OAUTH_URL);
        return StringUtil.isNotBlank(value) ? value : "https://eshotel.54laoban.cn/hotel-api/wechat/index?scene=$scene$&token=$token$";
    }

    /**
     * 读取扫码上机发送绑定身份证小程序消息appid
     */
    public String getWxBindIdCardMsgSwitch() {
        String value = RedisContext.getResourceCache().getResourceValue(CacheKeyConstant.ConfigResourcesConstants.BAR_LOGIN_CONFIG,
                CacheKeyConstant.ConfigResourcesConstants.SSO_BIND_IDCARD_WX_MSG_IS_OPEN);
        return StringUtil.isNotBlank(value) ? value : "";
    }

    /**
     * 单账号微信登录绑定手机，回复的微信消息
     */
    public String getWxLoginSingleSuccMsg(String appId){
        String value = getWxReplayMsg(appId, BarLoginEnum.ModeEnum.SCANNING_CODE.getValue(), BarLoginEnum.BindStateEnum.BIND_MOBILE.getValue());
        if (StringUtil.isBlank(value)) {
            value = RedisContext.getResourceCache().getResourceValue(CacheKeyConstant.ConfigResourcesConstants.WEIXIN_LOGIN_REPLY_MSG, CacheKeyConstant.ConfigResourcesConstants.WEIXIN_LOGIN_REPLY_MSG_SINGLE);
        }
        if (StringUtil.isNotBlank(value)){
            return value;
        }
        return "微信授权成功，请继续绑定手机号！";
    }

    /**
     * 多账号微信登录成功回复的微信消息
     */
    public String getWxLoginMultipleSuccMsg(String appId, String memberName){
        String value = getWxReplayMsg(appId, BarLoginEnum.ModeEnum.SCANNING_CODE.getValue(), BarLoginEnum.BindStateEnum.LOGIN_SUCC.getValue());
        if (StringUtil.isBlank(value)) {
            value = RedisContext.getResourceCache().getResourceValue(CacheKeyConstant.ConfigResourcesConstants.WEIXIN_LOGIN_REPLY_MSG, CacheKeyConstant.ConfigResourcesConstants.WEIXIN_LOGIN_REPLY_MSG_MULTIPLE);
        }
        if (StringUtil.isNotBlank(value)){
            return value.replace("$account$", memberName).replace( "$time$", DateUtil.ymdhmsFormat( new Date() ) ).replace( "$memberName$",
                    Base64Util.encode(memberName) );
        }
        return buildWxNotice(memberName);
    }

    /**
     * 构建业务扫码通知
     */
    public String getWxBusinessScanReplayMsg(String appId, String memberName) {
        String value = getWxReplayMsg(appId, BarLoginEnum.ModeEnum.SCANNING_NO_LOGIN_CODE.getValue(), BarLoginEnum.BindStateEnum.BUSINESS_SCAN.getValue());
        if (StringUtil.isBlank(value)) {
            value = RedisContext.getResourceCache().getResourceValue(CacheKeyConstant.ConfigResourcesConstants.WEIXIN_LOGIN_REPLY_MSG, CacheKeyConstant.ConfigResourcesConstants.WEIXIN_BUSINESS_SACAN_REPLAY_MSG);
        }
        if (StringUtil.isNotBlank(value)){
            return value.replace("$account$", memberName).replace( "$time$", DateUtil.ymdhmsFormat( new Date() ) ).replace( "$memberName$",
                    Base64Util.encode(memberName) );
        }
        return "扫码成功，业务处理中，马上就好。";
    }

    /**
     * 单账号微信登录,去小程序内绑定手机，回复的微信消息
     */
    public String getWxLoginToBindMobileMsg(String appId, Integer bindState, String scene){
        String value = getWxReplayMsg(appId, BarLoginEnum.ModeEnum.SCANNING_CODE.getValue(), bindState);
        if (StringUtil.isBlank(value)) {
            value = RedisContext.getResourceCache().getResourceValue(CacheKeyConstant.ConfigResourcesConstants.WEIXIN_LOGIN_REPLY_MSG, CacheKeyConstant.ConfigResourcesConstants.WEIXIN_LOGIN_REPLY_MSG_SINGLE);
        }
        if (StringUtil.isNotBlank(value)){
            return value.replace("$scene$", scene);
        }
        return "success";
    }

    /**
     * 计算获取微信回复消息
     */
    public String getWxReplayMsg(String appId, Integer mode, Integer state){
        //查询配置的回复消息
        Integer[] list = appidReplyDao.calculate(appId, mode, state);
        //如果不为空，则读配置
        if (list != null){
            Integer id = list.length == 1 ? 1 : list[random.nextInt(list.length)];
            AppidReply reply = appidReplyDao.getById(id);
            if (reply != null){
                return reply.getReplyMsg();
            }
        }
        return StringUtils.EMPTY;
    }
    /**
     * 构建微信登录成功通知
     */
    public String buildWxNotice(String memberName) {
        return "登录成功通知" + "\n\n" +
                "登录帐号：\n" + memberName + "\n" +
                "登录时间：\n" + DateUtil.getCurrentDateStr() + "\n\n" +
                "您已登录成功，欢迎使用！";
    }

    public boolean sendPollWxMsgIsOpen(String appId){
        Map<String, String> paramMap = RedisContext.getStringMapConfig(CacheKeyConstant.ConfigResourcesConstants.BAR_LOGIN_CONFIG,
                CacheKeyConstant.ConfigResourcesConstants.SEND_POLL_WX_MSG_IS_OPEN);
        String value = paramMap.get(appId);

        return StringUtil.isNotBlank(value) && value.equalsIgnoreCase("y");
    }

    public void setWeixinOauthTokenService(WeixinOauthTokenService weixinOauthTokenService) {
        this.weixinOauthTokenService = weixinOauthTokenService;
    }

    public AppidReplyDao getAppidReplyDao() {
        return appidReplyDao;
    }

    public void setAppidReplyDao(AppidReplyDao appidReplyDao) {
        this.appidReplyDao = appidReplyDao;
    }

    public MemberAccountBindDao getMemberAccountBindDao() {
        return memberAccountBindDao;
    }

    public void setMemberAccountBindDao(MemberAccountBindDao memberAccountBindDao) {
        this.memberAccountBindDao = memberAccountBindDao;
    }

    public WeixinOauthTokenService getWeixinOauthTokenService() {
        return weixinOauthTokenService;
    }

    public WeixinOpenIdUnionIdService getWeixinOpenIdUnionIdService() {
        return weixinOpenIdUnionIdService;
    }

    public void setWeixinOpenIdUnionIdService(WeixinOpenIdUnionIdService weixinOpenIdUnionIdService) {
        this.weixinOpenIdUnionIdService = weixinOpenIdUnionIdService;
    }

    public MemberOutSiteDao getMemberOutSiteDao() {
        return memberOutSiteDao;
    }

    public void setMemberOutSiteDao(MemberOutSiteDao memberOutSiteDao) {
        this.memberOutSiteDao = memberOutSiteDao;
    }

    public WxTemplateMsgDao getWxTemplateMsgDao() {
        return wxTemplateMsgDao;
    }

    public void setWxTemplateMsgDao(WxTemplateMsgDao wxTemplateMsgDao) {
        this.wxTemplateMsgDao = wxTemplateMsgDao;
    }
}
