package com.shunwang.baseStone.sso.weixin.task;


import com.shunwang.baseStone.sso.util.HttpClientUtil;
import com.shunwang.baseStone.sso.weixin.pojo.WeixinTemplateMsgData;
import com.shunwang.basepassport.config.pojo.WxTemplateMsg;
import com.shunwang.util.date.DateUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;

public class BindTemplateMsgJob implements Runnable{

    private static final Logger logger = LoggerFactory.getLogger( BindTemplateMsgJob.class);

    private String accessToken;
    private String openId;
    private WxTemplateMsg wxTemplateMsg;
    private String sendUrl;
    private String idCardShow;

    public BindTemplateMsgJob(String accessToken, String openId,String idCardShow, String sendUrl, WxTemplateMsg wxTemplateMsg) {
        this.accessToken = accessToken;
        this.openId = openId;
        this.sendUrl = sendUrl;
        this.wxTemplateMsg = wxTemplateMsg;
        this.idCardShow = idCardShow.replace(idCardShow.substring(3,14),"***");
    }

    @Override
    public void run() {
        try {
            logger.info( "开始发送微信绑定成功模版消息，openId：[{}]", openId);
            String bindTime = DateUtil.ymdhmsFormat( new Date() );
            if (wxTemplateMsg.getDelayedTime() != null && wxTemplateMsg.getDelayedTime() > 0) {
                Thread.sleep( wxTemplateMsg.getDelayedTime() * 1000 );
            }
            String jsonStr = WeixinTemplateMsgData.New().setTouser( openId )
                    .setTemplate_id( wxTemplateMsg.getTemplateId() )
                    .setUrl( wxTemplateMsg.getUrl() )
                    .add( "first", wxTemplateMsg.getFirst(), wxTemplateMsg.getFirstColor() )
                    .add( "keyword1", replaceIdCard(wxTemplateMsg.getKeyword1(), idCardShow), wxTemplateMsg.getKeyword1Color() )
                    .add( "keyword2", bindTime, wxTemplateMsg.getKeyword2Color() )
                    .add( "keyword3", wxTemplateMsg.getKeyword3(), wxTemplateMsg.getKeyword3Color() )
                    .add( "remark", wxTemplateMsg.getRemark(), wxTemplateMsg.getRemarkColor() )
                    .build();

            logger.info( "发送微信绑定成功模版消息参数：[{}]", jsonStr);
            String resultMsg = HttpClientUtil.sendPostJsonStr( sendUrl + accessToken, jsonStr );
            logger.info( "发送微信绑定成功模版消息结果：[{}]", resultMsg);
        } catch (Exception e) {
            logger.error( "发送微信绑定成功模版消息线程异常,openid = [{}]", openId);
            logger.error( "发送微信绑定成功模版消息线程异常 [{}]", e.getMessage());
        }

    }

    private String replaceIdCard(String str, String content){
        String reg = "[$][\\D]{0,8}[$]";
        return str.replaceAll(reg,content);
    }

}
