package com.shunwang.basepassport.actu.service;

import com.shunwang.basepassport.actu.pojo.PersonalActuVerifyRecord;
import com.shunwang.basepassport.user.pojo.Member;

public interface IdCardVerifyBo {

    //2、查询数据库方法
    //3、调用三方接口方法
    //4、写记录表方法 save or update
    //5、写记录表和关联表方法 save or update

    /**
     * 1、判断开关和缓存锁
     *
     * @param member
     * @param idCard
     * @return
     */
    boolean needVerifyIdCard(Member member, String idCard);

    /**
     * 2、查询数据库记录
     *
     * @param idCard
     * @param realName
     * @return
     */
    PersonalActuVerifyRecord getRecord(String idCard);

    /**
     * 3、调用三方接口获取匹配结果
     *
     * @param idCard
     * @param realName
     * @return
     */
    PersonalActuVerifyRecord getRecordRemote(String idCard, String realName);

    /**
     * 2.5 调用大数据接口
     * 大数据接口现在已经不能使用
     * @param idCard
     * @param realName
     * @param barId
     * @return
     */
    @Deprecated
    PersonalActuVerifyRecord getRecordRemote(String idCard, String realName, String barId);

    /**
     * 4、保存记录
     *
     * @param record
     * @return
     */
    void saveOrUpdate(PersonalActuVerifyRecord record);


    PersonalActuVerifyRecord getByMemberId(Integer memberId);

}
