package com.shunwang.basepassport.actu.pojo;

import com.google.gson.annotations.Expose;
import com.shunwang.baseStone.core.pojo.BaseStoneObject;
import com.shunwang.xmlbean.annotation.XmlInit;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description:实名认证上报
 * <AUTHOR>
@XmlInit
public class PersonalActuReport extends BaseStoneObject {

	@Expose
	private Integer memberId;
	@Expose
	private String memberName;
	/**
	 * 实名认证进件上报类型 1重新进件 2更新进件 4更新进件银行卡
	 */
	@Expose
	private Integer type;
	/**
	 * 上报状态 1待上报 2已上报
	 */
	private Integer state;
	private Date timeAdd;
	private Date timeEdit;
	private String remark;
	private Integer dataFrom;

	/**
	 * 实名表、银行卡表主键id
	 */
	@Expose
	private Integer identifier;

	@XmlInit
	public Integer getMemberId() {
		return memberId;
	}

	public void setMemberId(Integer memberId) {
		this.memberId = memberId;
	}
	@XmlInit
	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}

	public Date getTimeAdd() {
		return timeAdd;
	}

	public void setTimeAdd(Date timeAdd) {
		this.timeAdd = timeAdd;
	}
	public Date getTimeEdit() {
		return timeEdit;
	}

	public void setTimeEdit(Date timeEdit) {
		this.timeEdit = timeEdit;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getMemberName() {
		return memberName;
	}

	public void setMemberName(String memberName) {
		this.memberName = memberName;
	}

	public Integer getDataFrom() {
		return dataFrom;
	}

	public void setDataFrom(Integer dataFrom) {
		this.dataFrom = dataFrom;
	}

	@XmlInit
	public Integer getIdentifier() {
		return identifier;
	}

	public void setIdentifier(Integer identifier) {
		this.identifier = identifier;
	}

	@Override
	public Serializable getPk() {
		return null;
	}
}
