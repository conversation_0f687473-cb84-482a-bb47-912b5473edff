package com.shunwang.basepassport.weixin.pojo;

import com.shunwang.baseStone.core.pojo.BaseStoneObject;

import java.io.Serializable;
import java.util.Date;

/**
 * @author: lj.zeng
 * @create: 2025-06-16 11:03:51
 * @Description:
 */
public class WxWorkUserBind extends BaseStoneObject {

    private Integer id;
    private String externalId;
    private Integer type;
    private String scene;
    private Integer memberId;
    private Date timeAdd;
    private Date timeEdit;
    private String remark;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getExternalId() {
        return externalId;
    }

    public void setExternalId(String externalId) {
        this.externalId = externalId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getScene() {
        return scene;
    }

    public void setScene(String scene) {
        this.scene = scene;
    }

    public Integer getMemberId() {
        return memberId;
    }

    public void setMemberId(Integer memberId) {
        this.memberId = memberId;
    }

    public Date getTimeAdd() {
        return timeAdd;
    }

    public void setTimeAdd(Date timeAdd) {
        this.timeAdd = timeAdd;
    }

    public Date getTimeEdit() {
        return timeEdit;
    }

    public void setTimeEdit(Date timeEdit) {
        this.timeEdit = timeEdit;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Override
    public Serializable getPk() {
        return id;
    }
}
