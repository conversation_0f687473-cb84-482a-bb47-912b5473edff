package com.shunwang.basepassport.manager.service.geetest;

import com.shunwang.baseStone.core.exception.BaseStoneException;
import com.shunwang.basepassport.binder.common.ErrorCode;
import com.shunwang.basepassport.config.dao.ConfigInterfaceDao;
import com.shunwang.basepassport.config.dao.ConfigOneLoginDao;
import com.shunwang.basepassport.config.enums.OneLoginEnum;
import com.shunwang.basepassport.config.pojo.ConfigInterface;
import com.shunwang.basepassport.config.pojo.ConfigOneLogin;
import com.shunwang.basepassport.manager.request.geetest.CheckMobileH5Request;
import com.shunwang.basepassport.manager.request.geetest.CheckMobileRequest;
import com.shunwang.basepassport.manager.response.geetest.CheckMobileH5Response;
import com.shunwang.basepassport.manager.response.geetest.CheckMobileResponse;
import com.shunwang.util.net.HttpClientUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Constructor;
import java.nio.charset.StandardCharsets;
import java.util.Map;

public class CheckMobileH5ServiceClient {
    private static final Logger LOGGER = LoggerFactory.getLogger(CheckMobileH5ServiceClient.class);

    private static ConfigInterfaceDao configInterfaceDao;
    private static ConfigOneLoginDao configOneLoginDao;
    public void setConfigOneLoginDao(ConfigOneLoginDao configOneLoginDao) {
        CheckMobileH5ServiceClient.configOneLoginDao = configOneLoginDao;
    }
    public void setConfigInterfaceDao(ConfigInterfaceDao configInterfaceDao) {
        CheckMobileH5ServiceClient.configInterfaceDao = configInterfaceDao;
    }

    public static CheckMobileH5Response execute(CheckMobileH5Request request, String siteId) {
        try {
            ConfigInterface setting = configInterfaceDao.findByInterfaceKey(request.getInterfaceKey());
            request.doInterfaceSetting(setting);
            resetRequest(request,  siteId);
            String url = request.buildUrl();
            Map<String, String> requestParams = request.buildParams();
            Map<String, String> headers = request.getHeaders();
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("----------------------------------------");
                LOGGER.debug("url:{}", url);
                LOGGER.debug("method:{}", request.getHttpMethod());
                LOGGER.debug("requestParams:{}", requestParams);
                LOGGER.debug("headers:{}", headers);
                LOGGER.debug("----------------------------------------");
            }
            String response = new String(HttpClientUtils.doPost(url, requestParams, headers, null, StandardCharsets.UTF_8));

            Class<CheckMobileH5Response> responseClass = request.getResponseClass();
            Constructor constructor = responseClass.getConstructor();
            CheckMobileH5Response resp = (CheckMobileH5Response) constructor.newInstance();
            resp.setRawJson(response);
            return resp.parse();
        } catch (BaseStoneException e) {
            throw e;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private static void resetRequest(CheckMobileH5Request request,  String siteId) {
        ConfigOneLogin configOneLogin = configOneLoginDao.findBySiteIdAndTerminalType(siteId, OneLoginEnum.TerminalTypeEnum.H5.getValue());
        if (configOneLogin == null) {
            LOGGER.warn("一键登录渠道[{}]配置不存在，请联系管理员后台配置", siteId);
            throw new BaseStoneException(ErrorCode.C_1096);
        }
        request.setAppId(configOneLogin.getAppid());
        request.setAppKey(configOneLogin.getMd5Key());
    }

}
