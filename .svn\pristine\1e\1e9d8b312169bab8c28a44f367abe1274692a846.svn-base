// // TODO: 20250610 新版校验验证码，验证码输入逻辑

$(document).ready(function () {
  
  const $codeInputs = $(".code-input"); // 所有code输入框元素
  const $codeBackBtn = $(".verify-code-header>.back"); // 返回按钮元素
  const $codeTimerTips = $(".verify-code-content>.timer-tips"); // 倒计时文案元素
  const $codeErrorTips = $(".verify-code-content>.error-tips"); // 错误文案元素
  const $codeDescText = $(".verify-code-content>.desc>span"); // 描述手机号文案元素
  const $codeReSendBtn = $(".verify-code-bottom>button"); // 重新发送按钮元素

  // 限制只能输入数字
  $codeInputs.on("input", function () {
    this.value = this.value.replace(/[^0-9]/g, "");

    // 如果输入了数字且不是最后一个输入框，自动聚焦到下一个
    if (this.value.length === 1 && parseInt(this.dataset.index) < 6) {
      $(this).next(".code-input").focus();
    }
  });

  // 处理退格键
  $codeInputs.on("keydown", function (e) {
    if (
      e.key === "Backspace" &&
      this.value.length === 0 &&
      parseInt(this.dataset.index) > 1
    ) {
      $(this).prev(".code-input").focus();
    }

    if(
      e.key === "Backspace" &&
      this.value.length === 1 &&
      parseInt(this.dataset.index) === 6
    ) {
      initVerificationCode();
    }
  });

  // 粘贴处理（允许一次性粘贴6位数字）
  $codeInputs.on("paste", function (e) {
    e.preventDefault();
    const pasteData = e.originalEvent.clipboardData
      .getData("text/plain")
      .replace(/[^0-9]/g, "");

    if (pasteData.length === 6) {
      for (let i = 0; i < 6; i++) {
        $codeInputs.eq(i).val(pasteData[i]);
      }
      $codeInputs.last().focus();
      verificationCode();
    }
  });

  // 当所有输入框都有值时触发
  $codeInputs.last().on("input", function () {
    verificationCode();
  });

  // TODO: old verify code 点击重新获取验证码按钮，走老的逻辑就在sendAgain事件上加上一下处理逻辑 
  // 重新获取验证码函数
  $codeReSendBtn.on("click", function () {
    sendVerificationCode();
  });

  $codeBackBtn.on("click", function () {
    // 返回按钮逻辑
    console.log("返回按钮被点击");
    initVerificationCode();
    $(".login-verify-code").hide();
  });

  // 获取验证码函数
  function sendVerificationCode() {
    $codeReSendBtn.addClass("verify-disabled");
    wait = 60; // 重置倒计时
    time(function(wait) {
      if(wait === 0) {
        $codeReSendBtn.removeClass("verify-disabled");
        $codeInputs.each(function () {
          $(this).val("");
        });
        $codeTimerTips.text("");
      } else {
        $codeTimerTips.text(wait + "秒后可重新获取验证码");
      }
    });
  }
  
  // 获取完整输入验证码的函数
  function getVerificationCode() {
    let code = "";
    $codeInputs.each(function () {
      code += $(this).val();
    });
    return code;
  };

  // 校验验证码函数
  function verificationCode() {
    const code = getVerificationCode();
    if (code.length === 6) {
      console.log("验证码输入完成:", code);
      // 校验验证码
      var outMobileConfirmNew = $.ajax({
        url: "/outMobileConfirmNew.do", // 校验验证码 action
        data: {
          number: $phoneNumber.val(),
          site_id: $("#login-form").find("input[name='site_id']").val(),
          env: $("#login-form").find("input[name='env']").val(),
          callbackUrl: $("#login-form").find("input[name='callbackUrl']").val(),
          mobileActiveNo: code,
        },
        type: "post",
        cache: false,
        dataType: "json",
        success: function (data) {
          if (!data.result) {//校验失败
            showToastError(data.msg);
          } else {//校验成功
            window.location.href = data.redirectUrl;
          }
        },
        error: function (e) {
          showToastError("异常！");
        },
      });
      // if(code !== "123456") {
      //   $(".verify-code-content").addClass("verify-error");
      //   $codeErrorTips.text("验证码错误，请重新输入");
      //   $codeErrorTips.show();
      //   $codeTimerTips.hide();
      // } else {
      //   initVerificationCode();
      //   console.log("验证码验证成功,走提交逻辑跳转下一步");
      // }
    }
  };

  // 初始化code页面状态
  function initVerificationCode() {
    console.log("还原初始状态")
    $(".verify-code-content").removeClass("verify-error");
    $codeTimerTips.text("");
    $codeTimerTips.show();
    $codeErrorTips.hide();
  };


  const $phoneNumber = $("#phoneNumber"); // 所有手机输入框元素
  const $phoneBackBtn = $(".verify-phone-header>.back"); // 返回按钮元素
  const $phoneNextBtn = $("#verify-next"); // 下一步按钮元素
  const $phoneAuthorizationBtn = $("#mergeAuthorization"); // 合并授权按钮元素
  const $phoneAgreementBtn = $("#agreementCheckboxStep"); // 隐私协议复选框元素
  const $phonePrivaryCloseBtn = $("#privacyClose"); // 隐私协议弹窗关闭按钮元素
  const $phonePrivacyModal = $("#privacyModal"); // 隐私协议弹窗元素
  const $phoneErrorTips = $(".verify-phone-content>.error-tips"); // 错误文案元素

  $phoneBackBtn.on("click", function () {
    // 返回按钮逻辑
    $(".login-verify-phone").hide();
  });

  //发送验证码点击事件
  function sendAgain(callback) {
    if(!$phoneAgreementBtn.is(":checked")) {
      return false;
    }
    var geetest_challenge = $("#geetest_challenge").val();
    var geetest_validate = $("#geetest_validate").val();
    var geetest_seccode = $("#geetest_seccode").val();

    var number = $("#phoneNumber").val();
    if (!checkMobile(number)) {
      $("#phoneNumber").focus();
      return false;
    }

    hideTipsError();
    $("#sent-code").prop("disabled", true);
    var timestamp = Date.parse(new Date().toString());
    try {
      window.sessionStorage.setItem(CONSTANTS.SEND_ACTIVENO_TIME, timestamp);
      window.sessionStorage.setItem(CONSTANTS.HAS_SEND, true);
    } catch (e) {
      console.error(e);
    }
    $("#mobileCheckCode").removeAttr("disabled");

    var aj = $.ajax({
      url: "/sendActiveNo.do", // 发送验证码 action
      data: {
        number: number,
        site_id: $("#login-form").find("input[name='site_id']").val(),
        env: $("#login-form").find("input[name='env']").val(),
        geetest_challenge: geetest_challenge,
        geetest_validate: geetest_validate,
        geetest_seccode: geetest_seccode,
      },
      type: "post",
      cache: false,
      dataType: "json",
      success: function (data) {
        if (!data.result) {
          if (data.msg == "同一业务同一手机号码1分钟内只能发一次短信验证码！") {
            data.msg = "短信发送过于频繁，请稍后再试";
          }
          showToastError(data.msg);
          // showTipsError(data.msg);
          $("#sent-code").prop("disabled", false);
          return;
        } else {
          wait = 60;
          time($("#sent-code"));
        }

        //成功发送验证码，进入下一步页面
        nextStepVerification();

        callback && callback();
      },
      error: function (e) {
        showToastError("异常！");
        $("#sent-code").prop("disabled", true);
      },
    });
  }

  function nextPageCheck(){
    //检查手机号
    if(!numberInputCheck()){
      return false;
    }
    // 校验隐私协议
    if(!$phoneAgreementBtn.is(":checked")) {
      $phonePrivacyModal.show();
      return false;
    }
    return true;
  }

  function optAfter() {
    sendAgain(function () {
      if (!!smsLoginGt.captchaObj) {
        smsLoginGt.captchaObj.reset();
      }
    });
  }
  var smsSendAgainGt = $.gtUtil({
    "showGt": needSmsCheckCode,
    "btnId": "verify-send",
    "formId": "login-form",
    'bussSend': optAfter,
    'checkParam': numberInputCheck,
    'showGtMsg': function (msg) {
      showSmsLoginError(msg);
    }
  });
  var smsLoginGt = $.gtUtil({
    "showGt": needSmsCheckCode,
    "btnId": "verify-next",
    "formId": "login-form",
    'bussSend': optAfter,
    'checkParam': nextPageCheck,
    'showGtMsg': function (msg) {
      showSmsLoginError(msg);
    }
  });

  $phoneNextBtn.on("click", function () {
    // 校验手机号
    if(!isMobileNo($phoneNumber.val())) {
      showToastError("请输入正确的手机号")
      return;
    }

    // initVerificationPhone();

    // else {
    //   // 先触发 Geetest 验证
    //   if (smsLoginGt && smsLoginGt.captchaObj) {
    //     smsLoginGt.captchaObj.verify(); // 触发验证码弹窗
    //     return; // 不立即发送短信，等验证码通过后再继续
    //   }
    //
    //   // 校验成功后发送验证码，打开验证码输入页面
    //   nextStepVerification();
    // }
  });

  $phoneAuthorizationBtn.on("click", function () {
    console.log("合并授权按钮被点击");
    $phoneAgreementBtn.attr("checked", true);
    $phonePrivacyModal.hide();
    // nextStepVerification();
  });

  $phonePrivaryCloseBtn.on("click", function () {
    $phonePrivacyModal.hide();
  });

  // 下一步交互
  function nextStepVerification () {
    const _phoneVal = $phoneNumber.val();
    $(".login-verify-code").show();
    $phonePrivacyModal.hide();
    $codeDescText.text(`${_phoneVal.slice(0, 3)}******${_phoneVal.slice(-2)}`);
    sendVerificationCode();
  };

  // 初始化手机号页面
  function initVerificationPhone () {
    console.log("还原初始状态")
    $(".verify-phone-content").removeClass("verify-error");
    $phoneErrorTips.text("");
    $phoneErrorTips.hide();
  };

  // 初始化
  initVerificationPhone();
  initVerificationCode();
});
