package com.shunwang.basepassport.user.action;

import com.shunwang.baseStone.core.util.SignTool;
import com.shunwang.basepassport.iplocation.response.IpLocationResponse;
import com.shunwang.util.date.DateUtil;
import com.shunwang.util.encrypt.Md5Encrypt;
import com.shunwang.util.json.GsonUtil;
import com.shunwang.util.net.HttpClientUtils;
import org.junit.Test;

import java.io.UnsupportedEncodingException;
import java.nio.charset.Charset;
import java.util.HashMap;
import java.util.Map;

/**
 * User:pf.ma
 * Date:2017/02/07
 * Time:16:23
 */
public class TestAccessToken {
	private String url = "http://interface.kedou.com/front/interface/getAccessToken.htm";

	private Map<String, String> header = new HashMap<>();
	{
		header.put("Accept","application/json");
	}
	@Test
	public void test() throws UnsupportedEncodingException {
		String md5Key = "123456";
		Map<String, String> param = new HashMap<String, String>();
		param.put("siteId", "sw_pay");
		param.put("time", DateUtil.getCurrentDateStamp());
		param.put("signVersion", "1.0");
		param.put("authorizedSiteId", "Passport");
		param.put("authorizedToken", "7e738842-8c0d-4980-98cc-9ef121edc8c9");
		param.put("memberId", "100072");
		String signSource = SignTool.buildSignStringSorted(param, "sign", md5Key);
		String sign = Md5Encrypt.encrypt(signSource).toUpperCase();
		param.put("sign", sign);

		String respond = HttpClientUtils.doPost(url, param, header, null, Charset.defaultCharset());
		IpLocationResponse responses = GsonUtil.fromJson(respond, IpLocationResponse.class);
		System.out.println(responses.toString());
	}
}
