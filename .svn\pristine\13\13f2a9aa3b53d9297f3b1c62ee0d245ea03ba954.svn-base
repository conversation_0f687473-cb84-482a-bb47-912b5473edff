package com.shunwang.baseStone.sso.apapter;

import com.shunwang.baseStone.sso.constant.LoginConstant;
import com.shunwang.baseStone.sso.constant.UserOutsiteConstant;
import com.shunwang.baseStone.sso.context.SsoDomainContext;
import com.shunwang.baseStone.sso.netbar.NetBarLoginJob;
import com.shunwang.baseStone.sso.netbar.NetBarService;
import com.shunwang.baseStone.sso.pojo.QrCodeResponse;
import com.shunwang.baseStone.sso.weixin.oauth.service.WeixinRemoteCall;
import com.shunwang.baseStone.sso.weixin.pojo.WeixinResult;
import com.shunwang.baseStone.sso.weixin.pojo.WeixinUser;
import com.shunwang.basepassport.asynctask.AsyncTaskExecutor;
import com.shunwang.basepassport.config.pojo.Oauth;
import com.shunwang.basepassport.user.common.MemberConstants;
import com.shunwang.basepassport.user.common.MemberUtil;
import com.shunwang.basepassport.user.common.SensitiveInfoUtil;
import com.shunwang.basepassport.user.common.UserLoginSessionUtil;
import com.shunwang.basepassport.user.dao.MemberOutSiteDao;
import com.shunwang.basepassport.user.dao.WxIdCardBindDao;
import com.shunwang.basepassport.user.pojo.WxIdCardBind;
import com.shunwang.basepassport.user.service.DcReportForWxWorkService;
import com.shunwang.basepassport.util.UserAgentUtil;
import com.shunwang.basepassport.weixin.constant.BarLoginEnum;
import com.shunwang.basepassport.weixin.constant.WeixinConstant;
import com.shunwang.basepassport.weixin.constant.WxWorkConstant;
import com.shunwang.basepassport.weixin.pojo.WeixinOauth;
import com.shunwang.basepassport.weixin.pojo.WeixinOauthToken;
import com.shunwang.basepassport.weixin.pojo.WeixinOpenIdUnionId;
import com.shunwang.basepassport.weixin.service.WxWorkUserBindService;
import com.shunwang.util.OS.OsUtil;
import com.shunwang.util.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

public class WeixinAuthAdapter extends WeixinAdapter {

    private static final long serialVersionUID = 125231234L;
    private static final Logger log = LoggerFactory.getLogger(WeixinAuthAdapter.class);

    private static final String WEIXIN_OAUTH2_URL = "https://open.weixin.qq.com/connect/oauth2/authorize";
    private static final String WEIXIN_OAUTH2_QR_URL = "https://open.weixin.qq.com/connect/qrconnect";

    private static final String NET_BAR_BIND_ID_CARD_URL = SsoDomainContext.getSsoServer() + "/wxBindIdCard/goBind.do?type=" + LoginConstant.TYPE_FROM_WX_WORK + "&unionId=";
    private static final String NET_BAR_RESULT_URL = SsoDomainContext.getSsoServer() + "/wxBindIdCard/goResult.do?type=" + LoginConstant.TYPE_FROM_WX_WORK + "&idCardNoResult=";

    private static final String RESULT_TIP = "tip";
    //传入的为空时，使用默认的站点passport
    private static final String DEFAULT_SITE_ID = "passport";

    /**
     * 微信授权请求的参数
     */
    private static final String APPID = "appid";
    private static final String REDIRECT_URI = "redirect_uri";
    private static final String RESPONSE_TYPE = "response_type";
    private static final String SCOPE = "scope";
    private static final String STATE = "state";
    private static final String STYLE = "style";
    private static final String SELF_REDIRECT = "self_redirect";
    private static final String COMPONENT_APPID = "component_appid";
    private static final String V_WECHAT_REDIRECT = "#wechat_redirect";

    private static final String V_SNSAPI_USERINFO = "snsapi_userinfo";
    private static final String V_SNSAPI_LOGIN = "snsapi_login";
    private static final String V_STYLE_BLACK = "black";


    private String code;

    private WeixinRemoteCall weixinRemoteCall;
    private WxIdCardBindDao wxIdCardBindDao;
    private NetBarService netBarService;
    private String weixinWebCallbackUrl;
    private WxWorkUserBindService wxWorkUserBindService;

    @Override
    public String getInterfaceId() {
        return UserOutsiteConstant.WX_INTERFACE_ID;
    }

	@Override
	public String goToOauth() throws Exception {
        if(UserAgentUtil.isWechatInnerWeb()) { //微信内嵌浏览器打开
            return goToOauthH5();
        }
        //以下都当PC处理，生成二维码
        Oauth configOauth = getOauthConfig();
        String userSceneKey = StringUtil.isBlank(innerScene) ? buildCacheUserKey() : innerScene;
        //未配置有效期，默认30秒
        cacheExtData(userSceneKey, 30 * 60);
        String redirectUrl = concatRedirectUrl(getWeixinWebCallbackUrl(), userSceneKey, false);
        initCss(site_id);
        String cssHref = null;
        if (getLoginCss() != null && StringUtil.isNotBlank(getLoginCss().getCssUrl())) {
            cssHref = URLEncoder.encode(getLoginCss().getCssUrl(), StandardCharsets.UTF_8.name());
        }
        return concatWechatUrl(WEIXIN_OAUTH2_QR_URL, redirectUrl, configOauth.getAppId(), null, V_SNSAPI_LOGIN, cssHref);
    }

    private String concatWechatUrl(String url, String redirectUrl, String appid, String componentAppid, String scope, String cssHref) {
        StringBuilder sb = new StringBuilder(url)
                .append("?").append(APPID).append("=").append(appid)
                .append("&").append(REDIRECT_URI).append("=").append(redirectUrl)
                .append("&").append(RESPONSE_TYPE).append("=").append("code")
                .append("&").append(SCOPE).append("=").append(scope)
                .append("&").append(STATE).append("=").append(UUID.randomUUID());
        if (StringUtil.isNotBlank(componentAppid)) {
            sb.append("&").append(COMPONENT_APPID).append("=").append(componentAppid);
        }
        if (StringUtil.isNotBlank(cssHref)) {
            sb.append("&").append(HREF).append("=").append(cssHref);
        }
        if (StringUtil.isNotBlank(tgt)) {
            sb.append("&").append(SELF_REDIRECT).append("=").append("true");
        }
        sb.append("&").append(V_WECHAT_REDIRECT);
        return sb.toString();
    }

    public WeixinOauth loadWechatAppidOauth() {
        WeixinOauth oauth = getWeixinOauthService().getBySiteId(site_id, WeixinConstant.TYPE.AUTHORIZER) ;
        if (oauth == null) {
            log.info("siteId[{}]公众号配置不存在，走默认passport公众号", site_id);
            oauth = getWeixinOauthService().getBySiteId(DEFAULT_SITE_ID, WeixinConstant.TYPE.AUTHORIZER);
        }
        if (null == oauth) {
            log.error("第三方配置异常,[siteId:{}]", site_id);
            throw new RuntimeException("第三方配置异常");
        }
        return oauth;
    }

    private String goToOauthH5() throws Exception{
        WeixinOauth componetOauth = getWeixinOauthService().getBySiteId(getSsoSiteId(), WeixinConstant.TYPE.COMPONENT);
        String cacheUserKey = siteScene;
        if (StringUtil.isBlank(siteScene)) {
            cacheUserKey = StringUtil.isBlank(innerScene) ? buildCacheUserKey() : innerScene;
        }
        //未配置有效期，默认30秒
        cacheExtData(cacheUserKey, 30 * 60);
        String redirectUrl = concatRedirectUrl(getWeixinWebCallbackUrl(), cacheUserKey, true);
        String appid = StringUtil.isBlank(appId) ? loadWechatAppidOauth().getAppId() : appId;
        return concatWechatUrl(WEIXIN_OAUTH2_URL, redirectUrl, appid, componetOauth.getAppId(), V_SNSAPI_USERINFO, null);
    }

    /**
     * 目前仅用于星云企微
     * @param cacheUserKey 场景值
     * @param appId 对应的微信appid
     * @return 结果页
     */
    public String goToOauthH5(String cacheUserKey, String appId) throws UnsupportedEncodingException {
        WeixinOauth componetOauth = getWeixinOauthService().getBySiteId(getSsoSiteId(), WeixinConstant.TYPE.COMPONENT);
        String redirectUrl = URLEncoder.encode(getWeixinWebCallbackUrl() + "?userSceneKey=" + cacheUserKey, StandardCharsets.UTF_8.name());
        String appid = StringUtil.isBlank(appId) ? loadWechatAppidOauth().getAppId() : appId;
        return concatWechatUrl(WEIXIN_OAUTH2_URL, redirectUrl, appid, componetOauth.getAppId(), V_SNSAPI_USERINFO, null);
    }

    @Override
    public Map<String, Object> getOauth() throws UnsupportedEncodingException {
        Oauth configOauth = getOauthConfig() ;
        // 创建一个随机场景值，用来查询是否已经关注
        String cacheUserKey = buildCacheUserKey() ;
        //未配置有效期，默认30秒
        cacheExtData(cacheUserKey, 30 * 60);
        String redirectUrl = concatRedirectUrl(getWeixinWebCallbackUrl(), cacheUserKey, false);
        initCss(site_id);
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put(APPID, configOauth.getAppId());
        resultMap.put(REDIRECT_URI, redirectUrl);
        resultMap.put(SCOPE, V_SNSAPI_LOGIN);
        resultMap.put(STATE, UUID.randomUUID());
        resultMap.put(STYLE, V_STYLE_BLACK);
        resultMap.put(HREF, getLoginCss().getCssUrl()) ;
        return resultMap;
    }

    @Override
    public Map<String, Object> getOauthByServer() throws Exception {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("oauthUrl", goToOauthH5());
        return resultMap;
    }

    @Override
    public String oauthCallback() {
        //从缓存还原接入方透传的数据
        pullCacheExtData(userSceneKey);
        initContextInfo();
        loadPrivacyCss();
        initCss(getSite_id());
        try {
            WeixinResult result = codeToWechatResult();
            if (result == null) {
                return tipOrInput();
            }
            WeixinUser weixinUser = getWeixinRemoteCall().queryWeixinUserInfo(result);
            if (weixinUser == null || StringUtil.isBlank(weixinUser.getUnionid())) {
                log.error("微信获取用户信息失败openId[{}], unionId[{}]", result.getOpenid(), weixinUser != null ? weixinUser.getUnionid() : "");
                setErrorMsg("微信获取用户信息失败");
                return tipOrInput();
            }
            log.info("微信nickname[{}],微信headImg[{}]", weixinUser.getNickname(), weixinUser.getHeadimgurl());
            memberOutSite = buildMemberOutSite(weixinUser, UserOutsiteConstant.WX_INTERFACE_ID, result.getWechatAppid());
            MemberOutSiteDao memberOutSiteDao = memberOutSite.getDao();
            member = memberOutSiteDao.getByOutMemberId(memberOutSite.getOutMemberId());
            // 外部用户不存在
            if (member == null) {
                // sso 第三方微信登录
                member = outSiteMemberRegister(memberOutSite);
            } else if (member.getMemberState() == MemberConstants.USER_CANCEL_STATE) {
                setMsg("您的账号已被注销！");
                memberName = member.getMemberName();
                refreshPage = true;
                return inputCancelView();
            } else {
                updateHeadAndNick(weixinUser);
            }
            //缓存openId，在验证票据时返回，用于业务方与微信交互使用
            member.setOpenId(weixinUser.getOpenid());
            //获取请求二维码的原始客户端ip到登录日志
            member.setClientIp(clientIp);
            //处理登录来源
            if (StringUtil.isNotBlank(cacheResult) && cacheResult.equals("1")) {
                memberOutSite.setMemberFrom(UserOutsiteConstant.WX_QR_INTERFACE_ID);
            }
            MemberUtil.doLogin(member, memberOutSite);
            memberName = member.getMemberName();

            UserLoginSessionUtil.saveSession(memberName, UserLoginSessionUtil.LoginType.SSO_OUT.getType() + getInterfaceId(), null, site_id);
            saveWechatInfo(result.getWechatAppid(), weixinUser);
            if (StringUtil.isNotBlank(visitType) && "bind".equals(visitType)) {
                responseVisitType();
                return SUCCESS;
            }
            initLoginElement(super.getSite_id());
            if (isSingleAccount) {
                String toBind = goSingleBind(weixinUser.getNickname(), weixinUser.getHeadimgurl());
                if (!NO_RETURN.equalsIgnoreCase(toBind)) {
                    return toBind;
                }
                UserLoginSessionUtil.saveMasterSession(memberName, member.getMemberName(), UserLoginSessionUtil.LoginType.SSO_OUT.getType() + getInterfaceId(), null, site_id);
            }
            login(member);
            //场景值归属计费上机则走上机流程
            String mode = getRedisOperation().get(WeixinConstant.NET_BAR_LOGIN + userSceneKey);
            if (StringUtil.isNotBlank(mode) && BarLoginEnum.ModeEnum.NET_BAR.getValue().toString().equals(mode)) {
                barLoginProcess(weixinUser);
                return null;
            }
            if (log.isInfoEnabled()) {
                log.info(URLDecoder.decode(getCallbackUrl(), StandardCharsets.UTF_8.name()));
            }
            return WeixinConstant.MULTI_TERMINAL_LOGIN_JUMP.equals(cacheResult) ? qrSuccess() : SUCCESS;
        } catch (IOException e) {
            log.error("openid授权重定向出错:{}", e.getMessage(), e);
            try {
                getResponse().sendRedirect(callbackUrl);
                return null;
            } catch (IOException e1) {
                log.error("跳转[{}]异常", callbackUrl, e);
            }
        }
        return INPUT;
    }

    private WeixinResult codeToWechatResult() {
        WeixinResult result;
        String wechatAppId;
        log.info("微信授权回调，code:{}", code);
        if(UserAgentUtil.isWechatWeb()) {
            String appId = getRedisOperation().get(WeixinConstant.EXT_SCENE_KEY_APPID + userSceneKey);
            WeixinOauth componetOauth = getWeixinOauthService().getBySiteId(getSsoSiteId(), WeixinConstant.TYPE.COMPONENT);
            WeixinOauthToken componentOauthToken = getWeixinOauthTokenService().getByAppIdAndType(componetOauth.getAppId(), WeixinConstant.TYPE.COMPONENT) ;
            wechatAppId = StringUtil.isBlank(appId) ? loadWechatAppidOauth().getAppId() : appId;
            result = getWeixinRemoteCall().queryOpenId(code, wechatAppId, componetOauth.getAppId(), componentOauthToken.getAccessToken());
        } else {
            Oauth configOauth = getOauthConfig();
            wechatAppId = configOauth.getAppId();
            result = getWeixinRemoteCall().queryOpenId(code, wechatAppId, configOauth.getAppSecret());
        }
        if (result == null) {
            log.error("微信授权code[{}]获取openId失败", code);
            setErrorMsg("微信授权code获取openId失败");
            return null;
        }
        result.setWechatAppid(wechatAppId);
        return result;
    }

    private void saveWechatInfo(String wechatAppId, WeixinUser weixinUser) {
        WeixinOpenIdUnionId weixinOpenIdUnionId = new WeixinOpenIdUnionId();
        weixinOpenIdUnionId.setAppId(wechatAppId);
        weixinOpenIdUnionId.setUnionId(weixinUser.getUnionid());
        weixinOpenIdUnionId.setOpenId(weixinUser.getOpenid());
        weixinOpenIdUnionId.setOriginFrom(WeixinOpenIdUnionId.ORIGIN_FROM.WEIXIN_AUTH.name());
        saveIfNotExist(weixinOpenIdUnionId);
    }

    private String tipOrInput() {
        if(OsUtil.isMobile(getRequest().getHeader("User-Agent"))){
            return RESULT_TIP;
        }
        return INPUT;
    }

    private void barLoginProcess(WeixinUser weixinUser) throws IOException {
        String unionId = weixinUser.getUnionid();
        WxIdCardBind wxIdCardBind = getWxIdCardBindDao().getByUnionId(unionId);
        QrCodeResponse result = new QrCodeResponse(QrCodeResponse.TypeStage.NET_BAR_LOGIN);
        result.setClientTicket(getClientTicket());
        result.setExtData(extData);
        result.setUnionId(unionId);
        if (wxIdCardBind == null || StringUtil.isBlank(wxIdCardBind.getIdCardNoSend())) {
            // 向缓存中添加，通过场景值，缓存30分钟 用于后续绑定身份证后完成上机请求 binded
            DcReportForWxWorkService.wxWorkReport(userSceneKey, DcReportForWxWorkService.OptTypeAndExtInfo.CALLBACK_ROUT_TO_BIND_ID_CARD, unionId);
            result.setOpenId(weixinUser.getOpenid());
            redisOperation.set(userSceneKey, result, 30, TimeUnit.MINUTES);
            getResponse().sendRedirect(NET_BAR_BIND_ID_CARD_URL + unionId + "&eventKey=" + userSceneKey
                    + "&authFreeType=" + MemberConstants.AuthFreeType.NORMAL);
        } else {
            redisOperation.set(userSceneKey, result, 1, TimeUnit.MINUTES);
            //已绑定关系，则直接异步调用上机接口
            AsyncTaskExecutor.submitForBar(new NetBarLoginJob(userSceneKey, wxIdCardBind.getIdCardNoSend(), getNetBarService()));
            DcReportForWxWorkService.wxWorkReport(userSceneKey, DcReportForWxWorkService.OptTypeAndExtInfo.CALLBACK_ROUT_TO_RESULT, unionId);
            //建立绑定关系
            AsyncTaskExecutor.asyncTraceRun(() -> wxWorkUserBindService.updateByScene(userSceneKey, member.getMemberId(), WxWorkConstant.ExternalType.XY.getType()));
            getResponse().sendRedirect(NET_BAR_RESULT_URL + SensitiveInfoUtil.idCardNoStarReplace(wxIdCardBind.getIdCardNo()) + "&eventKey=" + userSceneKey);
        }
    }

	@Override
	protected String getOutOauthLogName() {
		return "微信";
	}

	@Override
    protected String getUserKeyPrefix() {
        return WeixinConstant.QRSCENE_OAURH_PRE;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getCode() {
        return this.code;
    }

    public WeixinRemoteCall getWeixinRemoteCall() {
        return weixinRemoteCall;
    }

    public void setWeixinRemoteCall(WeixinRemoteCall weixinRemoteCall) {
        this.weixinRemoteCall = weixinRemoteCall;
    }

    public String getWeixinWebCallbackUrl() {
        return weixinWebCallbackUrl;
    }

    public void setWeixinWebCallbackUrl(String weixinWebCallbackUrl) {
        this.weixinWebCallbackUrl = weixinWebCallbackUrl;
    }

    public WxIdCardBindDao getWxIdCardBindDao() {
        return wxIdCardBindDao;
    }

    public void setWxIdCardBindDao(WxIdCardBindDao wxIdCardBindDao) {
        this.wxIdCardBindDao = wxIdCardBindDao;
    }

    public NetBarService getNetBarService() {
        return netBarService;
    }

    public void setNetBarService(NetBarService netBarService) {
        this.netBarService = netBarService;
    }

    public WxWorkUserBindService getWxWorkUserBindService() {
        return wxWorkUserBindService;
    }

    public void setWxWorkUserBindService(WxWorkUserBindService wxWorkUserBindService) {
        this.wxWorkUserBindService = wxWorkUserBindService;
    }
}