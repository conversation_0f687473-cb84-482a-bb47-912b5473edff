// @yidun/quickpass-sdk-one-login-h5 v3.1.3 by <EMAIL>
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e="undefined"!=typeof globalThis?globalThis:e||self).NEOneLogin=t()}(this,(function(){"use strict";function e(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function t(e,t,r,n,o,a,i){try{var s=e[a](i),c=s.value}catch(e){return void r(e)}s.done?t(c):Promise.resolve(c).then(n,o)}function r(e){return function(){var r=this,n=arguments;return new Promise((function(o,a){var i=e.apply(r,n);function s(e){t(i,o,a,s,c,"next",e)}function c(e){t(i,o,a,s,c,"throw",e)}s(void 0)}))}}function n(e,t,r){return t=u(t),function(e,t){if(t&&("object"==typeof t||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,h()?Reflect.construct(t,r||[],u(e).constructor):t.apply(e,r))}function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function a(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,y(n.key),n)}}function i(e,t,r){return t&&a(e.prototype,t),r&&a(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function s(e,t,r){return(t=y(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function c(){return c="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,r){var n=function(e,t){for(;!{}.hasOwnProperty.call(e,t)&&null!==(e=u(e)););return e}(e,t);if(n){var o=Object.getOwnPropertyDescriptor(n,t);return o.get?o.get.call(arguments.length<3?e:r):o.value}},c.apply(null,arguments)}function u(e){return u=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},u(e)}function l(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&v(e,t)}function h(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(h=function(){return!!e})()}function p(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function f(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?p(Object(r),!0).forEach((function(t){s(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):p(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function d(){d=function(){return t};var e,t={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(e,t,r){e[t]=r.value},a="function"==typeof Symbol?Symbol:{},i=a.iterator||"@@iterator",s=a.asyncIterator||"@@asyncIterator",c=a.toStringTag||"@@toStringTag";function u(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{u({},"")}catch(e){u=function(e,t,r){return e[t]=r}}function l(e,t,r,n){var a=t&&t.prototype instanceof y?t:y,i=Object.create(a.prototype),s=new L(n||[]);return o(i,"_invoke",{value:_(e,r,s)}),i}function h(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=l;var p="suspendedStart",f="suspendedYield",v="executing",g="completed",m={};function y(){}function b(){}function w(){}var k={};u(k,i,(function(){return this}));var A=Object.getPrototypeOf,C=A&&A(A(N([])));C&&C!==r&&n.call(C,i)&&(k=C);var E=w.prototype=y.prototype=Object.create(k);function T(e){["next","throw","return"].forEach((function(t){u(e,t,(function(e){return this._invoke(t,e)}))}))}function x(e,t){function r(o,a,i,s){var c=h(e[o],e,a);if("throw"!==c.type){var u=c.arg,l=u.value;return l&&"object"==typeof l&&n.call(l,"__await")?t.resolve(l.__await).then((function(e){r("next",e,i,s)}),(function(e){r("throw",e,i,s)})):t.resolve(l).then((function(e){u.value=e,i(u)}),(function(e){return r("throw",e,i,s)}))}s(c.arg)}var a;o(this,"_invoke",{value:function(e,n){function o(){return new t((function(t,o){r(e,n,t,o)}))}return a=a?a.then(o,o):o()}})}function _(t,r,n){var o=p;return function(a,i){if(o===v)throw Error("Generator is already running");if(o===g){if("throw"===a)throw i;return{value:e,done:!0}}for(n.method=a,n.arg=i;;){var s=n.delegate;if(s){var c=O(s,n);if(c){if(c===m)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===p)throw o=g,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=v;var u=h(t,r,n);if("normal"===u.type){if(o=n.done?g:f,u.arg===m)continue;return{value:u.arg,done:n.done}}"throw"===u.type&&(o=g,n.method="throw",n.arg=u.arg)}}}function O(t,r){var n=r.method,o=t.iterator[n];if(o===e)return r.delegate=null,"throw"===n&&t.iterator.return&&(r.method="return",r.arg=e,O(t,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),m;var a=h(o,t.iterator,r.arg);if("throw"===a.type)return r.method="throw",r.arg=a.arg,r.delegate=null,m;var i=a.arg;return i?i.done?(r[t.resultName]=i.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,m):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,m)}function I(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function S(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function L(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(I,this),this.reset(!0)}function N(t){if(t||""===t){var r=t[i];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var o=-1,a=function r(){for(;++o<t.length;)if(n.call(t,o))return r.value=t[o],r.done=!1,r;return r.value=e,r.done=!0,r};return a.next=a}}throw new TypeError(typeof t+" is not iterable")}return b.prototype=w,o(E,"constructor",{value:w,configurable:!0}),o(w,"constructor",{value:b,configurable:!0}),b.displayName=u(w,c,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===b||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,w):(e.__proto__=w,u(e,c,"GeneratorFunction")),e.prototype=Object.create(E),e},t.awrap=function(e){return{__await:e}},T(x.prototype),u(x.prototype,s,(function(){return this})),t.AsyncIterator=x,t.async=function(e,r,n,o,a){void 0===a&&(a=Promise);var i=new x(l(e,r,n,o),a);return t.isGeneratorFunction(r)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},T(E),u(E,c,"Generator"),u(E,i,(function(){return this})),u(E,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},t.values=N,L.prototype={constructor:L,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(S),!t)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function o(n,o){return s.type="throw",s.arg=t,r.next=n,o&&(r.method="next",r.arg=e),!!o}for(var a=this.tryEntries.length-1;a>=0;--a){var i=this.tryEntries[a],s=i.completion;if("root"===i.tryLoc)return o("end");if(i.tryLoc<=this.prev){var c=n.call(i,"catchLoc"),u=n.call(i,"finallyLoc");if(c&&u){if(this.prev<i.catchLoc)return o(i.catchLoc,!0);if(this.prev<i.finallyLoc)return o(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return o(i.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return o(i.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var a=o;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var i=a?a.completion:{};return i.type=e,i.arg=t,a?(this.method="next",this.next=a.finallyLoc,m):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),m},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),S(r),m}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var o=n.arg;S(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={iterator:N(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=e),m}},t}function v(e,t){return v=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},v(e,t)}function g(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,a,i,s=[],c=!0,u=!1;try{if(a=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(s.push(n.value),s.length!==t);c=!0);}catch(e){u=!0,o=e}finally{try{if(!c&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(u)throw o}}return s}}(e,t)||w(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function m(t){return function(t){if(Array.isArray(t))return e(t)}(t)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(t)||w(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function y(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}function b(e){return b="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},b(e)}function w(t,r){if(t){if("string"==typeof t)return e(t,r);var n={}.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?e(t,r):void 0}}function k(e){var t="function"==typeof Map?new Map:void 0;return k=function(e){if(null===e||!function(e){try{return-1!==Function.toString.call(e).indexOf("[native code]")}catch(t){return"function"==typeof e}}(e))return e;if("function"!=typeof e)throw new TypeError("Super expression must either be null or a function");if(void 0!==t){if(t.has(e))return t.get(e);t.set(e,r)}function r(){return function(e,t,r){if(h())return Reflect.construct.apply(null,arguments);var n=[null];n.push.apply(n,t);var o=new(e.bind.apply(e,n));return r&&v(o,r.prototype),o}(e,arguments,u(this).constructor)}return r.prototype=Object.create(e.prototype,{constructor:{value:r,enumerable:!1,writable:!0,configurable:!0}}),v(r,e)},k(e)}var A="undefined"==typeof atob?function(e){return Buffer.from(e,"base64").toString("binary")}:atob,C="undefined"==typeof btoa?function(e){return Buffer.from(e,"binary").toString("base64")}:btoa;function E(e){for(var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=e.length,n=new Uint8Array(t?4*r:r),o=0,a=0;o<r;o++){var i=e.charCodeAt(o);if(t&&55296<=i&&i<=56319){if(++o>=r)throw new Error("Malformed string, low surrogate expected at position "+o);i=(55296^i)<<10|65536|56320^e.charCodeAt(o)}else if(!t&&i>>>8)throw new Error("Wide characters are not allowed.");!t||i<=127?n[a++]=i:i<=2047?(n[a++]=192|i>>6,n[a++]=128|63&i):i<=65535?(n[a++]=224|i>>12,n[a++]=128|i>>6&63,n[a++]=128|63&i):(n[a++]=240|i>>18,n[a++]=128|i>>12&63,n[a++]=128|i>>6&63,n[a++]=128|63&i)}return n.subarray(0,a)}function T(e){for(var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=e.length,n=new Array(r),o=0,a=0;o<r;o++){var i=e[o];if(!t||i<128)n[a++]=i;else if(i>=192&&i<224&&o+1<r)n[a++]=(31&i)<<6|63&e[++o];else if(i>=224&&i<240&&o+2<r)n[a++]=(15&i)<<12|(63&e[++o])<<6|63&e[++o];else{if(!(i>=240&&i<248&&o+3<r))throw new Error("Malformed UTF8 character at byte offset "+o);var s=(7&i)<<18|(63&e[++o])<<12|(63&e[++o])<<6|63&e[++o];s<=65535?n[a++]=s:(s^=65536,n[a++]=55296|s>>10,n[a++]=56320|1023&s)}}var c="",u=16384;for(o=0;o<a;o+=u)c+=String.fromCharCode.apply(String,n.slice(o,o+u<=a?o+u:a));return c}function x(e){return e instanceof Uint8Array}function _(e,t,r,n,o){var a=e.length-t,i=a<o?a:o;return e.set(r.subarray(n,n+i),t),i}var O=function(e){function t(){o(this,t);for(var e=arguments.length,r=new Array(e),a=0;a<e;a++)r[a]=arguments[a];return n(this,t,[].concat(r))}return l(t,e),i(t)}(k(Error)),I=function(e){function t(){o(this,t);for(var e=arguments.length,r=new Array(e),a=0;a<e;a++)r[a]=arguments[a];return n(this,t,[].concat(r))}return l(t,e),i(t)}(k(Error)),S=function(e){function t(){o(this,t);for(var e=arguments.length,r=new Array(e),a=0;a<e;a++)r[a]=arguments[a];return n(this,t,[].concat(r))}return l(t,e),i(t)}(k(Error)),L=function(){var e,t,r=!1;function n(r,n){var o=e[(t[r]+t[n])%255];return 0!==r&&0!==n||(o=0),o}var o,a,i,s,c=!1;function u(){function u(r){var n,o,a;for(o=a=function(r){var n=e[255-t[r]];return 0===r&&(n=0),n}(r),n=0;n<4;n++)a^=o=255&(o<<1|o>>>7);return a^=99}r||function(){e=[],t=[];var n,o,a=1;for(n=0;n<255;n++)e[n]=a,o=128&a,a<<=1,a&=255,128===o&&(a^=27),a^=e[n],t[e[n]]=n;e[255]=e[0],t[0]=0,r=!0}(),o=[],a=[],i=[[],[],[],[]],s=[[],[],[],[]];for(var l=0;l<256;l++){var h=u(l);o[l]=h,a[h]=l,i[0][l]=n(2,h)<<24|h<<16|h<<8|n(3,h),s[0][h]=n(14,l)<<24|n(9,l)<<16|n(13,l)<<8|n(11,l);for(var p=1;p<4;p++)i[p][l]=i[p-1][l]>>>8|i[p-1][l]<<24,s[p][h]=s[p-1][h]>>>8|s[p-1][h]<<24}c=!0}var l=function(e,t){c||u();var r=new Uint32Array(t);r.set(o,512),r.set(a,768);for(var n=0;n<4;n++)r.set(i[n],4096+1024*n>>2),r.set(s[n],8192+1024*n>>2);var l=function(e,t,r){"use asm";var n=0,o=0,a=0,i=0,s=0,c=0,u=0,l=0,h=0,p=0,f=0,d=0,v=0,g=0,m=0,y=0,b=0,w=0,k=0,A=0,C=0;var E=new e.Uint32Array(r),T=new e.Uint8Array(r);function x(e,t,r,s,c,u,l,h){e=e|0;t=t|0;r=r|0;s=s|0;c=c|0;u=u|0;l=l|0;h=h|0;var p=0,f=0,d=0,v=0,g=0,m=0,y=0,b=0;p=r|0x400,f=r|0x800,d=r|0xc00;c=c^E[(e|0)>>2],u=u^E[(e|4)>>2],l=l^E[(e|8)>>2],h=h^E[(e|12)>>2];for(b=16;(b|0)<=s<<4;b=b+16|0){v=E[(r|c>>22&1020)>>2]^E[(p|u>>14&1020)>>2]^E[(f|l>>6&1020)>>2]^E[(d|h<<2&1020)>>2]^E[(e|b|0)>>2],g=E[(r|u>>22&1020)>>2]^E[(p|l>>14&1020)>>2]^E[(f|h>>6&1020)>>2]^E[(d|c<<2&1020)>>2]^E[(e|b|4)>>2],m=E[(r|l>>22&1020)>>2]^E[(p|h>>14&1020)>>2]^E[(f|c>>6&1020)>>2]^E[(d|u<<2&1020)>>2]^E[(e|b|8)>>2],y=E[(r|h>>22&1020)>>2]^E[(p|c>>14&1020)>>2]^E[(f|u>>6&1020)>>2]^E[(d|l<<2&1020)>>2]^E[(e|b|12)>>2];c=v,u=g,l=m,h=y}n=E[(t|c>>22&1020)>>2]<<24^E[(t|u>>14&1020)>>2]<<16^E[(t|l>>6&1020)>>2]<<8^E[(t|h<<2&1020)>>2]^E[(e|b|0)>>2],o=E[(t|u>>22&1020)>>2]<<24^E[(t|l>>14&1020)>>2]<<16^E[(t|h>>6&1020)>>2]<<8^E[(t|c<<2&1020)>>2]^E[(e|b|4)>>2],a=E[(t|l>>22&1020)>>2]<<24^E[(t|h>>14&1020)>>2]<<16^E[(t|c>>6&1020)>>2]<<8^E[(t|u<<2&1020)>>2]^E[(e|b|8)>>2],i=E[(t|h>>22&1020)>>2]<<24^E[(t|c>>14&1020)>>2]<<16^E[(t|u>>6&1020)>>2]<<8^E[(t|l<<2&1020)>>2]^E[(e|b|12)>>2]}function _(e,t,r,n){e=e|0;t=t|0;r=r|0;n=n|0;x(0x0000,0x0800,0x1000,C,e,t,r,n)}function O(e,t,r,n){e=e|0;t=t|0;r=r|0;n=n|0;var a=0;x(0x0400,0x0c00,0x2000,C,e,n,r,t);a=o,o=i,i=a}function I(e,t,r,s){e=e|0;t=t|0;r=r|0;s=s|0;x(0x0000,0x0800,0x1000,C,h,p,f,d);d=~y&d|y&d+1;f=~m&f|m&f+((d|0)==0);p=~g&p|g&p+((f|0)==0);h=~v&h|v&h+((p|0)==0);n=n^e;o=o^t;a=a^r;i=i^s}function S(e,t,r,n){e=e|0;t=t|0;r=r|0;n=n|0;var o=0,a=0,i=0,h=0,p=0,f=0,d=0,v=0,g=0,m=0;e=e^s,t=t^c,r=r^u,n=n^l;o=b|0,a=w|0,i=k|0,h=A|0;for(;(g|0)<128;g=g+1|0){if(o>>>31){p=p^e,f=f^t,d=d^r,v=v^n}o=o<<1|a>>>31,a=a<<1|i>>>31,i=i<<1|h>>>31,h=h<<1;m=n&1;n=n>>>1|r<<31,r=r>>>1|t<<31,t=t>>>1|e<<31,e=e>>>1;if(m)e=e^0xe1000000}s=p,c=f,u=d,l=v}function L(e){e=e|0;C=e}function N(e,t,r,s){e=e|0;t=t|0;r=r|0;s=s|0;n=e,o=t,a=r,i=s}function P(e,t,r,n){e=e|0;t=t|0;r=r|0;n=n|0;s=e,c=t,u=r,l=n}function U(e,t,r,n){e=e|0;t=t|0;r=r|0;n=n|0;h=e,p=t,f=r,d=n}function j(e,t,r,n){e=e|0;t=t|0;r=r|0;n=n|0;v=e,g=t,m=r,y=n}function D(e,t,r,n){e=e|0;t=t|0;r=r|0;n=n|0;d=~y&d|y&n,f=~m&f|m&r,p=~g&p|g&t,h=~v&h|v&e}function M(e){e=e|0;if(e&15)return-1;T[e|0]=n>>>24,T[e|1]=n>>>16&255,T[e|2]=n>>>8&255,T[e|3]=n&255,T[e|4]=o>>>24,T[e|5]=o>>>16&255,T[e|6]=o>>>8&255,T[e|7]=o&255,T[e|8]=a>>>24,T[e|9]=a>>>16&255,T[e|10]=a>>>8&255,T[e|11]=a&255,T[e|12]=i>>>24,T[e|13]=i>>>16&255,T[e|14]=i>>>8&255,T[e|15]=i&255;return 16}function H(e){e=e|0;if(e&15)return-1;T[e|0]=s>>>24,T[e|1]=s>>>16&255,T[e|2]=s>>>8&255,T[e|3]=s&255,T[e|4]=c>>>24,T[e|5]=c>>>16&255,T[e|6]=c>>>8&255,T[e|7]=c&255,T[e|8]=u>>>24,T[e|9]=u>>>16&255,T[e|10]=u>>>8&255,T[e|11]=u&255,T[e|12]=l>>>24,T[e|13]=l>>>16&255,T[e|14]=l>>>8&255,T[e|15]=l&255;return 16}function R(){_(0,0,0,0);b=n,w=o,k=a,A=i}function B(e,t,r){e=e|0;t=t|0;r=r|0;var s=0;if(t&15)return-1;while((r|0)>=16){F[e&7](T[t|0]<<24|T[t|1]<<16|T[t|2]<<8|T[t|3],T[t|4]<<24|T[t|5]<<16|T[t|6]<<8|T[t|7],T[t|8]<<24|T[t|9]<<16|T[t|10]<<8|T[t|11],T[t|12]<<24|T[t|13]<<16|T[t|14]<<8|T[t|15]);T[t|0]=n>>>24,T[t|1]=n>>>16&255,T[t|2]=n>>>8&255,T[t|3]=n&255,T[t|4]=o>>>24,T[t|5]=o>>>16&255,T[t|6]=o>>>8&255,T[t|7]=o&255,T[t|8]=a>>>24,T[t|9]=a>>>16&255,T[t|10]=a>>>8&255,T[t|11]=a&255,T[t|12]=i>>>24,T[t|13]=i>>>16&255,T[t|14]=i>>>8&255,T[t|15]=i&255;s=s+16|0,t=t+16|0,r=r-16|0}return s|0}function G(e,t,r){e=e|0;t=t|0;r=r|0;var n=0;if(t&15)return-1;while((r|0)>=16){J[e&1](T[t|0]<<24|T[t|1]<<16|T[t|2]<<8|T[t|3],T[t|4]<<24|T[t|5]<<16|T[t|6]<<8|T[t|7],T[t|8]<<24|T[t|9]<<16|T[t|10]<<8|T[t|11],T[t|12]<<24|T[t|13]<<16|T[t|14]<<8|T[t|15]);n=n+16|0,t=t+16|0,r=r-16|0}return n|0}var F=[_,O,undefined,undefined,undefined,undefined,undefined,I];var J=[undefined,S];return{set_rounds:L,set_state:N,set_iv:P,set_nonce:U,set_mask:j,set_counter:D,get_state:M,get_iv:H,gcm_init:R,cipher:B,mac:G}}({Uint8Array:Uint8Array,Uint32Array:Uint32Array},e,t);return l.set_key=function(e,t,n,a,i,c,u,h,p){var f=r.subarray(0,60),d=r.subarray(256,316);f.set([t,n,a,i,c,u,h,p]);for(var v=e,g=1;v<4*e+28;v++){var m=f[v-1];(v%e==0||8===e&&v%e==4)&&(m=o[m>>>24]<<24^o[m>>>16&255]<<16^o[m>>>8&255]<<8^o[255&m]),v%e==0&&(m=m<<8^m>>>24^g<<24,g=g<<1^(128&g?27:0)),f[v]=f[v-e]^m}for(var y=0;y<v;y+=4)for(var b=0;b<4;b++){m=f[v-(4+y)+(4-b)%4];d[y+b]=y<4||y>=v-4?m:s[0][o[m>>>24]]^s[1][o[m>>>16&255]]^s[2][o[m>>>8&255]]^s[3][o[255&m]]}l.set_rounds(e+5)},l};return l.ENC={ECB:0,CBC:2,CFB:4,OFB:6,CTR:7},l.DEC={ECB:1,CBC:3,CFB:5,OFB:6,CTR:7},l.MAC={CBC:0,GCM:1},l.HEAP_DATA=16384,l}(),N=function(){return i((function e(t,r){var n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],a=arguments.length>3?arguments[3]:void 0,i=arguments.length>4?arguments[4]:void 0,s=arguments.length>5?arguments[5]:void 0;o(this,e),this.pos=0,this.len=0,this.mode=a,this.heap=i||function(e,t){var r=e?e.byteLength:t||65536;if(4095&r||r<=0)throw new Error("heap size must be a positive integer and a multiple of 4096");return e||new Uint8Array(new ArrayBuffer(r))}().subarray(L.HEAP_DATA),this.asm=s||new L(null,this.heap.buffer),this.pos=0,this.len=0;var c=t.length;if(16!==c&&24!==c&&32!==c)throw new I("illegal key size");var u=new DataView(t.buffer,t.byteOffset,t.byteLength);if(this.asm.set_key(c>>2,u.getUint32(0),u.getUint32(4),u.getUint32(8),u.getUint32(12),c>16?u.getUint32(16):0,c>16?u.getUint32(20):0,c>24?u.getUint32(24):0,c>24?u.getUint32(28):0),void 0!==r){if(16!==r.length)throw new I("illegal iv size");var l=new DataView(r.buffer,r.byteOffset,r.byteLength);this.asm.set_iv(l.getUint32(0),l.getUint32(4),l.getUint32(8),l.getUint32(12))}else this.asm.set_iv(0,0,0,0);this.padding=n}),[{key:"AES_Encrypt_process",value:function(e){if(!x(e))throw new TypeError("data isn't of expected type");for(var t=this.asm,r=this.heap,n=L.ENC[this.mode],o=L.HEAP_DATA,a=this.pos,i=this.len,s=0,c=e.length||0,u=0,l=0,h=new Uint8Array(i+c&-16);c>0;)i+=l=_(r,a+i,e,s,c),s+=l,c-=l,(l=t.cipher(n,o+a,i))&&h.set(r.subarray(a,a+l),u),u+=l,l<i?(a+=l,i-=l):(a=0,i=0);return this.pos=a,this.len=i,h}},{key:"AES_Encrypt_finish",value:function(){var e=this.asm,t=this.heap,r=L.ENC[this.mode],n=L.HEAP_DATA,o=this.pos,a=this.len,i=16-a%16,s=a;if(this.hasOwnProperty("padding")){if(this.padding){for(var c=0;c<i;++c)t[o+a+c]=i;s=a+=i}else if(a%16)throw new I("data length must be a multiple of the block size")}else a+=i;var u=new Uint8Array(s);return a&&e.cipher(r,n+o,a),s&&u.set(t.subarray(o,o+s)),this.pos=0,this.len=0,u}},{key:"AES_Decrypt_process",value:function(e){if(!x(e))throw new TypeError("data isn't of expected type");var t=this.asm,r=this.heap,n=L.DEC[this.mode],o=L.HEAP_DATA,a=this.pos,i=this.len,s=0,c=e.length||0,u=0,l=i+c&-16,h=0,p=0;this.padding&&(l-=h=i+c-l||16);for(var f=new Uint8Array(l);c>0;)i+=p=_(r,a+i,e,s,c),s+=p,c-=p,(p=t.cipher(n,o+a,i-(c?0:h)))&&f.set(r.subarray(a,a+p),u),u+=p,p<i?(a+=p,i-=p):(a=0,i=0);return this.pos=a,this.len=i,f}},{key:"AES_Decrypt_finish",value:function(){var e=this.asm,t=this.heap,r=L.DEC[this.mode],n=L.HEAP_DATA,o=this.pos,a=this.len,i=a;if(a>0){if(a%16){if(this.hasOwnProperty("padding"))throw new I("data length must be a multiple of the block size");a+=16-a%16}if(e.cipher(r,n+o,a),this.hasOwnProperty("padding")&&this.padding){var s=t[o+i-1];if(s<1||s>16||s>i)throw new S("bad padding");for(var c=0,u=s;u>1;u--)c|=s^t[o+i-u];if(c)throw new S("bad padding");i-=s}}var l=new Uint8Array(i);return i>0&&l.set(t.subarray(o,o+i)),this.pos=0,this.len=0,l}}])}(),P=68719476704,U=function(){function e(t,r,n){var a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:16,i=arguments.length>4?arguments[4]:void 0;if(o(this,e),this.tagSize=a,this.gamma0=0,this.counter=1,this.aes=i||new N(t,void 0,!1,"CTR"),this.aes.asm.gcm_init(),this.tagSize<4||this.tagSize>16)throw new I("illegal tagSize value");var s=r.length||0,c=new Uint8Array(16);12!==s?(this._gcm_mac_process(r),this.aes.heap[0]=0,this.aes.heap[1]=0,this.aes.heap[2]=0,this.aes.heap[3]=0,this.aes.heap[4]=0,this.aes.heap[5]=0,this.aes.heap[6]=0,this.aes.heap[7]=0,this.aes.heap[8]=0,this.aes.heap[9]=0,this.aes.heap[10]=0,this.aes.heap[11]=s>>>29,this.aes.heap[12]=s>>>21&255,this.aes.heap[13]=s>>>13&255,this.aes.heap[14]=s>>>5&255,this.aes.heap[15]=s<<3&255,this.aes.asm.mac(L.MAC.GCM,L.HEAP_DATA,16),this.aes.asm.get_iv(L.HEAP_DATA),this.aes.asm.set_iv(0,0,0,0),c.set(this.aes.heap.subarray(0,16))):(c.set(r),c[15]=1);var u=new DataView(c.buffer);if(this.gamma0=u.getUint32(12),this.aes.asm.set_nonce(u.getUint32(0),u.getUint32(4),u.getUint32(8),0),this.aes.asm.set_mask(0,0,0,4294967295),void 0!==n){if(n.length>P)throw new I("illegal adata length");n.length?(this.adata=n,this._gcm_mac_process(n)):this.adata=void 0}else this.adata=void 0;if(this.counter<1||this.counter>4294967295)throw new RangeError("counter must be a positive 32-bit integer");this.aes.asm.set_counter(0,0,0,this.gamma0+this.counter|0)}return i(e,[{key:"encrypt",value:function(e){return this.AES_GCM_encrypt(e)}},{key:"decrypt",value:function(e){return this.AES_GCM_decrypt(e)}},{key:"AES_GCM_Encrypt_process",value:function(e){var t=0,r=e.length||0,n=this.aes.asm,o=this.aes.heap,a=this.counter,i=this.aes.pos,s=this.aes.len,c=0,u=s+r&-16,l=0;if((a-1<<4)+s+r>P)throw new RangeError("counter overflow");for(var h=new Uint8Array(u);r>0;)s+=l=_(o,i+s,e,t,r),t+=l,r-=l,l=n.cipher(L.ENC.CTR,L.HEAP_DATA+i,s),(l=n.mac(L.MAC.GCM,L.HEAP_DATA+i,l))&&h.set(o.subarray(i,i+l),c),a+=l>>>4,c+=l,l<s?(i+=l,s-=l):(i=0,s=0);return this.counter=a,this.aes.pos=i,this.aes.len=s,h}},{key:"AES_GCM_Encrypt_finish",value:function(){var e=this.aes.asm,t=this.aes.heap,r=this.counter,n=this.tagSize,o=this.adata,a=this.aes.pos,i=this.aes.len,s=new Uint8Array(i+n);e.cipher(L.ENC.CTR,L.HEAP_DATA+a,i+15&-16),i&&s.set(t.subarray(a,a+i));for(var c=i;15&c;c++)t[a+c]=0;e.mac(L.MAC.GCM,L.HEAP_DATA+a,c);var u=void 0!==o?o.length:0,l=(r-1<<4)+i;return t[0]=0,t[1]=0,t[2]=0,t[3]=u>>>29,t[4]=u>>>21,t[5]=u>>>13&255,t[6]=u>>>5&255,t[7]=u<<3&255,t[8]=t[9]=t[10]=0,t[11]=l>>>29,t[12]=l>>>21&255,t[13]=l>>>13&255,t[14]=l>>>5&255,t[15]=l<<3&255,e.mac(L.MAC.GCM,L.HEAP_DATA,16),e.get_iv(L.HEAP_DATA),e.set_counter(0,0,0,this.gamma0),e.cipher(L.ENC.CTR,L.HEAP_DATA,16),s.set(t.subarray(0,n),i),this.counter=1,this.aes.pos=0,this.aes.len=0,s}},{key:"AES_GCM_Decrypt_process",value:function(e){var t=0,r=e.length||0,n=this.aes.asm,o=this.aes.heap,a=this.counter,i=this.tagSize,s=this.aes.pos,c=this.aes.len,u=0,l=c+r>i?c+r-i&-16:0,h=c+r-l,p=0;if((a-1<<4)+c+r>P)throw new RangeError("counter overflow");for(var f=new Uint8Array(l);r>h;)c+=p=_(o,s+c,e,t,r-h),t+=p,r-=p,p=n.mac(L.MAC.GCM,L.HEAP_DATA+s,p),(p=n.cipher(L.DEC.CTR,L.HEAP_DATA+s,p))&&f.set(o.subarray(s,s+p),u),a+=p>>>4,u+=p,s=0,c=0;return r>0&&(c+=_(o,0,e,t,r)),this.counter=a,this.aes.pos=s,this.aes.len=c,f}},{key:"AES_GCM_Decrypt_finish",value:function(){var e=this.aes.asm,t=this.aes.heap,r=this.tagSize,n=this.adata,o=this.counter,a=this.aes.pos,i=this.aes.len,s=i-r;if(i<r)throw new O("authentication tag not found");for(var c=new Uint8Array(s),u=new Uint8Array(t.subarray(a+s,a+i)),l=s;15&l;l++)t[a+l]=0;e.mac(L.MAC.GCM,L.HEAP_DATA+a,l),e.cipher(L.DEC.CTR,L.HEAP_DATA+a,l),s&&c.set(t.subarray(a,a+s));var h=void 0!==n?n.length:0,p=(o-1<<4)+i-r;t[0]=0,t[1]=0,t[2]=0,t[3]=h>>>29,t[4]=h>>>21,t[5]=h>>>13&255,t[6]=h>>>5&255,t[7]=h<<3&255,t[8]=t[9]=t[10]=0,t[11]=p>>>29,t[12]=p>>>21&255,t[13]=p>>>13&255,t[14]=p>>>5&255,t[15]=p<<3&255,e.mac(L.MAC.GCM,L.HEAP_DATA,16),e.get_iv(L.HEAP_DATA),e.set_counter(0,0,0,this.gamma0),e.cipher(L.ENC.CTR,L.HEAP_DATA,16);for(var f=0,d=0;d<r;++d)f|=u[d]^t[d];if(f)throw new S("data integrity check failed");return this.counter=1,this.aes.pos=0,this.aes.len=0,c}},{key:"AES_GCM_decrypt",value:function(e){var t=this.AES_GCM_Decrypt_process(e),r=this.AES_GCM_Decrypt_finish(),n=new Uint8Array(t.length+r.length);return t.length&&n.set(t),r.length&&n.set(r,t.length),n}},{key:"AES_GCM_encrypt",value:function(e){var t=this.AES_GCM_Encrypt_process(e),r=this.AES_GCM_Encrypt_finish(),n=new Uint8Array(t.length+r.length);return t.length&&n.set(t),r.length&&n.set(r,t.length),n}},{key:"_gcm_mac_process",value:function(e){for(var t=this.aes.heap,r=this.aes.asm,n=0,o=e.length||0,a=0;o>0;){for(n+=a=_(t,0,e,n,o),o-=a;15&a;)t[a++]=0;r.mac(L.MAC.GCM,L.HEAP_DATA,a)}}}],[{key:"encrypt",value:function(t,r,n,o,a){return new e(r,n,o,a).encrypt(t)}},{key:"decrypt",value:function(t,r,n,o,a){return new e(r,n,o,a).decrypt(t)}}])}(),j=function(e,t){var r,n,o="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz".split(""),a=[];if(t=t||o.length,e)for(r=0;r<e;r++)a[r]=o[0|Math.random()*t];else for(a[8]=a[13]=a[18]=a[23]="-",a[14]="4",r=0;r<36;r++)a[r]||(n=0|16*Math.random(),a[r]=o[19===r?3&n|8:n]);return a.join("")},D=function(e){"object"===b(e)&&(e=JSON.stringify(e));var t="".concat(j(3),":").concat(j(3),":").concat(j(3),":").concat(j(4)),r=j(12),n=U.encrypt(E(e),E(t),E(r));return{key:t,iv:r,eyptStr:C(T(n))}},M=function(e,t,r){var n=U.decrypt(function(e){return E(A(e))}(e),E(t),E(r));return T(n)},H=function(e){return function(e){for(var t="0123456789abcdef",r="",n=0;n<4*e.length;n++)r+=t.charAt(e[n>>2]>>n%4*8+4&15)+t.charAt(e[n>>2]>>n%4*8&15);return r}(function(e,t){e[t>>5]|=128<<t%32,e[14+(t+64>>>9<<4)]=t;for(var r=1732584193,n=-271733879,o=-1732584194,a=271733878,i=0;i<e.length;i+=16){var s=r,c=n,u=o,l=a;r=B(r,n,o,a,e[i+0],7,-680876936),a=B(a,r,n,o,e[i+1],12,-389564586),o=B(o,a,r,n,e[i+2],17,606105819),n=B(n,o,a,r,e[i+3],22,-1044525330),r=B(r,n,o,a,e[i+4],7,-176418897),a=B(a,r,n,o,e[i+5],12,1200080426),o=B(o,a,r,n,e[i+6],17,-1473231341),n=B(n,o,a,r,e[i+7],22,-45705983),r=B(r,n,o,a,e[i+8],7,1770035416),a=B(a,r,n,o,e[i+9],12,-1958414417),o=B(o,a,r,n,e[i+10],17,-42063),n=B(n,o,a,r,e[i+11],22,-1990404162),r=B(r,n,o,a,e[i+12],7,1804603682),a=B(a,r,n,o,e[i+13],12,-40341101),o=B(o,a,r,n,e[i+14],17,-1502002290),r=G(r,n=B(n,o,a,r,e[i+15],22,1236535329),o,a,e[i+1],5,-165796510),a=G(a,r,n,o,e[i+6],9,-1069501632),o=G(o,a,r,n,e[i+11],14,643717713),n=G(n,o,a,r,e[i+0],20,-373897302),r=G(r,n,o,a,e[i+5],5,-701558691),a=G(a,r,n,o,e[i+10],9,38016083),o=G(o,a,r,n,e[i+15],14,-660478335),n=G(n,o,a,r,e[i+4],20,-405537848),r=G(r,n,o,a,e[i+9],5,568446438),a=G(a,r,n,o,e[i+14],9,-1019803690),o=G(o,a,r,n,e[i+3],14,-187363961),n=G(n,o,a,r,e[i+8],20,1163531501),r=G(r,n,o,a,e[i+13],5,-1444681467),a=G(a,r,n,o,e[i+2],9,-51403784),o=G(o,a,r,n,e[i+7],14,1735328473),r=F(r,n=G(n,o,a,r,e[i+12],20,-1926607734),o,a,e[i+5],4,-378558),a=F(a,r,n,o,e[i+8],11,-2022574463),o=F(o,a,r,n,e[i+11],16,1839030562),n=F(n,o,a,r,e[i+14],23,-35309556),r=F(r,n,o,a,e[i+1],4,-1530992060),a=F(a,r,n,o,e[i+4],11,1272893353),o=F(o,a,r,n,e[i+7],16,-155497632),n=F(n,o,a,r,e[i+10],23,-1094730640),r=F(r,n,o,a,e[i+13],4,681279174),a=F(a,r,n,o,e[i+0],11,-358537222),o=F(o,a,r,n,e[i+3],16,-722521979),n=F(n,o,a,r,e[i+6],23,76029189),r=F(r,n,o,a,e[i+9],4,-640364487),a=F(a,r,n,o,e[i+12],11,-421815835),o=F(o,a,r,n,e[i+15],16,530742520),r=J(r,n=F(n,o,a,r,e[i+2],23,-995338651),o,a,e[i+0],6,-198630844),a=J(a,r,n,o,e[i+7],10,1126891415),o=J(o,a,r,n,e[i+14],15,-1416354905),n=J(n,o,a,r,e[i+5],21,-57434055),r=J(r,n,o,a,e[i+12],6,1700485571),a=J(a,r,n,o,e[i+3],10,-1894986606),o=J(o,a,r,n,e[i+10],15,-1051523),n=J(n,o,a,r,e[i+1],21,-2054922799),r=J(r,n,o,a,e[i+8],6,1873313359),a=J(a,r,n,o,e[i+15],10,-30611744),o=J(o,a,r,n,e[i+6],15,-1560198380),n=J(n,o,a,r,e[i+13],21,1309151649),r=J(r,n,o,a,e[i+4],6,-145523070),a=J(a,r,n,o,e[i+11],10,-1120210379),o=J(o,a,r,n,e[i+2],15,718787259),n=J(n,o,a,r,e[i+9],21,-343485551),r=q(r,s),n=q(n,c),o=q(o,u),a=q(a,l)}return Array(r,n,o,a)}(function(e){for(var t=Array(),r=255,n=0;n<8*e.length;n+=8)t[n>>5]|=(e.charCodeAt(n/8)&r)<<n%32;return t}(e),8*e.length))};function R(e,t,r,n,o,a){return q((i=q(q(t,e),q(n,a)))<<(s=o)|i>>>32-s,r);var i,s}function B(e,t,r,n,o,a,i){return R(t&r|~t&n,e,t,o,a,i)}function G(e,t,r,n,o,a,i){return R(t&n|r&~n,e,t,o,a,i)}function F(e,t,r,n,o,a,i){return R(t^r^n,e,t,o,a,i)}function J(e,t,r,n,o,a,i){return R(r^(t|~n),e,t,o,a,i)}function q(e,t){var r=(65535&e)+(65535&t);return(e>>16)+(t>>16)+(r>>16)<<16|65535&r}function z(e){var t,r,n,o,a,i,s,c="",u="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",l=0;for(e=function(e){e=e.replace(/\r\n/g,"\n");for(var t="",r=0;r<e.length;r++){var n=e.charCodeAt(r);n<128?t+=String.fromCharCode(n):n>127&&n<2048?(t+=String.fromCharCode(n>>6|192),t+=String.fromCharCode(63&n|128)):(t+=String.fromCharCode(n>>12|224),t+=String.fromCharCode(n>>6&63|128),t+=String.fromCharCode(63&n|128))}return t}(e);l<e.length;)o=(t=e.charCodeAt(l++))>>2,a=(3&t)<<4|(r=e.charCodeAt(l++))>>4,i=(15&r)<<2|(n=e.charCodeAt(l++))>>6,s=63&n,isNaN(r)?i=s=64:isNaN(n)&&(s=64),c=c+u.charAt(o)+u.charAt(a)+u.charAt(i)+u.charAt(s);return c}var W="ne-one-login",X="#ne-one-login_auth-iframe",V="1",Y="2",$={AUTH:["".concat(W,"-auth"),"授权窗口"],MAIN:["".concat(W,"-main"),"主窗口"]},K=["auth_token","获取 token"],Q=["auth-lose","关闭授权页"],Z=["auth-switch","授权页切换按钮点击"],ee=["auth-phone-input-complete","输入手机号完整"],te=["auth-login-btn-click","登录按钮点击"],re=["auth-tip","发送提示"],ne=["auth-disposed","授权页面实例已销毁"],oe=["auth-update-styles-completed","授权页初始化样式更新完成"],ae=["update-styles","更新授权页样式"],ie=["events","触发事件"],se=["login-btn-enable"],ce=["login-btn-disable"],ue=["auth-frame-dispose"],le="error",he="ready",pe="success",fe="close",de="switch",ve="mask-phone",ge="complete",me="login-click",ye="tip",be=3,we=[-8001,"网络请求错误"],ke=[-8002,"预取号超时"],Ae=[1e4,"预取号失败"],Ce=[20001,"移动取号失败"],Ee=[20002,"移动获取 accessToken 失败"],Te=[20010,"移动报备 referer 校验失败"],xe=[30001,"联通获取省份URL失败"],_e=[30002,"联通获取省份失败"],Oe=[30003,"联通获取 accessToken 失败"],Ie=[30010,"联通报备 referer 校验失败"],Se=[40001,"电信预授权失败"],Le=[40002,"电信获取 accessCode 失败"],Ne=[40010,"电信报备 referer 校验失败"],Pe=[4e4,"未知运营商，仅支持电信、联通、移动"],Ue={CHINA_TELECOM:[1,"中国电信","中国电信天翼账号服务条款","https://e.189.cn/sdk/agreement/detail.do?hidetop=true"],CHINA_MOBILE:[2,"中国移动","中国移动认证服务条款","https://wap.cmpassport.com/resources/html/contract.html"],CHINA_UNICOM:[3,"中国联通","中国联通认证服务协议","https://msv6.wosms.cn/html/oauth/protocol2.html"],UNKNOWN:[4,"未知运营商"]},je=["back","logo","slogan","phone","policy","loginButton"],De={width:"100%",height:"100%",border:0,position:"fixed",top:0,left:0,right:0,bottom:0,zIndex:9999999,background:"#fff"},Me={width:"100%",height:"100%",border:0},He=["debugLog","调试日志"],Re=["apiError","接口错误"];function Be(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"callback",n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:6e3;return new Promise((function(o,a){var i,s=window.encodeURIComponent,c=document.getElementsByTagName("script")[0]||document.head,u=null;function l(){var e=document.getElementById("jsonp-id"),t=null==e?void 0:e.parentNode;t&&e&&t.removeChild(e),u&&clearTimeout(u)}n&&(u=setTimeout((function(){l()}),n)),window[r]=function(e){window.jsonpData=e,delete window[r]};var h=e+(~e.indexOf("?")?"&":"?")+function(e){var t=[];for(var r in e)e.hasOwnProperty(r)&&t.push("".concat(s(r),"=").concat(s(e[r])));return t.join("&")}(t)+(r?"&callback=".concat(r):"");h=h.replace("?&","?");var p=document.createElement("script");p.type="text/javascript",p.src=h,p.id="jsonp-id",p.onload=function(){l(),o({jsonpData:window.jsonpData,res:{responseURL:h}}),delete window.jsonpData},p.onerror=function(e){l();var t="Your jsonp request to ".concat(e.target.src," is fail, please check your url or params again."),r=new Error(t);r.res={responseURL:e.target.src},a(r)},null===(i=c.parentNode)||void 0===i||i.insertBefore(p,c)}))}var Ge={"Access-Control-Allow-Origin":"*"};function Fe(e){return Je.apply(this,arguments)}function Je(){return Je=r(d().mark((function e(t){var r,n,o,a,i,s,c,u,l,h,p,f,v,g,m,y=arguments;return d().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=y.length>1&&void 0!==y[1]?y[1]:function(){},n=y.length>2&&void 0!==y[2]?y[2]:function(){},o=t.url,a=t.method,i=void 0===a?"post":a,s=t.timeout,c=void 0===s?12e3:s,u=t.headers,l=t.data,h=t.useBaseUrl,p=void 0===h||h,f=t.callbackName,v=t.apiServer,g=v||"https://ye.dun.163yun.com",Object.assign(Ge,u),m=new Promise((function(e,t){if("jsonp"!==i){var a=new XMLHttpRequest;if(!a){var s=new Error("cannot creat an XMLHTTP instance");n(we[0],s)}"onloadend"in a?a.onloadend=k:a.onreadystatechange=function(){var e=void 0!==XMLHttpRequest.DONE?XMLHttpRequest.DONE:4;if(a.readyState===e)if(a.status>=200&&a.status<300||304===a.status)setTimeout(k);else{var r=new Error("网络请求错误");r.res={target:a.responseURL,status:a.status,response:a.response},t(r)}};var h="".concat(p?g:"").concat(o),d=i;if("GET"===i.toUpperCase()){var v,m=[];try{v="string"==typeof l?JSON.parse(l):l}catch(A){t(new Error("error"))}for(var y in v)Object.prototype.hasOwnProperty.call(v,y)&&m.push("".concat(y,"=").concat(v[y]));var b=m.join("&");h="".concat(h,"?").concat(b),d="get"}for(var w in"POST"===i.toUpperCase()&&(d="post"),a.open(d,h,!0),a.timeout=c,u)Object.prototype.hasOwnProperty.call(u,w)&&a.setRequestHeader(w,u[w]);a.send("get"===d?null:JSON.stringify(l))}else Be(o,l,f,c).then((function(t){var n;null==r||r(null==t||null===(n=t.res)||void 0===n?void 0:n.responseURL),e(null==t?void 0:t.jsonpData)})).catch((function(e){var r,n=new Error(e.message);n.res={target:null===(r=e.res)||void 0===r?void 0:r.responseURL},t(n)}));function k(){if(a){var t;try{t=JSON.parse(a.response)}catch(A){t=a.response}null==r||r(a.responseURL),e(t)}}})),e.abrupt("return",m);case 7:case"end":return e.stop()}}),e)}))),Je.apply(this,arguments)}var qe=new Map;var ze=Object.prototype.toString;function We(e){return"string"==typeof e||"[object String]"===ze.call(e)}var Xe=/([\w-]+)?(?:#([\w-]+))?((?:\.(?:[\w-]+))*)/;function Ve(e,t,r){var n,o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:W,a=""===o?"":"_",i=[];e&&(i=null!==(n=Xe.exec(e))&&void 0!==n?n:[]);var s=document.createElement(i[1]||"div");return i[2]&&(s.id=i[2]),i[3]&&(s.className=i[3].replace(/\./g," ".concat(o).concat(a)).trim()),t&&Object.keys(t).forEach((function(e){var r=t[e];void 0!==r&&(/^on\w+$/.test(e)?s[e]=r:s.setAttribute(e,r))})),r&&(We(r)?s.innerHTML=r:r.forEach((function(e){return s.appendChild(e)}))),s}function Ye(e){return e?We(e)?document.querySelector(e):(t=e,("object"===("undefined"==typeof HTMLElement?"undefined":b(HTMLElement))?t instanceof HTMLElement:t&&"object"===b(t)&&1===t.nodeType&&"string"==typeof t.nodeName)?e:null):null;var t}function $e(e){e&&(e.remove?e.remove():e.parentNode&&e.parentNode.removeChild(e))}var Ke=i((function e(t){o(this,e);var r=t.url,n=t.operatorType,a=t.code,i=t.msg,s=t.origin;this.url=r,this.operatorType=n,this.code=a,this.msg=i,this.origin=s})),Qe=function(){return i((function e(){o(this,e),this._events=Object.create(null)}),[{key:"emit",value:function(e){for(var t=this,r=arguments.length,n=new Array(r>1?r-1:0),o=1;o<r;o++)n[o-1]=arguments[o];return!!this._events[e]&&(m(this._events[e]).forEach((function(e){e.apply(t,n)})),!0)}},{key:"on",value:function(e,t){if("function"!=typeof t)throw new TypeError("The event-triggered callback must be a function.");return this._events[e]?this._events[e].push(t):this._events[e]=[t],{dispose:this.off.bind(this,e,t)}}},{key:"once",value:function(e,t){var r=this,n=function(){null==t||t.apply(r),r.off(e,n)};return this.on(e,n)}},{key:"off",value:function(e,t){if(!this._events[e])return this;if(!t)return this._events[e]&&(this._events[e].length=0),this;for(var r=this._events[e].length,n=0;n<r;n++)if(this._events[e][n]===t){this._events[e].splice(n,1);break}return this}},{key:"removeAllListeners",value:function(e){return e?this._events[e]&&(this._events[e].length=0):this._events=Object.create(null),this}}])}(),Ze="[NEOneLogin]",et={log:function(e,t){var r=["info","warn","error"];"string"==typeof e&&-1!==r.indexOf(e)?console&&console[e]("".concat(Ze," ").concat(t)):et.error('[Inner util error]: log "type" must be one of the ['.concat(r.join(", "),"]}]"))},warn:function(e){et.log("warn",e)},error:function(e){et.log("error",e)},assert:function(e,t,r){if(!e)throw null==r||r(t),new Error("".concat(Ze," ").concat(t))}};function tt(e,t){for(var r=0,n=Object.entries(t);r<n.length;r++){var o=g(n[r],2);o[0];var a=o[1];if(a[0]===e)return a}return null}function rt(){return encodeURIComponent(z(function(){var e=window.top||window||{};try{e.navigator.platform}catch(n){e=window}var t="".concat(e.navigator.platform,"@@").concat(e.navigator.userAgent,"@@").concat(e.navigator.appVersion,"@@").concat(e.navigator.cookieEnabled,"@@").concat(e.navigator.cpuClass,"@@").concat(e.navigator.hardwareConcurrency,"@@").concat(e.navigator.language,"@@").concat(e.navigator.plugins,"@@").concat(e.screen.availWidth,"@@").concat(e.navigator.availHeight,"@@").concat(e.screen.colorDepth,"@@").concat(e.Date.getTimezoneOffset),r=e.navigator.userAgent;return r.length>100&&(r=r.substring(0,100)),"".concat(e.navigator.platform,"@@").concat(r,"@@").concat(H(t))}()))}function nt(e){var t={},r=decodeURIComponent(e||window.location.search);return(""!==r?r.substring(1).split("&"):[]).forEach((function(e){if(e){var r=e.split("=");t[r[0]]=r[1]}})),t}function ot(e,t){var r={"M+":e.getMonth()+1,"d+":e.getDate(),"h+":e.getHours(),"m+":e.getMinutes(),"s+":e.getSeconds(),"q+":Math.floor((e.getMonth()+3)/3),"S+":e.getMilliseconds()};for(var n in/(y+)/.test(t)&&(t=t.replace(RegExp.$1,"".concat(e.getFullYear()).substr(4-RegExp.$1.length))),r)new RegExp("(".concat(n,")")).test(t)&&(t=t.replace(RegExp.$1,1===RegExp.$1.length?r[n]:((3===RegExp.$1.length&&"S+"===n?"000":"00")+r[n]).substr("".concat(r[n]).length)));return t}function at(e){return Object.entries(e).map((function(e){var t=g(e,2),r=t[0],n=t[1];return"".concat(r.split(/(?=[A-Z])/).join("-").toLowerCase(),":").concat(n)})).join(";")}function it(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{return JSON.parse(e)}catch(r){return t}}function st(e){return e?e.split("?")[0]:e}function ct(e,t){var r=e;if(("string"==typeof r||"number"==typeof r)&&(r=parseFloat(String(r)),!Number.isNaN(r)))return r.toFixed(t)}function ut(e,t){e&&null!=t&&t.type&&null!=t&&t.name&&null!=t&&t.value&&e.collect(t.type,t.name,t.value)}var lt=window.performance||window.msPerformance||window.webkitPerformance||{},ht=lt&&"getEntriesByName"in lt;function pt(e){if(e&&ht){return function(t){var r=lt.getEntriesByName(t)[0];if(r){var n=function(e){if(!e)return{};var t=document.createElement("a");return t.href=e,{source:e,protocol:t.protocol.replace(":",""),host:t.hostname,port:t.port,query:t.search,hash:t.hash.replace("#",""),path:t.pathname.replace(/^([^/])/,"/$1"),segments:t.pathname.replace(/^\//,"").split("/")}}(t),o=n.path||"-",a=JSON.stringify({tc:ct(r.responseEnd-(r.domainLookupStart||r.connectStart),1),dc:ct(r.domainLookupEnd-r.domainLookupStart,1),cc:ct(r.connectEnd-r.connectStart,1),rc:ct(r.responseStart-r.requestStart,1),rr:ct(r.responseEnd-r.responseStart,1),url:t,host:n.host,https:"https"===n.protocol});e.collectAsync("netPerformance",o,a)}}}}var ft={mode:"float",elements:je,businessId:"",phoneInputStyle:"sub",toastDelay:3e3,toastShow:!0};function dt(e,t){var r=f(f({},ft),e),n=r.businessId;return et.assert(!!n&&/^[a-zA-Z0-9]+$/.test(n),'Correct "businessId" is required',(function(e){ut(t,function(e,t,r){return{type:He[0],name:e,value:JSON.stringify({type:"confErr",conf:t,log:r})}}("businessId",r,e))})),r}function vt(){return f({},arguments.length>0&&void 0!==arguments[0]?arguments[0]:{})}var gt=function(){return i((function e(t){o(this,e),this.oneLogin=t}),[{key:"getToken",value:(n=r(d().mark((function e(t){var r,n,o,a,i;return d().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=null===(r=JSON.parse(t.url||""))||void 0===r?void 0:r.preGetMobileUrl,o=null==n?void 0:n.body,a={version:o.version,openType:o.openType,timestamp:o.timestamp,appId:o.appId,traceId:o.traceId,msgId:o.traceId,sign:o.sign,expandParams:o.expandParams,userInformation:rt(),businessType:"8",authPageType:"1",YDData:{code:"",message:""},CTData:{code:"",message:""},CUData:{code:"",message:""}},e.next=5,this.getYDInfo(a);case 5:return i=e.sent,e.abrupt("return",i);case 7:case"end":return e.stop()}}),e,this)}))),function(e){return n.apply(this,arguments)})},{key:"getYDInfo",value:(t=r(d().mark((function e(t){var r,n,o,a,i,s,c,u;return d().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n={version:t.version,timestamp:t.timestamp,appId:t.appId,businessType:t.businessType,traceId:t.traceId,msgId:t.traceId,sign:t.sign,userInformation:t.userInformation,expandParams:t.expandParams,authPageType:t.authPageType},o="/h5/httpsPreGetmobile",a="".concat("https://verify.cmpassport.com").concat(o),i=null,s={},e.prev=6,e.next=9,Fe({method:"post",url:a,data:n,useBaseUrl:!1},pt(this.oneLogin.netCollector));case 9:s=e.sent,e.next=16;break;case 12:e.prev=12,e.t0=e.catch(6),(i=e.t0).data=new Ke({url:a,operatorType:Ue.CHINA_MOBILE[0],code:Ce[0],msg:Ce[1],origin:e.t0});case 16:if(!i){e.next=20;break}return this.collectLikeError(Ce[0],Ce[1],a,o,"post",n,null===(c=i.res)||void 0===c?void 0:c.response),this.oneLogin.emit(le,i),e.abrupt("return","");case 20:if("103000"===(null===(r=s)||void 0===r||null===(r=r.body)||void 0===r?void 0:r.resultCode)){e.next=28;break}if("170001"!==(null===(u=s)||void 0===u||null===(u=u.body)||void 0===u?void 0:u.resultCode)){e.next=25;break}return this.collectLikeError(Te[0],Te[1],a,o,"post",n,s),this.oneLogin.emit(le,f(f({},i),{},{data:new Ke({url:a,operatorType:Ue.CHINA_MOBILE[0],code:Te[0],msg:Te[1],origin:s})})),e.abrupt("return","");case 25:return this.collectLikeError(Ce[0],Ce[1],a,o,"post",n,s),this.oneLogin.emit(le,f(f({},i),{},{data:new Ke({url:a,operatorType:Ue.CHINA_MOBILE[0],code:Ce[0],msg:Ce[1],origin:s})})),e.abrupt("return","");case 28:return e.abrupt("return",{appId:n.appId,traceId:n.traceId,version:n.version,accessToken:s.body.accessToken,maskPhone:s.body.maskPhone});case 29:case"end":return e.stop()}}),e,this,[[6,12]])}))),function(e){return t.apply(this,arguments)})},{key:"getAccessToken",value:(e=r(d().mark((function e(t){var r,n,o,a,i,s,c,u,l,h,p,f,v,g,m;return d().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t.from!==$.AUTH[0]||t.event!==K[0]){e.next=17;break}return n=t.params,o=n.token,a=n.accessToken,i=n.phone,s=n.expandParams,c=void 0===s?"":s,u=n.appId,l=n.traceId,h=n.version,p={accessToken:a,phone:H(i),userInformation:rt(),expandParams:c},f="/h5/onekeylogin/authGetToken",v="".concat("https://www.cmpassport.com").concat(f),g=new Error,m={},e.next=10,Fe({method:"post",url:v,data:p,useBaseUrl:!1,headers:{interfaceVersion:h,timestamp:ot(new Date,"yyyyMMddhhmmssSSS"),appId:u,businessType:"8",traceId:l,"Content-Type":"application/json"}},pt(this.oneLogin.netCollector));case 10:if(m=e.sent,"103000"===(null===(r=m)||void 0===r?void 0:r.resultCode)){e.next=16;break}return ut(this.oneLogin.collector,{type:Re[0],name:"getAccessToken",value:JSON.stringify({errType:Ee[0],target:v,msg:Ee[1],pathname:f,method:"post",params:p,response:m})}),g.data=new Ke({url:v,operatorType:Ue.CHINA_MOBILE[0],code:Ee[0],msg:Ee[1],origin:m}),this.oneLogin.emit(le,g),e.abrupt("return");case 16:this.oneLogin.emit(pe,{token:o,accessToken:m.data.token});case 17:case"end":return e.stop()}}),e,this)}))),function(t){return e.apply(this,arguments)})},{key:"collectLikeError",value:function(e,t,r,n,o,a,i){ut(this.oneLogin.collector,{type:Re[0],name:"getToken",value:JSON.stringify({errType:e,msg:t,target:r,pathname:n,method:o,params:a,response:i})})}}]);var e,t,n}(),mt=function(){return i((function e(t){o(this,e),this.oneLogin=t}),[{key:"getToken",value:(s=r(d().mark((function e(t){var r,n,o,a,i,s,c,u,l,h,p,f,v,g,m,y;return d().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=t.fp,n=JSON.parse(t.url||JSON.stringify({})),o=n.authorization,a=n.clientId,i=n.reqTime,s=n.signType,e.next=4,this.getAuthUrl({authorization:o,clientId:a,reqTime:i,signType:s,fp:r});case 4:return c=e.sent,u=c.authUrl,l=c.appid,h=c.retUrl,e.next=10,this.getCode({authUrl:u,appid:l});case 10:return p=e.sent,f=p.code,v=p.province,e.next=15,this.getAccessInfo({code:f,province:v,retUrl:h});case 15:if(g=e.sent,m=g.accessToken,y=g.maskPhone,m&&y){e.next=20;break}return e.abrupt("return","");case 20:return e.abrupt("return",{accessToken:m,maskPhone:y});case 21:case"end":return e.stop()}}),e,this)}))),function(e){return s.apply(this,arguments)})},{key:"getAuthUrl",value:(a=r(d().mark((function e(t){var r,n,o,a,i,s,c,u,l,h,p,v,m,y,b,w,k,A,C,E,T,x,_,O;return d().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return o=t.authorization,a=t.clientId,i=t.reqTime,s=t.signType,c=t.fp,u={appKey:a,authenticator:o,ts:i,bwid:c,signType:s},l="/api/atrace/isp",h="".concat("https://hs.wosms.cn").concat(l),p=null,v={},e.prev=6,e.next=9,Fe({method:"get",url:h,data:u,useBaseUrl:!1,headers:{"Content-Type":"application/x-www-form-urlencoded"}},pt(this.oneLogin.netCollector));case 9:v=e.sent,e.next=16;break;case 12:e.prev=12,e.t0=e.catch(6),(p=e.t0).data=new Ke({url:h,operatorType:Ue.CHINA_UNICOM[0],code:xe[0],msg:xe[1],origin:e.t0});case 16:if(!p){e.next=20;break}return this.collectLikeError(xe[0],xe[1],h,l,"get",u,null===(m=p.res)||void 0===m?void 0:m.response),this.oneLogin.emit(le,p),e.abrupt("return",{authUrl:""});case 20:if("0000"===(null===(r=v)||void 0===r?void 0:r.code)){e.next=28;break}if("1002"!==(null===(y=v)||void 0===y?void 0:y.code)){e.next=25;break}return this.collectLikeError(Ie[0],Ie[1],h,l,"get",u,v),this.oneLogin.emit(le,f(f({},p),{},{data:new Ke({url:h,operatorType:Ue.CHINA_UNICOM[0],code:Ie[0],msg:Ie[1],origin:v})})),e.abrupt("return",{authUrl:""});case 25:return this.collectLikeError(xe[0],xe[1],h,l,"get",u,v),this.oneLogin.emit(le,f(f({},p),{},{data:new Ke({url:h,operatorType:Ue.CHINA_UNICOM[0],code:xe[0],msg:xe[1],origin:v})})),e.abrupt("return",{authUrl:""});case 28:return b=(null===(n=v.url)||void 0===n?void 0:n.split("?"))||[],w=g(b,2),k=w[0],A=w[1],C=nt("?".concat(A)),E=C.appid,T={appid:E},x="",e.prev=32,e.next=35,Fe({method:"jsonp",url:k,data:T,callbackName:"a",useBaseUrl:!1,headers:{"Content-Type":"application/json"}},pt(this.oneLogin.netCollector));case 35:_=e.sent,x=_.authurl||"",e.next=43;break;case 39:e.prev=39,e.t1=e.catch(32),(p=e.t1).data=new Ke({url:k,operatorType:Ue.CHINA_UNICOM[0],code:xe[0],msg:xe[1],origin:e.t1});case 43:if(!p){e.next=47;break}return this.collectLikeError(xe[0],xe[1],k,new URL(k).pathname,"jsonp",T,null===(O=p.res)||void 0===O?void 0:O.response),this.oneLogin.emit(le,p),e.abrupt("return",{authUrl:""});case 47:if(x){e.next=51;break}return this.collectLikeError(xe[0],xe[1],k,new URL(k).pathname,"jsonp",T,_),this.oneLogin.emit(le,f(f({},p),{},{data:new Ke({url:k,operatorType:Ue.CHINA_UNICOM[0],code:xe[0],msg:xe[1],origin:_})})),e.abrupt("return",{authUrl:""});case 51:return e.abrupt("return",{authUrl:x,appid:E,retUrl:window.atob(v.url.split("&ret_url=")[1])});case 52:case"end":return e.stop()}}),e,this,[[6,12],[32,39]])}))),function(e){return a.apply(this,arguments)})},{key:"getCode",value:(n=r(d().mark((function e(t){var r,n,o,a,i,s,c;return d().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r=t.authUrl,n=t.appid,r&&n){e.next=3;break}return e.abrupt("return",{code:"",province:""});case 3:return o="/api",a="".concat(r).concat(o),i=null,s={appid:n},c={},e.prev=8,e.next=11,Fe({method:"jsonp",url:a,data:s,callbackName:"a",useBaseUrl:!1,headers:{"Content-Type":"application/json"}},pt(this.oneLogin.netCollector));case 11:c=e.sent,e.next=18;break;case 14:e.prev=14,e.t0=e.catch(8),(i=e.t0).data=new Ke({url:a,operatorType:Ue.CHINA_UNICOM[0],code:_e[0],msg:_e[1],origin:e.t0});case 18:if(!i){e.next=22;break}return this.collectLikeError(_e[0],_e[1],a,o,"jsonp",s),this.oneLogin.emit(le,i),e.abrupt("return",{code:"",province:""});case 22:if(c.code&&c.province){e.next=26;break}return this.collectLikeError(_e[0],_e[1],a,o,"jsonp",s,c),this.oneLogin.emit(le,f(f({},i),{},{data:new Ke({url:a,operatorType:Ue.CHINA_UNICOM[0],code:_e[0],msg:_e[1],origin:c})})),e.abrupt("return",{code:"",province:""});case 26:return e.abrupt("return",{code:c.code||"",province:c.province});case 27:case"end":return e.stop()}}),e,this,[[8,14]])}))),function(e){return n.apply(this,arguments)})},{key:"getAccessInfo",value:(t=r(d().mark((function e(t){var r,n,o,a,i,s,c,u,l,h,p,v=this;return d().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(a=t.code,i=t.province,s=t.retUrl,a&&i&&s){e.next=3;break}return e.abrupt("return",{accessToken:"",maskPhone:""});case 3:return c={code:a,province:i},u=null,l={},e.prev=6,e.next=9,Fe({method:"get",url:s,data:c,useBaseUrl:!1,headers:{"Content-Type":"application/x-www-form-urlencoded"}},pt(this.oneLogin.netCollector));case 9:l=e.sent,e.next=16;break;case 12:e.prev=12,e.t0=e.catch(6),(u=e.t0).data=new Ke({url:s,operatorType:Ue.CHINA_UNICOM[0],code:Oe[0],msg:Oe[1],origin:e.t0});case 16:if(h=function(e){ut(v.oneLogin.collector,{type:Re[0],name:"getAccessToken",value:JSON.stringify({errType:Oe[0],target:s,msg:Oe[1],pathname:new URL(s).pathname,method:"get",params:c,response:e})})},!u){e.next=21;break}return h(null===(p=u.res)||void 0===p?void 0:p.response),this.oneLogin.emit(le,u),e.abrupt("return",{accessToken:"",maskPhone:""});case 21:if("0000"===(null===(r=l)||void 0===r?void 0:r.code)){e.next=25;break}return h(l),this.oneLogin.emit(le,f(f({},u),{},{data:new Ke({url:s,operatorType:Ue.CHINA_UNICOM[0],code:Oe[0],msg:Oe[1],origin:l})})),e.abrupt("return",{accessToken:"",maskPhone:""});case 25:return e.abrupt("return",{accessToken:(null===(n=l)||void 0===n?void 0:n.accessCode)||"",maskPhone:(null===(o=l)||void 0===o?void 0:o.pmobile)||""});case 26:case"end":return e.stop()}}),e,this,[[6,12]])}))),function(e){return t.apply(this,arguments)})},{key:"getAccessToken",value:(e=r(d().mark((function e(t){var r,n,o,a;return d().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t.from===$.AUTH[0]&&t.event===K[0]&&(r=t.params,n=r.token,o=r.accessToken,a=r.phone,this.oneLogin.emit(pe,{token:n,accessToken:"".concat(o,"_").concat(a)}));case 1:case"end":return e.stop()}}),e,this)}))),function(t){return e.apply(this,arguments)})},{key:"collectLikeError",value:function(e,t,r,n,o,a,i){ut(this.oneLogin.collector,{type:Re[0],name:"getToken",value:JSON.stringify({errType:e,msg:t,target:r,pathname:n,method:o,params:a,response:i})})}}]);var e,t,n,a,s}(),yt="https://open.e.189.cn",bt=function(){return i((function e(t){o(this,e),this.oneLogin=t}),[{key:"getToken",value:(a=r(d().mark((function e(t){var r,n,o,a,i,s,c,u;return d().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=JSON.parse(t.url||JSON.stringify({})),this.callbackName=r.callback,e.next=4,this.getPreUrl(r);case 4:if(n=e.sent,o=n.preUrl,a=n.params,o||0!==Object.keys(a).length){e.next=9;break}return e.abrupt("return","");case 9:return e.prev=9,e.next=12,this.pingPreUrl(o,a);case 12:if(i=e.sent,s=i.tn,c=i.clientId,u=i.maskPhone,s){e.next=18;break}return e.abrupt("return","");case 18:return e.abrupt("return",{tn:s,clientId:c,maskPhone:u});case 21:e.prev=21,e.t0=e.catch(9),this.oneLogin.emit(le,{data:new Ke({url:o,operatorType:Ue.CHINA_TELECOM[0],code:Se[0],msg:Se[1],origin:e.t0})});case 24:return e.abrupt("return","");case 25:case"end":return e.stop()}}),e,this,[[9,21]])}))),function(e){return a.apply(this,arguments)})},{key:"getPreUrl",value:(n=r(d().mark((function e(t){var r,n,o,a,i,s,c,u,l,h,p,v,g,m,y,b,w;return d().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return o=t.clientId,a=t.clientType,i=t.finger,s=t.format,c=t.seqNum,u=t.timeStamp,l=t.version,h=t.bussinessType,p=t.sign,v={clientId:o,clientType:a,finger:i,format:s,seqNum:c,timeStamp:u,version:l,bussinessType:h,sign:p},g="/gw/gbcs/jssdk/getPreUrl.do",m="".concat(yt).concat(g),y=null,b={},e.prev=6,e.next=9,Fe({method:"jsonp",url:m,data:v,callbackName:this.callbackName,useBaseUrl:!1,timeout:8e3},pt(this.oneLogin.netCollector));case 9:b=e.sent,e.next=16;break;case 12:e.prev=12,e.t0=e.catch(6),(y=e.t0).data=new Ke({url:m,operatorType:Ue.CHINA_TELECOM[0],code:Se[0],msg:Se[1],origin:e.t0});case 16:if(!y){e.next=20;break}return this.collectLikeError(Se[0],Se[1],m,g,"jsonp",v),this.oneLogin.emit(le,y),e.abrupt("return",{preUrl:"",params:{}});case 20:if("0"===(null===(r=b)||void 0===r?void 0:r.result)){e.next=28;break}if("-30010"!==(null===(w=b)||void 0===w?void 0:w.result)){e.next=25;break}return this.collectLikeError(Ne[0],Ne[1],m,g,"jsonp",v,b),this.oneLogin.emit(le,f(f({},y),{},{data:new Ke({url:m,operatorType:Ue.CHINA_TELECOM[0],code:Ne[0],msg:Ne[1],origin:b})})),e.abrupt("return",{preUrl:"",params:{}});case 25:return this.collectLikeError(Se[0],Se[1],m,g,"jsonp",v,b),this.oneLogin.emit(le,f(f({},y),{},{data:new Ke({url:m,operatorType:Ue.CHINA_TELECOM[0],code:Se[0],msg:Se[1],origin:b})})),e.abrupt("return",{preUrl:"",params:{}});case 28:return e.abrupt("return",{preUrl:(null===(n=b)||void 0===n?void 0:n.preUrl)||"",params:v});case 29:case"end":return e.stop()}}),e,this,[[6,12]])}))),function(e){return n.apply(this,arguments)})},{key:"pingPreUrl",value:function(e,t){var n=this;if(e)return new Promise((function(o,a){var i=n,s=new Image;s.src=e,s.onload=r(d().mark((function e(){var r;return d().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,i.getPreData(t);case 2:r=e.sent,o(r);case 4:case"end":return e.stop()}}),e)}))),s.onerror=function(t){a(new Ke({url:e,operatorType:Ue.CHINA_TELECOM[0],code:Se[0],msg:Se[1],origin:t}))}}))}},{key:"getPreData",value:(t=r(d().mark((function e(t){var r,n,o,a,i,s,c,u;return d().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n="/gw/gbcs/jssdk/getPreData.do",o="".concat(yt).concat(n),a=null,i={},e.prev=4,e.next=7,Fe({method:"jsonp",url:o,data:t,callbackName:this.callbackName,useBaseUrl:!1,timeout:8e3},pt(this.oneLogin.netCollector));case 7:i=e.sent,e.next=14;break;case 10:e.prev=10,e.t0=e.catch(4),(a=e.t0).data=new Ke({url:o,operatorType:Ue.CHINA_TELECOM[0],code:Se[0],msg:Se[1],origin:e.t0});case 14:if(!a){e.next=18;break}return this.collectLikeError(Se[0],Se[1],o,n,"jsonp",t),this.oneLogin.emit(le,a),e.abrupt("return",{tn:""});case 18:if("10000"===(null===(r=i)||void 0===r?void 0:r.result)){e.next=22;break}return this.collectLikeError(Se[0],Se[1],o,n,"jsonp",t,i),this.oneLogin.emit(le,f(f({},a),{},{data:new Ke({url:o,operatorType:Ue.CHINA_TELECOM[0],code:Se[0],msg:Se[1],origin:i})})),e.abrupt("return",{tn:""});case 22:return s=this.checkSign(i||{}),c=s.tn,u=s.number,e.abrupt("return",{tn:c,clientId:t.clientId,maskPhone:u});case 24:case"end":return e.stop()}}),e,this,[[4,10]])}))),function(e){return t.apply(this,arguments)})},{key:"checkSign",value:function(e){if("string"==typeof e.data){var t=atob(e.data),r=H(t);if(e.sign===r)return JSON.parse(t)}return e}},{key:"getAccessToken",value:(e=r(d().mark((function e(t){var r,n,o,a,i,s,c,u,l,h,p,f;return d().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t.from!==$.AUTH[0]||t.event!==K[0]){e.next=17;break}return n=t.params,o=n.token,a=n.tn,i=n.clientId,s=n.phone,c=(new Date).getTime(),u={tn:a,clientId:i,sphone:H(s),timeStamp:c,format:"jsonp"},l="/gw/gbcs/jssdk/auth.do",h="".concat(yt).concat(l),p=new Error,f={},e.next=10,Fe({method:"jsonp",url:h,data:u,useBaseUrl:!1,callbackName:"js_callback_".concat(c)},pt(this.oneLogin.netCollector));case 10:if(f=e.sent,"10000"===(null===(r=f)||void 0===r?void 0:r.result)){e.next=16;break}return ut(this.oneLogin.collector,{type:Re[0],name:"getAccessToken",value:JSON.stringify({errType:Le[0],target:h,msg:Le[1],pathname:l,method:"jsonp",params:u,response:f})}),p.data=new Ke({url:h,operatorType:Ue.CHINA_MOBILE[0],code:Le[0],msg:Le[1],origin:f}),this.oneLogin.emit(le,p),e.abrupt("return");case 16:this.oneLogin.emit(pe,{token:o,accessToken:f.accessCode});case 17:case"end":return e.stop()}}),e,this)}))),function(t){return e.apply(this,arguments)})},{key:"collectLikeError",value:function(e,t,r,n,o,a,i){ut(this.oneLogin.collector,{type:Re[0],name:"getToken",value:JSON.stringify({errType:e,msg:t,target:r,pathname:n,method:o,params:a,response:i})})}}]);var e,t,n,a}();function wt(e,t){switch(e){case Ue.CHINA_MOBILE[0]:return new gt(t);case Ue.CHINA_UNICOM[0]:return new mt(t);case Ue.CHINA_TELECOM[0]:return new bt(t);default:throw new Error("当前仅支持移动/联通/电信")}}function kt(e){var t,r;t=Array.prototype.forEach,r=Array.prototype.map,this.each=function(e,r,n){if(null!==e)if(t&&e.forEach===t)e.forEach(r,n);else if(e.length===+e.length){for(var o=0,a=e.length;o<a;o++)if(r.call(n,e[o],o,e)==={})return}else for(var i in e)if(e.hasOwnProperty(i)&&r.call(n,e[i],i,e)==={})return},this.map=function(e,t,n){var o=[];return null==e?o:r&&e.map===r?e.map(t,n):(this.each(e,(function(e,r,a){o[o.length]=t.call(n,e,r,a)})),o)},"object"==b(e)?(this.hasher=e.hasher,this.screen_resolution=e.screen_resolution,this.canvas=e.canvas,this.ie_activex=e.ie_activex):"function"==typeof e&&(this.hasher=e)}kt.prototype={get:function(){var e=[];(e.push(navigator.userAgent),e.push(navigator.language),e.push(screen.colorDepth),this.screen_resolution)&&(void 0!==this.getScreenResolution()&&e.push(this.getScreenResolution().join("x")));return e.push((new Date).getTimezoneOffset()),e.push(this.hasSessionStorage()),e.push(this.hasLocalStorage()),e.push(!!window.indexedDB),document.body?e.push(b(document.body.addBehavior)):e.push("undefined"),e.push(b(window.openDatabase)),e.push(navigator.cpuClass),e.push(navigator.platform),e.push(navigator.doNotTrack),e.push(this.getPluginsString()),this.canvas&&this.isCanvasSupported()&&e.push(this.getCanvasFingerprint()),this.hasher?this.hasher(e.join("###"),31):this.murmurhash3_32_gc(e.join("###"),31)},murmurhash3_32_gc:function(e,t){var r,n,o,a,i,s,c,u;for(r=3&e.length,n=e.length-r,o=t,i=3432918353,s=461845907,u=0;u<n;)c=255&e.charCodeAt(u)|(255&e.charCodeAt(++u))<<8|(255&e.charCodeAt(++u))<<16|(255&e.charCodeAt(++u))<<24,++u,o=27492+(65535&(a=5*(65535&(o=(o^=c=(65535&(c=(c=(65535&c)*i+(((c>>>16)*i&65535)<<16)&4294967295)<<15|c>>>17))*s+(((c>>>16)*s&65535)<<16)&4294967295)<<13|o>>>19))+((5*(o>>>16)&65535)<<16)&4294967295))+((58964+(a>>>16)&65535)<<16);switch(c=0,r){case 3:c^=(255&e.charCodeAt(u+2))<<16;case 2:c^=(255&e.charCodeAt(u+1))<<8;case 1:o^=c=(65535&(c=(c=(65535&(c^=255&e.charCodeAt(u)))*i+(((c>>>16)*i&65535)<<16)&4294967295)<<15|c>>>17))*s+(((c>>>16)*s&65535)<<16)&4294967295}return o^=e.length,o=2246822507*(65535&(o^=o>>>16))+((2246822507*(o>>>16)&65535)<<16)&4294967295,o=3266489909*(65535&(o^=o>>>13))+((3266489909*(o>>>16)&65535)<<16)&4294967295,(o^=o>>>16)>>>0},hasLocalStorage:function(){try{return!!window.localStorage}catch(e){return!0}},hasSessionStorage:function(){try{return!!window.sessionStorage}catch(e){return!0}},isCanvasSupported:function(){var e=document.createElement("canvas");return!(!e.getContext||!e.getContext("2d"))},isIE:function(){return"Microsoft Internet Explorer"===navigator.appName||!("Netscape"!==navigator.appName||!/Trident/.test(navigator.userAgent))},getPluginsString:function(){return this.isIE()&&this.ie_activex?this.getIEPluginsString():this.getRegularPluginsString()},getRegularPluginsString:function(){return this.map(navigator.plugins,(function(e){var t=this.map(e,(function(e){return[e.type,e.suffixes].join("~")})).join(",");return[e.name,e.description,t].join("::")}),this).join(";")},getIEPluginsString:function(){if(window.ActiveXObject){return this.map(["ShockwaveFlash.ShockwaveFlash","AcroPDF.PDF","PDF.PdfCtrl","QuickTime.QuickTime","rmocx.RealPlayer G2 Control","rmocx.RealPlayer G2 Control.1","RealPlayer.RealPlayer(tm) ActiveX Control (32-bit)","RealVideo.RealVideo(tm) ActiveX Control (32-bit)","RealPlayer","SWCtl.SWCtl","WMPlayer.OCX","AgControl.AgControl","Skype.Detection"],(function(e){try{return new ActiveXObject(e),e}catch(t){return null}})).join(";")}return""},getScreenResolution:function(){return[screen.height,screen.width]},getCanvasFingerprint:function(){var e=document.createElement("canvas"),t=e.getContext("2d"),r="https://id.189.cn";return t.textBaseline="top",t.font="14px 'Arial'",t.textBaseline="alphabetic",t.fillStyle="#f60",t.fillRect(125,1,62,20),t.fillStyle="#069",t.fillText(r,2,15),t.fillStyle="rgba(102, 204, 0, 0.7)",t.fillText(r,4,17),e.toDataURL()}};var At=function(){return At=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},At.apply(this,arguments)};var Ct,Et={userData:null,name:location.hostname+"_snaker",init:function(){if(!Et.userData)try{Et.userData=document.createElement("INPUT"),Et.userData.type="hidden",Et.userData.style.display="none",Et.userData.addBehavior("#default#userData"),Et.userData&&document.body.appendChild(Et.userData);var e=new Date;e.setDate(e.getDate()+365),Et.userData.expires=e.toUTCString()}catch(t){return console.log("userData is disabled!"),!1}return!0},setItem:function(e,t){Et.init()&&Et.userData&&(Et.userData.load(Et.name),Et.userData.setAttribute(e,t),Et.userData.save(Et.name))},getItem:function(e){return Et.init()&&Et.userData?(Et.userData.load(Et.name),Et.userData.getAttribute(e)||""):""},removeItem:function(e){Et.init()&&Et.userData&&(Et.userData.load(Et.name),Et.userData.removeAttribute(e),Et.userData.save(Et.name))}};try{Ct=localStorage||Et}catch(It){Ct=Et}var Tt=function(){function e(e){this.name=e}return e.prototype.push=function(e){if(e)try{var t=Ct.getItem(this.name);Ct.setItem(this.name,t?t+","+e:e)}catch(r){console.log("localstorage or userData is disabled!")}},e.prototype.length=function(){try{var e=Ct.getItem(this.name)||"";return e?e.split(",").length:0}catch(t){return console.log("localstorage or userData is disabled!"),0}},e.prototype.pop=function(e){var t;void 0===e&&(e=1);try{var r=Ct.getItem(this.name),n=r?r.split(","):[];t=n.splice(0,e),Ct.setItem(this.name,n.join(","))}catch(o){t=[],console.log("localstorage or userData is disabled!")}return t},e.prototype.clear=function(){try{Ct.removeItem(this.name)}catch(e){console.log("localstorage or userData is disabled!")}},e}(),xt=function(){function e(e){if(!e.pid)throw new Error("product id is required!");var t=e.pid,r=e.bid,n=e.url,o=e.random,a=e.limit,i=e.disabled,s=e.version;this.pid=t,this.bid=r,this.random=o||1,this.limit=a||5,this.disabled=i,this.version=s||"",this.url=n||"https://da.dun.163.com/sn.gif",this.prefix="__snaker__id",this.cache=new Tt(this.prefix);var c,u,l=(c=this.prefix,(u=new RegExp("(^|;)[ ]*"+c+"=([^;]*)").exec(document.cookie))?decodeURIComponent(u[2]):"");l?this.uuid=l:(this.uuid=function(){for(var e="abcdefghijklmnopqrstuvwxyz0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ",t="",r=0;r<16;r++)t+=e.charAt(Math.floor(62*Math.random()));return t}(),function(e,t,r){var n,o=e+"="+encodeURIComponent(t)+";";r&&((n=new Date).setTime(n.getTime()+r),o+="expires="+n.toUTCString()),document.cookie=o}(this.prefix,this.uuid,31536e6))}return e.prototype.setUser=function(e){if("string"==typeof e)this.user={uid:e};else for(var t in this.user={uid:e.uid},e)e.hasOwnProperty(t)&&"uid"!==t&&(this.user["$user_"+t]=e[t])},e.prototype.serialize=function(e,t){var r,n,o=this,a=o.pid,i=o.bid,s=o.uuid,c=o.user,u=o.version,l=e.type,h=e.name,p=e.value,f=screen.width+"x"+screen.height,d=(r=location.href,n=200,r.substring(0,n)),v=(new Date).getTime()+"",g=At(At({pid:a,bid:i,uuid:s,type:l,name:h,version:u,value:p,res:f,pu:d,nts:v},t),c),m=[];for(var y in g)g.hasOwnProperty(y)&&void 0!==g[y]&&m.push(encodeURIComponent(y+"=")+encodeURIComponent(encodeURIComponent(g[y])));return m.join("%26")},e.prototype.sendRequest=function(e,t){this.disabled||(new Image(1,1).src=e+"?d="+t)},e.prototype.report=function(e,t,r,n,o){if(!(this.disabled||this.random<Math.random())){var a=this.serialize({type:e,name:t,value:r},o||{});n?(this.cache.push(a),this.cache.length()>=this.limit&&this.flush()):this.sendRequest(this.url,a)}},e.prototype.track=function(e,t,r,n){this.report(e,t,r,!1,n)},e.prototype.trackAsync=function(e,t,r,n){this.report(e,t,r,!0,n)},e.prototype.flush=function(){for(var e="",t=this.cache.pop(this.limit);t.length;){var r=t.pop()||"";r&&((e=e?e+","+r:r).length>1800&&(this.sendRequest(this.url,e),e=""))}e&&this.sendRequest(this.url,e)},e}(),_t=function(){return i((function e(t){o(this,e),this.snaker=new xt(f(f({},t),{},{pid:"quickpass",version:"3.1.3"})),this.extraParams={tt:"web",dataVersion:"v1"}}),[{key:"collect",value:(t=r(d().mark((function e(t,r,n){return d().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.snaker.track(t,r,n,f({},this.extraParams));case 2:case"end":return e.stop()}}),e,this)}))),function(e,r,n){return t.apply(this,arguments)})},{key:"collectAsync",value:(e=r(d().mark((function e(t,r,n){return d().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.snaker.trackAsync(t,r,n,f({},this.extraParams));case 2:case"end":return e.stop()}}),e,this)}))),function(t,r,n){return e.apply(this,arguments)})}]);var e,t}(),Ot=function(e){function t(e){var r;o(this,t),s(r=n(this,t),"hasTimeout",!1),s(r,"handleProcessMessage",(function(e){r.processMessage(e,r.servicer)}));var a=e.buriedDisabled,i=e.buriedUrl,c=e.businessId;return r.collector=a?void 0:new _t({bid:c,url:i}),r.netCollector=a?void 0:new _t({bid:c,url:i,limit:9,random:.3}),r.options=dt(e,r.collector),r}return l(t,e),i(t,[{key:"sendMessage",value:function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};null===(t=this.authIframe)||void 0===t||null===(t=t.contentWindow)||void 0===t||t.postMessage(JSON.stringify({from:$.MAIN[0],event:e,options:r.options,events:r.events}),"*")}},{key:"getAuthIframeUrl",value:function(e,t){var r,n=this.options,o=n.mode,a=n.iframePath,i=n.elements,s=n.toastShow,c=a||"".concat("https://ye.dun.163yun.com/oneclick","/auth-page_").concat("3.1.3","/index.html"),u=i;Array.isArray(u)&&u||(u=je);var l=f(f({},t),{},{token:e,mode:o,elements:u.join(","),toastShow:s?V:Y});return r="".concat(c,"?").concat(function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t="",r=Object.entries(e);return r.forEach((function(e,n){t+="".concat(e[0],"=").concat(e[1]),n<r.length-1&&(t+="&")})),t}(l)),r}},{key:"preCheck",value:(v=r(d().mark((function e(){var t,r,n,o,a,i,s,c,u,l,h,p,v,g,m,y,b,w;return d().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this.options,r=t.businessId,n=t.apiServer,o=(new kt).get(),a={businessId:r,timestamp:Date.now(),envType:be,nonce:j(32),operatorType:Ue.UNKNOWN[0],extData:JSON.stringify({cm:{v:"2.0",userInformation:rt()},cu:{},ct:{callback:"fjs_callback",bussinessType:"jq",seqNum:j(32),finger:o}})},i=D(JSON.stringify(a)),s=i.key,c=i.eyptStr,u=i.iv,l={d:c,rk:u+s,version:"v1.0",versionType:"v3.0"},h="/v1/oneclick/preCheck",p=null,v={},e.prev=8,e.next=11,Fe({method:"get",url:h,data:l,headers:vt(),apiServer:n},pt(this.netCollector));case 11:v=e.sent,e.next=18;break;case 14:e.prev=14,e.t0=e.catch(8),(p=e.t0).data=new Ke(f(f({},e.t0.data),{},{url:h,code:Ae[0],msg:Ae[1]}));case 18:if(!p){e.next=22;break}return ut(this.collector,{type:Re[0],name:"preCheck",value:JSON.stringify({errType:Ae[0],target:st(null===(g=p.res)||void 0===g?void 0:g.target),msg:Ae[1],pathname:h,method:"get",status:null===(m=p.res)||void 0===m?void 0:m.status,params:a,response:it(null===(y=p.res)||void 0===y?void 0:y.response,null===(b=p.res)||void 0===b?void 0:b.response)})}),this.emit(le,p),e.abrupt("return","");case 22:if(200===v.code){e.next=26;break}return ut(this.collector,{type:Re[0],name:"preCheck",value:JSON.stringify({errType:v.code,msg:v.msg,pathname:h,method:"get",status:200,params:a,response:v})}),this.emit(le,f(f({},p),{},{data:{url:h,code:v.code,msg:v.msg}})),e.abrupt("return","");case 26:return w=f(f({},it(M(v.data,s,u))),{},{fp:o}),e.abrupt("return",w);case 28:case"end":return e.stop()}}),e,this,[[8,14]])}))),function(){return v.apply(this,arguments)})},{key:"getToken",value:(p=r(d().mark((function e(){var t,r,n,o,a=this;return d().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if("number"==typeof(t=this.options.timeout)&&0!==t){e.next=5;break}return e.next=4,this._getToken();case 4:return e.abrupt("return");case 5:return r=new Promise((function(e){setTimeout((function(){return e(!0)}),t)})),n=new Promise((function(e){var t=function(){return e(!1)};a._getToken().then(t).catch(t)})),this.hasTimeout=!1,e.next=10,Promise.race([n,r]);case 10:if(e.sent){e.next=13;break}return e.abrupt("return");case 13:this.removeAuthFrame(),o={data:new Ke({url:"",operatorType:Ue.UNKNOWN[0],code:ke[0],msg:ke[1]})},ut(this.collector,{type:Re[0],name:"getToken",value:JSON.stringify({errType:ke[0],msg:ke[1]})}),this.emit(le,o),this.hasTimeout=!0;case 18:case"end":return e.stop()}}),e,this)}))),function(){return p.apply(this,arguments)})},{key:"_getToken",value:(h=r(d().mark((function e(){var t,r,n,o,a,i,s,c,u,l,h,p,v=this;return d().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.preCheck();case 2:if(t=e.sent){e.next=5;break}return e.abrupt("return");case 5:if((r=t.ot)&&r!==Ue.UNKNOWN[0]){e.next=10;break}return ut(this.collector,{type:Re[0],name:"preCheck",value:JSON.stringify({errType:Pe[0],msg:Pe[1],response:t})}),this.emit(le,{data:new Ke({url:"",operatorType:Ue.UNKNOWN[0],code:Pe[0],msg:Pe[1]})}),e.abrupt("return");case 10:return this.servicer=wt(r,this),n=this.options,o=n.mode,a=n.popupContainer,i=n.iframeStyles,s=n.__preview__,this.authIframe=Ve("iframe".concat(X),{},void 0,""),(c=Ye(X))&&($e(c),c=null),"popup"===o&&a?(u=Ye(a),l="display:none;".concat(at(f(f({},Me),i))),this.authIframe.style.cssText=l,u&&u.appendChild(this.authIframe)):(h="display:none;".concat(at(f(f({},De),i))),this.authIframe.style.cssText=h,document.body.appendChild(this.authIframe)),e.next=18,this.servicer.getToken(t);case 18:if(p=e.sent,s&&(p={maskPhone:"178xxxx7822"}),""!==p){e.next=23;break}return this.removeAuthFrame(),e.abrupt("return");case 23:if(!this.hasTimeout){e.next=26;break}return this.removeAuthFrame(),e.abrupt("return");case 26:this.emit(ve,p.maskPhone),this.authIframe.src=this.getAuthIframeUrl(t.token,p),this.authIframe.addEventListener("load",(function(){var e=tt(r,Ue)||[];v.sendMessage(ae[0],{options:f(f({},v.options),{},{policy:[{content:e[2],url:e[3]}].concat(m(v.options.policy||[]))})}),v.emit(he)})),window.addEventListener("message",this.handleProcessMessage,!1);case 30:case"end":return e.stop()}}),e,this)}))),function(){return h.apply(this,arguments)})},{key:"processMessage",value:(a=r(d().mark((function e(t,r){var n,o,a,i;return d().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if((n=it(t.data)).from===$.AUTH[0]){e.next=3;break}return e.abrupt("return");case 3:e.t0=n.event,e.next=e.t0===K[0]?6:e.t0===Q[0]?15:e.t0===Z[0]?18:e.t0===ee[0]?20:e.t0===te[0]?22:e.t0===re[0]?24:e.t0===ne[0]?27:e.t0===oe[0]?29:32;break;case 6:return e.prev=6,e.next=9,r.getAccessToken(n);case 9:e.next=14;break;case 11:e.prev=11,e.t1=e.catch(6),this.emit(le,e.t1);case 14:return e.abrupt("break",33);case 15:return this.emit(fe),this.disposeAuthFrame(),e.abrupt("break",33);case 18:return this.emit(de),e.abrupt("break",33);case 20:return this.emit(ge),e.abrupt("break",33);case 22:return this.emit(me),e.abrupt("break",33);case 24:return o=g(n.params,2),a=o[0],i=o[1],this.emit(ye,{code:a,text:i}),e.abrupt("break",33);case 27:return this.removeAuthFrame(),e.abrupt("break",33);case 29:return(n.params||{}).isInit&&this.displayIframe(),e.abrupt("break",33);case 32:return e.abrupt("break",33);case 33:case"end":return e.stop()}}),e,this,[[6,11]])}))),function(e,t){return a.apply(this,arguments)})},{key:"emit",value:function(e){if(this.hasTimeout&&e===le)return!1;for(var r=arguments.length,n=new Array(r>1?r-1:0),o=1;o<r;o++)n[o-1]=arguments[o];return(a=t,i="emit",s=this,h=c(u(1&(l=3)?a.prototype:a),i,s),2&l&&"function"==typeof h?function(e){return h.apply(s,e)}:h)([e].concat(n));var a,i,s,l,h}},{key:"enableLoginButton",value:function(){this.sendMessage(ie[0],{events:[se[0]]})}},{key:"disableLoginButton",value:function(){this.sendMessage(ie[0],{events:[ce[0]]})}},{key:"displayIframe",value:function(){this.authIframe&&(this.authIframe.style.display="")}},{key:"disposeAuthFrame",value:function(){this.sendMessage(ie[0],{events:[ue[0]]})}},{key:"removeAuthFrame",value:function(){this.authIframe&&($e(this.authIframe),this.authIframe=null),window.removeEventListener("message",this.handleProcessMessage,!1)}},{key:"dispose",value:function(){var e,t;e=this,qe.has(e)&&(null===(t=qe.get(e))||void 0===t||t.forEach((function(e){e.dispose()})),qe.delete(e)),this.removeAllListeners(),this.disposeAuthFrame()}}]);var a,h,p,v}(Qe);return Ot}));
