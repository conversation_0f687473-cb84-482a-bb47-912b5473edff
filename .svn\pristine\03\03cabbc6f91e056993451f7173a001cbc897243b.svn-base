var SSO_URL = $CONFIG.SSO_SERVER+"/sso/login/jsonp.do";
var SSO_CK_URL = $CONFIG.SSO_SERVER+"/sso/checkCode.do";

$(document).ready(function(){
	//delete checkcode cookie
	$("#img2").attr("src", SSO_CK_URL+"?d");
});

/**
 * SSO登录
 */
function submitSSO(){
	$("#loginButton").attr('disabled', true).val("").removeClass("sure_login").addClass("sure_login1");
	var params = [];
	params.push('userName='+encodeURIComponent($("input[name=logMember.memberName]").val()));
	params.push('password='+document.getElementById("logMember.memberPwd").value);
	params.push('confirmPassword='+document.getElementById("logMember.memberPwdConfirm").value);
	params.push('checkCode='+$("#checkCode").val());
	params.push('callBack=ssoCallback');
	var loginFrom = $('#site_id').val();
	if (loginFrom==''){
		loginFrom ='basePassport';
	}
	params.push('loginFrom='+loginFrom);//登录来源
	$.getScript(SSO_URL+"?"+params.join("&"));
}
/**
 * 回调函数
 * ${callBack }({'returnUrl':'${returnUrl }','errorMsg':'${errorMsg }','ticket':'${loginResponse.ticketString }'});
 * @param {Object} json
 */
function ssoCallback(json){
	if (json.errorMsg!=''){
		showInfo(json.errorMsg);
		$("#loginButton").attr('disabled', false).val("登录").removeClass("sure_login1").addClass("sure_login");
		//如果错误次数大于3次，则显示验证码
		if (json.checkcodeShow){
			if ($("#checkCodeContainerId:visible").length == 0) {
				login_changeImage();
			}
			$("#checkCodeContainerId").show();
		}
		return;
	}
	//重新提交表单到ssoLogin
	$("#loginFormId").submit();
}