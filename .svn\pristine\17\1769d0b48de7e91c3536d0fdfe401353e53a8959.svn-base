package com.shunwang.basepassport;

import com.shunwang.util.encrypt.Md5Encrypt;
import com.shunwang.util.net.HttpUtil;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.*;

/**
 * User:pf.ma
 * Date:2018/07/06
 * Time:11:20
 */
public abstract class BaseTest {

	private final static String md5Key = "123456" ;
	protected  Map<String,String> params = new HashMap<>() ;

	public abstract void init() throws Exception;

	@Test
	public void test() throws Exception {
		init();
		String respond = "";
//		HttpURLConnection conn = null;

		try {
			respond = HttpUtil.doPost(getUrl(), params,"UTF-8");

//			URL url = new URL(getUrl());
//			conn = (HttpURLConnection) url.openConnection();
//			conn.setDoOutput(true);
//			conn.setUseCaches(false);
//			conn.setRequestMethod(getMethodType());
//
//			conn.getOutputStream().write(getQueryString(params).getBytes());
//			conn.connect();
//
//			BufferedReader reader = new BufferedReader(new InputStreamReader(conn.getInputStream(), "utf-8"));
//			String lines = "";
//
//			while ((lines = reader.readLine()) != null) {
//				respond += lines;
//			}
//			System.out.println(respond);
//			reader.close();
//			conn.disconnect();
		} catch (Exception e) {
			respond = e.getMessage();
		} finally {
//			if (conn != null) {
//				conn.disconnect();
//			}
//			conn = null;
		}

		System.out.println(respond);
	}

	private String getQueryString(Map<String,String> params){
		Set<String> keys = params.keySet();
		List<String> paramsList = new LinkedList<>() ;
		for (String key : keys) {
			paramsList.add(key+"="+(StringUtils.isBlank(params.get(key))?"":params.get(key)));
		}

		return StringUtils.join(paramsList,"&");
	}

	protected String getSign(Map<String,String> params){
		List<String> keys = new ArrayList<String>(params.keySet());
		Collections.sort(keys);
		StringBuilder plainText = new StringBuilder();
		for (String key : keys) {
			if(key.equals("sign") || key.startsWith("t_")){
				//略过sign和以t_开头的参数
				continue;
			}
			plainText.append(params.get(key)).append("|");
		}
		plainText.append(getMd5Key());
		System.out.println(plainText.toString());
		return Md5Encrypt.encrypt(plainText.toString()).toUpperCase() ;
	}
	protected abstract String getUrl() ;

	protected abstract String getMd5Key() ;

	protected String getMethodType(){
		return "POST" ;
	}

	protected boolean getJson(){
		return true ;
	}
}
