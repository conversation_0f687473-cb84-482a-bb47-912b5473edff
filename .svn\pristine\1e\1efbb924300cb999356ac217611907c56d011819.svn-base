package com.shunwang.baseStone.category.web;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.shunwang.baseStone.category.dao.BusinessCategoryDao;
import com.shunwang.baseStone.category.pojo.BusinessCategory;
import com.shunwang.baseStone.core.detail.DetailItem;
import com.shunwang.framework.ibatis.annotation.SingleValue;
import com.shunwang.framework.struts2.action.ConditionCrudAction;

public class BusinessCategoryAction extends ConditionCrudAction<BusinessCategory,BusinessCategoryDao>{

	private static final long serialVersionUID = 976514823276481071L;
	private static final Logger log = LoggerFactory.getLogger(BusinessCategoryAction.class);
	
	private String categoryid;
	private String categoryname;
	private String remark;
	
	private String schCategoryName;
	
	@Override
	public String list() {
		this.setNeedCloth(true);
		return super.list();
	}
	
	@Override
	public String add() {
		BusinessCategory  category = getCrudbo().getById(categoryid);
		if(null!=category&&category.getCategoryid().equals(category.getCategoryid())){
			setMsg("部门ID已存在");
			return SUCCESS;
		}
		
		try {
			BusinessCategory cate = new BusinessCategory();
			cate.beginBuildLog();
			cate.setCategoryid(Integer.valueOf(categoryid));
			cate.setCategoryname(categoryname);
			cate.setRemark(remark);
			String cateinfo ="\n"+"作业单元管理:"+categoryid+"\n"+"作业单元名称："+categoryname+"\n"+"备注："+remark;
			cate.editLog.addItem(new DetailItem("添加作业单元","",cateinfo));
			this.getCrudbo().save(cate);
		} catch (Exception e) {
			this.setMsg("添加数据错误");
			log.error("", e);
		}	    
		return SUCCESS;
	}
	
	@Override
	public String update() {
		try {
			BusinessCategory  category = getCrudbo().getById(categoryid);
			category.beginBuildLog();
			category.setCategoryname(categoryname);
			category.setRemark(remark);
			String cateinfo ="\n"+"作业单元管理:"+categoryid+"\n"+"作业单元名称："+categoryname+"\n"+"备注："+remark;
			category.editLog.addItem(new DetailItem("更新作业单元","",cateinfo));
			this.getCrudbo().update(category);
		} catch (Exception e) {			
			this.setMsg("更新数据错误");
			log.error("", e);
		}
		return SUCCESS;
	}

	@Override
	public String del() {
		try {
			BusinessCategory bussinessCategory = getCrudbo().getById(categoryid);
		    this.getCrudbo().delete(bussinessCategory);
		} catch (Exception e) {
			log.error("", e);
		}
		return SUCCESS;
	}

	public String getCategoryid() {
		return categoryid;
	}

	public void setCategoryid(String categoryid) {
		this.categoryid = categoryid;
	}

	public String getCategoryname() {
		return categoryname;
	}
	
	public void setCategoryname(String categoryname) {
		this.categoryname = categoryname;
	}

	@SingleValue(equal="like")
	public String getSchCategoryName() {
		if(null!=schCategoryName&&!"".equals(schCategoryName))
			return schCategoryName+"%";
		return schCategoryName;
	}

	public void setSchCategoryName(String schCategoryName) {
		this.schCategoryName = schCategoryName;
	}

	public BusinessCategoryAction(){
		this.setNeedCheckExist(true);
	}
	
	protected java.io.Serializable getPk() {
		return categoryid;
	}
	public void setRemark(String value){
		this.remark=value;
	}
	public String getRemark(){
		return this.remark;
	}		

}
