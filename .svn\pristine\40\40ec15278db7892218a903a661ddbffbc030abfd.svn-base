package com.shunwang.basepassport.binder.web;

import com.shunwang.baseStone.context.BaseStoneContext;
import com.shunwang.baseStone.core.exception.BaseStoneException;
import com.shunwang.baseStone.encrypt.Encrypt;
import com.shunwang.baseStone.encrypt.pojo.EncryptItem;
import com.shunwang.basepassport.binder.common.BinderConstants;
import com.shunwang.basepassport.binder.dao.BinderDao;
import com.shunwang.basepassport.binder.exception.IsBindedExp;
import com.shunwang.basepassport.binder.pojo.Binder;
import com.shunwang.basepassport.binder.pojo.EmailActiveNoBinder;
import com.shunwang.basepassport.binder.pojo.EmailBinder;
import com.shunwang.basepassport.binder.pojo.SendBinder;
import com.shunwang.basepassport.commonExp.ParamNotFoundExp;
import com.shunwang.basepassport.commonExp.UserFormateErrorExp;
import com.shunwang.basepassport.user.common.UserCheckUtil;
import com.shunwang.basepassport.user.exception.MsgIllExp;
import com.shunwang.util.lang.StringUtil;

public class EmailSendAction extends SendAction{

	/**
	 * 
	 */
	private static final long serialVersionUID = -1083636026452135364L;
	private String email;
	private String emailContent;
	private String emailTitle;
	private String newEmail;
	private String emailLink;

	/**
	 *  true：邮件内容发送验证码
	 *  false：邮件内容发送链接
	 */
	private boolean sendActiveNo;

	@Override
	public void processTransaction() throws Exception {
		if (!BinderConstants.REGISTER.equals(this.getInterfaceType())) {
			super.process();
		} else {
			doProcess();
		}
	}

	@Override
	public void doProcess() {
		checkFormat();
		SendBinder sendBinder = buildSendBinder();
		if(this.getInterfaceType().equals(BinderConstants.CHANGENUMBER)) {//更换邮箱
			checkIsCouldBind(sendBinder,this.getNewNumber());
			checkIsBindedAndBindNum(sendBinder);
			sendBinder.setNumber(this.getNewNumber());
			this.setNumber(this.getNewNumber());
		} else if(this.getInterfaceType().equals(BinderConstants.BINDNUMBER)) {//绑定邮箱
			if(sendBinder.isBinded())
				throw new IsBindedExp(this.getType());
			checkIsCouldBind(sendBinder,this.getNumber());
			sendBinder.setNumber(this.getNumber());
		} else if (this.getInterfaceType().equals(BinderConstants.REGISTER)) {
			if (!sendActiveNo) {
				throw new BaseStoneException("1100", "邮件内部链接形式不支持注册");
			}
			checkIsCouldBind(sendBinder, this.getNumber());
			sendBinder.setNumber(this.getNumber());
		} else {
			checkIsBindedAndBindNum(sendBinder);
			checkIsBindQuestion(sendBinder);
		}
		checkSendNumber();

		/**
		 * 邮件内容发送验证码
		 */
		if (sendActiveNo) {
			sendBinder = new EmailActiveNoBinder((EmailBinder) sendBinder);
		}

		sendBinder.send();
	}
	
	@Override
	protected void checkFormat() {
		if(!StringUtil.isBlank(this.getEmail())&&!UserCheckUtil.checkEmail(this.getEmail()))
			throw new UserFormateErrorExp(this.getType());
		if(!StringUtil.isBlank(this.getNewEmail())&&!UserCheckUtil.checkEmail(this.getNewEmail()))
			throw new UserFormateErrorExp(BinderConstants.NEW+this.getType());
	}

	@SuppressWarnings("unchecked")
	@Override
	public BinderDao<Binder> getBinderDao() {
		return (BinderDao<Binder>) BaseStoneContext.getInstance().getBean("emailBinderDao");
	}
	
	@Override
	public void checkParam() {
		if(StringUtil.isBlank(this.getSign())||StringUtil.isBlank(this.getInterfaceType())||StringUtil.isBlank(this.getEmailTitle())||StringUtil.isBlank(this.getTime()))
			throw new ParamNotFoundExp();
		super.checkParam();
		if(this.getInterfaceType().equals(BinderConstants.BINDNUMBER)&&StringUtil.isBlank(this.getEmail()))
			throw new ParamNotFoundExp();
		if(this.getInterfaceType().equals(BinderConstants.CHANGENUMBER)&&(StringUtil.isBlank(this.getEmail())||StringUtil.isBlank(this.getNewEmail())))
			throw new ParamNotFoundExp();
		if(!StringUtil.isBlank(this.getEmailLink())&&this.getEmailLink().trim().length()>128)
			throw new MsgIllExp("emailLink");
	}

	@Override
	protected Integer getDoType() {
		return BinderConstants.DOTYPE_EMAIL;
	}

	@Override
	protected String getNewNumber() {
		return this.getNewEmail();
	}

	@Override
	protected String getNumber() {
		return StringUtil.isBlank(this.getEmail())?getMemberInfo().getEmail():this.getEmail();
	}

	@Override
	protected String getType() {
		return BinderConstants.EMAIL;
	}

	@Override
	protected void setNumber(String number) {
		this.setEmail(number);
	}

	@Override
	public String buildSignString() {
		Encrypt  encrypt=new Encrypt();
		encrypt.addItem(new EncryptItem(this.getSiteId()));
		encrypt.addItem(new EncryptItem(this.getUserName(),true));
		encrypt.addItem(new EncryptItem(this.getInterfaceType()));
		encrypt.addItem(new EncryptItem(this.getEmail()));
		encrypt.addItem(new EncryptItem(StringUtil.trimNull(this.getNewEmail())));
		encrypt.addItem(new EncryptItem(StringUtil.trimNull(this.getEmailLink())));
		encrypt.addItem(new EncryptItem(this.getTime()));
		this.setUserName(decodeByUTF(this.getUserName()));
		return encrypt.buildSign();
	}

	@Override
	protected SendBinder buildSendBinder() {
		EmailBinder emailBinder = (EmailBinder) super.buildSendBinder();
		emailBinder.setEmailLink(this.getEmailLink());
		emailBinder.setEmailTitle(decodeByUTF(this.getEmailTitle()));
		emailBinder.setContent(decodeByUTF(this.getEmailContent()));
		return emailBinder;
	}

	@Override
	public String getSiteName() {
		return "发送邮件";
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getEmailContent() {
		return emailContent;
	}

	public void setEmailContent(String emailContent) {
		this.emailContent = emailContent;
	}

	public String getEmailTitle() {
		return emailTitle;
	}

	public void setEmailTitle(String emailTitle) {
		this.emailTitle = emailTitle;
	}

	public String getNewEmail() {
		return newEmail;
	}

	public void setNewEmail(String newEmail) {
		this.newEmail = newEmail;
	}

	public String getEmailLink() {
		return emailLink;
	}

	public void setEmailLink(String emailLink) {
		this.emailLink = emailLink;
	}

	public boolean getSendActiveNo() {
		return sendActiveNo;
	}

	public void setSendActiveNo(boolean sendActiveNo) {
		this.sendActiveNo = sendActiveNo;
	}

	@Override
	protected SendBinder createSendBinder() {
		EmailBinder emailBinder = new EmailBinder();
		emailBinder.setMemberId(getMemberInfo().getMemberId());
		return emailBinder;
	}

}
