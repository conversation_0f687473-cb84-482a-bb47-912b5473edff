<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ArtifactsWorkspaceSettings">
    <artifacts-to-build>
      <artifact name="member-center-sso:war exploded" />
    </artifacts-to-build>
  </component>
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="ALL" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="187630cf-8336-45d8-9d18-11ebcff3bfa8" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="LogFilters">
    <option name="FILTER_ERRORS" value="false" />
    <option name="FILTER_WARNINGS" value="false" />
    <option name="FILTER_INFO" value="true" />
    <option name="FILTER_DEBUG" value="true" />
    <option name="CUSTOM_FILTER" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="$PROJECT_DIR$/../../apache-maven-3.6.3" />
        <option name="localRepository" value="D:\develop\apache-maven-3.6.3\repository" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="D:\develop\apache-maven-3.6.3\conf\setting-shunwang.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 4
}</component>
  <component name="ProjectId" id="2ZW4MPVbbwQuVV5T1vX6FhoSs6q" />
  <component name="ProjectViewState">
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_flatOrder0": "0",
    "FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_flatOrder1": "1",
    "FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_flatOrder2": "2",
    "FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_flatOrder3": "3",
    "FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_flatOrder4": "4",
    "FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_flatOrder5": "5",
    "FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_flatWidth0": "227",
    "FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_flatWidth1": "227",
    "FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_flatWidth2": "227",
    "FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_flatWidth3": "227",
    "FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_flatWidth4": "228",
    "FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_flatWidth5": "227",
    "FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_treeOrder0": "0",
    "FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_treeOrder1": "1",
    "FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_treeOrder2": "2",
    "FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_treeOrder3": "3",
    "FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_treeOrder4": "4",
    "FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_treeOrder5": "5",
    "FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_treeWidth0": "227",
    "FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_treeWidth1": "227",
    "FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_treeWidth2": "227",
    "FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_treeWidth3": "227",
    "FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_treeWidth4": "228",
    "FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_treeWidth5": "227",
    "Remote JVM Debug.Unnamed.executor": "Debug",
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "Tomcat Server.本地sso.executor": "Debug",
    "WebServerToolWindowFactoryState": "false",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "D:/develop/IDEAworkplace/com.shunwang.member.center",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "project.structure.last.edited": "Project",
    "project.structure.proportion": "0.0",
    "project.structure.side.proportion": "0.5",
    "settings.editor.selected.configurable": "MavenSettings",
    "spring.configuration.checksum": "582ed6467c67884d290c8d6266f91b49",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RebelAgentSelection">
    <selection>jr</selection>
  </component>
  <component name="RunManager" selected="Tomcat Server.本地sso">
    <configuration name="Unnamed" type="Remote" nameIsGenerated="true">
      <option name="USE_SOCKET_TRANSPORT" value="true" />
      <option name="SERVER_MODE" value="false" />
      <option name="SHMEM_ADDRESS" />
      <option name="HOST" value="**************" />
      <option name="PORT" value="8787" />
      <option name="AUTO_RESTART" value="false" />
      <RunnerSettings RunnerId="Debug">
        <option name="DEBUG_PORT" value="8787" />
        <option name="LOCAL" value="false" />
      </RunnerSettings>
      <method v="2" />
    </configuration>
    <configuration name="研发base" type="Remote">
      <option name="USE_SOCKET_TRANSPORT" value="true" />
      <option name="SERVER_MODE" value="false" />
      <option name="SHMEM_ADDRESS" />
      <option name="HOST" value="***************" />
      <option name="PORT" value="8787" />
      <option name="AUTO_RESTART" value="false" />
      <RunnerSettings RunnerId="Debug">
        <option name="DEBUG_PORT" value="8787" />
        <option name="LOCAL" value="false" />
      </RunnerSettings>
      <method v="2" />
    </configuration>
    <configuration name="研发interface" type="Remote">
      <option name="USE_SOCKET_TRANSPORT" value="true" />
      <option name="SERVER_MODE" value="false" />
      <option name="SHMEM_ADDRESS" />
      <option name="HOST" value="***************" />
      <option name="PORT" value="8787" />
      <option name="AUTO_RESTART" value="false" />
      <RunnerSettings RunnerId="Debug">
        <option name="DEBUG_PORT" value="8787" />
        <option name="LOCAL" value="false" />
      </RunnerSettings>
      <method v="2" />
    </configuration>
    <configuration name="研发sso" type="Remote">
      <option name="USE_SOCKET_TRANSPORT" value="true" />
      <option name="SERVER_MODE" value="false" />
      <option name="SHMEM_ADDRESS" />
      <option name="HOST" value="***************" />
      <option name="PORT" value="8787" />
      <option name="AUTO_RESTART" value="false" />
      <RunnerSettings RunnerId="Debug">
        <option name="DEBUG_PORT" value="8787" />
        <option name="LOCAL" value="false" />
      </RunnerSettings>
      <method v="2" />
    </configuration>
    <configuration name="测试sso" type="Remote">
      <option name="USE_SOCKET_TRANSPORT" value="true" />
      <option name="SERVER_MODE" value="false" />
      <option name="SHMEM_ADDRESS" />
      <option name="HOST" value="***************" />
      <option name="PORT" value="8787" />
      <option name="AUTO_RESTART" value="false" />
      <RunnerSettings RunnerId="Debug">
        <option name="DEBUG_PORT" value="8787" />
        <option name="LOCAL" value="false" />
      </RunnerSettings>
      <method v="2" />
    </configuration>
    <configuration name="sso测试" type="Remote">
      <option name="USE_SOCKET_TRANSPORT" value="true" />
      <option name="SERVER_MODE" value="false" />
      <option name="SHMEM_ADDRESS" />
      <option name="HOST" value="***************" />
      <option name="PORT" value="8787" />
      <option name="AUTO_RESTART" value="false" />
      <RunnerSettings RunnerId="Debug">
        <option name="DEBUG_PORT" value="8787" />
        <option name="LOCAL" value="false" />
      </RunnerSettings>
      <method v="2" />
    </configuration>
    <configuration name="本地base" type="#com.intellij.j2ee.web.tomcat.TomcatRunConfigurationFactory" factoryName="Local" APPLICATION_SERVER_NAME="Tomcat 8.0.52" ALTERNATIVE_JRE_ENABLED="false">
      <option name="UPDATING_POLICY" value="restart-server" />
      <deployment>
        <artifact name="member-center-base:war exploded">
          <settings>
            <option name="CONTEXT_PATH" value="/" />
          </settings>
        </artifact>
      </deployment>
      <server-settings>
        <option name="BASE_DIRECTORY_NAME" value="700743f9-1a3f-443b-8cfc-b8fd6ea094f1" />
        <option name="HTTP_PORT" value="8091" />
      </server-settings>
      <predefined_log_file enabled="true" id="Tomcat" />
      <predefined_log_file enabled="true" id="Tomcat Catalina" />
      <predefined_log_file id="Tomcat Manager" />
      <predefined_log_file id="Tomcat Host Manager" />
      <predefined_log_file id="Tomcat Localhost Access" />
      <RunnerSettings RunnerId="Debug">
        <option name="DEBUG_PORT" value="54106" />
      </RunnerSettings>
      <RunnerSettings RunnerId="JRebel Debug">
        <option name="DEBUG_PORT" value="54109" />
      </RunnerSettings>
      <ConfigurationWrapper VM_VAR="JAVA_OPTS" RunnerId="Cover">
        <option name="USE_ENV_VARIABLES" value="true" />
        <STARTUP>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </STARTUP>
        <SHUTDOWN>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </SHUTDOWN>
      </ConfigurationWrapper>
      <ConfigurationWrapper VM_VAR="JAVA_OPTS" RunnerId="Debug">
        <option name="USE_ENV_VARIABLES" value="true" />
        <STARTUP>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </STARTUP>
        <SHUTDOWN>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </SHUTDOWN>
        <EnvironmentVariables>
          <option name="NAME" value="BASE_KEDOU_CONFIG_HOME" />
          <option name="VALUE" value="D:\develop\IDEAworkplace\com.shunwang.member.center\member-center-base\src\main\config" />
          <option name="IS_PREDEFINED" value="false" />
        </EnvironmentVariables>
      </ConfigurationWrapper>
      <ConfigurationWrapper VM_VAR="JAVA_OPTS" RunnerId="JRebel Debug">
        <option name="USE_ENV_VARIABLES" value="true" />
        <STARTUP>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </STARTUP>
        <SHUTDOWN>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </SHUTDOWN>
      </ConfigurationWrapper>
      <ConfigurationWrapper VM_VAR="JAVA_OPTS" RunnerId="JRebel Executor">
        <option name="USE_ENV_VARIABLES" value="true" />
        <STARTUP>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </STARTUP>
        <SHUTDOWN>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </SHUTDOWN>
      </ConfigurationWrapper>
      <ConfigurationWrapper VM_VAR="JAVA_OPTS" RunnerId="Profile">
        <option name="USE_ENV_VARIABLES" value="true" />
        <STARTUP>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </STARTUP>
        <SHUTDOWN>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </SHUTDOWN>
      </ConfigurationWrapper>
      <ConfigurationWrapper VM_VAR="JAVA_OPTS" RunnerId="Run">
        <option name="USE_ENV_VARIABLES" value="true" />
        <STARTUP>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </STARTUP>
        <SHUTDOWN>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </SHUTDOWN>
      </ConfigurationWrapper>
      <method v="2">
        <option name="Make" enabled="true" />
        <option name="BuildArtifacts" enabled="true">
          <artifact name="member-center-base:war exploded" />
        </option>
      </method>
    </configuration>
    <configuration name="本地sso" type="#com.intellij.j2ee.web.tomcat.TomcatRunConfigurationFactory" factoryName="Local" APPLICATION_SERVER_NAME="Tomcat 8.0.52" ALTERNATIVE_JRE_ENABLED="false">
      <option name="UPDATING_POLICY" value="restart-server" />
      <deployment>
        <artifact name="member-center-sso:war exploded">
          <settings>
            <option name="CONTEXT_PATH" value="/" />
          </settings>
        </artifact>
      </deployment>
      <server-settings>
        <option name="BASE_DIRECTORY_NAME" value="c29e2572-7ce8-4ea5-acac-02503639ac14" />
        <option name="HTTP_PORT" value="8083" />
      </server-settings>
      <predefined_log_file enabled="true" id="Tomcat" />
      <predefined_log_file enabled="true" id="Tomcat Catalina" />
      <predefined_log_file id="Tomcat Manager" />
      <predefined_log_file id="Tomcat Host Manager" />
      <predefined_log_file id="Tomcat Localhost Access" />
      <RunnerSettings RunnerId="Debug">
        <option name="DEBUG_PORT" value="9869" />
      </RunnerSettings>
      <RunnerSettings RunnerId="JRebel Debug">
        <option name="DEBUG_PORT" value="9870" />
      </RunnerSettings>
      <ConfigurationWrapper VM_VAR="JAVA_OPTS" RunnerId="Cover">
        <option name="USE_ENV_VARIABLES" value="true" />
        <STARTUP>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </STARTUP>
        <SHUTDOWN>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </SHUTDOWN>
      </ConfigurationWrapper>
      <ConfigurationWrapper VM_VAR="JAVA_OPTS" RunnerId="Debug">
        <option name="USE_ENV_VARIABLES" value="true" />
        <STARTUP>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </STARTUP>
        <SHUTDOWN>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </SHUTDOWN>
        <EnvironmentVariables>
          <option name="NAME" value="SSO_KEDOU_CONFIG_HOME" />
          <option name="VALUE" value="D:\develop\IDEAworkplace\com.shunwang.member.center\member-center-sso\src\main\config" />
          <option name="IS_PREDEFINED" value="false" />
        </EnvironmentVariables>
      </ConfigurationWrapper>
      <ConfigurationWrapper VM_VAR="JAVA_OPTS" RunnerId="JRebel Debug">
        <option name="USE_ENV_VARIABLES" value="true" />
        <STARTUP>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </STARTUP>
        <SHUTDOWN>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </SHUTDOWN>
      </ConfigurationWrapper>
      <ConfigurationWrapper VM_VAR="JAVA_OPTS" RunnerId="JRebel Executor">
        <option name="USE_ENV_VARIABLES" value="true" />
        <STARTUP>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </STARTUP>
        <SHUTDOWN>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </SHUTDOWN>
      </ConfigurationWrapper>
      <ConfigurationWrapper VM_VAR="JAVA_OPTS" RunnerId="Profile">
        <option name="USE_ENV_VARIABLES" value="true" />
        <STARTUP>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </STARTUP>
        <SHUTDOWN>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </SHUTDOWN>
      </ConfigurationWrapper>
      <ConfigurationWrapper VM_VAR="JAVA_OPTS" RunnerId="Run">
        <option name="USE_ENV_VARIABLES" value="true" />
        <STARTUP>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </STARTUP>
        <SHUTDOWN>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </SHUTDOWN>
        <EnvironmentVariables>
          <option name="NAME" value="SSO_KEDOU_CONFIG_HOME" />
          <option name="VALUE" value="D:\develop\IDEAworkplace\com.shunwang.member.center\member-center-sso\src\main\config" />
          <option name="IS_PREDEFINED" value="false" />
        </EnvironmentVariables>
      </ConfigurationWrapper>
      <method v="2">
        <option name="Make" enabled="true" />
        <option name="BuildArtifacts" enabled="true">
          <artifact name="member-center-sso:war exploded" />
        </option>
      </method>
    </configuration>
    <list>
      <item itemvalue="Remote JVM Debug.Unnamed" />
      <item itemvalue="Remote JVM Debug.研发base" />
      <item itemvalue="Remote JVM Debug.研发interface" />
      <item itemvalue="Remote JVM Debug.研发sso" />
      <item itemvalue="Tomcat Server.本地base" />
      <item itemvalue="Tomcat Server.本地sso" />
    </list>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9f38398b9061-39b83d9b5494-intellij.indexing.shared.core-IU-241.19072.14" />
        <option value="bundled-js-predefined-1d06a55b98c1-0b3e54e931b4-JavaScript-IU-241.19072.14" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="SvnConfiguration" cleanupOnStartRun="true">
    <configuration>C:\Users\<USER>\AppData\Roaming\Subversion</configuration>
    <supportedVersion>125</supportedVersion>
  </component>
  <component name="SvnFileUrlMappingImpl">
    <option name="myMappingRoots">
      <list>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="D:\develop\IDEAworkplace\com.shunwang.member.center" />
          <option name="myCopyRoot" value="D:\develop\IDEAworkplace\com.shunwang.member.center" />
        </SvnCopyRootSimple>
      </list>
    </option>
    <option name="myMoreRealMappingRoots">
      <list>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="D:\develop\IDEAworkplace\com.shunwang.member.center" />
          <option name="myCopyRoot" value="D:\develop\IDEAworkplace\com.shunwang.member.center" />
        </SvnCopyRootSimple>
      </list>
    </option>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="187630cf-8336-45d8-9d18-11ebcff3bfa8" name="Changes" comment="" />
      <created>1702524452978</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1702524452978</updated>
      <workItem from="1702524453991" duration="5896000" />
      <workItem from="1702602382720" duration="728000" />
      <workItem from="1703120778861" duration="3779000" />
      <workItem from="1703207194287" duration="9657000" />
      <workItem from="1703466355790" duration="602000" />
      <workItem from="1704244333372" duration="7004000" />
      <workItem from="1706145305640" duration="13733000" />
      <workItem from="1706231127719" duration="1654000" />
      <workItem from="1706490374172" duration="1649000" />
      <workItem from="1706576729549" duration="24000" />
      <workItem from="1706663264892" duration="599000" />
      <workItem from="1706749641264" duration="5279000" />
      <workItem from="1706836374001" duration="11014000" />
      <workItem from="1707095814558" duration="2390000" />
      <workItem from="1707211526016" duration="599000" />
      <workItem from="1708305293601" duration="2863000" />
      <workItem from="1708408542735" duration="2037000" />
      <workItem from="1709261898071" duration="5980000" />
      <workItem from="1709518092930" duration="48000" />
      <workItem from="1709624337257" duration="1323000" />
      <workItem from="1709860044222" duration="1863000" />
      <workItem from="1710119222642" duration="599000" />
      <workItem from="1710319923615" duration="2468000" />
      <workItem from="1710378451266" duration="6318000" />
      <workItem from="1710465028741" duration="1447000" />
      <workItem from="1710723982599" duration="934000" />
      <workItem from="1710810594469" duration="4458000" />
      <workItem from="1710896771210" duration="598000" />
      <workItem from="1710983178048" duration="601000" />
      <workItem from="1711069575360" duration="526000" />
      <workItem from="1711415134908" duration="599000" />
      <workItem from="1711501574760" duration="599000" />
      <workItem from="1711680673824" duration="1226000" />
      <workItem from="1712023089852" duration="7013000" />
      <workItem from="1712106385557" duration="1823000" />
      <workItem from="1712452045545" duration="2903000" />
      <workItem from="1712538644134" duration="352000" />
      <workItem from="1712624846806" duration="5750000" />
      <workItem from="1712661696056" duration="310000" />
      <workItem from="1712711120727" duration="3688000" />
      <workItem from="1712797615834" duration="1918000" />
      <workItem from="1712883967367" duration="3774000" />
      <workItem from="1713143312803" duration="598000" />
      <workItem from="1713315987624" duration="599000" />
      <workItem from="1713402404118" duration="750000" />
      <workItem from="1713427764427" duration="1709000" />
      <workItem from="1713748013810" duration="1243000" />
      <workItem from="1713920768423" duration="1418000" />
      <workItem from="1714093618729" duration="2710000" />
      <workItem from="1714266458567" duration="1321000" />
      <workItem from="1714440176336" duration="1198000" />
      <workItem from="1714957570268" duration="680000" />
      <workItem from="1715840860705" duration="72000" />
      <workItem from="1715841555497" duration="360000" />
      <workItem from="1715908018494" duration="15000" />
      <workItem from="1716253576817" duration="4598000" />
      <workItem from="1716340008804" duration="6921000" />
      <workItem from="1716427061000" duration="1958000" />
      <workItem from="1716512824243" duration="1206000" />
      <workItem from="1716771970083" duration="599000" />
      <workItem from="1716858400425" duration="1227000" />
      <workItem from="1717031182929" duration="1224000" />
      <workItem from="1717117581955" duration="16360000" />
      <workItem from="1717377196142" duration="1560000" />
      <workItem from="1717463132946" duration="599000" />
      <workItem from="1718074987612" duration="1784000" />
      <workItem from="1718154773133" duration="599000" />
      <workItem from="1718327354092" duration="599000" />
      <workItem from="1718761177366" duration="3285000" />
      <workItem from="1719562811373" duration="23000" />
      <workItem from="1719905716045" duration="57000" />
      <workItem from="1720518155956" duration="557000" />
      <workItem from="1720682341833" duration="841000" />
      <workItem from="1720684414535" duration="375000" />
      <workItem from="1720746554997" duration="401000" />
      <workItem from="1721807797188" duration="393000" />
      <workItem from="1721869796392" duration="1506000" />
      <workItem from="1721956278768" duration="820000" />
      <workItem from="1723511343199" duration="115000" />
      <workItem from="1723778314784" duration="12001000" />
      <workItem from="1724029742003" duration="3171000" />
      <workItem from="1724116741371" duration="4122000" />
      <workItem from="1724216351148" duration="111000" />
      <workItem from="1724227803471" duration="2895000" />
      <workItem from="1724375335296" duration="1198000" />
      <workItem from="1724634596656" duration="3217000" />
      <workItem from="1726624804296" duration="15038000" />
      <workItem from="1727077269153" duration="5558000" />
      <workItem from="1727140174131" duration="1198000" />
      <workItem from="1727226522204" duration="3741000" />
      <workItem from="1729754075646" duration="2873000" />
      <workItem from="1729818573277" duration="253000" />
      <workItem from="1730772388986" duration="636000" />
      <workItem from="1731462307773" duration="592000" />
      <workItem from="1731546636292" duration="1360000" />
      <workItem from="1732069948171" duration="3762000" />
      <workItem from="1732151396932" duration="1193000" />
      <workItem from="1732859449404" duration="2452000" />
      <workItem from="1733124041926" duration="1209000" />
      <workItem from="1734333670893" duration="908000" />
      <workItem from="1736125677339" duration="2668000" />
      <workItem from="1736212110925" duration="17084000" />
      <workItem from="1736298439700" duration="594000" />
      <workItem from="1736384847972" duration="10631000" />
      <workItem from="1736730622833" duration="11303000" />
      <workItem from="1736816843483" duration="10124000" />
      <workItem from="1736989728050" duration="514000" />
      <workItem from="1737422915007" duration="88000" />
      <workItem from="1739776776823" duration="3165000" />
      <workItem from="1739840973064" duration="5334000" />
      <workItem from="1739927297575" duration="3372000" />
      <workItem from="1740013941337" duration="720000" />
      <workItem from="1740101251383" duration="4013000" />
      <workItem from="1740359383254" duration="1209000" />
      <workItem from="1740445764465" duration="595000" />
      <workItem from="1741656591405" duration="4479000" />
      <workItem from="1741914566520" duration="4132000" />
      <workItem from="1742173729335" duration="135000" />
      <workItem from="1742524990447" duration="603000" />
      <workItem from="1743398983991" duration="5078000" />
      <workItem from="1743581550483" duration="3440000" />
      <workItem from="1743988284852" duration="593000" />
      <workItem from="1744077948362" duration="7285000" />
      <workItem from="1744351932418" duration="62000" />
      <workItem from="1744679614781" duration="229000" />
      <workItem from="1745804641056" duration="25000" />
      <workItem from="1750929339231" duration="1243000" />
      <workItem from="1750994041749" duration="4000" />
      <workItem from="1751618749543" duration="780000" />
      <workItem from="1751621426598" duration="48000" />
      <workItem from="1751621497696" duration="595000" />
      <workItem from="1751869547236" duration="17000" />
      <workItem from="1751937150743" duration="608000" />
      <workItem from="1752223475273" duration="694000" />
    </task>
    <task id="LOCAL-00001" summary="key名生成">
      <option name="closed" value="true" />
      <created>1712632728755</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1712632728755</updated>
    </task>
    <task id="LOCAL-00002" summary="用户导入">
      <option name="closed" value="true" />
      <created>1740116473223</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1740116473223</updated>
    </task>
    <option name="localTasksCounter" value="3" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="key名生成" />
    <MESSAGE value="用户导入" />
    <option name="LAST_COMMIT_MESSAGE" value="用户导入" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" suspend="NONE" type="java-line">
          <url>file://$PROJECT_DIR$/member-center-service/src/main/java/com/shunwang/baseStone/sysconfig/context/SysConfigContext.java</url>
          <line>18</line>
          <log-expression expression="gtSwitch=&quot;1&quot;" language="JAVA" />
          <option name="timeStamp" value="17" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/member-center-sso/src/main/java/com/shunwang/baseStone/sso/web/WxIdCardBindAction.java</url>
          <line>92</line>
          <option name="timeStamp" value="20" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/member-center-base/src/main/java/com/shunwang/baseStone/category/web/BusinessCategoryAction.java</url>
          <line>30</line>
          <option name="timeStamp" value="24" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/member-center-sso/src/main/java/com/shunwang/baseStone/sso/web/InterfaceLoginAuthorizeAction.java</url>
          <line>75</line>
          <option name="timeStamp" value="39" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="NONE" type="java-line">
          <url>file://$PROJECT_DIR$/member-center-sso/src/main/java/com/shunwang/baseStone/sso/apapter/IdCardAdapter.java</url>
          <line>71</line>
          <log-expression expression="siteList.add(&quot;Passport&quot;)" language="JAVA" />
          <option name="timeStamp" value="45" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>