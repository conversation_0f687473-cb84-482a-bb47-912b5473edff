package com.shunwang.baseStone.cache.keygenerator;

import com.shunwang.baseStone.cache.CacheKeyGenerator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.interceptor.KeyGenerator;

import java.lang.reflect.Method;
import java.util.Arrays;

/**
 * 相关缓存
 *
 * <AUTHOR>
 * @date 2021/08/10
 **/
public class AppidReplyKeyGenerator implements KeyGenerator {

    private final static Logger log = LoggerFactory.getLogger(AppidReplyKeyGenerator.class);

    @Override
    public Object generate(Object target, Method method, Object... params) {
        if (params.length != 3) {
            log.error("错误调用:{}", Arrays.toString(params));
            throw new IllegalArgumentException("错误调用");
        }
        String appid = (String) params[0];
        Integer mode = (Integer) params[1];
        Integer bindState = (Integer) params[2];
        String key = CacheKeyGenerator.getAppidReplyKey(appid, mode, bindState);
        log.debug("生成key: appid={},bindState={}\tkey:{}", appid, bindState, key);
        return key;
    }
}
