package com.shunwang.basepassport.binder.web.bind.processor;

import com.shunwang.baseStone.context.BaseStoneContext;
import com.shunwang.basepassport.binder.common.BinderConstants;
import com.shunwang.basepassport.binder.pojo.SendBinder;
import com.shunwang.basepassport.binder.processor.Processor;
import com.shunwang.basepassport.binder.processor.ProcessorContext;
import com.shunwang.basepassport.binder.response.PhoneLoginRespone;
import com.shunwang.basepassport.binder.web.send.bean.MobileBindBean;
import com.shunwang.basepassport.interc.SingleAccountBindService;
import com.shunwang.basepassport.user.common.MemberConstants;
import com.shunwang.basepassport.user.common.TicketUtil;
import com.shunwang.basepassport.user.common.UserLoginSessionUtil;
import com.shunwang.basepassport.user.pojo.Member;
import com.shunwang.basepassport.user.response.ValidateAgainResponse;

/**
 * 短信验证：只绑定手机号码且设为登录账号
 *
 * <AUTHOR>
 * @date 2018/12/18
 **/
public class MobileBindAsLoginProcessor implements Processor {
    @Override
    public boolean matches(ProcessorContext context) {
        return BinderConstants.MOBLIE_BIND_AS_LOGIN.equals(context.getSendBinder().getBusinessType());
    }

    @Override
    public boolean doProcess(ProcessorContext context) {
        SendBinder sendBinder = context.getSendBinder();
        MobileBindBean bean = (MobileBindBean) context.getT();
        Member member = sendBinder.getMember();

        sendBinder.setNumber(bean.getNumber());
        sendBinder.validate(bean.getActiveNo());
        if (member.getIsBindMobile()) {// 设为登录账号
            sendBinder.bindAsLoginAccount();
        } else {// 绑定且设为登录账号
            sendBinder.bind(true);
        }
        member.setLoginType(MemberConstants.LOGIN_TYPE_ACTIVE_NO);
        member.loginWithNoPwd();
        String sign = getSingleAccountBindService().bindTable(member, bean.getBindType(),
                bean.getSingleAccountToken(), bean.getAccessSiteId());
        if (null != sign) {
            ValidateAgainResponse response = new ValidateAgainResponse();
            response.setMsgId("1019");
            response.setToken(sign);
            response.setMsg("请绑定手机号");
            context.put("response", response);
            return false;
        }

        PhoneLoginRespone response = new PhoneLoginRespone(member);
        response.setTicket(TicketUtil.buildLoginTicket(member));
        context.put("response", response);
        UserLoginSessionUtil.saveSession(member.getMemberName(), UserLoginSessionUtil.LoginType.INTERFACE_SMS_AS_LOGIN.getType(), null, sendBinder.getAccessSiteId());

        return false;
    }

    private SingleAccountBindService getSingleAccountBindService() {
        return (SingleAccountBindService) BaseStoneContext.getInstance().getBean("singleAccountBindService");
    }
}
