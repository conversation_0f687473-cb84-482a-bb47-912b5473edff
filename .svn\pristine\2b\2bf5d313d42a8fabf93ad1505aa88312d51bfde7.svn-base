<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd" >
<sqlMap namespace="com.shunwang.basepassport.actu.pojo.PersonalActuInfo" >
<resultMap id="personalActuInfoQueryMap" class="com.shunwang.basepassport.actu.pojo.PersonalActuInfo" >
	<result column="personal_actuality_id" property="actuId" jdbcType="INTEGER" />
    <result column="member_id" property="memberId" jdbcType="INTEGER" />
    <result column="member_name" property="memberName" jdbcType="VARCHAR" />
    <result column="real_name_coded" property="realNameCoded" jdbcType="VARCHAR" />
    <result column="link_phone_coded" property="linkPhoneCoded" jdbcType="VARCHAR" />
    <result column="id_card_no_coded" property="idCardNoCoded" jdbcType="VARCHAR" />
    <result column="id_card_type" property="idCardType" jdbcType="CHAR" />
    <result column="id_card_endtime" property="idCardEndtime" jdbcType="TIMESTAMP" />
    <result column="id_card_img1_coded" property="idCardImg1Coded" jdbcType="VARCHAR" />
    <result column="id_card_img2_coded" property="idCardImg2Coded" jdbcType="VARCHAR" />
    <result column="id_card_img3_coded" property="idCardImg3Coded" jdbcType="VARCHAR" />
    <result column="risk_type" property="riskType" jdbcType="VARCHAR" />
    <result column="link_addr_coded" property="linkAddrCoded" jdbcType="VARCHAR" />
    <result column="bank_user_coded" property="bankUserCoded" jdbcType="VARCHAR" />
    <result column="bank_name" property="bankName" jdbcType="VARCHAR" />
    <result column="province_name" property="provinceName" jdbcType="VARCHAR" />
    <result column="city_name" property="cityName" jdbcType="VARCHAR" />
    <result column="bank_no_coded" property="bankNoCoded" jdbcType="VARCHAR" />
    <result column="info_state" property="infoState" jdbcType="CHAR" />
    <result column="refuse_content" property="refuseContent" jdbcType="VARCHAR" />
    <result column="refuse_time" property="refuseTime" jdbcType="TIMESTAMP" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="check_time" property="checkTime" jdbcType="TIMESTAMP" />
    <result column="check_user" property="checkUser" jdbcType="VARCHAR" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="repeal_time" property="repealTime" jdbcType="TIMESTAMP" />
    <result column="is_del" property="isDel" jdbcType="TINYINT" />
</resultMap>
<typeAlias alias="personalActuInfo" type="com.shunwang.basepassport.actu.pojo.PersonalActuInfo"/>
<select id="getByMemberId" resultMap="personalActuInfoQueryMap">
	SELECT
         personal_actuality_id,  
         member_id,
         member_name,
         real_name_coded,
         link_phone_coded,
         id_card_no_coded,
         id_card_type,
         id_card_endtime,
         id_card_img1_coded,
         id_card_img2_coded,
         id_card_img3_coded,
         risk_type,
         link_addr_coded,
         bank_user_coded,
         bank_name,
         bank_no_coded,
         province_name,
         city_name,        
         info_state,
         refuse_content,
         refuse_time,
         create_time,
         check_time,
         check_user,
         remark,
         repeal_time,
         area_id,
         is_del
   FROM personal_personal_actuality_info
   WHERE member_id = #value:INTEGER#
   order by create_time DESC
   limit 1
</select>
<select id="findLatestedUnCheckCntByMemberId" resultClass="java.lang.Integer" parameterClass="java.lang.Integer">
	SELECT count(1)
   FROM personal_personal_actuality_info
   WHERE member_id = #value:INTEGER# and info_state = 1
</select>

<!-- 添加记录 -->
<insert id="insert" parameterClass="personalActuInfo" >
    INSERT INTO 
	personal_personal_actuality_info(
    member_id,
    member_name,
    real_name_coded,
    link_phone_coded,
    id_card_no_coded,
    id_card_type,
    id_card_endtime,
    id_card_img1_coded,
    id_card_img2_coded,
    id_card_img3_coded,
    risk_type,
    link_addr_coded,
    bank_user_coded,
    bank_name,
    bank_no_coded,
    province_name,
    city_name,
    info_state,
    refuse_content,
    refuse_time,
    create_time,
    check_time,
    check_user,
    remark,
    repeal_time,
    area_id
    ) VALUES (
    #memberId:INTEGER#,
    #memberName:VARCHAR#,
    #realNameCoded:VARCHAR#,
    #linkPhoneCoded:VARCHAR#,
    #idCardNoCoded:VARCHAR#,
    #idCardType:CHAR#,
    #idCardEndtime:TIMESTAMP#,
    #idCardImg1Coded:VARCHAR#,
    #idCardImg2Coded:VARCHAR#,
    #idCardImg3Coded:VARCHAR#,
    #riskType:VARCHAR#,
    #linkAddrCoded:VARCHAR#,
    #bankUserCoded:VARCHAR#,
    #bankName:VARCHAR#,
    #bankNoCoded:VARCHAR#,
    #provinceName:VARCHAR#,
    #cityName:VARCHAR#,
    #infoState:VARCHAR#,
    #refuseContent:VARCHAR#,
    #refuseTime:TIMESTAMP#,
    #createTime:TIMESTAMP#,
    #checkTime:TIMESTAMP#,
    #checkUser:VARCHAR#,
    #remark:VARCHAR#,
    #repealTime:TIMESTAMP#,
    #areaId:INTEGER#
    )
    <selectKey resultClass="INTEGER" keyProperty="actuId" >
        SELECT LAST_INSERT_ID()
    </selectKey>
</insert>

<update id="repeal" parameterClass="java.lang.Integer" >
    UPDATE personal_personal_actuality_info SET 
   		info_state = 4,repeal_time=NOW(),remark='用户撤销'
		WHERE member_id = #value:INTEGER# 
		and info_state=1
</update>


<update id="updateLinkPhone" parameterClass="personalActuInfo" >
    UPDATE personal_personal_actuality_info
    set link_phone_coded = #linkPhoneCoded:VARCHAR#
	WHERE member_id = #memberId:INTEGER#
</update>

</sqlMap>
