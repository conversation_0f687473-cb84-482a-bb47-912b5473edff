<%@ page contentType="text/html; charset=UTF-8" %>
<%@ include file="../common/taglibs.jsp" %>
<%@ page import="com.shunwang.basepassport.user.common.UserCheckUtil" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="Access-Control-Allow-Origin" content="*"/> 
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <title>顺网SSO 登录页</title>
    <link href="${staticServer}/${cdnVersion}/style/public.css" rel="stylesheet" type="text/css"/>
    <link href="${staticServer}/${cdnVersion}/style/public-gray.css" rel="stylesheet" type="text/css"/>

    <c:choose>
		<c:when test="${not empty loginCss && not empty loginCss.cssUrl }">
		    <link href="${loginCss.cssUrl}" rel="stylesheet" type="text/css"/>
		</c:when>
		<c:otherwise>
		    <link href="${staticServer}/${cdnVersion}/style/default.css" rel="stylesheet" type="text/css"/>
		</c:otherwise>
	</c:choose>
    
   <c:if test="${not empty outOauthCss && not empty outOauthCss.cssUrl }">
        <link href="${outOauthCss.cssUrl}" rel="stylesheet" type="text/css"/>
    </c:if>
    
    <script type="text/javascript" src="${staticServer}/${cdnVersion}/js/md5.js"></script>
    <script type="text/javascript" src="${staticServer}/${cdnVersion}/js/jquery-1.4.3.js"></script>
    <script type="text/javascript" src="${staticServer}/${cdnVersion}/js/jquery.slider.js"></script>
    <script type="text/javascript" src="${staticServer}/${cdnVersion}/js/qrcode.min.js"></script>
    <script type="text/javascript" src="${staticServer}/${cdnVersion}/login/js/StringUtil.js"></script>
    <script type="text/javascript" src="${staticServer}/${cdnVersion}/js/base.js"></script>
    <script type="text/javascript" src="${staticServer}/${cdnVersion}/login/js/jsencrypt.js"></script>
    <script type="text/javascript" src="${staticServer}/${cdnVersion}/login/js/login.js?v=00"></script>
    <script type="text/javascript" src="${staticServer}/${cdnVersion}/login/js/vc_plug.js"></script>
    <script type="text/javascript" src="${staticServer}/${cdnVersion}/login/js/quick_login_min.js"></script>
    <script type="text/javascript" src="https://res.wx.qq.com/connect/zh_CN/htmledition/js/wxLogin.js"></script>
    <s:if test="needReportData">
        <script type="text/javascript" src="${staticServer}/${cdnVersion}/login/js/sw-pv-report-js.min.js"></script>
    </s:if>

    <%
		String validPhone = UserCheckUtil.getValidPhoneNum();
	%>
</head>
<body id="dialog-checkcode" class="${bodyClassName}">
<script type="text/javascript">
    var SSO_SERVER = "${ssoServer}";
    var interfaceServer = "${interfaceServer}";
    var identityServer = "${identityServer}";
    var validPhoneRex = <%=validPhone%>;
</script>

<div class="passport-login" style="display:none;">
<!-- app二维码 -->
<s:if test="needAPPQrCode">
<div class="app_qr_code"><img src="${staticServer}/${cdnVersion}/images/qr_code.png" alt=""></div>
</s:if>
<%--   没有登录用户的情况  开始标记     --%>
<s:form name="login" id="login_form_" class="app_login_from" method="post" onsubmit="return false;">
    <div class="login-box" id="normal_login">
        <div class="login-msg msg">
			<div class="field-tip">
	            <ul class="error" id="login_">
	                <li><span id="errorSpan">&nbsp;</span></li>
	            </ul>
	            <c:if test="${needSmsQuickLogin == true }">
	            	<div class="login-type-link"><a class="a035 btn-back-sms" href="#"><span class="icon_type icon_sms"></span>短信快捷登录</a></div>
	            </c:if>
        	</div>
        </div>

        <div class="field placeholder field-username">
            <label style="float: left;">用户名：</label>
            <input class="login-text text-one" name=userName type="text"
                   tabindex="1" id="userName" placeholder="用户名/邮箱/手机号" value="<s:property value="userName"/>"/>
            <em></em></div>
        <div class="field placeholder field-password">
           <label style="float: left;">密　码：</label>
           <span id="inputEdit">
                <input tabindex="2" class="login-text text-two" placeholder="请输入密码" id="passwordInp" type="password" value=""/>
            </span>
            <div id="safeEdit" style="display:none; float: left; margin-bottom: 17px;_margin-left:3px;_margin-top:2px"></div>
        </div>
        <!--多账号计费免登-->
        <div class="protocol-tip multiple-agreement" style="display: none">
            <p>
                <input type="checkbox" class="form_check" id="agreementCheckbox2">
                <span class="agreement_chk2">同意授权绑定身份标识,享免密登录</span>
                <a href="//i.kedou.com/agreement/netBarFreeLogin" target="_blank" class="a035">《免登协议》</a>
            </p>
        </div>
        <div class="protocol-dialog" style="display: none">
            <div class="protocol-dialog-main">
                <div class="header">
                    <div class="header-title">绑定授权</div>
                </div>
                <div class="main">
                    <div class="text-area">
                        授权我们绑定您的网吧身份信息，下次在同一网吧无需登录验证，享受快捷免登服务
                        我已同意并阅读
                        <a href="//i.kedou.com/agreement/netBarFreeLogin" target="_blank">《授权免登协议》</a>
                    </div>
                </div>
                <div class="bottom">
                    <button class="agree-btn">授权绑定并登录</button>
                    <a class="link protocol-dialog-close" href="#">不绑定，继续登录 </a>
                </div>
            </div>
        </div>
        <!--多账号计费免登end-->
        <div class="submit">
            <s:hidden name="callbackUrl"/>
            <s:hidden name="loginCssUrl"/>
            <s:hidden name="site_id"/>
            <s:hidden name="login_rbt"/>
            <s:hidden name="cssSiteId"/>
            <s:hidden name="css"/>
            <s:hidden name="version"/>
            <s:hidden name="env"/>
            <s:hidden name="extData"/>
            <s:hidden name="reportData"/>
            <s:hidden name="password" id="password"/>
            <s:hidden name="md5" id="md5"/>
            <s:hidden name="loginSiteId" id="loginSiteId"/>
            <s:hidden name="token_site_id" id="tokenSiteId"/>
            <s:hidden name="freeTicket" id="freeTicket"/>
            <s:hidden name="memberId" id="memberId"/>
            <s:hidden name="loginType" id="loginType" value="quickLogin"/>
            <s:hidden name="loginPage" value="inline"/>
            <s:hidden name="tgt"/>
            <s:hidden name="tgtCancel"/>
            <s:hidden name="needSmsQuickLogin" />
        	<s:hidden name="needImmediatelyReg" />
            <s:hidden name="sessionId" id="sessionId" value="%{sessionId}"/>
            <s:hidden name="weakPwdState" id="weakPwdState"/>
            <s:hidden name="siteScene"/>
            <s:hidden name="businessScene" id="businessScene"/>
            <s:hidden name="idCardName" id="idCardName"/>
            <s:hidden name="authFreeType" id="authFreeType"/>
            <s:hidden name="guestType" id="guestType"/>
            <s:hidden name="guestLogin" id="guestLogin"/>

            <s:url var="regUrlTag" escapeAmp="false" value="%{#attr.regLink}">
                <s:param name="returnUrl" value="%{callbackUrl}"/>
                <s:param name="site_id" value="%{site_id}" />
                <s:param name="regVersion" value="%{version}"/>
                <s:param name="env" value="%{env}" />
            </s:url>
            <button id="loginButton_YHJ" type="button" tabindex="5" class="enter"></button>
            <c:if test="${needRetrievePwd == true }">
                <a class="a035 line-four" href="${identityServer}/findPwd/step1" target="${linkTgt}">忘记密码？</a>
            </c:if>
        </div>
        
        <c:if test="${not empty outOauthCss && not empty outOauthDirs}">
        <div class="login-other">
            <div class="login-input">使用以下账号登录：</div>
            <ul class="other-list clearfix">
                <c:forEach items="${outOauthDirs}" var="oauthDir">
                <li class="drop drop${oauthDir.linkId}" id="${oauthDir.linkId}">
                    <a href="javascript:void(0);" title="使用${oauthDir.dirName}登录">
                        <img alt="使用${oauthDir.dirName}登录" src="${staticServer}/${cdnVersion}/images/outOauth/${oauthDir.dirImg}"/><span class="font">${oauthDir.dirName}</span>
                    </a>
                    <div class="dropup">
                        <ul class="goto-list">
                            <c:forEach items="${oauthDir.outOauthInterfaces}" var="useroutInterface">
                            <li>
                                <a href="${useroutInterface.linkUrl}">
                                    <img alt="使用${useroutInterface.serviceProvider}登录"
                                         src="${staticServer}/${cdnVersion}/images/outOauth/${useroutInterface.serviceImg}"/>
                                    <span>${useroutInterface.serviceProvider}</span>
                                </a>
                            </li>
                            </c:forEach>
                        </ul>
                        <i class="arrow"></i>
                    </div>
                </li>
                </c:forEach>
            </ul>
        </div>
        </c:if>
        
        <c:if test="${needImmediatelyReg == true }">
        	<div class="register_link">
        	    <a href="<s:url value="%{regUrlTag}" />" class="a035" target="${linkTgt}" >立即注册</a>
			</div>
        </c:if>
        
    </div>
</s:form>
<%--   没有登录用户的情况  结束标记     --%>
</div>
<%--   使用手机短信快速登录的情况    --%>
<div class="sms-login" id="sms-login" style="display:none;">
    <!-- app二维码 -->
<s:if test="needAPPQrCode">
    <div class="app_qr_code"><img src="${staticServer}/${cdnVersion}/images/qr_code.png" alt=""></div>
</s:if>
	<s:form name="sms_login_form" id="sms_login_form" class="app_login_from" method="post" onsubmit="return false;">
		<div  class="login-box">

            <div class="login-msg msg">
				<div class="field-tip">
					<ul class="error" id="login2">
						<li><span id="errorSpan2">&nbsp;</span></li>
					</ul>
                    <div class="login-type-link"><a class="a035 btn-back-passport" href="#"><span class="icon_type icon_passport"></span>通行证账号登录</a></div>
				</div>
				<div class="field placeholder field-phonenumber">
			    <label style="float: left;">手机号：</label>
			    <span>
					<input id="sms-phonenumber" class="login-text phonenumber-input" type="text" placeholder="请输入手机号"   value="${number}">
				</span>
			</div>
			<div class="field placeholder field-sms-verify">
				<label style="float: left;">短信验证码：</label>
				<input tabindex="3" id="sms-checkcode" class="login-text sms-checkcode" maxlength="6" placeholder="短信验证码" type="text" value=""
                       onpaste="value=value.replace(/[^\0-9\.]/g,'')" onkeyup="value=value.replace(/[^\0-9\.]/g,'')" >
				<input type="button" id="btnSendSMS" class="phone_sms_resend" tabindex="2" value="发送短信验证码" maxlength="10">
            </div>
            <s:hidden name="smsMobileActiveNo" id="checkcode" />
            <s:hidden name="smsNumber" id="phonenumber"/>
			<s:hidden name="callbackUrl"/>
            <s:hidden name="loginCssUrl"/>
        	<s:hidden name="site_id" id="site_id"/>
            <s:hidden name="cssSiteId"/>
        	<s:hidden name="css"/>
        	<s:hidden name="version"/>
        	<s:hidden name="env" />
        	<s:hidden name="needCode" />
        	<s:hidden name="extData"/>
        	<s:hidden name="reportData"/>
            <s:hidden name="tgt"/>
            <s:hidden name="tgtCancel"/>
        	<s:hidden name="needSmsCheckCode" />
        	<s:hidden name="needSmsQuickLogin" />
        	<s:hidden name="needImmediatelyReg" />
			<div class="submit">
				<button type="button" tabindex="5" class="enter" onclick="smsLoginClick()"></button>
			</div>
			<div class="protocol-tip" style="padding:8px 0;">
				<p>
                    <input type="checkbox" class="form_check" tabindex="7" id="agreementCheckbox" <c:if test="${userAgreementCss.state eq 1}">checked="true"</c:if>/>
                    <span class="agreement_chk">我已阅读并接受</span>
                    <c:if test="${not empty userAgreementCss.cssUrl}"><a href="${userAgreementCss.cssUrl}" target="_blank" class="a035">《${userAgreementCss.remark}》</a></c:if>
                    <c:if test="${not empty privacyCss.cssUrl}">及<a href="${privacyCss.cssUrl}"  target="_blank" class="a035">《隐私政策》</a></c:if>
                </p>
			</div>
			</div>
			<c:if test="${not empty outOauthCss && not empty outOauthDirs}">
				<div class="login-other">
					<div class="login-input">使用以下账号登录：</div>
					<s:hidden name="linkTgt" id="linkTgt" />
					<ul class="other-list clearfix">
						<c:forEach items="${outOauthDirs}" var="oauthDir">
							<li class="drop drop${oauthDir.linkId}" id="${oauthDir.linkId}">
								<a href="javascript:void(0);" title="使用${oauthDir.dirName}登录">
									<img alt="使用${oauthDir.dirName}登录" src="${staticServer}/${cdnVersion}/images/outOauth/${oauthDir.dirImg}"/><span class="font">${oauthDir.dirName}</span>
								</a>
								<div class="dropup">
									<ul class="goto-list">
										<c:forEach items="${oauthDir.outOauthInterfaces}" var="useroutInterface">
											<li>
												<a href="${useroutInterface.linkUrl}">
													<img alt="使用${useroutInterface.serviceProvider}登录"
														 src="${staticServer}/${cdnVersion}/images/outOauth/${useroutInterface.serviceImg}"/>
													<span>${useroutInterface.serviceProvider}</span>
												</a>
											</li>
										</c:forEach>
									</ul>
									<i class="arrow"></i>
								</div>
							</li>
						</c:forEach>
					</ul>
				</div>
			</c:if>
		</div>
	</s:form>
</div>

<div class="weChat-login" style="display: none;">
    <div class="title">微信扫码登录</div>
    <!-- 二维码 -->
    <div class="code-box">
        <!-- 二维码登录 -->
        <div class="code_login" id="code_login">
        </div>
        <!-- 加载二维码 -->
        <div class="code_load" style="display: none;">
            <div class="load-icon"></div>
            <div class="load-text">加载中</div>
        </div>
        <!-- 加载失败 -->
        <div class="code_fail" style="display: none;">
            <div class="code_fail_box">
                <div class="code_fail_text">二维码已失效</div>
                <div class="code_refresh_btn">
                    <button>请点击刷新</button>
                </div>
            </div>
        </div>
    </div>
    <div class="text" >扫码关注公众号登录注册</div>
    <div class="text change-login-type">
        <a class="change-login-type-a" href="javascript:void(0)"> 扫描登录失败，试试这个</a>
    </div>
    <div class="back-passport">
        <a href="###">
            <img src="${staticServer}/${cdnVersion}/images/accounticon.png" alt="">
            <span>账号登录</span>
        </a>
    </div>
</div>

<!-- 撤销注销 -->
<div class="weChat-repeal-tip-content" style="display: none">
    <!-- 已注销提示 -->
    <div class="weChat-repeal-box">
        <div class="title">温馨提示</div>
        <div>该账号已注销,如需登录请撤销</div>
        <button class="btn-repeal">撤销注销</button>
        <button class="btn-cancel">取消</button>
    </div>
    <form method="post" id="cancel_form_"  onsubmit="return false;">
        <input type="hidden" name="optToken" id="c_optToken"/>
        <input type="hidden" name="site_id" id="c_site_id"/>
        <input type="hidden" name="memberName" id="c_memberName"/>
    </form>
</div>

<div class="weChat-login2" style="display: none;">
    <!-- 二维码 -->
    <div class="code-box">
        <!-- 二维码登录 -->
        <div class="code_login" id="wechat_login"style="height: 300px;">
        </div>
    </div>
    <div class="code_fail" style="display: none;">
        <div class="code_fail_box">
            <div class="code_fail_text">二维码已失效</div>
            <div class="code_refresh_btn">
                <button>请点击刷新</button>
            </div>
        </div>
    </div>
</div>

<!-- 小程序扫码登录 -->
<div class="mini-weChat-login" style="display:none;">
    <div class="title">微信扫码登录</div>
    <!-- 小程序扫码登录 二维码 -->
    <div class="code-box">
        <!-- 小程序扫码登录 二维码登录 -->
        <div class="code_login" id="mini_code_login">
            <img src="${staticServer}/${cdnVersion}/images/application_code_loading.png" alt="">
        </div>
        <!-- 小程序扫码登录 二维码登录加载中 -->
        <div class="code_load" style="display: none;">
            <div class="load-icon"></div>
            <div class="load-text">加载中</div>
        </div>
        <!-- 小程序扫码登录 二维码失效 -->
        <div class="code_fail" style="display: none;">
            <div class="code_fail_box">
                <div class="code_fail_text">二维码已失效</div>
                <div class="code_refresh_btn">
                    <button>请点击刷新</button>
                </div>
            </div>

        </div>
    </div>
    <div class="text">请使用微信扫描此小程序码登录</div>
    <div class="back-passport">
        <a href="">
            <img src="${staticServer}/${cdnVersion}/images/accounticon.png" alt="">
            <span>账号登录</span>
        </a>
    </div>
</div>
<!-- 小程序扫码登录 -->

<%--   loading    开始标记     --%>
<div class="login-loading" id="loading_login" style="display: none"></div>
<%--   loading    结束标记     --%>
<!-- APP二维码登录 -->
<div class="app-weChat-login" style="display:none">
    <div class="phone-code">
        <img src="${staticServer}/${cdnVersion}/images/verification_code.png" alt="">
    </div>
    <div class="title">手机扫码，安全登录</div>
    <!-- 二维码 -->
    <div class="code-box">
        <!-- 二维码登录 -->
        <div class="code_login" style="display:block;">
            <div id="app_code_login"></div>
            <div class="code-load-desc">
                <div class="code-load-desc-1">
                    打开
                    <img src="${staticServer}/${cdnVersion}/images/symbol_icon_1.png" class="symbol-1" alt="">
                    ${appName}
                    <img src="${staticServer}/${cdnVersion}/images/symbol_icon_2.png" class="symbol-2" alt="">
                    扫描二维码
                </div>
                <!-- 文案过长 -->
                <div class="code-load-desc-2">在页面顶部打开扫一扫，即可快速安全登录</div>
            </div>
        </div>
        <!-- 二维码登录加载中 -->
        <div class="code_load" style="display:none;">
            <div class="load-icon"></div>
            <div class="load-text">加载中</div>
            <div class="code-load-desc">
                <div class="code-load-desc-1">
                    打开
                    <img src="${staticServer}/${cdnVersion}/images/symbol_icon_1.png" class="symbol-1" alt="">
                    ${appName}
                    <img src="${staticServer}/${cdnVersion}/images/symbol_icon_2.png" class="symbol-2" alt="">
                    扫描二维码
                </div>
                <div class="code-load-desc-2">在页面顶部打开扫一扫，即可快速安全登录</div>
            </div>
        </div>
        <!-- 二维码失效 -->
        <div class="code_fail" style="display:none;">
            <div class="code_fail_box">
                <div class="code_fail_text">二维码已失效</div>
                <div class="code_refresh_btn">
                    <button>请点击刷新</button>
                </div>
            </div>
            <div class="code-fail-desc">打开
                <img src="${staticServer}/${cdnVersion}/images/symbol_icon_1.png" class="symbol-1" alt="">
                ${appName}
                <img src="${staticServer}/${cdnVersion}/images/symbol_icon_2.png" class="symbol-2" alt="">
                扫描二维码
            </div>
        </div>
        <!-- 扫码成功 -->
        <div class="code_success" style="display:none;">
            <div class="code_success_box">
                <img src="${staticServer}/${cdnVersion}/images/chenggong.png" alt="">
                <div class="code_success_text">扫描成功</div>
                <div class="code_refresh_btn">
                    请在手机上点击确认即可登录
                </div>
            </div>
        </div>
    </div>
</div>
<!-- SSO授权登录 -->
<div class="sso-authorize-login" style="display: none;">
    <div class="anthorization-content">
        <div class="header-title">
            欢迎使用顺网通行证登录
        </div>
        <div class="header-tip">
            <div class="title-tip">点击头像授权我们使用您的会员信息快速登录</div>
        </div>
        <div class="userinfo">
            <div class="avatar">
                <div class="avatar-img">
                    <img src="${staticServer}/${cdnVersion}/images/application_code_loading.png" alt="">
                </div>
                <div class="nickname"></div>
            </div>
        </div>
        <div class="error-tip" id="errorSpanAuto"></div>
        <div class="other-ways-to-login">其他方式登录>></div>
        <div class="anthorization-footer">
            <div class="check-box">
                <input type="checkbox" id="agreementCheckbox-auto" >
            </div>
            <div class="agreement-text">
                我已阅读并接受
            </div>
            <c:if test="${not empty userAgreementCss.cssUrl}">
                <div class="agreement agreement-user">
                    <a href="${userAgreementCss.cssUrl}" target="_blank" class="a035">《${userAgreementCss.remark}》</a>
                </div>
            </c:if>
            <c:if test="${not empty privacyCss.cssUrl}">
                <div class="agreement-text">及</div>
                <div class="agreement agreement-privacy">
                    <a href="${privacyCss.cssUrl}"  target="_blank" class="a035">《隐私政策》</a>
                </div>
            </c:if>
        </div>
    </div>
</div>
<!-- 手机端SSO授权登录 -->
<div class="sso-authorize-login-wx"  style="display:none;">
    <div class="page-content">
        <div class="header">
            <div class="header-title">通行证登录</div>
            <div class="desc">授权使用您的网吧身份信息（<span></span>）完成快速登录</div>
        </div>
        <div class="main">
            <div class="code-box qrcode-wrapper">
                <div class="code_login code_default" id="auth_code_login">
                    <div class="">
                        <img class="qrcode" src="${staticServer}/${cdnVersion}/images/application_code_loading.png" alt="" />
<%--                        <div class="mask"></div>--%>
                    </div>
                </div>
                <!-- 小程序扫码登录 二维码登录加载中 -->
                <div class="code_load code_default" style="display: none;">
                    <div class="load-icon"></div>
                    <div class="load-text">加载中</div>
                </div>
                <!-- 小程序扫码登录 二维码失效 -->
                <div class="code_fail code_default" style="display: none;">
                    <div class="code_fail_box">
                        <div class="code_fail_text">二维码已失效</div>
                        <div class="code_refresh_btn">
                            <button>请点击刷新</button>
                        </div>
                    </div>
                </div>
                <div class="qrcodeDesc" style="display: none;">微信扫码关注公众号</div>
            </div>
            <div class="text-area">
                1.本次授权将会获取您在网吧上机的身份证信息(加密处理),用于创建通行证账号，并与您的微信及手机号进行绑定，后续可在该网吧授权登录。
                2.如您使用非本人身份上机，系违规操作，请勿使用本功能。如您违规授权，造成的账号纠纷及风险将由您自行承担。
                <a href="${userAgreementCss.cssUrl}" target="_blank">《顺网用户协议》</a>
                <a href="${privacyCss.cssUrl}" target="_blank">《隐私政策》</a>
                <a href="//i.kedou.com/agreement/netBarFreeLogin" target="_blank">《网吧免登协议》</a>
            </div>
        </div>
        <div class="bottom">
            <button>同意协议，授权免登</button>
            <div class="other-ways-to-login">
                <a class="loglink" href="javascript:void(0);">其他方式登录 </a>
<%--                <span class="tip">*如非本人身份上网，推荐使用</span>--%>
            </div>
            <div class="privacy-control">
                <div class="linkList">
                    同意
                    <a href="${userAgreementCss.cssUrl}" target="_blank">《顺网用户协议》</a>
                    <a href="${privacyCss.cssUrl}" target="_blank">《隐私政策》</a>
                    <a href="//i.kedou.com/agreement/netBarFreeLogin" target="_blank">《网吧免登协议》</a>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- 极验校验 -->
<div id="captcha" style="display:none"></div>

<script type="text/javascript" src="${staticServer}/${cdnVersion}/js/gt.js"></script>
<script type="text/javascript" src="${staticServer}/${cdnVersion}/js/gtUtil.js"></script>

<script type="text/javascript" src="${staticServer}/${cdnVersion}/login/js/pwdUtil.js"></script>

<c:choose>
    <c:when test="${'t' eq login_rbt }">
        <script src="https://res.icafe28.com/slot/js/common.js"></script>
    </c:when>
</c:choose>
<script type="text/javascript">
    if ("https:" == document.location.protocol) {
        interfaceServer = interfaceServer.replace("http:", "https:");
        SSO_SERVER = SSO_SERVER.replace("http:", "https:");
    }
    
    var errorMsg = "${msg}";
    var loginType = "${loginType}";
    var originLoginType = loginType;
    var loginCssUrl = "${loginCssUrl}";
    var cssSiteId = "${cssSiteId}";
    var siteId = "${site_id}";
    var callbackUrl = "${callbackUrl}";
    var loginMode = "${loginMode}";
    var isFreeLoginFlag = "${isFreeLoginFlag}" == 'true' ? true : false;
    var showOauthPageFlag = "${showOauthPageFlag}" == 'true' ? true : false;
    var noSsoAuthFreeLoginFlag = "${noSsoAuthFreeLoginFlag}" == 'true' ? true : false;
    var needCheckCode = "${needCheckCode}" == 'true' ? true : false;
    var needSmsCheckCode = "${needSmsCheckCode}" == 'true' ? true : false;
    var needReportData = "${needReportData}" == 'true';
    var isSmsLogin = (!!loginMode && loginMode.indexOf("smsLogin") !== -1);
    var agreeAutoChecked = '${userAgreementCss.state eq 1}' == 'true';//短信登录使用
    var tgt = "${tgt}";
    var freeName;
    var dllSwitch = "${dllSwitch}";
    var smsSendExpTime = "${smsSendExpTime}";
    var checkTimeInterval = "${checkTimeInterval}";

    var showQuickAuthFlag = false;
    var unAuthAgreementFlag = "${unAuthAgreementFlag}" == 'true'? true : false;

    var _isSwBrowser = false;
    var _isNewSwBrowser = false;

    var baseCallBackParam = "&callbackUrl=" + encodeURIComponent(callbackUrl) + "&site_id=" + siteId + "&tgt=" + tgt + "&cssSiteId=" + cssSiteId + "&loginCssUrl=" + encodeURIComponent(loginCssUrl);

    $(document).keydown(function (event) {
        if (event.keyCode == 13) {
            event.preventDefault();
              $("#loginButton_YHJ").trigger("click");
        }
    });

    //隐私协议勾选框
    $(".agreement_chk").bind('click', function () {
    	$("#agreementCheckbox").trigger('click');
    });

    function showNormalLoginError(errorMsg) {
        document.getElementById("errorSpan").innerHTML = errorMsg;
        $("#errorSpan").addClass("errorSpan");
    }
    function showSmsLoginError(errorMsg) {
        document.getElementById("errorSpan2").innerHTML = errorMsg;
        $("#errorSpan2").addClass("errorSpan");
    }

    $(document).ready(function () {
        showLogin(); //用户已经选择普通登录
        try {
            _isSwBrowser = window.external.getWebCltType();
            var _swClientFileVersion = window.external.getClientFileVersion();
            _isNewSwBrowser = !!_swClientFileVersion && _swClientFileVersion >= '2024.02.22.2';
        } catch (e) {
        }
        
        $(".login-type-link>a").click(function(e){
			e.preventDefault();

			//显示通行证登录窗口
			if($(this).hasClass("btn-back-passport")) {
			    hideAllLogin();
                $(".passport-login").show();
                isSmsLogin = false;
			} else{//显示短信登录窗口
                hideAllLogin();
				$(".sms-login").show();
                isSmsLogin = true;
			}
		});
        $(".back-passport").bind('click', function () {
            $(".weChat-login").hide();
            if (isSmsLogin) {
                $(".sms-login").show();
            } else {
                $(".passport-login").show();
            }
        });
        $(".bottom button").bind('click', function () {
            $(".sso-authorize-login-wx .page-content").addClass("type3-1").removeClass("privacy-page");
        });
        $('.app_qr_code').click(function (e) {
            hideAllLogin();
            document.getElementById("loginSiteId").value = '1012';
            doAppLogin();
        });
        $(".phone-code > img").bind('click', function () {
            hideAllLogin();
            if (isSmsLogin) {
                $(".sms-login").show();
            } else {
                $(".passport-login").show();
            }
        });
        
        refresh();
        
        function infoCallback() {}
        
		function checkInfoCallback() {}

		if (needReportData) {
            try {
                setInterval(SwPvReport.getBarInfo(function (data) {
                    $("input[name = 'reportData']").each(function (i, e) {
                        if ($(e).val() == '') {
                            $(e).val(JSON.stringify(data))
                        }
                    })
                }), 200);
            } catch (e) {
                console.log('load reportData error');
            }
        }
        var loginInfo;
        var ssoAuth;
        var dllJson = [];
		if(isFreeLoginFlag) {
			//获取本地服务用户登录信息
	        $.ajax({
	            url:"https://plugin.kedou.com:9199/getMemberInfo.htm",  
	            dataType:'jsonp',  
	            data:'',
	            type : 'get',
	            jsonp:'callback',
	            jsonpCallback:'infoCallback',
	            async : false, //同步
	            success:function(result) {
	            	if(null==result.key)
	            		return;
                    loginInfo = result.key;
                    dllJson = JSON.parse(result.value);
                    ssoAuth = dllJson.ssoAuth;
                    if (!ssoAuth) {
                        if (!!dllSwitch && (dllSwitch&4) === 4) {
                            return;
                        }
                        if (noSsoAuthFreeLoginFlag) {
                            checkMemberInfo();
                            return;
                        }
                        return;
                    }
                    if (!!dllSwitch && (dllSwitch&2) === 2) {
                        return;
                    }
                    if (showOauthPageFlag) {
                        var titleName = !!dllJson.nickName ? unescape(dllJson.nickName.replace(/\\/g, "%")) : dllJson.memberName;
                        $(".sso-authorize-login").find(".avatar-img img").attr("src", "https://interface.kedou.com/front/interface/headImageQuery.htm?memberId="+ dllJson.memberId);
                        $(".sso-authorize-login").find(".avatar-img img").attr("alt", dllJson.memberName);
                        $(".sso-authorize-login").find(".nickname").html(titleName);
                        showQuickAuthFlag = true;
                        hideAllLogin();
                        $(".sso-authorize-login").show();
                        return;
                    } else {
                        checkMemberInfo();
                    }
	            },  
	            timeout:500
	        });

            $(".other-ways-to-login").bind("click", function (){
                $(".sso-authorize-login").hide();
                $(".sso-authorize-login-wx").hide();
                showQuickAuthFlag = false;
                $("#businessScene").val(undefined);
                loginType = originLoginType;
                showLogin();
            });

            var headLoginClicked = false;
            $(".sso-authorize-login-wx .code_login").bind('click', function () {
                if (!headLoginClicked && $(".sso-authorize-login-wx .type3-4").length > 0) {
                    headLoginClicked = true;
                    dllJump();
                    headLoginClicked = false;
                }
            });

            $(".avatar-img").bind("click", function () {
                if (!checkAutoLoginAgreement()) {
                    return;
                }
                hideAutoLoginError();
                checkMemberInfo();
            });

            function checkMemberInfo() {
                $.ajax({
                    url:"https://plugin.kedou.com:9199/checkMemberInfo.htm",
                    dataType: "jsonp",
                    data:{"memberId":loginInfo},
                    type: 'get',
                    jsonp:'callback',
                    jsonpCallback:'checkInfoCallback',
                    async: false, //同步
                    success: function(data) {
                        var freeTicket = data.freeTicket;
                        var memberId = data.memberId;
                        if(null == freeTicket || null == memberId)
                            return;
                        if (ssoAuth) {
                            autoLogin(freeTicket, memberId);
                        } else {
                            autoLoginAjax(freeTicket, memberId);
                        }
                    },
                    timeout: 500
                });
            }
		}
        function autoLogin(freeTicket, memberId) {
            $("#memberId").val(memberId);
            $("#freeTicket").val(freeTicket);
            document.getElementById("login_form_").action = SSO_SERVER + "/freeLogin.do";
            document.getElementById("login_form_").submit();
        }

        function autoLoginAjax(freeTicket, memberId) {
            $("#memberId").val(memberId);
            $("#freeTicket").val(freeTicket);
            var data = $("#login_form_").serialize();
            $.ajax({
                url: SSO_SERVER + "/freeLogin.do",
                data: data,
                dataType: 'json',
                type: 'post',
                success: function(data) {
                    $("#businessScene").val(data.businessScene);
                    loginType = data.qrType;
                    if (loginType == 'none') {
                        if (data.dataBind == 'true') {
                            $("#idCardName").val(dllJson.memberName);
                            $("#authFreeType").val("2");
                            $(".multiple-agreement").show();
                        }
                        return;
                    }
                    freeName = data.name;
                    if (loginType == 'dll') {
                        dllJson = data;
                        var needHeadAuth = data.needHeadAuth;
                        if (showOauthPageFlag || (!!needHeadAuth && needHeadAuth === 'true')) {
                            var classId = "sso-authorize-login-wx";
                            $("." + classId + " .header .header-title").text("通行证登录")
                            $("." + classId + " .page-content").addClass("type3-4");
                            $("." + classId + " .header .desc span").html(freeName);
                            $("." + classId + " .qrcodeDesc").html("网吧会员");
                            $("#auth_code_login img").attr("src", "https://interface.kedou.com/front/interface/headImageQuery.htm?memberId="+ dllJson.memberId);
                            showQuickAuthFlag = true;
                            hideAllLogin();
                            $("." + classId).show();
                            return;
                        }
                        dllJump();
                        return ;
                    }
                    showLogin();
                }
            });
        }

		function dllJump() {
            var urlParam = parseJsonParam(dllJson);
            window.location.href = "/autoLoginCallback.do?" + urlParam + baseCallBackParam;
        }
    });

    function reportDllMemberName(scene) {
        if(isFreeLoginFlag && noSsoAuthFreeLoginFlag && (dllSwitch&4) !== 4) {
            //获取本地服务用户登录信息
            $.ajax({
                url:"https://plugin.kedou.com:9199/getMemberInfo.htm",
                dataType:'jsonp',
                data:'',
                type : 'get',
                jsonp:'callback',
                jsonpCallback:'infoCallback',
                async : false, //同步
                success:function(result) {
                    if(null == result.key)
                        return;
                    loginInfo = result.key;
                    dllJson = JSON.parse(result.value);
                    ssoAuth = dllJson.ssoAuth;
                    if (!ssoAuth) {
                        var postData = {
                            memberName:dllJson.memberName,
                            businessScene:scene
                        }
                        $.ajax({
                            url: SSO_SERVER + "/reportDllData.do",
                            data: postData,
                            dataType: 'json',
                            type: 'post',
                            success: function(data) {
                            }
                        });
                    }
                },
                timeout:500
            });
        }
    }

    function showLogin() {
        if (loginType === '1008') {
            document.getElementById("loginSiteId").value = loginType;
            doWxSubLogin();
        } else if (loginType === '1005') {
            document.getElementById("loginSiteId").value = loginType;
            doWxChatLogin();
        } else if (loginType === '1012') {
            document.getElementById("loginSiteId").value = loginType;
            doAppLogin();
        } else if(loginType === '1013') {
            document.getElementById("loginSiteId").value = loginType;
            doWxMiniLogin();
        } else if (!!loginMode && loginMode.indexOf("smsLogin") !== -1) {//登录异常时展示短信登录页面
            //hideAllLogin();
            $(".passport-login").hide();
            $(".sms-login").show();
            isSmsLogin = true;
            if (errorMsg !== "" && errorMsg != null) {
                showSmsLoginError(errorMsg);
            }
        } else {
            hideAllLogin();
            $("#quick_login").hide();
            $("#loading_login").hide();
            $(".passport-login").show();
            $("#loginType").val("normalLogin");
            if (errorMsg !== "" && errorMsg != null) {
                showNormalLoginError(errorMsg);
                isSmsLogin = false;
            }
        }
    }
    
    function showChildSite() {
        if ($(this).attr("id")) {
            goToOtherSite($(this).attr("id"));
            return false;
        }
        var isActive = $(this).hasClass('on');
        cleanTip();
        if (!isActive) {
            $(this).toggleClass('on');
        }
        return false
    }
    function cleanTip() {
        $(".drop").each(function () {
            $(this).removeClass('on');
        });
    }
    function goToOtherSite(siteId) {
        cleanTip();
        document.getElementById("loginSiteId").value = siteId;
        if (siteId == 1008) {
            doWxSubLogin();
        } else if (siteId == 1013) {
            doWxMiniLogin();
        } else {
            var isSingleAccount = ${isSingleAccount} ;
            if (isSingleAccount) {
                var iWidth = 600; //弹出窗口的宽度;
                var iHeight = 400; //弹出窗口的高度;
                var iTop = (window.screen.availHeight - 30 - iHeight) / 2; //获得窗口的垂直位置;
                var iLeft = (window.screen.availWidth - 10 - iWidth) / 2; //获得窗口的水平位置;
                window.open(SSO_SERVER + "/goOutOauth.do?" + $("#login_form_").serialize(), "_blank", 'width=' + iWidth + 'px,height=' + iHeight + 'px,top=' + iTop + 'px,left=' + iLeft + 'px');
            } else {
                var loginForm = document.getElementById("login_form_");
                loginForm.action = SSO_SERVER + "/goOutOauth.do";
                var linkTgt = $("#linkTgt").val();
                if (linkTgt == "_blank" || !linkTgt) {//
                    loginForm.target = "_blank";
                    if (!!_isSwBrowser && (_isSwBrowser === 1 || !_isNewSwBrowser)) {
                        loginForm.action = SSO_SERVER + "/goOutOauth.do?" + $("#login_form_").serialize();
                    }
                }
                loginForm.submit();
            }
        }
    }
    function doWxChatLogin() {
        var data = $("#login_form_").serialize();
        $.ajax({
            url:  "/getOutSiteOauth.do",
            type: 'post',
            dataType: 'json',
            data: data,
            success: function(data){
                if (showQuickAuthFlag) {
                    return;
                }
                if (!data || data["errorMsg"]) {
                    showQrcodeMsg(data["errorMsg"], "weChat-login2");
                } else {
                    hideAllLogin();
                    $(".weChat-login2 .code_login").html("");
                    $(".weChat-login2").show();
                    new WxLogin({
                        self_redirect: true,
                        id: "wechat_login",
                        appid: data.appid,
                        scope: data.scope,
                        redirect_uri: data.redirect_uri,
                        state: data.state,
                        style: data.style,
                        href: data.href
                    });
                    $("weChat-login2 .code_login").show();
                }
            },
            error: function(e){
                showQrcodeMsg("系统异常", "weChat-login2");
            }
        });
    }
    var userKeyForOpen = '';
    function doWxSubLogin() {
        var data = $("#login_form_").serialize();
        var classId = "weChat-login";
        var codeId = "code_login";
        var businessScene = $("#businessScene").val();
        if (!!businessScene) {
            classId = "sso-authorize-login-wx";
            codeId = "auth_code_login";
            if (!!freeName) {
                $("." + classId + " .header .header-title").text("登录安全验证")
                $("." + classId + " .page-content").addClass("type3-2");
                $("." + classId + " .header .desc span").html(freeName);
                $("." + classId + " .qrcodeDesc").html("微信扫码验证");
            } else {
                $("." + classId + " .header .header-title").text("微信扫码授权登录")
                if (!!unAuthAgreementFlag && !$("." + classId + " .page-content").hasClass("type3-1")) {
                    $("." + classId + " .page-content").addClass("privacy-page");
                } else {
                    $("." + classId + " .page-content").addClass("type3-1").removeClass("privacy-page");
                }
                $("." + classId + " .qrcodeDesc").html("微信扫码关注公众号");
                $("." + classId + " .desc").html("授权使用您的网吧身份信息完成快速登录");
            }
        }
        $.ajax({
            url:  "/getOutSiteOauth.do",
            type: 'post',
            dataType: 'json',
            data: data,
            success: function(data){
                if (showQuickAuthFlag) {
                    return;
                }
                if (data["errorMsg"]) {
                    showQrcodeMsg(data["errorMsg"], classId);
                } else {
                    hideAllLogin();
                    $("." + classId + " .code_login").html("");
                    $("." + classId).show();
                    new QRCode(document.getElementById(codeId), {
                        width:180,
                        height:180,
                        correctLevel: QRCode.CorrectLevel.H,
                        text: data["qrcodeUrl"]
                    });
                    userKeyForOpen = data["key"];
                    $("." + classId + " .code_login").show();
                    //reportDllMemberName(userKeyForOpen);
                    checkWxSubLogin(data["expireSeconds"], classId);
                }
            },
            error: function(e){
                showQrcodeMsg("系统异常", classId);
            }
        });
    }

    function doAppLogin() {
        var data = $("#login_form_").serialize();
        $.ajax({
            url:  "/getOutSiteOauth.do",
            type: 'post',
            dataType: 'json',
            data: data,
            success: function(data){
                if (showQuickAuthFlag) {
                    return;
                }
                if (data["errorMsg"]) {
                    showQrcodeMsg(data["errorMsg"], "app-weChat-login");
                } else {
                    hideAllLogin();
                    $("#app_code_login").html("");
                    new QRCode(document.getElementById("app_code_login"), {
                        width:180,
                        height:180,
                        correctLevel: QRCode.CorrectLevel.H,
                        text: data["qrcodeUrl"]
                    });
                    userKeyForOpen = data["key"];
                    $(".app-weChat-login").show();
                    $('.app-weChat-login .code_login').show();
                    //$(".code_login").show();
                    checkWxSubLogin(data["expireSeconds"], "app-weChat-login");
                }
            },
            error: function(e){
                showQrcodeMsg("系统异常", "weChat-login");
            }
        });
    }

    function hideAllLogin() {
        $(".passport-login").hide();
        $(".sms-login").hide();
        $(".weChat-login").hide();
        $(".mini-weChat-login").hide();
        $(".weChat-login2").hide();
        $(".code_fail").hide();
        $(".code_load").hide();
        $(".app-weChat-login").hide();
        $(".sso-authorize-login-wx").hide();
    }
    function doWxMiniLogin() {
        var data = $("#login_form_").serialize();
        var classId = "mini-weChat-login";
        var codeId = "mini_code_login";
        var businessScene = $("#businessScene").val();
        if (!!businessScene) {
            classId = "sso-authorize-login-wx";
            codeId = "auth_code_login";
            $("." + classId + " .header .header-title").text("登录安全验证")
            $("." + classId + " .page-content").addClass("type3-3");
            $("." + classId + " .header .desc span").html(freeName);
            $("." + classId + " .qrcodeDesc").html("微信扫码验证");
        }
        $.ajax({
            url:  "/getOutSiteOauth.do",
            type: 'post',
            dataType: 'json',
            data: data,
            success: function(data){
                if (showQuickAuthFlag) {
                    return;
                }
                if (data["errorMsg"]) {
                    showQrcodeMsg(data["errorMsg"], classId);
                } else {
                    hideAllLogin();
                    $("." + classId).show();
                    $("#" + codeId + " img").attr("src", data["qrcodeUrl"]);
                    userKeyForOpen = data["key"];
                    $("." + classId + " .code_login").show();
                    reportDllMemberName(data.key);
                    checkWxSubLogin(data["expireSeconds"], classId);
                }
            },
            error: function(e){
                showQrcodeMsg("系统异常", classId);
            }
        });
    }
    //二维码失效提示
    function showQrcodeMsg(msg, classId) {
        hideAllLogin();
        $("." + classId + " .code-box").children("div").hide();
        $("." + classId + " .code_fail_text").html(msg);
        $("." + classId + " .code-box .code_fail").show();
        $("." + classId).show();
    }
    //预登陆提示
    function showQrcodePreLogin(classId) {
        hideAllLogin();
        $("." + classId + " .code_login").hide();
        $("." + classId + " .code_load").hide();
        $("." + classId + " .code_fail").hide();
        $("." + classId + " .code_success").show();
        $("." + classId).show();
    }
    //二维码失效刷新
    $(".code_refresh_btn").bind('click', function () {
        $(".code_fail").hide();
        $(".code_load").show();
        isCheckWxSub = true;
        var siteId = document.getElementById("loginSiteId").value;
        if (siteId == '1008') {
            $(".code_login").html("");//todo code_login是共用样式需优化
            doWxSubLogin();
        }
        if (siteId == "1013") {
            doWxMiniLogin();
        }
        if (siteId == "1012") {
            doAppLogin();
        }
    });

    $(".change-login-type-a").bind("click", function () {
        document.getElementById("loginSiteId").value = '1005';
        doWxChatLogin();
    })

    //二维码失效刷新
    $(".btn-repeal").bind('click', function () {
        var data = $("#cancel_form_").serialize();
        $.ajax({
            url:"/undoCancel.do",
            data:data,
            type: 'post',
            dataType: 'json',
            async: false, //同步
            success: function(data) {
                $(".weChat-repeal-tip-content").show();
                location.reload(true);
            }
        });
    });

    $(".btn-cancel").bind('click', function () {
        location.reload(true);
    });

    var isCheckWxSub = true;
    //扫码状态检测
    function checkWxSubLogin(wxsub_sec, classId){
        if (!isCheckWxSub) {
            return;//异常后无需检测
        }
        if(wxsub_sec > 0){
            wxsub_sec = wxsub_sec - checkTimeInterval;
            $.ajax({
                url:  "/checkWxOpenLogin.do",
                type: 'post',
                dataType: 'json',
                data: {"userKey" : userKeyForOpen, 'site_id': siteId},
                success: function(data){
                    if (data == null || data == '') {
                        showQrcodeMsg("二维码已失效", classId);
                        isCheckWxSub = false;
                    } else if (data.stage == 'init') {

                    } else if (data.stage == 'preLogin') {
                        showQrcodePreLogin(classId);
                    } else if (data.stage == 'loginSuccess' || data.stage == 'singleBind') {
                        var urlParam = parseJsonParam(data);
                        window.location.href = "/loginCallback.do?" + urlParam + baseCallBackParam;
                    } else if (data.stage == 'userCancel') {
                        hideAllLogin();
                        $(".weChat-repeal-tip-content").show();
                        $("#c_memberName").val(data["memberName"]);
                        $("#c_optToken").val(data["optToken"]);
                        $("#c_site_id").val(data["siteId"]);
                        isCheckWxSub = false;
                        return;
                    } else if (data.stage == 'error') {
                        showQrcodeMsg(data["errorMsg"], classId);
                        isCheckWxSub = false;
                        return;
                    } else {
                        var urlParam = parseJsonParam(data);
                        window.location.href = "/loginCallback.do?" + urlParam + baseCallBackParam;
                    }
                },
                error: function(e){
                    console.log(e);
                }
            });
            setTimeout("checkWxSubLogin("+wxsub_sec+",'" +classId + "')",checkTimeInterval * 1000);
        } else {
            showQrcodeMsg("二维码已失效", classId);
        }
    }

	function jumpUrl(json) {
		var tgt = json["tgt"];
		var callbackUrl = json["fmtCallbackUrl"];
		if (tgt == "self") {
			window.location.href = callbackUrl;
			return;
		} else {
			top.location = callbackUrl;
			return;
		}
	}

    $(document).click(cleanTip);
    $('.drop').click(showChildSite).find('.goto-list').click(function (e) {
        e.stopPropagation()
    });

    function optAfter() {
        if (isSmsLogin || this.btnId == 'btnSendSMS') {
            sendAgain(function(){
                if (!!smsLoginGt.captchaObj) {
                    smsLoginGt.captchaObj.reset();
                }
            })
        } else {
            loginDefaultClick("/login.do");
        }
    }

    $(".protocol-dialog-close").bind("click",function (e){
        e.preventDefault();
        $("#authFreeType").val("4");
        $("#loginButton_YHJ").trigger("click");
    })
    $(".agree-btn").bind("click",function (e){
        e.preventDefault();
        $("#agreementCheckbox2").attr('checked', true);
        $("#loginButton_YHJ").trigger("click");
    })

    var loginGt = $.gtUtil({
            "showGt": needCheckCode,
            "btnId": "loginButton_YHJ",
            "formId": "normal_login",
            'bussSend': optAfter,
            'checkParam': loginDefaultCheck,
            'showGtMsg': function (msg) {
                showNormalLoginError(msg);
            }
        }
    );
    var smsLoginGt = $.gtUtil({
            "showGt": needSmsCheckCode,
            "btnId": "btnSendSMS",
            "formId": "sms_login_form",
            'bussSend': optAfter,
            'checkParam': numberInputCheck,
            'showGtMsg': function (msg) {
                showSmsLoginError(msg);
            }
        }
    );
</script>
</body>
</html>
