package com.shunwang.basepassport.binder.response;

import com.google.gson.annotations.Expose;
import com.shunwang.baseStone.core.response.BaseStoneResponse;
import com.shunwang.basepassport.user.pojo.Member;
import com.shunwang.xmlbean.annotation.XmlInit;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by bd.fang on 2016/3/7.
 */
public class PhoneLoginRespone extends BaseStoneResponse {
    private List<MemberInfo> memberInfoList;

    @Expose
    private Integer size;



    /**登录校验票据**/

    @Expose
    private String ticket;

    public PhoneLoginRespone(Member member) {
        size = 1;
        memberInfoList = new ArrayList<MemberInfo>();;
        MemberInfo  memberInfo = new MemberInfo(member.getMemberName(),member.getMemberId());
        memberInfoList.add(memberInfo);
        setItems(memberInfoList);
    }
    public PhoneLoginRespone(List<Member> memberList) {
        if(memberList == null)
            memberInfoList = null;
        else {
            size = memberList.size();
            memberInfoList = new ArrayList<MemberInfo>();
            for (int i = 0; i < memberList.size(); i++)
            {
                MemberInfo memberInfo = new MemberInfo(memberList.get(i).getMemberName(), memberList.get(i).getMemberId());
                memberInfoList.add(memberInfo);
            }
            setItems(memberInfoList);
        }
    }
    @XmlInit(path="size")
    public Integer getSize() {
        return size;
    }

    public void setSize(Integer size) {
        this.size = size;
    }

    @XmlInit(path="ticket")
    public String getTicket() {
        return ticket;
    }

    public void setTicket(String ticket) {
        this.ticket = ticket;
    }

    public class MemberInfo {
        /**用户名**/
        @Expose
        private String memberName;

        /**用户ID**/
        @Expose
        private Integer memberId;

        MemberInfo(String memberName,Integer memberId) {
            this.memberName = memberName;
            this.memberId = memberId;
        }

        @XmlInit(path="userName")
        public String getMemberName() {
            return memberName;
        }

        public void setMemberName(String memberName) {
            this.memberName = memberName;
        }

        @XmlInit(path="userId")
        public Integer getMemberId() {
            return memberId;
        }

        public void setMemberId(Integer memberId) {
            this.memberId = memberId;
        }

    }
    public List<MemberInfo> getMemberInfoList() {
        return memberInfoList;
    }

    public void setMemberInfoList(List<MemberInfo> memberInfoList) {
        this.memberInfoList = memberInfoList;
    }


}
