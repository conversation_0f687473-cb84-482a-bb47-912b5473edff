package com.shunwang.baseStone.siteinterface.dao;
import java.util.List;

import com.shunwang.baseStone.core.detail.HasDetail;
import com.shunwang.baseStone.siteinterface.pojo.SiteInterface;
import com.shunwang.framework.exception.WinterException;
public class SiteInterfaceDbDao extends SiteDao<SiteInterface>{
	
	public SiteInterface getByserviceKey(String serviceKey){		
		return (SiteInterface) this.getSqlMapClientTemplate().queryForObject(getStatementNameWrap("getByserviceKey"), serviceKey);
	}

	@Override
	public void delete(SiteInterface p) throws WinterException {
		if(((HasDetail)p).getDetail().isLog())
			((HasDetail)p).getDetail().save();
		super.delete(p);
	}

	@Override
	public SiteInterface save(SiteInterface p) throws WinterException {
		if(((HasDetail)p).getDetail().isLog())
			((HasDetail)p).getDetail().save();
		return super.save(p);
	}
	/**
	 * 根据bussinessKey查询对应的服务
	 *
	 * @return
	 * <AUTHOR> 创建于2014-1-14 下午04:22:23
	 * @throws
	 */
	@SuppressWarnings("unchecked")
	public List<SiteInterface> getByBussinessKey(String bussinessKey){		
		return  (List<SiteInterface>) this.getSqlMapClientTemplate().queryForList(getStatementNameWrap("getByBussinessKey"), bussinessKey);
	}
	
	@SuppressWarnings("unchecked")
	public List<SiteInterface> getAllByBussinessKey(String bussinessKey){		
		return  (List<SiteInterface>) this.getSqlMapClientTemplate().queryForList(getStatementNameWrap("getAllByBussinessKey"), bussinessKey);
	}
	
	public void deleteByBussinessKey(String businessKey) throws WinterException {
		this.getSqlMapClientTemplate().delete(getStatementNameWrap("deleteByBussinessKey"), businessKey);
	}
	
	
	
}
