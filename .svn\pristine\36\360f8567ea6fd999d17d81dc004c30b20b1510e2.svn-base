package com.shunwang.baseStone.listener;

import javax.servlet.ServletContextEvent;
import javax.servlet.ServletContextListener;

import org.springframework.web.context.support.WebApplicationContextUtils;

import com.shunwang.baseStone.zone.generator.ZoneListGenerator;
/**
 * @Description:地区初始化listener
 * <AUTHOR>  create at 2011-8-30 下午09:43:14
 * @FileName com.shunwang.baseStone.listener.ZoneInitListener.java
 */
@Deprecated
public class ZoneInitListener implements ServletContextListener {

	@Override
	public void contextDestroyed(ServletContextEvent arg0) {
		// TODO nothing

	}

	@Override
	public void contextInitialized(ServletContextEvent arg0) {
		ZoneListGenerator.setContext(WebApplicationContextUtils.getRequiredWebApplicationContext(arg0.getServletContext()));
//		ZoneListGenerator.flushZoneData();
	}

}
