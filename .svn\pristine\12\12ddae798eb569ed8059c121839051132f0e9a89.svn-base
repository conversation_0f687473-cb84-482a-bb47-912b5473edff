package com.shunwang.basepassport.binder.web.bind;

import com.shunwang.baseStone.bussiness.dao.BussinessDao;
import com.shunwang.baseStone.bussiness.pojo.Bussiness;
import com.shunwang.baseStone.context.BaseStoneContext;
import com.shunwang.baseStone.core.exception.BaseStoneException;
import com.shunwang.baseStone.core.response.BaseStoneResponse;
import com.shunwang.baseStone.encrypt.Encrypt;
import com.shunwang.baseStone.encrypt.pojo.EncryptItem;
import com.shunwang.basepassport.binder.common.BinderConstants;
import com.shunwang.basepassport.binder.common.ErrorCode;
import com.shunwang.basepassport.binder.dao.BinderDao;
import com.shunwang.basepassport.binder.exception.FormatErrorExp;
import com.shunwang.basepassport.binder.exception.MobileBindExp;
import com.shunwang.basepassport.binder.pojo.Binder;
import com.shunwang.basepassport.binder.pojo.MobileBinder;
import com.shunwang.basepassport.binder.pojo.SendBinder;
import com.shunwang.basepassport.binder.processor.Processor;
import com.shunwang.basepassport.binder.processor.ProcessorContext;
import com.shunwang.basepassport.binder.web.BindAction;
import com.shunwang.basepassport.binder.web.bind.processor.*;
import com.shunwang.basepassport.binder.web.send.bean.MobileBindBean;
import com.shunwang.basepassport.commonExp.LegalNotExp;
import com.shunwang.basepassport.commonExp.ParamNotFoundExp;
import com.shunwang.basepassport.config.dao.ConfigResourcesDao;
import com.shunwang.basepassport.config.dao.WeakPasswordDao;
import com.shunwang.basepassport.config.pojo.WeakPasswordDO;
import com.shunwang.basepassport.enums.WeakPasswordEnum;
import com.shunwang.basepassport.key.common.SmsKeyUtil;
import com.shunwang.basepassport.user.common.MemberConstants;
import com.shunwang.basepassport.user.common.MemberUtil;
import com.shunwang.basepassport.user.common.UserCheckUtil;
import com.shunwang.basepassport.user.dao.MemberDao;
import com.shunwang.basepassport.user.pojo.Member;
import com.shunwang.util.lang.StringUtil;
import org.apache.struts2.ServletActionContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;

import javax.servlet.RequestDispatcher;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 短信验证基类,总入口
 *
 * <AUTHOR>
 * @date 2018/12/18
 */
public class MobileBindBaseAction extends BindAction {

    protected static final Logger logger = LoggerFactory.getLogger(MobileBindBaseAction.class);
    private static Map<String, String> urlMap = new HashMap<>();
    private static List<Processor> processorChain = new ArrayList<>();

    static {
        urlMap.put(BinderConstants.ConfirmConstants.ConfirmTypes.TYPE_LOGIN, "/front/interface/confirmForLogin.htm");
        urlMap.put(BinderConstants.ConfirmConstants.ConfirmTypes.TYPE_NORMAL, "/front/interface" +
                "/confirmForNormalNoBind.htm");
        urlMap.put(BinderConstants.ConfirmConstants.ConfirmTypes.TYPE_RGE, "/front/interface/confirmForReg.htm");
        urlMap.put(BinderConstants.ConfirmConstants.ConfirmTypes.TYPE_FIND_PWD, "/front/interface/confirmForFindPwd" +
                ".htm");
        urlMap.put(BinderConstants.ConfirmConstants.ConfirmTypes.TYPE_SINGLE_ACCOUNT_BIND, "/front/interface" +
                "/confirmForSingleAccountBind.htm");
    }

    static {
        processorChain.add(new RedirectLoginProcessor());
        processorChain.add(new LoginProcessor());
        processorChain.add(new RegisterProcessor());
        processorChain.add(new FindPwdProcessor());
        processorChain.add(new MobileBindAsLoginProcessor());
        processorChain.add(new MobileBindTableProcessor());
        processorChain.add(new HotelMobileBindTableProcessor());
        processorChain.add(new HotelMobileChooseAccountToBindProcessor());
        processorChain.add(new HotelRegisterProcessor());
        processorChain.add(new MobileChooseAccountToBindProcessor());
        processorChain.add(new MobileNormalCodeProcessor());
        processorChain.add(new MobileUpdatePwdProcessor());
        //必须是最后一个
        processorChain.add(new DefaultProcessor());
    }

    protected MemberDao memberDao;
    protected WeakPasswordDao weakPasswordDao;
    protected Bussiness bussiness;
    private String userName;
    private String mobile;
    private String mobileActiveNo;
    private String bindType;
    private String loginIp;
    private String environment;
    private String remark;
    private String version;
    private String accessSiteId;
    private String password;
    private String encryptType;
    private String encryptMode;
    private String terminal;
    /**
     * 上报大数据的数据 json格式
     */
    private String reportData;
    /**
     * 用来存储额外的账号类型和unionId,从cached中取数据
     */
    private String singleAccountToken;
    /**
     * 需要从configResourcesDao数据库里面查询私钥
     */
    private ConfigResourcesDao configResourcesDao;
    private BussinessDao bussinessDao;

    /**
     * 验证短信验证码路由方法
     */
    public void route() {
        HttpServletRequest request = ServletActionContext.getRequest();
        String bindType = request.getParameter("bindType");
        String url = urlMap.get(bindType);
        try {
            if (StringUtil.isBlank(bindType)) {
                RequestDispatcher dispatcher = request.getRequestDispatcher("/front/interface/confirmForNormal.htm");
                logger.info("url跳转:/front/interface/confirmForNormal.htm");
                dispatcher.forward(request, ServletActionContext.getResponse());
            } else if (StringUtil.isNotBlank(url)) {
                RequestDispatcher dispatcher = request.getRequestDispatcher(url);
                logger.info("url跳转:" + url);
                dispatcher.forward(request, ServletActionContext.getResponse());
            } else {
                throw new IllegalArgumentException("wrong bindType");
            }
        } catch (Exception e) {
            logger.error("请求重定向异常[{}]", e.getMessage(), e);
        }
    }

    @Override
    public void doProcess() {

        checkFormat();

        String realSiteId = StringUtil.isNotBlank(getAccessSiteId()) ? getAccessSiteId() : getSiteId();
        bussiness = bussinessDao.findBybussinesskey(realSiteId);

        SendBinder sendBinder = buildSendBinder();
        sendBinder.setAccessSiteId(realSiteId);

        //build necessary params for processor
        MobileBindBean mobileBindBean = new MobileBindBean();
        BeanUtils.copyProperties(this, mobileBindBean);

        mobileBindBean.setType(getType());
        mobileBindBean.setNumber(getNumber());
        mobileBindBean.setDoType(getDoType());
        mobileBindBean.setActiveNo(getActiveNo());
        mobileBindBean.setAccessSiteId(realSiteId);
        mobileBindBean.setTerminal(terminal);

        ProcessorContext<MobileBindBean> context = new ProcessorContext<>(sendBinder, mobileBindBean);

        /**
         * hack
         */
        HttpServletRequest request = ServletActionContext.getRequest();
        if (request != null && "application/json".equals(request.getHeader("Accept"))) {
            context.put("json", true);
        }

        for (Processor processor : processorChain) {
            if (processor.matches(context)) {
                if (!processor.doProcess(context)) {
                    break;
                }
            }
        }

        /**
         * hack
         */
        String responseString = (String) context.get("responseString");
        if (StringUtil.isNotBlank(responseString)) {
            this.setResponseString(responseString);
            return;
        }

        BaseStoneResponse response = (BaseStoneResponse) context.get("response");
        if (response != null) {
            checkWeakPwd(response);
        }else{
            response = new BaseStoneResponse();
            checkWeakPwd(response);
        }

        this.setBaseResponse(response);
    }

    private BaseStoneResponse checkWeakPwd(BaseStoneResponse response){
        // 手机校验码校验，并设置新密码 ,校验弱密码
        if(BinderConstants.FIND_PWD_BIND_TYPE.equals(bindType)){
            WeakPasswordDO byPwdMd5 = weakPasswordDao.findByPwdMd5(password);
            response.setIsWeakPwd(byPwdMd5 != null ? 1 : 0);
            member.setWeakPwdState(byPwdMd5 != null ? MemberConstants.WEAK_PWD_STATE_1 : MemberConstants.WEAK_PWD_STATE_2);
            memberDao.update(member);
        }

        //手机短信注册验证校验,同时会设置密码，校验弱密码
        if (BinderConstants.BINDNUMBER.equals(bindType)){
            WeakPasswordDO byPwdMd5 = weakPasswordDao.findByPwdMd5(password);
            response.setIsWeakPwd(byPwdMd5 != null ? 1 : 0);
        }
        return response;
    }

    /**
     * 此方法中不做任何实现由子类重写
     */
    @Override
    protected SendBinder buildSendBinder() {
        throw new UnsupportedOperationException("A sub class must implement this method");
    }

    /**
     * 从缓存中获取binder信息
     *
     * @return
     */
    protected SendBinder getSendBinderFromCache(Member member) {
        return getSendBinderFromCache(member.getMemberId());
    }

    protected SendBinder getSendBinderFromCache(Integer memberId) {
        String key = SmsKeyUtil.buildSmsKey(memberId, getNumber());
        return SmsKeyUtil.getSmsCode(key, MobileBinder.class);
    }

    /***
     * 根据传入手机号在member表中的状态
     * 确定后续流程是注册、绑定为登录账号还是直接抛异常
     * @return
     */
    protected SendBinder adjustBindNum() {
        int cnt = getDao().getCntByMobile(getNumber());
        if (cnt >= BinderConstants.getMobileBinderLimit()) {
            throw new MobileBindExp();
        }
        SendBinder sendBinder = getSendBinderFromCache(-1);
        if (sendBinder == null) {
            sendBinder = (SendBinder) getBinderDao().getRegitsterBinderByNumber(getNumber());
        }
        if (sendBinder == null) {
            throw new BaseStoneException(ErrorCode.C_1033);
        }
        sendBinder.setBusinessType(BinderConstants.MOBLIE_REGISTER);
        return sendBinder;
    }

    @Override
    public String getIp() {
        if (StringUtil.isBlank(this.loginIp)) {
            this.loginIp = super.getIp();
        }
        return this.loginIp;
    }

    @Override
    protected void checkFormat() {
        if (!UserCheckUtil.checkMobile(this.getMobile())) {
            throw new FormatErrorExp(this.getType());
        }
        if (StringUtil.isNotBlank(password) && StringUtil.isNotBlank(encryptType)) {
            password = MemberUtil.decryt(password, encryptType, encryptMode);
        }

        if (!StringUtil.isBlank(getTerminal()) && !SendBinder.isLegalTerminal(getTerminal())) {
            throw new LegalNotExp();
        }
    }

    @Override
    protected String getActiveNo() {
        return this.getMobileActiveNo();
    }

    @SuppressWarnings("unchecked")
    @Override
    public BinderDao<Binder> getBinderDao() {
        return (BinderDao<Binder>) BaseStoneContext.getInstance().getBean("mobileBinderDao");
    }

    @Override
    protected Integer getDoType() {
        return BinderConstants.DOTYPE_MOBILE;
    }

    @Override
    protected String getNumber() {
        return this.getMobile();
    }

    /**
     * 子类按需求重写
     */
    @Override
    public void checkParam() {
        if (StringUtil.isBlank(this.getSign()) || StringUtil.isBlank(this.getNumber())
                || StringUtil.isBlank(this.getActiveNo()) || StringUtil.isBlank(this.getTime())) {
            throw new ParamNotFoundExp();
        }
    }

    @Override
    protected String getType() {
        return BinderConstants.MOBILE;
    }

    @Override
    public String getMemberName() {
        return this.getUserName();
    }

    @Override
    public boolean checkMemberIsExist() {
        if (BinderConstants.FIND_PWD_BIND_TYPE.equals(this.getBindType())) {
            return loadMember() != null;
        }
        if (!StringUtil.isBlank(getMemberName())) {
            setMember(getDao().getByName(getMemberName()));
            return null != getMember();
        }
        return true;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getLoginIp() {
        return loginIp;
    }

    public void setLoginIp(String loginIp) {
        this.loginIp = loginIp;
    }

    public String getEnvironment() {
        return environment;
    }

    public void setEnvironment(String environment) {
        this.environment = environment;
    }

    public String getBindType() {
        return bindType;
    }

    public void setBindType(String bindType) {
        this.bindType = bindType;
    }

    @Override
    public String buildSignString() {
        Encrypt encrypt = new Encrypt();
        encrypt.addItem(new EncryptItem(this.getSiteId()));
        encrypt.addItem(new EncryptItem(this.getUserName(), true));
        encrypt.addItem(new EncryptItem(this.getMobile()));
        encrypt.addItem(new EncryptItem(this.getActiveNo()));
        encrypt.addItem(new EncryptItem(this.getTime()));
        this.setUserName(decodeByUTF(this.getUserName()));
        return encrypt.buildSign();
    }

    @Override
    public String getSiteName() {
        return "手机认证";
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getMobileActiveNo() {
        return mobileActiveNo;
    }

    public void setMobileActiveNo(String mobileActiveNo) {
        this.mobileActiveNo = mobileActiveNo;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getAccessSiteId() {
        return accessSiteId;
    }

    public void setAccessSiteId(String accessSiteId) {
        this.accessSiteId = accessSiteId;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getSingleAccountToken() {
        return singleAccountToken;
    }

    public void setSingleAccountToken(String singleAccountToken) {
        this.singleAccountToken = singleAccountToken;
    }

    public MemberDao getMemberDao() {
        return memberDao;
    }

    public void setMemberDao(MemberDao memberDao) {
        this.memberDao = memberDao;
    }

    public BussinessDao getBussinessDao() {
        return bussinessDao;
    }

    public void setBussinessDao(BussinessDao bussinessDao) {
        this.bussinessDao = bussinessDao;
    }

    public Bussiness getBussiness() {
        return bussiness;
    }

    public void setBussiness(Bussiness bussiness) {
        this.bussiness = bussiness;
    }

    public String getEncryptType() {
        return encryptType;
    }

    public void setEncryptType(String encryptType) {
        this.encryptType = encryptType;
    }

    public String getEncryptMode() {
        return encryptMode;
    }

    public void setEncryptMode(String encryptMode) {
        this.encryptMode = encryptMode;
    }

    public String getReportData() {
        return reportData;
    }

    public void setReportData(String reportData) {
        this.reportData = reportData;
    }

    public String getTerminal() {
        return terminal;
    }

    public void setTerminal(String terminal) {
        this.terminal = terminal;
    }

    public WeakPasswordDao getWeakPasswordDao() {
        return weakPasswordDao;
    }

    public void setWeakPasswordDao(WeakPasswordDao weakPasswordDao) {
        this.weakPasswordDao = weakPasswordDao;
    }
}
