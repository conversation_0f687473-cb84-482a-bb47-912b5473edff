package com.shunwang.baseStone.siteinterface.dao;

import com.shunwang.baseStone.cache.CacheKeyGenerator;
import com.shunwang.baseStone.core.dao.CacheMapDao;
import com.shunwang.baseStone.core.dao.RefreshMem;
import com.shunwang.basepassport.jms.CacheMessageBuilder;
import com.shunwang.baseStone.siteinterface.pojo.SiteInterface;
import com.shunwang.basepassport.jms.MessageProducer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

public class SiteInterfaceDao extends CacheMapDao<SiteInterface> implements ISiteDao<SiteInterface>, RefreshMem {

    private final static Logger log = LoggerFactory.getLogger(SiteInterfaceDao.class);

    private MessageProducer messageProducer;

    @Override
    protected String getKey(SiteInterface pojo) {
        return CacheKeyGenerator.getSiteInterfaceKey(pojo.getBussinessKey(), pojo.getServiceKey());
    }

    @Override
    public SiteInterface getBySiteIdAndName(String bussinessKey, String serviceKey) {
        if (null == bussinessKey) {
            return null;
        }
        SiteInterfaceDbDao dao = (SiteInterfaceDbDao) this.getDbDao();
        SiteInterface siteInterface = dao.getBySiteIdAndName(bussinessKey, serviceKey);
        if (siteInterface == null) {
            log.info("根据,bussinessKey=" + bussinessKey + ",serviceKey=" + serviceKey + "从数据库中未查询到siteInterface数据");
            return null;
        }
        return siteInterface;
    }

    @Override
    protected SiteInterface loadCachePojo(SiteInterface pojo) {
        SiteInterfaceDbDao dao = (SiteInterfaceDbDao) this.getDbDao();
        return dao.getBySiteIdAndName(pojo.getBussinessKey(), pojo.getServiceKey());
    }

    @Override
    protected String getClassName() {
        return "com.shunwang.baseStone.siteinterface.pojo.SiteInterface";
    }

    @Override
    public void refresh() {
        this.loadMap();
        //Map<String, SiteInterface> ret=this.loadMap();
        //saveMap(ret);
    }

    public void refreshCachePojo(String bussinessKey) {
        List<SiteInterface> list = ((SiteInterfaceDbDao) this.getDbDao()).getAllByBussinessKey(bussinessKey);
        //没有要更新的缓存
        if (list == null || list.size() == 0) {
            return;
        }
        String[] keys = new String[list.size()];
        for (int i = 0; i < list.size(); i++) {
            keys[i] = "";
        }
        messageProducer.sendMessage(CacheMessageBuilder.newDeleteArrayMessage(keys));
        //for (SiteInterface siteInterface : list) {
        //  removeCachePojo(siteInterface);
        //}
    }

    public void removeCache(SiteInterface site) {
        removeCachePojo(site);
    }


    @Override
    protected void removeCachePojo(SiteInterface siteInterface) {
        //改成发送消息到消息中间件
        String key = "";
        messageProducer.sendMessage(CacheMessageBuilder.newDeleteArrayMessage(key));
    }

    public MessageProducer getMessageProducer() {
        return messageProducer;
    }

    public void setMessageProducer(MessageProducer messageProducer) {
        this.messageProducer = messageProducer;
    }
}
