<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE struts PUBLIC
	"-//Apache Software Foundation//DTD Struts Configuration 2.0//EN"
	"http://struts.apache.org/dtds/struts-2.0.dtd">
<struts>
	<package name="backuser" extends="struts-default">
		<action name="loginBackuser" class="backuserAction" method="login">
			<result name="success" type="redirect">
			     <param name="location">/bussiness/bussiness.jsp</param>
			</result>
			<result name="input">/index.jsp</result>
		</action>
		
		<action name="logout" class="backuserAction" method="logout">
            <result name="success">/welcome.jsp</result>
        </action>
	</package>

	<package name="backoauth2" namespace="/back/oauth2" extends="struts-default">
		<action name="login" class="oAuth2Action" method="login">
		</action>
		<action name="callback" class="oAuth2Action" method="authorizeCallback">
			<result name="input" type="redirect">/nologin/oauth2/authorize.jsp</result>
			<result name="error" type="redirectAction">
				<param name="actionName">login</param>
				<param name="namespace">/back/oauth2</param>
				<param name="msg">${msg}</param>
			</result>
			<result name="success" type="redirect">
				<param name="location">/bussiness/bussiness.jsp</param>
			</result>
		</action>
		<action name="handlerAccount" class="oAuth2Action" method="doHandlerAccount">
			<result name="input">/nologin/oauth2/authorize.jsp</result>
			<result name="success" type="redirect">
				<param name="location">/bussiness/bussiness.jsp</param>
			</result>
		</action>
	</package>
</struts>
