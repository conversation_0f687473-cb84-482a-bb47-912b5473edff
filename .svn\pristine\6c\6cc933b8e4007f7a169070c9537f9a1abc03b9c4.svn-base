package com.shunwang.passport.bind.web;

import com.shunwang.baseStone.cache.CacheKeyConstant;
import com.shunwang.baseStone.checkCode.context.CheckCodeContext;
import com.shunwang.baseStone.context.BaseStoneContext;
import com.shunwang.baseStone.context.RedisContext;
import com.shunwang.baseStone.core.exception.BaseStoneException;
import com.shunwang.basepassport.binder.common.BinderConstants;
import com.shunwang.basepassport.binder.dao.PersonalSendNumberDao;
import com.shunwang.basepassport.binder.exception.BindByOtherExp;
import com.shunwang.basepassport.binder.exception.IsBindedExp;
import com.shunwang.basepassport.binder.exception.NoBindExp;
import com.shunwang.basepassport.binder.exception.OneMinuteSendExp;
import com.shunwang.basepassport.binder.pojo.Binder;
import com.shunwang.basepassport.binder.pojo.MobileBinder;
import com.shunwang.basepassport.binder.pojo.PersonalSendNumber;
import com.shunwang.basepassport.binder.pojo.SendBinder;
import com.shunwang.basepassport.context.UserContext;
import com.shunwang.basepassport.user.common.MemberUtil;
import com.shunwang.passport.common.geetest.GeetestUtil;
import com.shunwang.util.date.DateUtil;
import com.shunwang.util.encrypt.AesEncrypt;
import com.shunwang.util.lang.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.PrintWriter;
import java.text.ParseException;
import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * **************************** 版权所有：顺网科技 保留所有权利 创建日期: 2011-8-25 下午01:23:00 创建作者：xiangjie 文件名称： 版本：
 * 1.0 功能：邮箱和手机发送的父类 最后修改时间： 修改记录：
 * ***************************************
 */
public abstract class SendAction extends BinderSendAction {

    private static final long serialVersionUID = 560841426637194495L;
    private String newNumber;
    private String number;
    private String activeNo;
    private String checkCode;
    private String isCheckCode = "1";//默认校验验证码
    private String oldNumber;

    private boolean showGt;
    private static final Logger log = LoggerFactory.getLogger(SendAction.class);

    /**
     * 去绑定页面
     *
     * @return
     * @throws
     * <AUTHOR> 创建于 2011-8-25 下午03:50:15
     */
    public String toBind() {
        try {
            //getKeyId(UserContext.getUserId() + "bindValidate");
            setMember(UserContext.getMember());
            setBinder(getBinderDao().getById(UserContext.getUserId()));
            setBindPageShow();
            showGt = GeetestUtil.isShowGt();
            return isBinderd() ? "change" : "bind";
        } catch (Exception e) {
            log.error("memberName["+UserContext.getUserName()+"]去绑定页面时出错，异常信息为：", e);
            return INPUT;
        }
    }

    /**
     * 构建sendBinder
     *
     * @return
     * @throws
     * <AUTHOR> 创建于 2011-8-25 下午01:24:59
     */
    public void buildSendBinder() {
        if(getBinder().getMemberId()==null){
            if (UserContext.getUserId() != null) {// 无登录状态发送
                getBinder().setMember(UserContext.getUser());
            }
        }else{ // 无登陆下,有登陆帐户发送
            String paramMemberId=getBinder().getMemberId()+"";
            if (UserContext.getUserId() != null&&(UserContext.getUserId().equals(paramMemberId)||StringUtil.isBlank(paramMemberId))) {// 需要无登录状态发送
                getBinder().setMember(UserContext.getUser());
            }
        }


    }

    protected void sendNoticeSms() {
    }

    @Override
    public String send() {
        try {
            UserContext.getMember();
            buildSendBinder();
            checkSend();
            checkSendNoticeSms();
            getBinder().send();
        } catch (BaseStoneException e) {
            log.error("通行证账号[" + UserContext.getUserName() + "]" +
                    BinderConstants.BUSSINESSTYPE_STR_MAP.get(getBinder().getBusinessType()) + getType() + "获取验证码时出错，异常信息为：", e);
            this.setErrorMsg(e.getMessage());
            return INPUT;
        } catch (Exception e) {
            log.error("通行证账号[" + UserContext.getUserName() + "]" +
                    BinderConstants.BUSSINESSTYPE_STR_MAP.get(getBinder().getBusinessType()) + getType() + "获取验证码时出错，异常信息为：", e);
            this.setErrorMsg("系统异常！");
            return INPUT;
        }

        if (this.getDoType() != null && (this.getDoType().equals(BinderConstants.DOTYPE_EMAIL) || this.getDoType().equals(BinderConstants.DOTYPE_MOBILE))) {
            this.putSecond();
        }
        return SUCCESS;
    }

    @Override
    public String unBind() {
        return null;
    }

    /**
     * 检查number格式
     *
     * @return
     * @throws
     * <AUTHOR> 创建于 2011-8-25 下午01:39:10
     */
    protected abstract void checkFormat(String number, boolean isNew);
    protected abstract boolean isBinderd();


    @Override
    public Binder findBinderByMember() {
        setBinder((SendBinder) getBinderDao().getById(UserContext.getUserId()));
        if (null == getBinder())
            throw new NoBindExp(this.getType());
        getBinder().setMember(UserContext.getUser());
        return getBinder();
    }

    /**
     * 发送前的检查
     *
     * @return
     * @throws
     * <AUTHOR> 创建于 2011-8-25 下午01:42:09
     */
    protected void checkSend() {
        Binder binder = getBinder();
        String bussinessType = binder.getBusinessType();
        if (bussinessType.equals(BinderConstants.PASSWORD_EDIT)) {
            setNumber(binder.getMember().getMobile());
        } else  {
            checkFormat(this.getNumber(), false);
            if (bussinessType.equals(BinderConstants.REGISTER)) {
                checkForBind();
            } else if (bussinessType.equals(BinderConstants.BINDNUMBER)) {
                if (binder instanceof MobileBinder && !isCheckCode.equals("0")) { //0：不需要验证验证码
                    CheckCodeContext.validate(checkCode);
                }
                checkForBind();
            } else if (bussinessType.equals(BinderConstants.CHANGENUMBER)) {
                if (!isCheckCode.equals("0")) { //0：不需要验证验证码
                    CheckCodeContext.validate(checkCode);
                }
                checkFormat(this.getNewNumber(), true);
                checkIsBindedAndBindNum((SendBinder) binder);
                checkForChange(this.getNewNumber());
            } else {
                checkIsBindedAndBindNum((SendBinder) binder);
            }
        }
        checkSendNumber();
    }

    /**
     * 用户绑定不发送 修改则发送 创建日期: 2013-12-27下午02:38:05 创建作者: Zeng Lingjie
     */
    private void checkSendNoticeSms() {
        if (getBinder().getBusinessType().equals(BinderConstants.CHANGENUMBER)) {
            sendNoticeSms();
        }
    }

    /**
     * 绑定前的检查
     *
     * @return
     * @throws
     * <AUTHOR> 创建于 2011-8-28 上午11:57:37
     */
    @Override
    protected void checkBind() {
        checkFormat(this.getNumber(), false);
        if (getBinder().getBusinessType().equals(BinderConstants.BINDNUMBER)
                || getBinder().getBusinessType().equals(BinderConstants.REGISTER))
            checkForBind();
        else if (getBinder().getBusinessType().equals(BinderConstants.CHANGENUMBER) || getBinder().getBusinessType().equals(BinderConstants.CHANGENUMBERFORSTEP2) )
            checkForChange(this.getNumber());
        else
            checkIsBindedAndBindNum(((SendBinder) getBinder()));
    }

    /**
     * 检查发送次数
     *
     * @return
     * @throws
     * <AUTHOR> 创建于 2011-8-25 下午01:34:53
     */
    public void checkSendNumber() {
        PersonalSendNumber personalSendNumber = buildPersonalSendNumber();
        personalSendNumber.setNumber(this.getNumber());
        personalSendNumber.setBeginDate(DateUtil.ymdhmsFormat(DateUtil.addMinute(new Date(), -1)));
        Integer cnt = getPersonalSendNumberDao().findCntByTime(personalSendNumber);
        if (null != cnt && cnt > 0)
            throw new OneMinuteSendExp(this.getType());
    }

    /**
     * 返回PersonalSendNumberDao
     *
     * @return
     * @throws
     * <AUTHOR> 创建于 Jul 25, 2011 4:31:57 PM
     */
    public PersonalSendNumberDao getPersonalSendNumberDao() {
        return (PersonalSendNumberDao) BaseStoneContext.getInstance().getBean("personalSendNumberDao");
    }

    /**
     * 构建PersonalSendNumber
     *
     * @return
     * @throws
     * <AUTHOR> 创建于 Jul 21, 2011 9:03:21 AM
     */
    protected PersonalSendNumber buildPersonalSendNumber() {
        PersonalSendNumber personalSendNumber = new PersonalSendNumber();
        personalSendNumber.setDoType(this.getDoType());
        personalSendNumber.setFromType(Integer.parseInt(this.getBinder().getBusinessType()));
        return personalSendNumber;
    }

    @Override
    protected void validateBinder() {
        getBinder().validate(activeNo);
    }

    /**
     * 返回操作类型 1为邮箱 2为手机
     *
     * @return
     * @throws
     * <AUTHOR> 创建于 Jul 25, 2011 4:39:13 PM
     */
    protected abstract Integer getDoType();

    /**
     * 设置绑定页面的显示内容
     *
     * @return
     * @throws
     * <AUTHOR> 创建于 2011-9-9 下午01:47:54
     */
    protected abstract void setBindPageShow();

    /**
     * 绑定number检查
     *
     * @return
     * @throws
     * <AUTHOR> 创建于 2011-8-25 下午01:52:21
     */
    protected void checkForBind() {
        if (getBinder().isBinded())
            throw new IsBindedExp(this.getType());
        if (!getBinder().isCouldBind(this.getNumber()))
            throw new BindByOtherExp(this.getType());
        ((SendBinder) getBinder()).setNumber(this.getNumber());

    }

    /**
     * 更换number检查
     *
     * @return
     * @throws
     * <AUTHOR> 创建于 2011-8-25 下午01:52:40
     */
    protected void checkForChange(String number) {
        if (!getBinder().isCouldBind(number))
            throw new BindByOtherExp(this.getType());
        ((SendBinder) getBinder()).setNumber(number);
        this.setOldNumber(this.getNumber());//新增邮箱重新申请发送功能时用到,在不改变原代码逻辑影响之前功能的前提下,新增此属性记录下之前的账号。
        this.setNumber(number);
    }

    /**
     * 检查账户是否绑定和账户是否绑定number正确
     *
     * @return
     * @throws
     * <AUTHOR> 创建于 Jul 25, 2011 7:10:04 PM
     */
    protected void checkIsBindedAndBindNum(SendBinder sendBinder) {
        if (!sendBinder.isBinded())
            throw new NoBindExp(this.getType());
        sendBinder.isMemberBindNumber(this.getNumber());
    }

    /**
     * 邮箱无需操作，手机需要极验校验
     * @return
     */
    protected boolean checkGt() {
        return true;
    }

    /**
     *
     * 重新发送验证码
     *
     * @return
     * <AUTHOR> 创建于 2011-9-17 下午03:13:11
     * @throws
     */
    public String sendAgain() {
        getResponse().setContentType("text/json;charset=UTF-8");
        PrintWriter out = getPrintWriter();
        if (checkGt() && send().equals(SUCCESS)) {
            out.write("{\"success\":\"ok\"}");
        } else {
            out.write("{\"errorMsg\":\"" + this.getErrorMsg() + "\"}");
        }
        out.close();
        return null;
    }

    /**
     * 检查验证码是否正确
     * @return
     */
    public String validateActiveNo() {
        getResponse().setContentType("text/json;charset=UTF-8");
        String msg = null;
        String smsToken = null;//用于ajax校验后的跳转验证
        try {
            findBinderByMember();
            getBinder().validate(activeNo);
            smsToken = AesEncrypt.Encrypt(number + activeNo, MemberUtil.getKey());
            smsToken = smsToken.substring(8);
        } catch (Exception e) {
            log.error("通行证账号[" + UserContext.getUserName() + "]，检验验证码["+activeNo+"]时出错，异常信息为：", e);
            msg = e.getMessage();
        }
        PrintWriter out = getPrintWriter();
        if (msg == null) {
            out.write("{\"success\":\"ok\",\"smsToken\":\"" + smsToken + "\"}");
        } else {
            out.write("{\"errorMsg\":\"" + msg + "\"}");
        }
        out.close();
        return null;
    }

    /**
     * 查询发送验证码倒计时
     *
     * <AUTHOR>
     * @date 2014-12-16
     * @return
     */
    public String querySendSMSSecond() {
        if (getBinder() != null && getBinder().getBusinessType().equals(BinderConstants.PASSWORD_EDIT)) {
            findBinderByMember();
            setNumber(getBinder().getMember().getMobile());
        }
        getResponse().setContentType("text/json;charset=UTF-8");
        PrintWriter out = getPrintWriter();
        out.write("{\"sec\":\"" + refrashSecond() + "\"}");
        out.close();
        return null;
    }

    private String getCacheKey(){
        return CacheKeyConstant.Passport.PASSPORT_SEND_SECOND_KEY+UserContext.getUserId()+CacheKeyConstant.CACHE_SPLIT+getNumber()+CacheKeyConstant.CACHE_SPLIT+getDoType();
    }

    /**
     * 设置发送秒数
     *
     * @return
     * @throws
     * <AUTHOR> 创建于 2011-9-17 下午02:00:10
     */
    protected void putSecond() {
        RedisContext.getRedisCache().set(getCacheKey(), DateUtil.getCurrentDateStr(),60, TimeUnit.SECONDS);
    }


    /**
     * 刷新发送秒数
     *
     * @return
     * <AUTHOR> 创建于 2011-9-16 下午01:58:52
     * @throws
     */
    protected Integer refrashSecond() {

         String timestr = RedisContext.getRedisCache().get(getCacheKey());
        if(StringUtil.isNotBlank(timestr)){
            try{
                Date time = DateUtil.praseDate(timestr);
                if (null != time) {
                    Date nowTime = new Date();
                    return (60 - (int) DateUtil.compare(nowTime, time, DateUtil.ONE_SECOND));
                }
            }catch(ParseException e){
                log.error("解析缓存日期异常{}",timestr,e);
            }

        }

        return 0;
    }

    /**
     * 删除发送秒数
     *
     * @return
     * <AUTHOR> 创建于 2011-9-16 下午01:58:52
     * @throws
     */
    protected Integer deleteSecond() {
       RedisContext.getRedisCache().del(UserContext.getUserId() + getNumber() + "_" + this.getDoType());
       return 0;
    }

    public String getNewNumber() {
        return newNumber;
    }

    public void setNewNumber(String newNumber) {
        this.newNumber = newNumber;
    }

    public String getNumber() {
        return number;
    }

    public void setNumber(String number) {
        this.number = number;
    }

    public String getActiveNo() {
        return activeNo;
    }

    public void setActiveNo(String activeNo) {
        this.activeNo = activeNo;
    }

    public String getCheckCode() {
        return checkCode;
    }

    public void setCheckCode(String checkCode) {
        this.checkCode = checkCode;
    }

    public String getIsCheckCode() {
        return isCheckCode;
    }

    public void setIsCheckCode(String isCheckCode) {
        this.isCheckCode = isCheckCode;
    }

    public String getOldNumber() {
        return oldNumber;
    }

    public void setOldNumber(String oldNumber) {
        this.oldNumber = oldNumber;
    }

    public boolean isShowGt() {
        return showGt;
    }

    public void setShowGt(boolean showGt) {
        this.showGt = showGt;
    }
}
