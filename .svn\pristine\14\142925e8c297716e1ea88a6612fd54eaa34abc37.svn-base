package com.shunwang.baseStone.context;

import com.shunwang.baseStone.bussiness.dao.BussinessDao;
import com.shunwang.baseStone.bussiness.pojo.Bussiness;
import com.shunwang.baseStone.loginelement.constants.LoginElementConstant;
import com.shunwang.baseStone.loginelement.dao.LoginElementDao;
import com.shunwang.baseStone.loginelement.pojo.LoginElement;
import com.shunwang.baseStone.siteinterface.dao.SiteInterfaceDbDao;
import com.shunwang.baseStone.siteinterface.pojo.SiteInterface;
import com.shunwang.baseStone.useroutinterface.dao.OutOauthDirDao;
import com.shunwang.baseStone.useroutinterface.pojo.OutOauthDir;
import com.shunwang.util.lang.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * User:pf.ma
 * Date:2017/04/25
 * Time:15:25
 */
public class LoginElementService {
    private static final Logger LOG = LoggerFactory.getLogger(LoginElementService.class);

    private BussinessDao bussinessDao;
    private LoginElementDao loginElementDao;
    private OutOauthDirDao outOauthDirDao;

    private SiteInterfaceDbDao siteInterfaceDbDao;

    /**
     * 获取配置是否开启
     *
     */
    public boolean getConfigIsOpen(String siteId, String name) {
        if (StringUtil.isBlank(siteId)) {
            return false;
        }
        Map<String, String> configMap = getLoginElementService().getLoginConfig(siteId, name);
        if (null == configMap) {
            return false;
        }
        String state = configMap.get(LoginElementConstant.STATE);
        return state != null && (state.equals("1") || state.equalsIgnoreCase("y"));
    }

    /**
     * 获取是否需要实名注册,默认返回false，不需要
     *
     * @param siteId
     * @return
     */
    public boolean getIsNeedActualRegBySiteId(String siteId) {
        if (StringUtil.isBlank(siteId)) {
            return false;
        }
        Map<String, String> map = getLoginElementService().getLoginConfig(siteId, LoginElementConstant.NEED_ACTUAL);
        return configIsOpen(map, LoginElementConstant.STATE);
    }

    /**
     * 获取是否需要验证码
     *
     * @param siteId
     * @return
     */
    public boolean getIsNeedSmsCheckCodeBySiteId(String siteId) {
        if (StringUtil.isBlank(siteId)) {
            return false;
        }
        Map<String, String> map = getLoginElementService().getLoginConfig(siteId, LoginElementConstant.SMS_CHECK_CODE);
        return configIsOpen(map, LoginElementConstant.STATE);
    }

    @Cacheable(value = "cache", keyGenerator = "loginConfigKeyGenerator", unless = "#result==null")
    public Map<String, String> getLoginConfig(String bussinesskey, String name) {
        if (StringUtil.isBlank(bussinesskey) || StringUtil.isBlank(name)) {
            LOG.error("传入bussinesskey或name为空 bysinessKey={},name={}", bussinesskey, name);
            return null;
        }
        SiteInterface ret = siteInterfaceDbDao.findByBussinessKey(bussinesskey);
        if (ret == null || StringUtil.isBlank(ret.getBussinessKey())) {
            LOG.info("根据bussinesskey=" + bussinesskey + "未查询到对应的站点信息");
            return null;
        }
        bussinesskey = ret.getBussinessKey(); //数据库对大小写不敏感，但是缓存是对大小写敏感的,所以bussinesskey统一用数据库里的
        return loadLoginElement(bussinesskey, name);
    }

    private Map<String, String> loadLoginElement(String bussinesskey, String name) {
        Map<String, String> valueMap = new HashMap<String, String>();
        Bussiness bussiness = bussinessDao.findBybussinesskey(bussinesskey);
        if (bussiness == null) {
            LOG.info("根据bussinesskey=" + bussinesskey + "未查询到对应的Bussiness配置信息");
            return valueMap;
        }
        LoginElement element;
        //商户不共享作业单元配置时,取商户自己的配置
        if (null == bussiness.getShareloginelement() || 0 == bussiness.getShareloginelement()) {
            LoginElement loginElement = loginElementDao.findLoginElementByCategoryId(buildLoginElement(bussiness,
                    LoginElementConstant.LOGIN_ELEMENT_CONFIG, name));
            if (loginElement == null) {
                LOG.info("根据bussinesskey=" + bussinesskey + ",categoryid=" + bussiness.getCategoryid() + ",type" +
                        "=partlyAccessConfig未查询到对应的LoginElement信息");
                return valueMap;
            }
            element = loginElement;

        } else if (1 == bussiness.getShareloginelement()) { //商户共享作业单元配置时,取作业单元配置
            if (bussiness.getCategoryid() == null) {
                LOG.info("bussinesskey=" + bussinesskey + "没有对应的类别信息,categoryid的值为null");
                return valueMap;
            }
            List<LoginElement> elementList = loginElementDao.findLoginElementsByBus(buildLoginElement(bussiness,
                    LoginElementConstant.LOGIN_ELEMENT_CONFIG, name));
            if (elementList == null || elementList.size() == 0) {
                LOG.info("根据bussinesskey is null ,categoryid=" + bussiness.getCategoryid() + ",type" +
                        "=partlyAccessConfig未查询到对应的LoginElement信息");
                return valueMap;
            }
            element = elementList.get(0);
        } else {
            return valueMap;
        }

        valueMap.put(LoginElementConstant.STATE, String.valueOf(element.getState()));
        valueMap.put(LoginElementConstant.VALUE, element.getValue());
        valueMap.put(LoginElementConstant.VALUE1, element.getValue1());

        if (StringUtil.isNotBlank(element.getRegLink())) {
            valueMap.put(LoginElementConstant.REG_LINK, element.getRegLink());
        }
        return valueMap;
    }

    @Cacheable(value = "cache", keyGenerator = "outOauthDirKeyGenerator", unless = "#result==null")
    public List<OutOauthDir> getOutOauthDirList(String bussinesskey) {
        SiteInterface ret = siteInterfaceDbDao.findByBussinessKey(bussinesskey);
        if (ret == null || StringUtil.isBlank(ret.getBussinessKey())) {
            LOG.info("根据bussinesskey=" + bussinesskey + "未查询到对应的站点信息");
            return new ArrayList<OutOauthDir>();
        }
        bussinesskey = ret.getBussinessKey(); //数据库对大小写不敏感，但是缓存是对大小写敏感的,所以bussinesskey统一用数据库里的
        return loadOutOauthDir(bussinesskey);
    }

    private LoginElement buildLoginElement(Bussiness bussiness, String type, String name) {
        LoginElement element = new LoginElement();
        element.setType(type);
        element.setCategoryId(Integer.valueOf(bussiness.getCategoryid()));
        element.setBussinessKey(bussiness.getBussinesskey());
        element.setName(name);
        return element;
    }

    private List<OutOauthDir> loadOutOauthDir(String bussinesskey) {
        List<OutOauthDir> dirs = new ArrayList<OutOauthDir>();
        Bussiness bussiness = bussinessDao.findBybussinesskey(bussinesskey);
        if (bussiness == null) {
            LOG.info("根据bussinesskey=" + bussinesskey + "未查询到对应的Bussiness配置信息");
            return dirs;
        }
        //商户不共享作业单元配置时,取商户自己的配置
        if (null == bussiness.getShareloginelement() || 0 == bussiness.getShareloginelement()) {
            LoginElement loginElement = loginElementDao.findLoginElementByCategoryId(buildLoginElement(bussiness,
                    LoginElementConstant.LOGIN_ELEMENT_CONFIG, LoginElementConstant.PARTLY_ACCESS));
            if (loginElement == null || loginElement.getState() != 1) {
                LOG.info("根据bussinesskey=" + bussinesskey + ",categoryid=" + bussiness.getCategoryid() + ",name" +
                        "=partlyAccess, type=partlyAccessConfig,未查询到开启的第三方登录配置信息");
                return new ArrayList<OutOauthDir>();
            }
            dirs = outOauthDirDao.findOutOauthDirByBus(buildLoginElement(bussiness,
                    LoginElementConstant.PARTLY_ACCESS_CONFIG, null));
            if (dirs == null || dirs.size() == 0) {
                LOG.info("根据bussinesskey=" + bussinesskey + ",categoryid=" + bussiness.getCategoryid() + ",type" +
                        "=outOauthDirConfig, state=1,未查询到对应的第三方登录配置信息");
                return new ArrayList<OutOauthDir>();
            }
            return dirs;

        } else if (1 == bussiness.getShareloginelement()) { //商户共享作业单元配置时,取作业单元配置
            if (bussiness.getCategoryid() == null) {
                LOG.info("第三方登录bussinesskey=" + bussinesskey + "没有对应的类别信息,categoryid的值为null");
                return dirs;
            }
            List<LoginElement> elementList = loginElementDao.findLoginElementsByBus(buildLoginElement(bussiness,
                    LoginElementConstant.LOGIN_ELEMENT_CONFIG, LoginElementConstant.PARTLY_ACCESS));
            if (elementList == null || elementList.size() == 0 || elementList.get(0).getState() != 1) {
                LOG.info("根据bussinesskey is null" + ",categoryid=" + bussiness.getCategoryid() + ",name=partlyAccess," +
                        " type=partlyAccessConfig,未查询到开启的第三方登录配置信息");
                return new ArrayList<OutOauthDir>();
            }
            dirs = outOauthDirDao.findOutOauthDirByCategoryId(buildLoginElement(bussiness,
                    LoginElementConstant.PARTLY_ACCESS_CONFIG, null));
            if (dirs == null || dirs.size() == 0) {
                LOG.info("根据bussinesskey is null, categoryid=" + bussiness.getCategoryid() + ",type" +
                        "=outOauthDirConfig, state=1,未查询到对应的第三方登录配置信息");
                return new ArrayList<OutOauthDir>();
            }
        }
        return dirs;
    }


    public boolean configIsOpen(Map<String, String> map, String key) {
        return null != map && null != map.get(key) && Integer.parseInt(map.get(key)) == LoginElementConstant.OPEN;
    }

    /**
     * 为了使用aop来缓存
     *
     * @return
     */
    protected LoginElementService getLoginElementService() {
        return (LoginElementService) BaseStoneContext.getInstance().getBean("loginElementService");
    }

    public BussinessDao getBussinessDao() {
        return bussinessDao;
    }

    public void setBussinessDao(BussinessDao bussinessDao) {
        this.bussinessDao = bussinessDao;
    }

    public LoginElementDao getLoginElementDao() {
        return loginElementDao;
    }

    public void setLoginElementDao(LoginElementDao loginElementDao) {
        this.loginElementDao = loginElementDao;
    }

    public OutOauthDirDao getOutOauthDirDao() {
        return outOauthDirDao;
    }

    public void setOutOauthDirDao(OutOauthDirDao outOauthDirDao) {
        this.outOauthDirDao = outOauthDirDao;
    }

    public SiteInterfaceDbDao getSiteInterfaceDbDao() {
        return siteInterfaceDbDao;
    }

    public void setSiteInterfaceDbDao(SiteInterfaceDbDao siteInterfaceDbDao) {
        this.siteInterfaceDbDao = siteInterfaceDbDao;
    }
}
