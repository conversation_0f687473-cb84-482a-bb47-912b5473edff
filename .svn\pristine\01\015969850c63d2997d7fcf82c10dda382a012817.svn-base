// 公告
function Notice(el) {
    this.$el = $(el)
    this.$content = this.$el.find('[data-content]')
    this.maxWidth = this.$content.width()
    this.initialize()
}

Notice.prototype = {
    initialize: function() {
        this.calcWords()
        this.initEvent()
        if (this.width >= this.maxWidth - 100) {
            this.initStyle()
            this.marquee()
        }
    },

    initEvent: function() {
        this.$el.find('[data-act="close"]').click($.proxy(this.close, this))
    },

    calcWords: function() {
        var text = this.$content.text()
        var item, words = []
        // 数字英文单词
        words.push({
            word: text.match(/[0-9a-zA-Z]/g),
            pad: 1
        })

        // 中文字符
        words.push({
            word: text.match(/[\u4e00-\u9fa5]/g),
            pad: 2
        })

        // 中文标点
        words.push({
            word: text.match(/[\u3002\uff1b\uff0c\uff1a\u201c\u201d\uff08\uff09\u3001\uff1f\u300a\u300b]/g),
            pad: 2
        })

        // 英文标点
        words.push({
            word: text.match(/[\-,\.;\:"'!]/g),
            pad: 1
        })

        var count = 0, width = 0
        for ( var i = 0, len = words.length; i < len; i++ ) {
            item = words[i]
            if (item.word)
                width += item.word.length * item.pad * 7
        }

        this.width = width
    },

    initStyle: function() {
        this.$content.css({
            overflow: 'hidden',
            position: 'relative',
            width: this.$content.width(),
            height: this.$el.height()
        })
        this.$innerContent = $('<div></div>')
        this.$innerContent.html(this.$content.html())
        this.$innerContent.css({
            width: this.width,
            position: 'absolute'
        })
        this.$content.html(this.$innerContent)
    },

    marquee: function() {
        var that = this
        this.$innerContent.animate({
            left: -this.width
        }, this.width * 25, 'linear', function() {
            that.$innerContent.css('left', that.maxWidth)
            that.marquee()
        })
    },

    close: function() {
        var that = this
        this.$el.hide(0, function() {
            that.$el.remove()
            that.$el = null
        })
    }
}

jQuery(function($) {
    // 公告
    $('[data-ui-notice]').each(function() {
        new Notice(this)
    })
})

//时间选择
$.fn.customDate=function(opt){
    var $that=$(this),
        $current=$that.find(".custom_date_current"),
        $list=$that.find(".custom_date_list"),
        $dateCheck=$that.find(".custom_date_check"),
        $dateBtn=$that.find(".custom_date_btn"),
        $showTime=$that.find(".custom_time");
    if(typeof(opt.set)!="undefined" && typeof(opt.setText)!="undefined"){
        customTime(opt.set,opt.setText);
    }
    $current.on("click",function(){
        $list.toggle();
    })
    $dateBtn.on("click",function(){
        $list.toggle();
    })
    $list.find("a").on("click",function(){
        var $self=$(this),
            type=$(this).attr("date-time"),
            text=$self.text();
        if(type=="custom"){
            $current.hide();
            $dateCheck.show();
            $showTime.hide();
        }else{
            customTime(type,text);
            $showTime.show();
            $current.show();
            $dateCheck.hide();
        }
        $list.hide();
        return false;
    })

    function customTime(type,text){
        var now=new Date(),
            day=now.getDate();
        month=now.getMonth()+1;
        year=now.getFullYear(),
            $startTime=$that.find(".time_start"),
            $endTime=$that.find(".time_end"),
            start=year+"-"+month+"-"+day,
            end="";
        if(type!="day"){
            if(type=="week"){
                now.setDate(now.getDate() + 6);
            }else if(type=="month" || type=="quarter" || type=="half_year"){
                var i=0;
                if(type=="month"){
                    i=1
                }else if(type=="quarter"){
                    i=3
                }else if(type="half_year"){
                    i=6
                }
                now.setMonth(now.getMonth() +i);
                now.setDate(now.getDate() -1);
            }
            day=now.getDate();
            month=now.getMonth()+1;
            year=now.getFullYear();
        }
        end=year+"-"+month+"-"+day;
        $showTime.text(start+" 至 "+end);
        $startTime.val(start);
        $endTime.val(end);
        $current.text(text);
    }
}