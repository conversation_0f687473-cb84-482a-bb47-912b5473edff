<%@ page contentType="text/html; charset=UTF-8" %>
<%@ include file="../common/taglibs.jsp" %>
<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1,minimum-scale=1,user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="format-detection" content="telephone=no, email=no">
    <meta name="full-screen" content="yes">
    <meta name="x5-page-mode" content="app">
    <meta name="msapplication-tap-highlight" content="no">
    <meta name="description" content="">
    <meta name="keywords" content="">
    <link rel="stylesheet" href="${staticServer}/${cdnVersion}/style/style.css">
    <title>绑定会员卡</title>
  </head>
  <body>
    <div class="content page-index">
      <div class="top-panel">
        <img src="${staticServer}/${cdnVersion}/images/logo.png" alt="" class="logo">
        <p>扫码上机 - <i>用户身份绑定</i></p>
      </div>
<%--      <h2 class="tip">首次使用微信扫码解锁需要绑定会员卡哦!</h2>--%>
      <div class="input-box">
        <span class="icon-card"></span>
        <input id="id" type="text" placeholder="请输入身份证号">
      </div>
      <a class="btn-bind" href="javascript:void(0)">绑定身份证号 </a>
      <p class="tip">绑定后微信扫码自动上机 <br>(免输卡号密码，隐私保护更安全）</p>
      <div class="activity-panel" id="privacy">

        <div class="help-list flex">
          <a href="${identityServer}/agreement/privacy">隐私保护政策</a>
        </div>
      </div>
    </div>

    <div class="toast" id="toast"> 
      <div class="toast-content" id="toastTxt"></div>
    </div>
    <script src="${staticServer}/${cdnVersion}/js/jquery-3.6.0.min.js"></script>
    <script src="${staticServer}/${cdnVersion}/js/utils.js"></script>
    <script src="${staticServer}/${cdnVersion}/js/index.js"></script>
    <script src="https://res.wx.qq.com/open/js/jweixin-1.3.2.js" type="text/javascript"></script>
    <script type="text/javascript">
        $(document).ready(function(){
            if (window.__wxjs_environment === 'miniprogram') {
                $('#privacy').hide()
            }
        });
    </script>
  </body>
</html>