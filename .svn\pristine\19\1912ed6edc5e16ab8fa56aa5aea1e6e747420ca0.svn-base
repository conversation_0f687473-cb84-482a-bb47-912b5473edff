package com.shunwang.basepassport.manager.request.netbar;

import com.shunwang.basepassport.config.pojo.ConfigInterface;
import com.shunwang.basepassport.manager.IResponse;
import com.shunwang.basepassport.manager.request.BaseRequest;
import com.shunwang.basepassport.manager.util.SignTool;
import com.shunwang.util.date.DateUtil;
import com.shunwang.util.encrypt.Md5Encrypt;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;

public abstract class BaseNetbarRequest<T extends IResponse> extends BaseRequest<T> {

    private String time;
    private String sign;
    private String md5Key;

    @Override
    public void addHeader(String key, String value) {
        if (headers == null) {
            headers = new HashMap<>();
        }
        headers.put(key, value);
    }

    @Override
    public Map<String, String> getHeaders() {
        return headers;
    }

    @Override
    public String buildUrl() {
        return getUrl();
    }

    public Map<String, String> buildCommonParams() throws UnsupportedEncodingException {
        Map<String, String> params = buildParams();
        params.put("time", DateUtil.getCurrentDateStamp());

        String signSource = SignTool.buildSignStringSortedMap(params, "sign", md5Key);
        String sign = Md5Encrypt.encrypt(URLEncoder.encode(signSource, SignTool.ENCODE).toUpperCase()).toUpperCase();
        params.put("sign", sign);
        return params;
    }


    /**
     * 设置配置
     */
    @Override
    public void doInterfaceSetting(ConfigInterface setting) {
        setUrl(setting.getInterfaceUrl1());
        setMd5Key(setting.getInterfaceMd5Key());
    }

    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    public String getMd5Key() {
        return md5Key;
    }

    public void setMd5Key(String md5Key) {
        this.md5Key = md5Key;
    }

    @Override
    public void setHeaders(Map<String, String> headers) {
        this.headers = headers;
    }
}
