package com.shunwang.passport.weixin.action;

import com.shunwang.common.ResponseUtil;
import org.apache.commons.lang3.StringUtils;
import com.shunwang.passport.weixin.pojo.WeixinResponseMsg;
import com.shunwang.passport.weixin.pojo.WeixinBasicMsg;
import com.shunwang.passport.weixin.service.WeixinMsgSerializer;
import com.shunwang.passport.weixin.service.WeixinMsgProcesser;
import com.shunwang.passport.weixin.pojo.WeixinConf;
import com.shunwang.passport.weixin.util.WeixinClientUtil;
import java.io.IOException;
import java.io.BufferedReader;
import javax.servlet.http.HttpServletRequest;
import org.apache.struts2.ServletActionContext;
import com.shunwang.framework.struts2.action.BaseAction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
/**
 * 微信的前端Action, 此Action有两种功能.
 *
 * 1: 验证功能: GET请求, 带signature,timestamp,nonce,echostr参数
 * 验证功能是成为微信开发者必须的,在微信管理服务号开发的界面中填入.
 * 只有成为开发者,才能调用微信提供的接口.
 *
 * 2: 接收和处理消息和事件: POST请求, xml格式消息.
 *  消息包括微信用户发送的文本,语音,视频等用户生成内容.
 *  事件包括用户点击菜单, 用户关注本微信服务号,取消关注, 发送地理
 *  位置信息等.
 *
 * <AUTHOR>
 * @since 2014-04
 *
 */
public class WeixinFacade extends BaseAction {

	private static final long serialVersionUID = 9049468750884448894L;
	private static Logger log = LoggerFactory.getLogger(WeixinFacade.class);

    private WeixinConf weixinConf;
    private WeixinMsgProcesser weixinMsgProcesser;

    //以下各参数为验证功能所需, 微信服务器调用时发送
    private String signature;
    private String timestamp;
    private String nonce;
    private String echostr;

    public String execute() {
        HttpServletRequest request = ServletActionContext.getRequest();
        boolean valid = isValidateRequest();

        //非法请求
        if (!valid ) {
            log.error("非法微信回调消息: signature为" + signature +",timestamp为"
                    + timestamp + ",nonce为" + nonce);
            ResponseUtil.writeResponse("error","text/plain");
            return null;
        }

        if (request.getMethod().equals("GET")) {
            //完成验证
            ResponseUtil.writeResponse(echostr,"text/plain");
        } else {
            //处理消息(包括用户发的消息和菜单点击事件)
            processMsg(); 
        }
        return null;
    }


    private boolean isValidateRequest() {
        if (StringUtils.isEmpty(timestamp) || StringUtils.isEmpty(nonce)) return false;
        String sign=WeixinClientUtil.createSign(weixinConf.getSubmitToken(),timestamp,nonce); 
        return sign.equals(signature);
    }


    private void processMsg() {
        try {
            String xml = readPostMsg();
            WeixinBasicMsg msg = WeixinMsgSerializer.fromXml(xml);
            WeixinResponseMsg responseMsg = weixinMsgProcesser.process(msg);
            String responseXml = "";
            if (responseMsg != null ) {
                responseXml = WeixinMsgSerializer.toXml(responseMsg);
            }
            ResponseUtil.writeResponse(responseXml,"text/xml");
        } catch (Exception e) {
            log.error("处理微信消息出错:",e);
            ResponseUtil.writeResponse("","text/plain");
        }
    }

    public String readPostMsg() {
        BufferedReader reader = null;
        try {
            HttpServletRequest request = ServletActionContext.getRequest();
            reader = request.getReader();
            String s;
            StringBuffer postData = new StringBuffer();
            while((s = reader.readLine()) != null) {
                postData.append(s);
            }
            return postData.toString();
        } catch (IOException e) {
            log.warn("读取post数据出错:" + e.toString());
        } finally {
            if (reader != null ) try { reader.close(); } catch (Exception e) {}
        }
        return "";
    }

    public void setSignature(String signature) {
        this.signature=signature;
    }
    public String getSignature() {
        return this.signature;
    }


    public void setTimestamp(String timestamp) {
        this.timestamp=timestamp;
    }
    public String getTimestamp() {
        return this.timestamp;
    }


    public void setNonce(String nonce) {
        this.nonce=nonce;
    }
    public String getNonce() {
        return this.nonce;
    }


    public void setEchostr(String echostr) {
        this.echostr=echostr;
    }
    public String getEchostr() {
        return this.echostr;
    }

    public void setWeixinConf(WeixinConf weixinConf) {
        this.weixinConf=weixinConf;
    }
    public WeixinConf getWeixinConf() {
        return this.weixinConf;
    }


    public void setWeixinMsgProcesser(WeixinMsgProcesser weixinMsgProcesser) {
        this.weixinMsgProcesser=weixinMsgProcesser;
    }
    public WeixinMsgProcesser getWeixinMsgProcesser() {
        return this.weixinMsgProcesser;
    }

}
