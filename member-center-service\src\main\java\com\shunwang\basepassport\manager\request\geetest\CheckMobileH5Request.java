package com.shunwang.basepassport.manager.request.geetest;

import com.shunwang.basepassport.config.pojo.ConfigInterface;
import com.shunwang.basepassport.manager.HttpMethod;
import com.shunwang.basepassport.manager.InterfaceConstant;
import com.shunwang.basepassport.manager.request.BaseRequest;
import com.shunwang.basepassport.manager.response.geetest.CheckMobileH5Response;
import com.shunwang.basepassport.manager.util.HmacSHAUtil;

import java.util.HashMap;
import java.util.Map;

public class CheckMobileH5Request extends BaseRequest<CheckMobileH5Response> {

    private String appId;
    private String appKey;
    private String processId;
    private String token;
    private String authCode;
    private String phone;

    @Override
    public Map<String, String> buildParams() {
        long timeStamp = System.currentTimeMillis();
        Map<String, String> params = new HashMap<>();
        params.put("process_id", processId);
        params.put("token", token);
        params.put("phone", phone);
        params.put("timestamp", timeStamp+"");
        params.put("auth_code", authCode);
        String plainText = appId + "&&" + timeStamp;
        String sign = HmacSHAUtil.sha256(plainText, getAppKey());
        params.put("sign", sign);
        return params;
    }


    @Override
    public Class<CheckMobileH5Response> getResponseClass() {
        return CheckMobileH5Response.class;
    }

    @Override
    public Integer getInterfaceKey() {
        return InterfaceConstant.INTERFACE_KEY_GEETEST_H5_CHECK_MOBILE;
    }

    @Override
    public void doInterfaceSetting(ConfigInterface setting) {
        setUrl(setting.getInterfaceUrl1());
        setAppId(setting.getInterfacePartnerId());
        setAppKey(setting.getInterfaceMd5Key());
    }

    @Override
    public Map<String, String> getHeaders() {
        addHeader("Content-Type","application/x-www-form-urlencoded");
        return headers;
    }

    @Override
    public HttpMethod getHttpMethod() {
        return HttpMethod.POST;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getProcessId() {
        return processId;
    }

    public void setProcessId(String processId) {
        this.processId = processId;
    }

    public String getAuthCode() {
        return authCode;
    }

    public void setAuthCode(String authCode) {
        this.authCode = authCode;
    }

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }
}
