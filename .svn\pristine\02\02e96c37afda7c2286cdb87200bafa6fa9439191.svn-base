/***
  顺网上报pv
**/
var SwPvReport = {
    projectId : "gamecommon",
    businessId : "templatead6",
    //上报时间,格式: 2019-03-18 11:03:30
    report_time : "",
    //客户机的外网ip
    ip : "",
    //网维类型 网维类型，如网维大师
    wwType : "", 
    //网维账号 网维id
    wwAccount : "",  
    //计费类型 如万象、PUBWINOL
    billingType : "",  
    //计费账号 计费id
    billlingAccount : "",  
    //客户机的mac地址
    mac : "",
    //开机唯一标识
    guid : "", 
    //该页面的url
    pageURL : window.location.href,
    
    native_jsonp : function () {
        var that = {};
    
        that.send = function (src, opts) {
            var options = opts || {},
                callback_name = options.callbackName || 'callback',
                on_success = options.onSuccess || function () {
                },
                on_timeout = options.onTimeout || function () {
                },
                timeout = options.timeout || 10;
    
            var timeout_trigger = window.setTimeout(function () {
                window[callback_name] = function () {
                };
                on_timeout();
            }, timeout * 1000);
    
            window[callback_name] = function (data) {
                window.clearTimeout(timeout_trigger);
                on_success(data);
            };
    
            var script = document.createElement('script');
            script.type = 'text/javascript';
            script.async = true;
            script.src = src;
    
            document
                .getElementsByTagName('head')[0]
                .appendChild(script);
        };
    
        return that;
    },
    
    _params : function (data) {
        if (data == null) return data;
        var arr = [];
        for (var i in data) {
            arr.push(encodeURIComponent(i) + '=' + encodeURIComponent(data[i]));
        }
        return arr.join("&");
    },
    getBarInfo : function (swcallback) {
        var _this = this;
        var _url = 'https://swac.5iss.cn:9186/SWBasicInfo';
        var data = null;
        var _data = _this._params(data);
        if (typeof jQuery !== "undefined") {
            $.ajax({
                url: _url,
                dataType: 'jsonp',
                type: 'get',
                jsonp: 'callback',
                jsonpCallback: 'SWR_LOC_JSONP',
                timeout: 800,
                success: function (result) {
                    try {
                        var json = JSON.parse(_this.decrypt_mode1(_this.Decode(result.data)));
                        _this.guid = json.guid;
                        //网维类型 网维类型，如网维大师
                        _this.wwType = json.bar_manage.manage_type;
                        //网维账号 网维id
                        _this.wwAccount = json.bar_manage.manage_id;
                        //计费类型 如万象、PUBWINOL
                        _this.billingType = json.bar_charge.charge_type;
                        //计费账号 计费id
                        _this.billlingAccount = json.bar_charge.charge_id;
                        //页面加载的时候上报
                        _this.shangbao(swcallback);
                    } catch (e) {
                        _this.shangbao(swcallback);
                    }
                },
                error: function (e, t) {
                    //页面加载的时候上报
                    _this.shangbao(swcallback);
                }
            });
        } else {
            _this.native_jsonp().send(_url + '?' + _data + '&callback=swcallback&_t='+new Date().getTime(), {
                callbackName: 'swcallback',
                onSuccess: function (res) {
                    //_this.shangbao(swcallback);
                    //swcallback && swcallback();
                    try {
                        var json = JSON.parse(_this.decrypt_mode1(_this.Decode(res.data)));
                        _this.guid = json.guid;
                        //网维类型 网维类型，如网维大师
                        _this.wwType = json.bar_manage.manage_type;
                        //网维账号 网维id
                        _this.wwAccount = json.bar_manage.manage_id;
                        //计费类型 如万象、PUBWINOL
                        _this.billingType = json.bar_charge.charge_type;
                        //计费账号 计费id
                        _this.billlingAccount = json.bar_charge.charge_id;
                        //页面加载的时候上报
                        _this.shangbao(swcallback);
                    } catch (e) {
                        _this.shangbao(swcallback);
                    }
                },
                onTimeout: function (e) {
                    //swcallback && swcallback();
                    _this.shangbao(swcallback);
                },
                timeout: 3
            });
        }
    },

    decrypt_mode1 : function (data) {
        var out = ""
        var len = data.length;
        var key = new Uint8Array([0x21, 0x43, 0x65, 0x87, 0x90, 0xBA, 0xDC, 0xFE])
        var index = 0;
        for (var i = 0; i < len; ++i) {
            var charCode = data[i].charCodeAt() ^ key[index]
            out += String.fromCharCode(charCode)
            index = (index + 1) % (key.length)
        }

        return out
    },

    Decode : function (str) {
        var length = str.length;
        //解码表
        var base64_pad = '=';
        var DecodeTable = new Array(
            -2, -2, -2, -2, -2, -2, -2, -2, -2, -1, -1, -2, -2, -1, -2, -2,
            -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2,
            -1, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, 62, -2, -2, -2, 63,
            52, 53, 54, 55, 56, 57, 58, 59, 60, 61, -2, -2, -2, -2, -2, -2,
            -2, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14,
            15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, -2, -2, -2, -2, -2,
            -2, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40,
            41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, -2, -2, -2, -2, -2,
            -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2,
            -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2,
            -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2,
            -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2,
            -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2,
            -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2,
            -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2,
            -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2
        );
        var bin = 0, i = 0, pos = 0;
        var _decode_result = "";
        var current = str;
        var ch;
        var count = 0;
        while ((ch = current[count]) != '\0' && length-- > 0) {
            if (ch == base64_pad) { // 当前一个字符是“=”号
                /*
                先说明一个概念：在解码时，4个字符为一组进行一轮字符匹配。
                两个条件：
                1、如果某一轮匹配的第二个是“=”且第三个字符不是“=”，说明这个带解析字符串不合法，直接返回空
                2、如果当前“=”不是第二个字符，且后面的字符只包含空白符，则说明这个这个条件合法，可以继续。
                */
                if (current[count++] != '=' && (i % 4) == 1) {
                    return NULL;
                }
                continue;
            } else
                count++;
            var tmp = ch.charCodeAt();
            ch = DecodeTable[tmp];
            //这个很重要，用来过滤所有不合法的字符
            if (ch < 0) { /* a space or some other separator character, we simply skip over */
                continue;
            }
            switch (i % 4) {
                case 0:
                    bin = ch << 2;
                    break;
                case 1:
                    bin |= ch >> 4;
                    _decode_result += String.fromCharCode(bin);
                    bin = (ch & 0x0f) << 4;
                    break;
                case 2:
                    bin |= ch >> 2;
                    _decode_result += String.fromCharCode(bin);
                    bin = (ch & 0x03) << 6;
                    break;
                case 3:
                    bin |= ch;
                    _decode_result += String.fromCharCode(bin);
                    break;
            }
            i++;
        }
        return _decode_result;
    },

    getSessionId : function () {
        var data = ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z", "a", "b", "c", "d", "e", "f", "g", "h", "i", "j", "k", "l", "m", "n", "o", "p", "q", "r", "s", "t", "u", "v", "w", "x", "y", "z"];
        var nums = "";
        for (var i = 0; i < 32; i++) {
            var r = parseInt(Math.random() * 61);
            nums += data[r];
        }
        return nums;

    },
    shangbao : function (swcallback) {
        var _this = this;
        swcallback && swcallback(_this);
    }
}
