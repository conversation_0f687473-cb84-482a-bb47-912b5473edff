
<configuration>
    <contextName>sso</contextName>
    <property name="logPattern" value="[%d{yyyy-MM-dd HH:mm:ss.SSS}][%contextName][%X{kd-traceId:-N/A}][%X{kd-spanId:-N/A}][%X{kd-parent-spanId:-N/A}][%X{appId:-N/A}][%X{service:-N/A}][%p][%t][%logger{36}.%L]-%m%n"/>

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${logPattern}</pattern>
        </encoder>
    </appender>

    <appender name="appLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${SSO_KEDOU_LOG_HOME}/app.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${SSO_KEDOU_LOG_HOME}/app-%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>${logPattern}</pattern>
        </encoder>

    </appender>

    <appender name="errorLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${SSO_KEDOU_LOG_HOME}/error.log</file>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${SSO_KEDOU_LOG_HOME}/error-%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>${logPattern}</pattern>
        </encoder>
    </appender>

    <appender name="sqlLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${SSO_KEDOU_LOG_HOME}/sql/sql.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${SSO_KEDOU_LOG_HOME}/sql/sql-%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>${logPattern}</pattern>
        </encoder>
    </appender>

    <appender name="filterLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${SSO_KEDOU_LOG_HOME}/filter/filter.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${SSO_KEDOU_LOG_HOME}/filter/filter-%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>${logPattern}</pattern>
        </encoder>
    </appender>

    <appender name="userLoginSession" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${SSO_KEDOU_LOG_HOME}/userLoginSession.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${SSO_KEDOU_LOG_HOME}/userLoginSession-%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>${logPattern}</pattern>
        </encoder>
    </appender>

    <!-- 计费上机上报数据 -->
    <appender name="reportLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${SSO_KEDOU_LOG_HOME}/report/report.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${SSO_KEDOU_LOG_HOME}/report/report_%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>5</maxHistory>
        </rollingPolicy>

        <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
            <providers class="net.logstash.logback.composite.loggingevent.LoggingEventJsonProviders">
                <pattern>
                    <pattern>
                        {
                        "date":"%d{yyyy-MM-dd HH:mm:ss.SSS}",
                        "msg":"#asJson{%msg}"
                        }
                    </pattern>
                </pattern>
            </providers>
        </encoder>
    </appender>

    <logger name="com.shunwang" level="INFO" additivity="true">
        <appender-ref ref="appLog"/>
        <appender-ref ref="errorLog"/>
    </logger>

    <logger name="org.springframework.cache" level="info">
        <appender-ref ref="appLog"/>
    </logger>

    <logger name="java.sql" level="DEBUG" additivity="true">
        <appender-ref ref="sqlLog"/>
    </logger>

    <logger name="com.shunwang.basepassport.filter.InterfaceLogFilter" level="INFO" additivity="false">
        <appender-ref ref="filterLog"/>
    </logger>

    <logger name="com.shunwang.baseStone.sso.netBarReportLog" level="INFO" additivity="true">
        <appender-ref ref="reportLog"/>
    </logger>

    <logger name="com.shunwang.basepassport.user.common.UserLoginSessionUtil" level="INFO" additivity="true">
        <appender-ref ref="userLoginSession"/>
    </logger>
    
    <logger name="java.sql.ResultSet" level="error" />

    <root level="INFO">
        <appender-ref ref="STDOUT"/>
    </root>
</configuration>
