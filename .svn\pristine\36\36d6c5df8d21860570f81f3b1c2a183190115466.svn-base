<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>无标题文档</title>
<link rel="stylesheet" href="../styles/control/msgbox.css" />
<link rel="stylesheet" href="../styles/v2/mend.css" />
</head>
<body>
<input type="button" value="确 定" id="content_n" />
<div id="content" class="tc mar40">
<table class="autForm" border="0" cellspacing="0" cellpadding="0">
<tbody>
  <tr>
    <td class="title" colspan="2">请确认公司信息：</td>
    </tr>
  <tr>
    <th>公司名称：</th>
    <td>xxx有限公司</td>
  </tr>
  <tr>
    <th>法人：</th>
    <td>张阿达</td>
  </tr>
  <tr>
    <th>法人手机号码：</th>
    <td>13555252545</td>
  </tr>
  <tr>
    <th>组织机构代码：</th>
    <td>123456789</td>
  </tr>
    <tr>
    <th>营业执照号：</th>
    <td>123456789</td>
  </tr>
    <tr>
    <th>营业执照所在地：</th>
    <td>浙江省  杭州市</td>
  </tr>
    <tr>
    <th>营业执照有效期：</th>
    <td>长期</td>
  </tr>
    <tr>
    <th>联系地址：</th>
    <td>杭州市文一西路98号数娱大厦5f</td>
  </tr>
    <tr>
    <th>联系人：</th>
    <td>张阿大</td>
  </tr>
    <tr>
    <th>联系电话：</th>
    <td>0571-123456789</td>
  </tr>
    <tr>
    <th>传真：</th>
    <td>0571-123456789</td>
  </tr>
    <tr>
    <th>开票类型：</th>
    <td>增值税专业发票</td>
  </tr>
    <tr>
    <th>企业税务登记证税号：</th>
    <td>123456789</td>
  </tr>
  <tr>
    <th>企业税务登记证扫描件：</th>
    <td>已上传 查看</td>
  </tr>
    <tr>
    <th>营业执照副本扫描件：</th>
    <td>已上传 查看</td>
  </tr>
  </tbody>
</table>
</div>
<script type="text/javascript" src="../scripts/common/jquery.js"></script>
<script type="text/javascript" src="../scripts/common/control/msgbox.min.js"></script>
<script type="text/javascript">
	$(function(){
		var obj1=new MsgBox({
			title:"请确认个人信息" ,//动态添加title
			masklayer:{
				bgColor:"#000", //遮罩层颜色
				opacity:0.6 //遮罩层透明度
			},
			zIndex: 1000, //层叠顺序
			content:$("#content"), //内容
			btns:[ //是否显示按钮
					{
						text:"确 认" ,//按钮显示的文字
						//style:null,//按钮样式，string类型为class名称，也可为对象类型，{"margin-right":"30px"}
						onClick:function(){alert("你点击我了");}//点击按钮触发的事件在这里绑定
					}
				]
		});
		
		
		
		$("#content_n").click(function(){
			obj1.show();
		});
		

	})


</script>
</body>
</html>
