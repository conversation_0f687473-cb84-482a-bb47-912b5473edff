<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">
	<bean id="pageHomeDao" class="com.shunwang.baseStone.pagehome.dao.PageHomeDao"  >
		<property name="dbDao" ref="pageHomeDbDao"></property>
		<property name="cacheDao" ref="pageHomeCacheDao"></property>
	</bean>
	<bean id="pageHomeDbDao" class="com.shunwang.baseStone.pagehome.dao.PageHomeDbDao"  >
		<property name="sqlMapClient" ref="baseStone.sqlMapClient"></property>
	</bean>
	<bean id="pageHomeCacheDao" class="com.shunwang.baseStone.pagehome.dao.PageHomeCacheDao"  >
	</bean>

</beans>
