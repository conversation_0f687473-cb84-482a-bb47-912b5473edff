package com.shunwang.passport.swpaySDK.web.action;

import com.opensymphony.xwork2.util.logging.Logger;
import com.opensymphony.xwork2.util.logging.LoggerFactory;
import com.shunwang.baseStone.cache.lock.CounterLock;
import com.shunwang.baseStone.cache.service.CacheService;
import com.shunwang.baseStone.checkCode.context.CheckCodeContext;
import com.shunwang.baseStone.context.BaseStoneContext;
import com.shunwang.baseStone.context.LoginElementService;
import com.shunwang.baseStone.core.exception.BaseStoneException;
import com.shunwang.baseStone.loginelement.constants.LoginElementConstant;
import com.shunwang.baseStone.sso.context.RegContext;
import com.shunwang.basepassport.actu.constant.ActuConstant;
import com.shunwang.basepassport.binder.common.BinderConstants;
import com.shunwang.basepassport.binder.common.ErrorCode;
import com.shunwang.basepassport.binder.dao.BinderDao;
import com.shunwang.basepassport.binder.dao.PersonalSendNumberDao;
import com.shunwang.basepassport.binder.exception.*;
import com.shunwang.basepassport.binder.pojo.Binder;
import com.shunwang.basepassport.binder.pojo.MobileBinder;
import com.shunwang.basepassport.binder.pojo.PersonalSendNumber;
import com.shunwang.basepassport.binder.pojo.SendBinder;
import com.shunwang.basepassport.context.UserContext;
import com.shunwang.basepassport.key.common.UserRegisterKeyUtil;
import com.shunwang.basepassport.user.common.*;
import com.shunwang.basepassport.user.dao.MemberDao;
import com.shunwang.basepassport.user.pojo.Member;
import com.shunwang.basepassport.user.pojo.MemberInfo;
import com.shunwang.common.StringUtils;
import com.shunwang.common.util.string.StringUtil;
import com.shunwang.passport.common.context.DomainContext;
import com.shunwang.passport.common.geetest.GeetestUtil;
import com.shunwang.passport.swpaySDK.exception.SWPaySDKRegException;
import com.shunwang.util.date.DateUtil;
import com.shunwang.util.encrypt.Md5Encrypt;
import com.shunwang.util.math.RandomUtil;
import com.shunwang.util.net.HttpClientUtils;
import com.shunwang.util.net.IpUtil;
import org.apache.struts2.ServletActionContext;
import org.jdom.Document;
import org.jdom.Element;
import org.jdom.JDOMException;
import org.jdom.input.SAXBuilder;

import java.io.IOException;
import java.io.StringReader;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.time.Duration;
import java.util.Date;
import java.util.Map;
import java.util.TreeMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @since 2015-04-28
 */
public class RegisterAction extends SWPaySDKBaseAction {

    private static final long serialVersionUID = 1728491056510848246L;

	private Logger log = LoggerFactory.getLogger(RegisterAction.class) ;

	private LoginElementService loginElementService ;

    private String mobile;
    private String password;
	private String mobileActiveNo ;
    private String siteId;
    private String regEnvironment;
    private String siteVersion;
    private String remark;
	private String userName;
	private String confrimPwd;
	private String returnUrl;
	/**
	 * 1:需要;0或空：不需要
	 */
	private String needCode ;
	private String interfaceSiteId;
	private String interfaceMd5Key;
	private String interfaceUrl;

	private String realName ;
	private String idCard ;
	
	private String h5RegCheckCode;
	private String h5RegCheckCodeSiteId;
	/**
	 * 是否是SDK调用，默认为是
	 */
	private Boolean sdk ;
	/**
	 * 是否极验校验
	 */
	private boolean showGt;

	@Override
	public String execute() {
		Map<String, Object> json = createResultJSON(false);
		try {
			validParam();

			Member member = buildMember().regist();
			// 非sdk，注册完做一次登录
			if(null != sdk && !sdk){
				UserContext.setUser(member);
			}
			json.put(RESULT, true);
			json.put(MSG, "注册成功");
			json.put("userId", member.getMemberId());
		} catch (SWPaySDKRegException e) {
			logger.error("手机SDK注册异常，[memberName=" + userName + "，siteId=" + siteId + "]：", e);
			json.put(MSG, e.getMsg());
		} catch (BaseStoneException e) {
			logger.error("手机SDK注册异常，[memberName=" + userName + "，siteId=" + siteId + "]：", e);
			json.put(MSG, e.getMsg());
		} catch (Exception e) {
			logger.error("手机SDK注册异常，[memberName=" + userName + "，siteId=" + siteId + "]：", e);
			json.put(MSG, "系统异常，请稍后重试");
		}

		writeJSON(json);
		return null;
	}
	
	public String doNewReg() {
		Member member = null;
		try {
			validParam();
			member = buildMember().regist();
		} catch (SWPaySDKRegException e) {
			logger.error("手机SDK注册异常，[memberName=" + userName + "，siteId=" + siteId + "]：", e);
			this.setMsg(e.getMessage());
			return INPUT;
		} catch (BaseStoneException e) {
			logger.error("手机SDK注册异常，[memberName=" + userName + "，siteId=" + siteId + "]：", e);
			this.setMsg(e.getMessage());
			return INPUT;
		} catch (Exception e) {
			logger.error("手机SDK注册异常，[memberName=" + userName + "，siteId=" + siteId + "]：", e);
			this.setMsg("系统异常，请稍后重试");
			return INPUT;
		}
		try {	
			
			if(null != sdk && !sdk){
				UserContext.setUser(member);
			}
			RegContext.jumpUrl(
					member.getMemberId(),
					member.getMemberName(),
					buildJumpUrl(member.getMemberId()),
					siteVersion==null?"":siteVersion,
					regEnvironment==null?"":regEnvironment,
					remark==null?"":remark,
					siteId==null?"":siteId,
					ServletActionContext.getRequest(),
					ServletActionContext.getResponse());
			return null;
	    }catch (UnsupportedEncodingException e){
			logger.error("doNewReg跳转登录失败",e) ;
		} catch (Exception e) {
			logger.error("doNewReg获取app免登code失败",e) ;
		}
		try {
			ServletActionContext.getResponse().sendRedirect(DomainContext.getAppServer() + "/front/swpaysdk/member/login.jsp");
		} catch (IOException e) {
			logger.error("doNewReg重定向出错", e);
		}
		return null;
	}

    public String doRegForHtml5() {

		//检查参数
		if (StringUtil.isBlank(mobile)) {
			this.setMsg("请输入手机短信验证码");
			return INPUT ;
		}
		if(!UserCheckUtil.checkMobile(mobile)) {
			this.setMsg("手机号码格式错误，请重新输入");
			return INPUT ;
		}
		if (StringUtil.isBlank(password)) {
			this.setMsg("请设置登录密码");
			return INPUT ;
		}
		if(StringUtil.isBlank(mobileActiveNo)) {
			this.setMsg("请输入手机短信验证码") ;
			return INPUT ;
		}
		//实名注册
		if(!checkRegActual()){
			return INPUT ;
		}
		//验证验证码是否正确
		SendBinder sendBinder;
		try {
            CounterLock smsCheckLock = CacheService.newCounterLock("ANTI_ADDITION_REG_" + getMobile(), 3, Duration.ofMinutes(1L));
            if (!smsCheckLock.tryLock()) {
                this.setMsg(ErrorCode.C_1102.getDescription()) ;
                return INPUT ;
            }
			BinderDao binderDao = getBinderDao();
			sendBinder = (SendBinder) binderDao.getRegitsterBinderByNumber(getMobile());
			if(null == sendBinder){
				this.setMsg("请获取短信验证码") ;
				return INPUT ;
			}
			sendBinder.setNumber(getMobile());
			sendBinder.setCodeAvailableMinutes(10);
			sendBinder.validate(this.getMobileActiveNo());
		} catch (TimeOutAndInvalidExp exp) {
			//验证码已经过期
			this.setMsg("手机短信验证码已过期,请重新获取");
			return INPUT ;
		} catch (ActiveNoErrorExp exp) {
			//验证码不正确
			this.setMsg("手机短信验证码错误，请重新输入");
			return INPUT ;
		}
		//判断手机是否绑定通行证
		if (null != getMemberDao().getByMobile(mobile)) {
			//已经绑定,提醒去登录
			this.setMsg("该手机号码已注册，请直接登录");
			return INPUT ;
		} else {
		    Member currentMember;
            //没有绑定,去注册
            Member member = buildRegisterMember();
            try {
                currentMember = member.regist(false);
            }catch (Exception e){
                this.setMsg(e.getMessage()) ;
                return INPUT ;
            }

			UserContext.setUser(currentMember);
			try {
				RegContext.jumpUrl(
						currentMember.getMemberId(),
						currentMember.getMemberName(),
						buildJumpUrl(currentMember.getMemberId()),
						siteVersion==null?"":siteVersion,
						regEnvironment==null?"":regEnvironment,
						remark==null?"":remark,
						siteId==null?"":siteId,
						ServletActionContext.getRequest(),
						ServletActionContext.getResponse());
				return null;
			}catch (UnsupportedEncodingException e){
				logger.error("跳转登录失败",e) ;
			} catch (Exception e) {
				logger.error("获取app免登code失败",e) ;
			}
			try {
				ServletActionContext.getResponse().sendRedirect(DomainContext.getAppServer() + "/front/swpaysdk/member/login.jsp");
			} catch (IOException e) {
				logger.error("doRegForHtml5 重定向出错", e);
			}
			return null;
		}
    }

    private void bindAsLoginAccount(Member member, String mobile){
    	member.setMobile(mobile);
    	member.setIsBindMobile(true) ;
		member.setMoblieAsLoginAccount(true) ;
		member.setSafeItemsScore(member.recheckSafeItemsScore());
		member.update();
	}

    private boolean checkRegActual(){
		if(loginElementService.getIsNeedActualRegBySiteId(siteId)){
			if(StringUtil.isBlank(realName)){
				this.setMsg("请输入真实姓名") ;
				return false ;
			}
			if(realName.length() < 2 || realName.length() > 10){
				this.setMsg("真实姓名请使用中文，2-10个汉字");
				return false ;
			}
			String regEx = "^[\\u4e00-\\u9fa5|\\u00b7]{2,32}$" ;
			Pattern pattern = Pattern.compile(regEx);
			Matcher matcher = pattern.matcher(realName);
			if(!matcher.matches()){
				this.setMsg("真实姓名请使用中文，2-10个汉字") ;
				return false ;
			}
			if(StringUtil.isBlank(idCard)){
				this.setMsg("请输入身份证号码") ;
				return false ;
			}
			if (!IDCardCheckUtil.isAvailableIDCard(idCard)) {
				this.setMsg("身份证号码不合法");
				return false ;
			}
			if (!IDCardCheckUtil.isAgeAbove18(idCard)) {
				this.setMsg("注册用户须年满18周岁");
				return false ;
			}
		}
		return true ;
	}

	private Member buildMember() {
		Member member = new Member();

		member.setMemberName(userName);
		member.setRealName(realName) ;

		String md5PwdStr = PwdUtil.convertMd5(password);
		member.setMemberPwd(md5PwdStr);

		member.setBindState(MemberConstants.MEMBER_STATE_NOTHING);
		member.setMemberState(MemberConstants.USER_NATURAL_STATE);
		member.setPersonCertState(Integer.parseInt(ActuConstant.INFO_STATE_UN_APPLY));
		member.setCompanyCertState(Integer.parseInt(ActuConstant.INFO_STATE_UN_APPLY));
		member.setMemberType(MemberConstants.USER_TYPE_PERSONAL);

		MemberInfo memberInfo = new MemberInfo();
		memberInfo.setIdCardNo(idCard) ;
		memberInfo.setRegFrom(siteId);
		memberInfo.setRegEnvironment(regEnvironment);
		memberInfo.setRegVersion(siteVersion);
		memberInfo.setRemark(remark);
		memberInfo.setRegIp(IpUtil.getIpAddress(getRequest()));

		member.setMemberInfo(memberInfo);

		return member;
	}

	private void validParam() {
		if (org.apache.commons.lang.StringUtils.isBlank(userName)) {
			throw new SWPaySDKRegException("用户名不能为空");
		}
		if (org.apache.commons.lang.StringUtils.isBlank(password)) {
			throw new SWPaySDKRegException("密码不能为空");
		}
		if (org.apache.commons.lang.StringUtils.isBlank(confrimPwd)) {
			throw new SWPaySDKRegException("确认密码不能为空");
		}
		if (!password.equals(confrimPwd)) {
			throw new SWPaySDKRegException("两次密码输入不一致");
		}
		if (!checkRegActual()){
			throw new SWPaySDKRegException(this.getMsg()) ;
		}
		if (!GeetestUtil.validate(getRequest())) {
			throw new SWPaySDKRegException("极验验证失败");
		}
	}


	private String buildJumpUrl(Integer memberId) throws Exception {
		String jumpUrl = "";
		if(StringUtils.isNotBlank(returnUrl)) {
			jumpUrl = returnUrl;
		} else {
			jumpUrl = DomainContext.getAppServer() + "/front/swpaysdk/myAccount.htm";
		}
		if ("1".equals(needCode) ) {
			String result = HttpClientUtils.doPost(getInterfaceUrl(),buildParams(memberId));
			String code = parseResult(result);
			String split = "?";
			if(jumpUrl.indexOf("?") > -1) {
				split = "&";
			}
			if (StringUtils.isNotBlank(code)) {
				jumpUrl = jumpUrl + split + "msg=succ&code=" + code;
			} else {
				jumpUrl = jumpUrl + split + "msg=" + getErrorMsg();
			}
		}
		jumpUrl = URLEncoder.encode(jumpUrl, "UTF-8");
		return jumpUrl;
	}

	/**
	 * 当手机短信快捷登录或使用手机号+密码注册时（bindType=1或bindType=3）。
	 * 若用户使用手机号135XXX注册或快捷登录时，
	 * 已存在手机账号SJ_135XXX且该账号的绑定账号为136XXX（非135XXX）未被设置为登录账号，
	 * 则系统自动创建SJ_135XXX随机三位数账号。（取消现“系统异常”的报错）
	 * SJ_手机号_xxx
	 * @param sjName
	 * @return
	 */
	private String  buildTmpMemberName(String sjName){
		String name = sjName + RandomUtil.getRandomStr(3);
		Member registerMember = getMemberDao().getByName(name);
		if(registerMember != null){
			return buildTmpMemberName(sjName);
		}

		return name;
	}

	/**
	 * 构建实体
	 * @return
	 */
	private Member buildRegisterMember() {
		Member member = new Member() ;
		//通行证账号
		String memberName = MemberUtil.buildTmpMemberName(getMobile()) ;

		member.setMemberName(memberName);
		member.setRealName(realName) ;
		//密码加密规则
		member.setMemberPwd(PwdUtil.convertMd5(getPassword())) ;
		member.setMobile(mobile) ;
		//设置手机绑定
		MobileBinder mobileBinder = new MobileBinder() ;
		mobileBinder.setNumber(getMobile()) ;
		mobileBinder.setFirstActiveTime(new Date());
		member.setMobileBinder(mobileBinder) ;
		member.setBindState(MemberConstants.MEMBER_STATE_PHONE|MemberConstants.MEMBER_STATE_PHONE_AS_LOGIN_ACCOUNT);
		//用户类型(个人/公司)
		member.setMemberType(MemberConstants.USER_TYPE_PERSONAL) ;
		//账号状态
		member.setMemberState(MemberConstants.MEMBER_STATE_NOTHING) ;
		//实名状态
		member.setPersonCertState(0) ;
		member.setCompanyCertState(0) ;
		MemberInfo memberInfo = new MemberInfo() ;
		memberInfo.setIdCardNo(idCard) ;
		//设置注册ip
		memberInfo.setRegIp(getRealIp());
		//设置注册环境
		memberInfo.setRegFrom(siteId!=null?siteId:"Passport_html5");
		memberInfo.setRegVersion(siteVersion) ;
		memberInfo.setRegEnvironment(null==regEnvironment?"":regEnvironment) ;
		member.setMemberInfo(memberInfo) ;
		return member ;
	}

	public String sendRegActiveNo() {
		Map<String, Object> json = createResultJSON(false);
		try {
			if (GeetestUtil.isShowGt()) {
				if (!GeetestUtil.validate(getRequest())) {
					json.put(RESULT, false);
					json.put(MSG, "极验校验失败");
					writeJSON(json);
					return null;
				}
			} else {
                Map<String, String> configMap = loginElementService.getLoginConfig(h5RegCheckCodeSiteId, LoginElementConstant.SMS_CHECK_CODE);
				if (configMap != null) {
                    //初始化短信登录验证码
                    if (null != configMap.get(LoginElementConstant.STATE)
                            && configMap.get(LoginElementConstant.STATE).equals("1")) { //手机短信登录校验码开启
                        if (StringUtil.isBlank(h5RegCheckCode)) {
                            json.put(RESULT, false);
                            json.put(MSG, "请输入验证码");
                            writeJSON(json);
                            return null;
                        } else {
                            try {
                                CheckCodeContext.validateH5RegCheckCode(h5RegCheckCode);
                            } catch (Exception e) {
                                log.error("验证码校验失败", e);
                                json.put(RESULT, false);
                                json.put(MSG, "验证码错误");
                                writeJSON(json);
                                return null;
                            }
                        }

                    }
                }
			}
			beforeSend() ;
			send();
			json.put(RESULT, true);
			json.put(MSG, "发送成功");
			//校验用户是否已注册, 已注册用户需要勾选 用户协议,反之则系统不勾选.
			Member member = UserRegisterKeyUtil.getMember(getMobile());
			json.put(IS_REG, member != null);
		} catch (FormatErrorExp e) {
			json.put(MSG, e.getMsg());
		} catch (OneMinuteSendExp ex){
			json.put(MSG,"同一业务一分钟只能发送一次验证码") ;
		}catch (Exception e) {
			logger.error("注册时发送手机验证码失败"+e.getMessage());
			json.put(MSG, "系统异常，请稍后重试");
		}
		writeJSON(json);
		return null;
	}

	protected void send() {
		//检查是否发送时间和次数
		checkSendNumber();
		getBinder().send();
	}

	private void beforeSend() {
		PersonalSendNumber personalSendNumber = buildPersonalSendNumber() ;
		Integer cnt = getPersonalSendNumberDao().findCntByTime(personalSendNumber) ;
		if(null != cnt && cnt > 0 ){
			throw new OneMinuteSendExp("统一业务一分钟只能发送一次验证码") ;
		}
	}

	private PersonalSendNumberDao getPersonalSendNumberDao() {
		return (PersonalSendNumberDao) BaseStoneContext.getInstance().getBean("personalSendNumberDao");
	}

	private PersonalSendNumber buildPersonalSendNumber() {
		PersonalSendNumber personalSendNumber = new PersonalSendNumber() ;
		personalSendNumber.setNumber(getMobile()) ;
		personalSendNumber.setBeginDate(DateUtil.ymdhmsFormat(DateUtil.addMinute(new Date(),-1)));
		return personalSendNumber ;
	}

	protected Binder getBinder() {
		MobileBinder mobileBinder = new MobileBinder();
		mobileBinder.setNumber(this.getMobile());
		//注册,设memberId=-1
		mobileBinder.setMemberId(-1) ;
		mobileBinder.setBusinessType(BinderConstants.MOBLIE_REGISTER) ;
		return mobileBinder ;
	}

	private void checkSendNumber() {
		if(StringUtils.isBlank(mobile)){
			throw new ParamNullExp("手机号码") ;
		}
		if(!UserCheckUtil.checkMobile(mobile)) {
			throw new FormatErrorExp("手机号码格式");
		}
	}

	private String parseResult(String xmlText) {
		String code = "";
		SAXBuilder builder = new SAXBuilder();
		StringReader strReader = new StringReader(xmlText);
		try {
			Document document = (Document) builder.build(strReader);
			Element rootNode = document.getRootElement();
			String msgId =  ((Element)(rootNode.getChildren("Result").get(0))).getAttributeValue("msgId");
			setErrorMsg(((Element)(rootNode.getChildren("Result").get(0))).getAttributeValue("msg"));
			if("0".equals(msgId)) {
				code = ((Element) (rootNode.getChildren("Result").get(0))).getChildText("ticket");
			}
		} catch (IOException e) {
			log.error("解析消息出错", e);
		} catch (JDOMException e) {
			log.error("解析消息出错", e);
		}
		return code;
	}

	private Map<String, String> buildParams(Integer memberId){
		String time = DateUtil.getCurrentDateStamp();
		Map<String, String> paramMap = new TreeMap<String, String>();
		paramMap.put("memberId", String.valueOf(memberId));
		paramMap.put("siteId", getInterfaceSiteId());
		paramMap.put("time", time);
		paramMap.put("sign", buildSign(paramMap));
		return paramMap;
	}

	private String buildSign(Map<String, String> paramMap) {
		StringBuffer stringBuffer = new StringBuffer();
		for(String keySet: paramMap.keySet()){
			stringBuffer.append(paramMap.get(keySet)).append("|");
		}
		stringBuffer.append(getInterfaceMd5Key());
		String sign = "";
		try{
			sign = URLEncoder.encode(stringBuffer.toString(),"utf-8").toUpperCase();
		}catch(UnsupportedEncodingException e){
			log.error("对签名转码错误", e);
		}
		sign = Md5Encrypt.encrypt(sign).toUpperCase();
		if(log.isInfoEnabled()) {
			log.info("getTicketWithoutLogin:"+stringBuffer.toString()+",sign："+sign);
		}
		return sign;
	}

	private BinderDao getBinderDao() {
		return (BinderDao) BaseStoneContext.getInstance().getBean("mobileBinderDao") ;
	}

	private MemberDao getMemberDao() {
		return (MemberDao) BaseStoneContext.getInstance().getBean("memberDao") ;
	}

	public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getSiteId() {
        return siteId;
    }

    public void setSiteId(String siteId) {
        this.siteId = siteId;
    }

    public String getRegEnvironment() {
        return regEnvironment;
    }

    public void setRegEnvironment(String regEnvironment) {
        this.regEnvironment = regEnvironment;
    }

    public String getSiteVersion() {
        return siteVersion;
    }

    public void setSiteVersion(String siteVersion) {
        this.siteVersion = siteVersion;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public String getMobileActiveNo() {
		return mobileActiveNo;
	}

	public void setMobileActiveNo(String mobileActiveNo) {
		this.mobileActiveNo = mobileActiveNo;
	}


	public String getConfrimPwd() {
		return confrimPwd;
	}

	public void setConfrimPwd(String confrimPwd) {
		this.confrimPwd = confrimPwd;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getReturnUrl() {
		return returnUrl;
	}

	public void setReturnUrl(String returnUrl) {
		this.returnUrl = returnUrl;
	}

	public String getNeedCode() {
		return needCode;
	}

	public void setNeedCode(String needCode) {
		this.needCode = needCode;
	}

	public String getInterfaceUrl() {
		return interfaceUrl;
	}

	public void setInterfaceUrl(String interfaceUrl) {
		this.interfaceUrl = interfaceUrl;
	}

	public String getInterfaceSiteId() {
		return interfaceSiteId;
	}

	public void setInterfaceSiteId(String interfaceSiteId) {
		this.interfaceSiteId = interfaceSiteId;
	}

	public String getInterfaceMd5Key() {
		return interfaceMd5Key;
	}

	public void setInterfaceMd5Key(String interfaceMd5Key) {
		this.interfaceMd5Key = interfaceMd5Key;
	}

	public String getRealName() {
		return realName;
	}

	public void setRealName(String realName) {
		this.realName = realName;
	}

	public String getIdCard() {
		return idCard;
	}

	public void setIdCard(String idCard) {
		this.idCard = idCard;
	}

	public LoginElementService getLoginElementService() {
		return loginElementService;
	}

	public void setLoginElementService(LoginElementService loginElementService) {
		this.loginElementService = loginElementService;
	}


	public String getH5RegCheckCode() {
		return h5RegCheckCode;
	}


	public void setH5RegCheckCode(String h5RegCheckCode) {
		this.h5RegCheckCode = h5RegCheckCode;
	}


	public String getH5RegCheckCodeSiteId() {
		return h5RegCheckCodeSiteId;
	}


	public void setH5RegCheckCodeSiteId(String h5RegCheckCodeSiteId) {
		this.h5RegCheckCodeSiteId = h5RegCheckCodeSiteId;
	}

	public Boolean getSdk() {
		return sdk;
	}

	public void setSdk(Boolean sdk) {
		this.sdk = sdk;
	}

	public boolean isShowGt() {
		return showGt;
	}

	public void setShowGt(boolean showGt) {
		this.showGt = showGt;
	}
}
