package com.shunwang.basepassport.user.dao;

import com.shunwang.baseStone.core.dao.BaseStoneIbatisDao;
import com.shunwang.basepassport.user.pojo.IdcardBind;

public class IdcardBindDao extends BaseStoneIbatisDao<IdcardBind>{

    public IdcardBind getByIdcard(IdcardBind idcardBind) {
		return (IdcardBind)getSqlMapClientTemplate().queryForObject(getStatementNameWrap("getIdcardBind"),idcardBind);
    }

}
