<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd" >
<sqlMap namespace="com.shunwang.basepassport.detail.pojo.PersonalEditLog" >
<typeAlias alias="personalEditLog" type="com.shunwang.basepassport.detail.pojo.PersonalEditLog"/>
<resultMap id="personalEditLogMap" class="com.shunwang.basepassport.detail.pojo.PersonalEditLog">
    <result column="edit_id" property="editId" jdbcType="INTEGER" />
    <result column="member_id" property="memberId" jdbcType="INTEGER" />
    <result column="member_name" property="memberName" jdbcType="VARCHAR" />
    <result column="edit_item" property="editItem" jdbcType="VARCHAR" />
    <result column="value_pre" property="valuePre" jdbcType="VARCHAR" />
    <result column="value_cur" property="valueCur" jdbcType="VARCHAR" />
    <result column="time_add" property="timeAdd" jdbcType="TIMESTAMP" />
    <result column="user_add" property="userAdd" jdbcType="VARCHAR" />
     <result column="type" property="type" jdbcType="VARCHAR" />
    <result column="area" property="area" jdbcType="VARCHAR"/>
    <result column="client_ip" property="clientIp" jdbcType="VARCHAR"/>
</resultMap>

<!-- 添加记录 -->
<insert id="insert" parameterClass="personalEditLog" >
	INSERT INTO 
		personal_edit_log ( 
              member_id,
              member_name,
              edit_item,
              value_pre,
              value_cur,
              time_add,
              user_add,
              type,
              client_ip)
    VALUES ( 
              #memberId:INTEGER#,
              #memberName:VARCHAR#,
              #editItem:VARCHAR#,
              #valuePre:VARCHAR#,
              #valueCur:VARCHAR#,
              NOW(),
              #userAdd:VARCHAR#,
              #type:VARCHAR# ,
              #clientIp:VARCHAR#
              )
</insert>
    <!--操作查询记录-->
    <select id="queryForMap" resultClass="personalEditLog">
        select
        a.edit_id as editId
        ,a.member_id  as memberId
        ,a.member_name as memberName
        ,a.edit_item  as editItem
        ,a.time_add as timeAdd
        ,a.client_ip as clientIp
        ,a.type
        ,(p.province_name+c.city_name) as area
        from (personal_edit_log a,personal_member z)
        left join config_city c on a.area = c.city_key
        left join config_province p on c.province_key = p.province_key
        WHERE a.member_id=z.member_id
        <isNotEmpty property="memberName" prepend="and" >
            z.member_name=#memberName:VARCHAR#
        </isNotEmpty>
        <isNotEmpty property="begintime" prepend="and">
            a.time_add &gt;= #begintime:DATE#
        </isNotEmpty>
        <isNotEmpty property="endtime" prepend="and">
            a.time_add &lt;=#endtime:DATE#
        </isNotEmpty>
        <isNotEmpty property="type" prepend="and" >
            a.type=#type:INTEGER#
        </isNotEmpty>
        order by edit_id desc
        limit 10;
    </select>

</sqlMap>