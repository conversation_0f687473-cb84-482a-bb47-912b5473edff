package com.shunwang.basepassport.user.response;

import com.google.gson.annotations.Expose;
import com.shunwang.baseStone.core.response.BaseStoneResponse;
import com.shunwang.basepassport.user.pojo.IdcardBind;
import com.shunwang.xmlbean.annotation.XmlInit;

import java.util.ArrayList;
import java.util.List;

public class IdcardBindQueryResponse extends BaseStoneResponse {

    private IdcardBindInfo idcardBindInfo;

    public IdcardBindQueryResponse(IdcardBind idcardBind){
        List<IdcardBindInfo> results = new ArrayList<IdcardBindInfo>();
        idcardBindInfo = new IdcardBindInfo();
        idcardBindInfo.setMemberId(idcardBind.getMemberId());
        idcardBindInfo.setIdcard(idcardBind.getIdcard());

        results.add(idcardBindInfo);

        super.setItems(results);
	}


    public class IdcardBindInfo {
        @Expose
        private Integer memberId;
        @Expose
        private String idcard;

        @XmlInit
        public Integer getMemberId() {
            return memberId;
        }

        public void setMemberId(Integer memberId) {
            this.memberId = memberId;
        }

        @XmlInit
        public String getIdcard() {
            return idcard;
        }

        public void setIdcard(String idcard) {
            this.idcard = idcard;
        }

    }

    public IdcardBindInfo getIdcardBindInfo() {
        return idcardBindInfo;
    }

    public void setIdcardBindInfo(IdcardBindInfo idcardBind) {
        this.idcardBindInfo = idcardBind;
    }
}
