package com.shunwang.basepassport.user.response;

import com.google.gson.annotations.Expose;
import com.shunwang.baseStone.core.response.BaseStoneResponse;
import com.shunwang.baseStone.encrypt.Encrypt;
import com.shunwang.baseStone.encrypt.pojo.EncryptItem;
import com.shunwang.basepassport.actu.pojo.PersonalGameActuVerifyRecord;
import com.shunwang.basepassport.user.pojo.ShunLingInfoConfig;
import com.shunwang.xmlbean.annotation.XmlInit;

import java.util.Calendar;
import java.util.Date;

public class GameActuQueryRespone extends BaseStoneResponse {

	private GameActuQueryResult gameActuQueryResult;

	public GameActuQueryRespone(PersonalGameActuVerifyRecord gameActuVerifyRecord) {
		this.gameActuQueryResult = new GameActuQueryResult();
		this.gameActuQueryResult.memberId = gameActuVerifyRecord.getMemberId();
		this.gameActuQueryResult.memberName = gameActuVerifyRecord.getMemberName();
		this.gameActuQueryResult.idCardNo = gameActuVerifyRecord.getIdCardNo();
		this.gameActuQueryResult.realName = gameActuVerifyRecord.getRealName();
		this.gameActuQueryResult.pi = gameActuVerifyRecord.getPi();
		this.gameActuQueryResult.state = gameActuVerifyRecord.getState();
		this.gameActuQueryResult.remark = gameActuVerifyRecord.getRemark();

		this.gameActuQueryResult.birthday = calBirthday(gameActuVerifyRecord.getIdCardNo());
		this.gameActuQueryResult.adult = calAdult(gameActuVerifyRecord.getIdCardNo());
		this.gameActuQueryResult.nonage = nonage(gameActuVerifyRecord.getIdCardNo());
	}
	
	public String buildSign(){
		Encrypt  encrypt=new Encrypt();
		encrypt.addItem(new EncryptItem(String.valueOf(this.gameActuQueryResult.memberId)));
		encrypt.addItem(new EncryptItem(this.gameActuQueryResult.memberName));
		encrypt.addItem(new EncryptItem(String.valueOf(this.gameActuQueryResult.state)));
		encrypt.addItem(new EncryptItem(this.gameActuQueryResult.remark));
		return encrypt.buildSign();
	}
	
	public class GameActuQueryResult {
		@Expose
		private Integer memberId;
		@Expose
		private String memberName;
		@Expose
		private Integer state;
		@Expose
		private String idCardNo;
		@Expose
		private String realName;
		@Expose
		private String pi;
		@Expose
		private String remark;
		@Expose
		private String birthday;
		@Expose
		private String adult;
		@Expose
		private Boolean nonage;

		@XmlInit
		public Integer getMemberId() {
			return memberId;
		}

		public void setMemberId(Integer memberId) {
			this.memberId = memberId;
		}

		@XmlInit
		public String getMemberName() {
			return memberName;
		}

		public void setMemberName(String memberName) {
			this.memberName = memberName;
		}

		@XmlInit
		public Integer getState() {
			return state;
		}

		public void setState(Integer state) {
			this.state = state;
		}

		@XmlInit
		public String getIdCardNo() {
			return idCardNo;
		}

		public void setIdCardNo(String idCardNo) {
			this.idCardNo = idCardNo;
		}

		@XmlInit
		public String getRealName() {
			return realName;
		}

		public void setRealName(String realName) {
			this.realName = realName;
		}

		@XmlInit
		public String getPi() {
			return pi;
		}

		public void setPi(String pi) {
			this.pi = pi;
		}

		@XmlInit
		public String getRemark() {
			return remark;
		}

		public void setRemark(String remark) {
			this.remark = remark;
		}

		@XmlInit
		public String getBirthday() {
			return birthday;
		}

		public void setBirthday(String birthday) {
			this.birthday = birthday;
		}

		@XmlInit
		public String getAdult() {
			return adult;
		}

		public void setAdult(String adult) {
			this.adult = adult;
		}

		@XmlInit
		public Boolean getNonage() {
			return nonage;
		}

		public void setNonage(Boolean nonage) {
			this.nonage = nonage;
		}
	}

	@XmlInit(path="items/item")
	public GameActuQueryResult getGameActuQueryResult() {
		return gameActuQueryResult;
	}

	public void setGameActuQueryResult(GameActuQueryResult gameActuQueryResult) {
		this.gameActuQueryResult = gameActuQueryResult;
	}

	public String calBirthday(String idCardNo) {
		return idCardNo.substring(6, 14);
	}

	public String calAdult(String idCardNo) {
		int year = Integer.valueOf(idCardNo.substring(6, 10));
		String adultYear = String.valueOf(year + 18);
		String month = idCardNo.substring(10, 12);
		String day = idCardNo.substring(12, 14);

		return adultYear + month + day;
	}

	public Boolean nonage(String idCardNo) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(new Date());

		Integer age = 0;

		int yearNow = calendar.get(Calendar.YEAR);
		int monthNow = calendar.get(Calendar.MONTH) + 1;
		int dayNow = calendar.get(Calendar.DATE);

		int year = Integer.valueOf(idCardNo.substring(6, 10));
		int month = Integer.valueOf(idCardNo.substring(10, 12));
		int day = Integer.valueOf(idCardNo.substring(12, 14));

		if ((month < monthNow) || (month == monthNow && day <= dayNow)) {
			age = yearNow - year;
		} else {
			age = yearNow - year - 1;
		}

		return (age >= 18) ? Boolean.FALSE:Boolean.TRUE;
	}

}
