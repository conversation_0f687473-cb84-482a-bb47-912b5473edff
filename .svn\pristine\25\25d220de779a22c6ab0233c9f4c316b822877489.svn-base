package com.shunwang.passport.find.web;


import com.shunwang.baseStone.bussiness.pojo.Bussiness;
import com.shunwang.baseStone.checkCode.context.CheckCodeContext;
import com.shunwang.baseStone.context.BaseStoneContext;
import com.shunwang.baseStone.context.SessionContext;
import com.shunwang.baseStone.core.exception.BaseStoneException;
import com.shunwang.baseStone.key.SWKey;
import com.shunwang.baseStone.key.SWKeyContext;
import com.shunwang.baseStone.key.exception.KeyNotExistExp;
import com.shunwang.basepassport.actu.constant.ActuConstant;
import com.shunwang.basepassport.actu.dao.CafeActuDao;
import com.shunwang.basepassport.actu.dao.PersonalActuDao;
import com.shunwang.basepassport.actu.pojo.CafeActuInfo;
import com.shunwang.basepassport.binder.common.BinderConstants;
import com.shunwang.basepassport.binder.dao.BinderDao;
import com.shunwang.basepassport.binder.dao.EmailBinderDao;
import com.shunwang.basepassport.binder.dao.MobileBinderDao;
import com.shunwang.basepassport.binder.pojo.QuestionBinder;
import com.shunwang.basepassport.binder.pojo.SendBinder;
import com.shunwang.basepassport.find.common.AppealConstants;
import com.shunwang.basepassport.find.common.AppealFindUtil;
import com.shunwang.basepassport.find.dao.AppealDao;
import com.shunwang.basepassport.find.pojo.Appeal;
import com.shunwang.basepassport.question.dao.MemberQuestionDao;
import com.shunwang.basepassport.question.pojo.MemberQuestion;
import com.shunwang.basepassport.user.dao.MemberDao;
import com.shunwang.basepassport.user.pojo.Member;
import com.shunwang.basepassport.user.pojo.ProtectedQuestion;
import com.shunwang.framework.struts2.action.BaseAction;
import com.shunwang.passport.common.context.DomainContext;
import com.shunwang.util.lang.StringUtil;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;



/**
 * 
 * ****************************
 * 版权所有：顺网科技 保留所有权利
 * 创建日期: 2011-8-19  上午11:11:25
 * 创建作者：陈积慧
 * 文件名称：AppealAction.java
 * 版本： 1.0
 * 功能：
 * 最后修改时间：
 * 修改记录：
 ***************************************
 */
public class AppealAction extends BaseAction {

	protected final Logger log = LoggerFactory.getLogger(getClass());

	/**
	 * 
	 */
	private static final long serialVersionUID = -1823374405321499406L;

	
	@SuppressWarnings("unchecked")
	private BinderDao binderDao;
	private SendBinder  sendBinder;

	protected String checkCode;
    protected String memberName;
	protected MemberQuestion memberQuestion;
	//问题，答案
	private String answer1;
	private String answer2;
	private String answer3;
	private String answerShow1;
	private String answerShow2;
	private String answerShow3;
	private int question1;
	private int question2;
	private int question3;

	protected String appealResult ;
	protected String reAppeal;
	protected Member member;
	protected String findWay;

	protected boolean showGt;

	public String getReAppeal() {
		return reAppeal;
	}

	public void setReAppeal(String reAppeal) {
		this.reAppeal = reAppeal;
	}

	public String getAppealResult() {
		return appealResult;
	}

	public void setAppealResult(String appealResult) {
		this.appealResult = appealResult;
	}


	/**
	 * ***********
	  * 创建日期: 2011-8-22  下午01:47:27
	  * 创建作者：chenjh
	  * @return
	  * 功能：提交申诉信息  子类重写
	  *************
	 */
	public String commitAppealMsg(){
		return null;
	}


	/**
	 * 邮箱申诉 ,手机申诉
	 *  人工申诉提交审核待处理时,不可重复提交人工申诉;
 			人工申请审核拒绝后,可再次发起人工申诉;
	 * @param appealTemp
	 * @return
     */
	protected String goAppealResult(Appeal appealTemp){
		//如果有申诉,而且正在审核中,则显示申诉审批结果
		if (appealTemp != null ){

			if(reAppeal!= null && "1".equals(reAppeal) &&( AppealConstants.CHECKSTATE_REFUSE.equals( appealTemp.getAppealState()) ||  AppealConstants.CHECKSTATE_RATIFY.equals( appealTemp.getAppealState()))){
				return SUCCESS;
			}


			if(AppealConstants.CHECKSTATE_WATITING.equals( appealTemp.getAppealState()) ) {
				appealResult = "您的账号" + appealTemp.getMemberName() + "的申诉已经受理，我们将在1-3个工作日内进行审核，请留意您的邮箱或手机提醒！";
				return "appealResult";
			}else if (AppealConstants.CHECKSTATE_RATIFY.equals( appealTemp .getAppealState())) {
				appealResult = "您的通行证账号" + memberName
						+ "的申诉已经通过！请及时登录 个人中心 进行资料修正。"
						+ " <a class='a035' href='"+ DomainContext.getAppServer()+"/front/member/securityCenter_index.htm'>立即前往</a> >>" ;

				appealResult += "<div>"+getReAppealUrl(appealTemp)+"</div";
				return "appealResult";
			}  else if(AppealConstants.CHECKSTATE_REFUSE.equals( appealTemp.getAppealState()) ){
				String s = "";
				if(StringUtils.isNotBlank(appealTemp.getContent()) )
					s = "拒绝理由："+ appealTemp.getContent();
				appealResult = "您的通行证账号" + appealTemp.getMemberName() + "的申诉未通过，"+s+"请您仔细回忆以上资料后,"+getReAppealUrl(appealTemp);
				reAppeal = "1";
				return "appealResult";
			}
		}
		return SUCCESS;

	}

	private String getReAppealUrl(Appeal appealTemp){
		String a ="<a href='"+ DomainContext.getAppServer();
		if( AppealConstants.CHANGE_MOBILE_TYPE_APPEAL.equals(appealTemp.getAppealType())
				|| AppealConstants.CHANGE_MOBILE_TYPE_ACTU.equals(appealTemp.getAppealType())){
			a +="/front/noLogin/goAppealChangeMobile_front.htm";
		} else if(AppealConstants.CHANGE_EMAIL_TYPE_APPEAL.equals(appealTemp.getAppealType())
				||AppealConstants.CHANGE_EMAIL_TYPE_ACTU.equals(appealTemp.getAppealType())){
			a +="/front/noLogin/goAppealChangeEmail_front.htm";
		} else if(AppealConstants.FIND_TYPE_THREE.equals(appealTemp.getAppealType())
				||AppealConstants.FIND_TYPE_FIFE.equals(appealTemp.getAppealType())){
			a +="/front/noLogin/findProblem_appeal.htm";
		}else if(AppealConstants.FIND_TYPE_ONE.equals(appealTemp.getAppealType())
				|| AppealConstants.FIND_TYPE_FOUR.equals(appealTemp.getAppealType())){
			a +="/front/noLogin/pwdFind_appeal_front.htm";
		}

		a+="?findWay="+findWay+"&reAppeal=1&memberName="+appealTemp.getMemberName()+"'>重新申诉</a>";
		return a;
	}
	
	/**
	 * ***********
	  * 创建日期: 2011-9-8  下午05:01:46
	  * 创建作者：chenjh
	  * @param str
	  * @return
	  * 功能：获取用户信息
	  *************
	 */
	public Member getMemberInfo(String str){
		Member member=null;
		try {
			member=getMemberDao().getMember(str);
			return member;
		} catch (Exception e) {			
			log.error("获取用户" + str + "数据失败",e);
		}
		return member;
	}
	
	 /**
     * <AUTHOR>
     * @作用 读取用户的绑定状态 和实名认证状态 
     */
	   @SuppressWarnings("unchecked")
	public void getUserState(String userName) {
		Map<Integer, Boolean> map = null;
		Member member = null;
		if (null != userName && !"".equals(userName)) {
			try {
				member = getMemberDao().getMember(userName);
				map = AppealFindUtil.findMemberStateByMemberId(member
						.getMemberId());
			} catch (Exception e) {
				log.error("获取用户" + userName + "状态失败", e);
			}
			this.getRequest().setAttribute("memberType", member.getMemberType());
			this.getRequest().setAttribute("questionIsBind",
					map.get(AppealConstants.MEMBER_STATE_PROBLEM));
			this.getRequest().setAttribute("phoneIsBind",
					map.get(AppealConstants.MEMBER_STATE_PHONE));
			this.getRequest().setAttribute("mailIsBind",
					map.get(AppealConstants.MEMBER_STATE_MAIL));  //计算状态

			CafeActuInfo cafeActuInfo = new CafeActuInfo();
			CafeActuDao cafeActuDao =  cafeActuInfo.getDao(member);
			cafeActuInfo = cafeActuDao.getByMemberId(member.getMemberId());

			//网吧实名认证否
			if(cafeActuInfo!= null && cafeActuInfo.getCafeActuInfoWrapper()!= null
					&&  ActuConstant.INFO_STATE_PASS.equals(cafeActuInfo.getCafeActuInfoWrapper().getInfoState())){
				this.getRequest().setAttribute("actuIsTrue", true); //用于标识是否需要实名认证 按钮是否出现
				return ;
			}
			if (AppealConstants.USER_TYPE.equals(member.getMemberType())&&member.getPersonCertState().equals(Integer.parseInt(ActuConstant.INFO_STATE_PASS))) {
				this.getRequest().setAttribute("actuIsTrue", true); //用于标识是否需要实名认证 按钮是否出现
			} else
				this.getRequest().setAttribute("actuIsTrue", false);
		}
	}

	/**
	 * ***********
	  * 创建日期: 2011-8-22  下午01:58:35
	  * 创建作者：chenjh
	  * @return
	  * 功能：初始化注册来源信息
	  *************
	 */
	public void initRegMsg(){
		Bussiness bussiness=new Bussiness();		//下拉列表
		List<Bussiness> regList=bussiness.initAppealMsg();
		this.getRequest().setAttribute("regList", regList);	
	}

    /**
     * 检查验证码
     *
     * @return boolean 验证码是否正确
     * <AUTHOR> 创建于2013-7-5 下午02:28:13
     * @throws
     */
    protected boolean validateCheckCode(){


        try {
			String sessionCode = (String) SessionContext.get(CheckCodeContext.getKey(""));
			if(sessionCode!=null)
            	CheckCodeContext.validate(checkCode);
        } catch (BaseStoneException e) {
            this.addFieldError("checkCode","验证码错误！");
            return false;
        }
        return true;
    }

	/**
	 * 申诉中心校验用户名和验证码
	 * @return
	 */
	protected String validateMemberNameAndCode(){
		try {
			if (StringUtil.isNotBlank(memberName) ) {
				member = getMemberDao().getMember(memberName);
				if (member == null) {
					this.addFieldError("memberName", "您的账号不存在！");
					return INPUT;
				}
			} else {
				this.addFieldError("memberName","账号不能为空,请重新输入");
				return INPUT;
			}
		} catch (Exception e) {
			this.addFieldError("memberName","您的账号不存在！");
			return INPUT;
		}

//		if(!validateCheckCode() && !"1".equals(reAppeal))//reAppeal=1,从申诉结果链接,"重新申诉"进入,跳过验证码.
//			return INPUT;

		if(!validateCheckCode()){//reAppeal=1,从申诉结果链接,"重新申诉"进入,跳过验证码.
			return INPUT; //无验证成功标记则需要重新输入通行证账号,验证码
		}

		try{
			SWKeyContext.get(new String[]{"appealValidCodeSuccess",memberName});
		}catch(KeyNotExistExp exp){
			//验证成功在session中标记
			SWKeyContext.put(new SWKey("appealValidCodeSuccess"));
			SWKeyContext.put(new SWKey(memberName));
		}

		return null;
	}
	/**
	 * ***********
	  * 创建日期: 2011-8-19  下午03:56:04
	  * 创建作者：chenjh
	  * @return
	  * 功能：父类DAO 所有子类重写
	  *************
	 */
	public  AppealDao getDao(){
		return null;
	}
	
	
	/**
	 * ***********
	  * 创建日期: 2011-9-8  下午04:20:45
	  * 创建作者：chenjh
	  * 功能：校验验证码
	  *************
	 */
	public void checkCode(){
		if(null!=checkCode&&!"".equals(checkCode))
		CheckCodeContext.validate(checkCode);
	}

	/**
	 * ***********
	  * 创建日期: 2011-8-30  下午01:33:41
	  * 创建作者：chenjh
	  * @return
	  * 功能：密保问题校验
	 * @throws Exception 
	  *************
	 */
	public void checkData() throws Exception  {
		if(StringUtil.isBlank(question1+"")){			
			throw new Exception("问题一不能为空！");			
		}
		if(StringUtil.isBlank(question2+"")){
			throw new Exception("问题二不能为空！");			
		}
		if(StringUtil.isBlank(question3+"")){
			throw new Exception("问题三不能为空！");			
		}
		if(StringUtil.isBlank(answer1)){
			throw new Exception("答案一不能为空！");			
		}
		if(StringUtil.isBlank(answer2)){
			throw new Exception("答案二不能为空！");			
		}
		if(StringUtil.isBlank(answer3)){
			throw new Exception("答案三不能为空！");			
		}
		if(answer1.length()>64){
			throw new Exception("答案一长度超过64字符！");		
		}
		if(answer2.length()>64){
			throw new Exception("答案二长度超过64字符！");			
		}
		if(answer3.length()>64){
			throw new Exception("答案三长度超过64字符！");			
		}
		if((question1==question2)||(question2==question3)||(question1==question3)){
			throw new Exception("不能提交重复的问题！");
		}		

	}

	protected void validateProtectedQuestions(String memberName) {
		QuestionBinder questionBinder = new QuestionBinder();
		questionBinder.setBusinessType(BinderConstants.FINDPWD);
		questionBinder.setMember(getMemberDao().getByName(memberName));
		questionBinder.getBinderContent();

		List<ProtectedQuestion> questions = new ArrayList<ProtectedQuestion>();

		ProtectedQuestion q1 = new ProtectedQuestion();
		q1.setQuestionKey(memberQuestion.getQuestion1());
		q1.setAnswer(memberQuestion.getAnswer1());
		questions.add(q1);

		ProtectedQuestion q2 = new ProtectedQuestion();
		q2.setQuestionKey(memberQuestion.getQuestion2());
		q2.setAnswer(memberQuestion.getAnswer2());
		questions.add(q2);

		ProtectedQuestion q3 = new ProtectedQuestion();
		q3.setQuestionKey(memberQuestion.getQuestion3());
		q3.setAnswer(memberQuestion.getAnswer3());
		questions.add(q3);

		questionBinder.validate(questions.toArray(new ProtectedQuestion[questions.size()]));
	}

	public String showSuccResult() {
		return SUCCESS;
	}

	/**
	 * ***********
	  * 创建日期: 2011-8-23  上午11:26:46
	  * 创建作者：chenjh
	  * @return
	  * 功能：获取memberDao
	  *************
	 */
	public MemberDao getMemberDao(){		
		return (MemberDao)BaseStoneContext.getInstance().getBean("memberDao");
	}

	public PersonalActuDao getPersonalActuDao(){
		return (PersonalActuDao)BaseStoneContext.getInstance().getBean("personalActuDao");
	}
	
	public MemberQuestionDao getMemberQueDao(){
		return (MemberQuestionDao)BaseStoneContext.getInstance().getBean("memberQuestionDao");
	}
	public EmailBinderDao getEmailBinderDao(){
		return (EmailBinderDao)BaseStoneContext.getInstance().getBean("emailBinderDao");
	}
	
	public MobileBinderDao getMobileBinderDao(){
		return (MobileBinderDao)BaseStoneContext.getInstance().getBean("mobileBinderDao");
	}
	@SuppressWarnings("unchecked")
	public BinderDao getBinderDao() {
		return binderDao;
	}
	@SuppressWarnings("unchecked")
	public void setBinderDao(BinderDao binderDao) {
		this.binderDao = binderDao;
	}
	public SendBinder getSendBinder() {
		return sendBinder;
	}
	public void setSendBinder(SendBinder sendBinder) {
		this.sendBinder = sendBinder;
	}
	public String getAnswer1() {
		return  answer1;//Md5Encrypt.encrypt(answer1, "UTF-8");
	}
	public void setAnswer1(String answer1) {
		this.answer1 = answer1;
		setAnswerShow1(answer1);
	}
	public String getAnswer2() {
		return answer2;//Md5Encrypt.encrypt(answer2, "UTF-8");
	}
	public void setAnswer2(String answer2) {
		this.answer2 = answer2;
		setAnswerShow2(answer2);
	}
	public String getAnswer3() {
		return answer3;//Md5Encrypt.encrypt(answer3, "UTF-8");
	}
	public void setAnswer3(String answer3) {
		this.answer3 = answer3;
		setAnswerShow3(answer3);
	}
	public int getQuestion1() {
		return question1;
	}
	public void setQuestion1(int question1) {
		this.question1 = question1;
	}
	public int getQuestion2() {
		return question2;
	}
	public void setQuestion2(int question2) {
		this.question2 = question2;
	}
	public int getQuestion3() {
		return question3;
	}
	public void setQuestion3(int question3) {
		this.question3 = question3;
	}


	public String getCheckCode() {
		return checkCode;
	}


	public void setCheckCode(String checkCode) {
		this.checkCode = checkCode;
	}


	public String getAnswerShow1() {
		return answerShow1;
	}


	public void setAnswerShow1(String answerShow1) {
		this.answerShow1 = answerShow1;
	}


	public String getAnswerShow2() {
		return answerShow2;
	}


	public void setAnswerShow2(String answerShow2) {
		this.answerShow2 = answerShow2;
	}


	public String getAnswerShow3() {
		return answerShow3;
	}


	public void setAnswerShow3(String answerShow3) {
		this.answerShow3 = answerShow3;
	}


    public void setMemberName(String memberName) {
        this.memberName=memberName;
    }
    public String getMemberName() {
        return this.memberName;
    }

	public MemberQuestion getMemberQuestion() {
		return memberQuestion;
	}

	public void setMemberQuestion(MemberQuestion memberQuestion) {
		this.memberQuestion = memberQuestion;
	}

	public Member getMember() {
		return member;
	}

	public void setMember(Member member) {
		this.member = member;
	}

	public String getFindWay() {
		return findWay;
	}

	public void setFindWay(String findWay) {
		this.findWay = findWay;
	}

	public boolean isShowGt() {
		return showGt;
	}

	public void setShowGt(boolean showGt) {
		this.showGt = showGt;
	}
}
