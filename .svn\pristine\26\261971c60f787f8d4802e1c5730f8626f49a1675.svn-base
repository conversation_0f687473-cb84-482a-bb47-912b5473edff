package com.shunwang.basepassport.user.exception;

import com.shunwang.baseStone.core.exception.BaseStoneException;

public class RegisterErrorExp extends BaseStoneException {

	private static final long serialVersionUID = -1346308657938614419L;

	public RegisterErrorExp() {
		this.setMsgId("");
		this.setMsg("");
	}
	
	public RegisterErrorExp(String msgId, String msg) {
		this.setMsgId(msgId);
		this.setMsg(msg);
	}
}
