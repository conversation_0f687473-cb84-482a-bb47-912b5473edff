package com.shunwang.basepassport.binder.web;

import com.shunwang.basepassport.binder.common.BinderConstants;
import com.shunwang.basepassport.binder.dao.BinderDao;
import com.shunwang.basepassport.binder.exception.NoBindExp;
import com.shunwang.basepassport.binder.pojo.Binder;
import com.shunwang.basepassport.binder.pojo.SendBinder;
import com.shunwang.basepassport.commonExp.ParamNotFoundExp;
import com.shunwang.basepassport.user.web.MemberAction;
import com.shunwang.util.lang.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;

public abstract class BindAction extends MemberAction{

	/**
	 * 
	 *
	 *
	 */
	private static final long serialVersionUID = 736351641994672501L;
	private final static Logger log = LoggerFactory.getLogger(BindAction.class);
	
	@Override
	public void processTransaction() throws Exception {
		// TODO Auto-generated method stub
		super.process();
	}
	
	@Override
	public void checkParam() {
		// TODO Auto-generated method stub
		if(StringUtil.isBlank(this.getSign())||StringUtil.isBlank(this.getNumber())||StringUtil.isBlank(this.getActiveNo())||StringUtil.isBlank(this.getTime()))
			throw new ParamNotFoundExp();
	}

	/**
	 * 
	 * 号码的格式检查
	 * @return
	 * <AUTHOR> 创建于 Jul 25, 2011 4:29:43 PM
	 * @throws
	 */
	protected abstract void checkFormat();

	/**
	 * 
	 * 返回操作类型 1为邮箱 2为手机
	 * @return
	 * <AUTHOR> 创建于 Jul 25, 2011 4:39:13 PM
	 * @throws
	 */
	protected abstract Integer getDoType();
	/**
	 * 
	 * 返回操作类型 邮箱 或手机
	 * @return
	 * <AUTHOR> 创建于 Jul 25, 2011 4:39:13 PM
	 * @throws
	 */
	protected abstract String getType();
	/**
	 * 
	 * 返回接口传来的号码
	 * @return
	 * <AUTHOR> 创建于 Jul 25, 2011 4:28:25 PM
	 * @throws
	 */
	protected abstract String getNumber();
	/**
	 * 
	 * 返回接口传来的验证码
	 * @return
	 * <AUTHOR> 创建于 Jul 26, 2011 10:16:18 AM
	 * @throws
	 */
	protected abstract String getActiveNo();
	
	/**
	 * 构建binder
	 * 
	 * @return
	 * <AUTHOR> 创建于 2011-8-23 下午01:29:44
	 * @throws
	 */
	protected SendBinder buildSendBinder(){
		SendBinder sendBinder = (SendBinder) getBinderDao().getById(getMemberInfo().getMemberId());
		if(null == sendBinder)
			throw new NoBindExp(this.getType());
		sendBinder.setMember(getMemberInfo());
		sendBinder.setMemberName(getMemberName());
		if(!sendBinder.isBinded()){
			sendBinder.setBusinessType(BinderConstants.BINDNUMBER);
			sendBinder.beginBuildLog();
		}
		else if(!sendBinder.getNumber().equals(this.getNumber())){
			sendBinder.setBusinessType(BinderConstants.CHANGENUMBER);
			sendBinder.beginBuildLog();
		}
		return sendBinder;
	}
	/**
	 * 获取Dao
	 * 
	 * @return
	 * <AUTHOR> 创建于 2011-8-22 下午08:36:55
	 * @throws
	 */
	public abstract BinderDao<Binder> getBinderDao();
	
	/**
	 * utf-8解码
	 * @param string
	 * @return
	 */
	protected String decodeByUTF(String string){
		try {
			return  URLDecoder.decode(new String(StringUtil.trimNull(string).getBytes("ISO-8859-1")), "utf-8");
		} catch (UnsupportedEncodingException e) {
			// TODO Auto-generated catch block
		}
		return "";
	}

}
