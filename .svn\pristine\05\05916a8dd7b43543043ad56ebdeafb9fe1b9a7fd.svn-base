<%@ page contentType="text/html;charset=UTF-8"%>
<%@ include file="/common/taglibs.jsp"%>
<style>
    .form_group tbody th, .form_group tbody td {
        vertical-align: middle;
    }
</style>
<script type="text/javascript" src="${staticServer }/scripts/common/md5.js"></script>

					<c:forEach items="${questionBinder.protectedQuestions}" var="question" varStatus="status">
                        <tr>
                            <th id="questionName${status.index}">问题：</th>
                            <td>
                                <strong class="form_primary">${question.question}</strong>
                                <input type="hidden" name="questionKeys" value="${question.questionKey}" id="question${status.index}"/>
                            </td>
                        </tr>
                        <tr>
                            <th>答&nbsp;&nbsp;案：</th>
                            <td>
                                <input name="answerShow" type="text" class="form_input" style="width:220px;" id="answerShow${status.index}"  tabindex="1" maxlength="64" value="${answerShow[status.index]}"/>
                                <input name="answers" type="hidden" id="answer${status.index}"/>
                                <em class="form_tip" id="errorSpan${status.index}">64个字符以内</em>
                            </td>
                        </tr>
					 </c:forEach>
					<input type="hidden" name="passKeyString" value="${passKeyString}"/>


<script type="text/javascript" src="${staticServer }/scripts/front/member/questionCommon.js">
$(document).ready(function(){
	initParam();
});
<script type="text/javascript">
        $('.sidebar .menu li:first').addClass('current');
</script>
</script>