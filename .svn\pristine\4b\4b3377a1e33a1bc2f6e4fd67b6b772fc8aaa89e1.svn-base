<%@ page contentType="text/html; charset=UTF-8" %>
<%@ include file="../common/taglibs.jsp" %>
<%@ page import="com.shunwang.basepassport.user.common.UserCheckUtil" %>
<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <meta name="format-detection" content="telephone=no" />
    <title></title>
    <!-- TODO: ******** 网易本机登录 -->
    <meta content="always" name="referrer"/>
    <meta name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=3.0, minimum-scale=1.0, user-scalable=no"
    />
    <link href="${staticServer}/${cdnVersion}/style/public-gray.css" rel="stylesheet" type="text/css" />



    <!-- TODO: ******** 手机号验证码分步登录css -->
      <c:choose>
          <c:when test="${not empty html5Css && not empty html5Css.cssUrl }">
              <link href="${html5Css.cssUrl}" rel="stylesheet" type="text/css"/>
          </c:when>
          <c:otherwise>
              <link href="${staticServer}/${cdnVersion}/h5OneLogin/style/verify.css" rel="stylesheet" type="text/css" />
                  <link href="${staticServer}/${cdnVersion}/style/defaultForHtml5.css" rel="stylesheet" type="text/css"/>
          </c:otherwise>
      </c:choose>


    <!-- TODO: ******** 网易本机登录css -->
    <link href="${staticServer}/${cdnVersion}/h5OneLogin/style/NEOneLogin.css" rel="stylesheet" type="text/css" />
    <%
      String validPhone = UserCheckUtil.getValidPhoneNum();
    %>
  </head>

  <body id="dialog-checkcode" class="login-mobile">
    <header class="header-bar header-bar-sublevel">
      <h1 class="title"><b class="ico-logo"></b>顺网通行证</h1>
      <button type="button" class="back"></button>
    </header>

    <div role="main" class="main">
      <div class="tips-error" id="tips-error" style="display: none">
        <p></p>
      </div>
<%--      <div class="tab">--%>
<%--        <ul class="tabs noslide">--%>
<%--          <li><a class="react">通行证账号登录</a></li>--%>
<%--          <li class="active"><a class="react">手机快速登录</a></li>--%>
<%--        </ul>--%>
<%--        <div class="slide" style="left: 50%"></div>--%>
<%--      </div>--%>
      <form id="login-form" action="" method="get">
        <s:hidden name="callbackUrl"/>
        <s:hidden name="site_id" id="site_id"/>
        <s:hidden name="cssSiteId"/>
        <s:hidden name="css"/>
        <s:hidden name="version"/>
        <s:hidden name="env"/>
        <s:hidden name="needCode" />
        <s:hidden name="extData"/>
        <s:hidden name="oneLoginStyleJsUrl" id="oneLoginStyleJsUrl"/>
        <s:hidden name="smsCheckCode" id="smsCheckCode"/>
        <s:hidden name="checkCode" id="checkCode"/>
        <s:hidden name="md5" id="md5"/>
        <s:hidden name="loginSiteId" id="loginSiteId"/>
        <s:hidden name="token" id="token"/>
        <s:hidden name="loginType" id="loginType" value="quickLogin"/>
        <s:hidden name="loginPage" value="inline"/>
        <s:hidden name="unionid" />
        <s:hidden name="openId" />
        <s:hidden name="sign" />

        <s:hidden name="number" id="mobile"/>
        <s:hidden name="mobileActiveNo" id="mobileActiveNo"/>
        <s:hidden name="token_site_id" id="tokenSiteId"/>
        <s:hidden name="mobileValifyTokenId"/>

        <s:hidden name="tgt" id="oauthTgt"/>
        <s:hidden name="linkTgt" id="linkTgt" />
        <s:url var="regUrlTag" escapeAmp="false" value="%{#attr.regLink}">
          <s:param name="returnUrl" value="%{callbackUrl}"/>
          <s:param name="site_id" value="%{site_id}" />
          <s:param name="regVersion" value="%{version}"/>
          <s:param name="env" value="%{env}" />
        </s:url>
      </form>


<%--      <div id="tel-login-form" style="display: block" class="login-form form-box" >--%>
<%--        <div class="form-group placeholder inline-group">--%>
<%--          <input id="number" type="tel" placeholder="请输入手机号码" value="" class="form-control" />--%>
<%--          <button type="button" id="sent-code" class="btn btn-primary btn-mini">--%>
<%--            发送验证码--%>
<%--          </button>--%>
<%--        </div>--%>
<%--        <div class="form-group placeholder">--%>
<%--          <input type="tel" id="mobileCheckCode" placeholder="请输入手机短信中的验证码" class="form-control"/>--%>
<%--        </div>--%>

<%--        <div class="protocol-tip" style="padding: 10px 0 0 10px">--%>
<%--          <p>--%>
<%--            <input type="checkbox" class="form_check" tabindex="7" id="agreementCheckbox" />--%>
<%--            <span class="agreement_chk">我已阅读并接受</span>--%>
<%--            <a href="//i.kedou.com/agreement/register" target="_blank" class="a035">《用户协议》</a>--%>
<%--            及<a href="//i.kedou.com/agreement/privacy" target="_blank" class="a035" >《隐私政策》</a>--%>
<%--          </p>--%>
<%--        </div>--%>
<%--        <div class="other-group btn-box">--%>
<%--          <button type="submit" id="tel-login" class="btn btn-primary">--%>
<%--            登录--%>
<%--          </button>--%>
<%--        </div>--%>
<%--      </div>--%>

<%--      <div class="login-btns clearfix">--%>
<%--        <a--%>
<%--        href="https://i.kedou.com/goRegister?returnUrl=https%3A%2F%2Fi.kedou.com%2Findex%3FreturnUrl%3D&amp;site_id=identity&amp;regVersion=&amp;env=html5"--%>
<%--        class="link-reg"--%>
<%--        target="_blank"--%>
<%--        >立即注册</a--%>
<%--        >--%>

<%--        <a--%>
<%--        href="https://i.kedou.com/findPwd/step1"--%>
<%--        class="link-forget-password"--%>
<%--        target="_blank"--%>
<%--        >找回密码</a--%>
<%--        >--%>
<%--      </div>--%>

      <!-- TODO: ******** 手机号验证码分步登录html -->
      <div class="login-phone-step" id="loginPhoneStep" style="display: none;">
        <!-- 手机号 -->
        <div class="login-verify-phone verify-box">
          <div class="verify-phone-header verify-header">
<%--            <button class="back"></button>--%>
            <h1 class="title">手机号登录</h1>
          </div>
          <div class="verify-phone-content">
            <div class="logo"></div>
            <h2 class="title">手机号登录注册</h2>
            <p class="desc">未注册的用户验证后将自动注册并登录</p>
            <div class="form-group">
              <img class="icon" src="${staticServer}/${cdnVersion}/images/verify-phone.png" alt="">
              <input
                type="number"
                id="phoneNumber"
                placeholder="请输入手机号码"
                class="form-control"
              />
            </div>
            <p class="tips error-tips"></p>
          </div>
          <div class="verify-phone-bottom verify-bottom">
            <button id="verify-next">登录</button>
          </div>
          <div class="protocol-tip">
            <p>
              <input
                type="checkbox"
                class="form_check"
                tabindex="7"
                id="agreementCheckboxStep"
              />
              <span class="agreement_chk">我已阅读并接受</span>
              <a
                href="//i.kedou.com/agreement/register"
                target="_blank"
                id="agreement"
                class="a035 privacy-channel"
                >用户协议</a>
              及<a
                href="//i.kedou.com/agreement/privacy"
                target="_blank"
                id="privacy"
                class="a035 privacy-channel"
                >隐私政策</a>
            </p>
          </div>
          <div class="login-others">
            <div class="hd"><p>第三方登录</p></div>
            <div class="bd">
              <c:forEach items="${outOauthDirs}" var="oauthDir">
                <c:forEach items="${oauthDir.outOauthInterfaces}" var="useroutInterface">
                  <a class="drop drop${oauthDir.linkId}" href="###" onclick="${useroutInterface.linkUrl}">
                    <img alt="使用${useroutInterface.serviceProvider}登录"
                         src="${staticServer}/${cdnVersion}/images/outOauth/${useroutInterface.serviceBigImg}"/>
                      <span class="name">${useroutInterface.serviceProvider}</span>
                  </a>
                </c:forEach>
              </c:forEach>
            </div>
          </div>
        </div>
        <!-- 验证码 -->
        <div class="login-verify-code verify-box" id="verifyPhone" style="display: none">
          <div class="verify-code-header verify-header">
            <button class="back"></button>
          </div>
          <div class="verify-code-content">
            <h2 class="title">验证手机号</h2>
            <p class="desc">请输入发送至 +86 <span></span> 的 6 位验证码</p>
            <div class="form-group">
              <input type="number" class="code-input" maxlength="1" data-index="1">
              <input type="number" class="code-input" maxlength="1" data-index="2">
              <input type="number" class="code-input" maxlength="1" data-index="3">
              <input type="number" class="code-input" maxlength="1" data-index="4">
              <input type="number" class="code-input" maxlength="1" data-index="5">
              <input type="number" class="code-input" maxlength="1" data-index="6">
            </div>
            <p class="tips timer-tips"></p>
            <p class="tips error-tips"></p>
          </div>
          <div class="verify-code-bottom verify-bottom">
            <button class="verify-disabled" id="verify-send">重新发送</button>
          </div>
        </div>
      </div>

      <!-- TODO: ******** 网易本机登录html -->
      <div class="login-NEOne-box" id="NEOneContainer">
        <!-- <div class="login-others">
          <div class="hd"><p>其他登录方式</p></div>
          <div class="bd">
            <a
              class="drop drop1005"
              href="###"
              onclick="javascript:goToOtherSite('1005')"
            >
              <img
                alt="使用微信登录"
                src="${staticServer}/${cdnVersion}/images/weixin.png"
              />
              <span>微信登录</span>
            </a>
          </div>
        </div> -->
      </div>

<%--      <div class="login-others">--%>
<%--        <div class="hd">第三方登录</div>--%>
<%--        <div class="bd">--%>
<%--          <a--%>
<%--            class="drop drop1004"--%>
<%--            href="###"--%>
<%--            onclick="javascript:goToOtherSite('1004')"--%>
<%--          >--%>
<%--            <img--%>
<%--              alt="使用QQ登录"--%>
<%--              src="${staticServer}/${cdnVersion}/images/outOauth/qqbig.png"--%>
<%--            />--%>
<%--          </a>--%>

<%--          <a--%>
<%--            class="drop drop1007"--%>
<%--            href="###"--%>
<%--            onclick="javascript:goToOtherSite('1007')"--%>
<%--          >--%>
<%--            <img--%>
<%--              alt="使用支付宝登录"--%>
<%--              src="${staticServer}/${cdnVersion}/images/outOauth/alipayBig.png"--%>
<%--            />--%>
<%--          </a>--%>

<%--          <a--%>
<%--            class="drop drop1005"--%>
<%--            href="###"--%>
<%--            onclick="javascript:goToOtherSite('1005')"--%>
<%--          >--%>
<%--            <img--%>
<%--              alt="使用微信登录"--%>
<%--              src="${staticServer}/${cdnVersion}/images/outOauth/weixinBig.png"--%>
<%--            />--%>
<%--          </a>--%>

<%--          <a--%>
<%--            class="drop drop1006"--%>
<%--            href="###"--%>
<%--            onclick="javascript:goToOtherSite('1006')"--%>
<%--          >--%>
<%--            <img--%>
<%--              alt="使用微博登录"--%>
<%--              src="${staticServer}/${cdnVersion}/images/outOauth/weiboBig.png"--%>
<%--            />--%>
<%--          </a>--%>

<%--          <a--%>
<%--            class="drop drop1008"--%>
<%--            href="###"--%>
<%--            onclick="javascript:goToOtherSite('1008')"--%>
<%--          >--%>
<%--            <img--%>
<%--              alt="使用微信公众号登录"--%>
<%--              src="${staticServer}/${cdnVersion}/images/outOauth/weixinBig.png"--%>
<%--            />--%>
<%--          </a>--%>
<%--        </div>--%>
<%--      </div>--%>

      <!-- TODO: ******** 手机号分步登录modal-隐私协议提示弹窗 -->
      <div style="display: none" id="privacyModal" class="modal privacy-modal">
        <div class="modal-content">
          <header class="modal-header">
            <h3 class="title">服务协议和隐私政策</h3>
            <button type="button" id="privacyClose" class="close"></button>
          </header>
          <div class="modal-body">
            <p class="message">
              为了更好地保障您的权益，请阅读并知悉
              <a
                href="//i.kedou.com/agreement/register"
                target="_blank"
                class="a035"
                >《顺网用户协议》</a>
              <a
                href="//i.kedou.com/agreement/privacy"
                target="_blank"
                class="a035"
                >《隐私政策》</a>
            </p>
            <div class="btn-box">
              <button type="button" id="mergeAuthorization" class="btn btn-primary">
                去授权
              </button>
            </div>
          </div>
        </div>
      </div>

<%--      <div id="modal_select" style="display: none" class="modal">--%>
<%--        <div class="modal-content">--%>
<%--          <header class="header-bar">--%>
<%--            <h1 class="title">选择关联通行证账号</h1>--%>
<%--            <button type="button" class="close"></button>--%>
<%--          </header>--%>
<%--          <div class="modal-body">--%>
<%--            <div class="modal-logout-message">--%>
<%--              <section>--%>
<%--                <ul id="choisedUsername" class="user-name"></ul>--%>
<%--              </section>--%>
<%--            </div>--%>
<%--            <div class="btn-box">--%>
<%--              <button type="button" id="choiseAccount" class="btn btn-primary">--%>
<%--                确认--%>
<%--              </button>--%>
<%--            </div>--%>
<%--          </div>--%>
<%--        </div>--%>
<%--      </div>--%>

      <!-- TODO: ******** 新版本toast提示 -->
      <div class="toast-error" id="toastError" style="display: none;">
        <div class="toast-content">
          <p class="toast-text" id="toastErrorText"></p>
        </div>
      </div>

      <div id="captcha" style="display: none"></div>

      <script>
        var SSO_SERVER = "${ssoServer}";
        var interfaceServer = "${interfaceServer}";
        var needCheckCode = "${needCheckCode}" == 'true';
        var needSmsCheckCode = "${needSmsCheckCode}" == 'true';
        var validPhoneRex = <%=validPhone%>;
        var smsSendExpTime = ${smsSendExpTime};
        var oneLoginBusinessId = "${oneLoginBusinessId}";
        var staticUrl = "${staticServer}${cdnVersion}";
      </script>

      <script src="https://unpkg.com/vconsole@latest/dist/vconsole.min.js"></script>
      <script type="text/javascript">var vConsole = new VConsole();</script>

      <script type="text/javascript" src="${staticServer}/${cdnVersion}/js/jquery-2.2.0.min.js" ></script>
      <script type="text/javascript" src="${staticServer}/${cdnVersion}/login/js/jsencrypt.js" ></script>
      <script type="text/javascript" src="${staticServer}/${cdnVersion}/h5OneLogin/js/loginForHtml5.js?v=1" ></script>
      <script type="text/javascript" src="${staticServer}/${cdnVersion}/js/md5.js" ></script>
      <script type="text/javascript" src="${staticServer}/${cdnVersion}/js/gt.js" ></script>
      <script type="text/javascript" src="${staticServer}/${cdnVersion}/js/gtUtil.js" ></script>
      <script type="text/javascript" src="${staticServer}/${cdnVersion}/login/js/pwdUtil.js" ></script>
      <!-- TODO: ******** 网易sdk -->
      <script type="text/javascript" src="${staticServer}/${cdnVersion}/h5OneLogin/js/NEOneLogin.umd.js" ></script>
      <!-- TODO: ******** 手机号分步登录js -->
      <script type="text/javascript" src="${staticServer}/${cdnVersion}/h5OneLogin/js/verifyCode.js" ></script>
      <!-- TODO: ******** 网易本机登录接入js -->
      <script type="text/javascript" src="${staticServer}/${cdnVersion}/h5OneLogin/js/NEOneLogin.js" > </script>
<%--      <script>--%>
<%--        // 查询兄弟元素函数--%>
<%--        function siblings(elm, className) {--%>
<%--          var a = [];--%>
<%--          var p = className--%>
<%--            ? elm.parentNode.querySelectorAll(className)--%>
<%--            : elm.parentNode.children;--%>
<%--          for (var i = 0, pl = p.length; i < pl; i++) {--%>
<%--            if (p[i] !== elm) a.push(p[i]);--%>
<%--          }--%>
<%--          return a;--%>
<%--        }--%>
<%--        //tab 切换--%>
<%--        var $tab = document.querySelector(".tabs"),--%>
<%--          $formBox = document.querySelectorAll(".form-box"),--%>
<%--          $slide = document.querySelector(".tab .slide"),--%>
<%--          $li = $tab.querySelectorAll("li"),--%>
<%--          len = $li.length;--%>

<%--        for (var i = 0; i < len; i++) {--%>
<%--          (function (i) {--%>
<%--            $li[i].onclick = function () {--%>
<%--              var $this = this,--%>
<%--                $thisForm = $formBox[i];--%>
<%--              $this.className = "active";--%>
<%--              $thisForm.style.display = "block";--%>
<%--              $slide.style.left = 50 * i + "%";--%>

<%--              // 给兄弟元素删除class--%>
<%--              var _sib = siblings(this),--%>
<%--                _sibForm = siblings($thisForm, ".form-box");--%>
<%--              for (var j = 0; j < _sib.length; j++) {--%>
<%--                // 删除样式--%>
<%--                _sib[j].removeAttribute("class");--%>
<%--                _sibForm[j].style.display = "none";--%>
<%--              }--%>
<%--            };--%>
<%--          })(i);--%>
<%--        }--%>
<%--      </script>--%>
      <script>


        var errorMsg = "";

        $(function () {
          var mobileLogin = "";
          if (mobileLogin == "MOBILE_LOGIN") {
            $(".tab .slide").css({
              left: "50%",
            });
          } else {
            $(".tab .slide").css({
              left: "0px",
            });
          }

          if ($(".tips-info").is(":visible")) {
            $("body").on("click", function () {
              tipClose();
            });
          }

          <%--var username = [${userNameList}];--%>
          <%--var usernameStr = "";--%>
          <%--if (username.length > 0) {--%>
          <%--  for (var i = 0; i < username.length; i++) {--%>
          <%--    usernameStr +=--%>
          <%--      "<li><label><input type='radio' name='userName' value='" +--%>
          <%--      username[i] +--%>
          <%--      "'>" +--%>
          <%--      username[i];--%>
          <%--    ("</label></li>");--%>
          <%--  }--%>
          <%--  $("#choisedUsername").html(usernameStr);--%>
          <%--  $("#modal_select").show();--%>
          <%--  $(".modal").on("click", ".close", function () {--%>
          <%--    $(this).parents(".modal").hide();--%>
          <%--  });--%>
          <%--}--%>

          $(document).click(cleanTip);
        });



        // TODO: ******** 需要拼接渠道号的协议，加上class="privacy-channel"的元素
        $('.privacy-channel').on('click', function(event) {
          // 阻止默认行为
          event.preventDefault();

          // 执行自定义逻辑
          console.log('拦截到跳转:', this.href);

          // const channel = '10086'; // 假设渠道号为'10086'
          // 确认后跳转
          if (confirm('确定要跳转到这个页面吗？')) {
              var url = this.href;
              // 判断URL是否已有参数
              var separator = (url.indexOf('?') === -1) ? '?' : '&';
            // 打开新页面
            window.open(url + separator +`channel=${channel}`, '_blank');
            // 在当前页打开
            <%--// window.location.href = url + separator + `channel=${channel}`;--%>
          }
        });
      </script>
    </div>
  </body>
</html>
