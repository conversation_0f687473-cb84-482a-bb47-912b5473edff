########################## database info ##########################
siteId=sso
#cookie domain
cookieDomain=.kedou.com
#cookie life time etc:1day,2hour,30min,30sec
cookieLifeTime=30min

##email config
mail.host=mail.kedou.com
mail.port=25
mail.smtp.auth=true
mail.smtp.starttls.enable=false
mail.smtp.ssl.enable=false
mail.from=<EMAIL>|<EMAIL>
mail.password=kf#123|kf#123
mail.username=kf|kf
mail.smtp.timeout=120000
mail.smtp.connectiontimeout=120000

sichuanNet.getUserInfo.url=http://shop.4gfy.com/kedou/get_uinfo.php
laokei.getUserInfo.url=http://www.lkgame.com/itf/qCheckToken.aspx

dynamicMd5Key = 123456
dynamicAesKey = 0123456789012345
##\u987A\u4EE4\u5BC6\u7801\u751F\u6210\u4E2A\u6570
effectiveTime = 60

ssoSiteId=sso
ssoDomain=sso.kedou.com
ssoServer=https://sso.kedou.com
logServer=https://log.kedou.com
staticServer=https://static.kedou.com/sso/static/
interfaceServer=http://interface.kedou.com
identityServer=https\://i.kedou.com
cdnVersion=1.0.0
backStaticServer=https://static.kedou.com/back/1.0.0/

##\u65F6\u95F4\u6233\u6709\u6548\u671F\uFF0C\u5355\u4F4D\u4E3A\u79D2
headImgMaxSize=300
whitelist.unsafe.siteid=f1_2013|f1_pkg|yh|yh_movie|Passport

##\u9A8C\u8BC1\u8BBF\u95EE\u4EE4\u724C\u63A5\u53E3
interface.verifyToken.url=http://interface.kedou.com/front/interface/verifyToken.htm

##APPID
weixinWebCallbackUrl=https://sso.kedou.com/oauth/nologinAuth.do

##weibo
weiboAppKey=**********
weiboAppSecret=e89d5dc2ae98fe0f9f86208a2511edd7

#\u77ED\u4FE1\u53D1\u9001\u3001\u9A8C\u8BC1\u63A5\u53E3
#现在发送验证短信都调用子接口下面的都没用了
#interface.outMobileSendActiveNo.url=http://interface.kedou.com/front/interface/outMobileSendActiveNo.htm
#interface.outMobileSendActiveNo.md5key=123456
#interface.outMobileConfirm.url=http://interface.kedou.com/front/interface/outMobileConfirm.htm
#interface.outMobileConfirm.md5key=123456
interface.bindAsLoginAccount.url=http://interface.kedou.com/front/interface/bindAsLoginAccount.htm
interface.bindAsLoginAccount.md5key=123456

#getTicketWithoutLogin interface paramsters
interface.getTicketWithoutLogin.url=http://interface.kedou.com/front/interface/getTicket.htm
interface.getTicketWithoutLogin.md5key=123456

interface.outSiteMemberRegister.url=http://interface.kedou.com/front/interface/outSiteMemberRegister.htm
interface.outSiteMemberRegister.md5Key=123456

interface.updateOutSiteMember.url=http://interface.kedou.com/front/interface/updateMemberOutSite.htm
interface.updateOutSiteMember.md5Key=123456

interface.sendDynamicPasswordInner.url=http://interface.kedou.com/front/interface/sendDynamicPasswordInner.htm
interface.sendDynamicPasswordInner.md5Key=123456

interface.dynamicSecurityValidateInner.url=http://interface.kedou.com/front/interface/dynamicSecurityValidateInner.htm
interface.dynamicSecurityValidateInner.md5Key=123456

interface.connectedAccounts.url=http://interface.kedou.com/front/interface/connectedAccounts.htm
interface.connectedAccounts.md5Key=123456

interface.updateMemberExt.url=http://interface.kedou.com/front/interface/updateMemberExt.htm
interface.updateMemberExt.md5Key=123456

interface.singleAccountBindExt.url=http://interface.kedou.com/front/interface/singleAccountBindExt.htm
interface.singleAccountBindExt.md5Key=123456

#短信验证接口:三方绑定
interface.confirmForSingleAccountBind.url=http://interface.kedou.com/front/interface/confirmForSingleAccountBind.htm
interface.confirmForSingleAccountBind.md5Key=123456

#短信验证接口:登录 bindType = 1
interface.confirmForLogin.url=http://interface.kedou.com/front/interface/confirmForLogin.htm
interface.confirmForLogin.md5Key=123456

#发送短信接口：单账号绑定
interface.sendForSingleAccountBind.url=http://interface.kedou.com/front/interface/sendForMobileSingleAccountBind.htm
interface.sendForSingleAccountBind.md5Key=123456

#发送短信接口：手机号注册
interface.sendForMobileRegister.url=http://interface.kedou.com/front/interface/sendForMobileRegister.htm
interface.sendForMobileRegister.md5Key=123456

#发送短信接口：手机号注册 一键登录使用
interface.confirmForRegNoActiveNo.url=http://interface.kedou.com/front/interface/confirmForRegNoActiveNo.htm
interface.confirmForRegNoActiveNo.md5Key=123456

sensitive.file.path=D:/working_branches/com.shunwang.member.center/member-center-service/src/main/resources/sensitive.txt

# 极验验证码配置
geetest.id=a246c949351b79071665324f1d66072c
geetest.key=42650890a5a54c9dfe64fcbc38f9a02c

#手机短信验证码缓存时间 单位:分钟
sms.cache.time=60
#注册后用户缓存时间 单位:分钟
member.cache.time=10

##==========================MQ配置==========================
activemq.brokerURL=failover\:(tcp\://***************:61616?wireFormat.maxInactivityDuration=30000&trace=true)&maxReconnectDelay=10000
cache.topic=cache_topic
##==========================MQ配置==========================

#共享上传图片读取地址
assets.server=http\://static.kedou.com/passport/
assets.path=static
assets.version=1.0.0

db.queryWithCipherColumn=true
db.aes.password.basePassport=a63e0bf1901e81a6
db.aes.password.baseConfig=
db.aes.password.basePassportLog=a63e0bf1901e81a6

preAuth.callbackUrl=https://callback.kedou.com/dev_mpf3/weixin/preAuthCallback.do
task.refresh.wx.token.cronExpression=0 */10 * * * ?
qrCode.expireSeconds=300
# 缓存1小时
sceneCode.expireSeconds=3600

task.service.notify.memberCancel.cronExpression=0 */1 * * * ?
task.service.notify.mobileChange.cronExpression=0 */1 * * * ?

qrCodeLogin.expireSeconds=3600

#启用rpc
sw.rpc.enable=true
