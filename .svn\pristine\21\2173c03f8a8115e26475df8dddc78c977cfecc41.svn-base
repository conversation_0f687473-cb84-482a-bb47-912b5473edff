<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd" >
<sqlMap namespace="com.shunwang.basepassport.actu.pojo.CafeActuInfo">
	<resultMap class="com.shunwang.basepassport.actu.pojo.CafeActuInfo"
		id="cafeActuInfoMap">
        <result column="cafe_actuality_id" property="cafeActuInfoWrapper.cafeActualityId" jdbcType="INTEGER" />
        <result column="cafe_actuality_id" property="actuId" jdbcType="INTEGER" />
        <result column="member_id" property="cafeActuInfoWrapper.memberId" jdbcType="INTEGER" />
        <result column="member_name" property="cafeActuInfoWrapper.memberName" jdbcType="VARCHAR" />
        <result column="comp_name" property="cafeActuInfoWrapper.compName" jdbcType="VARCHAR" />
        <result column="licence_comp_name" property="cafeActuInfoWrapper.licenceCompName" jdbcType="VARCHAR" />
        <result column="cafe_addr_coded" property="cafeActuInfoWrapper.cafeAddrCoded" jdbcType="VARCHAR" />
        <result column="cafe_licence_no_coded" property="cafeActuInfoWrapper.cafeLicenceNoCoded" jdbcType="VARCHAR" />
        <result column="cafe_licence_img_coded" property="cafeActuInfoWrapper.cafeLicenceImgCoded" jdbcType="VARCHAR" />
        <result column="cafe_licence_endtime" property="cafeActuInfoWrapper.cafeLicenceEndtime" jdbcType="DATATIME" />
        <result column="licence_province_name" property="cafeActuInfoWrapper.licenceProvinceName" jdbcType="VARCHAR" />
        <result column="licence_city_name" property="cafeActuInfoWrapper.licenceCityName" jdbcType="VARCHAR" />
        <result column="link_phone_coded" property="cafeActuInfoWrapper.linkPhoneCoded" jdbcType="VARCHAR" />
        <result column="cafe_fax" property="cafeActuInfoWrapper.cafeFax" jdbcType="VARCHAR" />
        <result column="bill_type" property="cafeActuInfoWrapper.billType" jdbcType="VARCHAR" />
        <result column="revenue_certificate_no" property="cafeActuInfoWrapper.revenueCertificateNo" jdbcType="VARCHAR" />
        <result column="revenue_certificate_img" property="cafeActuInfoWrapper.revenueCertificateImg" jdbcType="VARCHAR" />
        <result column="comp_bank_no_coded" property="cafeActuInfoWrapper.bankNoCoded" jdbcType="VARCHAR" />
        <result column="comp_bank_name" property="cafeActuInfoWrapper.bankName" jdbcType="VARCHAR" />
        <result column="link_user_coded" property="cafeActuInfoWrapper.linkUserCoded" jdbcType="VARCHAR" />
        <result column="id_card_no_coded" property="cafeActuInfoWrapper.idCardNoCoded" jdbcType="VARCHAR" />
        <result column="id_card_type" property="cafeActuInfoWrapper.idCardType" jdbcType="CHAR" />
        <result column="id_card_endtime" property="cafeActuInfoWrapper.idCardEndtime" jdbcType="TIMESTAMP" />
        <result column="id_card_img1_coded" property="cafeActuInfoWrapper.idCardImg1Coded" jdbcType="VARCHAR" />
        <result column="id_card_img2_coded" property="cafeActuInfoWrapper.idCardImg2Coded" jdbcType="VARCHAR" />
        <result column="id_card_img3_coded" property="cafeActuInfoWrapper.idCardImg3Coded" jdbcType="VARCHAR" />
        <result column="id_card_img4_coded" property="cafeActuInfoWrapper.idCardImg4Coded" jdbcType="VARCHAR" />
        <result column="id_card_hand_img_coded" property="cafeActuInfoWrapper.idCardHandImgCoded" jdbcType="VARCHAR" />
        <result column="cafe_net_licence_img_coded" property="cafeActuInfoWrapper.cafeNetLicenceImgCoded" jdbcType="VARCHAR" />
        <result column="link_mobile_coded" property="cafeActuInfoWrapper.linkMobileCoded" jdbcType="VARCHAR" />
        <result column="info_state" property="cafeActuInfoWrapper.infoState" jdbcType="CHAR" />
        <result column="refuse_content" property="cafeActuInfoWrapper.refuseContent" jdbcType="VARCHAR" />
        <result column="refuse_time" property="cafeActuInfoWrapper.refuseTime" jdbcType="TIMESTAMP" />
        <result column="create_time" property="cafeActuInfoWrapper.createTime" jdbcType="TIMESTAMP" />
        <result column="edit_time" property="cafeActuInfoWrapper.editTime" jdbcType="TIMESTAMP" />
        <result column="check_time" property="cafeActuInfoWrapper.checkTime" jdbcType="TIMESTAMP" />
        <result column="check_user" property="cafeActuInfoWrapper.checkUser" jdbcType="VARCHAR" />
        <result column="unbind_time" property="cafeActuInfoWrapper.unbindTime" jdbcType="TIMESTAMP" />
        <result column="unbind_user" property="cafeActuInfoWrapper.unbindUser" jdbcType="VARCHAR" />
        <result column="repeal_time" property="cafeActuInfoWrapper.repealTime" jdbcType="VARCHAR" />
		<result column="facade_img_coded" property="cafeActuInfoWrapper.facadeImgCoded" jdbcType="VARCHAR" />
		<result column="unification" property="cafeActuInfoWrapper.unification" jdbcType="CHAR" />
        <result column="business_place_img_coded" property="cafeActuInfoWrapper.businessPlaceImgCoded" jdbcType="VARCHAR" />
        <result column="legal_person_name_coded" property="cafeActuInfoWrapper.legalPersonNameCoded" jdbcType="VARCHAR" />
        <result column="area_id" property="cafeActuInfoWrapper.areaId" jdbcType="INTEGER" />
        <result column="need_complete" property="cafeActuInfoWrapper.needComplete" jdbcType="INTEGER"/>
        <result column="cafe_licence_start_time" property="cafeActuInfoWrapper.cafeLicenceStarttime" jdbcType="DATATIME" />
        <result column="id_card_start_time" property="cafeActuInfoWrapper.idCardStartTime" jdbcType="DATATIME" />
        <result column="user_card_type" property="cafeActuInfoWrapper.userCardType" jdbcType="VARCHAR" />
        <result column="is_del" property="cafeActuInfoWrapper.isDel" jdbcType="INTEGER" />
        <result column="id_card_addr_coded" property="cafeActuInfoWrapper.idCardAddrCoded" jdbcType="VARCHAR"/>
    </resultMap>
	<typeAlias alias="cafeActuInfo"
		type="com.shunwang.basepassport.actu.pojo.CafeActuInfo" />
	<select id="getByMemberIdOrName" resultMap="cafeActuInfoMap" parameterClass="cafeActuInfo">
		SELECT
		a.cafe_actuality_id
		,a.member_id
		,a.member_name
		,a.comp_name
		,a.licence_comp_name
		,a.cafe_addr_coded
		,a.cafe_licence_no_coded
		,a.cafe_licence_img_coded
		,a.cafe_licence_endtime
		,a.licence_province_name
		,a.licence_city_name
		,a.link_phone_coded
		,a.cafe_fax
		,a.bill_type
		,a.revenue_certificate_no
		,a.revenue_certificate_img
		,a.comp_bank_name
		,a.comp_bank_no_coded
		,a.link_user_coded
		,a.id_card_no_coded
		,a.id_card_type
		,a.id_card_endtime
		,a.id_card_img1_coded
		,a.id_card_img2_coded
		,a.id_card_img3_coded
		,a.id_card_img4_coded
		,a.id_card_hand_img_coded
		,a.id_card_addr_coded
		,a.cafe_net_licence_img_coded
		,a.link_mobile_coded
		,a.info_state
		,a.refuse_content
		,a.refuse_time
		,a.create_time
        ,a.edit_time
		,a.check_time
		,a.check_user
		,a.unbind_time
		,a.unbind_user
		,a.repeal_time
		,a.facade_img_coded
		,a.unification
		,a.business_place_img_coded
		,a.legal_person_name_coded
		,a.area_id
		,a.need_complete,
        a.cafe_licence_start_time,
        a.id_card_start_time,
        a.is_del,
        b.user_card_type
		FROM personal_cafe_actuality_info a
        left join (SELECT b.* FROM personal_user_bank_card b WHERE b.default_state = 1) b on a.member_name = b.member_name
		WHERE 1=1
		<isNotEmpty property="memberId" prepend="and">
            a.member_id = #memberId:INTEGER #
        </isNotEmpty>
        <isNotEmpty property="memberName" prepend="and">
            a.member_name = #memberName:VARCHAR #
        </isNotEmpty>
		<isNotEmpty property="infoState" prepend="and">
            a.info_state = #infoState:INTEGER #
        </isNotEmpty>
		order by a.create_time DESC
		limit 1
</select>
	<!-- 添加记录 -->
	<insert id="insert" parameterClass="cafeActuInfo">
		INSERT INTO
		personal_cafe_actuality_info (
        member_id
        ,member_name
        ,comp_name
        ,licence_comp_name
        ,cafe_addr_coded
        ,cafe_licence_no_coded
        ,cafe_licence_img_coded
        ,cafe_licence_endtime
        ,licence_province_name
        ,licence_city_name
        ,link_phone_coded
        ,cafe_fax
        ,bill_type
        ,revenue_certificate_no
        ,revenue_certificate_img
        ,comp_bank_name
        ,comp_bank_no_coded
        ,link_user_coded
        ,id_card_no_coded
        ,id_card_type
        ,id_card_endtime
        ,id_card_img1_coded
        ,id_card_img2_coded
        ,id_card_img3_coded
        ,id_card_img4_coded
        ,id_card_hand_img_coded
        ,cafe_net_licence_img_coded
        ,link_mobile_coded
        ,info_state
        ,refuse_content
        ,refuse_time
        ,create_time
        ,edit_time
        ,check_time
        ,check_user
        ,unbind_time
        ,unbind_user
        ,repeal_time
		,facade_img_coded
		,unification
		,business_place_img_coded
		,legal_person_name_coded
		,area_id
        ,need_complete
		)
		VALUES (
		#cafeActuInfoWrapper.memberId:INTEGER#,
		#cafeActuInfoWrapper.memberName:VARCHAR#,
		#cafeActuInfoWrapper.compName:VARCHAR#,
        #cafeActuInfoWrapper.licenceCompName:VARCHAR#,
		#cafeActuInfoWrapper.cafeAddrCoded:VARCHAR#,
		#cafeActuInfoWrapper.cafeLicenceNoCoded:VARCHAR#,
		#cafeActuInfoWrapper.cafeLicenceImgCoded:VARCHAR#,
		#cafeActuInfoWrapper.cafeLicenceEndtime:DATATIME#,
		#cafeActuInfoWrapper.licenceProvinceName:VARCHAR#,
		#cafeActuInfoWrapper.licenceCityName:VARCHAR#,
		#cafeActuInfoWrapper.linkPhone:VARCHAR#,
		#cafeActuInfoWrapper.cafeFax:VARCHAR#,
		#cafeActuInfoWrapper.billType:VARCHAR#,
		#cafeActuInfoWrapper.revenueCertificateNo:VARCHAR#,
		#cafeActuInfoWrapper.revenueCertificateImg:VARCHAR#,
        #cafeActuInfoWrapper.bankName:VARCHAR#,
        #cafeActuInfoWrapper.bankNoCoded:VARCHAR#,
        #cafeActuInfoWrapper.linkUserCoded:VARCHAR#,
        #cafeActuInfoWrapper.idCardNoCoded:VARCHAR#,
        #cafeActuInfoWrapper.idCardType:CHAR#,
        #cafeActuInfoWrapper.idCardEndtime:TIMESTAMP#,
        #cafeActuInfoWrapper.idCardImg1Coded:VARCHAR#,
        #cafeActuInfoWrapper.idCardImg2Coded:VARCHAR#,
        #cafeActuInfoWrapper.idCardImg3Coded:VARCHAR#,
        #cafeActuInfoWrapper.idCardImg4Coded:VARCHAR#,
        #cafeActuInfoWrapper.idCardHandImgCoded:VARCHAR#,
        #cafeActuInfoWrapper.cafeNetLicenceImgCoded:VARCHAR#,
        #cafeActuInfoWrapper.linkMobileCoded:VARCHAR#,
        #cafeActuInfoWrapper.infoState:VARCHAR#,
		#cafeActuInfoWrapper.refuseContent:VARCHAR#,
		#cafeActuInfoWrapper.refuseTime:DATATIME#,
		#cafeActuInfoWrapper.createTime:DATATIME#,
        #cafeActuInfoWrapper.editTime:DATATIME#,
		#cafeActuInfoWrapper.checkTime:DATATIME#,
		#cafeActuInfoWrapper.checkUser:VARCHAR#,
		#cafeActuInfoWrapper.unbindTime:DATETIME#,
        #cafeActuInfoWrapper.unbindUser:VARCHAR#,
        #cafeActuInfoWrapper.repealTime:DATETIME#,
		#cafeActuInfoWrapper.facadeImgCoded:VARCHAR#,
		#cafeActuInfoWrapper.unification:VARCHAR#
		,#cafeActuInfoWrapper.businessPlaceImgCoded#
		,#cafeActuInfoWrapper.legalPersonNameCoded#
		,#cafeActuInfoWrapper.areaId#
        ,#cafeActuInfoWrapper.needComplete#
		)
	<selectKey resultClass="INTEGER" keyProperty="actuId">
			SELECT LAST_INSERT_ID()
    </selectKey>
	</insert>
	<select id="findLatestedUnCheckCntByMemberId" resultClass="java.lang.Integer"
		parameterClass="java.lang.Integer">
		SELECT count(1)
		FROM personal_cafe_actuality_info
		WHERE member_id = #value:INTEGER# and info_state = 1
</select>
<update id="repeal" parameterClass="java.lang.Integer" >
    UPDATE personal_cafe_actuality_info SET
   		info_state = 4,repeal_time=NOW()
		WHERE member_id = #value:INTEGER# 
		and info_state=1
</update>

    <update id="update" parameterClass="cafeActuInfo" >
        UPDATE personal_cafe_actuality_info a, (
            SELECT MAX(cafe_actuality_id) cafe_actuality_id FROM personal_cafe_actuality_info
            WHERE member_id = #cafeActuInfoWrapper.memberId:INTEGER# AND info_state != 1 AND info_state != 2) b
        SET  edit_time = NOW()
        <isNotEmpty prepend="," property="cafeActuInfoWrapper.infoState">
            info_state = #cafeActuInfoWrapper.infoState:INTEGER#
        </isNotEmpty>
        <isNotEmpty prepend="," property="cafeActuInfoWrapper.compName">
            comp_name = #cafeActuInfoWrapper.compName:VARCHAR#
        </isNotEmpty>
        <isNotEmpty prepend="," property="cafeActuInfoWrapper.cafeAddrCoded">
            cafe_addr_coded = #cafeActuInfoWrapper.cafeAddrCoded:VARCHAR#
        </isNotEmpty>
        <isNotEmpty prepend="," property="cafeActuInfoWrapper.cafeLicenceImgCoded">
            cafe_licence_img_coded = #cafeActuInfoWrapper.cafeLicenceImgCoded:VARCHAR#
        </isNotEmpty>
        <isNotEmpty prepend="," property="cafeActuInfoWrapper.linkUserCoded">
            link_user_coded = #cafeActuInfoWrapper.linkUserCoded:VARCHAR#
        </isNotEmpty>
        <isNotEmpty prepend="," property="cafeActuInfoWrapper.idCardImg1Coded">
            id_card_img1_coded = #cafeActuInfoWrapper.idCardImg1Coded:VARCHAR#
        </isNotEmpty>
        <isNotEmpty prepend="," property="cafeActuInfoWrapper.idCardImg2Coded">
            id_card_img2_coded = #cafeActuInfoWrapper.idCardImg2Coded:VARCHAR#
        </isNotEmpty>
        <isNotEmpty prepend="," property="cafeActuInfoWrapper.idCardImg3Coded">
            id_card_img3_coded = #cafeActuInfoWrapper.idCardImg3Coded:VARCHAR#
        </isNotEmpty>
        <isNotEmpty prepend="," property="cafeActuInfoWrapper.idCardImg4Coded">
            id_card_img4_coded = #cafeActuInfoWrapper.idCardImg4Coded:VARCHAR#
        </isNotEmpty>
        <isNotEmpty prepend="," property="cafeActuInfoWrapper.idCardHandImgCoded">
            id_card_hand_img_coded = #cafeActuInfoWrapper.idCardHandImgCoded:VARCHAR#
        </isNotEmpty>
        <isNotEmpty prepend="," property="cafeActuInfoWrapper.cafeNetLicenceImgCoded">
            cafe_net_licence_img_coded = #cafeActuInfoWrapper.cafeNetLicenceImgCoded:VARCHAR#
        </isNotEmpty>
        <isNotEmpty prepend="," property="cafeActuInfoWrapper.linkMobileCoded">
            link_mobile_coded = #cafeActuInfoWrapper.linkMobileCoded:VARCHAR #
        </isNotEmpty>
        <isNotEmpty prepend="," property="cafeActuInfoWrapper.idCardNoCoded">
            id_card_no_coded = #cafeActuInfoWrapper.idCardNoCoded:VARCHAR #
        </isNotEmpty>
		<isNotEmpty prepend="," property="cafeActuInfoWrapper.facadeImgCoded">
			facade_img_coded = #cafeActuInfoWrapper.facadeImgCoded:VARCHAR#
		</isNotEmpty>
		<isNotEmpty prepend="," property="cafeActuInfoWrapper.unification">
			unification = #cafeActuInfoWrapper.unification:VARCHAR#
		</isNotEmpty>
        <isNotEmpty prepend="," property="cafeActuInfoWrapper.cafeLicenceNoCoded">
            cafe_licence_no_coded = #cafeActuInfoWrapper.cafeLicenceNoCoded:VARCHAR #
        </isNotEmpty>
        <isNotEmpty prepend="," property="cafeActuInfoWrapper.businessPlaceImgCoded">
            business_place_img_coded = #cafeActuInfoWrapper.businessPlaceImgCoded:VARCHAR#
        </isNotEmpty>
        <isNotEmpty prepend="," property="cafeActuInfoWrapper.legalPersonNameCoded">
            legal_person_name_coded = #cafeActuInfoWrapper.legalPersonNameCoded:VARCHAR#
        </isNotEmpty>
        <isNotEmpty prepend="," property="cafeActuInfoWrapper.infoState">
            info_state = #cafeActuInfoWrapper.infoState:VARCHAR#
        </isNotEmpty>
        <isNotNull prepend="," property="cafeActuInfoWrapper.needComplete">
            need_complete = #cafeActuInfoWrapper.needComplete:INTEGER #
        </isNotNull>
        <isNotEmpty prepend="," property="cafeActuInfoWrapper.areaId">
            area_id = #cafeActuInfoWrapper.areaId:INTEGER #
        </isNotEmpty>
        WHERE  a.cafe_actuality_id = b.cafe_actuality_id
            
    </update>

    <update id="updateInfoState" parameterClass="cafeActuInfo" >
        UPDATE personal_cafe_actuality_info
        SET info_state = #cafeActuInfoWrapper.infoState:INTEGER#, edit_time = NOW()
        <isNotEmpty prepend="," property="cafeActuInfoWrapper.unBindReason">
            unbind_reason = #cafeActuInfoWrapper.unBindReason:VARCHAR#
        </isNotEmpty>
        <isNotEmpty prepend="," property="cafeActuInfoWrapper.unbindTime">
            unbind_time = #cafeActuInfoWrapper.unbindTime:DATETIME#
        </isNotEmpty>
        <isNotEmpty prepend="," property="cafeActuInfoWrapper.unbindUser">
            unbind_user = #cafeActuInfoWrapper.unbindUser:VARCHAR#
        </isNotEmpty>
        WHERE  cafe_actuality_id = #cafeActuInfoWrapper.cafeActualityId:INTEGER #
    </update>

    <select id="getCntByInfoState" resultClass="java.lang.Integer" parameterClass="cafeActuInfo">
        SELECT count(1)
        FROM personal_cafe_actuality_info
        WHERE member_id = #memberId:INTEGER#
        and info_state = #infoState#
    </select>
</sqlMap>