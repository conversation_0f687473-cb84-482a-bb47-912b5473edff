package com.shunwang.basepassport.commonExp;

import com.shunwang.baseStone.core.exception.BaseStoneException;
import com.shunwang.util.lang.StringUtil;
/**
 * ****************************
 * 版权所有：顺网科技 保留所有权利
 * 创建日期: Jul 25, 2011 2:56:46 PM
 * 创建作者：陈积慧
 * 文件名称：ParamNotFoundExp.java
 * 版本： 1.0
 * 功能：检查唯一性接口异常类
 * 最后修改时间：
 * 修改记录：
 ***************************************
 */
public class ParamLenErrorExp extends BaseStoneException {

	private static final long serialVersionUID = 1L;
	
	public ParamLenErrorExp(){
		super("1020","参数太长！");
	}
	
	public ParamLenErrorExp(String msg) {
		this.setMsgId("1020");
		this.setMsg(StringUtil.isBlank(msg)?StringUtil.EMPTY_STRING:msg + "参数太长");
	}

}
