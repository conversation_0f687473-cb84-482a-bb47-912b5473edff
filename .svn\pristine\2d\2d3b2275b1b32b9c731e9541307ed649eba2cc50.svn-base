package com.shunwang.baseStone.sso.web;

import com.shunwang.baseStone.bussiness.dao.BussinessDao;
import com.shunwang.baseStone.sso.context.TicketContext;
import com.shunwang.baseStone.sso.context.UserTockenContext;
import com.shunwang.baseStone.sso.pojo.Ticket;
import com.shunwang.baseStone.sso.pojo.UserTocken;
import com.shunwang.basepassport.user.common.UserLoginSessionUtil;
import com.shunwang.util.lang.StringUtil;

public class TicketCheckAction extends SSOAction {
	/**
	 * <AUTHOR> create at 2011-10-17 下午05:10:04 
	 */
	private static final long serialVersionUID = -6074635058133842357L;
	private String ticketId;
	private String siteId;
	private String token;

	private BussinessDao bussinessDao ;
	
	public String getSiteId() {
		return siteId;
	}

	public void setSiteId(String siteId) {
		this.siteId = siteId;
	}

	public String getTicketId() {
		return ticketId;
	}

	public void setTicketId(String ticketId) {
		this.ticketId = ticketId;
	}

	@Override
	public void process() {
		Ticket ticket = TicketContext.validateTicket(ticketId);
		// 除了多不能免登单外，其他都可以免登
//		if( null != oldBusiness ) {
//			Bussiness bussiness = bussinessDao.getById(siteId) ;
//			if(bussiness.isSingleAccount() && !oldBusiness.isSingleAccount()){
//				LOGGER.info("用户[memberId:{}]在业务方[siteId:{}]不能免登陆[siteId:{}]",ticket.getUserName(),oldSiteId,siteId) ;
//				throw new BaseStoneException(ErrorCode.C_1080.getCode(),ErrorCode.C_1080.getDescription()) ;
//			}
//		}

		this.getBaseResponse().setUserId(ticket.getUserId());
		this.getBaseResponse().setUserName(ticket.getUserName());
		this.getBaseResponse().setTitleName(ticket.getTitleName());
		this.getBaseResponse().setNickName(ticket.getNickName());
		this.getBaseResponse().setHeadImg(ticket.getHeadImg());
		this.getBaseResponse().setOpenId(ticket.getOpenId());
		boolean ssoAuth = UserLoginSessionUtil.isSsoAuth(ticket.getUserName());
		this.getBaseResponse().setSsoAuth(ssoAuth);
		if(StringUtil.isNotBlank(token)){
			UserTocken tocken = new UserTocken(ticket);
			tocken.setUserTokenId(token);
			UserTockenContext.refreshTocken(tocken, StringUtil.trimNull(siteId));
			//跨站免登时写，其他sso单点登录已在入口写了，此处不写
			if (!siteId.equals(ticket.getSiteId())) {
				UserLoginSessionUtil.saveSession(tocken.getUserName(), ticket.getLoginType(), ticket.getSiteId(), siteId);
			}
		}
	}

	public void setToken(String token) {
		this.token = token;
	}

	public String getToken() {
		return token;
	}

	public BussinessDao getBussinessDao() {
		return bussinessDao;
	}

	public void setBussinessDao(BussinessDao bussinessDao) {
		this.bussinessDao = bussinessDao;
	}
}
