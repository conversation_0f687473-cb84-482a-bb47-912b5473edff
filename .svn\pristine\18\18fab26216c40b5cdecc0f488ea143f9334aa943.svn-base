<%@ page contentType="text/html; charset=UTF-8"%>
<%@ include file="../common/taglibs.jsp" %> 
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>正在跳转，请稍候...</title>
   <style type="text/css">
      .waitbg{width:100%;text-align:center }
      .waitwapper{margin:auto; width:460px;}
      .waitbg .waitbox{ margin:100px 0 100px 0px; width:460px; height:240px; border:1px solid #94bcdf; background-color:#f5f9fc;}
      .waitbg .waittitle{ text-align:left;height:25px; line-height:25px; background-color:#cde7f6; color:#1971b8; padding-left:10px; font-weight:700;}
      .waitbg .waitpic{ float:left; margin:60px 0 0 90px;*margin:60px 0 0 90px!important;*margin:60px 0 0 40px;width:47px; height:47px;}
      .waitbg .waittext{ float:left;margin:60px 0 0 20px; line-height:47px; font-weight:700; font-size:14px;}
   </style>
   <c:if test="${not empty loginCss && not empty loginCss.cssUrl }">
      <link href="${loginCss.cssUrl}" rel="stylesheet" type="text/css"/>
   </c:if>
</head>
<body>

<c:if test="${empty fmtCallback || fmtCallback=='' || fmtCallback=='null'}">
   <span>系统提示：fmtCallback不存在，页面响应终止</span>
</c:if>
<c:if test="${not empty fmtCallback && fmtCallback!='' && fmtCallback!='null'}">
   <c:if test="${mobileLogin eq false}">
   <div class="waitbg">
      <div class="waitwapper">
         <div class="waitbox">
            <div class="waittitle">跳转提示</div>
            <div class="waitpic"><img src="${staticServer}/${cdnVersion}/images/waiting.gif" width="47" height="47" /></div>
            <div class="waittext">页面正在跳转中，请稍等...</div>
         </div>
      </div>
   </div>
   </c:if>
   <c:if test="${isFreeLoginWriteDataFlag == true}">
<%--      <script>function setClientCallback(){};</script>--%>
      <script type="text/javascript">
         var script=document.createElement("script");
         script.src="https://plugin.kedou.com:9199/setClientTicket.htm?clientTicket=${clientTicket}&callback=JsonpCallBack";
         document.getElementsByTagName("head")[0].appendChild(script);
      </script>
   </c:if>
   <script type="text/javascript" src="${staticServer}/${cdnVersion}/js/jquery-1.4.3.js"></script>
   <script type="text/javascript" src="https://res.icafe28.com/slot/js/common.js"></script>
<script type="text/javascript">

		$(document).ready(function() {
			try{
               var isSingleAccount = ${isSingleAccount} ;
               var tgt = "${tgt}" ;
               if(isSingleAccount){
                  window.opener.jumpUrl({"fmtCallbackUrl":"${fmtCallback}","tgt":tgt}) ;
                  window.close();
               }else{
                  if (window.SW_RTB_SDK) {
                     SW_RTB_SDK.getIds(function (value) {
                        jump(value);
                     });
                  } else {
                     jump('');
                  }
               }
			}catch (e) {
				jump('');
			}

           function jump(sso_rbt_data) {
              var tgt = "${tgt}" ;
              if(tgt=="self"){
                 window.location.href="${fmtCallback}" + '&sso_rbt_data=' + sso_rbt_data;
                 return;
              }else{
                 top.location="${fmtCallback}" + '&sso_rbt_data=' + sso_rbt_data;
                 return;
              }
           }
		});
	
</script>
</c:if>
</body>
