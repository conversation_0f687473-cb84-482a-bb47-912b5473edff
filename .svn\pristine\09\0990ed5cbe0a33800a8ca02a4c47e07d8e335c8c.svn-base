<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.0.xsd">
	<!--新增其他平台接口时，需配置对应的service-bean-->
	<bean id="swpayServiceClient" class="com.shunwang.basepassport.manager.service.swpay.SwpayServiceClient">
	 	  <property name="configInterfaceDao" ref="configInterfaceDao"/>
	</bean>

	<!--新增其他平台接口时，需配置对应的service-bean-->
	<bean id="netbarServiceClient" class="com.shunwang.basepassport.manager.service.netbar.NetbarServiceClient">
		<property name="configInterfaceDao" ref="configInterfaceDao"/>
	</bean>

	<bean id="interfaceServiceClient" class="com.shunwang.basepassport.manager.service.InterfaceServiceClient">
		<property name="configInterfaceDao" ref="configInterfaceDao"/>
	</bean>

	<bean id="chuanglanServiceClient" class="com.shunwang.basepassport.manager.service.chuanglan.ChuanglanServiceClient">
		<property name="configInterfaceDao" ref="configInterfaceDao"/>
	</bean>

	<bean id="reportServiceClient" class="com.shunwang.basepassport.manager.service.report.ReportServiceClient">
		<property name="configInterfaceDao" ref="configInterfaceDao"/>
	</bean>

	<bean id="dcapiServiceClient" class="com.shunwang.basepassport.manager.service.dcapi.DcapiServiceClient">
		<property name="configInterfaceDao" ref="configInterfaceDao"/>
	</bean>
	<bean id="blackCardServiceClient" class="com.shunwang.basepassport.manager.service.dcapi.BlackCardServiceClient">
		<property name="configInterfaceDao" ref="configInterfaceDao"/>
	</bean>
    <bean id="riskServiceBo" class="com.shunwang.basepassport.manager.service.risk.RiskServiceClient">
        <property name="configInterfaceDao" ref="configInterfaceDao"/>
    </bean>
    <bean id="oaServiceBo" class="com.shunwang.basepassport.manager.service.oa.OaServiceClient">
        <property name="configInterfaceDao" ref="configInterfaceDao"/>
    </bean>

	<bean id="checkMobileServiceClient" class="com.shunwang.basepassport.manager.service.geetest.CheckMobileServiceClient">
		<property name="configInterfaceDao" ref="configInterfaceDao"/>
		<property name="configOneLoginDao" ref="configOneLoginDao"/>
	</bean>
	<bean id="swpayInterfaceBo" class="com.shunwang.basepassport.manager.service.bo.impl.SwpayInterfaceImpl">
	</bean>
	<bean id="netbarInterfaceBo" class="com.shunwang.basepassport.manager.service.bo.impl.NetbarInterfaceImpl">
	</bean>

	<bean id="reportServiceBo" class="com.shunwang.basepassport.manager.service.bo.impl.ReportServiceBoImpl">

	</bean>

	<bean id="userGeneralOrderServiceClient" class="com.shunwang.basepassport.manager.service.netbar.UserGeneralOrderServiceClient">
		<property name="configInterfaceDao" ref="configInterfaceDao"/>
	</bean>
	<bean id="idCardDesensitizationServiceClient" class="com.shunwang.basepassport.manager.service.netbar.IdCardDesensitizationServiceClient">
		<property name="configInterfaceDao" ref="configInterfaceDao"/>
	</bean>

	<bean id="logonApiClient" class="com.shunwang.basepassport.manager.service.LogonApiClient">
		<property name="configInterfaceDao" ref="configInterfaceDao"/>
	</bean>
</beans>
