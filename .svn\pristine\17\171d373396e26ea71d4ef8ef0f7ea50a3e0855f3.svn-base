package com.shunwang.basepassport.manager.request;

import com.shunwang.basepassport.config.pojo.ConfigInterface;
import com.shunwang.basepassport.manager.HttpRequest;
import com.shunwang.basepassport.manager.IResponse;

import java.util.HashMap;
import java.util.Map;

public abstract class BaseRequest<T extends IResponse> implements HttpRequest<T> {
    private String url;

    protected Map<String, String> headers;

    @Override
    public void addHeader(String key, String value) {
        if (headers == null) {
            headers = new HashMap<>();
        }
        headers.put(key, value);
    }

    @Override
    public Map<String, String> getHeaders() {
        return headers;
    }

    @Override
    public String buildUrl() {
        return getUrl();
    }

    /**
     * 构建请求参数
     *
     * @return
     */
    public abstract Map<String, String> buildParams();


    public abstract Class<T> getResponseClass();

    /**
     * 获取配置interfaceKey
     *
     * @return
     */
    public abstract Integer getInterfaceKey();

    /**
     * 设置配置
     */
    public abstract void doInterfaceSetting(ConfigInterface setting);

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public void setHeaders(Map<String, String> headers) {
        this.headers = headers;
    }
}
