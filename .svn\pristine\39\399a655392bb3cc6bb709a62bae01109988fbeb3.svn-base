<%@ page contentType="text/html;charset=UTF-8"%>
<%@ include file="/common/taglibs.jsp"%>
<script type="text/javascript" src="${staticServer }/scripts/front/common/config.js"></script>
<style type="text/css">
	a[disabled="disabled"]{color:#989898;}
	</style>

<div class="preview_box" id="show" style="overflow: hidden;z-index: 999999;width:180px;height:196px" onmouseover="showDiv()" onmouseout="hide();">
    <span id="content"></span><iframe style="width: 180px;height: 196px;position: absolute;left: 0;bottom: 0;z-index: -1;" name="iframe" frameborder="0" scrolling="no"></iframe>
</div>
<script type="text/javascript">
	var flag=0;
	
	function showDiv(){
		$("#show").show();
	}
	function show(event,imageName){	
		var x = event.pageX ? event.pageX : event.clientX + document.documentElement.scrollLeft;
		var y =  event.pageY ? event.pageY : event.clientY + document.documentElement.scrollTop;
		if(x>860){
			x-= 230;
		}else if(x<500&&x>=400){
			x+=240;
		}

		$("#show").show();
		$("#content").html('<img src='+headServerUrl+imageName+' width=180px height=196px/>');
	}
	function showSubmit(event,imageName){
		var x = event.pageX ? event.pageX : event.clientX + document.documentElement.scrollLeft;
		var y =  event.pageY ? event.pageY : event.clientY + document.documentElement.scrollTop;
		if(900>x>860){
			x-= 230;
		}else if(x<500&&x>=460){
			x+=240;
		}
		$("#show").show();
		$("#content").html('<img src='+imageName+' width=180px height=196px/>');
	}
	function showPersonal(event,imageName){
		var data=tipPositon(event);
		$("#show").show();
		$("#content").html('<img src='+headServerUrl+imageName+' width=180px height=196px/>');
	}

	function showNetImg(event,imageName){
		var data=tipPositon(event);
		$("#show").show();
		$("#content").html('<img src='+imageName+' width=180px height=196px/>');
	}

	function showPersonalFind(event,imageName){
		var data=tipPositon(event);
		$("#show").show();
		$("#content").html('<img src='+headServerUrl+imageName+' width=180px height=190px/>');
	}

	function tipPositon(event){
		var x = event.pageX ? event.pageX : event.clientX + document.documentElement.scrollLeft;
		var y =  event.pageY ? event.pageY : event.clientY + document.documentElement.scrollTop;
	    if(900>x>860){
			x-= 230;
		}else if(x<500&&x>=400){
			x+=240;
			}
		return {x:x,y:y}
		}
	function hide(){
		$("#show").hide();
		flag=0;
	}
	function showCert(event,personalCert,companyCert,type){
   		//var tab = "<table><tr><td>个人实名认证 </td><td id='personal'></td></tr><tr><td>企业实名认证 </td><td id='company'></td></tr></table>";
   		var tab = "<table><tr><td>企业实名认证 </td><td id='company'></td></tr><tr><td>个人实名认证 </td><td id='personal'></td></tr></table>";
		if(flag==0){
			flag=1;
			var x = event.pageX ? event.pageX : event.clientX + document.documentElement.scrollLeft;
			var y =  event.pageY ? event.pageY : event.clientY + document.documentElement.scrollTop;
			var obj = $("#show");
			x+=-20;
			y+=-65;
			obj.show();
			$("#content").html(tab);
			showCertState(personalCert,companyCert,type);
		}
		
	}
	
	function showCertState(personalCert,companyCert,type){
		if('1'==type){
			if('未认证'==personalCert ||'未申请'==personalCert || '已撤消' == personalCert|| '已拒绝' == personalCert){
				$("#content").html('未进行实名认证<br />'+'<a class="a035" href="${appServer}/front/actuality/goPersonalActuality_front.htm" id="str2">申请认证</a>');
			}if('已通过'==personalCert){
				$("#content").html('已进行实名认证<br />'+'<a class="a035" href="${appServer}/front/actuality/actu_checkResult_front.htm" id="str2">查看认证</a>');
			}if('审核中'==personalCert){
				$("#content").html('实名认证审核中<br />'+'<a class="a035" href="${appServer}/front/actuality/actu_checkResult_front.htm" id="str2">查看认证</a>');
			}
		}else{
			if(personalCert.indexOf('已通过')!=-1){
				$("#personal").html('已通过 '+'<a class="a035" href="${appServer}/front/actuality/actu_checkResult_front.htm" id="str2">查看</a>');
			}if(personalCert.indexOf('未申请')!=-1 || personalCert.indexOf('已撤消')!=-1||personalCert.indexOf('未认证')!=-1){
				$("#personal").html('未进行 '+'<a '+isDisable(companyCert)+' class="a035" href="'+getAHref(companyCert)+'" id="str2">申请</a>');
			}if(personalCert.indexOf('已拒绝')!=-1){
				$("#personal").html('未通过 '+'<a '+isDisable(companyCert)+'class="a035" href="'+getAHref(companyCert)+'" id="str2">申请</a>');
			}if(personalCert.indexOf('审核中')!=-1){
				$("#personal").html('审核中 '+'<a class="a035" href="${appServer}/front/actuality/actu_checkResult_front.htm" id="str2">查看</a>');
			}
			if(companyCert.indexOf('已通过')!=-1){
				$("#company").html('已通过 '+'<a class="a035" href="${appServer}/front/actuality/actu_checkResult_front.htm" id="str2">查看</a>');
			}if(companyCert.indexOf('未申请')!=-1 || companyCert.indexOf('已撤消')!=-1||companyCert.indexOf('未认证')!=-1){
				$("#company").html('未进行 '+'<a class="a035" href="${appServer}/front/actuality/companyActu_goCompanyInfo_front.htm" id="str2">申请</a>');
			}if(companyCert.indexOf('已拒绝')!=-1){
				$("#company").html('未通过 '+'<a class="a035" href="${appServer}/front/actuality/companyActu_goCompanyInfo_front.htm" id="str2">申请</a>');
			}if(companyCert.indexOf('审核中')!=-1){
				$("#company").html('审核中 '+'<a class="a035" href="${appServer}/front/actuality/actu_checkResult_front.htm" id="str2">查看</a>');
			}
		}
	}

	function isDisable(state){
		if(state.indexOf('已通过')==-1)
			return "disabled='disabled'";
	}
    function getAHref(state){
    	if(state.indexOf('已通过')==-1)
        	return "javascript:void(0)";
    	else
        	return "${appServer}/front/actuality/goPersonalActuality_front.htm";
        }
    function getNoticeHeight(){
    	var notice = document.getElementById("notice");
    	var refuse = document.getElementById("refuseContext20111019195049");
    	var height=0;
    	if(notice&&notice.style.display!="none"){
    		height = $("#notice").height();
    		}
    	if(refuse&&refuse.style.display!="none"){
    		height = $("#refuseContext20111019195049").height()+height;
    		}
		return height;
    }
</script>