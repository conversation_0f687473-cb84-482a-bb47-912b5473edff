<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd" >

<sqlMap namespace="com.shunwang.basepassport.weixin.pojo.WeixinTokenInfo">

  <typeAlias alias="weixinTokenInfo" type="com.shunwang.basepassport.weixin.pojo.WeixinTokenInfo"/>

  <resultMap class="com.shunwang.basepassport.weixin.pojo.WeixinTokenInfo" id="weixinTokenInfoMap">
    <result column="id" property="id" jdbcType="int"/>
    <result column="access_token" property="accessToken" jdbcType="varchar"/>
    <result column="update_time" property="updateTime" jdbcType="datetime"/>
    <result column="expires_in" property="expiresIn" jdbcType="int"/>
  </resultMap>

  <select id="get" resultMap="weixinTokenInfoMap" >
    select * from config_weixin_token_info order by update_time desc limit 1;
  </select>

  <insert id="insert" parameterClass="weixinTokenInfo" >
    insert into config_weixin_token_info
      (access_token, update_time,expires_in) 
    values ( 
      #accessToken:varchar#,
      #updateTime:datetime#,
      #expiresIn:int# 
    )
  </insert>

  <update id="update" parameterClass="weixinTokenInfo" >
    update config_weixin_token_info set
      access_token = #accessToken:varchar#,
      update_time = #updateTime:DATETIME# ,
      expires_in = #expiresIn:DATETIME# 
    where id = #id:int#
  </update>

</sqlMap>
