package com.shunwang.basepassport.mobile.web;

import java.util.Date;

import com.shunwang.baseStone.context.BaseStoneContext;
import com.shunwang.baseStone.encrypt.Encrypt;
import com.shunwang.baseStone.encrypt.pojo.EncryptItem;
import com.shunwang.sms.utils.SMSSenderUtil;
import com.shunwang.baseStone.sysconfig.context.SysConfigContext;
import com.shunwang.basepassport.binder.common.BinderConstants;
import com.shunwang.basepassport.binder.dao.MobileBinderDao;
import com.shunwang.basepassport.binder.dao.PersonalSendNumberDao;
import com.shunwang.basepassport.binder.exception.BindByOtherExp;
import com.shunwang.basepassport.binder.exception.FormatErrorExp;
import com.shunwang.basepassport.binder.exception.OneDaySendThreeTimesExp;
import com.shunwang.basepassport.binder.exception.OneMinuteSendExp;
import com.shunwang.basepassport.binder.pojo.PersonalSendNumber;
import com.shunwang.basepassport.commonExp.ParamNotFoundExp;
import com.shunwang.basepassport.mobile.dao.RegActiveNoDao;
import com.shunwang.basepassport.mobile.pojo.RegActiveNo;
import com.shunwang.basepassport.mobile.response.SendResultResponse;
import com.shunwang.basepassport.user.common.MemberConstants;
import com.shunwang.basepassport.user.common.UserCheckUtil;
import com.shunwang.util.StringUtil;
import com.shunwang.util.date.DateUtil;
import com.shunwang.util.math.RandomUtil;

/**
 * ****************************
 * 版权所有：顺网科技 保留所有权利
 * 创建日期: 2013-06-24
 * 创建作者：王松年
 * 文件名称：RegActNoSendAction.java
 * 版本： 1.0
 * 功能：手机验证码发送(手机SDK用)
 * 最后修改时间：
 * 修改记录：
 ***************************************
 */
public class RegActNoSendAction extends MobileSdkBaseAction {

	private static final long serialVersionUID = 1L;

	public static final String MOBILE_REG_MSG = "mobileRegMsg";

    private String uuid;
   	
    
	@Override
	public void process() throws Exception {

        //发送次数检查
        checkSendNumber(mobileNumber);

        //生成激活码,保存,发送
        String activeNo = RandomUtil.getRandomStr(6, true);
        savePersonalSendNumber();
        saveRegActiveNo(activeNo);
        sendRegActiveNo(activeNo);
		
        //结果处理
        SendResultResponse response = new SendResultResponse();
		this.setBaseResponse(response);
	}

	/**
	 * 发送次数检查
	 */
	private void checkSendNumber(String number){
		PersonalSendNumber personalSendNumber = new PersonalSendNumber();
		personalSendNumber.setNumber(number);

		if(!UserCheckUtil.checkMobile(number)) {
			throw new FormatErrorExp("手机号码");
		}
		
		MobileBinderDao mobileBinderDao = (MobileBinderDao)
				BaseStoneContext.getInstance().getBean("mobileBinderDao");
		if (mobileBinderDao.getBinderByNumber(number) != null ) {
			throw new BindByOtherExp(BinderConstants.MOBILE);
		}
		
        PersonalSendNumberDao personalSendNumberDao =
            (PersonalSendNumberDao) BaseStoneContext.getInstance().getBean("personalSendNumberDao");

        //每分钟最多1次
		personalSendNumber.setBeginDate(DateUtil.ymdhmsFormat(DateUtil.addMinute(new Date(), -1)));
		Integer cnt = personalSendNumberDao.findCntByTime(personalSendNumber);
		if(null != cnt && cnt>0)
			throw new OneMinuteSendExp(BinderConstants.MOBILE);

        //每天最多3次
		personalSendNumber.setBeginDate(DateUtil.ymdhmsFormat(DateUtil.getCurrentDateBegin()));
		cnt = personalSendNumberDao.findCntByTime(personalSendNumber);
		if(null != cnt && cnt>2)
			throw new OneDaySendThreeTimesExp(); 
	}


    /**
     * 保存发送次数
     */
	private PersonalSendNumber savePersonalSendNumber(){
		PersonalSendNumber personalSendNumber = new PersonalSendNumber();
		personalSendNumber.setDoType(BinderConstants.DOTYPE_MOBILE);
		personalSendNumber.setFromType(Integer.parseInt(BinderConstants.MOBILE_REGISTER));
		personalSendNumber.setNumber(mobileNumber);
		personalSendNumber.setSendTime(new Date());
        personalSendNumber.save();
		return personalSendNumber;
	}

    /**
     * 发送验证码
     */
    private void sendRegActiveNo(String activeNo) throws Exception {
		String content= SysConfigContext.getSysValueByKey(MOBILE_REG_MSG);
        content = content.replace("$activeNo$", activeNo);
		SMSSenderUtil.sendMsg(mobileNumber, content);
    }

    /**
     * 保存验证码发送信息
     */
    private RegActiveNo saveRegActiveNo(String activeNo) {
        RegActiveNoDao regActiveNoDao = (RegActiveNoDao) BaseStoneContext.getInstance().getBean("regActiveNoDao");

        RegActiveNo regActiveNo = regActiveNoDao.getBySiteIdAndMobile(this.getSiteId(), mobileNumber);

        if (regActiveNo != null ) {
            regActiveNo.setActiveNo(activeNo);
            regActiveNo.setSendTime(new Date());
            regActiveNo.setActiveTime(null);
            regActiveNoDao.update(regActiveNo);
        } else {
            regActiveNo = new RegActiveNo();
            regActiveNo.setUuid(uuid);
            regActiveNo.setActiveNo(activeNo);
            regActiveNo.setSendMobile(mobileNumber);
            regActiveNo.setSendTime(new Date());
            regActiveNo.setSiteId(this.getSiteId());
            regActiveNoDao.save(regActiveNo);
        }
        return regActiveNo;

    }

    /**
     * ***********
      * 创建日期: Jul 26, 2011 1:43:15 PM
      * 创建作者：chenjh
      * @return 
      * 功能：检查参数是否为空
      *************
     */
	public void checkParam(){	
		if(StringUtil.isBlank(getSiteId()))
			throw new ParamNotFoundExp("siteId");
		if(StringUtil.isBlank(getTime()))
			throw new ParamNotFoundExp("time");
		if(StringUtil.isBlank(getSign()))
			throw new ParamNotFoundExp("sign");
        if (StringUtil.isBlank(mobileNumber)) 
			throw new ParamNotFoundExp("mobileNumber");
        if (StringUtil.isBlank(uuid)) 
			throw new ParamNotFoundExp("uuid");
	}
	
	/**
	 * ***********
	  * 创建日期: Jul 25, 2011 3:18:34 PM
	  * 创建作者：chenjh
	  * @return 
	  * 功能：获取未加密的 串 
	  *************
	 */
	public String buildSignString(){
		Encrypt  encrypt=new Encrypt();
		encrypt.addItem(new EncryptItem(getSiteId()));
		encrypt.addItem(new EncryptItem(getTime()));		
		return encrypt.buildSign();
	}

	
	@Override
	public String getSiteName() {		
		return MemberConstants.SDK_CODE_SEND;
	}
	
    public void setMobileNumber(String mobileNumber) {
        this.mobileNumber=mobileNumber;
    }
    public String getMobileNumber() {
        return this.mobileNumber;
    }

    public void setUuid(String uuid) {
        this.uuid=uuid;
    }
    public String getUuid() {
        return this.uuid;
    }

}
