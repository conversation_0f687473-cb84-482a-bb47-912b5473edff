<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">
	
	
	<!-- 申诉找回密码 -->
	<bean id="appealFindPwdAction" class="com.shunwang.passport.find.web.AppealFindPwdAction" scope="prototype"></bean>
	<bean id="findManageAction" class="com.shunwang.passport.find.web.FindManageAction" scope="prototype"></bean>
	
	<!-- 实名认证找回密码 -->
	<bean id="appealActuPwdAction" class="com.shunwang.passport.find.web.FindActuAction" scope="prototype"></bean>
	
	<!-- 通过密保问题找回密码 -->
	<bean id="findPwdQueAction" class="com.shunwang.passport.find.web.FindQueAction" scope="prototype"></bean>
	
	<!-- 邮箱找回密码 -->
	<bean id="findPwdEmailAction" class="com.shunwang.passport.find.web.FindEmailAction" scope="prototype">
	   <property name="binderDao" ref="emailBinderDao"></property>
	 </bean>
	
	
	<bean id="findPwdMobileAction" class="com.shunwang.passport.find.web.FindMobileAction" scope="prototype">
	 <property name="binderDao" ref="mobileBinderDao"></property>
	</bean>
    <!-- 申诉找回密保问题 -->
	<bean id="appealFindQueAction" class="com.shunwang.passport.find.web.AppealFindQueAction" scope="prototype"></bean>
</beans>