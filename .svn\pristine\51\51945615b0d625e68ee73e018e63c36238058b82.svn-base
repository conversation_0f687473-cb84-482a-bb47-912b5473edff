var vcload = false;
var vcPswd = false;
//rsa加密公钥
var publicKey="MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAhWM2WBglvW4hAYCV7HFxmNQ9rq+bzxn7\n" +
	"ryZQMvH/v2pFxVTC5BiY8aR+mQFgewaIkJ8mXpOrZThtPiaxPSje/uOMNpi10X/X3HfuZwPCISeP\n" +
	"OZDoCaif/2Uf1bWaTcAysQsNJ55N/WsqPZVEUg02zZnQ+jIoDodZXX9LYIirCmsl1zpk1AtbXfj6\n" +
	"woEh05lbYv1Et4i3s0u00yKVhqg2E0UIeKOICQ8zWaFAjP6vF1zfclIXrRIYmU+7p7hhzIGLNfRM\n" +
	"C3BK+mbKkUOWqiYe6Kd7krQjLRMxe6mJvd0tFa1Bt6UYfxFtrr3Wbx3nOSyRs3BZNsVqHgV7oOiV\n" +
	"ohF/vQIDAQAB"

var sso_encrypt = new JSEncrypt();
sso_encrypt.setPublicKey(publicKey);
/**
 * 刷新检验码
 */
function refreshCheckcode(){
	var nowTime = new Date();
	$("#checkCodeImg").attr("src", "checkCode.do?=t"+nowTime.getTime());
}

function refreshSmsCheckcode() {
	var nowTime = new Date();
	$("#checkCode2").attr("src", "smsCheckCode.do?=t"+nowTime.getTime());
}

function refreshBindCheckcode() {
	var nowTime = new Date();
	$("#checkCode3").attr("src", "smsCheckCode.do?key=singleBindAccount&=t"+nowTime.getTime());
}
/**
 * vc控件被触发
 */
function checkCodeSel(e){
	if(e.checked){
		vcPswd = true;
		if(!vcload){
			initPlug();
			vcload = true;
		}
		document.getElementById("inputEdit").style.display="none";
		document.getElementById("safeEdit").style.display="";
	}else{
		vcPswd = false;
		document.getElementById("inputEdit").style.display="";
		document.getElementById("safeEdit").style.display="none";
	}
	document.getElementById("needSafe").value = e.checked;
}

function loginDefaultCheck(){
	var userName = document.getElementById("userName");
	if(userName.value == ""
			|| userName.value=="用户名/邮箱/手机号"){

		showError("请输入用户名和密码");
		return false;
	}
	var autoLoginType = $("#authFreeType").val();
	if(!!autoLoginType && autoLoginType === '2' && !$("#agreementCheckbox2").attr('checked')){
		$(".protocol-dialog").show();
		return false;
	}
	return true;
}

/**
 * login 按钮被点击
 */
function loginDefaultClick(actionUrl){
	var pswd = document.getElementById("password");
	var md5 = document.getElementById("md5")
	var userName = document.getElementById("userName");
	if(userName.value == ""
		|| userName.value=="用户名/邮箱/手机号" ){
		showError("请输入用户名和密码");
		return;
	}
	var isSubmit = false;
	if (!needCheckCode) {
		$.ajax({
			url:  "/loginPreCheck.do",
			type: 'post',
			dataType: 'json',
			data: {"userName": userName.value},
			async: false,
			success: function(data){
				if(data.code === -1) {//-1时账号不允许登录
					showError(data.msg);
					isSubmit = false;
				} else if (data.code === 1) {//1时需要极验校验
					needCheckCode = true;
					loginGt.settings.showGt = true;
					loginGt.settings.autoClick = true;
					loginGt.init();
					isSubmit = false;
				} else {
					isSubmit = true;
				}
			},
			error: function(){
				showSmsError("系统异常!");
				isSubmit = false;
			}
		});
	} else {
		isSubmit = true;
	}
	if (isSubmit) {
		try{
			var result = vcPswd &&  icafeEditer.getPassword();
		}catch(e){
			return false;
		}
		if(vcPswd && icafeEditer){
			icafeEditer.isNeedMD5Encode(false);
			pswd.value = icafeEditer.getPassword();
			md5.value = true;
		}else{
			var pwdi = document.getElementById("passwordInp").value;
			if (isWeakPwd(pwdi) || pwdi == userName.value) {
				document.getElementById("weakPwdState").value = isLooseWeakPwd(pwdi) ? "3" : "1";
			} else {
				document.getElementById("weakPwdState").value = "2";
			}
			//md5加密
			var md5Enc = hex_md5(pwdi);
			var encrypt = new JSEncrypt();
			encrypt.setPublicKey(publicKey);
			//rsa加密后传值
			pswd.value =encrypt.encrypt(md5Enc);

			document.getElementById("passwordInp").value="" ;
			md5.value = true;
		}
		if(pswd.value == ""
			|| userName.value=="用户名/邮箱/手机号" ){
			showError("请输入用户名和密码");
			return;
		}
		document.getElementById("login_form_").action=actionUrl;
		document.getElementById("login_form_").target="";
		document.forms[0].submit();
	}
}

function loginClick(){
	loginDefaultClick("/login.do");

}
function smsLoginClick() {
	smsLogin("/outMobileSmsConfirm.do");
}

function bodyLoad(){
	isIE = $.browser.msie;
	if(isIE){
		document.getElementById("safeDiv").style.display="";
//		document.getElementById("needSafeCheck").checked = needSafe;
//		checkCodeSel(document.getElementById("needSafeCheck"));
	}else{
		document.getElementById("safeDiv").style.display="none";
	}
	if(!$("#errorSpan").html()||$("#errorSpan").html()=='&nbsp;'){
		$("#errorSpan").removeClass("errorSpan");
	}
	else{
		$("#errorSpan").addClass("errorSpan");
	}
}

//短信验证码倒计时
var sms_sec = 60;
function showSendAgain(){
    var sendAgain=document.getElementById("btnSendSMS");
    if(null != sendAgain) {
        if(sms_sec > 0){
            sendAgain.disabled=true;
            sendAgain.className="phone_sms";
            sendAgain.value = "重新发送"+sms_sec.toString()+'s';
            sms_sec = sms_sec-1;
            setTimeout("showSendAgain()",1000); 
        } else {
            sendAgain.className="phone_sms_resend";
            sendAgain.disabled=false;
            sendAgain.value = "重新发送";
			sms_sec=60;
        }
    }
}

function numberInputCheck() {
	var number = $("#sms-phonenumber").val();
	if(!checkMobile(number)) {
		$("#number").focus();
		return false;
	}
	return true;
}

//发送验证码点击事件
function sendAgain(callback){
	if (!numberInputCheck()) {
		return false;
	}
    var sendAgain=document.getElementById("btnSendSMS");
	sendAgain.disabled = true;
	var geetest_challenge = $("#geetest_challenge").val();
	var geetest_validate = $("#geetest_validate").val();
	var geetest_seccode = $("#geetest_seccode").val();
	var number = $("#sms-phonenumber").val();
	var data = {
		smsNumber: sso_encrypt.encrypt(number),
		site_id: siteId,
		geetest_challenge:!!geetest_challenge ? geetest_challenge : '',
		geetest_validate:!!geetest_validate ? geetest_validate : '',
		geetest_seccode:!!geetest_seccode ? geetest_seccode : '',
		reportData: $("input[name = 'reportData']").val()
	};
	$.ajax({
        url:  "/sendSmsActiveNo.do",
        type: 'post',
        dataType: 'json',
        data: data,
        success: function(data){
        	// 条款默认不打勾，当用户输入手机号时点击发送验证码时，判断该手机号是否已经注册。如已经注册，则自动勾选
			var isAgreementChecked = $("#agreementCheckbox").attr("checked");
            $("#agreementCheckbox").attr("checked", isAgreementChecked || agreeAutoChecked);
        	if(!data.result){
        		if(data.msg=='同一业务同一手机号码1分钟内只能发一次短信验证码！'){
        			data.msg="短信发送过于频繁，请稍后再试";
				}
        		showSmsError(data.msg);
        		sendAgain.disabled = false;
            } else {
        		if (data.isReg=="true") {
                    $("#agreementCheckbox").attr("checked","true");
				}
				smsSendExpTime = 60;
            	showSendAgain();
            }
            callback&&callback();
        },
        error: function(){
            $("#agreementCheckbox").attr("checked",agreeAutoChecked);//默认
        	showSmsError("系统异常!");
            sendAgain.disabled = false;

        }
     });
	
}

//刷新浏览器处理
function refresh() {
	if (smsSendExpTime != "" && smsSendExpTime != undefined) {
		sms_sec = smsSendExpTime;
		showSendAgain();
	}
	
}

function checkMobile(number) {
    var number = $.trim(number);
    if (number.length == 0) {
    	showSmsError("请输入手机号码");
        return false;
    }
    hideSmsError();
    if (!isMobileNo(number)) {
    	showSmsError("手机号码格式错误");
        return false;
    }
    hideSmsError();
    return true;
}

function checkMobileCode(smsCheckcode) {
    var smsCheckcode = $.trim(smsCheckcode);
    if (smsCheckcode.length == 0) {
    	showSmsError("请输入短信验证码");
        return false;
    }
    hideSmsError();
    var patn = /^[0-9]{6}$/;
    if (!patn.test(smsCheckcode)) {
    	showSmsError("短信验证码错误");
        return false;
    }
    hideSmsError();
    return true;
}

function checkAgreement() {
    if ($("#agreementCheckbox").attr("checked"))
    	return true;

    showSmsError("请确认相关条款");
    return false;
}

function checkAutoLoginAgreement() {
	if ($("#agreementCheckbox-auto").attr("checked"))
		return true;

	showAutoLoginError("请确认相关条款");
	return false;
}
function isMobileNo(s){
	var reg = "";
	if (typeof(validPhoneRex)==="undefined" || validPhoneRex == "" || validPhoneRex == null) {
		reg = /^1[34578]\d{9}$/;
	} else{
		reg = validPhoneRex;
	}
    if(reg.exec(s)) return true;
    else return false;
}

var needSubmit = true;
function smsLogin(actionUrl) {
	var smsCheckcode = $("#sms-checkcode").val();
	var number = $("#sms-phonenumber").val();
	if(!checkAgreement() || !checkMobile(number) || !checkMobileCode(smsCheckcode))
        return false;
	if (needSubmit) {
		needSubmit = false;
		$("#phonenumber").val(sso_encrypt.encrypt(number));
		$("#checkcode").val(sso_encrypt.encrypt(smsCheckcode));
		document.getElementById("sms_login_form").action= actionUrl;
		document.getElementById("sms_login_form").target="";
		document.forms['sms_login_form'].submit();
	}
}

/**
 * 检测是否为弱密码
 * @param pwdi
 * @returns {boolean}
 */
function isWeakPwd(pwdi) {
	if (!/^[A-Za-z0-9`~!@#\$%\^&\*\(\)\-_=\+\[\{\]\}\\\|;:'"",<.>\/\?]+$/.test(pwdi)) {
		return true;
	}
	if (!/^(?![A-Za-z]+$)(?!\d+$)(?![`~!@#\$%\^&\*\(\)\-_=\+\[\{\]\}\\\|;:'"",<.>\/\?]+$)\S{2,16}$/.test(pwdi)) {
		return true;
	}
	return false;
}

/**
 * 检测是否为宽松弱密码
 * @param pwdi
 * @returns {boolean}
 */
function isLooseWeakPwd(pwdi) {
	var regex = /^(?![0-9]+$)(?![a-zA-Z]+$)[a-zA-Z0-9]{8,16}$/;
	return !regex.test(pwdi);
}
/**
 * 显示错误
 */
function showError(msg){
	document.getElementById("errorSpan").innerHTML = msg;
	$("#errorSpan").addClass("errorSpan");
}

function showSmsError(msg) {
	document.getElementById("errorSpan2").innerHTML = msg;
	$("#errorSpan2").addClass("errorSpan");
}

function hideSmsError() {
	document.getElementById("errorSpan2").innerHTML = "";
	$("#errorSpan2").removeClass("errorSpan");
}

function showAutoLoginError(msg) {
	document.getElementById("errorSpanAuto").innerHTML = msg;
}

function hideAutoLoginError() {
	document.getElementById("errorSpanAuto").innerHTML = "";
}