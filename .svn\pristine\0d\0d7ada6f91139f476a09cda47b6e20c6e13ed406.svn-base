package com.shunwang.basepassport.binder.web;

import com.shunwang.baseStone.context.IPContext;
import com.shunwang.baseStone.encrypt.Encrypt;
import com.shunwang.baseStone.encrypt.pojo.EncryptItem;
import com.shunwang.basepassport.binder.exception.LoginBindErrorExp;
import com.shunwang.basepassport.binder.exception.UserNameUnMatchedMobile;
import com.shunwang.basepassport.binder.response.MobileBindRespone;
import com.shunwang.basepassport.user.common.MemberConstants;
import com.shunwang.basepassport.user.common.TicketUtil;
import com.shunwang.basepassport.user.exception.MsgNotFoundExp;
import com.shunwang.basepassport.user.pojo.Member;
import com.shunwang.basepassport.user.web.MemberAction;
import com.shunwang.util.lang.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.Date;

/**
 * Created by bd.fang on 2016/3/8.
 */
public class MobileBindAsLoginAction extends MemberAction {

    private final static Logger log = LoggerFactory.getLogger(MobileBindAsLoginAction.class);
    private String userName;
    private String mobile;
    private String loginIp;
    private String environment;
    private String remark;
    @Override
    public String getMemberName() {
        return userName;
    }

    @Override
    public void doProcess() {
        Member member =  getDao().getByMobile(getMobile());
        if(member != null) {
            log.error(getMobile()+"该手机号已被其他账户设为登录手机号【" + member.getMemberName() + "】",new LoginBindErrorExp());
            throw new LoginBindErrorExp("【" + member.getMemberName() + "】");
        }
        member = getDao().getByName(getUserName());
        if(member == null) {
            throw new MsgNotFoundExp(getUserName());
        }
        if(!getMobile().equals(member.getMobile())) {
            log.error("需设为登录账号的手机号:"+getMobile()+"与通行证账号:"+member.getMemberName()+"不匹配",new UserNameUnMatchedMobile());
            throw new UserNameUnMatchedMobile();
        }

        member.setBindState(member.getBindState() | MemberConstants.MEMBER_STATE_PHONE_AS_LOGIN_ACCOUNT);
        member.update();
        member.setLoginType(MemberConstants.LOGIN_TYPE_ACTIVE_NO);
        member.setEnv(getEnvironment());
        member.setExtData(getRemark());
        IPContext.setIp(getIp());
        member.loginWithNoPwd();
        MobileBindRespone response =new MobileBindRespone(member);
        response.setTicket(TicketUtil.buildLoginTicket(member));
        this.setBaseResponse(response);
    }

    @Override
    public String getIp() {
        if(StringUtil.isBlank(this.loginIp)) {
            this.loginIp = super.getIp();
        }
        return this.loginIp;
    }

    @Override
    public String getSiteName() {
        return "手机号绑定为登录号";
    }

    @Override
    public String buildSignString() {
        // TODO Auto-generated method stub
        Encrypt encrypt=new Encrypt();
        encrypt.addItem(new EncryptItem(this.getSiteId()));
        encrypt.addItem(new EncryptItem(this.getUserName(),true));
        encrypt.addItem(new EncryptItem(this.getMobile()));
        encrypt.addItem(new EncryptItem(this.getTime()));
        this.setUserName(decodeByUTF(this.getUserName()));
        return encrypt.buildSign();
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    /**
     * utf-8解码
     * @param string
     * @return
     */
    protected String decodeByUTF(String string){
        try {
            return  URLDecoder.decode(new String(StringUtil.trimNull(string).getBytes("ISO-8859-1")), "utf-8");
        } catch (UnsupportedEncodingException e) {
            // TODO Auto-generated catch block
        }
        return "";
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getLoginIp() {
        return loginIp;
    }

    public void setLoginIp(String loginIp) {
        this.loginIp = loginIp;
    }

    public String getEnvironment() {
        return environment;
    }

    public void setEnvironment(String environment) {
        this.environment = environment;
    }


}
