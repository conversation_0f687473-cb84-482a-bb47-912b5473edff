package com.shunwang.baseStone.upload;

import java.io.File;
import java.util.UUID;

import com.shunwang.baseStone.sysconfig.context.SysConfigContext;
import com.shunwang.upload.action.CKEditorAction;

public class MyCkEditorAction extends CKEditorAction {
	/**
	 * <AUTHOR> create at 2011-10-17 下午02:18:12 
	 */
	private static final long serialVersionUID = -5376901058169200016L;
	private static final String fileSplit = "/";
	private String fileDir="rightHome";
	
	@Override
	protected String getDir(File file) {
		// TODO Auto-generated method stub
		StringBuffer sb = new StringBuffer();
		sb.append(SysConfigContext.getImgDir());
		if(fileDir != null && !fileDir.equals("")){
			sb.append(fileSplit);
			sb.append("images");
			sb.append(fileSplit);
			sb.append(fileDir);
			sb.append(fileSplit);
		}
		return sb.toString();
	}

	@Override
	protected String getFileName(File file) {
		// TODO Auto-generated method stub
		return UUID.randomUUID().toString()+"."+this.getExtension(this.getUploadFileName());
	}

	@Override
	protected String getFileUrl(String dir, String fileName) {
		// TODO Auto-generated method stub
		StringBuffer sb = new StringBuffer();
		sb.append(SysConfigContext.getImgUrl());
		if(fileDir != null && !fileDir.equals("")){
			sb.append(fileSplit);
			sb.append("images");
			sb.append(fileSplit);
			sb.append(fileDir);
			sb.append(fileSplit);
		}
		sb.append(fileName);
		return sb.toString();
	}
	
}
