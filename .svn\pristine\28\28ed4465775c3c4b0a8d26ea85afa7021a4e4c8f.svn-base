package com.shunwang.basepassport.user.response;

import com.google.gson.annotations.Expose;
import com.shunwang.baseStone.core.response.BaseStoneResponse;
import com.shunwang.basepassport.user.pojo.Member;
import com.shunwang.xmlbean.annotation.XmlInit;

/**
 * Created by bd.fang on 2016/3/17.
 */
public class ThirdLoginResponse extends BaseStoneResponse {

    /**用户名**/
    @Expose
    private String memberName;

    /**用户ID**/
    @Expose
    private Integer memberId;

    /**登录校验票据**/
    @Expose
    private String ticket;

    public ThirdLoginResponse(Member member) {
        memberName = member.getMemberName();
        memberId=member.getMemberId();
    }


    @XmlInit(path="ticket")
    public String getTicket() {
        return ticket;
    }

    public void setTicket(String ticket) {
        this.ticket = ticket;
    }

    @XmlInit(path="userName")
    public String getMemberName() {
        return memberName;
    }

    public void setMemberName(String memberName) {
        this.memberName = memberName;
    }

    @XmlInit(path="userId")
    public Integer getMemberId() {
        return memberId;
    }

    public void setMemberId(Integer memberId) {
        this.memberId = memberId;
    }


}
