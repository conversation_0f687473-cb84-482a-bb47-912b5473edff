package com.shunwang.basepassport.weixin.pojo;

import com.google.gson.JsonObject;
import com.shunwang.basepassport.weixin.constant.BarLoginReportEnum;
import com.shunwang.util.json.GsonUtil;

import java.io.Serializable;

public class BarLoginReportInfo implements Serializable {

    private static final long serialVersionUID = 7513728715554724193L;

    private String guid;
    private String unionId;
    private String appid;
    private Integer optType;
    private String extInfo;
    private String areaId;
    private String barid;
    private String scene;

    public BarLoginReportInfo(BarLoginReportEnum.OptTypeEnum optTypeEnum) {
        this.optType = optTypeEnum.getValue();
    }
    public BarLoginReportInfo(BarLoginReportEnum.OptTypeEnum optTypeEnum, String extData) {
        this.optType = optTypeEnum.getValue();
        JsonObject extJson = GsonUtil.jsonToBean(extData, JsonObject.class);
        this.guid = GsonUtil.getStringFromJsonObject(extJson, "guid");
    }


    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getUnionId() {
        return unionId;
    }

    public void setUnionId(String unionId) {
        this.unionId = unionId;
    }

    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid;
    }

    public Integer getOptType() {
        return optType;
    }

    public void setOptType(Integer optType) {
        this.optType = optType;
    }

    public String getExtInfo() {
        return extInfo;
    }

    public void setExtInfo(String extInfo) {
        this.extInfo = extInfo;
    }

    public String getAreaId() {
        return areaId;
    }

    public void setAreaId(String areaId) {
        this.areaId = areaId;
    }

    public String getBarid() {
        return barid;
    }

    public void setBarid(String barid) {
        this.barid = barid;
    }

    public String getScene() {
        return scene;
    }

    public void setScene(String scene) {
        this.scene = scene;
    }
}
