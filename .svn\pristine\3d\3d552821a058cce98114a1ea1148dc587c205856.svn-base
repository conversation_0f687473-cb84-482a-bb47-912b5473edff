package com.shunwang.passport.weixin.interceptor;

import com.shunwang.passport.weixin.constants.WeixinConstants;
import com.shunwang.baseStone.context.SessionContext;
import java.net.URLEncoder;
import com.shunwang.passport.weixin.pojo.WeixinConf;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpServletRequest;
import org.apache.struts2.ServletActionContext;
import com.opensymphony.xwork2.ActionInvocation;
import com.opensymphony.xwork2.interceptor.AbstractInterceptor;

/**
 * openId拦截,如果发现用户的session中没有openId, 则自动跳转
 * 到微信的授权页进行授权(浏览器自动2次重定向,用户无感知).
 * 
 * <AUTHOR>
 * @since 2014-04
 *
*/
public class OpenIdInterceptor extends AbstractInterceptor {

	private static final long serialVersionUID = 46614181472481L;
    private static final String WEIXIN_OAUTH2_URL = "https://open.weixin.qq.com/connect/oauth2/authorize";

    private WeixinConf weixinConf;

	@Override
    @SuppressWarnings("all")
	public String intercept(ActionInvocation invocator) throws Exception {
		HttpServletRequest req = ServletActionContext.getRequest();
		HttpServletResponse resp = ServletActionContext.getResponse();

        //本机服务的域名
        String weixinServer = "http://" + weixinConf.getDomain();

        String origUrl =weixinServer + req.getRequestURI();
        String redirectUrl = weixinServer + "/weixin/callback.htm?origUrl=" 
                + URLEncoder.encode(origUrl);

        String openId = (String)SessionContext.get(WeixinConstants.KEY_OPENID);
        if (openId == null ) {
            String authURL = buildOauthURL(redirectUrl);
            resp.sendRedirect(authURL);
        }

		return invocator.invoke();
    }
    public void setWeixinConf(WeixinConf weixinConf) {
        this.weixinConf=weixinConf;
    }
    public WeixinConf getWeixinConf() {
        return this.weixinConf;
    }

    @SuppressWarnings("all")
    private String buildOauthURL(String redirectURL) {
        StringBuilder sb = new StringBuilder();
        sb.append(WEIXIN_OAUTH2_URL);
        sb.append("?");
        sb.append("appid=").append(weixinConf.getAppid()).append("&");
        sb.append("redirect_uri=").append(URLEncoder.encode(redirectURL)).append("&");
        sb.append("response_type=").append("code").append("&");
        sb.append("scope=").append("snsapi_base").append("&");
        sb.append("state=").append("faint123").append("&");
        sb.append("#wechat_redirect");
        return sb.toString();
    }

}
