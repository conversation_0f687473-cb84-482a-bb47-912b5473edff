package com.shunwang.baseStone.encrypt.imp;

import com.shunwang.baseStone.encrypt.EncryptMode;
import com.shunwang.util.encrypt.AesEncrypt;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
/**
 * ****************************
 * 版权所有：顺网科技 保留所有权利
 * 创建日期: 2011-07-15 PM
 * 创建作者：陈积慧
 * 文件名称：AesEncryptImp.java
 * 版本： 1.0
 * 功能：AES加密实现类
 * 最后修改时间：
 * 修改记录：
 ****************************************/
public class AesEncryptImp implements EncryptMode{
		private final static Logger log = LoggerFactory.getLogger(AesEncryptImp.class);
	    
		@Override
		public String createSign(String value,String key)  {
			try {
				return AesEncrypt.Encrypt(value, key);
			} catch (Exception e) {
				log.error("signValue:["+value+"],签名加密出错",e);
			}
			return null;
		}
				
}
