package com.shunwang.basepassport.user.web;

import com.shunwang.baseStone.bussiness.dao.BussinessDao;
import com.shunwang.baseStone.bussiness.pojo.Bussiness;
import com.shunwang.baseStone.cache.CacheKeyConstant;
import com.shunwang.baseStone.context.RedisContext;
import com.shunwang.baseStone.encrypt.Encrypt;
import com.shunwang.baseStone.encrypt.pojo.EncryptItem;
import com.shunwang.basepassport.commonExp.ParamNotFoundExp;
import com.shunwang.basepassport.user.common.AuthorizedCodeUtil;
import com.shunwang.basepassport.user.exception.TokenException;
import com.shunwang.basepassport.user.pojo.AuthorizedCode;
import com.shunwang.basepassport.user.pojo.MemberToken;
import com.shunwang.basepassport.user.response.AuthorizedCodeResponse;
import com.shunwang.util.date.DateUtil;
import com.shunwang.util.lang.StringUtil;

import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * User:pf.ma
 * Date:2017/06/05
 * Time:9:13
 * 已经授权方使用accessToken来给其他方siteId（authorizedSiteId)发code,以便后续授权
 */
public class AuthorizedCodeAction extends AbstractLoginAction{

	private BussinessDao bussinessDao ;


	public final static long TIME_OUT = 5 ;//5分钟失效

	/**
	 * 要授权的siteId
	 */
	private String authorizedSiteId ;

	/**
	 * 已经授权的业务方accessToken
	 * @return
	 */
	private String accessToken ;


	@Override
	public void process() throws Exception {
		loadMemberName();

		String cacheKey = CacheKeyConstant.InterfaceToken.ACCESS_TOKEN_PREFIX + getMemberName() + accessToken;
		MemberToken memberToken =  RedisContext.getRedisCache().get(cacheKey,MemberToken.class);

		if (null == memberToken) {
			memberToken = findMemberTokenByMemberNameAndSiteId(getMemberName(), getSiteId());
		}
		// 检验访问令牌与刷新令牌
		if(null == memberToken){
			throw new TokenException(TokenException.TOKEN_NOT_EXIST, "访问令牌不存在");
		}
		if (!getAccessToken().equals(memberToken.getAccessToken())) {
			throw new TokenException(TokenException.ACCESS_TOKEN_INVALID, "无效的访问令牌");
		} else if (DateUtil.compare(new Date(), memberToken.getAccessTokenTimeOut(),
				DateUtil.ONE_MINUTE) > 0) {
			throw new TokenException(TokenException.ACCESS_TOKEN_TIME_OUT, "访问令牌已过期");
		} else if (DateUtil.compare(new Date(), memberToken.getRefreshTokenTimeOut(),
				DateUtil.ONE_MINUTE) > 0) {
			throw new TokenException(TokenException.REFRESH_TOKEN_TIME_OUT, "刷新令牌已过期");
		}
		// 校验被授权方siteId
		checkAuthorizedSiteId() ;
		// 生成code,由于具有一次性且时效性短的原因，故放在缓存中
		AuthorizedCode authorizedCode = saveAuthorizedCode() ;
		this.setBaseResponse(new AuthorizedCodeResponse(authorizedCode,getMemberName())) ;

	}

	private void checkAuthorizedSiteId(){
		Bussiness bussiness = bussinessDao.getById(authorizedSiteId);
		if (bussiness == null) {
			throw new TokenException(TokenException.AUTHORIZED_SITE_ID_NOT_EXISTED, "被授权站点不存在");
		}
	}

	private AuthorizedCode saveAuthorizedCode() {
		AuthorizedCode code = buildAuthorizedCode() ;
		String cacheKey = AuthorizedCodeUtil.getAuthorizedCodeKey(getMemberName(), authorizedSiteId);
		logger.info("用户[" + getMemberName() + "]增加authorizedCode["+ cacheKey +" => " + accessToken + "]缓存");
		if (!RedisContext.getRedisCache().set(cacheKey, code, Integer.valueOf(TIME_OUT+""), TimeUnit.MINUTES)) {
			logger.error("用户[" + getMemberName() + "]增加authorizedCode["+ cacheKey +" => " + accessToken + "]缓存异常");
		}
		return code ;
	}

	private AuthorizedCode buildAuthorizedCode(){
		String code = AuthorizedCodeUtil.createAuthorizedCode() ;
		AuthorizedCode authorizedCode = new AuthorizedCode() ;
		authorizedCode.setMemberId(getMemberId()) ;
		authorizedCode.setSiteId(getSiteId()) ;
		authorizedCode.setAuthorizedSiteId(authorizedSiteId) ;
		authorizedCode.setAuthorizedCode(code) ;
		return authorizedCode ;
	}

	@Override
	public String buildSignString() {
		Encrypt encrypt = new Encrypt();
		encrypt.addItem(new EncryptItem(super.getSiteId()));
		encrypt.addItem(new EncryptItem(super.getTime()));
		encrypt.addItem(new EncryptItem(getMemberId()));
		encrypt.addItem(new EncryptItem(accessToken));
		encrypt.addItem(new EncryptItem(authorizedSiteId));
		return encrypt.buildSign();
	}

	@Override
	public void checkParam() {
		if (StringUtil.isBlank(getMemberId())) {
			throw new ParamNotFoundExp("memberId");
		}
		if (StringUtil.isBlank(getAuthorizedSiteId())) {
			throw new ParamNotFoundExp("authorizedSiteId");
		}
		if (StringUtil.isBlank(getAccessToken())) {
			throw new ParamNotFoundExp("accessToken");
		}
	}

	@Override
	public String getSiteName() {
		return "获取授权Code";
	}

	public String getAuthorizedSiteId() {
		return authorizedSiteId;
	}

	public void setAuthorizedSiteId(String authorizedSiteId) {
		this.authorizedSiteId = authorizedSiteId;
	}

	public String getAccessToken() {
		return accessToken;
	}

	public void setAccessToken(String accessToken) {
		this.accessToken = accessToken;
	}

	public BussinessDao getBussinessDao() {
		return bussinessDao;
	}

	public void setBussinessDao(BussinessDao bussinessDao) {
		this.bussinessDao = bussinessDao;
	}
}
