<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ include file="/common/taglibs.jsp"%>
<!DOCTYPE HTML>
<html>
<head>
    <title>修改头像 - 顺网通行证</title>
    <link rel="stylesheet" href="${staticServer}/plugins/imgareaselect/css/imgareaselect-default.css"/>
    <link rel="stylesheet" href="${staticServer}/styles/avatar.css"/>
</head>
<body>
<div class="shop_head">
    <strong><a href="${appServer}/front/member/memberInfo_front.htm">修改资料</a></strong>&nbsp;&nbsp;
    <strong>修改头像</strong>
</div>
<div id="avatar" class="avatar-editor">
    <div class="hd">
        <p class="tip">请选择您要上传的图片，支持jpg、jpeg、png和gif格式</p>
    </div>
    <div class="bd group">
        <div class="editor">
            <form id="upload-form" action="${appServer}/front/upload/iframeBigHeadUpload.htm" method="post" enctype="multipart/form-data" target="upload-iframe">
                <input type="hidden" name="memberId" value="${member.memberId}" />
                <input type="hidden" name="avatarCallback" value="UploadHandler" />
                <input class="upload-input" type="file" name="avatar" id="upload"/>
                <span class="upload-icon"></span>
            </form>
            <iframe id="upload-iframe" name="upload-iframe" src="about:blank" width="0" height="0"></iframe>
            <div class="avatar native">
                <img src="${staticServer}/images/front/dot.png">
            </div>
            <div class="act">
                <a href="#!reupload" class="reupload">点击修改</a>
            </div>
        </div>
        <div class="result">
            <p class="tip">拖拽或缩放线框，生成满意的头像</p>
            <div class="avatar middle">
                <div class="preview">
                    <img src="${staticServer}/images/front/dot.png">
                </div>
                <span>120*120px</span>
            </div>
            <div class="avatar small">
                <div class="preview">
                    <img src="${staticServer}/images/front/dot.png">
                </div>
                <span>56*56px</span>
            </div>
            <div class="btn">
                <a href="javascript:;" class="btn_default_lg">生成头像</a>
            </div>
        </div>
    </div>
</div>
<!-- 提示 -->
<div id="msg-modal" tabindex="-1" role="dialog" aria-hidden="" data-delay="2000" class="modal hide fade alert">
    <div class="modal-dialog">
    </div>
</div>
<script src="${staticServer}/scripts/front/member/avatar.js"></script>
<script src="${staticServer}/plugins/imgareaselect/scripts/jquery.imgareaselect.pack.js"></script>
<script src="${staticServer}/scripts/common/bootstrap-modal.js"></script>
</body>
</html>
