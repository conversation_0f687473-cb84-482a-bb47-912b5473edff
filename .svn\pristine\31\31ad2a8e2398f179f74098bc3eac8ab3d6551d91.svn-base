package com.shunwang.baseStone.cache.keygenerator;

import com.shunwang.baseStone.cache.CacheKeyGenerator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.interceptor.KeyGenerator;

import java.lang.reflect.Method;
import java.util.Arrays;

/**
 * SiteInterface缓存key生成类
 * Spring @Cacheable 使用
 *
 * <AUTHOR>
 * @date 2019/3/7
 **/
public class SiteInterfaceKeyGenerator implements KeyGenerator {

    private final static Logger log = LoggerFactory.getLogger(SiteInterfaceKeyGenerator.class);

    @Override
    public Object generate(Object target, Method method, Object... params) {
        if (params.length != 2) {
            log.error("错误调用:{}", Arrays.toString(params));
            throw new IllegalArgumentException("错误调用");
        }
        String businessKey = (String) params[0];
        String serviceKey = (String) params[1];
        String siteInterfaceKey = CacheKeyGenerator.getSiteInterfaceKey(businessKey, serviceKey);
        log.debug("生成key: businessKey={}\tserviceKey:{}\tsiteInterfaceKey:{}",
                businessKey, serviceKey, siteInterfaceKey);
        return siteInterfaceKey;
    }
}
