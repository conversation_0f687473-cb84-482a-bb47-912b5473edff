<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd" >
<sqlMap namespace="com.shunwang.basepassport.binder.pojo.EmailBinder">
<typeAlias alias="emailBinder" type="com.shunwang.basepassport.binder.pojo.EmailBinder"/>
<resultMap class="com.shunwang.basepassport.binder.pojo.EmailBinder" id="BaseResultMap">
	<result column="member_id" property="memberId" jdbcType="int"/>
	<result column="email" property="number" jdbcType="varchar"/>
	<result column="email_active_no" property="activeNo" jdbcType="varchar"/>
	<result column="email_send_time" property="sendTime" jdbcType="datetime"/>
	<result column="first_email_active_time" property="firstActiveTime" jdbcType="datetime"/>
	<result column="send_email" property="sendNumber" jdbcType="varchar"/>

	<result column="email_coded" property="numberCoded" jdbcType="varchar"/>
	<result column="send_email_coded" property="sendNumberCoded" jdbcType="varchar"/>
</resultMap>
<select id="find" resultMap="BaseResultMap" parameterClass="com.shunwang.framework.ibatis.query.ConditionQuery" >
	SELECT
		t.member_id,
		t.email,
		t.email_active_no,
		t.email_send_time,
		t.first_email_active_time,
		t.send_email,
		t.email_coded,
		t.send_email_coded
	from personal_member_email t
	ORDER BY t.member_id DESC
	limit #firstResult#, #rp#
</select>
<insert id="insert" parameterClass="emailBinder" >
	insert into personal_member_email (
		member_id,
		email,
		email_active_no,
		email_send_time,
		first_email_active_time,
		send_email,
		email_coded,
		send_email_coded
		)
		values(
		#memberId:int#,
		#number:varchar#,
		#activeNo:varchar#,
		#sendTime:datetime#,
		#firstActiveTime:datetime#,
		#sendNumber:varchar#,
		#numberCoded:varchar#,
		#sendNumberCoded:varchar#
		)
</insert>
<update id="update" parameterClass="emailBinder" >
	update personal_member_email set
		member_id=member_id
		<isNotEmpty prepend="," property="number"> email = #number:VARCHAR# </isNotEmpty>
		<isNotEmpty prepend="," property="activeNo"> email_active_no = #activeNo:VARCHAR# </isNotEmpty>
		<isNotEmpty prepend="," property="sendTime"> email_send_time = #sendTime:DATETIME# </isNotEmpty>
		<isNotEmpty prepend="," property="firstActiveTime"> first_email_active_time = #firstActiveTime:DATETIME# </isNotEmpty>
		<isNotEmpty prepend="," property="sendNumber"> send_email = #sendNumber:VARCHAR# </isNotEmpty>
		<isNotEmpty prepend="," property="numberCoded"> email_coded = #numberCoded:VARCHAR# </isNotEmpty>
		<isNotEmpty prepend="," property="sendNumberCoded"> send_email_coded = #sendNumberCoded:VARCHAR# </isNotEmpty>
	where member_id = #memberId:int#
</update>
<delete id="delete" parameterClass="emailBinder" >
	delete from personal_member_email where member_id=#memberId:int#
</delete>
<select id="get" resultMap="BaseResultMap" >
	select
		member_id,
		email,
		email_active_no,
		email_send_time,
		first_email_active_time,
		send_email,
		email_coded,
		send_email_coded
	from personal_member_email
	where member_id = #value#
</select>
<select id="getBinderByNumber" resultMap="BaseResultMap" parameterClass="emailBinder">
	select
		member_id,
		email,
		email_active_no,
		email_send_time,
		first_email_active_time,
		send_email,
		email_coded,
		send_email_coded
	from personal_member_email
	<dynamic prepend="WHERE">
		<isParameterPresent>
			<isNotEmpty prepend="AND" property="number" >
				email = #number#
			</isNotEmpty>
			<isEmpty prepend="AND" property="number" >
				email_coded = #numberCoded:VARCHAR #
			</isEmpty>
		</isParameterPresent>
	</dynamic>
	limit 1
</select>
<select id="getRegitsterBinderByNumber" resultMap="BaseResultMap" parameterClass="emailBinder">
	select
		member_id,
		email,
		email_active_no,
		email_send_time,
		first_email_active_time,
		send_email,
		email_coded,
		send_email_coded
	from personal_member_email
	where member_id = -1
	<isNotEmpty prepend="and" property="sendNumber"> send_email = #sendNumber#</isNotEmpty>
	<isEmpty prepend="and" property="sendNumber"> send_email_coded = #sendNumberCoded#</isEmpty>
	limit 1
</select>
<select id="findCnt" parameterClass="com.shunwang.framework.ibatis.query.ConditionQuery" resultClass="java.lang.Integer" >
	select count(*) from personal_member_email t
</select>
</sqlMap>