<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ include file="/common/taglibs.jsp"%>
<!DOCTYPE html>
<html>
<head>
	<title>${member.isBindEmail?"换绑":"绑定"}邮箱</title>
</head>
<body>
<content tag="header_title">${member.isBindEmail?"换绑":"绑定"}邮箱</content>
<c:if test="${member.isBindEmail}">
	<div class="nav">
		<ul>
			<li class="active">选择验证方式</li>
			<li class="active">安全验证</li>
			<li>换绑邮箱</li>
		</ul>
	</div>
</c:if>
<div class="tips-error" style="display: none;">
	<p>${msg}</p>
</div>
<form action="${appServer}/front/swpaysdk/doBindEmail.htm" method="post" class="form-box">
	<div class="form-group inline-group">
		<input type="hidden" name="tokenid" value="${tokenid}" />
		<input type="hidden" name="source" value="${source}" />
		<input type="email" name="newNumber" placeholder="请输入要绑定的邮箱" class="form-control" value="${newNumber}">
		<button type="button" id="sent-code" class="btn btn-primary btn-mini" id="sent-code">发送验证码</button></div>
	<div class="form-group"><input type="tel" name="activeNo" placeholder="请输入邮箱邮件中的验证码" class="form-control"></div>
	<div class="other-group btn-box"><button type="submit" id="verity" class="btn btn-primary">确认绑定</button></div>
</form>
<content tag="scripts">	<script type="text/javascript" src="${staticServer}/scripts/front/swpaysdk/member/email.js"></script></content>
</body>

</html>