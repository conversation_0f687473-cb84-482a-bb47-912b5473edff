package com.shunwang.baseStone.service.pojo;

import junit.framework.TestCase;

import com.shunwang.baseStone.context.BaseStoneContext;
import com.shunwang.baseStone.service.dao.ServiceDao;

public class ServiceTest extends TestCase {
	public void testOpen(){
		BaseStoneContext context = BaseStoneContext.getInstance();
		context.setFileName("/baseStoneContext.xml");
		ServiceDao dao=(ServiceDao)BaseStoneContext.getInstance().getBean("serviceDao");
		Service bs = dao.getById("西游记网站");
		bs.open();
	}
	
	public void testClose(){
		BaseStoneContext context = BaseStoneContext.getInstance();
		context.setFileName("/baseStoneContext.xml");
		ServiceDao dao=(ServiceDao)BaseStoneContext.getInstance().getBean("serviceDao");
		Service bs = dao.getById("西游记网站");
		bs.close();
	}
}
