package com.shunwang.basepassport.user.dao;

import com.shunwang.baseStone.core.detail.HasDetailDao;
import com.shunwang.basepassport.user.pojo.MemberMultipleAccountBind;

public class MemberMultipleAccountBindDao extends HasDetailDao<MemberMultipleAccountBind> {

	public MemberMultipleAccountBind getByIdCard(Integer idcard) {
		return (MemberMultipleAccountBind) getSqlMapClientTemplate().queryForObject(
				getStatementNameWrap("getByIdCard"), idcard);
	}

	public MemberMultipleAccountBind getByMemberId(Integer memberId) {
		return (MemberMultipleAccountBind) getSqlMapClientTemplate().queryForObject(
				getStatementNameWrap("getByMemberId"), memberId);
	}

}
