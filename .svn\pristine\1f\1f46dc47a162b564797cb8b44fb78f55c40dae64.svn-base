<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE struts PUBLIC "-//Apache Software Foundation//DTD Struts Configuration 2.0//EN" "http://struts.apache.org/dtds/struts-2.0.dtd">

<struts>
	<package name="headImage" namespace="/front/member" extends="passport">
		<action name="isLoginHeadImg" method="isLogin" class="webHeadImageAction">
		</action>
	    <!-- 选择系统头像 -->
		<action name="chooseFace_front" method="chooseFace" class="webHeadImageAction">
			<result name="success">/front/member/headImage_front.jsp</result>
			<result name="input">/front/member/headImage_front.jsp</result>
			<result name="error">/front/member/headImage_front.jsp</result>
		</action>
        
		<!-- 去图像上传页面 -->
		<action name="toHeadImage_front" method="toHeadImage" class="webHeadImageAction">
			<result name="success">/front/member/headImage_front.jsp</result>
			<result name="input">/front/member/headImage_front.jsp</result>
			<result name="error">/front/member/headImage_front.jsp</result>
		</action>

        <action name="headImgGen" method="iframeUploadHeadImgGen" class="smallHeadImageUploadAction"></action>
	</package>
	<package name="headImage2" namespace="/front/member" extends="passport">
	    <action name="headImage_front" method="getBigHeadImageUrl" class="webHeadImageAction">
	    </action>
	    <action name="smallHeadImage_front" method="getSmallHeadImageUrl" class="webHeadImageAction">
	    </action>
	</package>
	
	<package  name="headUpload" extends="struts-default">
        <default-interceptor-ref name="fileUploadStack"></default-interceptor-ref>
        <action name="swfUpload" class="com.shunwang.passport.member.web.SmallHeadImageUploadAction" >
        </action>
    </package>
    
</struts>