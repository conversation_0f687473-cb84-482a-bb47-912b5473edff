<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd" >
<sqlMap namespace="com.shunwang.baseStone.useroutinterface.pojo.UseroutInterface">
<resultMap class="com.shunwang.baseStone.useroutinterface.pojo.UseroutInterface" id="BaseResultMap">
	<result property="inerfaceId" column="inerfaceId" jdbcType="int"/>	
	<result property="outServiceId" column="outServiceId" jdbcType="varchar"/>
	<result property="serviceURL" column="serviceURL" jdbcType="varchar"/>
	<result property="callbackURL" column="callbackURL" jdbcType="varchar"/>
	<result property="serviceKey" column="serviceKey" jdbcType="varchar"/>
	<result property="serviceState" column="serviceState" jdbcType="varchar"/>
	<result property="permitIp" column="permitIp" jdbcType="varchar"/>
	<result property="timeAdd" column="timeAdd" jdbcType="datetime"/>
	<result property="userAdd" column="userAdd" jdbcType="varchar"/>
	<result property="timeEdit" column="timeEdit" jdbcType="datetime"/>
	<result property="userEdit" column="userEdit" jdbcType="varchar"/>
	<result property="remark" column="remark" jdbcType="varchar"/>
	<result property="prefixName" column="prefixName" jdbcType="varchar"/>
	<result property="serviceProvider" column="serviceProvider" jdbcType="varchar"/>
	<result property="serviceImg" column="serviceImg" jdbcType="varchar"/>
	<result property="serviceBigImg" column="serviceBigImg" jdbcType="varchar"/>
	<result property="dirId" column="dirId" jdbcType="int"/>
	<result property="orderBy" column="orderBy" jdbcType="int"/>
	<result property="autoLogin" column="autoLogin" jdbcType="tinyint"/>
</resultMap>
<select id="find" resultMap="BaseResultMap" parameterClass="com.shunwang.framework.ibatis.query.ConditionQuery" >
	SELECT
		t.inerfaceId,
		t.outServiceId,
		t.serviceURL,
		t.callbackURL,
		t.serviceKey,
		t.serviceState,
		t.permitIp,
		t.timeAdd,
		t.userAdd,
		t.timeEdit,
		t.userEdit,
		t.remark,
		t.prefixName,
		t.serviceImg,
		t.serviceBigImg,
		t.dirId,
		t.orderBy,
		t.serviceProvider,
		t.autoLogin from config_useroutInterface t
	<isParameterPresent >
	<include refid="Example_Where_Clause" />
	</isParameterPresent>
	order by
	<isNotNull property="orderCol" >
		$orderCol$
	</isNotNull>
	<isNull property="orderCol" >
		t.inerfaceId desc 
	</isNull>
	<isNotEqual property="rp" compareValue="0" >
	    limit #firstResult#, #rp#
	</isNotEqual>
</select>
<select id="findByDirId" resultMap="BaseResultMap" parameterClass="java.lang.Integer" >
	select 
	t.inerfaceId,
	t.outServiceId,
	t.serviceURL,
	t.callbackURL,
	t.serviceKey,
	t.serviceState,
	t.permitIp,
	t.timeAdd,
	t.userAdd,
	t.timeEdit,
	t.userEdit,
	t.remark,
	t.prefixName,
	t.serviceImg,
	t.serviceBigImg,
	t.dirId,
	t.orderBy,
	t.serviceProvider,
	t.autoLogin
	from config_useroutInterface t
	WHERE t.serviceState = 0 and t.dirId = #dirId#
	order by t.orderBy
</select>
<insert id="insert" parameterClass="com.shunwang.baseStone.useroutinterface.pojo.UseroutInterface" >
	insert into config_useroutInterface (
		inerfaceId,
		outServiceId,
		serviceURL,
		callbackURL,
		serviceKey,
		serviceState,
		permitIp,
		timeAdd,
		userAdd,
		timeEdit,
		userEdit,
		remark,
		prefixName,
		serviceProvider,
		autoLogin)
		values (
		#inerfaceId:int#,
		#outServiceId:varchar#,
		#serviceURL:varchar#,
		#callbackURL:varchar#,
		#serviceKey:varchar#,
		#serviceState:varchar#,
		#permitIp:varchar#,
		#timeAdd:datetime#,
		#userAdd:varchar#,
		#timeEdit:datetime#,
		#userEdit:varchar#,
		#remark:varchar#,
		#prefixName:varchar#,
		#serviceProvider:varchar#,
		#autoLogin:INTEGER#)
</insert>
<update id="update" parameterClass="com.shunwang.baseStone.useroutinterface.pojo.UseroutInterface" >
	update config_useroutInterface set
		inerfaceId=#inerfaceId:int#,
		outServiceId=#outServiceId:varchar#,
		serviceURL=#serviceURL:varchar#,
		callbackURL=#callbackURL:varchar#,
		serviceKey=#serviceKey:varchar#,
		serviceState=#serviceState:varchar#,
		permitIp=#permitIp:varchar#,
		timeAdd=#timeAdd:datetime#,
		userAdd=#userAdd:varchar#,
		timeEdit=#timeEdit:datetime#,
		userEdit=#userEdit:varchar#,
		remark=#remark:varchar#,
		prefixName=#prefixName:varchar#,
		serviceProvider=#serviceProvider:varchar#,
		autoLogin=#autoLogin:INTEGER# where inerfaceId = #inerfaceId:int#
</update>
<delete id="delete" parameterClass="com.shunwang.baseStone.useroutinterface.pojo.UseroutInterface" >
	delete from config_useroutInterface where inerfaceId=#inerfaceId:int#
</delete>
<select id="get" resultMap="BaseResultMap" parameterClass="java.lang.String">
	select
	inerfaceId,
	outServiceId,
	serviceURL,
	callbackURL,
	serviceKey,
	serviceState,
	permitIp,
	timeAdd,
	userAdd,
	timeEdit,
	userEdit,
	remark,
	prefixName,
	serviceImg,
	serviceBigImg,
	dirId,
	orderBy,
	serviceProvider,
	autoLogin from config_useroutInterface
	where inerfaceId = #value#
</select>
<select id="findAll" resultMap="BaseResultMap">
	select
	inerfaceId,
	outServiceId,
	serviceURL,
	callbackURL,
	serviceKey,
	serviceState,
	permitIp,
	timeAdd,
	userAdd,
	timeEdit,
	userEdit,
	remark,
	prefixName,
	serviceImg,
	serviceBigImg,
	dirId,
	orderBy,
	serviceProvider,
	autoLogin from config_useroutInterface
</select>
<select id="getPrefixNameList" resultClass="java.lang.String">
	select
	prefixName	from config_useroutInterface
	where serviceState = 0
</select>
<select id="findCnt" parameterClass="com.shunwang.framework.ibatis.query.ConditionQuery" resultClass="java.lang.Integer" >
	select count(*) from config_useroutInterface t
	<include refid="Example_Where_Clause" />
</select>
</sqlMap>