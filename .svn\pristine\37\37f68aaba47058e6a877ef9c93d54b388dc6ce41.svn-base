package com.shunwang.basepassport.oa;

import com.shunwang.baseStone.core.util.SignTool;
import com.shunwang.util.date.DateUtil;
import com.shunwang.util.encrypt.Md5Encrypt;
import com.shunwang.util.json.GsonUtil;
import com.shunwang.util.net.HttpClientUtils;
import org.junit.Test;
import java.nio.charset.Charset;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class OaGetUserTest {

    private String url = "http://interface.kedou.com/front/interface/oaGetUser.htm";

    private Map<String, String> header = new HashMap<>();
    {
        header.put("Accept","application/json");
    }

	public void test(int type, Map header){
        String md5Key = "123456";
        Map<String, String> param = new HashMap<String, String>();
        param.put("siteId", "Passport");
        param.put("time", DateUtil.getCurrentDateStamp());
        if (type == 1) {
            param.put("email", "<EMAIL>");
        }
        if (type == 2) {
            param.put("idCard", "330321199901105333");
        }
        param.put("signVersion", "1.0");
        String signSource = SignTool.buildSignStringSorted(param, "sign", md5Key);
        String sign = Md5Encrypt.encrypt(signSource).toUpperCase();
        param.put("sign", sign);

        String respond = HttpClientUtils.doPost(url, param, header, null, Charset.defaultCharset());
        BaseStoneResponse responses = GsonUtil.fromJson(respond, BaseStoneResponse.class);
        GetUserData data = GsonUtil.fromJson((String)responses.getItems().get(0), GetUserData.class);
        System.out.println(data.getRealName());
	}

    @Test
    public void testEmail() {
        test(1,header);
    }

    @Test
    public void testIdcard() {
        test(2,header);
    }

    class BaseStoneResponse {
        protected List items;

        protected String msgId = "0";

        protected String msg = "操作成功";

        public String getMsgId() {
            return msgId;
        }

        public void setMsgId(String msgId) {
            this.msgId = msgId;
        }

        public String getMsg() {
            return msg;
        }

        public void setMsg(String msg) {
            this.msg = msg;
        }

        public List getItems() {
            return items;
        }

        public void setItems(List items) {
            this.items = items;
        }

        protected boolean isSuccess() {
            return "0".equals(msgId);
        }
    }

    class GetUserData {
        private String realName;
        private String mobile;
        private boolean isDel;
        private String delMsg;
        private Integer status;
        private String statusMsg;

        public String getRealName() {
            return realName;
        }

        public void setRealName(String realName) {
            this.realName = realName;
        }

        public String getMobile() {
            return mobile;
        }

        public void setMobile(String mobile) {
            this.mobile = mobile;
        }

        public boolean isDel() {
            return isDel;
        }

        public void setDel(boolean del) {
            isDel = del;
        }

        public String getDelMsg() {
            return delMsg;
        }

        public void setDelMsg(String delMsg) {
            this.delMsg = delMsg;
        }

        public Integer getStatus() {
            return status;
        }

        public void setStatus(Integer status) {
            this.status = status;
        }

        public String getStatusMsg() {
            return statusMsg;
        }

        public void setStatusMsg(String statusMsg) {
            this.statusMsg = statusMsg;
        }
    }
}
