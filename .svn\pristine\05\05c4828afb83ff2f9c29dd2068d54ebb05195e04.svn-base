package com.shunwang.basepassport.manager.service.netbar;

import com.shunwang.basepassport.config.dao.ConfigInterfaceDao;
import com.shunwang.basepassport.config.pojo.ConfigInterface;
import com.shunwang.basepassport.manager.HttpMethod;
import com.shunwang.basepassport.manager.request.netbar.BaseUserGeneralOrderRequest;
import com.shunwang.basepassport.manager.response.netbar.BaseUserGeneralOrderResponse;
import com.shunwang.util.net.throwing.HttpClientUtils;
import org.apache.http.client.config.RequestConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.lang.reflect.Constructor;
import java.nio.charset.StandardCharsets;
import java.util.Map;

public class UserGeneralOrderServiceClient {

    private static final Logger LOGGER = LoggerFactory.getLogger(UserGeneralOrderServiceClient.class);

    private static ConfigInterfaceDao configInterfaceDao;

    private static RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(500).setConnectTimeout(100).setConnectionRequestTimeout(100).build();

    public void setConfigInterfaceDao(ConfigInterfaceDao configInterfaceDao) {
        UserGeneralOrderServiceClient.configInterfaceDao = configInterfaceDao;
    }

    public static <T extends BaseUserGeneralOrderResponse> T execute(BaseUserGeneralOrderRequest<T> request) {
        try {
            ConfigInterface setting = configInterfaceDao.findByInterfaceKey(request.getInterfaceKey());
            request.doInterfaceSetting(setting);
            String response;
            String url = request.buildUrl();
            Map<String, String> requestParams = request.buildParams();
            Map<String, String> headers = request.getHeaders();
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("----------------------------------------");
                LOGGER.debug("url:{}", url);
                LOGGER.debug("method:{}", request.getHttpMethod());
                LOGGER.debug("requestParams:{}", requestParams);
                LOGGER.debug("headers:{}", headers);
                LOGGER.debug("----------------------------------------");
            }

            if (request.getHttpMethod() == HttpMethod.GET) {
                response = HttpClientUtils.doGet(url, requestParams, headers, requestConfig, StandardCharsets.UTF_8);
            } else {
                response = HttpClientUtils.doPost(url, requestParams, headers, requestConfig, StandardCharsets.UTF_8);
            }

            Class<T> responseClass = request.getResponseClass();
            Constructor<T> constructor = responseClass.getConstructor();
            T resp = constructor.newInstance();
            resp.setRawJson(response);
            return (T) resp.parse();
        } catch (IOException e) {
            LOGGER.error("请求计费开机接口网络异常[" + e.getMessage() + "]", e);
            return null;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

}
