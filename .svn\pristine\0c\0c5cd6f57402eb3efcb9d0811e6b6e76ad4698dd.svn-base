<%@ page contentType="text/html; charset=UTF-8"%>
<%@ include file="../common/taglibs.jsp" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信开机授权</title>
    <style>
        .login-container {
            padding: 85px 23px;
            font-size: 0;
            text-align: center;
        }

        .login-container img {
            width: 60px;
            height: 60px;
        }

        .login-container h5 {
            margin: 8px 0 100px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 16px;
            color: #000000;
            line-height: 22px;
        }

        .login-container p {
            margin: 15px 0;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: #2D4A71;
            line-height: 20px;
        }

        .login-container a {
            display: inline-block;
            text-decoration: none;
            width: 100%;
            height: 45px;
            color: #FFFFFF;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 16px;
            line-height: 45px;
            background: #50A937;
            border-radius: 4px;
            border: 1px solid#50A937;
        }
    </style>
</head>
<body>

<c:if test="${not empty serviceUrl && serviceUrl!='' && serviceUrl!='null'}">
    <div class="login-container">
        <img src="${appImg}" alt="">
        <h5>${appName}</h5>
        <p>请完成微信授权以继续使用</p>
        <a href="${serviceUrl}">同意微信授权</a>
    </div>
</c:if>
<c:if test="${empty serviceUrl || serviceUrl=='' || serviceUrl=='null'}">
    <c:if test="${not empty msg && msg ne '' && msg ne 'null'}">
        <br/>
        <span>错误：${msg}</span>
    </c:if>
</c:if>
</body>