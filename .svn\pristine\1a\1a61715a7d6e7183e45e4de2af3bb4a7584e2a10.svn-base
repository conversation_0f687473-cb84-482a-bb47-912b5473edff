package com.shunwang.basepassport.config.common;

import com.shunwang.baseStone.context.BaseStoneContext;
import com.shunwang.baseStone.siteinterface.context.SiteContext;
import com.shunwang.basepassport.config.dao.SiteInterfaceDao;
import com.shunwang.basepassport.config.pojo.SiteInterface;

/**
 * @author: lj.zeng
 * @create: 2024-03-18 10:48:57
 * @Description:
 */
public class SiteInterfaceUtil {

    private static SiteInterfaceDao siteInterfaceDao;

    public static SiteInterfaceDao getSiteInterfaceDao() {
        if (siteInterfaceDao == null) {
            siteInterfaceDao = BaseStoneContext.getInstance().getBean(SiteInterfaceDao.class);
        }
        return siteInterfaceDao;
    }

    public static SiteInterface loadSiteInterface(String appId, String serviceKey) {
        return getSiteInterfaceDao().getByAppIdAndServiceKey(appId, serviceKey);
    }

    public static SiteInterface loadOpenOneByAppId(String appId) {
        return getSiteInterfaceDao().getOpenOneByAppId(appId);
    }

    public static SiteInterface loadSiteInterface() {
        return loadSiteInterface(SiteContext.getSiteId(), SiteContext.getSiteName());
    }
}
