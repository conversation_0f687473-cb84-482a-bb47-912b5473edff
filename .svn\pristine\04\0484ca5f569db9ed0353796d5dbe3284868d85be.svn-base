<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">
	

    <bean id="actuAction" class="com.shunwang.basepassport.actu.web.ActuAction"  scope= "prototype">
		<property name="userBankCardDao" ref="userBankCardDao"/>
	</bean>
	<bean id="outActuAction" class="com.shunwang.basepassport.actu.web.OutActuAction" scope="prototype"></bean>
	<bean id="idCardActuQueryAction" class="com.shunwang.basepassport.actu.web.IdCardActuQueryAction" scope="prototype">
		<property name="personalActuVerifyRecordDao" ref="personalActuVerifyRecordDao"/>
	</bean>
	<bean id="actuChangeAction" class="com.shunwang.basepassport.actu.web.outActuChangeAction" abstract="true"></bean>
	<bean id="outCafeActuChangeAction" class="com.shunwang.basepassport.actu.web.outCafeActuChangeAction" scope="prototype" parent="actuChangeAction">
		<property name="actuChangeDao" ref="cafeActuChangeDao" />
	</bean>

	<bean id="cafeActuChangeAction" class="com.shunwang.basepassport.actu.web.cafeActuChangeAction" scope="prototype">
		<property name="cafeActuChangeDao" ref="cafeActuChangeDao" />
	</bean>

	<bean id="actuReportCallbackAction" class="com.shunwang.basepassport.actu.web.ActuReportCallbackAction" scope="prototype">
		<property name="personalActuReportDao" ref="personalActuReportDao"/>
		<property name="cafeActuDao" ref="cafeActuDao"/>
		<property name="userBankCardDao" ref="userBankCardDao"/>
		<property name="swpayInterfaceBo" ref="swpayInterfaceBo"/>
		<property name="memberAccountBindDao" ref="memberAccountBindDao"/>
		<property name="cafeActuChangeDao" ref="cafeActuChangeDao"/>
		<property name="memberDao" ref="memberDao"/>
		<property name="netbarInterfaceBo" ref="netbarInterfaceBo"/>
		<property name="configResourcesDao" ref="configResourcesDao"/>
	</bean>

	<bean id="actuReportQueryAction" class="com.shunwang.basepassport.actu.web.ActuReportQueryAction" scope="prototype">
		<property name="personalActuReportDao" ref="personalActuReportDao"/>
	</bean>

    <bean id="configAction" class="com.shunwang.basepassport.config.ConfigAction" scope="prototype">
        <property name="bankDao">
            <ref bean="bankDao"/>
        </property>
    </bean>
	<bean id="emailSendAction" class="com.shunwang.basepassport.binder.web.EmailSendAction" scope= "prototype">
	</bean>
	  
	<bean id="emailBindAction" class="com.shunwang.basepassport.binder.web.EmailBindAction" scope= "prototype">
	</bean>
	
	<bean id="mobileCheckCodeAction" class="com.shunwang.basepassport.mobile.web.MobileCheckCodeAction" scope= "prototype">
	</bean>
	
	<bean id="sendSmsCheckCodeAction" class="com.shunwang.basepassport.mobile.web.SendSmsCheckCodeAction" scope= "prototype">
	</bean>
	
	<bean id="mobileBindAsLoginAction" class="com.shunwang.basepassport.binder.web.MobileBindAsLoginAction" scope= "prototype">
	</bean>
	
	<bean id="mobileUnBindAsLoginAction" class="com.shunwang.basepassport.binder.web.MobileUnBindAsLoginAction" scope= "prototype">
		<property name="memberAccountBindDao" ref="memberAccountBindDao" />
	</bean>

	<bean id="emailBindAsLoginAction" class="com.shunwang.basepassport.binder.web.EmailBindAsLoginAction" scope= "prototype">
	</bean>

	<bean id="emailUnBindAsLoginAction" class="com.shunwang.basepassport.binder.web.EmailUnBindAsLoginAction" scope= "prototype">
	</bean>

	<bean id="singleAccountAction" class="com.shunwang.basepassport.binder.web.SingleAccountAction" abstract="true">
		<property name="bussinessDao" ref="bussinessDao" />
		<property name="memberAccountBindDao" ref="memberAccountBindDao" />
	</bean>

	<bean id="singleAccountQueryAction" class="com.shunwang.basepassport.binder.web.SingleAccountQueryAction" scope="prototype" >
		<property name="memberOutSiteDao" ref="memberOutSiteDao" />
		<property name="bussinessDao" ref="bussinessDao" />
		<property name="memberAccountBindDao" ref="memberAccountBindDao" />
	</bean>

	<bean id="singleAccountBindAction" class="com.shunwang.basepassport.binder.web.SingleAccountBindAction" scope="prototype" parent="singleAccountAction">
		<property name="memberOutSiteDao" ref="memberOutSiteDao" />
	</bean>

    <bean id="singleAccountBindExtAction" class="com.shunwang.basepassport.binder.web.SingleAccountBindExtAction" scope="prototype" parent="singleAccountAction">
		<property name="memberOutSiteDao" ref="memberOutSiteDao" />
    </bean>

	<bean id="singleAccountUnbindAction" class="com.shunwang.basepassport.binder.web.SingleAccountUnbindAction" scope="prototype" parent="singleAccountAction">
	</bean>
	 
	<bean id="shunLingBinderAction" class="com.shunwang.basepassport.binder.web.ShunLingBinderAction" scope= "prototype">
	</bean>
	<bean id="shunLingUnbinderAction" class="com.shunwang.basepassport.binder.web.ShunLingUnbinderAction" scope= "prototype">
	</bean>
	    
	<bean id="regActNoSendAction" class="com.shunwang.basepassport.mobile.web.RegActNoSendAction" scope= "prototype">
	</bean>
	
	<bean id="regActNoVerifyAction" class="com.shunwang.basepassport.mobile.web.RegActNoVerifyAction" scope= "prototype">
	</bean>
	
	<bean id="checkUniqueAction" class="com.shunwang.basepassport.user.web.CheckUniqueAction" scope= "prototype">	  	 
	</bean>
	
	<bean id="updatePwdAction" class="com.shunwang.basepassport.user.web.UpdatePwdAction" scope= "prototype">	  	 
	</bean>

	<bean id="shortUrlAction" class="com.shunwang.basepassport.shorturl.web.ShortUrlAction" scope= "prototype">

	</bean>

	<bean id="moibleThirdOutLoginAction" class="com.shunwang.basepassport.user.web.MoibleThirdOutLoginAction" scope= "prototype">
	
	</bean>
	
	<bean id="getTicketActon" class="com.shunwang.basepassport.user.web.GetTicketActon" scope= "prototype"/>
	<bean id="realnamePassportQueryAction" class="com.shunwang.basepassport.actu.web.RealnamePassportQueryAction" scope= "prototype">
		<property name="memberDao" ref="memberDao"></property>
	</bean>
	<bean id="memberQueryAction" class="com.shunwang.basepassport.user.web.MemberQueryAction" scope= "prototype"></bean>
	
	<!--<bean id="registerAction" class="com.shunwang.basepassport.user.web.RegisterAction" scope= "prototype"></bean>-->
	<bean id="kedouBarUserRegisterAction" class="com.shunwang.basepassport.user.web.KedouBarUserRegisterAction" scope= "prototype"></bean>
	
	<bean id="tmpRegisterAction" class="com.shunwang.basepassport.user.web.TmpRegisterAction" scope= "prototype"></bean>
		  	 
	<bean id="updateMemberAction" class="com.shunwang.basepassport.user.web.UpdateMemberAction" scope= "prototype">
		<property name="idCardVerifyBo" ref="idCardVerifyBo"/>
		<property name="bussinessDao" ref="bussinessDao" />
	</bean>
	
	<bean id="checkCurrentDayLogonAction" class="com.shunwang.basepassport.user.web.CheckCurrentDayLogonAction" scope= "prototype"></bean>
	<bean id="memberQueryFullAction" class="com.shunwang.basepassport.user.web.MemberQueryFullAction" scope="prototype"></bean>
	<bean id="memberQueryProvideToMobileAction" class="com.shunwang.basepassport.user.web.MemberQueryProvideToMobileAction" scope="prototype"></bean>
	<bean id="memberQueryByOutUserAction" class="com.shunwang.basepassport.user.web.MemberQueryByOutUserAction" scope="prototype"></bean>

	<bean id="outSiteMemberRegisterAction" class="com.shunwang.basepassport.user.web.OutSiteMemberRegisterAction" scope="prototype">
        <property name="memberOutSiteDao" ref="memberOutSiteDao" />
    </bean>
	<bean id="memberOutSiteAction" class="com.shunwang.basepassport.user.web.MemberOutSiteAction" scope="prototype">
        <property name="memberOutSiteDao" ref="memberOutSiteDao" />
    </bean>
	<bean id="updateMemberExtAction" class="com.shunwang.basepassport.user.web.UpdateMemberExtAction" scope="prototype"/>

	<bean id="memberOutSiteQueryAction" class="com.shunwang.basepassport.user.web.MemberOutSiteQueryAction"
		  scope="prototype">
		<property name="memberOutSiteDao" ref="memberOutSiteDao" />
	</bean>

	<bean id="headImageAction" class="com.shunwang.basepassport.user.web.HeadImageAction" scope= "prototype"></bean>
	<bean id="headImageModifyAction"
          class="com.shunwang.basepassport.user.web.HeadImageModifyAction" scope="prototype">
        <property name="headImgMaxSize" value="${headImgMaxSize}"/>
    </bean>

    <bean id="timeSyncAction" class="com.shunwang.basepassport.user.web.TimeSyncAction"
          scope="prototype"></bean>

    <bean id="logonlogQueryAction" class="com.shunwang.basepassport.user.web.LogonlogQueryAction"
          scope="prototype">
        <property name="logonLogDao">
            <ref bean="logonLogDao"/>
        </property>
    </bean>

    <bean id="sendDynamicPasswordInnerAction" class="com.shunwang.basepassport.user.web.SendDynamicPasswordInnerAction" scope="prototype"/>
    <bean id="dynamicSecurityValidateInnerAction" class="com.shunwang.basepassport.user.web.DynamicSecurityValidateInnerAction" scope="prototype"/>

    <bean id="sendDynamicPasswordAction"
          class="com.shunwang.basepassport.user.web.SendDynamicPasswordAction" scope="prototype">
    </bean>

    <bean id="dynamicSecurityValidateAction"
          class="com.shunwang.basepassport.user.web.DynamicSecurityValidateAction"
          scope="prototype">
        <property name="effectiveTime" value="${effectiveTime}"/>
        <property name="dynamicMd5Key" value="${dynamicMd5Key}"/>
        <property name="dynamicAesKey" value="${dynamicAesKey}"/>
    </bean>

	<bean id="mobileCheckCodeUpdateAction" class="com.shunwang.basepassport.user.web.MobileCheckCodeUpdateAction"/>
    <!--<bean id="getOutOauthDirAction"-->
          <!--class="com.shunwang.basepassport.user.web.GetOutOauthDirAction"-->
          <!--scope="prototype">-->
    <!--</bean>-->

	<bean id="getActualRegConfig" class="com.shunwang.basepassport.user.web.GetActualRegConfigAction" scope="prototype" >
		<property name="loginElementService" ref="loginElementService"/>
	</bean>

    <bean id="verifyLoginStatusAction"
          class="com.shunwang.basepassport.user.web.VerifyLoginStatusAction" scope="prototype">
    </bean>

    <bean id="refreshTokenAction" class="com.shunwang.basepassport.user.web.RefreshTokenAction"
          scope="prototype">
    </bean>

    <bean id="verifyTokenAction" class="com.shunwang.basepassport.user.web.VerifyTokenAction"
          scope="prototype">
    </bean>

    <bean id="SDKVerifyTokenAction" class="com.shunwang.basepassport.user.web.SDKVerifyTokenAction"  parent="verifyTokenAction"
          scope="prototype"/>


    <bean id="memberLogoutAction" class="com.shunwang.basepassport.user.web.MemberLogoutAction"
          scope="prototype">
    </bean>

    <bean id="blankAction" class="com.shunwang.basepassport.user.web.BlankAction" scope="prototype">
    </bean>

    <bean id="customServiceAction" class="com.shunwang.basepassport.user.web.CustomServiceAction" scope="prototype"></bean>

	<bean id="logCollectAction" class="com.shunwang.basepassport.user.web.LogCollectAction" scope="prototype"></bean>

	<bean id="authorizedCodeAction" class="com.shunwang.basepassport.user.web.AuthorizedCodeAction" scope="prototype">
		<property name="bussinessDao" ref="bussinessDao" />
	</bean>

	<bean id="authorizedTokenAction" class="com.shunwang.basepassport.user.web.AuthorizedTokenAction" scope="prototype"></bean>

	<bean id="accessTokenAction" class="com.shunwang.basepassport.user.web.AccessTokenAction" scope="prototype"></bean>
	<bean id="idcardBindAction" class="com.shunwang.basepassport.user.web.IdcardBindAction" scope="prototype">
		<property name="idcardBindDao" ref="idcardBindDao" />
		<property name="memberDao" ref="memberDao" />
	</bean>
	<bean id="idcardUnbindAction" class="com.shunwang.basepassport.user.web.IdcardUnbindAction" scope="prototype">
		<property name="idcardBindDao" ref="idcardBindDao" />
	</bean>
	<bean id="idcardBindQueryAction" class="com.shunwang.basepassport.user.web.IdcardBindQueryAction" scope="prototype">
		<property name="idcardBindDao" ref="idcardBindDao" />
	</bean>

	<bean id="mobileSendAction" class="com.shunwang.basepassport.mobile.web.MobileSendAction" scope= "prototype">
		<property name="mobileCheckCodeDao" ref="mobileCheckCodeDao"/>
		<property name="interfaceService" ref="interfaceService"/>
		<property name="smsConfigDao" ref="smsConfigDao"/>
		<property name="bussinessDao" ref="bussinessDao"/>
		<property name="categoryDao" ref="categoryDao"/>
	</bean>
	<bean id="emailSendAction2" class="com.shunwang.basepassport.mobile.web.EmailSendAction" scope= "prototype">
		<property name="emailCheckCodeDao" ref="emailCheckCodeDao"/>
		<property name="emailConfigDao" ref="emailConfigDao"/>
	</bean>
	<bean id="mobileValidateAction" class="com.shunwang.basepassport.mobile.web.MobileValidateAction" scope="prototype"/>
	<bean id="emailValidateAction" class="com.shunwang.basepassport.mobile.web.EmailValidateAction" scope="prototype">
		<property name="emailCheckCodeDao" ref="emailCheckCodeDao"/>
		<property name="emailConfigDao" ref="emailConfigDao"/>
 	</bean>

    <bean id="undoCancelAction" class="com.shunwang.basepassport.user.web.UndoCancelAction" scope="prototype">
			<property name="serviceNotifyDao" ref="serviceNotifyDao"/>
	</bean>


	<bean id="agreementQueryAction" class="com.shunwang.basepassport.config.AgreementQueryAction" scope="prototype"/>

    <!-- risk begin -->
    <bean id="checkRiskAction" class="com.shunwang.basepassport.risk.web.CheckRiskAction" scope="prototype"/>
    <!-- risk end -->

    <!-- oa begin -->
    <bean id="oaGetUserAction" class="com.shunwang.basepassport.oa.web.OaGetUserAction" scope="prototype"/>
    <!-- oa end -->

	<bean id="gameActuQueryAction" class="com.shunwang.basepassport.user.web.GameActuQueryAction"  scope= "prototype">
		<property name="gameIdCardVerifyBo" ref="gameIdCardVerifyBo"/>
		<property name="memberDao" ref="memberDao"/>
	</bean>

	<bean id="gameActuAction" class="com.shunwang.basepassport.user.web.GameActuAction"  scope= "prototype">
		<property name="gameIdCardVerifyBo" ref="gameIdCardVerifyBo"/>
		<property name="memberDao" ref="memberDao"/>
		<property name="redisOperation" ref="redisOperation"/>
	</bean>

	<bean id="gamePeriodQueryAction" class="com.shunwang.basepassport.user.web.GamePeriodQueryAction"  scope= "prototype">
	</bean>
	<bean id="ipLocationAction" class="com.shunwang.basepassport.iplocation.web.IpLocationAction" scope= "prototype">
	</bean>

	<bean class="com.shunwang.basepassport.iplocation.util.IPSeeker" init-method="initIpSeeker">
		<property name="file" value="${ip.location.file.path}"></property>
	</bean>

	<bean id="accessTokenQueryAction" class="com.shunwang.basepassport.weixin.web.AccessTokenQueryAction"  scope= "prototype">
		<property name="weixinOauthTokenService" ref="weixinOauthTokenService"/>
	</bean>

	<bean id="sendWxTemplateMsgAction" class="com.shunwang.basepassport.weixin.web.SendWxTemplateMsgAction"  scope= "prototype">
		<property name="weixinMsgService" ref="weixinMsgService"/>
	</bean>

	<bean id="queryUserByBindMobileAction" class="com.shunwang.basepassport.user.web.QueryUserByBindMobileAction" scope="prototype"/>

	<bean id="disburseSuccessCallbackAction"
		  class="com.shunwang.basepassport.weixin.web.DisburseSuccessCallbackAction" scope="prototype">
		<property name="weixinMsgService" ref="weixinMsgService"/>
		<property name="weixinOpenIdUnionIdService" ref="weixinOpenIdUnionIdService"/>
		<property name="wxTemplateMsgDao" ref="wxTemplateMsgDao"/>
		<property name="bigDataReportService" ref="bigDataReportService"/>
	</bean>
</beans>
