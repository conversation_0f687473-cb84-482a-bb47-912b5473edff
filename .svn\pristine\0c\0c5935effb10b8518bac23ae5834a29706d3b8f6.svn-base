package com.shunwang.baseStone.sso.web;

import com.shunwang.baseStone.cache.CacheKeyConstant;
import com.shunwang.baseStone.context.BaseStoneContext;
import com.shunwang.baseStone.context.RedisContext;
import com.shunwang.baseStone.core.action.BaseStoneAction;
import com.shunwang.baseStone.core.exception.BaseStoneException;
import com.shunwang.baseStone.sso.response.SinceCodeCreateResponse;
import com.shunwang.basepassport.binder.common.ErrorCode;
import com.shunwang.basepassport.commonExp.ParamFormatErrorExp;
import com.shunwang.basepassport.commonExp.ParamNotFoundExp;
import com.shunwang.basepassport.config.dao.ConfigOneLoginDao;
import com.shunwang.basepassport.config.pojo.ConfigOneLogin;
import com.shunwang.basepassport.user.common.MemberConstants;
import com.shunwang.basepassport.user.constant.OneLoginConstant;
import com.shunwang.util.math.RandomUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class SceneCodeCreateAction extends BaseStoneAction {

	private static final Logger logger = LoggerFactory.getLogger(SceneCodeCreateAction.class);

	private ConfigOneLoginDao configOneLoginDao;
	private Integer terminalType;
	private Integer supportChannel;

	private Integer sceneCodeExpireSeconds = 10 * 60;

	@Override
	public String getSiteName() {
		return MemberConstants.SINCE_CODE_CREATE;
	}

	@Override
	public void process() {
		ConfigOneLogin configOneLogin = getConfigOneLoginDao().findBySiteIdAndTerminalType(getSiteId(), terminalType);
		if (configOneLogin == null) {
			throw new BaseStoneException(ErrorCode.C_1096);
		}
		/**
		 * 处理客户端是不支持现在的渠道配置的情况
		 */
		if ((supportChannel & configOneLogin.getChannel()) != configOneLogin.getChannel()) {

		}

		String sceneCode = RandomUtil.getRandomStr(20);
		String cacheKey = CacheKeyConstant.InterfaceToken.ONE_LOGIN_SCENE_KEY + sceneCode;
		/**
         * 场景值保存对应的渠道配置id,缓存sceneCodeExpireSeconds分钟
		 */
		if (!RedisContext.getRedisCache().set(cacheKey, configOneLogin.getId()+"", sceneCodeExpireSeconds)) {
			logger.error("设置场景值{}缓存失败", cacheKey);
			throw new BaseStoneException(ErrorCode.C_1097);
		}
		SinceCodeCreateResponse response = new SinceCodeCreateResponse(sceneCode, configOneLogin.getAppid(), configOneLogin.getChannel());
		this.setBaseResponse(response);
	}

	public ConfigOneLoginDao getConfigOneLoginDao() {
		if (null == configOneLoginDao) {
			return (ConfigOneLoginDao) BaseStoneContext.getInstance().getBean("configOneLoginDao");
		}
		return configOneLoginDao;
	}

	@Override
	public void checkParam() {
		if (terminalType == null) {
			throw new ParamNotFoundExp("terminalType");
		}

		if (!terminalType.equals(OneLoginConstant.TERMINAL_TYPE_ANDROID) &&
			!terminalType.equals(OneLoginConstant.TERMINAL_TYPE_IOS)) {
			throw new ParamFormatErrorExp("terminalType");
		}

		if (supportChannel == null) {
			throw new ParamNotFoundExp("supportChannel");
		}

		if ((supportChannel & OneLoginConstant.CHANNEL_GEETEST) != OneLoginConstant.CHANNEL_GEETEST) {
			throw new ParamFormatErrorExp("supportChannel");
		}
	}

	public void setConfigOneLoginDao(ConfigOneLoginDao configOneLoginDao) {
		this.configOneLoginDao = configOneLoginDao;
	}

	public Integer getTerminalType() {
		return terminalType;
	}

	public void setTerminalType(Integer terminalType) {
		this.terminalType = terminalType;
	}

	public Integer getSupportChannel() {
		return supportChannel;
	}

	public void setSupportChannel(Integer supportChannel) {
		this.supportChannel = supportChannel;
	}

	public Integer getSceneCodeExpireSeconds() {
		return sceneCodeExpireSeconds;
	}

	public void setSceneCodeExpireSeconds(Integer sceneCodeExpireSeconds) {
		this.sceneCodeExpireSeconds = sceneCodeExpireSeconds;
	}
}
