package com.shunwang.passport.weixin.pojo;

/**
 * 微信客户端事件
 *
 * <AUTHOR>
 * @since 2014-04
 *
*/ 
public class WeixinEvent extends WeixinBasicMsg {

    public WeixinEvent () {
    }

    public WeixinEvent(String toUserName,String fromUserName) {
        super(toUserName,fromUserName);
    }

    public WeixinEvent(String toUserName,String fromUserName, String createTime, String msgType) {
        super(toUserName,fromUserName, createTime, msgType);
    }

    /** 事件类型 */
    private String event;

    //以下为客户端上报地理信息所用字段
    private String latitude;
    private String longitude;
    private String precision;

    /** click事件,用户点击的菜单的key 或者 view事件,用户点击的菜单的url*/
    private String eventKey;


    public void setEvent(String event) {
        this.event=event;
    }
    public String getEvent() {
        return this.event;
    }


    public void setLatitude(String latitude) {
        this.latitude=latitude;
    }
    public String getLatitude() {
        return this.latitude;
    }


    public void setLongitude(String longitude) {
        this.longitude=longitude;
    }
    public String getLongitude() {
        return this.longitude;
    }


    public void setPrecision(String precision) {
        this.precision=precision;
    }
    public String getPrecision() {
        return this.precision;
    }


    public void setEventKey(String eventKey) {
        this.eventKey=eventKey;
    }
    public String getEventKey() {
        return this.eventKey;
    }

}
