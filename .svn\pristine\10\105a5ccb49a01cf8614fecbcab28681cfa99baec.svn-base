/*
 *******************************
 * 版权所有：顺网科技 保留所有权利
 * 创建日期: 2011-3-31 上午11:20:50
 * 创建作者：jinzb
 * 文件名称：MessageConsumer.java
 * 版本：
 * 功能：
 * 最后修改时间：2011-3-31 上午11:20:50
 * 修改记录：
*****************************************/

package com.shunwang.baseStone.jms;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 消息消费者，监听消费服务（MQ）接收对应队列消息
 *
 * <AUTHOR>
 * @since 2011-3-31 上午11:20:50
 * @see
 */
public abstract class MessageConsumer<T> {

    protected final Logger logger = LoggerFactory.getLogger(MessageConsumer.class);

    /**
     * 处理信息
     *
     * @param msg 消息
     */
    public abstract void handleMessage(T msg);
}
