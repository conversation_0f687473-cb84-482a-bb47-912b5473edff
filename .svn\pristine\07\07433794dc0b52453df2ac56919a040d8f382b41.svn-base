<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">
	
	<!-- 为兼容登录日志 -->
	<bean id="domainContext" class="com.shunwang.baseStone.context.DomainContext" >
		<property name="aesLogKey" value="${db.aes.password.basePassportLog}"></property>
	</bean>
	
	<bean id="ssoDomainContext" class="com.shunwang.baseStone.sso.context.SsoDomainContext" >
        <property name="ssoDomain" value="${ssoDomain}"></property>
        <property name="staticServer" value="${staticServer}"></property>
        <property name="cdnVersion" value="${cdnVersion}"></property>
        <property name="interfaceServer" value="${interfaceServer}"></property>
		<property name="ssoServer" value="${ssoServer}"></property>
		<property name="identityServer" value="${identityServer}"></property>
		<property name="backStaticServer" value="${backStaticServer}"></property>
    </bean>
    
</beans>