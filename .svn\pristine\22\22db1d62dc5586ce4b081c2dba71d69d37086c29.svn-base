package com.shunwang.baseStone.sso.web;

import com.shunwang.baseStone.context.BaseStoneContext;
import com.shunwang.baseStone.context.RedisOperation;
import com.shunwang.baseStone.core.exception.BaseStoneException;
import com.shunwang.baseStone.core.exception.MsgNullExp;
import com.shunwang.baseStone.siteinterface.context.SiteContext;
import com.shunwang.baseStone.sso.apapter.UserOutsiteApapter;
import com.shunwang.baseStone.sso.constant.UserOutsiteConstant;
import com.shunwang.baseStone.sso.intfc.InterfaceService;
import com.shunwang.basepassport.binder.common.ErrorCode;
import com.shunwang.basepassport.config.pojo.Css;
import com.shunwang.basepassport.config.pojo.SiteInterface;
import com.shunwang.basepassport.manager.service.bo.ReportProcessor;
import com.shunwang.basepassport.user.dao.MemberAccountBindDao;
import com.shunwang.basepassport.user.dao.MemberDao;
import com.shunwang.basepassport.weixin.constant.WeixinConstant;
import com.shunwang.framework.struts2.action.BaseAction;
import com.shunwang.util.lang.StringUtil;
import com.shunwang.util.math.RandomUtil;
import com.shunwang.util.net.IpUtil;
import org.apache.struts2.ServletActionContext;

import java.io.UnsupportedEncodingException;
import java.math.BigInteger;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 提供统一的可嵌入的二维码，
 */
public class QrcodeLoginAction extends BaseAction implements ReportProcessor {

	private static final long serialVersionUID = -2740760971876416062L;
	//嵌入sso可传参数
	private String site_id;
	/** 登录成功后回调业务方的地址，该地址应该包含接入方自己的场景值，以便接入方服务端和客户端关联对应数据 **/
	private String callbackUrl = "";
	private String version;
	private String env;
	private String extData;
	private String reportData;
	private String appId;
	/** css展示站点id，优先取传入的站点，为空时取登录的site_id取样式 */
	private String cssSiteId;
	/** 是否覆盖父页面（默认为覆盖，如果传self则不覆盖，这个参数目前一般指的是回调、跳转之类）**/
	private String tgt ;
	/** 二维码解析参数 取名uk便于生成的二维码链接足够短，这样二维码更简单更容易识别 **/
	private String uk;

	/** sso平台生成的场景值，用于登录及第三方关联和后续页面跳转等 于接入方场景值无关 **/
	private String userKey;
	private Css loginCss;

	private Integer expireSeconds;

	private MemberAccountBindDao memberAccountBindDao ;
	private InterfaceService interfaceService;
	protected RedisOperation redisOperation;

	@Override
	public String execute() throws Exception {
		ServletActionContext.getResponse().addHeader("P3P", "CP=CAO PSA OUR");
		try {
			checkSite();
			initParams();
			checkSite();
			//生成sso自己的场景值，避免不同接入方传入相同值导致串号等问题
			String randomScene = new BigInteger(RandomUtil.getRandomStr(20)).toString(36);
			userKey = buildUserKey(randomScene) ;
			cacheData(userKey, expireSeconds);
		} catch (BaseStoneException e) {
			setMsg(e.getMessage());
		}
		return  "input";
	}

	/**
	 * 扫码后，微信或支付宝请求一遍拉起对应的授权页面
	 * @return 重定向授权地址，或者对应的错误提示页面
	 */
	public String goAuth() throws Exception {
		if (StringUtil.isBlank(uk)) {
			setMsg("请刷新二维码，并重新扫码。");
			return "tip";
		}
		Map<String, String> cacheMap = redisOperation.getMap(buildCacheKey(uk), String.class, String.class);
		if (cacheMap == null || cacheMap.isEmpty()) {
			setMsg("二维码已过期，请刷新并重新扫码。");
			return "tip";
		}
		String userAgent = ServletActionContext.getRequest().getHeader("User-Agent").toLowerCase();
		String loginSiteId;
		if (isWeixin(userAgent)) {
			loginSiteId = UserOutsiteConstant.WX_INTERFACE_ID;
		} else if (isAlipay(userAgent)) {
			loginSiteId = UserOutsiteConstant.ALIPAY_INTERFACE_ID;
		} else if (isQQ(userAgent)) {
			loginSiteId = UserOutsiteConstant.QQ_INTERFACE_ID;
		} else if (isWeibo(userAgent)) {
			loginSiteId = UserOutsiteConstant.WEIBO_INTERFACE_ID;
		} else {
			setMsg("微信,微博,QQ或支付宝扫码");
			return "tip";
		}
		UserOutsiteApapter outSiteAdapter = (UserOutsiteApapter) BaseStoneContext.getInstance().getBean(
				UserOutsiteConstant.USEROUT_APAPTERS
						.get(loginSiteId));
		outSiteAdapter.setCallbackUrl(URLEncoder.encode(cacheMap.get("callbackUrl"),"UTF-8"));
		outSiteAdapter.setSite_id(cacheMap.get("site_id"));
		outSiteAdapter.setVersion(cacheMap.get("version"));
		outSiteAdapter.setEnv(cacheMap.get("env"));
		outSiteAdapter.setExtData(cacheMap.get("extData"));
		outSiteAdapter.setTgt(cacheMap.get("tgt"));
		outSiteAdapter.setInnerScene(cacheMap.get("siteScene"));
		outSiteAdapter.setAppId(cacheMap.get("appId"));
		outSiteAdapter.setMultiTerminalLoginJump(true);
		Map<String,Object> response = outSiteAdapter.getOauthByServer();
		getResponse().sendRedirect(response.get("oauthUrl").toString());
		return null;
	}

	private boolean isWeixin(String userAgent) {
		return userAgent != null && userAgent.contains("micromessenger");
	}
	private boolean isAlipay(String userAgent) {
		return userAgent != null && userAgent.contains("aliapp");
	}
	private boolean isQQ(String userAgent) {
		return userAgent != null && userAgent.contains("qq/");
	}
	private boolean isWeibo(String userAgent) {
		return userAgent != null && userAgent.contains("weibo");
	}

	/**
	 * 构建场景值，用于生成二维码及登录查询
	 * @param scene 场景值
	 */
	private String buildUserKey(String scene){
		return WeixinConstant.QR_UK_PRE + scene;
	}

	/**
	 * 构建场景值关联的缓存key，用于存储登录原始表单数据，供后续扫码后拉起授权接口使用
	 * @param uk 场景值前缀
	 */
	private String buildCacheKey(String uk) {
		return WeixinConstant.QRCODE_LOGIN_KEY + uk;
	}

	/**
	 * 缓存原始登录表单数据
	 * @param uk 场景值前缀
	 * @param expireSeconds 过期时间
	 */
	protected void cacheData(String uk, Integer expireSeconds) {
		Map<String, String> cacheMap = new HashMap<>();
		cacheMap.put("callbackUrl", callbackUrl);
		cacheMap.put("site_id", site_id);
		cacheMap.put("tgt", tgt);
		cacheMap.put("siteScene", uk);
		cacheMap.put("appId", appId);
		cacheMap.put("env", env);
		cacheMap.put("version", version);
		cacheMap.put("extData", extData);
		cacheMap.put("clientIp", IpUtil.getIpAddress(ServletActionContext.getRequest()));

		redisOperation.setMap(buildCacheKey(uk), cacheMap, expireSeconds, TimeUnit.SECONDS);
	}

	/**
	 * 初始化参数信息
	 */
	private void initParams() throws UnsupportedEncodingException {
		if (StringUtil.isNotBlank(getMsg())) {
			setMsg(new String(getMsg().getBytes(StandardCharsets.ISO_8859_1), StandardCharsets.UTF_8));
		}
		if (StringUtil.isNotBlank(getExtData())) {
			setExtData(URLDecoder.decode(extData, "UTF-8"));
		}
	}

	public void checkSite() {
		SiteContext.setSiteName("SSO");
		SiteContext.setSiteId(this.getSite_id());
		if (StringUtil.isBlank(getSite_id())) {
			throw new MsgNullExp("站点ID");
		}
		SiteInterface site = SiteContext.getSiteInterface();
		if (site == null) {
			throw new BaseStoneException(ErrorCode.C_1004);
		}
	}

	public String getCallbackUrl() {
		return callbackUrl;
	}

	public void setCallbackUrl(String callbackUrl) {
		this.callbackUrl = callbackUrl;
	}

	protected MemberDao getMemberDao() {
		return (MemberDao) BaseStoneContext.getInstance().getBean("memberDao");
	}

	public String getSite_id() {
		return site_id;
	}

	public void setSite_id(String siteId) {
		site_id = siteId;
	}

    public String getCssSiteId() {
        return cssSiteId;
    }

    public void setCssSiteId(String cssSiteId) {
        this.cssSiteId = cssSiteId;
    }

	public String getExtData() {
		return extData;
	}

	public void setExtData(String extData) {
		this.extData = extData;
	}

	public String getEnv() {
		return env;
	}

	public void setEnv(String env) {
		this.env = env;
	}

	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}

	public Css getLoginCss() {
		return loginCss;
	}

	public void setLoginCss(Css loginCss) {
		this.loginCss = loginCss;
	}

	public String getTgt() {
		return tgt;
	}

	public void setTgt(String tgt) {
		this.tgt = tgt;
	}

	public MemberAccountBindDao getMemberAccountBindDao() {
		return memberAccountBindDao;
	}

	public void setMemberAccountBindDao(MemberAccountBindDao memberAccountBindDao) {
		this.memberAccountBindDao = memberAccountBindDao;
	}

    public InterfaceService getInterfaceService() {
        return interfaceService;
    }

    public void setInterfaceService(InterfaceService interfaceService) {
        this.interfaceService = interfaceService;
    }

	public String getReportData() {
		return reportData;
	}

	public void setReportData(String reportData) {
		this.reportData = reportData;
	}

	public RedisOperation getRedisOperation() {
		return redisOperation;
	}

	public void setRedisOperation(RedisOperation redisOperation) {
		this.redisOperation = redisOperation;
	}

	public Integer getExpireSeconds() {
		return expireSeconds;
	}

	public void setExpireSeconds(Integer expireSeconds) {
		this.expireSeconds = expireSeconds;
	}

	public String getUserKey() {
		return userKey;
	}

	public void setUserKey(String userKey) {
		this.userKey = userKey;
	}

	public String getUk() {
		return uk;
	}

	public void setUk(String uk) {
		this.uk = uk;
	}

	public String getAppId() {
		return appId;
	}

	public void setAppId(String appId) {
		this.appId = appId;
	}
}
