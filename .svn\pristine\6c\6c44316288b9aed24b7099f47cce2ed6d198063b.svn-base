<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:aop="http://www.springframework.org/schema/aop"
	xsi:schemaLocation="http://www.springframework.org/schema/beans
	http://www.springframework.org/schema/beans/spring-beans.xsd
    http://www.springframework.org/schema/aop
	http://www.springframework.org/schema/aop/spring-aop.xsd">


    <bean id="configurer" class="org.springframework.beans.factory.config.PropertyPlaceholderConfigurer">
	<property name="ignoreUnresolvablePlaceholders" value="true"/>
	<property name="locations">
		<list>
			<value>file:${PASSPORT_KEDOU_CONFIG_HOME}/server.properties</value>
			<value>file:${PASSPORT_KEDOU_CONFIG_HOME}/baseStoneDb.properties</value>
			<value>file:${PASSPORT_KEDOU_CONFIG_HOME}/cache.properties</value>
			<value>file:${PASSPORT_KEDOU_CONFIG_HOME}/email.properties</value>
			<value>file:${PASSPORT_KEDOU_CONFIG_HOME}/weixin.properties</value>
		</list>
	</property>
	
	</bean>
	
	<bean class="com.shunwang.spring.ExceptionProcesserIniter">	
    	<property name="exceptionProcesserFinder" ref="exceptionProcesserFinder" />
    </bean>
    <bean id="exceptionProcesserFinder" class="com.shunwang.finder.imp.XMLExceptionProcesserFinder">	
    </bean>
    <bean id="logExceptionProcesser" class="com.shunwang.processer.imp.LogExceptionProcesser">	
    </bean>
    
    <import resource="classpath*:/basepassportWeb/spring/*.xml" />
    <import resource="classpath*:/basepassportWeb/jms/*.xml" />
	<import resource="classpath*:/basepassport.xml" />
	<import resource="classpath*:/webCache/spring/cache.xml" />
	<import resource="classpath*:/baseStone.xml" />

	<aop:aspectj-autoproxy proxy-target-class="true"/>


	<bean id="encryptAspect" class="com.shunwang.basepassport.aspect.EncryptAspect">
		<property name="queryWithCipherColumn" value="${db.queryWithCipherColumn}"/>
		<property name="databaseKeyMapping">
			<map>
				<!--key为数据源的name-->
				<entry key="mysql-base-passport" value="${db.aes.password.basePassport}"/>
			</map>
		</property>
	</bean>
    

</beans>
