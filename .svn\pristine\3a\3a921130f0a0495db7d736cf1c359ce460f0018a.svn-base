package com.shunwang.baseStone.backuser.constant;


import com.shunwang.baseStone.core.exception.OAuth2Exception;

/**
 * <AUTHOR>
 * @since 2015-10-10
 */
public enum OAuth2Result {

    CODE_0("0", "成功"),

    CODE_1001("1001", "缺少必要的参数[code]"),

    CODE_1002("1002", "会话已过期，请重新登录[state]"),

    CODE_1003("1003", "参数不能为空[code]"),

    CODE_1004("1004", "会话已过期，请重新登录[redirect_uri]"),

    CODE_1005("1005", "参数不能为空[access_token]"),

    CODE_1006("1006", "用户信息获取失败"),

    CODE_1007("1007", "登录账号不能为空"),

    CODE_1008("1008", "登录密码不能为空"),

    CODE_1009("1009", "会话已过期，请重新登录[email]"),

    CODE_1010("1010", "登录账号或密码不对"),

    CODE_1011("1011", "AccessToken换取失败"),

    CODE_1012("1012", "获取用户信息失败"), ;

    private String code;
    private String msg;

    OAuth2Result(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public OAuth2Exception shoot(Throwable e) {
        if (e != null) {
            return new OAuth2Exception(this.code, this.msg, e);
        }
        return new OAuth2Exception(this.code, this.msg);
    }
}
