package com.shunwang.baseStone.siteinterface.exception;

import com.shunwang.baseStone.core.exception.BaseStoneException;
import com.shunwang.basepassport.binder.common.ErrorCode;

/******************************
 * 版权所有：顺网科技 保留所有权利
 * 创建日期: 2011 Jul 25, 2011 1:29:56 PM
 * 创建作者：zhangjp
 * 文件名称：TimeOutExp.java
 * 版本： 1.0
 * 功能：时间超时异常类
 * 最后修改时间：
 * 修改记录：
 *****************************************/
public class TimeOutExp extends BaseStoneException{

	/**
	 * 
	 */
	private static final long serialVersionUID = -7084699350878774139L;
	public TimeOutExp() {
		super();
		this.setMsgId(ErrorCode.C_1003.getCode());
		this.setMsg(ErrorCode.C_1003.getDescription());
	}
	
	
}
