package com.shunwang.basepassport.user.response;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;
import com.shunwang.baseStone.core.response.BaseStoneResponse;
import com.shunwang.baseStone.encrypt.Encrypt;
import com.shunwang.baseStone.encrypt.pojo.EncryptItem;
import com.shunwang.basepassport.user.common.HeadUrlUtil;
import com.shunwang.basepassport.user.pojo.Member;
import com.shunwang.util.StringUtil;
import com.shunwang.util.date.DateUtil;
import com.shunwang.xmlbean.annotation.XmlInit;

import java.util.Date;

public class OutSiteMemberQueryResponse extends BaseStoneResponse {

	private MemberAndInfo memberAndInfo;

	public OutSiteMemberQueryResponse(Member member) {
		this.memberAndInfo=new MemberAndInfo();
		this.memberAndInfo.memberId = String.valueOf(member.getMemberId());
		this.memberAndInfo.email = member.getEmail();
		this.memberAndInfo.mobile = member.getMobile();
		this.memberAndInfo.realName = member.getRealName();
		this.memberAndInfo.identity = member.getMemberInfo().getIdCardNo();
		this.memberAndInfo.companyName = member.getCompanyName();
		this.memberAndInfo.linkMan = member.getMemberInfo().getLinkMan();
		this.memberAndInfo.memberType = String.valueOf(member.getMemberType());
        this.memberAndInfo.memberSpecialType = String.valueOf(member.getMemberSpecialType());
		this.memberAndInfo.memberName = member.getMemberName();
		this.memberAndInfo.qq = member.getMemberInfo().getQq();
		this.memberAndInfo.linkAddress = member.getMemberInfo().getLinkAddress();
		this.memberAndInfo.fixedMobile = member.getMemberInfo().getFixedMobile();
		this.memberAndInfo.postalCode = member.getMemberInfo().getPostCode();
		this.memberAndInfo.companyCertState = member.getCompanyCertState()+"";
		this.memberAndInfo.personCertState = member.getPersonCertState()+"";
        this.memberAndInfo.netbarCertState = member.getCafeCertState() + "";
		this.memberAndInfo.bindState = String.valueOf(member.getBindState());
		this.memberAndInfo.headImg = HeadUrlUtil.getSmallHeadImageUrl(member);
		this.memberAndInfo.nickName = member.getNickName();
		this.memberAndInfo.titleName = member.getTitleName();
		if(null != member.getMemberInfo() && StringUtil.isNotBlank(member.getMemberInfo().getRegFrom())){
			this.memberAndInfo.regFrom = member.getMemberInfo().getRegFrom();
		}
		this.memberAndInfo.timeReg = member.getTimeAdd() ;
		this.memberAndInfo.timeLastLogin = member.getTimeLogon() ;
		this.memberAndInfo.setMemberState(member.getMemberState());

		this.memberAndInfo.setEmptyPwd(member.getIsEmptyPwd() ? "1" : "0");

	}

	@Override
	public String buildSign(){
		Encrypt  encrypt=new Encrypt();
		encrypt.addItem(new EncryptItem(this.memberAndInfo.email==null?"":this.memberAndInfo.email));
		encrypt.addItem(new EncryptItem(this.memberAndInfo.mobile==null?"":memberAndInfo.mobile, true));
		encrypt.addItem(new EncryptItem(this.memberAndInfo.realName==null?"":memberAndInfo.realName));
		encrypt.addItem(new EncryptItem(this.memberAndInfo.identity==null?"":memberAndInfo.identity));
		encrypt.addItem(new EncryptItem(this.memberAndInfo.bindState==null?"":memberAndInfo.bindState));
		encrypt.addItem(new EncryptItem(this.memberAndInfo.companyName==null?"":memberAndInfo.companyName));
		encrypt.addItem(new EncryptItem(this.memberAndInfo.linkMan==null?"":memberAndInfo.linkMan));
		return encrypt.buildSign();
	}
	
	public class MemberAndInfo {
		/**会员ID**/
        @Expose
        @SerializedName("userId")
		private String memberId;
		/**会员状态(0什么都没绑、1手机、2邮箱、4密保卡、8密保问题)**/
        @Expose
		private String bindState;
		/**真实姓名**/
        @Expose
		private String realName;
		/**身份证号(校验身份证长度15/18位**/
        @Expose
		private String identity;
		/**用户类型,1代表个人 2代表企业**/
        @Expose
        @SerializedName("userType")
		private String memberType;
        /**特殊用户类型,1代表网吧业主**/
        @Expose
        @SerializedName("userSpecialType")
        private String memberSpecialType;
		/**邮箱**/
        @Expose
		private String email;
		/**手机**/
        @Expose
		private String mobile;
		/**公司名**/
        @Expose
		private String companyName;
		/**联系人**/
        @Expose
		private String linkMan;
		/**头像地址**/
        @Expose
		@SerializedName("userName")
		private String memberName;
		/**QQ**/
        @Expose
		private String qq;
	    /**联系地址**/
        @Expose
		private String linkAddress;
		/**固定电话**/
        @Expose
		private String fixedMobile;
		/**邮政编码**/
        @Expose
		private String postalCode;
		/**企业认证：1-未申请，2-待审核，3-已拒绝，4-已认证 ,5-已撤消，默认为1**/
        @Expose
		private String companyCertState;
		/**个人认证：1-未申请，2-待审核，3-已拒绝，4-已认证,5-已撤消，默认为1**/
        @Expose
		private String personCertState;
		/**网吧业主认证：1-未申请，2-待审核，3-已拒绝，4-已认证,5-已撤消，默认为1**/
        @Expose
		private String netbarCertState;
        @Expose
		private String headImg;
        @Expose
		private String nickName;
        @Expose
		private String titleName;
		@Expose
		private Integer memberState;

		@Expose
		private String emptyPwd;

		private Date timeReg ;
		private String regFrom ;
		private Date timeLastLogin ;//最后一次登录时间
		
		@XmlInit(path="userId")
		public String getMemberId() {
			return memberId;
		}

		public void setMemberId(String memberId) {
			this.memberId = memberId;
		}

		@XmlInit
		public String getBindState() {
			return bindState;
		}

		public void setBindState(String bindState) {
			this.bindState = bindState;
		}

		@XmlInit
		public String getRealName() {
			return realName;
		}

		public void setRealName(String realName) {
			this.realName = realName;
		}

		@XmlInit
		public String getIdentity() {
			return identity;
		}

		public void setIdentity(String identity) {
			this.identity = identity;
		}

		@XmlInit(path="userType")
		public String getMemberType() {
			return memberType;
		}

		public void setMemberType(String memberType) {
			this.memberType = memberType;
		}

        @XmlInit(path="userSpecialType")
        public String getMemberSpecialType() {
            return memberSpecialType;
        }

        public void setMemberSpecialType(String memberSpecialType) {
            this.memberSpecialType = memberSpecialType;
        }

        @XmlInit
		public String getEmail() {
			return email;
		}

		public void setEmail(String email) {
			this.email = email;
		}

		@XmlInit
		public String getCompanyName() {
			return companyName;
		}

		public void setCompanyName(String companyName) {
			this.companyName = companyName;
		}

		@XmlInit
		public String getLinkMan() {
			return linkMan;
		}

		public void setLinkMan(String linkMan) {
			this.linkMan = linkMan;
		}

		@XmlInit
		public String getQq() {
			return qq;
		}

		public void setQq(String qq) {
			this.qq = qq;
		}

		@XmlInit
		public String getLinkAddress() {
			return linkAddress;
		}

		public void setLinkAddress(String linkAddress) {
			this.linkAddress = linkAddress;
		}

		@XmlInit
		public String getFixedMobile() {
			return fixedMobile;
		}

		public void setFixedMobile(String fixedMobile) {
			this.fixedMobile = fixedMobile;
		}

		@XmlInit
		public String getPostalCode() {
			return postalCode;
		}

		public void setPostalCode(String postalCode) {
			this.postalCode = postalCode;
		}

		@XmlInit
		public String getCompanyCertState() {
			return companyCertState;
		}

		public void setCompanyCertState(String companyCertState) {
			this.companyCertState = companyCertState;
		}

		@XmlInit
		public String getPersonCertState() {
			return personCertState;
		}

		public void setPersonCertState(String personCertState) {
			this.personCertState = personCertState;
		}

        @XmlInit
        public String getNetbarCertState() {
            return netbarCertState;
        }

        public void setNetbarCertState(String netbarCertState) {
            this.netbarCertState = netbarCertState;
        }

        @XmlInit(path="userName")
		public String getMemberName() {
			return memberName;
		}

		public void setMemberName(String memberName) {
			this.memberName = memberName;
		}

		@XmlInit
		public String getMobile() {
			return mobile;
		}

		public void setMobile(String mobile) {
			this.mobile = mobile;
		}
		@XmlInit
		public String getHeadImg() {
			return headImg;
		}

		public void setHeadImg(String headImg) {
			this.headImg = headImg;
		}
		@XmlInit
		public String getNickName() {
			return nickName;
		}

		public void setNickName(String nickName) {
			this.nickName = nickName;
		}
		@XmlInit
		public String getTitleName() {
			return titleName;
		}

		public void setTitleName(String titleName) {
			this.titleName = titleName;
		}

		@XmlInit(path = "timeReg")
		public String getTimeRegStr() {
			return null != timeReg ?  DateUtil.getDateStamp(timeReg) : "";
		}

		public Date getTimeReg() {
			return timeReg;
		}

		public void setTimeReg(Date timeReg) {
			this.timeReg = timeReg;
		}

		@XmlInit
		public String getRegFrom() {
			return regFrom;
		}

		public void setRegFrom(String regFrom) {
			this.regFrom = regFrom;
		}

		@XmlInit(path="timeLastLogin")
		public String getTimeLastLoginStr() {
			return null != timeLastLogin ? DateUtil.getDateStamp(timeLastLogin) : "";
		}

		public void setTimeLastLogin(Date timeLastLogin) {
			this.timeLastLogin = timeLastLogin;
		}

		public Date getTimeLastLogin() {
			return timeLastLogin;
		}

		@XmlInit
		public Integer getMemberState() {
			return memberState;
		}

		public void setMemberState(Integer memberState) {
			this.memberState = memberState;
		}
		@XmlInit
		public String getEmptyPwd() {
			return emptyPwd;
		}

		public void setEmptyPwd(String emptyPwd) {
			this.emptyPwd = emptyPwd;
		}
	}

	@XmlInit(path="items/item")
	public MemberAndInfo getMemberAndInfo() {
		return memberAndInfo;
	}

	public void setMemberAndInfo(MemberAndInfo memberAndInfo) {
		this.memberAndInfo = memberAndInfo;
	}

}
