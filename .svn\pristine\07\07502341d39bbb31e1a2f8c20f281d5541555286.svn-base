package com.shunwang.basepassport.mobile;

import com.shunwang.basepassport.BaseTest;
import com.shunwang.util.date.DateUtil;


public class TestMobileValidate extends BaseTest {
    @Override
    public void init() {
        params.put("siteId", "identity");
        params.put("memberId", "91485505");
        params.put("mobileActiveNo", "460334");
        params.put("time", DateUtil.getCurrentDateStamp());
        params.put("signVersion", "1.0");
        params.put("sign", getSign(params));
    }

    @Override
    protected String getUrl() {
        return "http://interface.kedou.com/front/interface/mobileValidate.htm";
    }

    @Override
    protected String getMd5Key() {
        return "123456";
    }
}
