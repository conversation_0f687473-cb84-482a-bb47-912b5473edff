package com.shunwang.basepassport.user.pojo;

import com.shunwang.baseStone.core.pojo.BaseStoneObject;

import java.io.Serializable;
import java.util.Date;

public class ServiceNotify extends BaseStoneObject {

    private Integer id;
    /**用户id**/
    private Integer memberId;
    /**用户名**/
    private String memberName;
    /**接入方站点id，用于获取接口配置**/
    private String siteId;
    /**接口对应的服务**/
    private String siteName;
    /**接入方通知地址**/
    private String notifyUrl;
    /**通知内容，需通知业务方的内容:json字符串**/
    private String jsonData;
    /**入库时间**/
    private Date timeCreate;
    /**更新时间**/
    private Date timeEdit;
    /**通知次数，默认为0**/
    private Integer notifyTimes = 0;
    /**通知状态，默认为0 未通知或已通知但未成功，1成功**/
    private Integer state = 0;
    /**通知组 处理存在先后顺序的通知**/
    private String notifyGroup;
    /**记录通知返回的说明**/
    private String remark;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getMemberId() {
        return memberId;
    }

    public void setMemberId(Integer memberId) {
        this.memberId = memberId;
    }

    public String getMemberName() {
        return memberName;
    }

    public void setMemberName(String memberName) {
        this.memberName = memberName;
    }

    public String getSiteId() {
        return siteId;
    }

    public void setSiteId(String siteId) {
        this.siteId = siteId;
    }

    public String getNotifyUrl() {
        return notifyUrl;
    }

    public void setNotifyUrl(String notifyUrl) {
        this.notifyUrl = notifyUrl;
    }

    public Date getTimeCreate() {
        return timeCreate;
    }

    public void setTimeCreate(Date timeCreate) {
        this.timeCreate = timeCreate;
    }

    public Date getTimeEdit() {
        return timeEdit;
    }

    public void setTimeEdit(Date timeEdit) {
        this.timeEdit = timeEdit;
    }

    public Integer getNotifyTimes() {
        return notifyTimes;
    }

    public void setNotifyTimes(Integer notifyTimes) {
        this.notifyTimes = notifyTimes;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getJsonData() {
        return jsonData;
    }

    public void setJsonData(String jsonData) {
        this.jsonData = jsonData;
    }

    public String getSiteName() {
        return siteName;
    }

    public void setSiteName(String siteName) {
        this.siteName = siteName;
    }

    public String getNotifyGroup() {
        return notifyGroup;
    }

    public void setNotifyGroup(String notifyGroup) {
        this.notifyGroup = notifyGroup;
    }

    @Override
    public Serializable getPk() {
        return id;
    }
}
