package com.shunwang.baseStone.encrypt;

import junit.framework.TestCase;

import com.shunwang.baseStone.encrypt.Encrypt;
import com.shunwang.baseStone.encrypt.pojo.EncryptItem;

public class Md5Test extends TestCase{

	/**
	 *
	 */
	private static final long serialVersionUID = 1L;

	//test
	public  void testMy(){
		Encrypt myMd5=new Encrypt("qwertyasd3e3ed#e","|","md5");
		String pwd="21354654";
		myMd5.addItem(new EncryptItem("maomy"));
		myMd5.addItem(new EncryptItem("fffff"));
		myMd5.addItem(new EncryptItem("tt343242tt"));
//		myMd5.addItem(new EncryptItem(new MD5TestTwo(),pwd));
		myMd5.addItem(new EncryptItem(pwd,"des","qwertyasd3e3ed#e"));//密码需要先加密


		System.out.println(myMd5.getSign());
	}








}
