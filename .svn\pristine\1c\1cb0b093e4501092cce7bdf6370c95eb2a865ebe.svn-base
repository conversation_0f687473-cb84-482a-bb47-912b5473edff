package com.shunwang.baseStone.sso.freeloginweb;

import java.util.Map;

import org.apache.commons.lang.StringUtils;

import com.google.gson.Gson;
import com.shunwang.baseStone.sso.context.ClientUserTockenContext;
import com.shunwang.baseStone.sso.context.FreeLoginTicketContext;
import com.shunwang.baseStone.sso.core.constants.SSOConstants;
import com.shunwang.baseStone.sso.exception.ClientUserTockenExp;
import com.shunwang.baseStone.sso.exception.ValidateExp;
import com.shunwang.baseStone.sso.pojo.ClientUserTocken;
import com.shunwang.baseStone.sso.pojo.FreeLoginTicket;

/**
 * sso页面免登时，验证clientToken数据以得到免登串freeTicket
 */
public class CheckClientTokenAction extends FreeLoginBaseAction {

	private static final long serialVersionUID = 6342790540583617559L;
	private String clientId;
	private String loginInfo;
	private ClientUserTocken clientUserToken;

	@Override
	protected Map<String, Object> process() throws Exception {
		try {
			Map<String, Object> result = createResultJSON();
			
			clientUserToken.setClientId(clientId);
			ClientUserTocken clientTocken = ClientUserTockenContext.validate(clientUserToken);
			
			FreeLoginTicket freeTicket = FreeLoginTicketContext.createTicket(clientUserToken,clientTocken.getSiteId());
			
			result.put("freeTicket", freeTicket.getFreeTicketId());
			result.put("memberName", freeTicket.getMemberName());
			result.put("memberId", freeTicket.getMemberId());
			
			return result;
		} catch(ClientUserTockenExp e) {
			//异常源点已输出日志,此处不需重复记录
			throw new ValidateExp(e.getMessage());
		}
		
	}

	@Override
	protected void checkParam() throws Exception {
		if (StringUtils.isBlank(loginInfo))
            throw new ValidateExp("参数[loginInfo]不能为空");
		Gson gson = new Gson();
		clientUserToken = gson.fromJson(loginInfo, ClientUserTocken.class);
		if(StringUtils.isBlank(clientId)) 
			throw new ValidateExp("参数[clientId]不能为空");
		if(StringUtils.isBlank(clientUserToken.getClientUserTokenId()))
			throw new ValidateExp("参数[clientUserTokenId]不能为空");
		if(StringUtils.isBlank(clientUserToken.getMemberName()))
			throw new ValidateExp("参数[memberName]不能为空");
	}

	@Override
	protected String getSiteName() {
		return SSOConstants.FreeLogin.checkClientToken;
	}

	public String getLoginInfo() {
		return loginInfo;
	}

	public void setLoginInfo(String loginInfo) {
		this.loginInfo = loginInfo;
	}

	public ClientUserTocken getClientUserToken() {
		return clientUserToken;
	}

	public void setClientUserToken(ClientUserTocken clientUserToken) {
		this.clientUserToken = clientUserToken;
	}

	public String getClientId() {
		return clientId;
	}

	public void setClientId(String clientId) {
		this.clientId = clientId;
	}
	
}
