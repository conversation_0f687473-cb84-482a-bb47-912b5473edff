package com.shunwang.basepassport.manager.service.dcapi;

import com.shunwang.basepassport.config.dao.ConfigInterfaceDao;
import com.shunwang.basepassport.config.pojo.ConfigInterface;
import com.shunwang.basepassport.manager.request.dcapi.BlackCardRequest;
import com.shunwang.basepassport.manager.response.dcapi.BlackCardResponse;
import com.shunwang.util.net.throwing.HttpClientUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Constructor;
import java.nio.charset.StandardCharsets;
import java.util.Map;

public class BlackCardServiceClient {

    private static final Logger LOGGER = LoggerFactory.getLogger(BlackCardServiceClient.class);

    private static ConfigInterfaceDao configInterfaceDao;

    public void setConfigInterfaceDao(ConfigInterfaceDao configInterfaceDao) {
        BlackCardServiceClient.configInterfaceDao = configInterfaceDao;
    }

    public static BlackCardResponse execute(BlackCardRequest request) {
        try {
            ConfigInterface setting = configInterfaceDao.findByInterfaceKey(request.getInterfaceKey());
            request.doInterfaceSetting(setting);
            String url = request.buildUrl();
            Map<String, String> requestParams = request.buildParams();
            Map<String, String> headers = request.getHeaders();
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("----------------------------------------");
                LOGGER.debug("url:{}", url);
                LOGGER.debug("method:{}", request.getHttpMethod());
                LOGGER.debug("requestParams:{}", requestParams);
                LOGGER.debug("headers:{}", headers);
                LOGGER.debug("----------------------------------------");
            }
            String response = HttpClientUtils.doPost(url, requestParams, headers, null, StandardCharsets.UTF_8, false);

            Class<BlackCardResponse> responseClass = request.getResponseClass();
            Constructor<BlackCardResponse> constructor = responseClass.getConstructor();
            BlackCardResponse resp = constructor.newInstance();
            resp.setRawJson(response);
            return resp.parse();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

}
