package com.shunwang.baseStone.sso.web;

import com.shunwang.baseStone.cache.CacheKeyConstant;
import com.shunwang.baseStone.cache.lock.CounterLock;
import com.shunwang.baseStone.cache.service.CacheService;
import com.shunwang.baseStone.context.BaseStoneContext;
import com.shunwang.baseStone.context.LoginElementService;
import com.shunwang.baseStone.context.RedisContext;
import com.shunwang.baseStone.css.context.CssContext;
import com.shunwang.baseStone.css.pojo.Css;
import com.shunwang.baseStone.loginelement.constants.LoginElementConstant;
import com.shunwang.baseStone.sso.context.TicketContext;
import com.shunwang.baseStone.sso.context.UserTockenContext;
import com.shunwang.baseStone.sso.geetest.GeetestLib;
import com.shunwang.baseStone.sso.intfc.InterfaceService;
import com.shunwang.baseStone.sso.util.CommonUtil;
import com.shunwang.baseStone.useroutinterface.pojo.OutOauthDir;
import com.shunwang.basepassport.binder.common.ErrorCode;
import com.shunwang.basepassport.key.common.UserRegisterKeyUtil;
import com.shunwang.basepassport.user.common.MemberUtil;
import com.shunwang.basepassport.user.dao.MemberDao;
import com.shunwang.basepassport.user.pojo.Member;
import com.shunwang.basepassport.user.service.BaseXmlResponse;
import com.shunwang.framework.struts2.action.BaseAction;
import com.shunwang.util.date.DateUtil;
import com.shunwang.util.encrypt.AesEncrypt;
import com.shunwang.util.encrypt.Md5Encrypt;
import com.shunwang.util.lang.StringUtil;
import com.shunwang.util.net.HttpClientUtils;
import com.shunwang.util.net.IpUtil;
import org.apache.struts2.ServletActionContext;
import org.jdom.Document;
import org.jdom.Element;
import org.jdom.JDOMException;
import org.jdom.input.SAXBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.PrintWriter;
import java.io.StringReader;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.time.Duration;
import java.util.*;

/**
 * Created by bd.fang on 2016/3/30.
 */
public class MobileLoginAction extends BaseAction {
    private static final long serialVersionUID = -7033211833508148225L;
    private static Logger log = LoggerFactory.getLogger(MobileLoginAction.class);
    private String site_id;
    // css展示站点id
    private String cssSiteId;
    //读取隐私政策url配置
    private Css privacyCss;

    private String bindAsLoginAccountUrl;
    private String bindAsLoginAccountMd5Key;
    private String getTicketWithoutLoginUrl;
    private String getTicketWithoutLoginMd5Key;
    private Integer userId;
    private String number;
    private String tockenId;
    private String ticket;
    private String mobileValifyTokenId;
    private String msgId;
    private String mobileActiveNo;
    private String userName ;
    private String callbackUrl;
    private String css;
    private String version;
    private String env;
    private String extData;
    private String password;
    private String md5;
    private String loginSiteId;
    private String token;
    private String loginType;
    private String loginPage;
    private String userNameList;
    //访问interface时使用的siteid
    private String siteId;
    private String needCode;
    private Css loginCss;
    private Css outOauthCss;
    private Css html5Css;
    private boolean needSmsCheckCode = false;
    private boolean needImmediatelyReg = false;
    private boolean needRetrievePwd = false;
    private String  regLink;

    private String optToken;
    private boolean refreshPage = false;
    private String memberName;

    private InterfaceService interfaceService;
    private LoginElementService loginElementService;

	// 手机验证码登录后跳转，用此标志是当前iframe打开页面（tgt=self）还是顶级iframe打开页面
    private String tgt ;

    private List<OutOauthDir> outOauthDirs = new ArrayList<OutOauthDir>();
    private String mobileLogin = "MOBILE_LOGIN";
    private boolean showGt;
    private int getFromCache(String sessionId){
        Integer cacheVal = RedisContext.getRedisCache().get(CacheKeyConstant.GeetestLib.SSO_STATUS_KEY + sessionId,Integer.class);
        if(cacheVal  != null){
            return cacheVal;
        }else{
            return 1;
        }
    }

    private boolean isGtSwitchOpen() {
        return RedisContext.getSwitchConfig(CacheKeyConstant.ConfigResourcesConstants.TYPE_BASE_CONFIG,
                CacheKeyConstant.ConfigResourcesConstants.BASE_CONFIG_GT_SWITCH, false);
    }
    public String sendActiveNo() throws IOException {
        PrintWriter out = this.getResponse().getWriter();
        try {
            if (StringUtil.isBlank(site_id)) {
                out.write("{\"result\":false,\"msg\":\"参数异常\"}");
                return null;
            }
            //初始化短信登录验证码
            boolean smsCheckCodeIsOpen = loginElementService.getConfigIsOpen(site_id, LoginElementConstant.SMS_CHECK_CODE) && isGtSwitchOpen();
            if (smsCheckCodeIsOpen) {
                try {
                    boolean isChecked = GeetestLib.validate(getRequest());
                    if (!isChecked) {
                        out.write("{\"result\":false,\"msg\":\"验证失败\"}");
                        return null;
                    }
                } catch (Exception e) {
                    out.write("{\"result\":false,\"msg\":\"验证异常\"}");
                    return null;
                }
            }
            BaseXmlResponse response = interfaceService.sendForMobileRegister(buildParamsForSendActiveNo());
            getResponse().setContentType("text/json;charset=UTF-8");
            setMsg(response.getMsg());

            if(response.isSuccess()) {
                //校验用户是否已注册, 已注册用户需要勾选 用户协议,反之则系统不勾选.
                Member member = UserRegisterKeyUtil.getByMobile(number);
                boolean isCheck = (isAgreementSwitchOpen() || isSiteIdInWhitelist(site_id))
                        && member != null;
                out.write("{\"result\": " + response.isSuccess() + ",\"isReg\":\"" + isCheck + "\"}");
            } else {
                out.write("{\"result\": " + response.isSuccess() + ",\"msg\":\"" + getMsg() + "\"}");
            }
            out.close();
        } catch (Exception e) {
            log.error("短信发送失败",e);
            out.write("{\"result\": " + false + ",\"msg\":\"短信发送失败\"}");
        }
        return  null;
    }
    /**
     * 用户协议自动选中开关
     */
    private boolean isAgreementSwitchOpen() {
        String resource = RedisContext.getResourceCache().getResourceValue(
                CacheKeyConstant.ConfigResourcesConstants.COMMON_CONFIG, CacheKeyConstant.ConfigResourcesConstants.AGREEMENT_AUTO_CHECK_SWITCH);
        if (StringUtil.isNotBlank(resource) && "y".equalsIgnoreCase(resource)) {
            return true;
        }
        return false;
    }

    /**
     * 用户协议自动选中站点白名单
     * resource的value值格式为“|identity|swpay|mch|swjoy|......|”站点前后需使用竖线包裹
     */
    private boolean isSiteIdInWhitelist(String siteId) {
        String resource = RedisContext.getResourceCache().getResourceValue(
                CacheKeyConstant.ConfigResourcesConstants.COMMON_CONFIG, CacheKeyConstant.ConfigResourcesConstants.AGREEMENT_AUTO_CHECK_SITE_ID_WHITELIST);
        String siteIdTemp = ("|" + siteId + "|").toLowerCase(Locale.ROOT);
        return !StringUtil.isBlank(resource) && resource.toLowerCase().contains(siteIdTemp);
    }

    private Map<String, String> buildParamsForSendActiveNo(){
        Map<String, String> paramMap = new LinkedHashMap<String, String>();
        paramMap.put("userName", "");
        paramMap.put("mobile", number);
        paramMap.put("newMobile", "");
		paramMap.put("accessSiteId", getSite_id());
        return paramMap;
    }
    private String inputCancelView() {
        initCss(site_id);
        try {
            optToken = AesEncrypt.Encrypt(memberName + site_id, MemberUtil.getKey()).substring(8);
        } catch (Exception e) {
            log.error("已注销用户登录跳撤销页面数据异常");
        }
        return "inputCancelForHtml5";
    }

    public String outMobileConfirm() {
        try {
            int result = 0;
            CounterLock smsCheckLock = CacheService.newCounterLock("ANTI_ADDITION_LOGIN_" + number, 2, Duration.ofMinutes(1L));
            if (!smsCheckLock.tryLock()) {
                setMsg(ErrorCode.C_1102.getDescription());
            } else {
                BaseXmlResponse response = interfaceService.confirmForLogin(buildParamsForOutMobileConfirmUrl());
                result = parseResultForOutMobileConfirmUrl(response.getRawXml());
                if (ErrorCode.C_1068.getCode().equals(response.getMsgId())) {
                    Member member = UserRegisterKeyUtil.getByMobile(number);
                    memberName = member.getMemberName();//撤销注销页面需要memberName
                    refreshPage = true;
                    return inputCancelView();
                }
            }
            if(result == 1) {
                //Member member = getMemberDao().getMember(userName);
                Member member = UserRegisterKeyUtil.getMember(userName);
                userId = member.getMemberId();
                this.setTicket(TicketContext.createTicket(member, getSite_id()).toString());
                this.setTockenId(UserTockenContext.createTocken(member, site_id).toString());

                CommonUtil.addCookieAfterLogin(getRequest(), getResponse(), getTockenId(), member.getMemberId());

                return SUCCESS;
            } else {
                initCss(site_id);
                if (isGtSwitchOpen()) {
                    setShowGt(true);
                } else {
                    setShowGt(false);
                }
                //初始化短信登录验证码
                Map<String, String> configMap = loginElementService.getLoginConfig(site_id, LoginElementConstant.SMS_CHECK_CODE);
                setNeedSmsCheckCode(loginElementService.configIsOpen(configMap, LoginElementConstant.STATE));
                //初始化立即注册
                configMap = loginElementService.getLoginConfig(site_id, LoginElementConstant.IMMEDIATELY_REG);
                initImmediatelyRegInfo(configMap);
                //设置忘记密码开关
                configMap = loginElementService.getLoginConfig(site_id, LoginElementConstant.RETRIEVE_PWD);
                setNeedRetrievePwd(loginElementService.configIsOpen(configMap, LoginElementConstant.STATE));

                return "inputForHtml5";
            }
        } catch (Exception e) {
            log.error("手机号"+number+"短信登录失败",e);
        }
        return  null;
    }

    /**
     * 初始化短信快速登录
     * @param configMap
     */
    private void initImmediatelyRegInfo(Map<String,String> configMap) {
        if (null != configMap) {
            if (null != configMap.get(LoginElementConstant.STATE)
                    && configMap.get(LoginElementConstant.STATE).equals("1")) {
                setNeedImmediatelyReg(Boolean.TRUE);
                setRegLink(configMap.get(LoginElementConstant.REG_LINK));
            } else {
                setNeedImmediatelyReg(Boolean.FALSE);
            }
        }
    }

    private int parseResultForOutMobileConfirmUrl(String xmlText) {

        SAXBuilder builder = new SAXBuilder();
        StringReader strReader = new StringReader(xmlText);
        try {
            Document document =  builder.build(strReader);
            Element rootNode = document.getRootElement();
            msgId =  ((Element)(rootNode.getChildren("Result").get(0))).getAttributeValue("msgId");
            if("0".equals(msgId)) {
                Integer size = Integer.valueOf(((Element)(rootNode.getChildren("Result").get(0))).getChildText("size"));
                List<Element> abc = ((Element)((Element)(rootNode.getChildren("Result").get(0))).getChildren("items").get(0)).getChildren("item");
                StringBuffer userNames = new StringBuffer("");
                userName = abc.get(0).getChildText("userName");
                for(int i = 0; i < size;i++) {
                    abc.get(i).getChildText("userName");
                    userNames.append("\"");
                    userNames.append(abc.get(i).getChildText("userName"));
                    userNames.append("\"");
                    userNames.append(",");
                }
                userNames.deleteCharAt(userNames.length() - 1);
                userNameList = userNames.toString();
                return size;
            } else if("1024".equals(msgId)) {
                setMsg("手机短信验证码错误，请重新输入");
            } else {
                setMsg(((Element) (rootNode.getChildren("Result").get(0))).getAttributeValue("msg"));
            }
        } catch (IOException e) {
            log.error("解析消息出错", e);
        } catch (JDOMException e) {
            log.error("解析消息出错", e);
        }
        return -1;
    }



    private Map<String, String> buildParamsForOutMobileConfirmUrl(){
        Map<String, String> paramMap = new LinkedHashMap<String, String>();

        paramMap.put("userName", "");
        paramMap.put("mobile", number);
        paramMap.put("mobileActiveNo", mobileActiveNo);
        paramMap.put("loginIp", IpUtil.getIpAddress(ServletActionContext.getRequest()));
        paramMap.put("version",version);
        paramMap.put("environment",env) ;
        paramMap.put("remark",extData) ;
		paramMap.put("accessSiteId", getSite_id());
        return paramMap;
    }

    private String buildSign(Map<String, String> paramMap,String md5Key) {
        StringBuffer stringBuffer = new StringBuffer();
        for(String keySet: paramMap.keySet()){
            stringBuffer.append(String.valueOf(paramMap.get(keySet))).append("|");
        }
        stringBuffer.append(md5Key);
        String sign = "";
        try{
            log.info("签名原串"+stringBuffer.toString());
            sign = URLEncoder.encode(stringBuffer.toString(),"utf-8").toUpperCase();
        }catch(UnsupportedEncodingException e){
            log.error("对签名转码错误", e);
        }
        sign = Md5Encrypt.encrypt(sign).toUpperCase();
        if(log.isInfoEnabled()) {
            log.info("SSO手机登录:" + stringBuffer.toString() + ",sign：" + sign);
        }
        return sign;
    }


    public String bindAsLoginAccount() {
        try {
            valifyTocken(mobileValifyTokenId, number);
            String returnData = HttpClientUtils.doPost(getBindAsLoginAccountUrl(),buildParamsForBindAsLoginAccount());
            boolean result =  parseResultForBindAsLoginAccount(returnData);
            if(result) {
                Member member = getMemberDao().getMember(userName);
                userId = member.getMemberId();
                this.setTicket(TicketContext.createTicket(member, getSite_id()).toString());
                this.setTockenId(UserTockenContext.createTocken(member, site_id).toString());

                CommonUtil.addCookieAfterLogin(getRequest(), getResponse(), getTockenId(), member.getMemberId());

                return SUCCESS;
            }
            else {
                initCss(site_id);
                return "inputForHtml5";
            }
        } catch (Exception e) {
            log.error("手机号"+number+"绑定为登录账号错误",e);
        }
        return  "inputForHtml5";
    }

    private Map<String, String> buildParamsForBindAsLoginAccount(){
        String time = DateUtil.getCurrentDateStamp();
        Map<String, String> paramMap = new LinkedHashMap<String, String>();
        paramMap.put("siteId", getSiteId());
        paramMap.put("userName", userName);
        paramMap.put("mobile", number);
        paramMap.put("time", time);
        paramMap.put("sign", buildSign(paramMap, getBindAsLoginAccountMd5Key()));
        paramMap.put("loginIp", IpUtil.getIpAddress(ServletActionContext.getRequest()));
        return paramMap;
    }

    private boolean parseResultForBindAsLoginAccount(String xmlText) {

        SAXBuilder builder = new SAXBuilder();
        StringReader strReader = new StringReader(xmlText);
        try {
            Document document = builder.build(strReader);
            Element rootNode = document.getRootElement();
            msgId =  ((Element)(rootNode.getChildren("Result").get(0))).getAttributeValue("msgId");

            if("0".equals(msgId)) {
                userId = Integer.valueOf(((Element)(rootNode.getChildren("Result").get(0))).getChildText("userId"));
                userName = ((Element)(rootNode.getChildren("Result").get(0))).getChildText("userName");
                return true;
            } else {
                setMsg(((Element) (rootNode.getChildren("Result").get(0))).getAttributeValue("msg"));
            }
        } catch (IOException e) {
            log.error("解析消息出错", e);
        } catch (JDOMException e) {
            log.error("解析消息出错", e);
        }
        return false;
    }

    private boolean valifyTocken(String mobileValifyTokenId, String number){
        if(mobileValifyTokenId != null && mobileValifyTokenId.equals(RedisContext.getRedisCache().get(CacheKeyConstant.SSO.MOBILE_TOKENID_PREFIX + number))) {
            return true;
        }
        setMsg("操作超时，请重新发送短信验证");
        return false;
    }

    public String getFmtCallback() {
        if (StringUtil.isBlank(callbackUrl)) {
            return null;
        }
        String split = "?";
        if (callbackUrl.indexOf("?") != -1) {
            split = "&";
        }
        StringBuffer sb = new StringBuffer();
        sb.append(callbackUrl);
        if ("1".equals(needCode) ) {
            String result = null;
            try {
                result = HttpClientUtils.doPost(getGetTicketWithoutLoginUrl(),buildParamsForGetTicketWithoutLogin(userId));
                String code = parseResultForGetTicketWithoutLogin(result);
                if(StringUtil.isNotBlank(code)) {
                    sb = sb.append(split).append("msg=succ&code=").append(code);
                } else {
                    sb = sb.append(split).append("msg=").append(getErrorMsg());
                }
                split="&";
            } catch (Exception e) {
                log.error("获取app免登code失败",e) ;
            }

        }
        sb.append(split).append("ticketId=").append(ticket)
                .append("&tockenId=").append(tockenId);

        return sb.toString();
    }

    private Map<String, String> buildParamsForGetTicketWithoutLogin(Integer memberId){
        String time = DateUtil.getCurrentDateStamp();
        Map<String, String> paramMap = new TreeMap<String, String>();
        paramMap.put("memberId", String.valueOf(memberId));
        paramMap.put("siteId", getSiteId());
        paramMap.put("time", time);
        paramMap.put("sign", buildSign(paramMap, getGetTicketWithoutLoginMd5Key()));
        return paramMap;
    }

    private String parseResultForGetTicketWithoutLogin(String xmlText) {
        String code = "";
        SAXBuilder builder = new SAXBuilder();
        StringReader strReader = new StringReader(xmlText);
        try {
            Document document =builder.build(strReader);
            Element rootNode = document.getRootElement();
            String msgId =  ((Element)(rootNode.getChildren("Result").get(0))).getAttributeValue("msgId");
            setErrorMsg(((Element)(rootNode.getChildren("Result").get(0))).getAttributeValue("msg"));
            if("0".equals(msgId)) {
                code = ((Element) (rootNode.getChildren("Result").get(0))).getChildText("ticket");
            }
        } catch (IOException e) {
            log.error("解析消息出错", e);
        } catch (JDOMException e) {
            log.error("解析消息出错", e);
        }
        return code;
    }

    private void initCss(String siteId) {
        String userAgent = ServletActionContext.getRequest().getHeader("User-Agent");
        if(!StringUtil.isBlank(getCssSiteId())) {
            siteId = getCssSiteId();
        }
        loginCss = CssContext.getCssBySiteIdAndBussCode(siteId, "login");
        outOauthCss = CssContext.getCssBySiteIdAndBussCode(siteId, "outOauthCss");
        html5Css = CssContext.getCssBySiteIdAndBussCode(siteId, "html5Css");
        //缓存存的引用
        outOauthDirs = new ArrayList<>(loginElementService.getOutOauthDirList(siteId));
        if(userAgent != null && userAgent.contains("Mobile") && !userAgent.contains("MicroMessenger")) {
            for(int i = 0;i < outOauthDirs.size() ;i++) {
                if("微信".equals(outOauthDirs.get(i).getDirName())) {
                    outOauthDirs.remove(i);
                    break;
                }
            }
        }
        privacyCss = CssContext.getCssBySiteIdAndBussCode(siteId, CacheKeyConstant.Css.PRIVACY_AGREEMENT);
        //作业单元没有配置则读取passport的隐私政策
        if(privacyCss == null){
            privacyCss = CssContext.getCssBySiteIdAndBussCode(CacheKeyConstant.SiteId.PASSPORT, CacheKeyConstant.Css.PRIVACY_AGREEMENT);
        }
    }

    public String getSite_id() {
        return site_id;
    }

    public void setSite_id(String site_id) {
        this.site_id = site_id;
    }

    public String getBindAsLoginAccountMd5Key() {
        return bindAsLoginAccountMd5Key;
    }

    public void setBindAsLoginAccountMd5Key(String bindAsLoginAccountMd5Key) {
        this.bindAsLoginAccountMd5Key = bindAsLoginAccountMd5Key;
    }

    public String getGetTicketWithoutLoginMd5Key() {
        return getTicketWithoutLoginMd5Key;
    }

    public void setGetTicketWithoutLoginMd5Key(String getTicketWithoutLoginMd5Key) {
        this.getTicketWithoutLoginMd5Key = getTicketWithoutLoginMd5Key;
    }

    public String getGetTicketWithoutLoginUrl() {
        return getTicketWithoutLoginUrl;
    }

    public void setGetTicketWithoutLoginUrl(String getTicketWithoutLoginUrl) {
        this.getTicketWithoutLoginUrl = getTicketWithoutLoginUrl;
    }

    public String getBindAsLoginAccountUrl() {
        return bindAsLoginAccountUrl;
    }

    public void setBindAsLoginAccountUrl(String bindAsLoginAccountUrl) {
        this.bindAsLoginAccountUrl = bindAsLoginAccountUrl;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public String getTicket() {
        return ticket;
    }

    public void setTicket(String ticket) {
        this.ticket = ticket;
    }

    public String getTockenId() {
        return tockenId;
    }

    public void setTockenId(String tockenId) {
        this.tockenId = tockenId;
    }

    public String getMsgId() {
        return msgId;
    }

    public void setMsgId(String msgId) {
        this.msgId = msgId;
    }

    public String getMobileActiveNo() {
        return mobileActiveNo;
    }

    public void setMobileActiveNo(String mobileActiveNo) {
        this.mobileActiveNo = mobileActiveNo;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getMobileValifyTokenId() {
        return mobileValifyTokenId;
    }

    public void setMobileValifyTokenId(String mobileValifyTokenId) {
        this.mobileValifyTokenId = mobileValifyTokenId;
    }

    public String getSiteId() {
        return siteId;
    }

    public void setSiteId(String siteId) {
        this.siteId = siteId;
    }

    public String getNeedCode() {
        return needCode;
    }

    public void setNeedCode(String needCode) {
        this.needCode = needCode;
    }


    protected MemberDao getMemberDao() {
        return (MemberDao) BaseStoneContext.getInstance().getBean("memberDao");
    }

    public String getCallbackUrl() {
        return callbackUrl;
    }

    public void setCallbackUrl(String callbackUrl) {
        this.callbackUrl = callbackUrl;
    }


    public String getLoginPage() {
        return loginPage;
    }

    public void setLoginPage(String loginPage) {
        this.loginPage = loginPage;
    }

    public String getCss() {
        return css;
    }

    public void setCss(String css) {
        this.css = css;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getEnv() {
        return env;
    }

    public void setEnv(String env) {
        this.env = env;
    }

    public String getExtData() {
        return extData;
    }

    public void setExtData(String extData) {
        this.extData = extData;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getMd5() {
        return md5;
    }

    public void setMd5(String md5) {
        this.md5 = md5;
    }

    public String getLoginSiteId() {
        return loginSiteId;
    }

    public void setLoginSiteId(String loginSiteId) {
        this.loginSiteId = loginSiteId;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getLoginType() {
        return loginType;
    }

    public void setLoginType(String loginType) {
        this.loginType = loginType;
    }

    public String getMobileLogin() {
        return mobileLogin;
    }

    public void setMobileLogin(String mobileLogin) {
        this.mobileLogin = mobileLogin;
    }

    public String getUserNameList() {
        return userNameList;
    }

    public void setUserNameList(String userNameList) {
        this.userNameList = userNameList;
    }

    public List<OutOauthDir> getOutOauthDirs() {
        return outOauthDirs;
    }

    public void setOutOauthDirs(List<OutOauthDir> outOauthDirs) {
        this.outOauthDirs = outOauthDirs;
    }

    public Css getLoginCss() {
        return loginCss;
    }

    public void setLoginCss(Css loginCss) {
        this.loginCss = loginCss;
    }

    public Css getOutOauthCss() {
        return outOauthCss;
    }

    public void setOutOauthCss(Css outOauthCss) {
        this.outOauthCss = outOauthCss;
    }

    public Css getHtml5Css() {
        return html5Css;
    }

    public void setHtml5Css(Css html5Css) {
        this.html5Css = html5Css;
    }

    public String getNumber() {
        return number;
    }

    public void setNumber(String number) {
        this.number = number;
    }

    public String getCssSiteId() {
        return cssSiteId;
    }

    public void setCssSiteId(String cssSiteId) {
        this.cssSiteId = cssSiteId;
    }

    public String getTgt() {
        return tgt;
    }

    public void setTgt(String tgt) {
        this.tgt = tgt;
    }

	public boolean isNeedSmsCheckCode() {
		return needSmsCheckCode;
	}

	public void setNeedSmsCheckCode(boolean needSmsCheckCode) {
		this.needSmsCheckCode = needSmsCheckCode;
	}

    public boolean isShowGt() {
        return showGt;
    }

    public void setShowGt(boolean showGt) {
        this.showGt = showGt;
    }

    public InterfaceService getInterfaceService() {
        return interfaceService;
    }

    public void setInterfaceService(InterfaceService interfaceService) {
        this.interfaceService = interfaceService;
    }

    public boolean isNeedImmediatelyReg() {
        return needImmediatelyReg;
    }

    public void setNeedImmediatelyReg(boolean needImmediatelyReg) {
        this.needImmediatelyReg = needImmediatelyReg;
    }

    public String getRegLink() {
        return regLink;
    }

    public void setRegLink(String regLink) {
        this.regLink = regLink;
    }

    public boolean isNeedRetrievePwd() {
        return needRetrievePwd;
    }

    public void setNeedRetrievePwd(boolean needRetrievePwd) {
        this.needRetrievePwd = needRetrievePwd;
    }

    public LoginElementService getLoginElementService() {
        return loginElementService;
    }

    public void setLoginElementService(LoginElementService loginElementService) {
        this.loginElementService = loginElementService;
    }

    public Css getPrivacyCss() {
        return privacyCss;
    }

    public void setPrivacyCss(Css privacyCss) {
        this.privacyCss = privacyCss;
    }

    public String getOptToken() {
        return optToken;
    }

    public void setOptToken(String optToken) {
        this.optToken = optToken;
    }

    public String getMemberName() {
        return memberName;
    }

    public void setMemberName(String memberName) {
        this.memberName = memberName;
    }

    public boolean isRefreshPage() {
        return refreshPage;
    }

    public void setRefreshPage(boolean refreshPage) {
        this.refreshPage = refreshPage;
    }
}
