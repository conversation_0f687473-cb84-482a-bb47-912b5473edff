package com.shunwang.baseStone.sender.imp;

import java.util.ArrayList;
import java.util.List;
import java.util.Properties;
import java.util.Random;

import com.shunwang.baseStone.sender.exp.EmailConfigExp;

/**
 * 邮件发送生成器
 * <AUTHOR>
 *
 */
public class EmailSenderGenerator {
	private String host;
	private String userName;
	private String password;
	private String fromName;
	private Integer port;
	private Properties javaMailProperties;
	private static List<JavaMailBaseSender> javaMailSenders = new ArrayList<JavaMailBaseSender>();
	/**
	 * 获取一个JavaMailBaseSender
	 * @return
	 */
	public static JavaMailBaseSender buildJavaMailSender(){
		int rand = new Random().nextInt(javaMailSenders.size());
		return javaMailSenders.get(rand);
	}
	
	/**
	 * 服务启动时加载JavaMailBaseSender
	 * @throws Exception
	 */
	public void init() throws Exception{
		String[] userNames = getUserName().trim().split("\\|");
		String[] passwords = getPassword().trim().split("\\|");
		String[] fromNames = getFromName().trim().split("\\|");
		if(userNames == null ||passwords==null ||fromNames == null || userNames.length != passwords.length || fromNames.length != passwords.length)
			throw new EmailConfigExp();
		for(int i=0;i<userNames.length;i++){
			JavaMailBaseSender javaMailSender = new JavaMailBaseSender();
			javaMailSender.setHost(host);
			javaMailSender.setPassword(passwords[i]);
			javaMailSender.setJavaMailProperties(javaMailProperties);
			javaMailSender.setPort(port);
			javaMailSender.setUsername(userNames[i]);
			javaMailSender.setFrom(fromNames[i]);
			javaMailSenders.add(javaMailSender);
		}
	}

	public String getHost() {
		return host;
	}

	public void setHost(String host) {
		this.host = host;
	}

	public Integer getPort() {
		return port;
	}

	public void setPort(Integer port) {
		this.port = port;
	}

	public Properties getJavaMailProperties() {
		return javaMailProperties;
	}

	public void setJavaMailProperties(Properties javaMailProperties) {
		this.javaMailProperties = javaMailProperties;
	}
	
	public String getFromName() {
		return fromName;
	}
	public void setFromName(String fromName) {
		this.fromName = fromName;
	}
	public String getUserName() {
		return userName;
	}
	public void setUserName(String userName) {
		this.userName = userName;
	}
	public String getPassword() {
		return password;
	}
	public void setPassword(String password) {
		this.password = password;
	}
}
