package com.shunwang.baseStone.sso.web;

import com.google.gson.Gson;
import com.opensymphony.xwork2.ActionContext;
import com.shunwang.baseStone.bussiness.dao.BussinessDao;
import com.shunwang.baseStone.bussiness.pojo.Bussiness;
import com.shunwang.baseStone.cache.CacheKeyConstant;
import com.shunwang.baseStone.cache.lock.CounterLock;
import com.shunwang.baseStone.cache.service.CacheService;
import com.shunwang.baseStone.checkCode.exp.CheckCodeExp;
import com.shunwang.baseStone.config.constants.ConfigOauthConstant;
import com.shunwang.baseStone.config.constants.MemberEnum;
import com.shunwang.baseStone.context.*;
import com.shunwang.baseStone.core.exception.BaseStoneException;
import com.shunwang.baseStone.core.exception.MsgNullExp;
import com.shunwang.basepassport.config.common.CssUtil;
import com.shunwang.basepassport.config.common.SiteInterfaceUtil;
import com.shunwang.basepassport.config.pojo.Css;
import com.shunwang.baseStone.loginelement.constants.LoginElementConstant;
import com.shunwang.baseStone.siteinterface.context.SiteContext;
import com.shunwang.baseStone.siteinterface.exception.IpLimitedExp;
import com.shunwang.baseStone.sso.apapter.UserOutsiteApapter;
import com.shunwang.baseStone.sso.apapter.WeixinOpenAuthAdapter;
import com.shunwang.baseStone.sso.constant.RandConstant;
import com.shunwang.baseStone.sso.constant.UserOutsiteConstant;
import com.shunwang.baseStone.sso.constant.UserTockenConstant;
import com.shunwang.baseStone.sso.context.*;
import com.shunwang.baseStone.sso.geetest.GeetestLib;
import com.shunwang.baseStone.sso.intfc.InterfaceService;
import com.shunwang.baseStone.sso.intfc.SmsCodeResponse;
import com.shunwang.baseStone.sso.intfc.pojo.SmsCode;
import com.shunwang.baseStone.sso.pojo.QrCodeResponse;
import com.shunwang.baseStone.sso.pojo.Ticket;
import com.shunwang.baseStone.sso.util.CommonUtil;
import com.shunwang.baseStone.sso.util.GsonUtil;
import com.shunwang.baseStone.useroutinterface.pojo.OutOauthDir;
import com.shunwang.basepassport.asynctask.AsyncTaskExecutor;
import com.shunwang.basepassport.asynctask.MemberCancelNoticeJob;
import com.shunwang.basepassport.binder.common.BinderConstants;
import com.shunwang.basepassport.binder.common.ErrorCode;
import com.shunwang.basepassport.binder.pojo.SendBinder;
import com.shunwang.basepassport.config.common.RichTextUtil;
import com.shunwang.basepassport.config.pojo.RichText;
import com.shunwang.basepassport.config.pojo.SiteInterface;
import com.shunwang.basepassport.key.common.UserRegisterKeyUtil;
import com.shunwang.basepassport.manager.bean.ReportEntity;
import com.shunwang.basepassport.manager.enums.RiskPartnerServiceEnum;
import com.shunwang.basepassport.manager.response.risk.RiskResponse;
import com.shunwang.basepassport.manager.response.risk.RiskResponseData;
import com.shunwang.basepassport.manager.service.bo.ReportProcessor;
import com.shunwang.basepassport.mobile.constants.ComplaintConstants;
import com.shunwang.basepassport.mobile.dao.MobileCheckCodeDao;
import com.shunwang.basepassport.user.common.*;
import com.shunwang.basepassport.user.dao.MemberAccountBindDao;
import com.shunwang.basepassport.user.dao.MemberDao;
import com.shunwang.basepassport.user.dao.ServiceNotifyDao;
import com.shunwang.basepassport.user.exception.UsernameOrPwdErrorExp;
import com.shunwang.basepassport.user.pojo.*;
import com.shunwang.basepassport.user.service.BaseXmlResponse;
import com.shunwang.basepassport.user.service.DcReportService;
import com.shunwang.basepassport.user.service.MultipleAccountBindService;
import com.shunwang.basepassport.user.service.RiskService;
import com.shunwang.basepassport.util.WeakPasswordUtil;
import com.shunwang.basepassport.weixin.constant.WeixinConstant;
import com.shunwang.basepassport.weixin.service.WeixinMsgService;
import com.shunwang.framework.struts2.action.BaseAction;
import com.shunwang.util.OS.OsUtil;
import com.shunwang.util.date.DateUtil;
import com.shunwang.util.encrypt.AesEncrypt;
import com.shunwang.util.encrypt.Md5Encrypt;
import com.shunwang.util.encrypt.RsaEncrypt;
import com.shunwang.util.lang.StringUtil;
import com.shunwang.util.net.CookieUtil;
import com.shunwang.util.net.HttpClientUtils;
import com.shunwang.util.net.IpUtil;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.apache.struts2.ServletActionContext;
import org.jdom.Document;
import org.jdom.Element;
import org.jdom.JDOMException;
import org.jdom.input.SAXBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.PrintWriter;
import java.io.StringReader;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.TimeUnit;

public class LoginAction extends BaseAction implements ReportProcessor {

	private static final long serialVersionUID = -2740760971876416062L;
	private static final Logger logger = LoggerFactory.getLogger(LoginAction.class);
	private static final Gson GSON = new Gson() ;
	private int rand = RandConstant.rand(20000);
	//嵌入sso可传参数
	private String site_id;
	private String callbackUrl = "";
	private String version;
	private String env;
	private String extData;
	private String reportData;
	private String bodyClassName;/**用于业务方自定义极验样式定制**/
	private String login_rbt = "t";/**用于控制是否加载游戏数据上报脚本**/
	private String cssSiteId;/** css展示站点id，优先取传入的站点，为空时取登录的site_id取样式 */
	private String loginType = "normalLogin";// 默认为普通登录
	private String loginMode;/** 用于区别普通登录和手机短信快速登录（由于历史原因这里只针对web页面） */
	private String tgt ;// 是否覆盖父页面（默认为覆盖，如果传self则不覆盖，这个参数目前一般指的是回调、跳转之类）
	private String tgtCancel;// 用于注销页面的跳转 默认全局，需要内部跳转时传self
	private String linkTgt ;//是否在新开页面打开链接（如立即注册和忘记密码等）
	private String mobileLogin;//是否默认手机快捷登陆（由于历史原因这里只针对H5页面）
	private String loginCssUrl;// 可以传入loginCSS的样式，后台则不再取配置
	private String hideHeader ;//是否隐藏头部（h5的头部)，默认不隐藏，传true为隐藏
	private String siteScene;
	private Integer guestType;/**游客账号 空或者0表示非游客 1表示计费游客**/
	private Integer guestLogin;/**游客登录 空或者0表示非游客 1表示游客登录**/

	private Integer smsSendExpTime;/**短信倒计时时间**/
	private Integer checkTimeInterval = 2;/**控制二维码查询状态间隔**/

	private String businessScene;/**内部业务逻辑使用，场景一：计费免登二维码场景值，场景二：H5绑定手机完成登录页在验证手机后使用, 场景三：小程序扫码上报插件数据**/
	private Integer dllSwitch = 0;//插件开关 dllSwitch&2==2表示不加载ssoAuth授权 dllSwitch&4==4表示不加载非ssoAuth授权
	private String idCardName;/**多账号登录在开启计费账号登录时，同步将计费账号提交到后台**/
	private String authFreeType;/**多账号关联网吧账号免登1:默认  2授权网吧账号  4不授权网吧账号**/
	private Integer qrCodeExpireSeconds;//小程序上报数据缓存时间，同小程序有效时间

	//表单提交参数
	private String checkCode = "";
	private String userName = "";
	private String password = "";
	//插件登录时，使用的是明文，登录时需要对密码加密，对应代码在login.js的loginDefaultClick方法
	private boolean md5;
	private String dynamicPwd;
	//手机短信快捷登录相关
	private String mobileActiveNo;
	//升级兼容,后续可以删除
	private String smsNumber;
	private String smsMobileActiveNo;
	private String mobileValifyTokenId;
	private String userNameList;
	private Integer userId;
	private String number;
	private String mobileTail;
	//第三方登录
	private String unionid;
	private String openId;
	private String sign;
	private String appId;
	/**  标识用户密码是否是弱密码 1弱密码 2非弱密码 */
	private String weakPwdState;

	/** 需要认证的外部站点id */
	private String loginSiteId;

	//后台配置参数
	/** * 密码输错一定次数后会强制开启，即使后台关闭了验证码 */
	private boolean needCheckCode = true;
	private String needCode;

	//处理生成数据
	private String ticket;
	private String tockenId;
	private String clientTicket;
	//主要用户页面展示类似F1浏览器，网维等
	private String site_name;
	private String loginPage;
	//用于传递第三方登录时授权地址到页面进行跳转
	private String serviceUrl;
	private int count;
	private Boolean needDynamicPwd = Boolean.TRUE;
	private List<OutOauthDir> showOutOauthDirList;
	//用于帐号注销业务
	private String memberName;
	private Css loginCss;
	private Css outOauthCss;
	private Css html5Css;
	private Css privacyCss; //读取隐私政策url配置
	private Css userAgreementCss;//用户协议
	private List<OutOauthDir> outOauthDirs = new ArrayList<OutOauthDir>();

	private boolean needImmediatelyReg = false;
	private boolean needSmsQuickLogin = false;
	private boolean needSmsCheckCode = false;
	private boolean needRetrievePwd = false;
	private boolean needAPPQrCode = false;//app扫码登录
	private boolean singleGeetestSwitch = false;//单账号开关
	private String  regLink;
	private String appName;//APP扫码登录页APP名称
	private Boolean isFreeLoginFlag = Boolean.FALSE;
	private Boolean showOauthPageFlag = Boolean.FALSE;
	private Boolean noSsoAuthFreeLoginFlag = Boolean.FALSE;
	private Boolean unAuthAgreementFlag = Boolean.FALSE;
	/** 网维客户端自动登录写数据开关 */
	private Boolean isFreeLoginWriteDataFlag = Boolean.FALSE;
	/** 数据上报开关 */
	private boolean needReportData = false;

	private Boolean isSingleAccount = Boolean.FALSE;
	private String singleBindSign ;

	private String optToken;
	//当前页面是否需要刷新（目前用于注销撤销页面的跳转）
	private boolean refreshPage = false;
	//H5单账号绑定页面显示使用
	private String nickName;
	private String headImage;
	private Member member;

	//访问interface时使用的siteid
	private String dynamicMd5Key;
	private String dynamicAesKey;
	private String effectiveTime;
	private String whitelistUnsafe;
	private String getTicketWithoutLoginUrl;
	private String getTicketWithoutLoginMd5Key;
	private String siteId;
	// interface end
	// TODO 未知用途
	private String appKey;
	// TODO 无使用场景，建议后续清除
	private String css;
	//TODO 没找到使用 后期删除
	private String type;

	private BussinessDao bussinessDao;
	private MemberAccountBindDao memberAccountBindDao ;
	private InterfaceService interfaceService;
	private LoginElementService loginElementService;
	private RichText agreementConfig ;
	private ServiceNotifyDao serviceNotifyDao;
	private WeixinMsgService weixinMsgService;
	private WeixinOpenAuthAdapter weixinOpenOauth;
	private MultipleAccountBindService multipleAccountBindService;

	@Override
	public String execute() throws Exception {
		ServletActionContext.getResponse().addHeader("P3P", "CP=CAO PSA OUR");
		Boolean isChecked = Boolean.FALSE;
        RiskResponse riskResponse = null;
		try {
			checkSite();
			try {
				initLoginElement(site_id);
			} catch (Exception e) {
				logger.error("初始化登录配置异常", e);
			}
			if (userName.equals("") || password.equals("")) {
				return inputView();
			}

			checkIp();
			
			initParams();
			
			if (needCheckCode) {
				try {
					if (!GeetestLib.validate(getRequest())) {
						setMsg("验证失败");
						return inputView();
					}
					isChecked = Boolean.TRUE;
				} catch (Exception e) {
					logger.error("极验校验异常", e);
					setMsg("验证异常");
					return inputView();
				}
			}

			member = UserRegisterKeyUtil.getMember(userName);
            riskResponse = RiskService.checkRisk(RiskPartnerServiceEnum.PartnerService.SSO_LOGIN,
					member == null ? userName : member.getMemberName(), IpUtil.getIpAddress(ServletActionContext.getRequest()), null, null);

			if (member == null) {
				if (UserCheckUtil.checkEmail(userName)) {
					setMsg("邮箱登录功能未开启！");
				} else if (UserCheckUtil.checkMobile(userName)) {
					setMsg("手机登录功能未开启！");
				} else {
					throw new UsernameOrPwdErrorExp();
				}
				return inputView();
			}
			
			//判断用户是否禁用
			if (member.getMemberState().intValue() == MemberConstants.USER_FORBID_STATE) {
				setMsg("用户登录问题，请联系客服！");
                return inputView();
			}

            Integer logCnt = queryErrPwdCnt(member);
			boolean isShowGt = isGtSwitchOpen();
			if (!isChecked && isShowGt && (needCheckCode || logCnt > count)) {
				try {
					if (!GeetestLib.validate(getRequest())) {
						this.setNeedCheckCode(true);
						setMsg("验证失败");
						return inputView();
					}
				} catch (Exception e) {
					logger.error("极验校验异常", e);
					this.setNeedCheckCode(true);
					setMsg("验证异常");
					return inputView();
				}
			}
			
			IPContext.setIp(IpUtil.getIpAddress(ServletActionContext.getRequest()));
			//rsa解密
			String privateKey = RedisContext.getResourceCache().getResourceValue(ComplaintConstants.RSA_CONFIG, ComplaintConstants.RSA_PRIVATE);
			this.password = RsaEncrypt.decrypt(this.password, privateKey);
			if (!md5) {
				this.password = Md5Encrypt.encrypt(this.password);
			}
			member.initLoginMsg(version, env, extData);
			member.login(this.password);
			if (jumpWeakPasswordCheck(getSite_id(), weakPwdState, password)) {
				createLoginCache(member, "identity");
				setMsg("密码强度弱，请先 <a href='https://i.kedou.com/member/toPwdReset?ticketId=" + getTicket() + "&tockenId=" + getToken() + "' target='_blank'>修改</a> 哈～");
				return inputView();
			}
			member.updateWeakPwdState(weakPwdState);

			doReport(member.getMemberId(), ReportEntity.InterfaceType.login);
			loginMode = "passwordLoginType";
			if(isSingleAccount){
				MemberAccountBind memberAccountBind = memberAccountBindDao.getByMemberId(member.getMemberId());
				if(null == memberAccountBind){
					if(member.getMobileAsLoginAccount()) {
						createSingleAccount() ;
					}else{
						return singleAccountBindView();
					}
				}
			}
			// 动态密码用户跳转到验证动态密码页面
            if (member.getIsBindDynamicPwd()) {
			    if (needDynamicPwd) {
					Integer smsDynamic = ExpTimeContext.getExpTime(ExpTimeContext.ExpType.SMS_DYNAMIC, site_id);
					if (smsDynamic != null && smsDynamic > 0) {
						smsSendExpTime = smsDynamic;
						setMsg("请勿频繁操作" + smsDynamic + "秒后再试");
					} else {
						BaseXmlResponse<SmsCode> response = interfaceService.sendDynamicPassword(member);
						mobileTail = member.getMobile().substring(7);
						if (response.isSuccess()) {
							if (response.getValue().isSend()) {
								ExpTimeContext.initOneMinute(ExpTimeContext.ExpType.SMS_DYNAMIC, site_id);
								smsSendExpTime = 60;
							} else {
								setMsg("验证码有效期为" + response.getValue().getValidTime() + "分钟，请直接输入");
							}
						}
					}
					initCss(site_id);
					return "dynamicPwd";
				}
			}


			createLoginCache(member);
            if (riskResponse != null && riskResponse.isSuccess() && !riskResponse.getData().isOk() && isShowGt) {
                return validView(riskResponse, false);
            } else {
				CommonUtil.addCookieAfterLogin(getRequest(), getResponse(), getTockenId(), member.getMemberId());
				UserLoginSessionUtil.saveSession(member.getMemberName(), UserLoginSessionUtil.LoginType.SSO_PWD.getType(), null, site_id);
				logger.info(member.getMemberName() + " UserTocken = " + getTockenId());
            }
			if (!StringUtil.isBlank(authFreeType)) {
				if (!MemberConstants.AuthFreeType.NORMAL.equalsIgnoreCase(authFreeType) && StringUtil.isNotBlank(idCardName)) {
					UserLoginSessionUtil.saveMasterSession(idCardName, member.getMemberName(), UserLoginSessionUtil.LoginType.SSO_PWD.getType(), null, site_id, false);
					boolean ssoUnAuthFreeLoginSwitch = UserLoginSessionUtil.ssoUnAuthFreeLoginSwitch(site_id);
					if (MemberConstants.AuthFreeType.AUTH.equalsIgnoreCase(authFreeType) && ssoUnAuthFreeLoginSwitch) {
						multipleAccountBindService.bindAccountIfNecessary(idCardName, member, site_id);
						DcReportService.freeLoginReport(member.getMemberName(), DcReportService.OptTypeAndExtInfo.MULTIPLE_AUTH_NORMAL_SUCCESS, getSite_id());
					} else {
						DcReportService.freeLoginReport(member.getMemberName(), DcReportService.OptTypeAndExtInfo.MULTIPLE_UN_AUTH_NORMAL, getSite_id());
					}
				}
			}
            //判断用户是否是初始化密码未修改
            MemberUnsafe temp = new MemberUnsafe();
            temp.setMemberId(member.getMemberId());
            temp = member.getMemberUnsafeDao().getMemberUnsafe(temp);
            if (temp != null && temp.getMemberType().equals(MemberConstants.MEMBERINIPWD_MEMBERTYPE_PWDUPDED)) {
                setUnsafeUrl();
                return "unsafe_success";
            }
			// 判断用户是否在疑似盗号表里
			if(!checkIsWhitelistSite(site_id)) {
				if (temp != null && temp.getMemberType().equals(MemberConstants.MEMBERUNSAFE_MEMBERTYPE_UNSAFE)) {
					setUnsafeUrl();
					return "unsafe_success";
				}
			}
		} catch (BaseStoneException e) {
			if (ErrorCode.C_1068.getCode().equals(e.getMsgId())) {
				setMsg("您的账号已被注销！");
				memberName = member.getMemberName();
				return inputCancelView();
			}
			boolean isShowGt = isGtSwitchOpen();
			if(needCheckCode) {
				this.setNeedCheckCode(true);
			} else if (null != member) {
                if (riskResponse != null && riskResponse.isSuccess() && !riskResponse.getData().isOk() && isShowGt) {
                    return validView(riskResponse, true);
                }
				int logCnt = queryErrPwdCnt(member);
				if(logCnt >= count) {
					this.setNeedCheckCode(isShowGt);
				}
			}
            this.setMsg(e.getMessage());
			loginMode = "passwordLoginType";
			return  inputView();
		}
		return SUCCESS;
	}

	/**
	 * 账号密码登录时预检测账号及安全
	 * @return 检测结果
	 */
	@SneakyThrows
    public String preCheckForLogin() {
		Map<String, Object> result = new HashMap<>();
		member = UserRegisterKeyUtil.getMember(userName);
		if (member == null) {
			if (UserCheckUtil.checkEmail(userName)) {
				result.put("code", -1);
				result.put("msg", "邮箱登录功能未开启！");
			} else if (UserCheckUtil.checkMobile(userName)) {
				result.put("code", -1);
				result.put("msg", "手机登录功能未开启！");
			} else {
				result.put("code", -1);
				result.put("msg", "用户名或密码不正确！");
			}
		} else if (member.getMemberState().intValue() == MemberConstants.USER_FORBID_STATE) {
			result.put("code", -1);
			result.put("msg", "用户登录问题，请联系客服！");
		} else {
			initCheckCodeInfo(loginElementService.getLoginConfig(siteId, LoginElementConstant.CHECK_CODE));
			int logCnt = queryErrPwdCnt(member);
			if (isGtSwitchOpen() && logCnt > count) {
				result.put("code", 1);
				result.put("msg", "需要拉起极验");
			} else {
				result.put("code", 0);
				result.put("msg", "success");
			}
		}
		PrintWriter out = this.getResponse().getWriter();
		getResponse().setContentType("text/json;charset=UTF-8");
		out.write(GsonUtil.GsonString(result));
		return null;
	}

	/**
	 * 账号密码登录时预检测账号及安全
	 * @return 检测结果
	 */
	@SneakyThrows
	public String reportDllData() {
		Map<String, Object> result = new HashMap<>();
		if (StringUtil.isNotBlank(businessScene) && StringUtil.isNotBlank(memberName)) {
			Map<String, String> extMap = RedisContext.getRedisCache().getMap(WeixinConstant.REQS_EXT_DATA_KEY + businessScene, String.class, String.class);
			if (extMap != null) {
				extMap.put("idCardName", memberName);
				RedisContext.getRedisCache().setMap(WeixinConstant.REQS_EXT_DATA_KEY + businessScene, extMap, qrCodeExpireSeconds, TimeUnit.SECONDS);
			}
		}
		result.put("code", 0);
		result.put("msg", "success");
		PrintWriter out = this.getResponse().getWriter();
		getResponse().setContentType("text/json;charset=UTF-8");
		out.write(GsonUtil.GsonString(result));
		return null;
	}

    /**
     * 创建两票一token(ticket,tockenId,clientTicket)
     */
	private void createLoginCache(Member member) {
        createLoginCache(member, getSite_id());
    }

	/**
	 * 创建两票一token(ticket,tockenId,clientTicket)
	 */
	private void createLoginCache(Member member, String siteId) {
		this.setTicket(TicketContext.createTicket(member, siteId).toString());
		this.setTockenId(UserTockenContext.createTocken(member, siteId).toString());
		this.setClientTicket(ClientTicketContext.createClientTicket(member,siteId).toString());
	}

	private boolean isGtSwitchOpen() {
		return RedisContext.getSwitchConfig(CacheKeyConstant.ConfigResourcesConstants.TYPE_BASE_CONFIG,
				CacheKeyConstant.ConfigResourcesConstants.BASE_CONFIG_GT_SWITCH, false);
	}

    /**
     * 获取密码错误次数
     * @param member 用户临时对象
     * @return 密码错误次数
     */
    private int queryErrPwdCnt(Member member) {
        String pwdErrKey = member.getPwdErrKey(member.getMemberName());
        Integer logCnt = RedisContext.getRedisCache().get(pwdErrKey,Integer.class);
        if(logger.isInfoEnabled()){
            logger.info("[redis get]getErrNum ,key=" + pwdErrKey + ",value=" + logCnt);
        }
        if( logCnt == null){
            logCnt = 0;
        }
        return logCnt;
    }

	private boolean jumpWeakPasswordCheck(String siteId, String weakPwdState, String password) {
		List<String> sites = RedisContext.getStringListConfig(CacheKeyConstant.ConfigResourcesConstants.COMMON_CONFIG,
				CacheKeyConstant.ConfigResourcesConstants.JUMP_WEAK_PASSWORD_ERROR_SITES);
		if (sites.contains(siteId)) {
			return MemberConstants.WEAK_PWD_STATE_3.equals(weakPwdState) ||
					MemberConstants.WEAK_PWD_STATE_1.equals(weakPwdState) && WeakPasswordUtil.weakPasswordCheck(password);
		}
		return false;
	}

    /**
     * 登录极验二次校验处理
     * @return null 实际内容通过response的write输出
     * @throws IOException io错误
     */
	public String geetestValid() throws IOException {
        if(GeetestLib.validate(getRequest())) {
            String key = CacheKeyConstant.InterfaceToken.VALID_SIGN+ optToken;
            Map<String, String> map =RedisContext.getRedisCache().getMap(key, String.class, String.class);
            this.setTicket(map.get("ticket"));
            this.setTockenId(map.get("tokenId"));
            this.setClientTicket(map.get("clientTicket"));
            if (StringUtil.isNotBlank(map.get("pwdErr"))) {
                getResponse().getWriter().write("{\"code\":false,\"msg\":\"" + "用户名或密码不正确！" + "\"}");
                return null;
            }
            if (StringUtil.isNotBlank(this.getTicket()) && StringUtil.isNotBlank(this.getTockenId())) {
                getResponse().getWriter().write("{\"code\":true,\"msg\":\"" + "验证成功" + "\",\"fmtCallback\":\"" + getFmtCallback() + "\"}");
                return null;
            }
        }
        getResponse().getWriter().write("{\"code\":false,\"msg\":\"" + "验证失败" + "\"}");
        return null;
    }

	/**
	 * 传入单账号的sign或者ticket跳到对应页面
	 * @return 指定业务result标识
	 */
	public String loginCallback(){
		// 单账号关系
		if(StringUtils.isNotBlank(singleBindSign)){
			initCss(site_id);
            //初始化极验信息
			initSingleGeetestSwitch();
			return "singleAccountBind" ;
		}else if(StringUtils.isNotBlank(ticket) && StringUtils.isNotBlank(tockenId)){ //普通登录

			initLoginElement(site_id);
			return SUCCESS ;
		}
		return null ;
	}

	/**
	 * 传入ticket跳到对应页面
	 * @return 指定业务result标识
	 */
	public String autoLoginCallback(){
		// 单账号关系
		if(StringUtils.isNotBlank(ticket) && StringUtils.isNotBlank(tockenId)){ //普通登录
			initLoginElement(site_id);
			Ticket ticketById = TicketContext.getTicketById(this.ticket);
			if (ticketById == null) {
				logger.error("票据不存在或已失效，不跳转");
				setMsg("票据不存在或已失效，请重试");
				return inputView();
			}
			if (!isSingleAccount) {
				multipleAccountBindService.bindAccountIfNecessary(ticketById.getUserId(), getSite_id());
				DcReportService.freeLoginReport(ticketById.getUserName(), DcReportService.OptTypeAndExtInfo.FREE_LOGIN_HEAD_AUTH_FOR_MULTIPLE_SUCCESS, getSite_id());
				return SUCCESS;
			}
			//主账号时需要发送微信消息
			if (ticketById.isMaster()) {
				weixinMsgService.sendAutoLoginTemplateMsg(ticketById.getUserId(), ticketById.getUserName(), getSite_id());
			}
			DcReportService.freeLoginReport(ticketById.getUserName(), ticketById.isMaster() ? DcReportService.OptTypeAndExtInfo.FREE_LOGIN_HEAD_AUTH_SUCCESS :
					DcReportService.OptTypeAndExtInfo.FREE_LOGIN_GUEST_HEAD_AUTH_SUCCESS, getSite_id());
			return SUCCESS ;
		}
		return inputView() ;
	}

	private void createSingleAccount() {
		MemberAccountBind memberAccountBind = new MemberAccountBind() ;
		memberAccountBind.setMemberId(member.getMemberId()) ;
		memberAccountBind.setPhone(member.getMobile()) ;
		interfaceService.singleAccountBindExt(memberAccountBind);
	}

	private String createSingleBindSign(Member member){
		String sign = UUID.randomUUID().toString();
		String key = CacheKeyConstant.InterfaceToken.SINGLE_ACCOUNT_SIGN+ sign;
		SingleAccountToken singleAccountToken = new SingleAccountToken() ;
		singleAccountToken.setType(ConfigOauthConstant.TYPE.NORMAL.getInt());
		singleAccountToken.setUnionId(member.getMemberId()) ;
		RedisContext.getRedisCache().set(key ,singleAccountToken, 30, TimeUnit.MINUTES);

		return sign ;
	}

    /**
     * 构建用于登录二次验证的sign，同时sign与登录票据及token关联
     * @return 签名串
     */
    private String createValidSign(boolean pwdErr){
        String sign = UUID.randomUUID().toString();
        String key = CacheKeyConstant.InterfaceToken.VALID_SIGN+ sign;
        Map<String, String> map = new HashMap<>();
        map.put("ticket", this.getTicket());
        map.put("tokenId", this.getTockenId());
        map.put("clientTicket", this.getClientTicket());
        map.put("pwdErr", pwdErr ? "true" : "");
        RedisContext.getRedisCache().set(key ,map, 5, TimeUnit.MINUTES);
        return sign ;
    }

	private String inputView() {
        initCss(site_id);
        CookieUtil.addSecureFlag(getResponse());
		if(isMobileByEnv()) {
			return "inputForHtml5";
		}
		return "input";
	}

    private String singleAccountBindView() {
        // 要求去绑定手机作为登录账号
        initCss(site_id);
        setSingleBindSign(createSingleBindSign(member));
        initSingleGeetestSwitch();
		Integer smsDynamic = ExpTimeContext.getExpTime(ExpTimeContext.ExpType.SMS_SINGLE_BIND, site_id);
		if (smsDynamic != null && smsDynamic > 0) {
			smsSendExpTime = smsDynamic;
		}
        if (OsUtil.isMobile(getRequest().getHeader("User-Agent"))) {
            return "singleAccountBindForH5";
        }
        return "singleAccountBind";
    }

    /**
     * 已注销用户登录时跳转到可撤销注销的页面
     * @return 返回注销页面
     */
    private String inputCancelView() {
        initCss(site_id);
        try {
            optToken = AesEncrypt.Encrypt(memberName + site_id, MemberUtil.getKey()).substring(8);
        } catch (Exception e) {
			logger.error("已注销用户登录跳撤销页面数据异常");
        }
        if(isMobileByEnv()) {
            return "inputCancelForHtml5";
        }
        return "inputCancel";
    }

    /**
     * 密码登录风控跳转至二次校验页面
     * @param riskResponse 风控结果
     * @param pwdErr 密码是否错误
     * @return 返回风控页面
     */
    private String validView(RiskResponse riskResponse, boolean pwdErr) {
		logger.info("风控结果[{}]" + riskResponse.getJson());
        RiskResponseData data = riskResponse.getData();
        initCss(site_id);
        optToken = createValidSign(pwdErr);
//        if (data.getLevel() <= 2) {
            return "geetestValided";
//        }
//        return "smsValid";
    }

	private boolean isMobileByEnv() {
		return LoginElementConstant.ENV_TYPE.equalsIgnoreCase(env);
	}

	public String sendSmsActiveNo() throws IOException {
        try {
        	if (StringUtil.isBlank(site_id)) {
				buildSmsJson(false, "参数异常");
				return null;
			}
			Integer smsTime = ExpTimeContext.getExpTime(ExpTimeContext.ExpType.SMS_LOGIN, site_id);
			if (smsTime != null && smsTime > 0) {
				buildSmsJson(false, "一分钟限制一次，请" + smsTime + "秒后再试");
				return null;
			}
			//初始化短信登录验证码
			setNeedSmsCheckCode(configIsOpen(loginElementService.getLoginConfig(site_id, LoginElementConstant.SMS_CHECK_CODE))
					&& isGtSwitchOpen());
			//手机短信登录校验码开启
			if (needSmsCheckCode) {
				try {
					if (!GeetestLib.validate(getRequest())) {
						buildSmsJson(false, "验证失败，请刷新重试");
						return null;
					}
				} catch (Exception e) {
					buildSmsJson(false, "验证异常，请联系客服");
					return null;
				}
			}
			this.number = decryptData(StringUtil.isNotBlank(this.smsNumber) ? this.smsNumber : this.number, 11);
			Map<String, String> paramsMap = buildParamsForSendActiveNo();
			if (SendBinder.checkNeedSpecialDeal(getSite_id())) {
				paramsMap.put("terminal" , getSite_id() + "PC");
			}

			BaseXmlResponse response = interfaceService.sendForMobileRegister(paramsMap);
			if("1206".equals(response.getMsgId())) {
				setMsg("今日短信发送次数已达上限");
			} else {
				setMsg(response.getMsg());
			}
			if(response.isSuccess()) {
				Member member = UserRegisterKeyUtil.getByMobile(number);
				boolean isCheck = (isAgreementSwitchOpen() || isSiteIdInWhitelist(site_id))
						&& member != null;
				//校验用户是否已注册, 已注册用户需要勾选 用户协议,反之则系统不勾选.
				doReport(member == null ? null : member.getMemberId(), ReportEntity.InterfaceType.reg);
				buildSmsJson(response.isSuccess(), isCheck);
				ExpTimeContext.initOneMinute(ExpTimeContext.ExpType.SMS_LOGIN, site_id);
			} else {
				buildSmsJson(response.isSuccess(), getMsg());
			}
        } catch (Exception e) {
        	logger.error("短信发送失败",e);
			buildSmsJson(false, "短信发送失败");
        }
        return null;
    }


	private void buildSmsJson(boolean result, String msg) {
		try (PrintWriter out = this.getResponse().getWriter()) {
			getResponse().setContentType("text/json;charset=UTF-8");
			out.write("{\"result\": " + result + ",\"msg\":\"" + msg + "\"}");
		} catch (Exception e) {
			logger.error("写返回数据异常", e);
		}
	}
	private void buildSmsJson(boolean result, String key, String value) {
		try (PrintWriter out = this.getResponse().getWriter()) {
			getResponse().setContentType("text/json;charset=UTF-8");
			out.write("{\"result\": " + result + ",\"" + key + "\":\"" + value + "\"}");
		} catch (Exception e) {
			logger.error("写返回数据异常", e);
		}
	}
	private void buildSmsJson(boolean result, boolean isReg) {
        try (PrintWriter out = this.getResponse().getWriter()) {
            getResponse().setContentType("text/json;charset=UTF-8");
            out.write("{\"result\": " + result + ",\"isReg\":\"" + isReg + "\"}");
        } catch (Exception e) {
            logger.error("写返回数据异常", e);
        }
	}
	
	public String outMobileSmsConfirm() {
        try {
        	initLoginElement(getSite_id());

			this.number = decryptData(StringUtil.isNotBlank(this.smsNumber) ? this.smsNumber : this.number, 11);
            CounterLock smsCheckLock = CacheService.newCounterLock("ANTI_ADDITION_LOGIN_" + number, 3, Duration.ofMinutes(1L));
            if (!smsCheckLock.tryLock()) {
                setMsg(ErrorCode.C_1102.getDescription());
                initCss(getSite_id());
                loginMode = "smsLoginType";
                return "input";
            }
			this.mobileActiveNo = decryptData(StringUtil.isNotBlank(smsMobileActiveNo) ? this.smsMobileActiveNo : this.mobileActiveNo, 6);
			Map<String, String> paramsMap = buildParamsForOutMobileConfirmUrl();
			if (SendBinder.checkNeedSpecialDeal(getSite_id())) {
				paramsMap.put("terminal" , getSite_id() + "PC");
			}

			BaseXmlResponse response = interfaceService.confirmForLogin(paramsMap);
            if(response.isSuccess()) {
				//优先从缓存中获取
				Member member = UserRegisterKeyUtil.getByMobile(number);
				userId = member.getMemberId();

				doReport(userId, ReportEntity.InterfaceType.login);

                createLoginCache(member);

				CommonUtil.addCookieAfterLogin(getRequest(), getResponse(), getTockenId(), member.getMemberId());

                loginMode = "smsLoginType";
                return SUCCESS;
            } else {
                if (ErrorCode.C_1068.getCode().equals(response.getMsgId())){
                    Member member = UserRegisterKeyUtil.getByMobile(number);
                    memberName = member.getMemberName();//撤销注销页面需要memberName
                    refreshPage = true;
                    return inputCancelView();
                }
            	if("1024".equals(response.getMsgId())) {
					setMsg("手机短信验证码错误");
				} else {
					setMsg(response.getMsg());
				}
                initCss(getSite_id());
                loginMode = "smsLoginType";
                return "input";
            }
        } catch (CheckCodeExp e) {
        	logger.error(e.getMessage(), e);
        	setMsg(e.getMessage());
        	initCss(getSite_id());
            loginMode = "smsLoginType";
            return "input";
        } catch (Exception e) {
        	logger.error("手机号"+number+"手机短信快捷登录验证码确认失败",e);
        	setMsg("系统异常!");
        	initCss(getSite_id());
            loginMode = "smsLoginType";
            return "input";
        }
    }

    public String sendSingleBindActiveNo() throws IOException {
		try {
			//初始化验证码
			initSingleGeetestSwitch();
			//单账号绑定页面强制使用验证码，与验证码开关无关
			if (singleGeetestSwitch) {
				try {
					if (!GeetestLib.validate(getRequest())) {
						buildSmsJson(false, "验证失败");
						return null;
					}
				} catch (Exception e) {
					buildSmsJson(false, "验证异常");
					return null;
				}
			}

			Map<String, String> paramsMap = buildParamsForSingleAccountBind();
			if (SendBinder.checkNeedSpecialDeal(getSite_id())) {
				paramsMap.put("terminal" , getSite_id() + "PC");
			}

			BaseXmlResponse response = interfaceService.sendForSingleAccountBind(paramsMap);
			if("1206".equals(response.getMsgId())) {
				setMsg("今日短信发送次数已达上限");
			} else {
				setMsg(response.getMsg());
			}

			getResponse().setContentType("text/json;charset=UTF-8");
			if(response.isSuccess()) {
				boolean isCheck = (isAgreementSwitchOpen() || isSiteIdInWhitelist(site_id))
						&& UserRegisterKeyUtil.getByMobile(number) != null;
				//校验用户是否已注册, 已注册用户需要勾选 用户协议,反之则系统不勾选.
				buildSmsJson(response.isSuccess(), isCheck);
				ExpTimeContext.initOneMinute(ExpTimeContext.ExpType.SMS_SINGLE_BIND, site_id);
			} else {
				setMsg(response.getMsg());
				buildSmsJson(response.isSuccess(), getMsg());
			}
		} catch (Exception e) {
			logger.error("短信发送失败",e);
			buildSmsJson(false, "短信发送失败");
		}
		return null;
	}

	public String comfirmSingleBindActionNo() throws IOException {
		PrintWriter out = this.getResponse().getWriter();
		getResponse().setContentType("text/json;charset=UTF-8");
		try {
			initLoginElement(getSite_id());

			Map<String, String> paramsMap = buildParamsForOutMobileConfirmForSingleAccountBind();
			if (SendBinder.checkNeedSpecialDeal(getSite_id())) {
				paramsMap.put("terminal" , getSite_id() + "PC");
			}
			String netBarProcess = weixinOpenOauth.canBindNetBar(number, businessScene);
			if (StringUtil.isNotBlank(netBarProcess)) {
				buildSmsJson(false, netBarProcess);
				return null;
			}
			BaseXmlResponse response = interfaceService.confirmForSingleAccountBind(paramsMap);
			if (response.isSuccess()) {
				//优先从缓存中获取
				Member member = UserRegisterKeyUtil.getByMobile(number);
				userId = member.getMemberId();
				createLoginCache(member);

				if (StringUtil.isNotBlank(businessScene)) {
					QrCodeResponse result = RedisContext.getRedisCache().get(businessScene, QrCodeResponse.class);
					if (result != null) {
						result.setType(QrCodeResponse.TypeStage.LOGIN_SUCCESS.getType());
						result.setStage(QrCodeResponse.TypeStage.LOGIN_SUCCESS.getStage());
						result.setTicket(this.getTicket());
						result.setTockenId(this.getTockenId());
						result.setClientTicket(getClientTicket());
						netBarProcess = weixinOpenOauth.bindNetBarProcess(member, businessScene, result, getSite_id());
					}
				}

				CommonUtil.addCookieAfterLogin(getRequest(), getResponse(), getTockenId(), member.getMemberId());
				if (StringUtil.isNotBlank(netBarProcess)) {
					buildSmsJson(false, netBarProcess);
				} else {
					buildSmsJson(true, "fmtCallback", getFmtCallback());
				}
			} else {
				if ("1024".equals(response.getMsgId())) {
					setMsg("手机短信验证码错误");
				} else {
					setMsg(response.getMsg());
					if (StringUtil.isNotBlank(businessScene)) {
						Map<String, String> extInfo = RedisContext.getRedisCache().getMap(CacheKeyConstant.SSO.INNER_SCAN + businessScene, String.class, String.class);
						if (extInfo != null) {
							DcReportService.freeLoginReport(extInfo.get("memberName"), DcReportService.OptTypeAndExtInfo.H5_BIND_BIND_ID_CARD_FAIL, getSite_id());
						}
					}
				}
				buildSmsJson(false, getMsg());
			}

		} catch (CheckCodeExp e) {
			logger.error(e.getMessage(), e);
			buildSmsJson(false, e.getMessage());
		} catch (Exception e) {
			logger.error("手机号"+number+"手机短信单账号绑定验证码确认失败",e);
			buildSmsJson(false, "系统异常");
		} finally {
			out.close();
		}
		return null;
	}


	private void doReport(Integer memberId, ReportEntity.InterfaceType interfaceType) {
		report(getSite_id(), memberId, interfaceType, reportData);
	}

	private Map<String, String> buildParamsForSendActiveNo(){
        String time = DateUtil.getCurrentDateStamp();
        Map<String, String> paramMap = new LinkedHashMap<>();
        paramMap.put("userName", "");
        paramMap.put("mobile", number);
        paramMap.put("newMobile", "");
        paramMap.put("interfaceType", BinderConstants.MOBLIE_REGISTER);
        paramMap.put("time", time);
		paramMap.put("accessSiteId", getSite_id());
        return paramMap;
    }
	
	private Map<String, String> buildParamsForOutMobileConfirmUrl(){
        Map<String, String> paramMap = new LinkedHashMap<>();

        paramMap.put("userName", "");
        paramMap.put("mobile", number);
        paramMap.put("mobileActiveNo", mobileActiveNo);
        paramMap.put("loginIp", IpUtil.getIpAddress(ServletActionContext.getRequest()));
        paramMap.put("version",version);
        paramMap.put("environment",env) ;
        paramMap.put("remark",extData) ;
		paramMap.put("accessSiteId", getSite_id());
        return paramMap;
    }

	private Map<String, String> buildParamsForSingleAccountBind(){
		Map<String, String> paramMap = new LinkedHashMap<>();
		paramMap.put("userName", "");
		paramMap.put("mobile", number);
		paramMap.put("newMobile", "");
		paramMap.put("accessSiteId", getSite_id());
		paramMap.put("singleAccountToken",singleBindSign) ;
		return paramMap;
	}

	private Map<String, String> buildParamsForOutMobileConfirmForSingleAccountBind(){
		Map<String, String> paramMap = new LinkedHashMap<>();

		paramMap.put("userName", "");
		paramMap.put("mobile", number);
		paramMap.put("mobileActiveNo", mobileActiveNo);
		paramMap.put("loginIp", IpUtil.getIpAddress(ServletActionContext.getRequest()));
		paramMap.put("accessSiteId", getSite_id());
		paramMap.put("version",version);
		paramMap.put("environment",env) ;
		paramMap.put("remark",extData) ;
		paramMap.put("singleAccountToken",singleBindSign) ;
		return paramMap;
	}

	/**
	 * *********** 创建日期: 2013-8-5 创建作者：JINBAO
	 * 
	 * @return String 功能：动态密码用户登录 ************
	 */
	public String dynamicPwdLogin() {
		ServletActionContext.getResponse().addHeader("P3P", "CP=CAO PSA OUR");
		try {
			if (StringUtil.isEmpty(userName)) {
				return getReturnUrl();
			}
			if (StringUtil.isBlank(dynamicPwd)) {
				setMsg("验证码不能为空");
				return getReturnUrl();
			}

			IPContext.setIp(IpUtil.getIpAddress(ServletActionContext.getRequest()));
			member = getMemberDao().getMember(userName);

			if (member.getIsBindDynamicPwd()) {
                BaseXmlResponse<SmsCode> response = interfaceService.dynamicSecurityValidate(member, dynamicPwd);

                SmsCodeResponse smsCodeResponse = new SmsCodeResponse(response);
                SmsCode smsCode = smsCodeResponse.getValue();
                if (smsCodeResponse.isNotExist() || smsCode == null) {
                    smsSendExpTime = 0;
                } else {
                    smsSendExpTime = ExpTimeContext.getExpTime(ExpTimeContext.ExpType.SMS_DYNAMIC, site_id);
                }
				if (smsCodeResponse.isIncorrect()) {
					setMsg("验证码不正确，请重新输入");
					return getReturnUrl();
				}
				if (smsCodeResponse.isExpired()) {
					setMsg("验证码已过期，请重新发送");
					return getReturnUrl();
				}

            } else {
				setMsg("登录错误未绑定顺令或动态密码");
				return getReturnUrl();
			}

			LogonLogUtil.saveLog(buildLogonLog(member));
			
			initLoginElement(site_id);
			if(isSingleAccount){
				MemberAccountBind memberAccountBind = memberAccountBindDao.getByMemberId(member.getMemberId());
				if(null == memberAccountBind){
					if(member.getMobileAsLoginAccount()) {
						createSingleAccount() ;
					}else{
						return singleAccountBindView();
					}
				}
			}
			createLoginCache(member);
			CookieUtil.setCookieUpgrade(this.getResponse(), UserTockenConstant.S_UserTockenKey, getTockenId());

			// 判断用户是否在疑似盗号表里
			if(!checkIsWhitelistSite(site_id)) {
				MemberUnsafe temp = new MemberUnsafe();
				temp.setMemberId(member.getMemberId());
				temp = member.getMemberUnsafeDao().getMemberUnsafe(temp);
				if (temp != null && temp.getMemberType().equals(MemberConstants.MEMBERUNSAFE_MEMBERTYPE_UNSAFE)) {
					setUnsafeUrl();
					return "unsafe_success";
				}
			}
			
			return SUCCESS;
		} catch (Exception e) {
			logger.error("登录失败", e);
			setMsg("登录失败");
			return getReturnUrl();
		}
	}

	/**
	 * 将疑似账号泄露用户转入通行证修改密码页面
	 */
	public void setUnsafeUrl() {
		try {
			setCallbackUrl(SsoDomainContext.getIdentityServer() + "/member/toPwdReset");
		} catch (Exception e) {
			logger.error("设置安全连接地址错误", e);
		}
	}

	private void initCss(String siteId) {
		if (StringUtil.isBlank(siteId)) {
			return ;
		}
		String userAgent = ServletActionContext.getRequest().getHeader("User-Agent");

		//缓存存的引用
        outOauthDirs = new ArrayList<>(loginElementService.getOutOauthDirList(siteId));
		//在移动端微信内需排除微信公众号，在微信外需排除微信授权
		if(userAgent != null && userAgent.contains("Mobile")) {
			boolean isWechat = userAgent.contains("MicroMessenger");
			for(int i = 0;i < outOauthDirs.size() ;i++) {
				if(!isWechat && "微信".equals(outOauthDirs.get(i).getDirName())) {
					outOauthDirs.remove(i);
					break;
				}
				if (isWechat && "微信公众号".equals(outOauthDirs.get(i).getDirName())) {
					outOauthDirs.remove(i);
					break;
				}
			}
		}

		privacyCss = CssUtil.loadCss(siteId, CacheKeyConstant.Css.PRIVACY_AGREEMENT);
		//作业单元没有配置则读取passport的隐私政策
		if(privacyCss == null){
			privacyCss = CssUtil.loadCss(CacheKeyConstant.SiteId.PASSPORT, CacheKeyConstant.Css.PRIVACY_AGREEMENT);
		}
        userAgreementCss = CssUtil.loadCss(siteId, CacheKeyConstant.Css.USER_AGREEMENT);
        //作业单元没有配置则读取passport的用户协议
        if(userAgreementCss == null){
            userAgreementCss = CssUtil.loadCss(CacheKeyConstant.SiteId.PASSPORT, CacheKeyConstant.Css.USER_AGREEMENT);
        }

		if(!StringUtil.isBlank(getCssSiteId())) {
			siteId = getCssSiteId();
		}
		if(StringUtil.isNotBlank(loginCssUrl)) {
			html5Css = new Css(siteId,loginCssUrl) ;
			loginCss = new Css(siteId,loginCssUrl) ;
		}else{
			loginCss = CssUtil.loadCss(siteId, CacheKeyConstant.Css.LOGIN);
			if (loginCss == null) {
				loginCss = new Css(siteId, SsoDomainContext.getStaticServer() + SsoDomainContext.getCdnVersion() + "/style/default.css");
			}
			html5Css = CssUtil.loadCss(siteId, CacheKeyConstant.Css.HTML5_CSS);
			if (html5Css == null) {
				html5Css = new Css(siteId, SsoDomainContext.getStaticServer() + SsoDomainContext.getCdnVersion() + "/style/defaultForHtml5.css");
			}
			loginCssUrl = loginCss.getCssUrl();
		}
		outOauthCss = CssUtil.loadCss(siteId, CacheKeyConstant.Css.OUT_OAUTH_CSS);
	}

	public String getPermitIpConfig() {
		return RedisContext.getResourceCache().getResourceValue(CacheKeyConstant.ConfigResourcesConstants.PERMIT_IP_CONFIG, CacheKeyConstant.ConfigResourcesConstants.PERMIT_IP_CONFIG);
	}

	public void initLoginElement(String siteId) {
		if (null == siteId) {
			return;
		}
		// 查询账号体系
		Bussiness bussiness = bussinessDao.getById(siteId) ;
		isSingleAccount = bussiness.isSingleAccount() ;
		if (isSingleAccount) {
			unAuthAgreementFlag = RedisContext.getSwitchConfig(CacheKeyConstant.ConfigResourcesConstants.NET_BAR_FREE_LOGIN_CONFIG,
					CacheKeyConstant.ConfigResourcesConstants.UN_AUTH_AGREEMENT_SWITCH, true);
		}
        //初始化验证码
		initCheckCodeInfo(loginElementService.getLoginConfig(siteId, LoginElementConstant.CHECK_CODE));
        //初始化短信验证码
        setNeedSmsCheckCode(configIsOpen(loginElementService.getLoginConfig(siteId, LoginElementConstant.SMS_CHECK_CODE))
				&& isGtSwitchOpen());
		// 初始化快速登录页
		initQuickLoginInfo(loginElementService.getLoginConfig(siteId, LoginElementConstant.QUICK_LOGIN), loginType);
		// 初始化动态密码
        setNeedDynamicPwd(configIsOpen(loginElementService.getLoginConfig(siteId, LoginElementConstant.DYNAMIC_PWD)));
        //初始化短信快速登录
		setNeedSmsQuickLogin(configIsOpen(loginElementService.getLoginConfig(siteId, LoginElementConstant.SMS_QUICK_LOGIN)));
		//初始化立即注册
		initImmediatelyRegInfo(loginElementService.getLoginConfig(siteId, LoginElementConstant.IMMEDIATELY_REG));
		//设置忘记密码开关
		setNeedRetrievePwd(configIsOpen(loginElementService.getLoginConfig(siteId, LoginElementConstant.RETRIEVE_PWD)));
		//初始化APP扫码登录名称
		initAppName(loginElementService.getLoginConfig(siteId, LoginElementConstant.APP_QR_CODE_CONFIG));
		// 网维免登录插件写数据
		initQuickLoginWriteData(loginElementService.getLoginConfig(siteId, LoginElementConstant.QUICK_LOGIN_WRITE_DATA));

		if (needSmsQuickLogin) {
			smsSendExpTime = ExpTimeContext.getExpTime(ExpTimeContext.ExpType.SMS_LOGIN, site_id);
		}
		loadReportElement();
		initCheckTimeInterval();
	}

	/**
	 * 加载获取上报信息的js
	 */
	private void loadReportElement() {
		String resource = RedisContext.getResourceCache().getResourceValue(
						CacheKeyConstant.ConfigResourcesConstants.DATA_REPORT_CONFIG, CacheKeyConstant.ConfigResourcesConstants.DATA_REPORT_SWITCH);
		if (StringUtil.isNotBlank(resource) && "y".equalsIgnoreCase(resource)) {
			setNeedReportData(true);
		}
	}

	/**
	 * 用户协议自动选中开关
	 */
	private boolean isAgreementSwitchOpen() {
		String resource = RedisContext.getResourceCache().getResourceValue(
				CacheKeyConstant.ConfigResourcesConstants.COMMON_CONFIG, CacheKeyConstant.ConfigResourcesConstants.AGREEMENT_AUTO_CHECK_SWITCH);
		if (StringUtil.isNotBlank(resource) && "y".equalsIgnoreCase(resource)) {
			return true;
		}
		return false;
	}

	private void initCheckTimeInterval() {
		checkTimeInterval = RedisContext.getIntConfig(CacheKeyConstant.ConfigResourcesConstants.COMMON_CONFIG,
				CacheKeyConstant.ConfigResourcesConstants.QRCODE_LOGIN_CHECK_TIME_INTERVAL, 2);
	}
	/**
	 * 用户协议自动选中站点白名单
	 * resource的value值格式为“|identity|swpay|mch|swjoy|......|”站点前后需使用竖线包裹
	 */
	private boolean isSiteIdInWhitelist(String siteId) {
		String resource = RedisContext.getResourceCache().getResourceValue(
				CacheKeyConstant.ConfigResourcesConstants.COMMON_CONFIG, CacheKeyConstant.ConfigResourcesConstants.AGREEMENT_AUTO_CHECK_SITE_ID_WHITELIST);
		String siteIdTemp = ("|" + siteId + "|").toLowerCase(Locale.ROOT);
		return !StringUtil.isBlank(resource) && resource.toLowerCase().contains(siteIdTemp);
	}

    /**
     * 初始化验证码
     * @param configMap
     */
	private void initCheckCodeInfo(Map<String, String> configMap) {
        // 初始化验证码信息
        if (null != configMap) {
            //mobile共享极验配置，但不受强制验证码开关限制
            if (null != configMap.get(LoginElementConstant.STATE) &&
                    LoginElementConstant.STSTUS_OPEN.equalsIgnoreCase(configMap.get(LoginElementConstant.STATE))) {
                setNeedCheckCode(isGtSwitchOpen());
            } else {
                setNeedCheckCode(Boolean.FALSE);
            }
            if (null != configMap.get(LoginElementConstant.VALUE)) {
                setCount(Integer.parseInt(configMap
                        .get(LoginElementConstant.VALUE)));
            } else {
                setCount(0);
            }
        }
    }

    private void initSingleGeetestSwitch() {
		setSingleGeetestSwitch(configIsOpen(loginElementService.getLoginConfig(site_id, LoginElementConstant.SINGLE_GEETEST_SWITCH))
				&& isGtSwitchOpen());
	}

    /**
     * 初始化快速登录
     * @param config 登录配置数据
     */
    private void initQuickLoginInfo(Map<String, String> config, String loginType) {
        if (configIsOpen(config)) {
            setIsFreeLoginFlag(Boolean.TRUE);
            String value = config.get(LoginElementConstant.VALUE);
            int freeSwitch = StringUtil.isBlank(value) ? 1 : Integer.parseInt(value);
            setShowOauthPageFlag((freeSwitch&2) == 2);
			setNoSsoAuthFreeLoginFlag((freeSwitch&4) == 4);
        } else {
            setIsFreeLoginFlag(Boolean.FALSE);
        }
        setLoginType(StringUtil.isBlank(loginType) ? "normalLogin" : loginType);
    }

	/**
	 * 初始化快速登录
	 * @param config 登录配置数据
	 */
	private void initQuickLoginWriteData(Map<String, String> config) {
		if (configIsOpen(config)) {
			setIsFreeLoginWriteDataFlag(Boolean.TRUE);
		} else {
			setIsFreeLoginWriteDataFlag(Boolean.FALSE);
		}
	}

    /**
     * 初始化短信快速登录
     * @param configMap 登录配置数据
     */
    private void initImmediatelyRegInfo(Map<String, String> configMap) {
        if (null != configMap) {
            if (null != configMap.get(LoginElementConstant.STATE)
                    && configMap.get(LoginElementConstant.STATE).equals("1")) {
                setNeedImmediatelyReg(Boolean.TRUE);
                setRegLink(configMap.get(LoginElementConstant.REG_LINK));
            } else {
                setNeedImmediatelyReg(Boolean.FALSE);
            }
        }
        if (StringUtil.isBlank(regLink)) {
        	setRegLink(SsoDomainContext.getIdentityServer() + "/goRegister");
		}
    }

	/**
	 * 初始化短信快速登录
	 * @param configMap 登录配置数据
	 */
	private void initAppName(Map<String, String> configMap) {
		if (null != configMap) {
			if (null != configMap.get(LoginElementConstant.STATE)
					&& configMap.get(LoginElementConstant.STATE).equals("1")) {
				setAppName(configMap.get(LoginElementConstant.VALUE));
				setNeedAPPQrCode(true);
			}
		}
	}


	private boolean configIsOpen(Map<String, String> configMap) {
        if (null != configMap) {
            if (null != configMap.get(LoginElementConstant.STATE)
                    && configMap.get(LoginElementConstant.STATE).equals("1")) {
                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;
    }

	private String getReturnUrl() {
		if ("default".equals(loginPage)) {
			return "default";
		} else {
			initCss(site_id);
			return INPUT;
		}
	}

	/**
	 * 构建登录日志
	 *
	 */
	private LogonLog buildLogonLog(Member member) {
		LogonLog logonLog = new LogonLog();
		logonLog.setLogonType(loginType);
		logonLog.setClientIp(IPContext.getIp());
		logonLog.setMemberId(member.getMemberId());
		logonLog.setLoginFrom(getSite_id());
		logonLog.setMemberName(member.getMemberName());
		if (member.getIsBindDynamicPwd()) {
			logonLog.setPwdType(MemberConstants.PWD_TYPE_DYNAMIC_PWD);
			logonLog.setStep(MemberConstants.LOGIN_STEP_NEXT);
		} else if (member.getIsBindShunLing()) {
			logonLog.setPwdType(MemberConstants.PWD_TYPE_SHUN_LIING);
			logonLog.setStep(MemberConstants.LOGIN_STEP_NEXT);
		}
		return logonLog;
	}

	/**
	 * 重新获取动态密码
	 */
	public String getAgainDynamicPwd() throws IOException {
		try {
			if (StringUtil.isEmpty(userName)) {
				return INPUT;
			}
			getResponse().setContentType("text/json;charset=UTF-8");
			member = getMemberDao().getMember(userName);
            BaseXmlResponse<SmsCode> response = interfaceService.sendDynamicPassword(member);
            SmsCodeResponse smsCodeResponse = new SmsCodeResponse(response);
            if (smsCodeResponse.pwdOnlyOnceAMinute()) {
                getResponse().getWriter().write("{\"errorMsg\":\"" + "一分钟只能获取一次验证码" + "\"}");
                return null;
            }
            if (smsCodeResponse.isSuccess()) {
                getResponse().getWriter().write("{\"success\":\"ok\"}");
            }

		} catch (Exception e) {
			logger.error("验证码发送失败", e);
			getResponse().getWriter().write("{\"errorMsg\":\"" + "获取验证码失败" + "\"}");
		}

		return null;
	}

	/**
	 * 初始化参数信息
	 */
	private void initParams() throws UnsupportedEncodingException {
		if (StringUtil.isNotBlank(getMsg())) {
			setMsg(new String(getMsg().getBytes(StandardCharsets.ISO_8859_1), StandardCharsets.UTF_8));
		}
		if (StringUtil.isNotBlank(getExtData())) {
			setExtData(URLDecoder.decode(extData, "UTF-8"));
		}
	}

	/**
	 * 通过Oauth2跳外部站点认证
	 *
	 */
	public String goOutSiteOauth() {
		try {
			checkSite();
			UserOutsiteApapter outsiteApapter = (UserOutsiteApapter) BaseStoneContext.getInstance().getBean(
							UserOutsiteConstant.USEROUT_APAPTERS
									.get(loginSiteId));
			outsiteApapter.setCallbackUrl(URLEncoder.encode(callbackUrl,"UTF-8"));
			outsiteApapter.setSite_id(site_id);
			outsiteApapter.setVersion(version);
			outsiteApapter.setEnv(env);
			outsiteApapter.setExtData(extData);
			outsiteApapter.setReportData(reportData);
			outsiteApapter.setNeedCode(needCode);
			outsiteApapter.setTgt(tgt);
			//用来控制微信二维码样式
			outsiteApapter.setLoginCssUrl(loginCssUrl);
			serviceUrl = outsiteApapter.goToOauth();

			return SUCCESS;
		} catch(BaseStoneException bse) {
			setMsg("系统异常，请稍候再试！");
			logger.error(bse.getMsg());
			return INPUT;
		} catch (Exception e) {
			logger.error("系统异常", e);
			setMsg("系统异常，请稍候再试！");
			return INPUT;
		}
	}
	private String userKey;

	public String getUserKey() {
		return userKey;
	}

	public void setUserKey(String userKey) {
		this.userKey = userKey;
	}

	/**
	 * 直接调用缓存查询
	 * @param cacheUserKey 缓存key
	 * @return 缓存的值
	 */
	public String queryOpenLoginInfo(String cacheUserKey){
		String obj = RedisContext.getRedisCache().get(cacheUserKey);
		return obj == null ? "" : obj;
	}

	public String checkWxOpenLogin() {
		outMsg(queryOpenLoginInfo(userKey));
		return null;
	}

	/**
	 * 通过Oauth2 Ajax返回数据
	 *
	 */
	public String getOutSiteOauth() {
		try {
			if (StringUtil.isNotBlank(siteScene) && siteScene.length() > 10) {
				throw new BaseStoneException("", "业务场景值长度不能超过10");
			}
			checkSite();
			UserOutsiteApapter outsiteApapter = (UserOutsiteApapter) BaseStoneContext.getInstance().getBean(
					UserOutsiteConstant.USEROUT_APAPTERS
							.get(loginSiteId));
			outsiteApapter.setCallbackUrl(URLEncoder.encode(callbackUrl,"UTF-8"));
			outsiteApapter.setSite_id(site_id);
			outsiteApapter.setVersion(version);
			outsiteApapter.setEnv(env);
			outsiteApapter.setExtData(extData);
			outsiteApapter.setNeedCode(needCode);
			outsiteApapter.setTgt(tgt);
			// 希望以后这里返回的格式能统一一些
            outsiteApapter.setLoginCssUrl(loginCssUrl);
            outsiteApapter.setSiteScene(siteScene);
            outsiteApapter.setInnerScene(businessScene);
			Map<String,Object> response = outsiteApapter.getOauth();
			outMsg(GSON.toJson(response));
		} catch(BaseStoneException bse) {
			setMsg("系统异常，请稍候再试！");
			logger.error(bse.getMsg());
			outMsg("{\"errorMsg\":\"" + bse.getMessage() + "\"}");
		} catch (Exception e) {
			logger.error("系统异常", e);
			setMsg("系统异常，请稍候再试！");
			outMsg("{\"errorMsg\":\"" + "系统异常，请稍候再试！" + "\"}");
		}
		return null;
	}

	/**
	 * 通过Oauth2 服务端返回数据
	 *
	 */
	public String getOutSiteOauthByServer() {
		try {
			checkSite();
			UserOutsiteApapter outsiteApapter = (UserOutsiteApapter) BaseStoneContext.getInstance().getBean(
					UserOutsiteConstant.USEROUT_APAPTERS.get(loginSiteId));
			outsiteApapter.setSite_id(site_id);
			outsiteApapter.setVersion(version);
			outsiteApapter.setEnv(env);
			outsiteApapter.setExtData(extData);
			outsiteApapter.setCallbackUrl(callbackUrl);
			outsiteApapter.setAppId(appId);
			Map<String,Object> response = outsiteApapter.getOauthByServer();
			outMsg(GSON.toJson(response));
		} catch(BaseStoneException bse) {
			setMsg("系统异常，请稍候再试！");
			logger.error(bse.getMsg());
			outMsg("{\"errorMsg\":\"" + bse.getMessage() + "\"}");
		} catch (Exception e) {
			logger.error("系统异常", e);
			setMsg("系统异常，请稍候再试！");
			outMsg("{\"errorMsg\":\"" + "系统异常，请稍候再试！" + "\"}");
		}
		return null;
	}

	private void outMsg(String json) {
		PrintWriter out = null;
		try {
			out = this.getResponse().getWriter();
			getResponse().setContentType("text/json;charset=UTF-8");
			out.write(json);
		} catch (IOException e) {
			logger.error("系统异常：",e);
			assert out != null;
			out.write("{\"errorMsg\":\"" + "系统异常，请稍候再试！" + "\"}");
		} finally {
			assert out != null;
			out.close();
		}
	}
	/**
	 * H5微信回调回来刷新页面会重新使用微信的票据
	 * 所以当回调后，服务端重定向到该地址，来避免页面刷新导致的错误
	 */
	public String goSingle() {
		initCss(site_id);
		//初始化验证码
		initSingleGeetestSwitch();
		if (OsUtil.isMobile(getRequest().getHeader("User-Agent"))) {
			return "singleAccountBindForH5";
		}
		return "singleAccountBind";
	}

	/**
	 * 微信回调后返回微信链接消息，跳转到H5绑定单帐号关系
	 */
	public String goSingleNoJump() {
		initCss(site_id);
		//初始化验证码
		initSingleGeetestSwitch();
		unAuthAgreementFlag = RedisContext.getSwitchConfig(CacheKeyConstant.ConfigResourcesConstants.NET_BAR_FREE_LOGIN_CONFIG,
				CacheKeyConstant.ConfigResourcesConstants.UN_AUTH_AGREEMENT_SWITCH, false);
		if (!unAuthAgreementFlag) {
			agreementConfig = RichTextUtil.loadRichText(CacheKeyConstant.SiteId.PASSPORT,
					CacheKeyConstant.Agreement.FREE_LOGIN_AUTH, CacheKeyConstant.Platform.WEB);
		}
		Integer smsDynamic = ExpTimeContext.getExpTime(ExpTimeContext.ExpType.SMS_SINGLE_BIND, site_id);
		if (smsDynamic != null && smsDynamic > 0) {
			smsSendExpTime = smsDynamic;
		}
		return INPUT;
	}

	public void checkSite() {
		SiteContext.setSiteName("SSO");
		SiteContext.setSiteId(this.getSite_id());
		if (StringUtil.isBlank(getSite_id())) {
			throw new MsgNullExp("站点ID");
		}
		SiteInterface site = SiteInterfaceUtil.loadSiteInterface();
		if (site == null) {
			throw new BaseStoneException(ErrorCode.C_1004);
		}
	}
	
	public void checkIp() {
		String ip = IpUtil.getIpAddress(ServletActionContext.getRequest());
		
		String permitIp = getPermitIpConfig();
		
		if(null==permitIp || permitIp.equals("")) {
			return;
		}
		String []ips =permitIp.split("\\|");
		if(ips.length==0) {
			return;
		}
		for(String s:ips){
			if(s.equals(ip)) {
				logger.error("IP非法:" + getUserName());
				throw new IpLimitedExp();
			}
		}
	}

	public boolean checkIsWhitelistSite(String word) {
		String[] whitelist = getWhitelistUnsafe().split("\\|");
		for (String s : whitelist) {
			if (word.equals(s)) {
				return true;
			}
		}
		return false;
	}

	public String getFmtCallback() {
		if (StringUtil.isBlank(callbackUrl)) {
			return null;
		}
		String split = "?";
		if (callbackUrl.contains("?")) {
			split = "&";
		}
		if(callbackUrl.contains("ticketId=")){
			callbackUrl = callbackUrl.replaceAll("ticketId=[0-9a-z-]*(&)?","") ;
		}
		if(callbackUrl.contains("tockenId=")){
			callbackUrl = callbackUrl.replaceAll("tocketId=[0-9a-z-]*(&)?","") ;
		}
		StringBuffer sb = new StringBuffer();
		sb.append(callbackUrl);
		if ("1".equals(needCode)) {
			String result = null;
			try {
				result = HttpClientUtils.doPost(getGetTicketWithoutLoginUrl(),buildParams(member.getMemberId()));
				String code = parseResult(result);
				if(StringUtil.isNotBlank(code)) {
					sb.append(split).append("msg=succ&code=").append(code);
				} else {
					sb.append(split).append("msg=").append(getErrorMsg());
				}
				split = "&";
			} catch (Exception e) {
				logger.error("获取app免登code失败",e) ;
			}

		}
		sb.append(split).append("ticketId=").append(ticket).append("&tockenId=").append(tockenId).append( "&loginMode=" ).append( loginMode );
		return sb.toString();
	}

	private String parseResult(String xmlText) {
		String code = "";
		SAXBuilder builder = new SAXBuilder();
		StringReader strReader = new StringReader(xmlText);
		try {
			Document document = builder.build(strReader);
			Element rootNode = document.getRootElement();
			String msgId =  ((Element)(rootNode.getChildren("Result").get(0))).getAttributeValue("msgId");
			setErrorMsg(((Element)(rootNode.getChildren("Result").get(0))).getAttributeValue("msg"));
			if("0".equals(msgId)) {
				code = ((Element) (rootNode.getChildren("Result").get(0))).getChildText("ticket");
			}
		} catch (IOException | JDOMException e) {
			logger.error("解析消息出错", e);
		}
		return code;
	}

	private Map<String, String> buildParams(Integer memberId){
		String time = DateUtil.getCurrentDateStamp();
		Map<String, String> paramMap = new TreeMap<String, String>();
		paramMap.put("memberId", String.valueOf(memberId));
		paramMap.put("siteId", getSiteId());
		paramMap.put("time", time);
		paramMap.put("sign", buildSign(paramMap));
		return paramMap;
	}

	private String buildSign(Map<String, String> paramMap) {
		StringBuilder stringBuffer = new StringBuilder();
		for(String keySet: paramMap.keySet()){
			stringBuffer.append(paramMap.get(keySet)).append("|");
		}
		stringBuffer.append(getGetTicketWithoutLoginMd5Key());
		String sign = "";
		try{
			sign = URLEncoder.encode(stringBuffer.toString(),"utf-8").toUpperCase();
		}catch(UnsupportedEncodingException e){
			logger.error("对签名转码错误", e);
		}
		sign = Md5Encrypt.encrypt(sign).toUpperCase();
		if(logger.isInfoEnabled()) {
			logger.info("getTicketWithoutLogin:" + stringBuffer + ",sign：" + sign);
		}
		return sign;
	}

	public String undoCancel() throws IOException {
        if (StringUtils.isBlank(optToken) || StringUtils.isBlank(memberName) || StringUtils.isBlank(site_id)) {
            getResponse().getWriter().write("{\"code\":false,\"msg\":\"" + "参数丢失，请刷新页面重新验证" + "\"}");
            return null;
        }
        String token;
        try {
            token = AesEncrypt.Encrypt(memberName + site_id, MemberUtil.getKey());
        } catch (Exception e) {
            getResponse().getWriter().write("{\"code\":false,\"msg\":\"" + "Token校验失败，请刷新页面重新验证" + "\"}");
            return null;
        }
        if (!optToken.equalsIgnoreCase(token.substring(8))) {
            getResponse().getWriter().write("{\"code\":false,\"msg\":\"" + "Token校验失败，请刷新页面重新验证" + "\"}");
            return null;
        }
        member = new Member().getDao().getByName(memberName);
        if (member != null) {
            member.setMemberState(MemberConstants.USER_NATURAL_STATE);
            member.beginBuildLog("撤销注销帐号");
            member.setTimeEdit(new Date());
            member.update();
			MemberCancelNoticeJob job = new MemberCancelNoticeJob(serviceNotifyDao, member,
					MemberEnum.CancelNotifyType.TYPE_REVOKE_CANCEL.getType());
			AsyncTaskExecutor.submit(job);
        }
        getResponse().getWriter().write("{\"code\":true,\"msg\":\"" + "撤销成功" + "\"}");
        return null;
    }

	/**
	 * 解密加密字段
	 * @param encryptData 加密内容
	 * @param checkLen 是否加密判断长度（用于升级兼容）
	 * @return
	 */
    private String decryptData(String encryptData, Integer checkLen) {
		if (StringUtil.isBlank(encryptData) || encryptData.length() <= checkLen) {
			return encryptData;
		}
		String privateKey = RedisContext.getResourceCache().getResourceValue(ComplaintConstants.RSA_CONFIG, ComplaintConstants.RSA_PRIVATE);
		try {
			String decrypt = RsaEncrypt.decrypt(encryptData, privateKey);
			logger.info("解密数据[{}],实际值[{}]", encryptData, decrypt);
			return decrypt;
		} catch (Exception e) {
			logger.error("解密失败");
		}
		return encryptData;
	}

	public boolean isMd5() {
		return md5;
	}

	public void setMd5(boolean md5) {
		this.md5 = md5;
	}

	public String getTicket() {
		return ticket;
	}

	public void setTicket(String ticket) {
		this.ticket = ticket;
	}

	public boolean isNeedCheckCode() {
		return needCheckCode;
	}

	public void setNeedCheckCode(boolean needCheckCode) {
		this.needCheckCode = needCheckCode;
	}
	// TODO 无使用场景，建议后续清除
	public String getFmtCss() {
		if (css == null) {
			return "default.css";
		}
		String cssName = null;
		Object objCss = ActionContext.getContext().getParameters().get(css);
		if (objCss instanceof String) {
			cssName = (String) objCss;
		}
		if (cssName == null) {
			return "default.css";
		}
		return cssName;
	}

	public String getCss() {
		return css;
	}

	public void setCss(String css) {
		this.css = css;
	}

	public String getCheckCode() {
		return checkCode;
	}

	public void setCheckCode(String checkCode) {
		this.checkCode = checkCode;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		if (!StringUtil.isBlank(userName)) {
			if (UserCheckUtil.checkEmail(userName)) {
				this.userName = userName.toLowerCase();
				return;
			}
		}
		this.userName = userName;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public int getRand() {
		return rand;
	}

	public String getCallbackUrl() {
		return callbackUrl;
	}

	public void setCallbackUrl(String callbackUrl) {
		this.callbackUrl = callbackUrl;
	}

	public String getAppKey() {
		return appKey;
	}

	public void setAppKey(String appKey) {
		this.appKey = appKey;
	}

	protected MemberDao getMemberDao() {
		return (MemberDao) BaseStoneContext.getInstance().getBean("memberDao");
	}

	private MobileCheckCodeDao getMobileCheckCodeDao() {
		return (MobileCheckCodeDao) BaseStoneContext.getInstance().getBean(
				"mobileCheckCodeDao");
	}

	public String getSite_id() {
		return site_id;
	}

	public void setSite_id(String siteId) {
		site_id = siteId;
	}

	public String getLoginSiteId() {
		return loginSiteId;
	}

	public void setLoginSiteId(String loginSiteId) {
		this.loginSiteId = loginSiteId;
	}

    public String getCssSiteId() {
        return cssSiteId;
    }

    public void setCssSiteId(String cssSiteId) {
        this.cssSiteId = cssSiteId;
    }

    public String getServiceUrl() {
		return serviceUrl;
	}

	public void setServiceUrl(String serviceUrl) {
		this.serviceUrl = serviceUrl;
	}

	public String getSite_name() {
		return site_name;
	}

	public void setSite_name(String site_name) {
		this.site_name = site_name;
	}

	public BussinessDao getBussinessDao() {
		return bussinessDao;
	}

	public void setBussinessDao(BussinessDao bussinessDao) {
		this.bussinessDao = bussinessDao;
	}

	public String getTockenId() {
		return tockenId;
	}

	public void setTockenId(String tockenId) {
		this.tockenId = tockenId;
	}

	public String getDynamicPwd() {
		return dynamicPwd;
	}

	public void setDynamicPwd(String dynamicPwd) {
		this.dynamicPwd = dynamicPwd;
	}


	public String getDynamicMd5Key() {
		return dynamicMd5Key;
	}

	public void setDynamicMd5Key(String dynamicMd5Key) {
		this.dynamicMd5Key = dynamicMd5Key;
	}

	public String getDynamicAesKey() {
		return dynamicAesKey;
	}

	public void setDynamicAesKey(String dynamicAesKey) {
		this.dynamicAesKey = dynamicAesKey;
	}

	public String getEffectiveTime() {
		return effectiveTime;
	}

	public void setEffectiveTime(String effectiveTime) {
		this.effectiveTime = effectiveTime;
	}

	public String getToken_site_id() {
		return site_id;
	}

	public String getToken() {
		return tockenId;
	}

	public Member getMember() {
		return member;
	}

	public String getLoginType() {
		return loginType;
	}

	public void setLoginType(String loginType) {
		this.loginType = loginType;
	}

	public String getExtData() {
		return extData;
	}

	public void setExtData(String extData) {
		this.extData = extData;
	}

	public String getEnv() {
		return env;
	}

	public void setEnv(String env) {
		this.env = env;
	}

	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}

	public String getLoginPage() {
		return loginPage;
	}

	public void setLoginPage(String loginPage) {
		this.loginPage = loginPage;
	}

	public int getCount() {
		return count;
	}

	public void setCount(int count) {
		this.count = count;
	}

	public Boolean getNeedDynamicPwd() {
		return needDynamicPwd;
	}

	public void setNeedDynamicPwd(Boolean needDynamicPwd) {
		this.needDynamicPwd = needDynamicPwd;
	}

	public String getAppName() {
		return appName;
	}

	public void setAppName(String appName) {
		this.appName = appName;
	}

	public boolean getNeedAPPQrCode() {
		return needAPPQrCode;
	}

	public void setNeedAPPQrCode(boolean needAPPQrCode) {
		this.needAPPQrCode = needAPPQrCode;
	}

	public List<OutOauthDir> getShowOutOauthDirList() {
		return showOutOauthDirList;
	}

	public void setShowOutOauthDirList(List<OutOauthDir> showOutOauthDirList) {
		this.showOutOauthDirList = showOutOauthDirList;
	}

	public String getWhitelistUnsafe() {
		return whitelistUnsafe;
	}

	public void setWhitelistUnsafe(String whitelistUnsafe) {
		this.whitelistUnsafe = whitelistUnsafe;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getMemberName() throws UnsupportedEncodingException {
		return java.net.URLDecoder.decode(
				new String(this.memberName.getBytes("ISO8859-1")), "UTF-8");
	}

	public void setMemberName(String memberName) {
		this.memberName = memberName;
	}

	public Css getOutOauthCss() {
		return outOauthCss;
	}

	public void setOutOauthCss(Css outOauthCss) {
		this.outOauthCss = outOauthCss;
	}

    public Css getHtml5Css() {
        return html5Css;
    }

    public void setHtml5Css(Css html5Css) {
        this.html5Css = html5Css;
    }

    public List<OutOauthDir> getOutOauthDirs() {
		return outOauthDirs;
	}

	public void setOutOauthDirs(List<OutOauthDir> outOauthDirs) {
		this.outOauthDirs = outOauthDirs;
	}

	public Css getLoginCss() {
		return loginCss;
	}

	public void setLoginCss(Css loginCss) {
		this.loginCss = loginCss;
	}

	public String getBg() {
		return SsoDomainContext.getStaticServer() + SsoDomainContext.getCdnVersion() + "/images/recharge-bg.jpg";
	}

	public String getTopBg() {
		return SsoDomainContext.getStaticServer() + SsoDomainContext.getCdnVersion() + "/images/recharge-topbg.jpg";
	}

	public String getGetTicketWithoutLoginMd5Key() {
		return getTicketWithoutLoginMd5Key;
	}

	public void setGetTicketWithoutLoginMd5Key(String getTicketWithoutLoginMd5Key) {
		this.getTicketWithoutLoginMd5Key = getTicketWithoutLoginMd5Key;
	}

	public String getGetTicketWithoutLoginUrl() {
		return getTicketWithoutLoginUrl;
	}

	public void setGetTicketWithoutLoginUrl(String getTicketWithoutLoginUrl) {
		this.getTicketWithoutLoginUrl = getTicketWithoutLoginUrl;
	}

	public String getSiteId() {
		return siteId;
	}

	public void setSiteId(String siteId) {
		this.siteId = siteId;
	}

	public String getNeedCode() {
		return needCode;
	}

	public void setNeedCode(String needCode) {
		this.needCode = needCode;
	}

	public String getUnionid() {
		return unionid;
	}

	public void setUnionid(String unionid) {
		this.unionid = unionid;
	}

	public String getOpenId() {
		return openId;
	}

	public void setOpenId(String openId) {
		this.openId = openId;
	}

	public String getSign() {
		return sign;
	}

	public void setSign(String sign) {
		this.sign = sign;
	}

	public String getTgt() {
		return tgt;
	}

	public void setTgt(String tgt) {
		this.tgt = tgt;
	}

	public String getNumber() {
		return number;
	}

	public void setNumber(String number) {
		this.number = number;
	}

	public Integer getUserId() {
		return userId;
	}

	public void setUserId(Integer userId) {
		this.userId = userId;
	}

	public String getMobileActiveNo() {
		return mobileActiveNo;
	}

	public void setMobileActiveNo(String mobileActiveNo) {
		this.mobileActiveNo = mobileActiveNo;
	}

	public String getUserNameList() {
		return userNameList;
	}

	public void setUserNameList(String userNameList) {
		this.userNameList = userNameList;
	}

	public String getMobileValifyTokenId() {
		return mobileValifyTokenId;
	}

	public void setMobileValifyTokenId(String mobileValifyTokenId) {
		this.mobileValifyTokenId = mobileValifyTokenId;
	}

	public String getLoginMode() {
		return loginMode;
	}

	public void setLoginMode(String loginMode) {
		this.loginMode = loginMode;
	}

	public boolean isNeedImmediatelyReg() {
		return needImmediatelyReg;
	}

	public void setNeedImmediatelyReg(boolean needImmediatelyReg) {
		this.needImmediatelyReg = needImmediatelyReg;
	}

	public boolean isNeedSmsQuickLogin() {
		return needSmsQuickLogin;
	}

	public void setNeedSmsQuickLogin(boolean needSmsQuickLogin) {
		this.needSmsQuickLogin = needSmsQuickLogin;
	}

	public boolean isNeedSmsCheckCode() {
		return needSmsCheckCode;
	}

	public void setNeedSmsCheckCode(boolean needSmsCheckCode) {
		this.needSmsCheckCode = needSmsCheckCode;
	}

	public String getRegLink() {
		return regLink;
	}

	public void setRegLink(String regLink) {
		this.regLink = regLink;
	}

	public String getClientTicket() {
		return clientTicket;
	}

	public void setClientTicket(String clientTicket) {
		this.clientTicket = clientTicket;
	}

	public Boolean getIsFreeLoginFlag() {
		return isFreeLoginFlag;
	}

	public void setIsFreeLoginFlag(Boolean isFreeLoginFlag) {
		this.isFreeLoginFlag = isFreeLoginFlag;
	}

	public Boolean getIsFreeLoginWriteDataFlag() {
		return isFreeLoginWriteDataFlag;
	}

	public void setIsFreeLoginWriteDataFlag(Boolean isFreeLoginWriteDataFlag) {
		this.isFreeLoginWriteDataFlag = isFreeLoginWriteDataFlag;
	}

	public String getMobileLogin() {
		return mobileLogin;
	}

	public void setMobileLogin(String mobileLogin) {
		this.mobileLogin = mobileLogin;
	}

	public String getLoginCssUrl() {
		return loginCssUrl;
	}

	public void setLoginCssUrl(String loginCssUrl) {
		this.loginCssUrl = loginCssUrl;
	}

	public String getHideHeader() {
		return hideHeader;
	}

	public void setHideHeader(String hideHeader) {
		this.hideHeader = hideHeader;
	}

	public String getLinkTgt() {
		return StringUtil.isBlank(linkTgt) ? "_blank" : linkTgt;
	}

	public void setLinkTgt(String linkTgt) {
		this.linkTgt = linkTgt;
	}

	public Boolean getIsSingleAccount() {
		return isSingleAccount;
	}

	public void setIsSingleAccount(Boolean singleAccount) {
		isSingleAccount = singleAccount;
	}

	public String getSingleBindSign() {
		return singleBindSign;
	}

	public void setSingleBindSign(String singleBindSign) {
		this.singleBindSign = singleBindSign;
	}

	public MemberAccountBindDao getMemberAccountBindDao() {
		return memberAccountBindDao;
	}

	public void setMemberAccountBindDao(MemberAccountBindDao memberAccountBindDao) {
		this.memberAccountBindDao = memberAccountBindDao;
	}

    public InterfaceService getInterfaceService() {
        return interfaceService;
    }

    public void setInterfaceService(InterfaceService interfaceService) {
        this.interfaceService = interfaceService;
    }

	public LoginElementService getLoginElementService() {
		return loginElementService;
	}

	public void setLoginElementService(LoginElementService loginElementService) {
		this.loginElementService = loginElementService;
	}

	public boolean isNeedRetrievePwd() {
		return needRetrievePwd;
	}

	public void setNeedRetrievePwd(boolean needRetrievePwd) {
		this.needRetrievePwd = needRetrievePwd;
	}

	public Css getPrivacyCss() {
		return privacyCss;
	}

	public void setPrivacyCss(Css privacyCss) {
		this.privacyCss = privacyCss;
	}

    public String getOptToken() {
        return optToken;
    }

    public void setOptToken(String optToken) {
        this.optToken = optToken;
    }

    public boolean isRefreshPage() {
        return refreshPage;
    }

    public void setRefreshPage(boolean refreshPage) {
        this.refreshPage = refreshPage;
    }

	public String getReportData() {
		return reportData;
	}

	public void setReportData(String reportData) {
		this.reportData = reportData;
	}

	public boolean getNeedReportData() {
		return needReportData;
	}

	public void setNeedReportData(boolean needReportData) {
		this.needReportData = needReportData;
	}

    public Css getUserAgreementCss() {
        return userAgreementCss;
    }

    public void setUserAgreementCss(Css userAgreementCss) {
        this.userAgreementCss = userAgreementCss;
    }

	public String getWeakPwdState() {
		return weakPwdState;
	}

	public void setWeakPwdState(String weakPwdState) {
		this.weakPwdState = weakPwdState;
	}

	public String getNickName() {
		return nickName;
	}

	public void setNickName(String nickName) {
		this.nickName = nickName;
	}

	public String getHeadImage() {
		return headImage;
	}

	public void setHeadImage(String headImage) {
		this.headImage = headImage;
	}

	public Boolean getShowOauthPageFlag() {
		return showOauthPageFlag;
	}

	public void setShowOauthPageFlag(Boolean showOauthPageFlag) {
		this.showOauthPageFlag = showOauthPageFlag;
	}

	public String getMobileTail() {
		return mobileTail;
	}

	public void setMobileTail(String mobileTail) {
		this.mobileTail = mobileTail;
	}

	public String getTgtCancel() {
		return tgtCancel;
	}

	public void setTgtCancel(String tgtCancel) {
		this.tgtCancel = tgtCancel;
	}

	public boolean isSingleGeetestSwitch() {
		return singleGeetestSwitch;
	}

	public void setSingleGeetestSwitch(boolean singleGeetestSwitch) {
		this.singleGeetestSwitch = singleGeetestSwitch;
	}

	public String getSiteScene() {
		return siteScene;
	}

	public void setSiteScene(String siteScene) {
		this.siteScene = siteScene;
	}

	public String getAppId() {
		return appId;
	}

	public void setAppId(String appId) {
		this.appId = appId;
	}

	public ServiceNotifyDao getServiceNotifyDao() {
		return serviceNotifyDao;
	}

	public void setServiceNotifyDao(ServiceNotifyDao serviceNotifyDao) {
		this.serviceNotifyDao = serviceNotifyDao;
	}

	public String getBodyClassName() {
		return bodyClassName;
	}

	public void setBodyClassName(String bodyClassName) {
		this.bodyClassName = bodyClassName;
	}

	public String getLogin_rbt() {
		return login_rbt;
	}

	public void setLogin_rbt(String login_rbt) {
		this.login_rbt = login_rbt;
	}

	public Boolean getNoSsoAuthFreeLoginFlag() {
		return noSsoAuthFreeLoginFlag;
	}

	public void setNoSsoAuthFreeLoginFlag(Boolean noSsoAuthFreeLoginFlag) {
		this.noSsoAuthFreeLoginFlag = noSsoAuthFreeLoginFlag;
	}

	public String getBusinessScene() {
		return businessScene;
	}

	public void setBusinessScene(String businessScene) {
		this.businessScene = businessScene;
	}

	public WeixinMsgService getWeixinMsgService() {
		return weixinMsgService;
	}

	public void setWeixinMsgService(WeixinMsgService weixinMsgService) {
		this.weixinMsgService = weixinMsgService;
	}

	public Integer getDllSwitch() {
		return dllSwitch;
	}

	public void setDllSwitch(Integer dllSwitch) {
		this.dllSwitch = dllSwitch;
	}

	public String getSmsMobileActiveNo() {
		return smsMobileActiveNo;
	}

	public void setSmsMobileActiveNo(String smsMobileActiveNo) {
		this.smsMobileActiveNo = smsMobileActiveNo;
	}

	public String getSmsNumber() {
		return smsNumber;
	}

	public void setSmsNumber(String smsNumber) {
		this.smsNumber = smsNumber;
	}

	public WeixinOpenAuthAdapter getWeixinOpenOauth() {
		return weixinOpenOauth;
	}

	public void setWeixinOpenOauth(WeixinOpenAuthAdapter weixinOpenOauth) {
		this.weixinOpenOauth = weixinOpenOauth;
	}

	public Boolean getUnAuthAgreementFlag() {
		return unAuthAgreementFlag;
	}

	public void setUnAuthAgreementFlag(Boolean unAuthAgreementFlag) {
		this.unAuthAgreementFlag = unAuthAgreementFlag;
	}

	public RichText getAgreementConfig() {
		return agreementConfig;
	}

	public void setAgreementConfig(RichText agreementConfig) {
		this.agreementConfig = agreementConfig;
	}

	public String getIdCardName() {
		return idCardName;
	}

	public void setIdCardName(String idCardName) {
		this.idCardName = idCardName;
	}

	public MultipleAccountBindService getMultipleAccountBindService() {
		return multipleAccountBindService;
	}

	public void setMultipleAccountBindService(MultipleAccountBindService multipleAccountBindService) {
		this.multipleAccountBindService = multipleAccountBindService;
	}

	public String getAuthFreeType() {
		return authFreeType;
	}

	public void setAuthFreeType(String authFreeType) {
		this.authFreeType = authFreeType;
	}

	public Integer getSmsSendExpTime() {
		return smsSendExpTime;
	}

	public void setSmsSendExpTime(Integer smsSendExpTime) {
		this.smsSendExpTime = smsSendExpTime;
	}

	public Integer getQrCodeExpireSeconds() {
		return qrCodeExpireSeconds;
	}

	public void setQrCodeExpireSeconds(Integer qrCodeExpireSeconds) {
		this.qrCodeExpireSeconds = qrCodeExpireSeconds;
	}

	public Integer getCheckTimeInterval() {
		return checkTimeInterval;
	}

	public void setCheckTimeInterval(Integer checkTimeInterval) {
		this.checkTimeInterval = checkTimeInterval;
	}

	public Integer getGuestType() {
		return guestType;
	}

	public void setGuestType(Integer guestType) {
		this.guestType = guestType;
	}

	public Integer getGuestLogin() {
		return guestLogin;
	}

	public void setGuestLogin(Integer guestLogin) {
		this.guestLogin = guestLogin;
	}
}
