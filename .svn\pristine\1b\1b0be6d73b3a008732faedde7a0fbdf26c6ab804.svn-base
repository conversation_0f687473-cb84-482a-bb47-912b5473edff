$import('SFGridView');
$import('SFGrid');
$import('SFInput');
$import('SFSelect');
$import('ValidateRule');
$import('SFDirect');

var nameId;
var PartlyAccessView = $createClass('PartlyAccessView',function(){
	var self = this;
	this.pk =  new SFInput({field:'loginElementId'
		,name:'作业单元ID'
		,rules:[
			new CheckPlusInt(),
			new CheckEmpty(),
			new CheckMaxLength({length:9})
		]});
	
	this.pojoControls = [
		new SFInput({field:'loginElementId'
			,name:'ID'
			,readOnly:true
		}),
		new SFInput({field:'categoryId'
			,name:'作业单元ID'
			,readOnly:false
			}),
		new SFInput({field:'bussinessKey'
			,name:'商户ID'
			,readOnly:false
			}),
		new SFSelect({field:'type','name': "类型:",
			needdefault:true,
			items:LoginElementUtil.getTypeItems()
			,onchange:function(event) {
				nameId.setItems( LoginElementUtil.getNameItems(this.getValue()));
			}
			,rules:[
				new CheckEmpty()
			]
		}),
		// new SFInput({field:'type'
		// 	,name:'类型'
		// 	,readOnly:false
		// 	}),
		nameId = new SFSelect({field:'name','name': "名称:",
			needdefault:true
			,rules:[
				new CheckEmpty()
			]
		}),
		// new SFInput({field:'name'
		// 	,name:'名称'
		// 	,readOnly:false
		// 	}),
		new SFInput({field:'showOrderBy'
			,name:'显示顺序'
			,rules:[
				new CheckMaxLength({length:5})
			]})
	];
	this.schControl =  [
	    new SFInput({field:'categoryName',name:'作业单元', value:categoryName}),
	    new SFInput({field:'bussinessName',name:'商户', value:bussinessName}),
		new SFInput({field:'typeName',name:'界面元素', value:"第三方登录配置"})
	];
	this.leftBtns = [this.createAddBtn()];
	this.SFGridView();
},'SFGridView');

PartlyAccessView.prototype.buildGrid = function(){
	var self = this;
	var grid = new SFGrid({
		url:'findPartlyAccess.do?categoryId='+key+'&bussinessKey='+bus,
		col:[
			{id:'nameCn',text:'名称'},
			{id:'stateshow',text:'状态'},
			{id:'showOrderBy',text:'排序'},
			{id:'userEdit',text:'修改人'},
			{id:'timeEdit',text:'修改时间'}
		],
		linebutton:[
		    this.createUpdateBtn(),
			{
				text:'关闭',
				onclick:function(pojo){
					if(confirm("是否关闭当前记录"))
						self.trigger("closeCategory",pojo);
				},
				showFun:function(data){
					return data.state==1;
				}
			},
			{
				text:'打开',
				onclick:function(pojo){
					if(confirm("是否打开当前记录"))
						self.trigger("openCategory",pojo);
				},
				showFun:function(data){
					return data.state==0;
				}
			}
		]
	});
	return grid;
}

PartlyAccessView.prototype.buildPojoWin = function(){
	var self = this;
	var controls = this.getPojoControls()
	if(controls){
		var ret = new SFFormWindow({
			title:'第三方登录配置',
			controls:controls,
			closeBtn:true,
			hidden:this.pojoHidden,
			btns:[
				{text:'保存',
					onclick:function(){
						self.save()
					}
				}
			]
		});
		return ret;
	}
}
