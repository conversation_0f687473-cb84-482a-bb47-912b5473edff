package com.shunwang.baseStone.sysconfig.constant;

public class SysconfigConstant {
	public static final String S_ImgUrl = "imgUrl";
	public static final String S_ImgDir = "imgDir";
	public static final String S_EncryptImgDir = "encryptImgDir";
	/**
	 * 默认的短信内容
	 */
	public static final String S_MOBILEDEFUALTMSG = "mobileDefualtMsg";
	/**
	 * 实名认证拒绝短信内容
	 */
	public static final String S_ACTUCHECKREFUSE_MOBILEMSG = "actuCheckRefuseMobileMsg";
	/**
	 * 实名认证通过短信内容
	 */
	public static final String S_ACTUCHECKPASS_MOBILEMSG = "actuCheckPassMobileMsg";

	/**
	 * 实名认证变更拒绝短信内容
	 */
	public static final String S_ACTUCHANGECHECKREFUSE_MOBILEMSG = "actuChangeCheckRefuseMobileMsg";
	/**
	 * 实名认证变更通过短信内容
	 */
	public static final String S_ACTUCHANGECHECKPASS_MOBILEMSG = "actuChangeCheckPassMobileMsg";

	/**
	 * 通行证登录背景活动开关
	 */
	public static final String S_PASSPORT_SSO_ADBG_ISOPEN = "passportSSOadBgIsOpen";

	/**
	 * 通行证登录背景活动跳转地址
	 */
	public static final String S_PASSPORT_SSO_AD_URL = "passportSSOAdUrl";
	
	/**
	 * 有效手机号码段配置
	 */
	public static final String S_VALID_PHONE_NUM = "validPhoneNum";

	/**
	 * 极验总开关配置
	 */
	public static final String S_GT_SWITCH = "gtSwitch";
	/**
	 * 极验异常自动关闭阀值
	 */
	public static final String S_GT_THRESHOLD = "gtThreshold";
	/**
	 * 极验异常报警通知邮件，多个邮箱 | 分割
	 */
	public static final String S_GT_EMAILS = "gtEmails";

	/**
	 * 银行预留手机号
	 */
	public static final String S_BANK_RESERVE_PHONE_SWITCH = "bankReservePhoneSwitch";
}
