package com.shunwang.basepassport.user.common;

import com.shunwang.baseStone.cache.CacheKeyConstant;
import com.shunwang.baseStone.context.RedisContext;
import com.shunwang.basepassport.binder.common.ErrorCode;
import com.shunwang.basepassport.config.common.UserOutInterfaceUtil;
import com.shunwang.basepassport.user.exception.MemberStateExp;
import com.shunwang.util.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


/**
 * @Description:用户检测工具类
 * <AUTHOR>  create at 2011-7-26 上午10:28:49
 * @FileName com.shunwang.basepassport.user.common.UserCheckUtil.java
 */
public class UserCheckUtil {
	
	private static final Logger logger = LoggerFactory.getLogger(UserCheckUtil.class);
	// 去掉特殊字符的正则
	private static final String regEx= "[ `~!@#$%^&*()_\\-+=|{}':;',\\[\\].<>/?~！@#￥%……&*（）——+|{}【】‘；：”“’。，、？]";
	private static final Pattern sensitivePattern = Pattern.compile(regEx);

	private static String sensitiveFilePath;
	/**
	 * @Description:检测用户名是否合法
	 * @param logon
	 * @return
	 * <AUTHOR> create at 2011-7-26 上午10:30:23
	 */
	public static boolean checkNameIsAvailable(String logon){
		int length = getStrRealLength(logon);
		//长度不能超过16个字符 更新为用户名不能超过18字符,最长用户名是:SJ_11位手机号+3位随机数
		if(length >18 || length<4){
			return false;
		}
		//不能纯数字
		Pattern pattern1 = Pattern.compile("^[0-9]+$");
		Matcher matcher1 = pattern1.matcher(logon);
		if(matcher1.matches()){
			return false;
		}
		//仅支持字母、数字、下划线、#和中文
		Pattern pattern = Pattern.compile("^[a-zA-Z0-9_]+$");
		Matcher matcher = pattern.matcher(logon);
		return matcher.matches();
	}
	public static String replaceSpectial(String str){
		String dest = null;
		if(str == null){
			return dest;
		}else{
			Matcher m = sensitivePattern.matcher(str);
			dest = m.replaceAll("").trim();
			return dest;
		}
	}
	/**
	 * @Description:检查是否是敏感词
	 * @param word
	 * @return
	 * <AUTHOR> create at 2011-7-26 上午10:58:04
	 */
	public static boolean checkIsSensitivityWord(String word){

		if (StringUtil.isBlank(word)) {
			return false;
		}
		word = replaceSpectial(word) ;
		long timeStart = System.currentTimeMillis() ;
		boolean resultFlag = false;
		for (int i = 0; i < word.length(); i++) {
			resultFlag = checkSensitiveWord(word, i); //判断是否包含敏感字符
			if(resultFlag) {
				break;
			}
		}
		long timeEnd = System.currentTimeMillis() ;
		logger.info("耗时:"+ (timeEnd - timeStart)+"ms");
		return resultFlag ;
	}

	/**
	 * @Description:检查邮箱格式
	 * @param email
	 * @return
	 * <AUTHOR> create at 2011-7-26 上午11:07:34
	 */
	public static boolean checkEmail(String email){
		Pattern pattern = Pattern.compile("[\\w\\.\\-\\_]+@[\\w]+\\.[a-zA-Z]+(\\.[a-zA-Z]+)?$");
		Matcher matcher = pattern.matcher(email);
		return matcher.matches();
	}
	
	/**
	 * @Description:检测手机格式
	 * @param phone
	 * @return
	 * <AUTHOR> create at 2011-7-26 下午12:45:33
	 */
	public static boolean checkMobile(String phone){
		String defaultStr = "^13\\d{9}||15\\d{9}||14\\d{9}||17\\d{9}||18\\d{9}$";
		String validPhone = RedisContext.getStrConfig(CacheKeyConstant.ConfigResourcesConstants.TYPE_BASE_CONFIG,
				CacheKeyConstant.ConfigResourcesConstants.BASE_CONFIG_VALID_MOBILE_NUM, defaultStr);

		//验证后台配置的手机号码段是否符合规则,不符合则按以前的规则校验
		//13;14;15;16;17
		try {
			if (!doMacthes("^((\\d{2,})|(\\d{2,};)){1,}$", validPhone)) {
				return doMacthes(defaultStr, phone);
			}
			StringBuilder sb = new StringBuilder();
			String[] str = validPhone.split(";");
			sb.append("^");
			for (int i = 0; i < str.length; i++) {
				int length = 11 - str[i].length();
				sb.append(str[i]).append("\\").append("d{"+length+"}");
				if (i < str.length - 1) {
					sb.append("||");
				}
			}
			sb.append("$");
			if (logger.isDebugEnabled()) {
				logger.debug("有效手机号码段拼装的正则表达式是:[{}]", sb);
			}
			return doMacthes(sb.toString(), phone);
		} catch (Exception e) {
			logger.error("有效手机号码段拼装异常" , e);
			return doMacthes(defaultStr, phone);
		}
		
	}
	
	//前台js校验手机号码格式
	public static String getValidPhoneNum() {
		String defaultReg = "/^1[34578]\\d{9}$/";
		String validPhone = RedisContext.getStrConfig(CacheKeyConstant.ConfigResourcesConstants.TYPE_BASE_CONFIG,
				CacheKeyConstant.ConfigResourcesConstants.BASE_CONFIG_VALID_MOBILE_NUM, defaultReg);
		//验证后台配置的手机号码段是否符合规则,不符合则按以前的规则校验
		//13;14;15;16;17
		try {
			if (!doMacthes("^((\\d{2,})|(\\d{2,};)){1,}$", validPhone)) {
				return defaultReg;
			}
			StringBuilder sb = new StringBuilder();
			String[] str = validPhone.split(";");
			sb.append("/^(");
			for (int i = 0; i < str.length; i++) {
				int length = 11 - str[i].length();
				sb.append(str[i]).append("\\").append("d{"+length+"}");
				if (i < str.length - 1) {
					sb.append("|");
				}
			}
			sb.append(")$/");
			if (logger.isDebugEnabled()) {
				logger.debug("拼装的js正则表达式是:[{}]", sb);
			}
			return sb.toString();
		} catch (Exception e) {
			logger.error("js正则表达式拼装异常", e);
			return defaultReg;
		}
	}
	
	private static boolean doMacthes(String rules, String str) {
		Pattern pattern = Pattern.compile(rules);
		Matcher matcher = pattern.matcher(str);
		return matcher.matches();
	}
	
	/**
	 * @Description:检测邮政编码格式
	 * @param postalCode
	 * @return
	 * <AUTHOR> create at 2011-7-26 下午01:58:53
	 */
	public static boolean checkPostalCode(String postalCode){
		if(postalCode.length()!=6)
			return false;
		Pattern pattern = Pattern.compile("[0-9]*"); 
		return pattern.matcher(postalCode).matches(); 
	}
	/**
	 * @Description:取字符串的真实长度 中文为2个字节
	 * @param str
	 * @return
	 * <AUTHOR> create at 2011-7-26 上午10:29:43
	 */
	public static int getStrRealLength(String str){ 
	    int len = 0;   
	    for (int i=0; i<str.length(); i++) {   
	        if (str.charAt(i)>127 || str.charAt(i)==94) {   
	            len += 2;   
	        } else {   
	            len ++;   
	        }   
	    }   
	    return len;   
	} 
	/**
	 * 检查用户名是否以prefixName开头
	 * 
	 * @return
	 * <AUTHOR> 创建于 2012-2-2 下午05:32:17
	 * @throws
	 */
	public static boolean checkPrefixName(String userName){
		List<String> prefixName = UserOutInterfaceUtil.loadPrefixNameList();
		for(String name:prefixName){
			if(StringUtil.isNotBlank(name) && userName.toLowerCase().startsWith(name.toLowerCase()))
				return false;
		}
		return true;
	}

	/**
	 * 敏感词
	 */
	public static List<String> sensitiveWordList = new LinkedList<>() ;
	public static Map sensitiveWordMap = null ;

	private void addSensitiveWordToHashMap(List<String> keyWordSet) {
		sensitiveWordMap = new HashMap(keyWordSet.size());     //初始化敏感词容器，减少扩容操作
		String key = null;
		Map nowMap = null;
		Map<String, String> newWorMap = null;
		//迭代keyWordSet
		Iterator<String> iterator = keyWordSet.iterator();
		while(iterator.hasNext()){
			key = iterator.next();    //关键字
			nowMap = sensitiveWordMap;
			for(int i = 0 ; i < key.length() ; i++){
				char keyChar = key.charAt(i);       //转换成char型
				Object wordMap = nowMap.get(keyChar);       //获取

				if(wordMap != null){        //如果存在该key，直接赋值
					nowMap = (Map) wordMap;
				}
				else{     //不存在则，则构建一个map，同时将isEnd设置为0，因为他不是最后一个
					newWorMap = new HashMap<String,String>();
					newWorMap.put("isEnd", "0");     //不是最后一个
					nowMap.put(keyChar, newWorMap);
					nowMap = newWorMap;
				}

				if(i == key.length() - 1){
					nowMap.put("isEnd", "1");    //最后一个
				}
			}
		}
	}

	public static boolean checkSensitiveWord(String txt,int beginIndex){
		boolean  flag = false;    //敏感词结束标识位：用于敏感词只有1位的情况
		int matchFlag = 0;     //匹配标识数默认为0
		char word = 0;
		Map nowMap = sensitiveWordMap;
		for(int i = beginIndex; i < txt.length() ; i++){
			word = txt.charAt(i);
			nowMap = (Map) nowMap.get(word);     //获取指定key
			if(nowMap != null){     //存在，则判断是否为最后一个
				matchFlag++;     //找到相应key，匹配标识+1
				if("1".equals(nowMap.get("isEnd"))){       //如果为最后一个匹配规则,结束循环，返回匹配标识数
					flag = true;       //结束标志位为true
					break;// 最小规则，找到则直接返回true，不继续找
				}
			} else{     //不存在，直接返回
				break;
			}
		}
		if(matchFlag < 2 && !flag){
			matchFlag = 0 ;
		}
		return flag;
	}

	public String getSensitiveFilePath() {
		return sensitiveFilePath;
	}
	@SuppressWarnings("static-access")
	public void setSensitiveFilePath(String sensitiveFilePath) {
		if (StringUtil.isBlank(sensitiveFilePath)) {
			logger.error("敏感词文件路径未配置");
			throw new RuntimeException("敏感词文件目录未配置");
		} else {
			logger.info("加载敏感词[{}]到内存", sensitiveFilePath);
		}
		this.sensitiveFilePath = sensitiveFilePath;
		try {
			File file = new File(sensitiveFilePath);
			if (file.exists()) {
                InputStream is = new FileInputStream(file);
                BufferedReader br = new BufferedReader(new InputStreamReader(is, "UTF-8"));
                String line;
                while ((line = br.readLine()) != null) {
                    sensitiveWordList.add(line);
                }
                if (is != null) {
                    is.close();
                }
                if (br != null) {
                    br.close();
                }
            }
		}catch (Exception e){
			logger.info("加载敏感词错误",e) ;
		}
		// 生成敏感词数
		long timeStart = System.currentTimeMillis() ;
		addSensitiveWordToHashMap(sensitiveWordList) ;
		long timeEnd = System.currentTimeMillis() ;
		logger.info("敏感词变成树耗时:"+ (timeEnd-timeStart) + "ms,添加数：" + sensitiveWordList.size()) ;
	}

    /**
     * memberState必填
     * memberName选填，有值则异常返回带用户名的错误信息
     * @param memberState
     * @param memberName
     */
	public static void checkMemberState(Integer memberState, String memberName) {
        if (memberState.intValue() == MemberConstants.USER_FORBID_STATE) {
            throw new MemberStateExp(ErrorCode.C_1067.getCode(), (StringUtil.isBlank(memberName) ? "" : memberName) + ErrorCode.C_1067.getDescription());
        }

        if (memberState.intValue() == MemberConstants.USER_CANCEL_STATE) {
            throw new MemberStateExp(ErrorCode.C_1068.getCode(), (StringUtil.isBlank(memberName) ? "" : memberName) + ErrorCode.C_1068.getDescription());
        }
    }
}
