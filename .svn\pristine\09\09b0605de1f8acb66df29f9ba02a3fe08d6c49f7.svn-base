package com.shunwang.baseStone.core.dao;

import java.io.Serializable;

import com.shunwang.baseStone.core.pojo.BaseStoneObject;
import com.shunwang.framework.exception.WinterException;

/******************************
 * 版权所有：顺网科技 保留所有权利
 * 创建日期: 2011 Jul 20, 2011 2:04:50 PM
 * 创建作者：zhangjp
 * 文件名称：I.java
 * 版本： 1.0
 * 功能：增删查改
 * 最后修改时间：
 * 修改记录：
 *****************************************/
public interface ICrud<Pojo extends BaseStoneObject>  {

	
	/**
	 * ****************************
	 * 创建日期: 2011 Jul 19, 2011 8:22:42 PM
	 * 创建作者：zhangjp
	 * 功能：删除
	 ****************************************
	 */
	public void delete(Pojo pojo) throws WinterException ;
	/**
	 * ****************************
	 * 创建日期: 2011 Jul 19, 2011 8:22:48 PM
	 * 创建作者：zhangjp
	 * 功能：保存
	 ****************************************
	 */
	public Pojo save(Pojo pojo) throws WinterException ;
	/**
	 * ****************************
	 * 创建日期: 2011 Jul 19, 2011 8:22:54 PM
	 * 创建作者：zhangjp
	 * 功能：更新
	 ****************************************
	 */
	public Pojo update(Pojo pojo) throws WinterException ;
	/**
	 * ****************************
	 * 创建日期: 2011 Jul 19, 2011 8:23:11 PM
	 * 创建作者：zhangjp
	 * 功能：根据id查询
	 ****************************************
	 */
	public Pojo getById(Serializable id) ;
	
}
