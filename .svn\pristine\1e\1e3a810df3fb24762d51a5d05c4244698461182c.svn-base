package com.shunwang.basepassport.weixin.pojo;


import com.shunwang.util.json.GsonUtil;

import java.util.HashMap;
import java.util.Map;

public class WeixinTemplateMsgData {

	private String touser;
	private String template_id;
	private String url;
	private String topcolor;
	private Integer type;
	private Map<String, String> miniprogram;
	private TemplateItem data;

	public static WeixinTemplateMsgData New() {
		return new WeixinTemplateMsgData();
	}


	private WeixinTemplateMsgData() {
		this.data = new TemplateItem();
	}

	public String getTouser() {
		return touser;
	}

	public WeixinTemplateMsgData setTouser(String touser) {
		this.touser = touser;
		return this;
	}

	public Map<String, String> getMiniprogram() {
		return miniprogram;
	}

	public WeixinTemplateMsgData setMiniprogram(Map<String, String> miniprogram) {
		this.miniprogram = miniprogram;
		return this;
	}

	public String getTemplate_id() {
		return template_id;
	}

	public WeixinTemplateMsgData setTemplate_id(String template_id) {
		this.template_id = template_id;
		return this;
	}

	public String getUrl() {
		return url;
	}

	public WeixinTemplateMsgData setUrl(String url) {
		this.url = url;
		return this;
	}

	public String getTopcolor() {
		return topcolor;
	}

	public WeixinTemplateMsgData setTopcolor(String topcolor) {
		this.topcolor = topcolor;
		return this;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public TemplateItem getData() {
		return data;
	}

	public WeixinTemplateMsgData add(String key, String value, String color){
		data.put(key, new Item(value, color));
		return this;
	}

	/**
	 * 直接转化成jsonString
	 * @return {String}
	 */
	public String build() {
		return GsonUtil.toJson(this);
	}

	public class TemplateItem extends HashMap<String, Item> {

		private static final long serialVersionUID = -3728490424738325020L;

		public TemplateItem() {}

		public TemplateItem(String key, Item item) {
			this.put(key, item);
		}
	}

	public class Item {
		private Object value;
		private String color;

		public Object getValue() {
			return value;
		}
		public void setValue(Object value) {
			this.value = value;
		}
		public String getColor() {
			return color;
		}
		public void setColor(String color) {
			this.color = color;
		}

		public Item(Object value, String color) {
			this.value = value;
			this.color = color;
		}
	}
}
