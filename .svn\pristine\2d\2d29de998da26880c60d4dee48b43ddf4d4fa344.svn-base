<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd" >
<sqlMap namespace="com.shunwang.basepassport.user.pojo.ProtectedQuestion" >
<typeAlias alias="protectedQuestion" type="com.shunwang.basepassport.user.pojo.ProtectedQuestion"/>
	<resultMap class="com.shunwang.basepassport.user.pojo.ProtectedQuestion" id="protectedQuestionMap">
	<result column="question_key" property="questionKey" jdbcType="INTEGER"/>
	<result column="question" property="question" jdbcType="VARCHAR"/>
	<result column="state" property="state" jdbcType="VARCHAR"/>
	<result column="order_no" property="orderNo" jdbcType="INTEGER"/>
	<result column="user_add" property="userAdd" jdbcType="VARCHAR"/>
	<result column="user_mod" property="userMod" jdbcType="VARCHAR"/>
	<result column="time_add" property="timeAdd" jdbcType="DATETIME"/>
	<result column="time_mod" property="timeMod" jdbcType="DATETIME"/>
</resultMap>
<select id="get" resultClass="protectedQuestion" parameterClass="java.lang.Integer">
    SELECT 
    		a.question_key as questionKey,
    		a.question as question,
    		a.state as state,
            a.order_no as orderNo,
            a.user_add as userAdd,
            a.user_mod as userMod,
            a.time_add as timeAdd,
            a.time_mod as timeMod
    FROM personal_question  a 
    WHERE a.question_key =  #questionKey:java.lang.Integer#  
</select>

<select id="find" resultMap="protectedQuestionMap">
    SELECT 
    		a.question_key,
    		a.question,
    		a.state,
            a.order_no,
            a.user_add,
            a.user_mod,
            a.time_add,
            a.time_mod 
    FROM personal_question  a 
    WHERE state=1
	order by order_no ASC
</select>

</sqlMap>