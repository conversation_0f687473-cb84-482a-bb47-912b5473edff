package com.shunwang.basepassport.binder.response;

import com.google.gson.annotations.Expose;
import com.shunwang.baseStone.core.response.BaseStoneResponse;
import com.shunwang.baseStone.encrypt.Encrypt;
import com.shunwang.baseStone.encrypt.pojo.EncryptItem;
import com.shunwang.basepassport.user.pojo.Member;
import com.shunwang.xmlbean.annotation.XmlInit;

public class ShunLingBinderRespone extends BaseStoneResponse {

	private MemberAndInfo memberAndInfo; 
	
	public ShunLingBinderRespone(Member member) {
		this.memberAndInfo=new MemberAndInfo();
		this.memberAndInfo.memberName = String.valueOf(member.getMemberName());
	}
	
	public String buildSign(){
		Encrypt  encrypt=new Encrypt();
		encrypt.addItem(new EncryptItem(this.memberAndInfo.memberName));
		return encrypt.buildSign();
	}
	
	public class MemberAndInfo {
		/**用户名**/
		@Expose
		private String memberName;
		

		@XmlInit(path="userName")
		public String getMemberName() {
			return memberName;
		}

		public void setMemberName(String memberName) {
			this.memberName = memberName;
		}

	}

	@XmlInit(path="items/item")
	public MemberAndInfo getMemberAndInfo() {
		return memberAndInfo;
	}

	public void setMemberAndInfo(MemberAndInfo memberAndInfo) {
		this.memberAndInfo = memberAndInfo;
	}

}
