package com.shunwang.basepassport.config.dao;

import com.shunwang.baseStone.core.dao.BaseStoneIbatisDao;
import com.shunwang.basepassport.config.pojo.EmailConfig;
import org.springframework.cache.annotation.Cacheable;

public class EmailConfigDao extends BaseStoneIbatisDao<EmailConfig> {

    @Cacheable(value = "cache", keyGenerator = "configEmailConfigKeyGenerator", unless = "#result == null")
    public EmailConfig findBySiteIdAndBusinessType(String siteId, String businessType) {
        EmailConfig config = new EmailConfig();
        config.setSiteId(siteId);
        config.setBusinessType(businessType);
        return (EmailConfig) getSqlMapClientTemplate().queryForObject(getStatementNameWrap("findOne"), config);
    }
}
