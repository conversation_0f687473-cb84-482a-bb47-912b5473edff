package com.shunwang.basepassport.actu.response;

import com.google.gson.annotations.Expose;
import com.shunwang.baseStone.core.response.BaseStoneResponse;
import com.shunwang.basepassport.user.common.HeadUrlUtil;
import com.shunwang.basepassport.user.pojo.Member;
import com.shunwang.xmlbean.annotation.XmlInit;
import org.jdom.Element;
import org.jdom.output.Format;
import org.jdom.output.XMLOutputter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR> 2014年6月4日下午7:22:19
 */
public class RealnamePassportsResponse extends BaseStoneResponse {

    private int size;

    public RealnamePassportsResponse(List<Member> members) {
        List<MemberAndInfo> memberAndInfos = new ArrayList<MemberAndInfo>();
        for (Member member : members) {
            MemberAndInfo memberAndInfo = new MemberAndInfo();
            memberAndInfo.setMemberId(String.valueOf(member.getMemberId()));
            memberAndInfo.setEmail(member.getEmail());
            memberAndInfo.setMobile(member.getMobile());
            memberAndInfo.setRealName(member.getRealName());
            memberAndInfo.setIdentity(member.getMemberInfo().getIdCardNo());
            memberAndInfo.setCompanyName(member.getCompanyName());
            memberAndInfo.setLinkMan(member.getMemberInfo().getLinkMan());
            memberAndInfo.setMemberType(String.valueOf(member.getMemberType()));
            memberAndInfo.setMemberName(member.getMemberName());
            memberAndInfo.setQq(member.getMemberInfo().getQq());
            memberAndInfo.setLinkAddress(member.getMemberInfo().getLinkAddress());
            memberAndInfo.setFixedMobile(member.getMemberInfo().getFixedMobile());
            memberAndInfo.setPostalCode(member.getMemberInfo().getPostCode());
            memberAndInfo.setCompanyCertState(member.getCompanyCertState() + "");
            memberAndInfo.setPersonCertState(member.getPersonCertState() + "");
            memberAndInfo.setBindState(String.valueOf(member.getBindState()));
            memberAndInfo.setHeadImg(HeadUrlUtil.getSmallHeadImageUrl(member));
            memberAndInfo.setNickName(member.getNickName());
            memberAndInfo.setTitleName(member.getTitleName());
            memberAndInfos.add(memberAndInfo);
        }
        this.setItems(memberAndInfos);
        this.setSize(memberAndInfos.size());
    }

    public void process(HttpServletRequest request,HttpServletResponse response){
        super.noNeedSignWithSignVersion(request);
        if (request != null && TYPE_JSON.equals(request.getHeader("Accept"))) {
            super.process(request, response);
        } else {
            if(response != null){
                response.setContentType("text/xml;charset=UTF-8");

                response.setCharacterEncoding("UTF-8");
                try {
                    Writer out = new BufferedWriter(new OutputStreamWriter(response.getOutputStream() , "UTF8"));
                    out.write("<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n");
                    out.write(toXml());
                    out.flush();
                } catch (IOException e) {
                    log.error("response异常",e);
                }
            }
        }
    }

    public String toXml(){
        String xml = element2String(this.getXmlHunter().toXml(this));
        return xml;
    }

    public static String element2String(Element e){

        XMLOutputter output = new XMLOutputter ();
        output.setFormat(Format.getRawFormat());
        StringWriter sw = new StringWriter();
        try {
            output.output(e, sw);
        } catch (IOException e1) {
            e1.printStackTrace();
        }
        return sw.toString();
    }





    @XmlInit(isAttribute = true, path = "total")
    public int getSize() {
        return size;
    }

    public void setSize(int size) {
        this.size = size;
    }



    public class MemberAndInfo {
        /** 会员ID **/
        @Expose
        private String memberId;
        /** 会员状态(0什么都没绑、1手机、2邮箱、4密保卡、8密保问题) **/
        @Expose
        private String bindState;
        /** 真实姓名 **/
        @Expose
        private String realName;
        /** 身份证号(校验身份证长度15/18位 **/
        @Expose
        private String identity;
        /** 用户类型,1代表个人 2代表企业 **/
        @Expose
        private String memberType;
        /** 邮箱 **/
        @Expose
        private String email;
        /** 手机 **/
        @Expose
        private String mobile;
        /** 公司名 **/
        @Expose
        private String companyName;
        /** 联系人 **/
        @Expose
        private String linkMan;
        /** 头像地址 **/
        @Expose
        private String memberName;
        /** QQ **/
        @Expose
        private String qq;
        /** 联系地址 **/
        @Expose
        private String linkAddress;
        /** 固定电话 **/
        @Expose
        private String fixedMobile;
        /** 邮政编码 **/
        @Expose
        private String postalCode;
        /** 企业认证：1-未申请，2-待审核，3-已拒绝，4-已认证 ,5-已撤消，默认为1 **/
        @Expose
        private String companyCertState;
        /** 个人认证：1-未申请，2-待审核，3-已拒绝，4-已认证,5-已撤消，默认为1 **/
        @Expose
        private String personCertState;
        @Expose
        private String headImg;
        @Expose
        private String nickName;
        @Expose
        private String titleName;

        @XmlInit(path = "userId")
        public String getMemberId() {
            return memberId;
        }

        public void setMemberId(String memberId) {
            this.memberId = memberId;
        }

        @XmlInit
        public String getBindState() {
            return bindState;
        }

        public void setBindState(String bindState) {
            this.bindState = bindState;
        }

        @XmlInit
        public String getRealName() {
            return realName;
        }

        public void setRealName(String realName) {
            this.realName = realName;
        }

        @XmlInit
        public String getIdentity() {
            return identity;
        }

        public void setIdentity(String identity) {
            this.identity = identity;
        }

        @XmlInit(path = "userType")
        public String getMemberType() {
            return memberType;
        }

        public void setMemberType(String memberType) {
            this.memberType = memberType;
        }

        @XmlInit
        public String getEmail() {
            return email;
        }

        public void setEmail(String email) {
            this.email = email;
        }

        @XmlInit
        public String getCompanyName() {
            return companyName;
        }

        public void setCompanyName(String companyName) {
            this.companyName = companyName;
        }

        @XmlInit
        public String getLinkMan() {
            return linkMan;
        }

        public void setLinkMan(String linkMan) {
            this.linkMan = linkMan;
        }

        @XmlInit
        public String getQq() {
            return qq;
        }

        public void setQq(String qq) {
            this.qq = qq;
        }

        @XmlInit
        public String getLinkAddress() {
            return linkAddress;
        }

        public void setLinkAddress(String linkAddress) {
            this.linkAddress = linkAddress;
        }

        @XmlInit
        public String getFixedMobile() {
            return fixedMobile;
        }

        public void setFixedMobile(String fixedMobile) {
            this.fixedMobile = fixedMobile;
        }

        @XmlInit
        public String getPostalCode() {
            return postalCode;
        }

        public void setPostalCode(String postalCode) {
            this.postalCode = postalCode;
        }

        @XmlInit
        public String getCompanyCertState() {
            return companyCertState;
        }

        public void setCompanyCertState(String companyCertState) {
            this.companyCertState = companyCertState;
        }

        @XmlInit
        public String getPersonCertState() {
            return personCertState;
        }

        public void setPersonCertState(String personCertState) {
            this.personCertState = personCertState;
        }

        @XmlInit(path = "userName")
        public String getMemberName() {
            return memberName;
        }

        public void setMemberName(String memberName) {
            this.memberName = memberName;
        }

        @XmlInit
        public String getMobile() {
            return mobile;
        }

        public void setMobile(String mobile) {
            this.mobile = mobile;
        }

        @XmlInit
        public String getHeadImg() {
            return headImg;
        }

        public void setHeadImg(String headImg) {
            this.headImg = headImg;
        }

        @XmlInit
        public String getNickName() {
            return nickName;
        }

        public void setNickName(String nickName) {
            this.nickName = nickName;
        }

        @XmlInit
        public String getTitleName() {
            return titleName;
        }

        public void setTitleName(String titleName) {
            this.titleName = titleName;
        }

    }
}
