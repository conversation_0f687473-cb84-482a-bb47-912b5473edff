package com.shunwang.baseStone.sso.apapter;

import com.shunwang.baseStone.context.BaseStoneContext;
import com.shunwang.baseStone.context.RedisOperation;
import com.shunwang.baseStone.sso.pojo.QrCodeResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 缓存数据保存在 redis，外部存储中
 */
public interface OutSiteCacheable {

    Logger LOGGER = LoggerFactory.getLogger(OutSiteCacheable.class);


    default String wrap(String prefix, String cacheKey) {
        return prefix + cacheKey;
    }

    default String getSiteIdKey() {
        return wrap(getType() + "_siteId_", getScene());
    }

    default String getReportDataKey() {
        return wrap(getType() + "_reportData_", getScene());
    }

    default String getExtDataKey() {
        return wrap(getType() + "_extData_", getScene());
    }


    /**
     * 缓存180秒
     *
     * @return
     */
    default int cacheTimeoutSeconds() {
        return 180;
    }

    /**
     * 获取类型
     *
     * @return
     */
    default String getType() {
        throw new UnsupportedOperationException("具有扫码功能的需要实现");
    }

    /**
     * 获取场景值 scene
     *
     * @return
     */
    default String getScene() {
        throw new UnsupportedOperationException("具有扫码功能的需要实现");
    }


    default RedisOperation getRedisOperation() {
        return (RedisOperation) BaseStoneContext.getInstance().getBean("redisOperation");
    }

    /**
     * 更新二维码登录结果
     *
     * @param response
     */
    default void updateResult(QrCodeResponse response) {
        updateResult(getScene(), response);
    }

    /**
     * 用于更新二维码结果信息，中间态，缓存时间一般较短
     * @param key 缓存key
     * @param response 缓存信息
     */
    default void updateResult(String key, QrCodeResponse response) {
        Objects.requireNonNull(response, "QrCodeResponse不能为空");
        getRedisOperation().set(key, response, 3, TimeUnit.MINUTES);
        LOGGER.info("缓存结果cacheKey[{}], response[{}]", key, response);
    }

    /**
     * 用于初始化二维码，缓存时间不固定
     * @param key 缓存key
     * @param response 缓存信息
     * @param qrCodeExpireSeconds 缓存时间
     */
    default void updateResult(String key, QrCodeResponse response, Integer qrCodeExpireSeconds) {
        Objects.requireNonNull(response, "QrCodeResponse不能为空");
        getRedisOperation().set(key, response, qrCodeExpireSeconds, TimeUnit.MINUTES);
        LOGGER.info("缓存结果cacheKey[{}], response[{}]", key, response);
    }


    /**
     * 获取扫码结果
     *
     * @param scene
     * @return
     */
    default String getQrCodeResult(String scene) {
        String result = getRedisOperation().get(scene);
        return result == null ? "" : result;
    }
}
