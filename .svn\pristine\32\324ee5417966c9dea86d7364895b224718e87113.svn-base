package com.shunwang.basepassport.bind.mobile;

import com.shunwang.basepassport.BaseTest;
import com.shunwang.util.date.DateUtil;
import org.junit.Test;

public class TestMobileLogin {

	@Test
	public void testSend() throws Exception {
		new BaseTest() {
			@Override
			public void init() {
				params.put("siteId", "sw_pay");
				params.put("mobile", "13111111111");
				params.put("time", DateUtil.getCurrentDateStamp());
				params.put("signVersion", "1.0");
				params.put("sign", getSign(params));
				System.out.println(getSign(params));
			}

			@Override
			protected String getUrl() {
				return "http://interface.kedou.com/front/interface/sendForMobileRegister.htm ";
			}

			@Override
			protected String getMd5Key() {
				return "123456";
			}
		}.test();
	}

	@Test
	public void testValid() throws Exception {
		new BaseTest() {
			@Override
			public void init() {
				params.put("siteId", "sw_pay");
				params.put("bindType", "1");
				params.put("loginIp", "127.0.0.1");
				params.put("mobile", "13111111111");
				params.put("mobileActiveNo", "288093");
				params.put("time", DateUtil.getCurrentDateStamp());
				params.put("signVersion", "1.0");
				params.put("sign", getSign(params));
			}

			@Override
			protected String getUrl() {
				return "http://interface.kedou.com/front/interface/confirmForLogin.htm";
			}

			@Override
			protected String getMd5Key() {
				return "123456";
			}
		}.test();
	}
}
