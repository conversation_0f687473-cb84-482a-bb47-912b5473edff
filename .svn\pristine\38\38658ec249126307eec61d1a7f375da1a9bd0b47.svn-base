package com.shunwang.baseStone.cache.alert;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 计数然后阈值判断，报警的抽象实现
 *
 * <AUTHOR>
 * @date 2020/4/1
 **/
public class SimpleCountWindow extends RedisWindow {
    private static Logger logger = LoggerFactory.getLogger(SimpleCountWindow.class);

    private SimpleCountConfig config;

    public SimpleCountWindow(String key, long expire, TimeUnit timeUnit, SimpleCountConfig config) {
        super(key, expire, timeUnit);
        Objects.requireNonNull(config, "配置不能为空");
        this.config = config;
    }


    @Override
    public void doTrigger() {
        triggerOn(config.getThreshold(), new TriggerCallback() {
            @Override
            public void onTriggered(long totalCount) {
                if (!config.isSwitchOn()) {
                    logger.info("[{}]配置开关已关闭", config.name());
                    return;
                }
                config.doAlert(totalCount, config.getThreshold(), config.getAlertEmails());
            }

            @Override
            public void afterTriggered() {
                updateTriggerTime();
            }
        });
    }

    public SimpleCountConfig getConfig() {
        return config;
    }

    public void setConfig(SimpleCountConfig config) {
        this.config = config;
    }
}
