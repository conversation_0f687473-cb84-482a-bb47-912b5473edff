package com.shunwang.baseStone.jms;

import com.shunwang.baseStone.jms.action.Action;
import com.shunwang.baseStone.jms.action.DeleteAction;
import com.shunwang.baseStone.jms.action.NoopAction;
import com.shunwang.baseStone.jms.action.QueryAction;
import com.shunwang.baseStone.jms.mode.ExactMode;
import com.shunwang.baseStone.jms.mode.Mode;
import com.shunwang.baseStone.jms.mode.NoneMode;
import com.shunwang.baseStone.jms.mode.PrefixMode;
import com.shunwang.util.json.GsonUtil;
import org.springframework.cache.Cache;

import java.io.Serializable;

/**
 * 操作缓存的消息
 * <p>
 * eg:{"action":1,"mode":1,"param":{"type":1,"value":"123"}}
 *
 * <AUTHOR>
 * @date 2019/3/11
 **/
public class CacheMessage<T> implements Serializable {
    private int action;
    private int mode;
    private T param;


    public CacheMessage(int action, int mode, T param) {
        this.action = action;
        this.mode = mode;
        this.param = param;
    }


    public void doAction(Cache cache) {
        Action action = getAction(this.action);
        Mode mode = getMode(this.mode);
        action.doAction(cache, mode, param);
    }

    private Action getAction(int action) {
        switch (action) {
            case Action.DELETE:
                return new DeleteAction();
            case Action.QUERY:
                return new QueryAction();
            default:
                return new NoopAction();
        }
    }

    private Mode getMode(int mode) {
        switch (mode) {
            case Mode.MODE_EXACT:
                return new ExactMode();
            case Mode.MODE_PREFIX:
                return new PrefixMode();
            default:
                return new NoneMode();
        }
    }

    @Override
    public String toString() {
        return GsonUtil.toJson(this);
    }


    public int getAction() {
        return action;
    }

    public int getMode() {
        return mode;
    }

    public T getParam() {
        return param;
    }
}
