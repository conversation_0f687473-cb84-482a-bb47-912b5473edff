<%@ page contentType="text/html; charset=UTF-8" %>
<%@ include file="../common/taglibs.jsp" %>
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1,minimum-scale=1,user-scalable=no">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="format-detection" content="telephone=no, email=no">
  <meta name="full-screen" content="yes">
  <meta name="x5-page-mode" content="app">
  <meta name="msapplication-tap-highlight" content="no">
  <meta name="description" content="">
  <meta name="keywords" content="">
  <link rel="stylesheet" href="${staticServer}/${cdnVersion}/style/style.css">
  <title>扫码结果</title>
</head>
<body onload="load()">
<div class="content page-result">
  <div class="result-wrapper">
    <img class="icon-success" src="${staticServer}/${cdnVersion}/images/success.png" alt="">
    <h2 class="title">绑定完成</h2>
    <p class="desc" id="text"></p>
  </div>
  <ul class="activity-list" id="adList">

  </ul>
</div>
<script src="${staticServer}/${cdnVersion}/js/jquery-3.6.0.min.js"></script>
<script src="${staticServer}/${cdnVersion}/js/utils.js"></script>
<script src="https://res.wx.qq.com/open/js/jweixin-1.3.2.js" type="text/javascript"></script>
<script type="text/javascript">
  function jumpMiniApp() {
    if (window.__wxjs_environment === 'miniprogram') {
        console.log('小程序内打开绑定页面');
        var url= '/pages/center/center?channel=' +
                getQueryString("channel") +
                "&draw=" + getQueryString("draw");
        wx.miniProgram.navigateTo({url: url});
    }
  }

  function load(){
    var idCard = getQueryString("idCardNoResult");
    if (isEmpty(idCard)){
      idCard = "";
    }
    $("#text").html("身份证" + idCard + "已绑定，请到电脑端上机");
    $.ajax({
      url: "/wxBindIdCard/getAllAds.do",
      type: 'post',
      dataType: 'json',
      data: {"barid": getQueryString("barid")},
      success: function(result){
        if (result.length == 0){
          return;
        }else{
          var html = "";
          for ( var i = 0; i < result.length; i++ ){
            html += "<li class=\"item flex\">";
            html += "<img src='" + result[i].imgUrlShow + "' alt=\"\" class=\"cover\">";
            html += "<div class=\"detail\">";
            if (!isEmpty(result[i].brand)){
              html += "<p class=\"desc ellipsis\">" + result[i].brand +"</p>";
            }
            html += "<p class=\"title ellipsis\">";
            if (!isEmpty(result[i].redWord)){
              html += "<i>" + result[i].redWord + "</i>";
            }
            if(!isEmpty(result[i].title)){
              html += result[i].title;
            }
            html += "</p>";
            if (!isEmpty(result[i].subTitle)){
              html += "<p class=\"desc ellipsis\">" + result[i].subTitle + "</p>";
            }
            html += "</div>";
            var url = result[i].jumpUrl;
            if (!(isEmpty(url))){
              html += "<a class=\"btn\" data-type='"+result[i].id+"' data-url='"+result[i].jumpUrl+"' >";
              html += "<span class=\"ellipsis\">" + result[i].btnWord + "</span>";
              html += "</a>"
            }
            html += "</li>";
          }
          $("#adList").html(html);
        }
      },
      error: function(){
      }
    });

    jumpMiniApp();
  }

  $(document).on("click", ".btn", function (){
    var type = this.dataset['type'];
    var url = this.dataset['url'];
    reportData("12", type);
    location.href = url;
  })

  function isEmpty(s){
    if(typeof(s)=="undefined" || s=='' || s==null){
      return true;
    }
    return false;
  }

  function getQueryString(name) {
    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
    var r = window.location.search.substr(1).match(reg);
    if (r != null) return unescape(r[2]); return null;
  }
  $().ready(function (){
    reportData("9");
  });
  function reportData(type, ext) {
    var eventKey = getQueryString("eventKey");
    $.ajax({
      url: "/wxBindIdCard/report.do",
      type: 'post',
      dataType: 'json',
      data: { eventKey : eventKey, type: type, "extInfo": ext},
      success: function(result){
        // console.log(result);
      },
      error: function(){
      }
    });
  }


</script>
</body>
</html>