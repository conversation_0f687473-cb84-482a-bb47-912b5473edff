package com.shunwang.basepassport.detail.dao;


import com.shunwang.baseStone.context.IPContext;
import com.shunwang.baseStone.core.dao.BaseStoneIbatisDao;
import com.shunwang.basepassport.detail.pojo.PersonalEditLog;
import com.shunwang.basepassport.user.pojo.LogonLog;
import com.shunwang.framework.exception.WinterException;

import java.util.List;
import java.util.Map;

/**
 * 用户明细DAO
 * <AUTHOR>
 *
 */
public class PersonalEditLogDao extends BaseStoneIbatisDao<PersonalEditLog> {
    @SuppressWarnings("all")
    public List<PersonalEditLog> queryForList(Map<String,String> paramMap){
        return getSqlMapClientTemplate().queryForList(getStatementNameWrap("queryForMap"), paramMap);
    }


    @Override
    public PersonalEditLog save(PersonalEditLog p) throws WinterException {
        p.setClientIp(IPContext.getIp());
        return super.save(p);
    }
}
