<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">
	<bean id="siteInterfaceKeyGenerator" class="com.shunwang.baseStone.cache.keygenerator.SiteInterfaceKeyGenerator"/>

	<bean id="loginConfigKeyGenerator" class="com.shunwang.baseStone.cache.keygenerator.LoginConfigKeyGenerator"/>

	<bean id="outOauthDirKeyGenerator" class="com.shunwang.baseStone.cache.keygenerator.OutOauthDirKeyGenerator"/>

	<bean id="cssConfigKeyGenerator" class="com.shunwang.baseStone.cache.keygenerator.CssConfigKeyGenerator"/>

	<bean id="sysConfigKeyGenerator" class="com.shunwang.baseStone.cache.keygenerator.SysConfigKeyGenerator"/>

	<bean id="weixinOauthKeyGenerator" class="com.shunwang.baseStone.cache.keygenerator.WeixinOauthKeyGenerator"/>

	<bean id="weixinOauthTokenKeyGenerator" class="com.shunwang.baseStone.cache.keygenerator.WeixinOauthTokenKeyGenerator"/>

	<bean id="agreementKeyGenerator" class="com.shunwang.baseStone.cache.keygenerator.AgreementKeyGenerator"/>

	<bean id="smsConfigKeyGenerator" class="com.shunwang.baseStone.cache.keygenerator.SmsConfigKeyGenerator"/>
	<bean id="emailConfigKeyGenerator" class="com.shunwang.baseStone.cache.keygenerator.EmailConfigKeyGenerator"/>
	<bean id="appidReplyKeyGenerator" class="com.shunwang.baseStone.cache.keygenerator.AppidReplyKeyGenerator"/>


	<bean id="configResourceKeyGenerator" class="com.shunwang.baseStone.cache.keygenerator.ConfigResourceKeyGenerator"/>
	<bean id="configRichTextKeyGenerator" class="com.shunwang.baseStone.cache.keygenerator.ConfigRichTextKeyGenerator"/>
	<bean id="configRichTextIdKeyGenerator" class="com.shunwang.baseStone.cache.keygenerator.ConfigRichTextIdKeyGenerator"/>
	<bean id="configServiceKeyGenerator" class="com.shunwang.baseStone.cache.keygenerator.ConfigServiceKeyGenerator"/>
	<bean id="configCssKeyGenerator" class="com.shunwang.baseStone.cache.keygenerator.ConfigCssKeyGenerator"/>
	<bean id="configOauthKeyGenerator" class="com.shunwang.baseStone.cache.keygenerator.ConfigOauthKeyGenerator"/>
	<bean id="configOauthDefaultKeyGenerator" class="com.shunwang.baseStone.cache.keygenerator.ConfigOauthDefaultKeyGenerator"/>
	<bean id="configUserOutInterfaceKeyGenerator" class="com.shunwang.baseStone.cache.keygenerator.ConfigUserOutInterfaceKeyGenerator"/>
	<bean id="configUserOutInterfacePrefixListKeyGenerator" class="com.shunwang.baseStone.cache.keygenerator.ConfigUserOutInterfacePrefixListKeyGenerator"/>
	<bean id="configSiteInterfaceKeyGenerator" class="com.shunwang.baseStone.cache.keygenerator.ConfigSiteInterfaceKeyGenerator"/>
	<bean id="configSiteInterfaceOpenKeyGenerator" class="com.shunwang.baseStone.cache.keygenerator.ConfigSiteInterfaceOpenKeyGenerator"/>

</beans>
