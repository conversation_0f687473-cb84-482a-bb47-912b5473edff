<%@ page contentType="text/html;charset=UTF-8"%>
<%@ include file="/common/taglibs.jsp"%>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name="Keywords" content="顺网通行证 ，密码 ，找回 ，密保问题" />
<meta name="Description" content="输入密保问题的答案。" />
<title>顺网通行证-找回密码-密保问题找回</title>

</head>
<body>
     
      <%@ include file="global_nav.jsp" %>
      <div class="c_body forget_s05">
          <ul class="step_bar">
              <li><em>1</em> 选择找回方式</li>
              <li class="current"><em>2</em> 进行安全验证</li>
              <li><em>3</em> 设置新密码</li>
          </ul>
          <div class="forget_send">
              <i class="f_question"></i>
              <p class="tip">密保问题验证</p>
              <p>根据系统设置，您在进行操作前需要进行安全验证，请先回答下列密保问题。</p>
          </div>
          <div id="errorMessagesShowSpan" class="fB col_f00 " style="margin-left:200px;">
              <script type="text/javascript">
                  <c:if test="${!empty errorMsg}">
                          document.getElementById("errorMessagesShowSpan").innerHTML="<img src='<c:url value='/images/front/error.gif'/>' />您输入的答案不正确，请重输 !";
                  </c:if>
                  <c:if test="${!empty msg}">
                          document.getElementById("errorMessagesShowSpan").innerHTML="<img src='<c:url value='/images/front/error.gif'/>' />您输入的答案不正确，请重输 !";
                  </c:if>
              </script>
          </div>
          <form class="passform mar180 f14px"  id="questionForm20140902141116" action="/front/noLogin/pwdFind_question_front.htm" method="post">
              <input type="hidden" name="memberName" value="${memberName}" />
              <input type="hidden" name="password" id="password"/>

          <div class="form_group">
              <table cellpadding="0" cellspacing="0">
                  <tbody>
                  <jsp:include page="memberBindQuestionSet_common.jsp"></jsp:include>

                  <tr>
                      <th></th>
                      <td>
                          <a href="###" onclick="return checkAnswer()" class="btn_default_lg">下一步</a>
                      </td>
                  </tr>
                  <tr>
                      <th></th>
                      <td>
                          <a href="${appServer}/front/noLogin/pwdFind_appeal_front.htm?memberName=${memberName}&findWay=1">选择其他找回方式>></a>
                      </td>
                  </tr>
                  </tbody>
              </table>
          </div>
          </form>
      </div>
</body>
</html>
