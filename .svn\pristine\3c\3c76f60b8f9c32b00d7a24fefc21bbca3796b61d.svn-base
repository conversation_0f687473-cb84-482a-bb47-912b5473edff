package com.shunwang.baseStone.sso.apapter;

import com.shunwang.baseStone.sso.constant.UserOutsiteConstant;
import com.shunwang.basepassport.user.pojo.MemberOutSite;

import java.math.BigInteger;

/**
 * 电竞酒店平台对接 - 账号导入
 *
 * example：http://sso.kedou.com/hotelBind.do?userId=75694226F326C98&siteId=jisheng_xgb&timestamp=20211203101041&sign=80F7B25BBAC75C0C1144F91FEFBC37E6
 * author: tao.feng
 * date: 2021-12-02
 */
public class HotelAdapter extends SimpleImportUsersAdapter {

    @Override
    public String getInterfaceId() {
        return UserOutsiteConstant.HOTEL_INTERFACE_ID;
    }

    @Override
    protected void outReg(MemberOutSite memberOutSite) throws Exception {
        super.outReg(memberOutSite);
        //电竞酒店标记为商户类型
        member.setMemberSpecialType(UserOutsiteConstant.MEMBER_SPECIAN_TYPE_SICENT);
        interfaceService.updateMemberSpecialType(member);
    }

    /**
     * 将id由十进制转成三十六进制
     * @return base后台配置的前缀 + 三十六进制后的id
     */
    @Override
    protected String genMemberName() {
        BigInteger bi = new BigInteger(getUserId(), 10);
        StringBuilder sb = new StringBuilder();
        sb.append(userOutInterface.getPrefixName());
        sb.append(bi.toString(36));
        return sb.toString();
    }

}
