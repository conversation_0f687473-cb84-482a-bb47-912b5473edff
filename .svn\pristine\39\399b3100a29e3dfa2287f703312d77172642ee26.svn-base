<%@ page contentType="text/html;charset=UTF-8"%>
<%@ include file="/common/taglibs.jsp"%>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta name="Keywords" content="顺网通行证、 密保问题 、找回 、实名认证" />
<meta name="Description" content="填写申诉理由，填写新的密保问题等待审核。" />

<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>顺网通行证-找回密保问题-实名认证找回</title>
<script type="text/javascript" src="${staticServer}/scripts/front/member/password.js"></script>
<script type="text/javascript" src="${staticServer}/scripts/common/jquery.js"></script>
<script type="text/javascript" src="${staticServer}/scripts/front/member/min/validation.min.js"></script>
<script type="text/javascript" src="${staticServer}/scripts/common/md5.js"></script>
<script type="text/javascript" src="${staticServer}/scripts/front/find/resetPwd_front.js"></script>
<script type="text/javascript" src="${staticServer}/scripts/front/common/citys.js" ></script>
<script type="text/javascript" src="${staticServer}/scripts/front/common/calendar.js"></script>
<script type="text/javascript" src="${staticServer}/scripts/front/find/globalAppeal_front.js"></script>
<script type="text/javascript">
  function toSubmitQue(){
     if(check()){
        document.getElementById("appealQuestionFind").submit();
     }
    
  }
</script>
</head>


<body>
<div class="c_head">
    <i class="forget_icon"></i>
    <span class="title">重置密保问题</span>
    <span class="desc">正在重置<strong>${memberName}</strong>的密保问题</span>
</div>
<div class="c_body forget_s01">
    <ul class="step_bar">
        <li><em>1</em> 填写基本资料</li>
        <li class="current"><em>2</em> 填写账号信息</li>
        <li><em>3</em> 设置密保问题</li>
    </ul>
    <div class="appeal_question">
        <p><strong>请重置您的密保问题，一旦审核通过后，该新密保问题开始生效（以下均为必填项）</strong></p>
        <p class="form_tip">请选择三个问题并填入答案，并请牢记该问题的答案，方便今后使用。</p>
    </div>

<div class="form_group">
				<form method="post" id="appealQuestionFind" action="/front/noLogin/commintMsgForQue.htm">
					<input type="hidden" name="appeal.userName" id="userName" value="${appeal.userName }"/> 
					<input type="hidden" name="appeal.realName" id="realName" value="${appeal.realName }"/> 
					<input type="hidden" name="appeal.idCardNo" id="userIdCard" value="${appeal.idCardNo}"/> 
					<input type="hidden" name="appeal.email" id="email" value="${appeal.email }"/> 
					<input type="hidden" name="appeal.mobile" id="mobile" value="${appeal.mobile }"/>
					<input type="hidden" name="appeal.idCardUrl" id="idCardUrl" value="${appeal.idCardUrl }"/>
                    <table cellpadding="0" cellspacing="0">
                        <thead>
                        <tr>
                            <th class="w_lg"></th>
                            <td><span class="form_error" id="errorMessagesShowSpan"><c:if test="${!empty errorMsg}">
                                <img src="<c:url value='/images/front/error.gif'/>" /><font color="red">${errorMsg}</font>
                            </c:if></span></td>
                        </tr>
                        </thead>
                        <tbody>


				    <s:action namespace="/front/noLogin" flush="true"   name="getAllquestions_front" executeResult="true"></s:action>

                    <tr>
                        <th></th>
                        <td>
                            <a href="###" class="btn_default_lg" onclick="toSubmitQue();" >确定</a>
                        </td>
                    </tr>
                        </tbody>
                    </table>

                </form>
			</div>
		</div>
</body>
</html>
