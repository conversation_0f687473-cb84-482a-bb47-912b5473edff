<?xml version="1.0" encoding="UTF-8"?>
<decorators defaultdir="/decorators">
		<!-- 不需要被装饰 -->
	   <excludes>
	       <pattern>/result.jsp*</pattern>
	       
       </excludes>
    
       <decorator name="main" page="main.jsp">
          <pattern>/notice/*</pattern>
          <pattern>/editBackground/*</pattern>
          <pattern>/reason/*</pattern>  
          <pattern>/sysconfig/*</pattern>  
          <pattern>/pageHome/*</pattern>  
          <pattern>/bussiness/*</pattern> 
          <pattern>/service/*</pattern> 
          <pattern>/pageHome/*</pattern>
          <pattern>/editLog/*</pattern>
          <pattern>/siteInterface/*</pattern>
          <pattern>/dictionary/*</pattern>
          <pattern>/sensitiveWord/*</pattern>
		  <pattern>/userOutInterface/*</pattern>
		  <pattern>/css/*</pattern>  
		  <pattern>/category/*</pattern>
		  <pattern>/loginElement/*</pattern>
		  <pattern>/partlyAccess/*</pattern>
		  <pattern>/busLoginElement/*</pattern>
		  <pattern>/richText/*</pattern>
		  <pattern>/configoauth/*</pattern>
       </decorator>
       
</decorators>